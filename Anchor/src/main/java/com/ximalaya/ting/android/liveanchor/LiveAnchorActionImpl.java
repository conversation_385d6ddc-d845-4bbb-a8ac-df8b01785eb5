package com.ximalaya.ting.android.liveanchor;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.util.SparseArray;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.fragment.ManageFragment;
import com.ximalaya.ting.android.framework.util.FragmentUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleException;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.entity.BaseRoomDetail;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.NewAudienceAwardInfo;
import com.ximalaya.ting.android.live.host.dialog.sell.LiveSellSettingDialogFragment;
import com.ximalaya.ting.android.live.host.fragment.entrance.LiveEntranceFragment;
import com.ximalaya.ting.android.live.host.liverouter.anchor.IAnchorAction;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCore;
import com.ximalaya.ting.android.live.host.request.IDataCallbackWithId;
import com.ximalaya.ting.android.live.host.scrollroom.fragment.BaseScrollRoomFragment;
import com.ximalaya.ting.android.live.host.scrollroom.fragment.LiveScrollFragment;
import com.ximalaya.ting.android.live.host.utils.LiveHostCommonUtil;
import com.ximalaya.ting.android.liveanchor.create.AdminManagerFragment;
import com.ximalaya.ting.android.liveanchor.create.MyLivesFragmentNew;
import com.ximalaya.ting.android.liveaudience.fragment.room.LiveRoomBaseFragment;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.liveaudience.util.start.LiveStartUtil;
import com.ximalaya.ting.android.liveaudience.view.DatePickerDialog;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.Map;

/**
 * Created by qianmenchao on 2020-03-23.
 *
 * <AUTHOR>
 */
public class LiveAnchorActionImpl implements IAnchorAction {

    private final SparseArray<Class<? extends BaseFragment>> fragmentMap = new SparseArray<Class<
            ? extends BaseFragment>>(3) {
        {
            put(Configure.LiveFragmentFid.MY_LIVES_FRAGMENT, MyLivesFragmentNew.class);
            put(Configure.LiveFragmentFid.ADMIN_MANAGER_FRAGMENT, AdminManagerFragment.class);
        }
    };

    @Override
    public BaseFragment newAdminManagerFragment(long roomId, boolean canJump) {
        return AdminManagerFragment.newInstance(roomId, canJump);
    }

    @Override
    public BaseFragment newHostFragment() {
        return LiveAnchorRoomFragment.newInstance();
    }

    @Override
    public BaseFragment newHostFragment(long roomId, long liveId) {
        return LiveAnchorRoomFragment.getInstance(liveId, roomId, LiveMediaType.TYPE_AUDIO);
    }

    @Override
    public BaseFragment newHostFragment(long roomId, long liveId, int mediaType) {
        return LiveAnchorRoomFragment.getInstance(liveId, roomId, mediaType);
    }

    @Override
    public BaseFragment newFragmentByFid(int fid) throws BundleException {
        BaseFragment fragment;
        Class<? extends BaseFragment> clazz = fragmentMap.get(fid);
        if (clazz != null) {
            try {
                fragment = clazz.newInstance();
            } catch (InstantiationException e) {
                e.printStackTrace();
                throw new BundleException(Configure.liveBundleModel.bundleName, "new a fragment " +
                        "by fid" + fid + " failure!, Exception:" + e);
            } catch (IllegalAccessException e) {
                e.printStackTrace();

                throw new BundleException(Configure.liveBundleModel.bundleName, "new a fragment " +
                        "by fid" + fid + " failure!,Execption:" + e);
            }
        } else {
            throw new BundleException(Configure.liveBundleModel.bundleName, "fid:" + fid + " --> " +
                    "can not find the Class, maybe fragment is not registered");
        }
        if (fragment != null) {
            fragment.fid = fid;
        }
        return fragment;
    }

    @Override
    public boolean notShowNotification(Fragment topFragment) {
        if (topFragment == null) {
            return false;
        }

        return topFragment instanceof LiveAnchorRoomFragment;
    }

    /**
     * 打开直播入口
     */
    @Override
    public BaseFragment newLiveEntranceFragment() {
        // 不做宿主状态判断，只提供对象
        return LiveEntranceFragment.newInstance();
    }

    @Override
    public BaseFragment newAdminManagerFragment(long roomId) {
        BaseFragment fragment = AdminManagerFragment.newInstance(roomId);
        fragment.fid = Configure.LiveFragmentFid.ADMIN_MANAGER_FRAGMENT;
        return fragment;
    }

    @Override
    public BaseFragment newMyLivesFragment() {
        return MyLivesFragmentNew.newInstance();
    }

    @Override
    public void showLiveUserCard(FragmentActivity activity, long userId, int forceShowUserCard) {
        MainActivity mainActivity = (MainActivity) activity;
        if (!LiveHostCommonUtil.isActivityEnable(mainActivity)) {
            return;
        }
        if (mainActivity.getManageFragment() != null) {
            Fragment currentFragment = mainActivity.getManageFragment().getCurrentFragment();
            if (currentFragment instanceof LiveAnchorRoomFragment) {
                LiveAnchorRoomFragment hostFragment = (LiveAnchorRoomFragment) currentFragment;
                if (forceShowUserCard == 1) {
                    hostFragment.showUserInfoPopOnly(userId);
                } else {
                    hostFragment.showUserInfoPop(userId);
                }
            }
        }
    }

    @Override
    public void showTimePickDialog(
            Activity act,
            String title,
            boolean showNow,
            long curTime,
            final OnGetTimeChangeCallback callback
    ) {

        final DatePickerDialog datePickerDialog = new DatePickerDialog(act, showNow);
        datePickerDialog.setTitle(title);
        datePickerDialog.setSetCallback(new DatePickerDialog.SetCallback() {
            @Override
            public void onExecute(int type, int year, int month, int day, int hour, int minute) {
                if (callback != null) {
                    callback.onExecute(type, year, month, day, hour, minute);
                }
                datePickerDialog.dismiss();
            }

            @Override
            public void onCancel() {
                if (callback != null) {
                    callback.onCancel();
                }
                datePickerDialog.dismiss();
            }
        });

        datePickerDialog.show(curTime);
    }

    @Override
    public void deletePersonLiveRecord(Context ctx, long id, boolean showNotice, final IDataCallBack<Integer> callBack) {
        LamiaHelper.deletePersonLiveRecord(ctx, id,
                new LamiaHelper.LightCallback() {
                    @Override
                    public void start() {

                    }

                    @Override
                    public void onError(int code, String message) {
                        if (callBack != null) {
                            callBack.onError(code, message);
                        }
                    }
                }, new LamiaHelper.DoActionCallback() {
                    @Override
                    public void onSuccess() {
                        callBack.onSuccess(1);
                    }

                    @Override
                    public void onCancel() {
                        if (callBack != null) {
                            callBack.onError(-1, "");
                        }
                    }

                    @Override
                    public boolean canUpdateMyUi() {
                        return true;
                    }
                }, new LamiaHelper.RetryCallback() {
                    @Override
                    public void onOkClick() {
                    }

                    @Override
                    public void onCancelClick() {

                    }
                }, false);

    }

    @Override
    public void updatePersonLiveRecord(Context ctx, Map<String, String> params, final IDataCallBack<Integer> callBack) {

        LamiaHelper.updatePersonLiveRecord(ctx, params, new IDataCallBack<Integer>() {
            @Override
            public void onSuccess(@Nullable Integer object) {
                if (callBack != null) {
                    callBack.onSuccess(1);
                }
            }

            @Override
            public void onError(int code, String message) {

                if (callBack != null) {
                    callBack.onError(code, message);
                }

            }
        });

    }

    @Override
    public void openGiftPackageItem(FragmentActivity activity, long id, long expireAt) {
        Fragment fragment = FragmentUtil.getShowingFragmentByClass(activity, LiveAnchorRoomFragment.class);
        if (fragment instanceof LiveAnchorRoomFragment) {
            LiveAnchorRoomFragment roomFragment = (LiveAnchorRoomFragment) fragment;
            NewAudienceAwardInfo newAudienceAwardInfo = new NewAudienceAwardInfo("");
            newAudienceAwardInfo.id = id;
            newAudienceAwardInfo.expireAt = expireAt;
            roomFragment.showPackageGiftAndLocate(newAudienceAwardInfo);
        }
    }

    @Override
    public void startSellSettingFragment(Activity activity, long roomId, long anchorId, int liveType, boolean isHybridFull, long liveId, String sellUrl) {
        if (!(activity instanceof MainActivity)) {
            return;
        }

        MainActivity mainActivity = (MainActivity) activity;
        if (!LiveHostCommonUtil.isActivityEnable(mainActivity)) {
            return;
        }
        LiveSellSettingDialogFragment.newInstance(mainActivity, roomId, anchorId, liveType, isHybridFull, liveId, sellUrl)
                .show(mainActivity.getSupportFragmentManager(), MainActivity.class.getSimpleName());
    }


    /**
     * 打开个人直播 的主播推流页面
     *
     * @param mediaType 区分 视频 or 音频
     * @param fragment  原始起点页面
     * @param liveId    场次id
     * @param roomId    房间id
     */
    @Override
    public void openPersonLivePushFragment(@LiveMediaType int mediaType, BaseFragment2 fragment, long liveId, long roomId) {
        LiveStartUtil.gotoHostLivePage(mediaType, fragment, liveId, roomId, false);
    }

    @Override
    public boolean isExistPiaLiveRoom(ManageFragment manager) {
        return RoomModeManager.isPiaMode();
    }

    @Override
    public boolean isExistAnchorRoom(ManageFragment manager) {
        Fragment target = manager.findFragment(LiveAnchorRoomFragment.class.getName());
        return target instanceof LiveAnchorRoomFragment;
    }

    @Override
    public boolean isExistPiaPreparedLiveRoom(ManageFragment manager, boolean requireMySelfAsHost) {
        if (!RoomModeManager.isPiaMode()) return false;

        boolean result = false;
        LiveRoomBaseFragment room = null;

        // 听众端房间
        Fragment target = manager.findFragment(LiveScrollFragment.class.getName());
        if (target instanceof LiveScrollFragment) {
            BaseScrollRoomFragment baseRoom = ((LiveScrollFragment) target).getCurrentScrollRoomFragment();
            if (baseRoom instanceof LiveRoomBaseFragment) {
                room = (LiveRoomBaseFragment) baseRoom;
            }
        }

        // 主播端房间
        if (room == null) {
            target = manager.findFragment(LiveAnchorRoomFragment.class.getName());
            if (target instanceof LiveAnchorRoomFragment) {
                room = (LiveRoomBaseFragment) target;
            }
        }

        if (room != null) {
            result = room.isNowPiaPrepared();

            if (requireMySelfAsHost) {
                boolean isHost = room.getHostUid() > 0 && room.getHostUid() == UserInfoMannage.getUid();
                result = result && isHost;
            }
        }

        return result;
    }

    @Override
    public int getBackgroundColor(Activity activity) {
        if (!(activity instanceof MainActivity)) {
            return 0;
        }

        MainActivity mainActivity = (MainActivity) activity;
        ManageFragment manageFragment = mainActivity.getManageFragment();
        if (null == manageFragment) {
            return 0;
        }

        Fragment currentFragment = manageFragment.getCurrentFragment();
        if (currentFragment instanceof LiveAnchorRoomFragment) {
            LiveAnchorRoomFragment lamiaFragment = (LiveAnchorRoomFragment) currentFragment;
            return lamiaFragment.getBackgroundColor();
        }
        return 0;
    }

    @Override
    public void loadRoomDetail(Bundle bundle, IDataCallbackWithId<BaseRoomDetail> callBack) {

    }

    @Override
    public RoomCore createRoom() {
        return new AnchorRoomCore();
    }

    @Override
    public String getAnchorRoomFragmentName() {
        return LiveAnchorRoomFragment.class.getName();
    }
}
