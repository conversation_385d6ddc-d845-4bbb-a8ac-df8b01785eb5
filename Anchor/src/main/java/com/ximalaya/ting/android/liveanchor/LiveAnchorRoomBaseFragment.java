package com.ximalaya.ting.android.liveanchor;

import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ximalaya.ting.android.common.lib.logger.ConnectLogUtilWrapper;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.live.asr.IAsrCallback;
import com.ximalaya.ting.android.host.live.asr.LiveMediaAsrInfo;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.music.IMusicFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MusicActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.SharePanelDialog;
import com.ximalaya.ting.android.host.model.live.PersonLiveBase;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.VerticalSlideUtil;
import com.ximalaya.ting.android.host.view.dialog.SimpleDialog;
import com.ximalaya.ting.android.live.biz.pia.panel.manager.XmPiaBgmPlayerManager;
import com.ximalaya.ting.android.live.common.danmaku.manager.LiveBulletMsgManager;
import com.ximalaya.ting.android.live.common.dialog.web.ProvideForH5CustomerDialogFragment;
import com.ximalaya.ting.android.live.common.lib.base.bgm.ILiveBgmCore;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.constants.PreferenceConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants;
import com.ximalaya.ting.android.live.common.lib.base.util.ILiveCommonDataCallback;
import com.ximalaya.ting.android.live.common.lib.entity.LiveHostNoPushStreamMessage;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.gift.panel.GiftHitFinishCallback;
import com.ximalaya.ting.android.live.common.lib.gift.panel.GiftRepeatHandImpl;
import com.ximalaya.ting.android.live.common.lib.gift.panel.SendGiftDialog;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.audio_quality.XmLiveAudioPreProcessor;
import com.ximalaya.ting.android.live.common.lib.manager.livesdkclient.LiveClientManager;
import com.ximalaya.ting.android.live.common.lib.manager.zego.ZegoUserInfo;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHandlerUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveRouterUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveWebUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.asr.LiveAsrProcessor;
import com.ximalaya.ting.android.live.common.lib.utils.monitor.PhoneCallNetworkAndHeadSetStateMonitor;
import com.ximalaya.ting.android.live.common.music.LiveBgMusicListFragment;
import com.ximalaya.ting.android.live.common.view.dialog.simpleconfirm.LiveSimpleConfirmDialog;
import com.ximalaya.ting.android.live.common.view.dialog.simpleconfirm.LiveSimpleConfirmDialogListener;
import com.ximalaya.ting.android.live.common.view.dialog.simpleconfirm.LiveSimpleConfirmDialogParams;
import com.ximalaya.ting.android.live.common.view.dialog.warning.WarningDialogFactory;
import com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig;
import com.ximalaya.ting.android.live.host.components.tuning.ICommonTuningComponent;
import com.ximalaya.ting.android.live.host.data.CheckRestart;
import com.ximalaya.ting.android.live.host.data.MicStreamInfo;
import com.ximalaya.ting.android.live.host.data.SceneLiveRealTime;
import com.ximalaya.ting.android.live.host.data.stream.ZegoRoomInfo;
import com.ximalaya.ting.android.live.host.data.topic.LiveTopicInfo;
import com.ximalaya.ting.android.live.host.dialog.LiveNormalDialog;
import com.ximalaya.ting.android.live.host.fragment.debug.LiveDebugFragment;
import com.ximalaya.ting.android.live.host.manager.aigift.AIGiftShowManager;
import com.ximalaya.ting.android.live.host.request.CommonRequestForLiveHost;
import com.ximalaya.ting.android.live.host.utils.LiveHostFragmentUtil;
import com.ximalaya.ting.android.live.host.utils.ShareUtils;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatUserJoinMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonFloatScreenMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonBusinessMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomAnchorVerifyWarningMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomStatusChangeMessage;
import com.ximalaya.ting.android.liveanchor.components.bottom.IHostBottomComponent;
import com.ximalaya.ting.android.liveanchor.components.countdown.IHostCountDownComponent;
import com.ximalaya.ting.android.liveanchor.dialog.AnchorCloseRoomAlertDialog;
import com.ximalaya.ting.android.liveanchor.dialog.LiveHostExitConfirmDialog;
import com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.IInteractivePlayComponent;
import com.ximalaya.ting.android.liveaudience.components.mic.IMicBaseComponent;
import com.ximalaya.ting.android.liveaudience.data.model.liveplay.AnchorLiveData;
import com.ximalaya.ting.android.liveaudience.data.request.CommonRequestForLive;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkInviteeResult;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkInviteeSyncResult;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkInviterResult;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkMicStatusRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkPropPanelNotify;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkStartMatchRsp;
import com.ximalaya.ting.android.liveaudience.fragment.finish.LiveAnchorFinishFragment;
import com.ximalaya.ting.android.liveaudience.fragment.income.LiveHostIncomeRecordFragment;
import com.ximalaya.ting.android.liveaudience.fragment.pk.PkStartMatchFragment;
import com.ximalaya.ting.android.liveaudience.fragment.room.LiveRoomBaseFragment;
import com.ximalaya.ting.android.liveaudience.friends.LoveModeLogicHelper;
import com.ximalaya.ting.android.liveaudience.giftModule.dialog.FriendsGiftDialog;
import com.ximalaya.ting.android.liveaudience.giftModule.loader.AnchorGiftLoader;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeManager;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeUIManager;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.manager.pk.LivePkHelper;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.liveaudience.util.LiveLamiaUtil;
import com.ximalaya.ting.android.liveaudience.view.dialog.AVUserInfoCardDialog;
import com.ximalaya.ting.android.liveaudience.view.dialog.LiveOpenFriendsModeDialog;
import com.ximalaya.ting.android.liveaudience.view.dialog.ReceiveInviteePkDialog;
import com.ximalaya.ting.android.liveav.lib.XmAVSdk;
import com.ximalaya.ting.android.liveav.lib.constant.Role;
import com.ximalaya.ting.android.liveav.lib.data.MixStreamConfig;
import com.ximalaya.ting.android.liveav.lib.data.StreamInfo;
import com.ximalaya.ting.android.liveav.lib.data.VideoAvConfig;
import com.ximalaya.ting.android.liveav.lib.impl.zego.constants.XmZegoConstants;
import com.ximalaya.ting.android.liveav.lib.util.log.LiveLogUtil;
import com.ximalaya.ting.android.liveim.base.callback.ISendCallback;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.lang.ref.SoftReference;
import java.lang.ref.WeakReference;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import LOVE.Base.LoveMode;
import PK.Base.ManualUserInfo;
import PK.Base.MicStatus;
import PK.Base.Mode;
import PK.Base.RandomPkType;

/**
 * 主播端
 *
 * <AUTHOR>
 */
public abstract class LiveAnchorRoomBaseFragment extends LiveRoomBaseFragment implements View
        .OnClickListener, IFragmentFinish,
        AVUserInfoCardDialog.IShowUserInfoDialog {

    public static final String TAG = "LiveAnchorRoomBaseFragment";

    public boolean needStartLoveMode;
    /**
     * 直播场次id
     */
    private long mLiveId;
    protected PersonLiveDetail mModel;
    protected PersonLiveDetail.LiveRecordInfo mLiveRecordInfo;
    protected PersonLiveDetail.LiveAnchorInfo mLiveUserInfo;
    protected int mInputMode;

    protected boolean mNewGiftMessageBoolean = false;
    protected boolean isOnResume;
    protected boolean gateWapCheckReady;
    protected ZegoRoomInfo mZegoRoomInfo;
    protected boolean tryPublishSuccess = false;
    protected Handler mHandler = LiveHandlerUtil.getMainHandler();

    protected boolean isKicked;   //是否被顶掉，用于在退出直播间时判断是否需要发关闭交友模式的消息
    protected boolean suggestRequesting = false;
    protected boolean hasStarted = false;
    protected boolean shallRestart = false;
    protected DialogBuilder mDialogBuilder;
    protected boolean hasShowCountDownAni;

    protected boolean isLiveStopping = false;
    protected boolean mGoToFinishFragmentCalled = false;
    private XmPlayerStatusListener xmPlayerStatusListener;
    /**
     * 配乐弹窗，强引用
     */
    protected VerticalSlideUtil.VerticalSlideWrapper<LiveBgMusicListFragment> mLiveBgmDialogWrapper;

    protected boolean needShowBgmDialog;

    /**
     * 收入弹窗，弱引用
     */
    protected WeakReference<VerticalSlideUtil.VerticalSlideWrapper<LiveHostIncomeRecordFragment>>
            mLiveIncomeRecordWrapper;
    /**
     * PK弹窗
     */
    protected PkStartMatchFragment mLiveStartMatchDialog;

    /**
     * PK邀请弹窗
     */
    private ReceiveInviteePkDialog mInvitePkDialog;

    /**
     * 主播强提示类对话框 目前只有一个对话框，状态不同
     */
    protected WarningDialogFactory mWarningDialogFactory;

    protected SharePanelDialog mShareDialog;

    /**
     * 标记是否开麦状态
     */
    protected boolean mEnableMic = true;


    private final Map<Long, Integer> mInviteData = new HashMap<>();

    public void setId(long id) {
        mLiveId = id;
    }

    public void setRoomId(long roomId) {
        mRoomId = roomId;
    }

    /**
     * 在线列表弹窗
     */
    protected VerticalSlideUtil.VerticalSlideWrapper mOnlineNobleWrapper;

    private ProvideForH5CustomerDialogFragment mPkPredictRecordFragment;

    /**
     * asr 回调
     */
    private AnchorAsrCallBack mAnchorAsrCallBack;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mInputMode = getWindow().getAttributes().softInputMode;
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);

        try {
            mLiveId = LiveHostFragmentUtil.getFragmentArgument(this, ParamsConstantsInLive.LIVE_ID);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onMyResume() {
        super.onMyResume();

        StatusBarManager.setStatusBarColorDelay(getWindow(), false, this);

        Logger.i(TAG, " ---- onMyResume ---- ");
        isOnResume = true;
        showBgmAfterAddMusic();
        if (mNewGiftMessageBoolean) {
            mNewGiftMessageBoolean = false;
        }
        LamiaHelper.WakeLockHelper.getInstance().acquireWakeLock(mContext);
        attachToPlayManagerListener();
    }

    @Override
    public void onResume() {
        super.onResume();
        Logger.i(TAG, " ---- onResume ---- ");
    }

    @Override
    public void onPause() {
        super.onPause();
        Logger.i(TAG, " ---- onPause ---- ");
        isOnResume = false;
    }

    @Override
    protected String getPageLogicName() {
        return "主播端";
    }

    @Override
    public void onDestroyView() {
        if (mLiveAdminListFraWrapper != null && mLiveAdminListFraWrapper.get() != null) {
            mLiveAdminListFraWrapper.get().dismiss();
            mLiveAdminListFraWrapper = null;
        }
        if (mLiveBanListFraWrapper != null && mLiveBanListFraWrapper.get() != null) {
            mLiveBanListFraWrapper.get().dismiss();
            mLiveBanListFraWrapper = null;
        }
        if (mLiveIncomeRecordWrapper != null && mLiveIncomeRecordWrapper.get() != null) {
            mLiveIncomeRecordWrapper.get().dismiss();
            mLiveIncomeRecordWrapper = null;
        }

        dismissPkStartMatchDialog();

        if (mInvitePkDialog != null && mInvitePkDialog.isShowing()) {
            mInvitePkDialog.dismiss();
            mInvitePkDialog = null;
        }

        // 释放主播信息单例
        AnchorLiveData.getInstance().release();

        getWindow().setSoftInputMode(mInputMode);

        //声音美化release
        XmLiveAudioPreProcessor.INSTANCE.release();
        mAnchorAsrCallBack = null;

        super.onDestroyView();
    }

    @Override
    public void onDestroy() {
        Logger.i(TAG, "HostFragment onDestroy");
        if (shallRestart) {
            LiveLamiaUtil.gotoHostLive(((MainActivity) getActivity()), mLiveId, mRoomId);
        }
        LiveAsrProcessor.Companion.getInstance().release();

        LiveBulletMsgManager.getInstance().release();

        RoomModeManager.getInstance().release();

        AnchorGiftLoader.release(AnchorGiftLoader.class);

        LamiaHelper.WakeLockHelper.release();

        releaseResource(false);

        dismissBgmDialog(true);
        deAttachToManagerListener();

        super.onDestroy();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        super.initUi(savedInstanceState);
        initViews();
    }

    protected void updateAfterLoaded(PersonLiveDetail object) {
        if (object != null && object.getLiveRecordInfo() != null && object
                .getLiveAnchorInfo() != null) {
            mModel = object;
            mLiveId = object.getLiveId();
            mLiveRecordInfo = object.getLiveRecordInfo();
            mLiveUserInfo = object.getLiveAnchorInfo();
            AnchorLiveData.getInstance().anchorUid = mLiveUserInfo.uid;
            AnchorLiveData.getInstance().roomId = mLiveRecordInfo.roomId;
            AnchorLiveData.getInstance().liveId = mLiveRecordInfo.id;


            // 直播状态， 1:已结束、5:未开始、9:直播中
            switch (mLiveRecordInfo.status) {
                //切换到结束页面
                case PersonLiveBase.LIVE_STATUS_END:
                    gotHostFinishFragment();
                    break;
                case PersonLiveBase.LIVE_STATUS_NOTICE:
                case PersonLiveBase.LIVE_STATUS_ING:

                    //请求直播推流信息
                    //当主播重新LoadData时，不走推流流程
                    if (!XmAVSdk.getInstance().isPublish()) {
                        requestLiveRoomPushInfo();
                    }
                    //TODO 以下3项考虑删除，直接在各自组件中进行处理
                    handleOnlineStatusChanged(mLiveRecordInfo.onlineCount,
                            mLiveRecordInfo.playCount);
                    break;
                default:
                    break;
            }
        }
    }


    /**
     * 请求直播推流信息
     */
    private void requestLiveRoomPushInfo() {
        Map<String, String> params = LiveHelper.buildTimeParams();
        params.put("liveId", "" + mLiveId);
        params.put("roomId", mLiveRecordInfo != null ? mLiveRecordInfo.roomId + "" : "");
        params.put("liveType", mLiveMediaType + "");

        CommonRequestForLiveHost.getPersonLivePushUrls(params, new IDataCallBack<ZegoRoomInfo>() {
            @Override
            public void onSuccess(@Nullable ZegoRoomInfo roomInfo) {
                if (!canUpdateUi()) {
                    return;
                }

                if (roomInfo != null && roomInfo.getRet() == 0) {
                    gateWapCheckReady = true;
                    mZegoRoomInfo = roomInfo;
                    // 停止Loading 开始倒计时准备说话
                    stopLoading();

                    // 服务端启动直播
                    tryToStartLiveAndChatRoom();

                    if (hasShowCountDownAni) {
                        tryStartPublish();
                    } else {
                        showCountDownAni();
                    }


                    LoveModeManager.getInstance().setZegoRoomId(roomInfo.getRoomId());
                    if (needStartLoveMode) {
                        //掉线重进或者互踢重进，需要开启交友模式，这时没有 zego 信息，就需要在获取到信息后重试
                        LoveModeManager.getInstance().startLoveMode();
                        needStartLoveMode = false;
                    }
                } else {
                    CustomToast.showFailToast("推流地址获取失败");
                    showPublishError();
                }
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }

                Logger.i(TAG, "requestCheckByGateway " + code + message);

                showPublishError();
                CustomToast.showFailToast("推流地址获取失败,code=" + code + ", message=" + message);

                LiveXdcsUtil.doXDCS(TAG, "requestLiveRoomPushInfo error, errorCode=" + code
                        + ", message=" + message
                        + ", roomId=" + getRoomId()
                        + "，liveId=" + getLiveId());
            }
        });
    }


    public void tryStartPublish() {
        if (gateWapCheckReady) {
            startPublish();
        }
    }

    private void tryToStartLiveAndChatRoom() {
        if (gateWapCheckReady) {
            if (mLiveRecordInfo.status == PersonLiveBase.LIVE_STATUS_NOTICE) {
                LamiaHelper.startRecord(getActivity(), mLiveRecordInfo.id, mLiveRecordInfo.roomId,
                        new LamiaHelper.DoActionCallback() {
                            @Override
                            public void onSuccess() {//启动直播成功
                                if (!canUpdateUi())
                                    return;
                                mLiveRecordInfo.status = PersonLiveBase.LIVE_STATUS_ING;
                                tryToShowLiveStart();//step 推流一成功就开始显示计时（并且直播的状态已是开始状态），提示直播开始

                                //上报AI礼物信息
                                if (mLiveMediaType == LiveMediaType.TYPE_VIDEO) {
                                    AIGiftShowManager.getInstance().setHostLiveId(mLiveId);
                                    AIGiftShowManager.getInstance().checkAndReportAiGiftStatus();
                                }
                            }

                            @Override
                            public void onCancel() {//启动直播出错，取消
                                hasStarted = false;
                                dismissAllDialog();
                                finish();
                            }

                            @Override
                            public boolean canUpdateMyUi() {
                                return canUpdateUi();
                            }
                        });
            } else if (mLiveRecordInfo.status == PersonLiveBase.LIVE_STATUS_ING) {

                //上报AI礼物信息
                if (mLiveMediaType == LiveMediaType.TYPE_VIDEO) {
                    AIGiftShowManager.getInstance().setHostLiveId(mLiveId);
                    AIGiftShowManager.getInstance().checkAndReportAiGiftStatus();
                }
            }
        }
    }

    private void requestStartLive() {
        final HashMap<String, String> params = new HashMap<>();
        params.put("id", "" + mLiveRecordInfo.id);
        params.put("roomId", "" + mLiveRecordInfo.roomId);
        CommonRequestForLive.startPersonLiveById(params, new IDataCallBack<Integer>() {
            @Override
            public void onSuccess(Integer object) {
                if (!canUpdateUi())
                    return;
                if (object != null && object == 0) {
                    mLiveRecordInfo.status = PersonLiveBase.LIVE_STATUS_ING;

                    tryToShowLiveStart();//step 推流一成功就开始显示计时（并且直播的状态已是开始状态），提示直播开始
                } else {
                    mLiveRecordInfo.status = PersonLiveBase.LIVE_STATUS_NOTICE;
                    showRequestAgain();
                }
            }

            @Override
            public void onError(int code, String message) {
                LiveXdcsUtil.doXDCS(TAG, "requestStartLive error, errorCode=" + code
                        + ", message=" + message
                        + ", roomId=" + getRoomId()
                        + "，liveId=" + getLiveId());
            }
        });
    }

    private void requestStopLive() {
        if (isLiveStopping) return;
        isLiveStopping = true;
        final HashMap<String, String> params = new HashMap<>();
        params.put("id", "" + mLiveId);
        CommonRequestForLive.stopPersonLiveById(params, new IDataCallBack<Integer>() {
            @Override
            public void onSuccess(Integer object) {
                CustomToast.showToast("停止直播");
                hasStarted = false;
                isLiveStopping = false;
                releaseResource(false);
                gotHostFinishFragment();
            }

            @Override
            public void onError(int code, String message) {
                isLiveStopping = false;
                CustomToast.showFailToast(message);
            }
        });
    }

    private void startPublish() {
        if (mZegoRoomInfo == null || !UserInfoMannage.hasLogined()) {
            CustomToast.showDebugFailToast("startPublish error mZegoRoomInfo null");
            showPublishError();
            return;
        }

        ZegoUserInfo zegoUserInfo = mZegoRoomInfo.toZegoUserInfo();

        final MicStreamInfo params = new MicStreamInfo();
        params.setMixId(zegoUserInfo.mixPublishStreamId);
        params.setRoomId(mZegoRoomInfo.getRoomId());
        params.setStreamId(mZegoRoomInfo.getStreamId());

        params.setUserId(zegoUserInfo.userID);
        params.setNickName(zegoUserInfo.userName);

        params.setContext(getActivity());
        params.setRole(Role.ANCHOR);

        params.setAppId(mZegoRoomInfo.getAppIdStr());
        String keyset = new String(mZegoRoomInfo.getSignKey(), StandardCharsets.ISO_8859_1);
        params.setAppKey(keyset);

        MixStreamConfig xmAVMixStreamConfig = getXmAVMixStreamConfig();
        if (xmAVMixStreamConfig != null) {
            params.setMixStreamConfig(xmAVMixStreamConfig);
            IMicBaseComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
            if (component != null) {
                component.setMixStreamConfig(xmAVMixStreamConfig);
            }
        }

        operateAfterSDKInit(params);
    }


    private MixStreamConfig mMixStreamConfig;

    protected MixStreamConfig getXmAVMixStreamConfig() {

        if (mMixStreamConfig == null) {
            mMixStreamConfig = new MixStreamConfig();

            XmZegoConstants.XmAvConfig zegoAvConfig = new XmZegoConstants.XmAvConfig(XmZegoConstants.XmAvConfig.Level.VeryHigh);
            mMixStreamConfig.outputFps = 20;
            mMixStreamConfig.outputWidth = zegoAvConfig.getVideoCaptureResolutionWidth();
            mMixStreamConfig.outputHeight = zegoAvConfig.getVideoCaptureResolutionHeight();
            mMixStreamConfig.outputBitrate = 2000000;
            mMixStreamConfig.outputQuality = 23;
            mMixStreamConfig.outputAudioBitrate = 128000;
            mMixStreamConfig.outputRateControlMode = 0;
            mMixStreamConfig.outputAudioConfig = 1;
        }

        return mMixStreamConfig;
    }


    protected boolean isOpenPreview = false;


    private void operateAfterSDKInit(MicStreamInfo params) {

        IXmMicService avService = getAvService();

        if (avService == null) {
            return;
        }

        //设置zegosdk的运行环境
        if (ConstantsOpenSdk.isDebug) {
            if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
                avService.setTestEnv(false);
            } else {
                avService.setTestEnv(true);
            }
        }

        if (mLiveMediaType == LiveMediaType.TYPE_VIDEO) {
            setZegoVideoAVConfig();

            if (!isOpenPreview) {
                //先开启预览
                initAndStartVideoPreview();
                isOpenPreview = true;
            }
        }
        JSONObject object = new JSONObject();
        try {
            object.put("isOnlyAudio", mLiveMediaType != LiveMediaType.TYPE_VIDEO);
            object.put("uid", UserInfoMannage.getUid());
            if (mLiveRecordInfo != null) {
                object.put("isAnchor", true);
            }
            params.setExtraInfo(object.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

        mAnchorAsrCallBack = new AnchorAsrCallBack(this);
        LiveAsrProcessor liveAsrProcessor = LiveAsrProcessor.Companion.getInstance();
        liveAsrProcessor.setAsrCallback(mAnchorAsrCallBack);
        XmLiveAudioPreProcessor xmLiveAudioPreProcessor = XmLiveAudioPreProcessor.INSTANCE;
        xmLiveAudioPreProcessor.setAsrProcessor(liveAsrProcessor);
        avService.setAudioFramePreProcessor(xmLiveAudioPreProcessor);

        //最后join房间进行推流
        avService.joinRoom(params, mLiveMediaType != LiveMediaType.TYPE_VIDEO);

    }

    /**
     * 主播端asr 回调
     */
    private class AnchorAsrCallBack implements IAsrCallback {

        // 使用弱引用持有Fragment
        private final WeakReference<LiveAnchorRoomBaseFragment> fragmentRef;

        public AnchorAsrCallBack(LiveAnchorRoomBaseFragment fragment) {
            this.fragmentRef = new WeakReference<>(fragment);
        }

        @Override
        public void onInitSuccess() {
            Logger.i(TAG, "AnchorAsrCallBack onInitSuccess");
        }

        @Override
        public void onInitFailure(@NonNull String error) {
            Logger.i(TAG, "AnchorAsrCallBack onInitFailure error=" + error);
            LiveXdcsUtil.doXDCS(TAG, "AnchorAsrCallBack onInitFailure, error=" + error
                    + ", roomId=" + getRoomId()
                    + "，liveId=" + getLiveId());
        }

        @Override
        public void onStartSuccess() {
            Logger.i(TAG, "AnchorAsrCallBack onStartSuccess");
        }

        @Override
        public void onStop() {
            Logger.i(TAG, "AnchorAsrCallBack onStop");
        }

        @Override
        public void onResult(@NonNull String result, int optType, long ts) {
            LiveAnchorRoomBaseFragment fragment = fragmentRef.get();
            if (fragment == null || !fragment.canUpdateUi()) {
                return;
            }
            Logger.i(TAG, "AnchorAsrCallBack onResult result=" + result + ", optType=" + optType);
            postOnUiThread(() -> {
                IInteractivePlayComponent interactivePlayComponent = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
                if (null != interactivePlayComponent) {
                    String anchorName = "";
                    if(null != getRoomDetail() && null != getRoomDetail().getHostNickname()){
                        anchorName = getRoomDetail().getHostNickname();
                    }
                    interactivePlayComponent.updateAsrResult(String.valueOf(getHostUid()), result, optType, anchorName, ts); // 发送新增内容
                }
            });

            IXmMicService avService = getAvService();
            if (null == avService) {
                return;
            }

            LiveMediaAsrInfo mediaSideInfo = new LiveMediaAsrInfo();
            mediaSideInfo.setType(LiveMediaAsrInfo.TYPE_ASR_MODE);
            LiveMediaAsrInfo.MediaAsrInfoContent.UserInfoBean userInfo = new LiveMediaAsrInfo.MediaAsrInfoContent.UserInfoBean();
            if (null != UserInfoMannage.getInstance().getUser()) {
                userInfo.setNickname(UserInfoMannage.getInstance().getUser().getNickname());
            }

            userInfo.setUid(getRoomCore().getHostUid());
            LiveMediaAsrInfo.MediaAsrInfoContent asrInfoContent = new LiveMediaAsrInfo.MediaAsrInfoContent();
            asrInfoContent.setOptType(optType);
            asrInfoContent.setText(result);
            asrInfoContent.setUserInfo(userInfo);
            asrInfoContent.setTs(ts);
            mediaSideInfo.setContent(asrInfoContent);
            avService.sendMediaSideInfo(GsonUtils.toJson(mediaSideInfo));
        }

        @Override
        public void onError(int errorCode, @NonNull String errorMessage) {
            LiveAnchorRoomBaseFragment fragment = fragmentRef.get();
            if (fragment == null || !fragment.canUpdateUi()) {
                return;
            }
            Logger.i(TAG, "AnchorAsrCallBack onError errorCode=" + errorCode + ", errorMessage=" + errorMessage);
            postOnUiThread(() -> {
                IInteractivePlayComponent interactivePlayComponent = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
                if (null != interactivePlayComponent) {
                    String anchorName = "";
                    if(null != getRoomDetail() && null != getRoomDetail().getHostNickname()){
                        anchorName = getRoomDetail().getHostNickname();
                    }
                    interactivePlayComponent.updateAsrResult(String.valueOf(getHostUid()), "AI正在识别中...", LiveMediaAsrInfo.OPT_TYPE_ERROR, anchorName, 0); // 发送新增内容
                }
            });

            IXmMicService avService = getAvService();
            if (null == avService) {
                return;
            }

            LiveMediaAsrInfo mediaSideInfo = new LiveMediaAsrInfo();
            mediaSideInfo.setType(LiveMediaAsrInfo.TYPE_ASR_MODE);
            LiveMediaAsrInfo.MediaAsrInfoContent.UserInfoBean userInfo = new LiveMediaAsrInfo.MediaAsrInfoContent.UserInfoBean();
            if (null != UserInfoMannage.getInstance().getUser()) {
                userInfo.setNickname(UserInfoMannage.getInstance().getUser().getNickname());
            }

            userInfo.setUid(getRoomCore().getHostUid());
            LiveMediaAsrInfo.MediaAsrInfoContent asrInfoContent = new LiveMediaAsrInfo.MediaAsrInfoContent();
            asrInfoContent.setText("AI正在识别中...");
            asrInfoContent.setUserInfo(userInfo);
            asrInfoContent.setOptType(LiveMediaAsrInfo.OPT_TYPE_ERROR);
            mediaSideInfo.setContent(asrInfoContent);
            avService.sendMediaSideInfo(GsonUtils.toJson(mediaSideInfo));
        }
    }

    protected abstract void initAndStartVideoPreview();

    private void setZegoVideoAVConfig() {

        if (getAvService() == null) {
            return;
        }

        XmZegoConstants.XmAvConfig zegoAvConfig = new XmZegoConstants.XmAvConfig(XmZegoConstants.XmAvConfig.Level.VeryHigh);

        VideoAvConfig videoAvConfig = new VideoAvConfig();
        videoAvConfig.mVideoFPS = 20;
        videoAvConfig.mVideoBitrate = 2000000;
        videoAvConfig.mVideoEncodeResolutionWidth = zegoAvConfig.getVideoEncodeResolutionWidth();
        videoAvConfig.mVideoEncodeResolutionHeight = zegoAvConfig.getVideoEncodeResolutionHeight();
        videoAvConfig.mVideoCaptureResolutionWidth = zegoAvConfig.getVideoCaptureResolutionWidth();
        videoAvConfig.mVideoCaptureResolutionHeight = zegoAvConfig.getVideoCaptureResolutionHeight();

        getAvService().setVideoAvConfig(videoAvConfig);

    }

    protected void handPublishStart() {
        IXmMicService avService = getAvService();
        if (avService == null) {
            return;
        }

        tryPublishSuccess = true;
        //step 推流一成功就开始显示计时（并且直播的状态已是开始状态），提示直播开始
        tryToShowLiveStart();

        // 设置耳返
        boolean isHeadSetOn = PhoneCallNetworkAndHeadSetStateMonitor.isHeadSetOn(mContext);
        avService.enableLoopback(isHeadSetOn);

        // 推流一成功就开始，开始请求主播连麦信息
        if (RoomModeManager.isPkMode()) {
            LivePkHelper.getInstance().syncMicStatus();
        }
    }

    /**
     * 关闭直播时释放资源
     * <p>
     * 注意：正常关闭直播，主播被踢（直播在其他设备上直播会导致该设备被踢），都会触发释放资源；但是主播被踢掉，Zego 不更新混流列表
     *
     * @param isKickOut true 主播被踢 false 主播正常关闭直播
     */
    protected void releaseResource(boolean isKickOut) {
        if (isKickOut) {
            releaseSDKResourse(false);
            LivePkHelper.releaseInstance();
            LoveModeManager.releaseInstance();
        } else {
            releaseSDKResourse(true);
        }

    }

    /**
     * 调用服务端开始直播接口成功 && Zego推流成功 && 倒计时结束
     */
    public void tryToShowLiveStart() {
        IHostCountDownComponent component = getComponentSafety(AnchorCompConfig.COMPONENT_HOST_COUNT_DOWN);
        if (tryPublishSuccess
                && component != null
                && component.isCountDownFinish()
                && mLiveRecordInfo.status == PersonLiveBase.LIVE_STATUS_ING &&
                canUpdateUi()) {

            Logger.i(TAG, "tryToShowLiveStart");

            showLiveStart();
        }
    }

    private void initViews() {
        AutoTraceHelper.bindData(findViewById(R.id.live_lamia_room_container), AutoTraceHelper
                .MODULE_DEFAULT, "");
    }

    protected void startTiming() {

    }

    /**
     * 切换房间模式，普通直播间和交友/ Pia 戏直播间
     *
     * @param newMode -1 时为交友切普通直播间，-2 时为Pia戏切普通直播间，否则切换交友/ Pia 戏直播间
     */
    protected void changeRoomMode(int newMode) {
        boolean isLoveMode = newMode == LoveMode.LOVE_MODE_NONE.getValue();
        final boolean isPiaMode = newMode == LoveMode.LOVE_MODE_PIA.getValue();
        final boolean closeFromPia = newMode == LoveModeUIManager.Mode.CLOSE_PIA_MODE;
        if (isLoveMode) {
            // 如果当前为 Pia 戏模式，需要提示先关闭 Pia 戏模式
            if (RoomModeManager.isPiaMode()) {
                CustomToast.showFailToast("请先关闭Pia戏模式，再开启交友模式哦");
                return;
            }
            // 开启交友模式，先进行权限校验
            CommonRequestForLive.checkLiveAudioFriendModePassport(new IDataCallBack<List<Boolean>>() {
                @Override
                public void onSuccess(@Nullable List<Boolean> passport) {
                    if (!canUpdateUi()) return;
                    if (passport == null || passport.size() != 2 || !passport.get(0)) {
                        // 没有白名单权限，跳转申请页面
                        LiveRouterUtil.startWebViewFragment(
                                (MainActivity) BaseApplication.getMainActivity(),
                                LiveUrlConstants.getInstance().getLiveFriendModeRequestUrl(),
                                false
                        );
                    } else if (passport.get(1)) {
                        // 权限校验均通过，开启交友模式
                        new LiveOpenFriendsModeDialog.Builder()
                                .setContext(getActivity())
                                .setFragmentManager(getChildFragmentManager())
                                .setOpenModeListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        requestOpenOrCloseLoveMode(false);

                                        LiveXdcsUtil.doXDCS(TAG, "Click to open love mode"
                                                + ", roomId=" + getRoomId()
                                                + "，liveId=" + getLiveId());
                                    }
                                })
                                .build()
                                .show("open-friends-mode");
                    } else {
                        // 被拉黑名单
                        CustomToast.showFailToast("由于您违反了直播协议已被取消权限，有疑问请联系客服");
                    }
                }

                @Override
                public void onError(int code, String message) {
                    CustomToast.showFailToast("开启失败(" + code + "): " + message);
                }
            });
        } else if (isPiaMode) {
            // 如果当前为交友模式，需要提示先关闭交友模式
            if (RoomModeManager.isFriendsMode() && !RoomModeManager.isPiaMode()) {
                CustomToast.showFailToast("请先关闭交友模式，再开启Pia戏哦");
                return;
            }
            // 开启 Pia 戏模式
            new LiveOpenFriendsModeDialog.Builder()
                    .setContext(getActivity())
                    .setFragmentManager(getChildFragmentManager())
                    .setTitle("Pia戏模式")
                    .setContent(LiveOpenFriendsModeDialog.PIA_MODE_DESC)
                    .setOpenModeListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            tracePiaBtnClick();
                            requestOpenOrCloseLoveMode(true);

                            LiveXdcsUtil.doXDCS(TAG, "Click to open love (pia) mode"
                                    + ", roomId=" + getRoomId()
                                    + "，liveId=" + getLiveId());
                        }
                    })
                    .build()
                    .show("open-friends-mode");
        } else {
            // 关闭交友/ Pia 戏模式
            String msg;
            if (closeFromPia) {
                msg = "确定要关闭Pia模式吗？";
            } else {
                msg = "确定要关闭交友模式吗？";
            }

            if (getContext() != null) {
                LiveSimpleConfirmDialog.Companion.show(
                        getContext(), getChildFragmentManager(),
                        new LiveSimpleConfirmDialogListener() {
                            @Override
                            public void onDialogParamsPrepare(
                                    @NonNull LiveSimpleConfirmDialogParams params
                            ) {
                                params.setContent(msg);
                                params.setPositiveBtnContent("关闭");
                                params.setCanceledOnTouchOutside(false);
                            }

                            @Override
                            public void onClickConfirm() {
                                requestOpenOrCloseLoveMode(closeFromPia);
                                LiveXdcsUtil.doXDCS(TAG, "Click to close love mode"
                                        + ", roomId=" + getRoomId()
                                        + "，liveId=" + getLiveId());
                            }
                        }
                );
            }
        }
    }

    private void tracePiaBtnClick() {
        // 直播间-Pia 戏模式-马上开启  点击事件
        new XMTraceApi.Trace()
                .click(40083)
                .put("currPage", "liveRoom")
                .put("liveId", getLiveId() + "")
                .put("liveRoomName", mRoomDetail.getRoomTitle())
                .put("liveRoomType", LiveMediaType.TYPE_AUDIO + "")
                .put("anchorId", getHostUid() + "")
                .put("roomId", getRoomId() + "")
                .createTrace();
    }

    protected void requestOpenOrCloseLoveMode(boolean isPiaSubMode) {

    }

    @Override
    public void onPiaModeOpen() {
        super.onPiaModeOpen();

        // 清空之前的配乐信息，如果在播放，停止播放
        if (getAvService() != null && getAvService().getAudioEffectManager() != null) {
            getAvService().getAudioEffectManager().getLiveBGMPlayer().stop();
        }
        if (getRoomCore() != null) {
            getRoomCore().releaseBgmCore();
            dismissBgmDialog(true);
        }
    }

    @Override
    public void onPiaModeClose() {
        super.onPiaModeClose();

        // 清空 BGM 信息，如果在播放，停止播放
        XmPiaBgmPlayerManager.Companion.getInstance().destroy();
    }

    private FriendsGiftDialog mLoveGiftDialog;
    private long mLastMicUserId;

    public SendGiftDialog getLoveGiftDialog(long micUserId) {
        if (mLoveGiftDialog == null) {
            mLoveGiftDialog = new FriendsGiftDialog.SendBuilder(mActivity, micUserId)
                    .setSendType(ParamsConstantsInLive.SEND_GIFT_TYPE_MAKE_FRIENDS)
                    .setLiveId(mLiveRecordInfo.id)
                    .setRoomId(mLiveRecordInfo.roomId)
                    .setReceiverUid(mLiveUserInfo.uid)
                    .setHostUid(mLiveUserInfo.uid)
                    .setIsLiveAnchor(PreferenceConstantsInLive.LiveAnchorType.TYPE_ANCHOR)
                    .setBizType(mLiveRecordInfo.bizType)
                    .setIsFollowed(mModel.isFollowed())
                    .build();
            mLoveGiftDialog.setRepeatHitHand(getHitHand(mLoveGiftDialog, new GiftHitFinishCallback() {
                @Override
                public void onHitFinished() {
                    getLoveGiftDialog(mLastMicUserId).show();
                }
            }));
        } else {
            mLoveGiftDialog.setFriendsMicUid(micUserId);
            mLoveGiftDialog.setRoomId(mLiveRecordInfo.roomId);
            mLoveGiftDialog.setLiveId(mLiveRecordInfo.id);
            mLoveGiftDialog.setChatId(mLiveRecordInfo.chatId);
            mLoveGiftDialog.setReceiverUid(mLiveUserInfo.uid);
            mLoveGiftDialog.setHostUid(mLiveUserInfo.uid);
        }
        mLastMicUserId = micUserId;

        return mLoveGiftDialog;
    }

    @Override
    public void showFriendGiftPanel() {
        getLoveGiftDialog(mLastMicUserId).show();
    }

    private SendGiftDialog.IInteractionFragment getHitHand(SendGiftDialog sendGiftDialog,
                                                           GiftHitFinishCallback callback) {
        return new GiftRepeatHandImpl(sendGiftDialog, new GiftRepeatHandImpl.IRoom() {

            @Override
            public void onHitButtonVisibilityChanged(int vis) {

            }

            @Override
            public void onFirstSendRepeatGiftSuccess() {

            }

            @Override
            public void onSendEnd() {

            }

            @Override
            public boolean canUpdateUi() {
                return LiveAnchorRoomBaseFragment.this.canUpdateUi();
            }

            @Override
            public FragmentManager getChildFragmentManager() {
                return LiveAnchorRoomBaseFragment.this.getChildFragmentManager();
            }
        }, callback);
    }

    /**
     * 背景配乐
     */
    protected void showBgmDialog() {
        if (getRoomCore() == null) {
            CustomToast.showDebugFailToast("RoomCore 为空");
            return;
        }

        if (mLiveBgmDialogWrapper == null || mLiveBgmDialogWrapper.isEmpty()) {
            ILiveBgmCore bgmCore = getRoomCore().getBgmCore();
            LiveBgMusicListFragment liveHostMusicListPage = new LiveBgMusicListFragment(bgmCore);
            liveHostMusicListPage.setMusicDialogCallback(this::addBgm);
            mLiveBgmDialogWrapper = VerticalSlideUtil.buildSlideWrapper(liveHostMusicListPage)
                    .setBgResource(R.drawable.livecomm_bg_bgm_panel_dialog)
                    .setShowSlideView(false)
                    .setHeight(liveHostMusicListPage.getDialogHeight())
                    .setRemoveIfDismiss(false)
                    .setAutoRelease(false);
        }

        mLiveBgmDialogWrapper.show(getChildFragmentManager(), "music");
    }


    /**
     * 打开收入记录
     */
    protected void showIncomeRecordDialogFragment() {
        if (mLiveRecordInfo == null || mLiveUserInfo == null) {
            return;
        }

        LiveHostIncomeRecordFragment fragment = LiveHostIncomeRecordFragment.newInstance
                (mLiveRecordInfo.id, mLiveUserInfo.uid);
        VerticalSlideUtil.VerticalSlideWrapper<LiveHostIncomeRecordFragment> wrapper
                = VerticalSlideUtil.buildSlideWrapper(fragment)
                .setHeight(BaseUtil.dp2px(getContext(), 470))
                .setOnlyScrollFromTop(true)
                .setShowSlideView(false)
                .setBgResource(com.ximalaya.ting.android.live.common.R.color.live_color_161616)
                .setTopHeight(BaseUtil.dp2px(mContext, 15));
        wrapper.show(getChildFragmentManager(), "income-list");
        mLiveIncomeRecordWrapper = new WeakReference<>(wrapper);
    }

    protected void showPkStartMatchDialog() {
        if (mLiveRecordInfo == null || mLiveUserInfo == null || !canUpdateUi()) {
            return;
        }

        int mediaType = LiveMediaType.TYPE_AUDIO;
        if (mRoomDetail != null) {
            mediaType = mRoomDetail.getMediaType();
        }
        mLiveStartMatchDialog = PkStartMatchFragment.newInstance(mLiveRecordInfo.id, mLiveRecordInfo.roomId, mLiveUserInfo.uid, mediaType);
        int finalMediaType = mediaType;
        mLiveStartMatchDialog.setRandomPkStartListener(new PkStartMatchFragment.IStartPKListener() {
            @Override
            public void micStartPk(boolean isVideoRankPk, boolean isQualifier, int pkMode) {
                IMicBaseComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
                if (component == null) {
                    return;
                }
                component.checkNeedShowSwitchModeWarningDialog(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (isVideoRankPk) {
                            LiveClientManager.getInstance().getLiveMicService().stopMic(new ISendCallback() {
                                @Override
                                public void onSendSuccess() {
                                    CustomToast.showDebugFailToast("已关闭连麦");
                                    LivePkHelper.getInstance().startPkMatch(pkMode, 0, LiveMediaType.TYPE_VIDEO, RandomPkType.RANDOM_PK_ORDINARY.getValue());
                                }

                                @Override
                                public void onSendError(int code, String message) {
                                    CustomToast.showDebugFailToast("关闭连麦失败");
                                }
                            });
                        } else {
                            if (isQualifier) {
                                LivePkHelper.getInstance().startPkMatch(pkMode, 0, finalMediaType, RandomPkType.RANDOM_PK_QUALIFIER.getValue());
                                return;
                            }
                            mLiveStartMatchDialog.showRandomPkSelectView();
                        }
                    }
                });
            }

            @Override
            public void showPkPredictRecord() {
                showPkPredictRecordPage();
            }

        });
        VerticalSlideUtil.VerticalSlideWrapper<PkStartMatchFragment> wrapper = VerticalSlideUtil.buildSlideWrapper(mLiveStartMatchDialog)
                .setRemoveIfDismiss(true)
                .setShowSlideView(false)
                .setOnlyScrollFromTop(true)
                .setBgResource(com.ximalaya.ting.android.live.common.R.color.live_color_262626)
                .setTopHeight(0)
                .setHeight(BaseUtil.dp2px(mContext, 460));

        wrapper.show(getChildFragmentManager(), "StartMatchDialogFragment");

        new XMTraceApi.Trace()
                .setMetaId(33503)
                .setServiceId("dialogView")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    private void showPkPredictRecordPage() {
        if (mRoomDetail == null || !canUpdateUi()) {
            return;
        }

        String url = LiveUrlConstants.getInstance().getPkPredictRecordH5Url();
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "roomId=" + mRoomDetail.getRoomId());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "liveId=" + mRoomDetail.getLiveId());
        url = LiveWebUtil.appendQueryParamToUriIfKeyNotExist(url, "anchorUid=" + mRoomDetail.getHostUid());

        FragmentManager fragmentManager = getChildFragmentManager();

        FragmentTransaction fragTransaction = fragmentManager.beginTransaction();
        Fragment fragment = fragmentManager.findFragmentByTag(ProvideForH5CustomerDialogFragment.TAG);

        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_POSITION, ProvideForH5CustomerDialogFragment.POSITION_BOTTOM);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_HEIGHT, 445);
        bundle.putString(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_EXTRAURL, url);
        bundle.putInt(BundleKeyConstants.KEY_LIVE_PROVIDE_H5_CUSTOMER_DIALOG_SHOWCLOSE, ProvideForH5CustomerDialogFragment.SHOWCLOSE_NO);

        mPkPredictRecordFragment = ProvideForH5CustomerDialogFragment.newInstance(bundle);
        try {
            if (fragment != null) {
                fragTransaction.remove(fragment);
                fragTransaction.commitNowAllowingStateLoss();
            }

            mPkPredictRecordFragment.showNow(fragmentManager, ProvideForH5CustomerDialogFragment.TAG);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void addBgm() {
        if (mLiveBgmDialogWrapper == null) {
            CustomToast.showDebugFailToast("wrapper is null!!");
            return;
        }
        mLiveBgmDialogWrapper.dismiss();

        Router.getActionByCallback(Configure.BUNDLE_MUSIC, new Router.BundleInstallCallbackWrapper() {

            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (!canUpdateUi()) {
                    return;
                }

                try {
                    // 直播 跳转配乐页面
                    BaseFragment addBgMusicFragment = Router.<MusicActionRouter>getActionRouter(
                            Configure.BUNDLE_MUSIC
                    ).getFragmentAction().newAddMusicFragment(
                            LiveAnchorRoomBaseFragment.this,
                            mLiveBgmDialogWrapper.mContentFragment.getSelectedBgMusicList(),
                            IMusicFragmentAction.SCENE_RECORD, IMusicFragmentAction.LIVE_RADIO);
                    startFragment(addBgMusicFragment);
                    needShowBgmDialog = true;

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, true, BundleModel.DOWNLOAD_ASK_USER);

    }

    private void dismissBgmDialog(boolean isDestroy) {
        if (mLiveBgmDialogWrapper != null) {
            if (isDestroy && mLiveBgmDialogWrapper.mContentFragment != null) {
                mLiveBgmDialogWrapper.mContentFragment.release();
            }
            mLiveBgmDialogWrapper.dismiss();
            if (isDestroy) {
                mLiveBgmDialogWrapper = null;
            }
        }
    }

    private void showBgmAfterAddMusic() {
        if (needShowBgmDialog) {
            showBgmDialog();
            needShowBgmDialog = false;
        }
    }


    @Override
    protected boolean setNetworkErrorButtonVisiblity() {
        return false;
    }

    @Override
    public void onClick(View v) {

    }

    private long mLastSyncPkMicStatusTimestamp;

    /**
     * 视频pk会在overPk结束后就清空panelSync数据，因此需要自己维护开始拉流的mVisitorStreamId
     */
    private String mVisitorStreamId;
    private long matchedUserId;

    /**
     * 处理主播端连麦状态同步消息
     *
     * @param micStatusSyncRsp 连麦状态同步消息
     */
    public void handlePkMicStatusResult(CommonPkMicStatusRsp micStatusSyncRsp) {
        if (!canUpdateUi() || micStatusSyncRsp == null) {
            return;
        }

        if (mLastSyncPkMicStatusTimestamp > micStatusSyncRsp.mTimeStamp) {
            return;
        }

        mLastSyncPkMicStatusTimestamp = micStatusSyncRsp.mTimeStamp;

        IXmMicService avService = getAvService();

        if (avService == null) {
            return;
        }

        LiveXdcsUtil.doXDCS(TAG, "handlePkMicStatusResult " + micStatusSyncRsp);

        if (micStatusSyncRsp.mMicStatus == MicStatus.MIC_STATUS_OPEN.getValue()) {
            if (micStatusSyncRsp.mVisitorUserId <= 0 || TextUtils.isEmpty(micStatusSyncRsp.mVisitorStreamId)) {
                return;
            }
            matchedUserId = micStatusSyncRsp.mVisitorUserId;
            mVisitorStreamId = micStatusSyncRsp.mVisitorStreamId;
            boolean ret = avService.setPlayVolume(micStatusSyncRsp.mute ? 0 : 100, mVisitorStreamId);
            if (!ret) {
                ConnectLogUtilWrapper.log(getRoomBizType(), TAG + " setPlayVolume failed");
            }
            avService.startPlayOtherStreams(getOtherRoomStreamList(String.valueOf(matchedUserId), micStatusSyncRsp.mVisitorStreamId,
                    micStatusSyncRsp.mVisitorMediaType == LiveMediaType.TYPE_AUDIO));

            return;
        }

        if (micStatusSyncRsp.mMicStatus == MicStatus.MIC_STATUS_CLOSE.getValue()) {
            if (matchedUserId == 0L) {
                matchedUserId = micStatusSyncRsp.mVisitorUserId;
            }
            if (TextUtils.isEmpty(mVisitorStreamId)) {
                mVisitorStreamId = micStatusSyncRsp.mVisitorStreamId;
            }
            if (matchedUserId <= 0) {
                return;
            }
            LiveLogUtil.logPullStream("stopPlayOtherStreams:uid=" + matchedUserId + "  streamID=" + mVisitorStreamId);
            avService.stopPlayOtherStreams(getOtherRoomStreamList(String.valueOf(matchedUserId), mVisitorStreamId, micStatusSyncRsp.mVisitorMediaType == LiveMediaType.TYPE_AUDIO));
            matchedUserId = 0;
            mVisitorStreamId = "";
        }
    }

    private List<StreamInfo> getOtherRoomStreamList(String matchedUserId, String matchedStreamId, boolean isOnlyAudio) {
        List<StreamInfo> streamInfoList = new ArrayList<>();
        StreamInfo streamInfo = new StreamInfo(matchedUserId, matchedStreamId, "", isOnlyAudio);
        streamInfoList.add(streamInfo);

        return streamInfoList;
    }

    /**
     * 处理PK邀请消息
     *
     * @param inviteeSyncResult
     */
    public void handlePkInviteeSyncResult(CommonPkInviteeSyncResult inviteeSyncResult) {
        if (!canUpdateUi() || inviteeSyncResult == null || mLiveRecordInfo == null) {
            return;
        }

        //关闭PK弹窗
        dismissPkStartMatchDialog();

        //保存被该主播邀请的次数
        int inviteCount = 0;
        if (inviteeSyncResult.inviterUserInfo != null) {
            ManualUserInfo inviterUserInfo = inviteeSyncResult.inviterUserInfo;
            Integer count = mInviteData.get(inviterUserInfo.userId);
            inviteCount = count != null ? count : 0;
            mInviteData.put(inviterUserInfo.userId, ++inviteCount);
        }

        mInvitePkDialog = new ReceiveInviteePkDialog(getActivity(), getLiveMediaType());
        mInvitePkDialog.setInviteeData(inviteeSyncResult);
        if (inviteCount > 1) {
            mInvitePkDialog.isRejectInvite(true);
        }
        mInvitePkDialog.setCanceledOnTouchOutside(false);
        mInvitePkDialog.setCancelable(false);
        mInvitePkDialog.setOwnerActivity(getActivity());
        mInvitePkDialog.show();
    }

    /**
     * 处理邀请者结果消息
     *
     * @param inviterResult
     */
    public void handlePkInviterResult(CommonPkInviterResult inviterResult) {
        if (!canUpdateUi() || inviterResult == null) {
            return;
        }
        showInvitePkResultDialog(inviterResult.msg);
    }

    private void showInvitePkResultDialog(String messageContent) {
        ViewGroup contentView = (ViewGroup) LayoutInflater.from(mContext)
                .inflate(com.ximalaya.ting.android.live.common.R.layout.livecomm_dialog_common, null);
        final SimpleDialog simpleDialog = new SimpleDialog(mActivity, contentView, Gravity.CENTER) {
            @Override
            public void onMyCreate() {
                super.onMyCreate();
                Window dialogWindow = getWindow();
                if (dialogWindow != null) {
                    WindowManager.LayoutParams lp = dialogWindow.getAttributes();
                    lp.width = BaseUtil.dp2px(mContext, 240);
                    lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
                    dialogWindow.setGravity(Gravity.CENTER);
                    dialogWindow.setAttributes(lp);
                    dialogWindow.setWindowAnimations(android.R.style.Theme_Holo_Dialog);
                }
            }

            @Override
            public void onClick(View view) {
                int id = view.getId();
                if (id == com.ximalaya.ting.android.live.R.id.live_cancel) {
                    dismiss();
                } else if (id == com.ximalaya.ting.android.live.R.id.live_ok) {
                    dismiss();
                    showPkSearchHostView();
                }
            }
        };
        contentView.findViewById(com.ximalaya.ting.android.live.R.id.live_cancel).setOnClickListener(simpleDialog);
        contentView.findViewById(com.ximalaya.ting.android.live.R.id.live_ok).setOnClickListener(simpleDialog);
        TextView tvMessage = contentView.findViewById(com.ximalaya.ting.android.live.R.id.live_content);
        (contentView.findViewById(com.ximalaya.ting.android.live.R.id.live_title)).setVisibility(View.GONE);
        (contentView.findViewById(com.ximalaya.ting.android.live.R.id.live_close)).setVisibility(View.GONE);
        ((TextView) contentView.findViewById(com.ximalaya.ting.android.live.R.id.live_cancel)).setText("知道了");
        ((TextView) contentView.findViewById(com.ximalaya.ting.android.live.R.id.live_ok)).setText("重新选择");
        tvMessage.setText(messageContent);
        tvMessage.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) tvMessage.getLayoutParams();
        layoutParams.topMargin = BaseUtil.dp2px(mContext, 40);
        tvMessage.setLayoutParams(layoutParams);
        simpleDialog.show();
    }

    /**
     * 处理被邀请者结果消息
     *
     * @param inviteeResult
     */
    public void handlePkInviteeResult(CommonPkInviteeResult inviteeResult) {
        if (!canUpdateUi() || inviteeResult == null) {
            return;
        }
        //被邀请方收到消息，需先关闭PK邀请弹窗
        if (mInvitePkDialog != null && mInvitePkDialog.isShowing()) {
            mInvitePkDialog.dismiss();
        }
        CustomToast.showFailToast(inviteeResult.msg);
    }

    public void handlePkStartMatchResult(boolean isSuccess, CommonPkStartMatchRsp startMatchRsp) {
        if (!canUpdateUi() || startMatchRsp == null) {
            return;
        }

        if (isSuccess) {
            dismissPkStartMatchDialog();
        } else {
            if (TextUtils.isEmpty(startMatchRsp.mReason)) {
                CustomToast.showFailToast("操作失败");
            } else {
                CustomToast.showFailToast(startMatchRsp.mReason);
            }
            LivePkHelper.getInstance().stopSyncMicStatus();
        }
    }

    public void dismissPkStartMatchDialog() {
        if (mLiveStartMatchDialog != null) {
            mLiveStartMatchDialog.dismiss();
            mLiveStartMatchDialog = null;
        }
    }

    @Override
    public void shareLive() {
        if (mModel != null) {
            mLiveRecordInfo = mModel.getLiveRecordInfo();
            if (mLiveRecordInfo != null && mLiveRecordInfo.id > 0) {
                //每次分享时都要注册回调监听，上传服务器，接收完就清除
                Long uid = UserInfoMannage.getInstance().getUser() != null ? UserInfoMannage
                        .getUid() : 0L;
                ShareUtils.registerShareResultAndUpload(getContext(), mLiveRecordInfo.roomId,
                        mLiveRecordInfo.chatId, mLiveRecordInfo.id, uid, null);

                try {
                    if (getActivity() != null) {
                        mShareDialog = ShareUtils.shareLive(getActivity(), mLiveRecordInfo.id,
                                mLiveRecordInfo
                                        .roomId,
                                ShareUtils.getLiveShareData(mModel),
                                ICustomShareContentType.SHARE_TYPE_LIVEPERSONAL, mLiveUserInfo != null ? mLiveUserInfo.uid : 0);

                        shareBuryPoint();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            CustomToast.showToast("获取数据中");
        }
    }

    private void shareBuryPoint() {
        new XMTraceApi.Trace()
                .setMetaId(33464)
                .setServiceId("dialogView")
                .put("currPage", "liveRoom")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    /**
     * 退出直播逻辑
     *
     * @return 是否拦截处理
     */
    protected boolean onExitBtnPress() {
        //退出直播弹框逻辑
        if (hasStarted && canUpdateUi()) {
            suggestRestartLiveOrNot();
            return true;
        } else {//直接退出
            gotHostFinishFragment();
            return super.onBackPressed();
        }
    }

    @Override
    public boolean onBackPressed() {
        if (getComponentSafety(AnchorCompConfig.COMPONENT_HOST_COUNT_DOWN) != null
                && !((IHostCountDownComponent) getComponentSafety(AnchorCompConfig.COMPONENT_HOST_COUNT_DOWN)).isCountDownFinish()) {
            //倒计时期间屏蔽返回键
            return true;
        }
        boolean superBackPress = super.onBackPressed();
        if (superBackPress) {
            return true;
        }
        return onExitBtnPress();
    }

    private void suggestRestartLiveOrNot() {
        if (suggestRequesting) {
            CustomToast.showToast("正在请求数据,请稍候");
            return;
        }
        suggestRequesting = true;
        Map<String, String> map = new HashMap<>();
        map.put(ParamsConstantsInLive.DEVICE, "android");
        map.put(ParamsConstantsInLive.ID, String.valueOf(mLiveId));
        CommonRequestForLive.suggestRestartLiveOrNot(map, new IDataCallBack<CheckRestart>() {
            @Override
            public void onSuccess(CheckRestart object) {
                Logger.i(TAG, "suggestRestartLiveOrNot  " + object);
                if (canUpdateUi()) {
                    if (object != null && object.getRet() == 0 && object.getData() > 10) {
                        showExitDialog();
                    } else {
                        showExitDialog();
                    }
                    suggestRequesting = false;
                }
            }

            @Override
            public void onError(int code, String message) {
                Logger.i(TAG, "suggestRestartLiveOrNot " + code + message);
                suggestRequesting = false;
                if (canUpdateUi()) {
                    showExitDialog();
                }
            }
        });
    }


    public abstract boolean isOnMicStatus();

    public abstract boolean isOnMicPKStatus();


    public  boolean isNewRankPk(){
        return RoomModeManager.isNewPkMode();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if(ConstantsOpenSdk.isDebug){
            LiveDebugFragment.addDebugEntrance((ViewGroup) getView());
        }
    }

    private void showExitDialog() {
        if (getActivity() == null) {
            return;
        }
        checkAndShowCloseAlertDialog();
    }

    private void checkAndShowCloseAlertDialog(){
        AnchorCloseRoomAlertDialog dialog = new AnchorCloseRoomAlertDialog();
        if (isNewRankPk()) {
            IInteractivePlayComponent interactivePlayComponent = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
            if (null != interactivePlayComponent) {
                int pkStatus = interactivePlayComponent.getNewRankPkStatus();
                if(pkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_ING){
                    List<Integer> exitPkCountData = interactivePlayComponent.getExitPkCountData();
                    if (exitPkCountData.size() >= 2) {
                        dialog.addLocalItem(AnchorCloseRoomAlertDialog.getRankPkIngItem(exitPkCountData.get(0),exitPkCountData.get(1)));
                    }
                }else if(pkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_MATCH_ING){
                    dialog.addLocalItem(AnchorCloseRoomAlertDialog.getRankPkMatchingItem());
                }
            }
        }else{
            if(RoomModeManager.isPkMode()){
                IInteractivePlayComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
                boolean isQualifierPkMode = component != null && component.isQualifierPkMode();
                if(isQualifierPkMode){
                    dialog.addLocalItem(AnchorCloseRoomAlertDialog.getQualifierPkItem());
                }else{
                    dialog.addLocalItem(AnchorCloseRoomAlertDialog.getNormalPkItem());
                }
            }
            if(isOnMicPKStatus()){
                dialog.addLocalItem(AnchorCloseRoomAlertDialog.getNormalPkItem());
            }
            if(isOnMicStatus()){
                dialog.addLocalItem(AnchorCloseRoomAlertDialog.getGroupMicItem());
            }
        }
        dialog.addDebug();
        Map<String,String> map = LiveHelper.buildTimeParams();
        map.put("roomId",String.valueOf(getRoomId()));
        map.put("bizType",String.valueOf(getRoomBizType()));
        dialog.syncData(map, new ILiveCommonDataCallback<List<AnchorCloseRoomAlertDialog.CloseRoomAlertItem>>() {
            @Override
            public void onDataCallback(@Nullable List<AnchorCloseRoomAlertDialog.CloseRoomAlertItem> items) {
                if(!canUpdateUi()){
                    return;
                }
                if(items == null || items.isEmpty()){
                    showNoFunctionDialog();
                }else{
                    showExitAlertDialog(dialog);
                }
            }
        });
    }

    protected void showExitAlertDialog(AnchorCloseRoomAlertDialog dialog){
        Fragment fragment = getChildFragmentManager().findFragmentByTag("AnchorCloseRoomAlertDialog");
        if(fragment instanceof DialogFragment){
            ((DialogFragment) fragment).dismissAllowingStateLoss();
        }
        dialog.setAnchorCloseRoomListener(new AnchorCloseRoomAlertDialog.IAnchorCloseRoomListener() {
            @Override
            public void onCloseRoomClick() {
                onExit(false);
            }

            @Override
            public void onCancelCloseClick() {

            }
        });
        dialog.show(getChildFragmentManager(),"AnchorCloseRoomAlertDialog");
    }

    protected void showNoFunctionDialog(){
        if(MainApplication.getMainActivity() == null){
            return;
        }
        String title = "一大批观众正在赶来，确定结束本场直播？";
        String content = "";
        LiveHostExitConfirmDialog closeConfirmDialog = new LiveHostExitConfirmDialog(MainApplication.getMainActivity(), title, content,
                new LiveHostExitConfirmDialog.IClickListener() {
                    @Override
                    public void onClickOK() {
                        onExit(false);
                    }

                    @Override
                    public void onClickCancel() {

                    }
                });
        closeConfirmDialog.setCanceledOnTouchOutside(true);
        closeConfirmDialog.setOkText("结束直播");
        closeConfirmDialog.setCancelText("取消");
        closeConfirmDialog.show();
    }

    private void onExit(boolean restart) {
        if (restart) {
            releaseResource(false);
            hasStarted = false;
            shallRestart = true;
            finish();
            return;
        }
        Logger.i(TAG, "onExit");
        requestStopLive();

    }

    protected void dismissAllDialog() {
        if (mShareDialog != null && mShareDialog.isShowing()) {
            mShareDialog.dismiss();
        }
        if (mDialogBuilder != null) {
            mDialogBuilder.cancle();
        }

        ICommonTuningComponent component = getComponentSafety(
                IBaseRoomCompConfig.COMPONENT_COMMON_TUNING
        );
        if (component != null) component.closeAllPanel();

        dismissBgmDialog(false);

        if (mLiveAdminListFraWrapper != null && mLiveAdminListFraWrapper.get() != null) {
            mLiveAdminListFraWrapper.get().dismiss();
        }
        if (mLiveBanListFraWrapper != null && mLiveBanListFraWrapper.get() != null) {
            mLiveBanListFraWrapper.get().dismiss();
        }
        if (mLiveIncomeRecordWrapper != null && mLiveIncomeRecordWrapper.get() != null) {
            mLiveIncomeRecordWrapper.get().dismiss();
        }
        dismissPkStartMatchDialog();
        if (mInvitePkDialog != null && mInvitePkDialog.isShowing()) {
            mInvitePkDialog.dismiss();
        }
        if (mWarningDialogFactory != null && !mGoToFinishFragmentCalled) {
            mWarningDialogFactory.dismiss();
        }
    }

    private void gotHostFinishFragment() {
        if (!mGoToFinishFragmentCalled) {
            mGoToFinishFragmentCalled = true;
            hasStarted = false;
            dismissAllDialog();
            finishFragment();
            //主播被顶掉时关闭直播间，不发关闭交友模式、取消 pk 匹配的消息
            if (!isKicked) {
                if (RoomModeManager.isFriendsMode()) {
                    LoveModeLogicHelper.log("gotHostFinishFragment, gonna stopFriendsMode!");
                    LoveModeManager.getInstance().stopLoveMode(RoomModeManager.isPiaMode());
                }
                IInteractivePlayComponent friendModeComponent = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
                if (RoomModeManager.isPkMode() && friendModeComponent != null && friendModeComponent.isPkMatching()) {
                    LoveModeLogicHelper.log("gotHostFinishFragment, gonna cancelPKMatch!");
                    LivePkHelper.getInstance().cancelPKMatch(Mode.MODE_MIC_RANDOM.getValue());
                }
            }
            LoveModeManager.releaseInstance();
            startFragment(LiveAnchorFinishFragment.newInstance(mLiveId, LiveAnchorFinishFragment.TYPE_FROM_LIVE_ANCHOR_FRAGMENT).setPremiereRoom(mPremiereInfo));
        }
    }

    protected void showPublishError() {
        if (!canUpdateUi()) {
            return;
        }
        String dialogString = hasStarted ? "直播信号中断，是否重试？" : "直播准备失败，请重试";
        if (mDialogBuilder == null) {
            mDialogBuilder = new DialogBuilder(getActivity());
        }
        mDialogBuilder.setMessage(dialogString).setOkBtn("重试", new DialogBuilder.DialogCallback() {
            @Override
            public void onExecute() {
                if (canUpdateUi()) {
                    startLoading();
                    retryingCheckSceneLiveRealTime();
                }
            }
        }).setCancelBtn("取消", new DialogBuilder.DialogCallback() {
            @Override
            public void onExecute() {
                if (canUpdateUi()) {
                    hasStarted = false;
                    dismissAllDialog();
                    finish();
                }
            }
        }).setOutsideTouchCancel(false).showConfirm();
    }

    private void retryingCheckSceneLiveRealTime() {
        Map<String, String> map = LiveHelper.buildTimeParams();
        map.put("liveRecordId", "" + mLiveId);
        CommonRequestForLive.queryPersonalLiveRealTime(map, new IDataCallBack<SceneLiveRealTime>() {
            @Override
            public void onSuccess(SceneLiveRealTime object) {
                if (!canUpdateUi())
                    return;
                if (object == null) {
                    showPublishError();
                    return;
                }
                switch (object.getStatus()) {
                    case PersonLiveBase.LIVE_STATUS_END:
                        gotHostFinishFragment();
                        break;
                    case PersonLiveBase.LIVE_STATUS_ING:
                        requestLiveRoomPushInfo();
                        break;
                    case PersonLiveBase.LIVE_STATUS_NOTICE:
                        requestLiveRoomPushInfo();
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onError(int code, String message) {//接口出问题，默认刷新页面
                if (!canUpdateUi())
                    return;
                showPublishError();
            }
        });
    }

    protected void showLiveStart() {
        hasStarted = true;
        startTiming();
        stopLoading();
        if (mModel != null && mModel.getLiveRecordInfo() != null) {
            mTopicContent = mModel.getLiveRecordInfo().description;
        }
        refreshTopicData();
        dismissPublishErrorDialog();


    }

    private void dismissPublishErrorDialog() {
        if (mDialogBuilder != null) {
            mDialogBuilder.cancle();
        }
    }


    // 直播详情中公告信息为兼容老版本，第一次进入直播间需要主动拉取话题信息
    private void refreshTopicData() {
        HashMap<String, String> map = new HashMap<>();
        map.put("liveRecordId", String.valueOf(mLiveId));
        CommonRequestForLive.queryTopic(map, new IDataCallBack<LiveTopicInfo>() {
            @Override
            public void onSuccess(LiveTopicInfo object) {
                if (object != null && !TextUtils.isEmpty(object.content)) {
                    mTopicContent = object.content;
                }
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    private void showRequestAgain() {
        if (!canUpdateUi()) {
            return;
        }

        new DialogBuilder(getActivity()).setMessage("出错了，是否重试？").setOkBtn("确定", new DialogBuilder
                .DialogCallback() {
            @Override
            public void onExecute() {
                requestStartLive();
            }
        }).setOutsideTouchCancel(false).showConfirm();
    }

    public abstract void startLoading();

    public abstract void stopLoading();

    private void showCountDownAni() {
        hasShowCountDownAni = true;
        IHostCountDownComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_COUNT_DOWN);
        if (component != null) {
            component.playOneSecondAni();
        }
    }


    @SuppressWarnings("unchecked")
    @Override
    public void onFinishCallback(Class cls, int fid, Object... objects) {
        super.onFinishCallback(cls, fid, objects);
        if (fid == Configure.MusicFragmentId.ADD_MUSIC_FRAGMENT && canUpdateUi()) {
            if (objects != null && objects.length > 0 && objects[0] instanceof Map &&
                    getRoomCore() != null) {
                List addedMusicList = new ArrayList(((Map) objects[0]).values());
                getRoomCore().getBgmCore().setBgmSource(addedMusicList);
            }
        }
    }

    protected void handleOnlineStatusChanged(long onLineCount, long participateCount) {
    }

    @Override
    protected void setEnterRoomMsgIsHostFragment(CommonChatUserJoinMessage userJoinMessage) {
        userJoinMessage.isHostFragment = true;
    }

    @Override
    public void onReceiveRoomStatusChangeMessage(CommonChatRoomStatusChangeMessage message) {
        super.onReceiveRoomStatusChangeMessage(message);
        handleStreamShutdown(message);
        if (canUpdateUi() && message.triggerAlert && message.status == PersonLiveBase.LIVE_STATUS_END) {
            //当收到审核下架的消息时，弹出对话框
            onReceiveAnchorVerifyWarningMessage(new CommonChatRoomAnchorVerifyWarningMessage(message.alertTitle, message.alertText, message.alertBtnTitle, message.type));
        }
    }

    @Override
    public void onReceiveFloatScreenMessage(CommonFloatScreenMessage msg) {
        //主播端不展示飘屏
    }

    private void handleStreamShutdown(CommonChatRoomStatusChangeMessage info) {
        if (info != null && mLiveRecordInfo != null && info
                .status == PersonLiveBase.LIVE_STATUS_END && getActivity() != null) {
            postOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (canUpdateUi() && hasStarted) {
                        releaseResource(false);
                        gotHostFinishFragment();
                    }
                }
            });
        }
    }

    /**
     * TODO 隐藏键盘，检查空实现
     */
    public void hideKeyBoard() {

    }

    public void showFansClubDialogFragment() {
        if (mModel == null || TextUtils.isEmpty(mModel.getFansClubHtmlUrl())) {
            return;
        }
        super.showFansClubDialogFragment(mModel.getFansClubHtmlUrl());
    }

    @Override
    protected void onKickOutChatRoom() {
        super.onKickOutChatRoom();
        LoveModeLogicHelper.kickLog("callback onRMKickUser");

        //防止onExitBtnPress 弹出结束直播对话框
        hasStarted = false;
        isKicked = true;
        releaseResource(true);
        if (canUpdateUi()) {
            dismissAllDialog();
            finish();
            CustomToast.showToast("该账号已在其它设备登录，请到新设备上直播");
        }
    }

    /**
     * 掉线重进或者互踢重进，需要开启交友模式，这时没有 zego 信息，就需要在获取到 zego 信息后开启交友模式
     */
    public void setNeedStartLoveMode(boolean needStartLoveMode) {
        this.needStartLoveMode = needStartLoveMode;
    }

    protected void attachToPlayManagerListener() {
        Logger.i(TAG, "attachToPlayManagerListener  ");
        XmPlayerManager xmPlayerManager = XmPlayerManager.getInstance(mContext);
        if (xmPlayerStatusListener == null) {
            xmPlayerStatusListener = new XmPlayerStatusListener(this);
        }
        if (!xmPlayerManager.containPlayerStatusListener(xmPlayerStatusListener) && canUpdateUi
                ()) {
            xmPlayerManager.addPlayerStatusListener(xmPlayerStatusListener);
        }
    }

    protected void deAttachToManagerListener() {
        Logger.i(TAG, "removeManagerListener  ");
        XmPlayerManager xmPlayerManager = XmPlayerManager.getInstance(mContext);
        if (xmPlayerManager.containPlayerStatusListener(xmPlayerStatusListener)) {
            XmPlayerManager.getInstance(mContext).removePlayerStatusListener
                    (xmPlayerStatusListener);
        }
    }

    private static class XmPlayerStatusListener implements IXmPlayerStatusListener {

        private final SoftReference<LiveAnchorRoomBaseFragment> mRef;

        public XmPlayerStatusListener(LiveAnchorRoomBaseFragment room) {
            this.mRef = new SoftReference<>(room);
        }

        @Override
        public void onPlayStart() {
            if (mRef == null || mRef.get() == null) {
                return;
            }
            LiveAnchorRoomBaseFragment room = mRef.get();
            PlayTools.stop(room.mContext);
        }

        @Override
        public void onPlayPause() {

        }

        @Override
        public void onPlayStop() {

        }

        @Override
        public void onSoundPlayComplete() {

        }

        @Override
        public void onSoundPrepared() {

        }

        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {

        }

        @Override
        public void onBufferingStart() {

        }

        @Override
        public void onBufferingStop() {

        }

        @Override
        public void onBufferProgress(int percent) {

        }

        @Override
        public void onPlayProgress(int currPos, int duration) {

        }

        @Override
        public boolean onError(XmPlayerException exception) {
            return false;
        }
    }


    @Override
    public void onReceiveHostNoPushStreamMessage(LiveHostNoPushStreamMessage message) {
        if (message == null || mActivity == null) {
            return;
        }
        LiveNormalDialog dialog = new LiveNormalDialog(mActivity);
        dialog.setTitle(message.getAlarm()).setTitleSize(16)
                .setOk(message.getButton()).hideContent()
                .hideCancelBtn();
        dialog.show();
    }

    @Override
    public void onReceiveCommonBusinessMessage(CommonBusinessMsg message) {
        super.onReceiveCommonBusinessMessage(message);
        if (message != null && message.getType() != null &&
                message.getType() == CommonBusinessMsg.TYPE_AI_SETTING) {
            IHostBottomComponent component = getComponentSafety(COMPONENT_BOTTOM_BAR);
            if (null != component) {
                component.showAiHelperDot();
            }
        }
    }

}

