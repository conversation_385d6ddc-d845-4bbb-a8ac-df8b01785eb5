package com.ximalaya.ting.android.liveanchor;

import static com.ximalaya.ting.android.live.common.lib.entity.LiveAnchorTaskMessage.TYPE_ANCHOR_TASK;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_AI_PENALTY;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_CHAT_LIST_GUIDE;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_HEADER;
import static com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig.COMPONENT_HOST_TASK;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.live.video_beautify.VideoLiveBeautifyCache;
import com.ximalaya.ting.android.host.live.video_beautify.VideoLiveBeautifySetting;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFunctionAction;
import com.ximalaya.ting.android.live.biz.mode.data.PrivateChatViewModel;
import com.ximalaya.ting.android.live.biz.pia.constant.PiaConstantKt;
import com.ximalaya.ting.android.live.biz.pia.entity.CommonPiaStatusRsp;
import com.ximalaya.ting.android.live.biz.pia.entity.PiaDramaModel;
import com.ximalaya.ting.android.live.biz.pia.panel.dialog.PiaBGMDialog;
import com.ximalaya.ting.android.live.biz.pia.panel.manager.XmPiaBgmPlayerManager;
import com.ximalaya.ting.android.live.common.chatlist.constant.ChatItemViewType;
import com.ximalaya.ting.android.live.common.dialog.web.CommonXmlObjcJsCall;
import com.ximalaya.ting.android.live.common.lib.base.bean.PublishExceptionEvent;
import com.ximalaya.ting.android.live.common.lib.base.constants.BundleKeyConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.constants.LiveMediaType;
import com.ximalaya.ting.android.live.common.lib.base.constants.LivePageAvailabilityConst;
import com.ximalaya.ting.android.live.common.lib.base.request.AnchorPublishExceptionReport;
import com.ximalaya.ting.android.live.common.lib.base.util.LivePageAvailabilityUtil;
import com.ximalaya.ting.android.live.common.lib.entity.HeadAnchorInfo;
import com.ximalaya.ting.android.live.common.lib.entity.ILiveRoomDetail;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAiSoundPenalty;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAnchorTaskMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAuditNotify;
import com.ximalaya.ting.android.live.common.lib.entity.LiveCarouselRankNotify;
import com.ximalaya.ting.android.live.common.lib.entity.LiveGetAiGiftMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveTaskNotify;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.entity.ai.LiveImAiHelperMsg;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.PremiereInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.NewAudienceAwardInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.PackageInfo;
import com.ximalaya.ting.android.live.common.lib.manager.livesdkclient.LiveClientManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.videoplayer.constants.PlayerConstants;
import com.ximalaya.ting.android.live.common.view.dialog.warning.LiveWarningDialog;
import com.ximalaya.ting.android.live.common.view.dialog.warning.WarningDialogFactory;
import com.ximalaya.ting.android.live.host.components.IBaseRoomCompConfig;
import com.ximalaya.ting.android.live.host.components.input.IInputPanelComponent;
import com.ximalaya.ting.android.live.host.data.stream.ZegoRoomInfo;
import com.ximalaya.ting.android.live.host.liveservice.photo.LivePhotoService;
import com.ximalaya.ting.android.live.host.manager.aigift.AIGiftShowManager;
import com.ximalaya.ting.android.live.host.manager.minimize.BaseVirtualRoom;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomConstants;
import com.ximalaya.ting.android.live.host.manager.roomcore.RoomCoreManager;
import com.ximalaya.ting.android.live.host.utils.LiveHostCommonUtil;
import com.ximalaya.ting.android.live.host.utils.checkwindow.LiveCommonPopQueueManager;
import com.ximalaya.ting.android.live.lib.chatroom.ChatRoomConnectionManager;
import com.ximalaya.ting.android.live.lib.chatroom.constant.BaseCommonProtoConstant;
import com.ximalaya.ting.android.live.lib.chatroom.entity.BaseCommonChatRsp;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatAudienceMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatHotTopicMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatQueryRoomModeRsp;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatShareLiveRoomMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.LiveFansRemindMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomAnchorVerifyWarningMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomCompleteWishListMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomLoveValueChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNotifyBottomButtonMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomOnlineUserListMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoShoppingMessage;
import com.ximalaya.ting.android.live.lib.livetopic.LiveAnnouncementAlbumDialog;
import com.ximalaya.ting.android.live.lib.livetopic.LiveAnnouncementNewFeatureDialog;
import com.ximalaya.ting.android.live.lib.livetopic.bean.SOURCE;
import com.ximalaya.ting.android.liveanchor.components.backpress.IHostBackPressComponent;
import com.ximalaya.ting.android.liveanchor.components.bottom.IHostBottomComponent;
import com.ximalaya.ting.android.liveanchor.components.giftpanel.IAnchorGiftPanelComponent;
import com.ximalaya.ting.android.liveanchor.components.header.IHostHeaderComponent;
import com.ximalaya.ting.android.liveanchor.components.mic.IHostMicComponent;
import com.ximalaya.ting.android.liveanchor.components.soundeffect.IHostSoundEffectComponent;
import com.ximalaya.ting.android.liveanchor.components.videopreview.IHostVideoPreviewComponent;
import com.ximalaya.ting.android.liveaudience.components.aiprop.ILiveAiPropComponent;
import com.ximalaya.ting.android.liveaudience.components.chatlist.chat.AVChatListMsgProducer;
import com.ximalaya.ting.android.liveaudience.components.chatlist.chat.IAVChatListComponent;
import com.ximalaya.ting.android.liveaudience.components.chatlist.guide.IAVChatListGuideComponent;
import com.ximalaya.ting.android.liveaudience.components.component2.AudienceCompConfig;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.IInteractivePlayComponent;
import com.ximalaya.ting.android.liveaudience.components.interactiveplay.IPiaModeComponent;
import com.ximalaya.ting.android.liveaudience.components.premiere.IPremierePopComponent;
import com.ximalaya.ting.android.liveaudience.components.shoppingfloatscreen.IShoppingFloatComponent;
import com.ximalaya.ting.android.liveaudience.components.task.ITaskComponent;
import com.ximalaya.ting.android.liveaudience.friends.LoveModeAnchor;
import com.ximalaya.ting.android.liveaudience.friends.LoveModeLogicHelper;
import com.ximalaya.ting.android.liveaudience.friends.base.ILoveModeAnchor;
import com.ximalaya.ting.android.liveaudience.giftModule.loader.LiveGiftLoader;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeManager;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeMicStateManager;
import com.ximalaya.ting.android.liveaudience.manager.love.LoveModeUIManager;
import com.ximalaya.ting.android.liveaudience.manager.love.util.LoveModeManagerInjectUtil;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.liveaudience.manager.pk.LivePkHelper;
import com.ximalaya.ting.android.liveaudience.manager.pk.util.PkModeManagerInjectUtil;
import com.ximalaya.ting.android.liveaudience.mvp.LiveAudienceRoomPresenter;
import com.ximalaya.ting.android.liveaudience.util.LamiaHelper;
import com.ximalaya.ting.android.liveaudience.util.premiere.PremiereRoomUtil;
import com.ximalaya.ting.android.liveaudience.view.dialog.LiveWishFinishDialog;
import com.ximalaya.ting.android.liveaudience.view.mode.IRoomModeFragment;
import com.ximalaya.ting.android.liveaudience.view.pk.host.IPKSearchRecordClickListener;
import com.ximalaya.ting.android.liveaudience.view.pk.host.PKSearchHostView;
import com.ximalaya.ting.android.liveaudience.view.pk.host.PkSearchHostViewModel;
import com.ximalaya.ting.android.liveaudience.viewmodel.AIEnableStatusViewModel;
import com.ximalaya.ting.android.liveaudience.viewmodel.LiveHostMicViewModel;
import com.ximalaya.ting.android.liveim.mic.api.IXmMicService;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.receive.PlayerReceiver;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

/**
 * 音视频主播侧，mediaType = 1 音频直播，mediaType = 2 视频直播
 *
 * <AUTHOR>
 */
public class LiveAnchorRoomFragment extends LiveAnchorRoomBaseFragment
        implements IPKSearchRecordClickListener {
    private static final String TAG = "LiveAnchorRoomFragment";

    private LiveAnchorRoomImplHelper mHostRoomImplHelper;

    /**
     * 切换交友模式相关
     */
    protected ILoveModeAnchor mLoveModeAnchor;

    private LocalBroadcastReceiver mLocalBroadcastReceiver;
    private LocalPiaDramaBroadcastReceiver mPiaDramaLocalBroadcastReceiver;
    // 指定PK搜索
    private PkSearchHostViewModel pkSearchHostViewModel;
    private PKSearchHostView pkSearchHostView;
    private LiveHostMicViewModel mLiveHostMicViewModel;


    /**
     * 讲解中挂件是否显示
     */
    private boolean isGoodsOperationViewShow = false;
    /**
     * 顶部大挂件是否显示
     */
    private boolean isTopOperationViewShow = false;
    /**
     * 观众连麦小窗是否显示
     */
    private boolean isAudiMicViewShow = false;
    /**
     * 底部小挂件是否显示
     */
    private boolean isBottomOperationViewShow = false;

    protected AnchorRoomBackground mAnchorRoomBackground;

    protected AnchorRoomCore mAnchorRoomCore = new AnchorRoomCore();

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        RoomCoreManager.getInstance().setAnchorRoom(mAnchorRoomCore);
        bindRoomCore(mAnchorRoomCore);
        if (getArguments() == null) {
            CustomToast.showDebugFailToast("LiveAnchorRoomFragment getArguments null");
            return;
        }
        long roomId = getArguments().getLong(ILiveFunctionAction.KEY_ROOM_ID, 0);
        long liveId = getArguments().getLong(ILiveFunctionAction.KEY_LIVE_ID, 0);
        if (getRoomCore() != null) {
            getRoomCore().enterRoom(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO, roomId, liveId, RoomConstants.RoomEnterType.ENTER_INIT, null);
        }
    }

    @Override
    protected void loadData() {
        super.loadData();
        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (canUpdateUi()) {
                    if (getRoomCore() != null) {
                        getRoomCore().bindRoomCallBack(LiveAnchorRoomFragment.this);
                    }
                }
            }
        });
    }

    @Override
    public boolean isNeedAnonymity() {
        return false;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (getRoomCore() != null) {
            getRoomCore().unBindRoomCallback(this);
            getRoomCore().exitRoom();
        }
        RoomCoreManager.getInstance().setAnchorRoom(null);
    }

    public static LiveAnchorRoomFragment newInstance() {
        return new LiveAnchorRoomFragment();
    }

    public static LiveAnchorRoomFragment getInstance(long liveId, long roomId, @LiveMediaType int mediaType) {
        LiveAnchorRoomFragment fragment = new LiveAnchorRoomFragment();
        Bundle bundle = new Bundle();
        bundle.putLong(ILiveFunctionAction.KEY_ROOM_ID, roomId);
        bundle.putLong(ILiveFunctionAction.KEY_LIVE_ID, liveId);
        bundle.putInt(ILiveFunctionAction.KEY_LIVE_MEDIA_TYPE, mediaType);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mHostRoomImplHelper = new LiveAnchorRoomImplHelper(this);
        privateChatViewModel = new ViewModelProvider(this).get(PrivateChatViewModel.class);
        pkSearchHostViewModel = new ViewModelProvider(this).get(PkSearchHostViewModel.class);
        mLiveHostMicViewModel = new ViewModelProvider(this).get(LiveHostMicViewModel.class);
    }


    @Override
    public void onMyResume() {
        tabIdInBugly = 163841;
        super.onMyResume();

        if (getWindow() != null) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED);
        }

        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                IXmMicService avService = getAvService();
                if (mLiveMediaType == LiveMediaType.TYPE_VIDEO && avService != null) {
                    avService.enableCamera(false);
                    avService.enableCamera(true);

                    VideoLiveBeautifySetting beautifySetting = VideoLiveBeautifyCache.getInstance().getBeautifySetting();
                    if (beautifySetting != null && beautifySetting.isCameraFront) {
                        avService.enableCameraFront(false);
                        avService.enableCameraFront(true);
                    }
                    //如果是视频直播，需要提取美颜等设置参数的本地存储
                    initAndStartVideoPreview();

                } else {
                    stopVideoPreview();
                }

                // 此处的屏幕常亮设置 和baseRoomFragment里面并非重复，不要删除；
                // 主播推流页面展示之前有较长的动画转场时间，在页面不可见情况下，设置屏幕常亮设置的flag不能生效
                // 这里的延时就是为了保证页面可见情况下设置
                // 如果修改麻烦进行一下简单自测，看常亮是否生效
                postOnUiThreadDelayed(new Runnable() {
                    @Override
                    public void run() {
                        setKeepScreenOn(true);
                    }
                }, 3000);

                //AI礼物上报
                if (AIGiftShowManager.getInstance().getHostLiveId() > 0) {
                    AIGiftShowManager.getInstance().checkAndReportAiGiftStatus();
                }

            }
        });
    }

    private void setKeepScreenOn(boolean keepScreenOn) {
        Window window = getWindow();
        if (window != null) {
            if (keepScreenOn) {
                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            } else {
                window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            }
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.liveanchor_fra_room;
    }

    @Override
    protected LiveAudienceRoomPresenter createPresenter() {
        return new LiveAnchorRoomPresenter(this, mConnectionManager, mRoomId);
    }

    @Override
    protected void parseBundle() {
        super.parseBundle();

        try {
            Bundle arguments = getArguments();

            if (arguments == null) {
                return;
            }
            mLiveMediaType = arguments.getInt(ILiveFunctionAction.KEY_LIVE_MEDIA_TYPE);

            String pushJsonStr = arguments.getString(ILiveFunctionAction.KEY_PUSH_PARAMS);
            mZegoRoomInfo = new Gson().fromJson(pushJsonStr, ZegoRoomInfo.class);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mAnchorRoomBackground = new AnchorRoomBackground(this);
        ImageView bgView = findViewById(R.id.live_bg_blur);
        mAnchorRoomBackground.initRoomBackground(bgView, getRoomBizType());
        if (mRoomParentFragment == null) {
            mRoomParentFragment = mAnchorRoomBackground;
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        super.initUi(savedInstanceState);
        LiveCommonPopQueueManager.getInstance().joinHost(this);

    }

    @Override
    protected void initMyUi(Bundle savedInstanceState) {
        super.initMyUi(savedInstanceState);
        reportBeautifySetting(true);
    }

    @Override
    protected boolean shouldShowLoadingView() {
        return false;
    }

    /**
     * 提取美颜等设置参数的本地存储
     */
    protected void initAndStartVideoPreview() {

        VideoLiveBeautifyCache.getInstance().getBeautifySetting();

        //将视频预览数据加入组件
        IHostVideoPreviewComponent videoPreviewComponent = getComponentSafety(AnchorCompConfig.COMPONENT_VIDEO_PREVIEW);
        if (videoPreviewComponent != null) {
            //开启视频预览
            videoPreviewComponent.startVideoPreview();

            isOpenPreview = true;
        }
    }

    private void stopVideoPreview() {
        IHostVideoPreviewComponent videoPreviewComponent = getComponentSafety(AnchorCompConfig.COMPONENT_VIDEO_PREVIEW);
        if (videoPreviewComponent != null) {
            //关闭视频预览
            videoPreviewComponent.hideVideoPreview();
        }
    }


    @Override
    protected void loadMyData() {
        super.loadMyData();
        if (mLoveModeAnchor == null) {
            mLoveModeAnchor = new LoveModeAnchor(getContext());
            getLifecycle().addObserver(mLoveModeAnchor);
            mLoveModeAnchor.setRoomFragment(mHostRoomImplHelper.getHostRoomFragment());
        }
        IInteractivePlayComponent friendModeComponent = getComponentSafety(AudienceCompConfig.COMPONENT_FRIEND_MODE);
        if (null != friendModeComponent) {
            friendModeComponent.setActionCallback(mLoveModeAnchor.getActionCallback());
        }

        // 开始Loading 请求详情接口
        startLoading();

        //主播直播的时候移除通知栏
        Intent intent = new Intent(XmNotificationCreater.ACTION_CONTROL_CLOSE_MAIN);
        intent.setClass(mContext, PlayerReceiver.class);
        mContext.sendBroadcast(intent);
        LiveGiftLoader.getInstance(LiveGiftLoader.class).updateGiftList();
        if (mLiveHostMicViewModel != null) {
            mLiveHostMicViewModel.getMicPkWhiteList(null, getLiveMediaType());
        }
        //获取ai助手状态
        if (mAIEnableStatusViewModel != null) {
            mAIEnableStatusViewModel.isEnableScreenShot();
        }
    }

    @Override
    public void onRequestRoomDetailSuccess(ILiveRoomDetail roomDetail) {
        super.onRequestRoomDetailSuccess(roomDetail);

        LivePageAvailabilityUtil.postSucceed(LivePageAvailabilityConst.ANCHOR_ROOM, roomDetail);

        if (!(roomDetail instanceof PersonLiveDetail)) {
            notifyTracePageFailed();
            return;
        }

        mPresenter.getComponentManager().beginComponentSequenceLoader()
                .enqueueLoadComponentRequest(AudienceCompConfig.COMPONENT_RIGHT_AD, 500)
                .load();
        getComponentSafety(COMPONENT_HEADER);
        getComponentSafety(COMPONENT_HOST_TASK);
        getPresenter().getComponentHost().dispatchHostData(mRoomDetail);

        if (mAnchorRoomBackground != null) {
            mAnchorRoomBackground.bindDetail(mRoomDetail);
        }
        // 加载运营位信息
        loadRoomRightOperations();

        //检查是否需要存在需要领取的优惠卷
        checkHasCouponOrNot();

        showNormalBackground();

        doAfterAnimation(new IHandleOk() {
            @Override
            public void onReady() {
                if (!canUpdateUi()) {
                    notifyTracePageFailed();
                    return;
                }
                //获取房间的直播场次信息后，进行直播状态处理
                updateAfterLoaded(mRoomDetail);

                notifyTracePageEnd();
            }
        });
    }

    @Override
    protected void handCommonRoomLoadErrorView(int code, String msg) {

    }

    @Override
    public void onRequestRoomDetailError(long roomId, int code, String message) {
        super.onRequestRoomDetailError(roomId, code, message);

        LivePageAvailabilityUtil.postFail(LivePageAvailabilityConst.ANCHOR_ROOM, code, message);

        if (!canUpdateUi()) {
            return;
        }
        if (code == 2930) {
            CustomToast.showFailToast(message);
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (canUpdateUi()) {
                        finish();
                    }
                }
            }, 500);
            return;
        } else {
            final DialogBuilder dialogBuilder = new DialogBuilder(getActivity());
            dialogBuilder.setMessage("直播详情获取失败").setOkBtn("重试", new DialogBuilder
                    .DialogCallback() {
                @Override
                public void onExecute() {
                    if (canUpdateUi()) {
                        loadRoomData();
                    }
                }
            }).setCancelBtn("取消").setOutsideTouchCancel(false).showConfirm();
        }
        if (!TextUtils.isEmpty(message) && !message.equals(BaseCall
                .getDefaultNetErrorContent())) {
            CustomToast.showFailToast(message);
        } else {
            CustomToast.showToast(BaseCall.getDefaultNetErrorContent());
        }
    }

    @Override
    protected void onDisconnectChatRoom() {
        super.onDisconnectChatRoom();
        if (mLoveModeAnchor != null) {
            mLoveModeAnchor.onDisconnectChatRoom();
        }
    }

    @Override
    protected void onConnectedChatRoom() {
        super.onConnectedChatRoom();

        if (mLoveModeAnchor != null) {
            mLoveModeAnchor.onConnectChatRoom();
        }
    }

    @Override
    protected void onKickOutChatRoom() {
        super.onKickOutChatRoom();

        if (mLoveModeAnchor != null) {
            mLoveModeAnchor.onKickOutChatRoom();
        }

        // 上报账号异常
        AnchorPublishExceptionReport.reportException(PublishExceptionEvent.ACCOUNT_EXCEPTION);
    }

    @Override
    protected String getTraceName() {
        return "房间-主播端直播间";
    }

    @Override
    public boolean isAnchor() {
        return true;
    }

    @Override
    public boolean isFromHostFragment() {
        return true;
    }


    // 获取SDK的推拉流服务
    @Override
    public IXmMicService getAvService() {
        return LiveClientManager.getInstance().getLiveMicService();
    }

    @Override
    public int getLiveMediaType() {
        return mLiveMediaType;
    }

    public PlayerConstants.ResolutionRatio getVideoSizeRatio() {
        return PlayerConstants.ResolutionRatio.PORTRAIT;
    }

    public PrivateChatViewModel getPrivateChatViewModel() {
        return privateChatViewModel;
    }

    public AIEnableStatusViewModel getAiEnableStatusViewModel() {
        return mAIEnableStatusViewModel;
    }

    @Override
    public boolean isRoomCreatedFromSlide() {
        return false;
    }

    @Override
    public void onPiaModeOpen() {
        super.onPiaModeOpen();
        IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
        if (null != piaModeComponent) {
            piaModeComponent.updateStreamRoleType(
                    BaseCommonProtoConstant.CommonUserType.USER_TYPE_PRESIDE
            );
        }
        IHostBottomComponent bottomComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != bottomComponent) {
            bottomComponent.onPiaModeUISwitch(true);
        }
    }

    @Override
    public void onPiaModeClose() {
        super.onPiaModeClose();
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.onPiaModeUISwitch(false);
        }
    }

    @Override
    public void onPiaStatusNotify(CommonPiaStatusRsp piaStatusRsp) {
        super.onPiaStatusNotify(piaStatusRsp);
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.onUpdatePiaStatus(piaStatusRsp.piaStatus);
        }
    }

    @Override
    public void onPiaStartClicked() {
        super.onPiaStartClicked();
        tracePiaStartClick();
        LoveModeManager.getInstance().startPia(new ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp>() {
            @Override
            public void onSuccess(BaseCommonChatRsp baseCommonChatRsp) {
                CustomToast.showToast("开始成功");
                IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
                if (null != piaModeComponent) {
                    piaModeComponent.scrollDramaToTop();
                }
                // 开始成功自动播放第一个 Bgm
                XmPiaBgmPlayerManager.Companion.getInstance().startPlay();
            }

            @Override
            public void onError(int errorCode, String errorMessage) {
                CustomToast.showToast(errorMessage);
            }
        });
    }

    @Override
    public void onPiaTuningClicked() {
        super.onPiaTuningClicked();
        showPiaTuningDialog();
    }

    @Override
    public void onPiaBGMClicked() {
        super.onPiaBGMClicked();
        PiaBGMDialog.Companion.showDialog(getChildFragmentManager(), getRoomId(), getHostUid());
    }

    @Override
    public void onPiaScrollProgressBroadcast(float progress) {
        super.onPiaScrollProgressBroadcast(progress);
        LoveModeManager.getInstance().broadcastScrollProgress(progress);
    }

    @Override
    public void clearTopPage() {
        getManageFragment().clearTopFragment(LiveAnchorRoomFragment.class.getName(), true);
    }

    public void onHostBottomOpenCallClick() {
        if (mConnectionManager != null && !mConnectionManager.isConnected()) {
            closeRoomImConnection();
            joinRoomImConnection();
        }
        IHostMicComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
        if (component != null) {
            component.showMicDialog();
        }
    }

    public void onHostBottomSoundEffectClick() {
        IHostSoundEffectComponent djEffectComponent = getComponentSafety(AudienceCompConfig.COMPONENT_SOUND_EFFECT);
        if (djEffectComponent != null) {
            djEffectComponent.show(((MainActivity) mActivity).getSupportFragmentManager());
        }
    }

    public void onHostBottomMusicClick() {

        if (getAvService() == null) {
            return;
        }
        showBgmDialog();
        getAvService().enableAux(true);
    }

    public void onHostBottomPkClick() {
        PkModeManagerInjectUtil.injectPkMessageManager(mPkMessageManager);
        PkModeManagerInjectUtil.injectPkDispatcherManager(mPkMessageDispatcherManager);
        PkModeManagerInjectUtil.injectRoomId(mRoomId);
        if (mRoomDetail != null) {
            PkModeManagerInjectUtil.injectHostNickname(mRoomDetail.getHostNickname());
        }
        showPkStartMatchDialog();
    }


    public void onHostBottomManagerClick() {
        showAdminListDialog();
    }

    public void onHostBottomTopicClick() {
        if (!canUpdateUi() || mRoomDetail == null) {
            return;
        }
        boolean isShowed = LiveAnnouncementNewFeatureDialog.showed();
        LiveHelper.Log.i("测试新版提示-", " " + isShowed);
        if (isShowed) {
            //没新版提示，直接打开编辑
            openEditPage();
        } else {
            //有新版提示，打开新版介绍页
            openPhotoHintPage();
        }
    }

    public void onHostThemeClick() {
        if (!canUpdateUi() || mRoomDetail == null) {
            return;
        }
        showThemeEditDialog();
    }

    private void openPhotoHintPage() {
        LiveAnnouncementNewFeatureDialog dialog = LiveAnnouncementNewFeatureDialog.newInstance();
        dialog.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openEditPage();
            }
        });
        dialog.show(getChildFragmentManager(), LiveAnnouncementNewFeatureDialog.TAG);
    }

    private void openEditPage() {
        // 主播端直播间内底部菜单 - 公告话题编辑入口
        boolean isPremiere = PremiereRoomUtil.isValidPremiereStatus(mPremiereInfo);
        long anchorId = UserInfoMannage.getUid();

        LiveAnnouncementAlbumDialog.show(
                this, SOURCE.CHANNEL_2, true, getRoomBizType(),
                mRoomDetail.getLiveId(), getRoomId(), anchorId, isPremiere
        );
    }

    public void onHostBottomMixerClick() {
        if (isPiaMode()) {
            showPiaTuningDialog();
        } else {
            showMixerDialogFragment();
        }
    }

    public void onHostBottomMuteClick(boolean mute) {

        IXmMicService avService = getAvService();
        if (avService == null) {
            return;
        }

        if (mLiveMediaType == LiveMediaType.TYPE_VIDEO) {
            boolean micEnabled = avService.getMicEnabled();
            avService.enableMic(!micEnabled);
        } else if (mLiveMediaType == LiveMediaType.TYPE_AUDIO) {
            if (mute) {
                CustomToast.showToast("已静音自己");
                mEnableMic = false;
                avService.enableMic(false);
                startMicFadeInFadeOutAnim();
            } else {
                CustomToast.showToast("取消静音");
                mEnableMic = true;
                avService.enableMic(true);
                stopMicFadeInFadeOutAnim();
            }
        }
        if (ConstantsOpenSdk.isDebug) {
            if (mute) {
                //要关掉麦克风
                boolean micEnabled = avService.getMicEnabled();
                if (micEnabled) {
                    ToastManager.showToast("当前麦克风开启中，关闭麦克风失败");
                }
            } else {
                //要开启麦克风
                boolean micEnabled = avService.getMicEnabled();
                if (!micEnabled) {
                    ToastManager.showToast("当前麦克风关闭中，打开麦克风失败");
                }
            }
        }

    }

    public void onHostBottomSendPictureClick() {
        // 视频直播间，直接进入选择图片
        if (mLiveMediaType == LiveMediaType.TYPE_VIDEO) {
            selectPhotoToSend(true);
        } else {
            selectPhotoToSend(false);
        }

    }

    public void onHostBottomChatClickChat() {
        if (mConnectionManager != null && !mConnectionManager.isConnected()) {
            closeRoomImConnection();
            joinRoomImConnection();
        }
        try {
            IInputPanelComponent component = getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_INPUT_PANEL);
            component.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void tracePiaStartClick() {
        // 直播间-Pia戏-开始按钮  点击事件
        new XMTraceApi.Trace()
                .click(40095)
                .put("currPage", "liveRoom")
                .put("liveId", getLiveId() + "")
                .put("liveRoomName", mRoomDetail.getRoomTitle())
                .put("liveRoomType", LiveMediaType.TYPE_AUDIO + "")
                .put("anchorId", getHostUid() + "")
                .put("roomId", getRoomId() + "")
                .createTrace();
    }

    public void onHostBottomChangeModeClick(int newMode) {
        changeRoomMode(newMode);
    }

    public void onHostBottomForbidClick() {
        showBanListDialog();
    }

    public void onHostHeaderExitClick() {
        if (!onExitBtnPress()) {
            finish();//不拦截退出
        }
    }

    public void onHostHeaderInComeClick() {
        showIncomeRecordDialogFragment();
    }

    public void onHostHeaderFansClubClick() {
        showFansClubDialogFragment();
    }

    public void onClickHostAvatar() {
        showUserInfoPop(getHostUid());
    }


    public void onHostHeaderGuardClick() {
        showGuardListPage();
    }

    @Override
    public void showPackageGiftAndLocate(NewAudienceAwardInfo newAudienceAwardInfo) {
        try {
            IAnchorGiftPanelComponent component = getPresenter().getComponentManager().getComponent(AnchorCompConfig.COMPONENT_GIFT_PANEL);
            component.showPackageGiftAndLocate(newAudienceAwardInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void doFollowHost(int sceneType) {
        // 主播自己不能关注自己
    }

    @Override
    public void onCurrentLoginUserInfo(LiveUserInfo object) {
        super.onCurrentLoginUserInfo(object);
        // 更新礼物面板当前用户信息
        try {
            IAnchorGiftPanelComponent component = getPresenter().getComponentManager().getComponent(AnchorCompConfig.COMPONENT_GIFT_PANEL);
            component.updateCurrentUserInfo(object);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isLiveHasStart() {
        return hasStarted;
    }

    public void onTimePlus(long betweenTime) {
        IHostMicComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
        if (component != null) {
            component.onTimePlus(betweenTime);
        }
    }

    private void startMicFadeInFadeOutAnim() {
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.startMicFadeInFadeOutAnim();
        }
    }

    private void stopMicFadeInFadeOutAnim() {
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.stopMicFadeInFadeOutAnim();
        }
    }

    @Override
    protected void showLiveStart() {
        super.showLiveStart();
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.onLiveBegin();
        }
    }

    @Override
    public void hideKeyBoard() {
        super.hideKeyBoard();
    }

    @Override
    protected void handleOnlineStatusChanged(long onLineCount, long participateCount) {
        super.handleOnlineStatusChanged(onLineCount, participateCount);
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.initOnlineAndParticipateCount(onLineCount, participateCount);
        }
    }

    @Override
    protected void startTiming() {
        super.startTiming();
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.startTiming();
        }
    }

    @Override
    public void startLoading() {
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.updateDotStatus(true);
        }
    }

    @Override
    public void stopLoading() {
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.updateDotStatus(false);
        }
    }

    /**
     * 切换pia戏模式
     *
     * @param isPiaSubMode 打开或者关闭pia模式
     */
    @Override
    protected void requestOpenOrCloseLoveMode(boolean isPiaSubMode) {
        super.requestOpenOrCloseLoveMode(isPiaSubMode);
        LiveHelper.showProgressDialog(getContext(), true, this);

        final int originMode;
        if (isPiaSubMode) {
            originMode = LoveModeLogicHelper.getSwitchPiaMode();
        } else {
            originMode = LoveModeLogicHelper.getSwitchMode();
        }
        mLoveModeAnchor.changeRoomMode(
                originMode, new ILoveModeAnchor.IRoomModeChangedListener() {
                    @Override
                    public void onRoomModeChanged(boolean success, int mode, String errorReason) {
                        LiveHelper.dismissProgressDialog(LiveAnchorRoomFragment.this);
                        LoveModeLogicHelper.log("changeRoomMode result :" + success + ", mode: " +
                                mode);

                        if (!success) {
                            CustomToast.showFailToast(errorReason);

                            LiveXdcsUtil.doXDCS(TAG, "Open or close love mode failed, reason: " + errorReason
                                    + ", roomId=" + getRoomId()
                                    + "，liveId=" + getLiveId());
                            return;
                        }
                        if (originMode == RoomModeManager.MODE_MAKE_FRIENDS) {

                            CustomToast.showSuccessToast("您已开启交友模式");
                            IHostMicComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
                            if (component != null) {
                                component.stopMicAndResetUI();
                            }

                            LiveXdcsUtil.doXDCS(TAG, "Open love mode success!"
                                    + ", roomId=" + getRoomId()
                                    + "，liveId=" + getLiveId());
                        } else if (originMode == RoomModeManager.MODE_PIA) {
                            CustomToast.showSuccessToast("您已开启 Pia 戏模式");
                            IHostMicComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
                            if (component != null) {
                                component.stopMicAndResetUI();
                            }

                            LiveXdcsUtil.doXDCS(TAG, "Open pia mode success!"
                                    + ", roomId=" + getRoomId()
                                    + "，liveId=" + getLiveId());
                        } else {
                            if (originMode == LoveModeUIManager.Mode.CLOSE_PIA_MODE) {
                                CustomToast.showSuccessToast("您已关闭 Pia 戏模式");
                            } else {
                                CustomToast.showSuccessToast("您已关闭交友模式");
                            }

                            LiveXdcsUtil.doXDCS(TAG, "Close love mode success!"
                                    + ", roomId=" + getRoomId()
                                    + "，liveId=" + getLiveId());
                        }
                    }
                }
        );
    }

    @Override
    protected boolean onExitBtnPress() {
        if (mLoveModeAnchor != null) {
            mLoveModeAnchor.dismissAllDialog();
        }
        IHostBackPressComponent backPress = getComponentSafety(AnchorCompConfig.COMPONENT_HOST_BACKPRESS);
        if (backPress != null && backPress.isEnable()) {
            return backPress.onBackPress();
        }
        return super.onExitBtnPress();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, @Nullable Bundle savedInstanceState) {
        registerLocalReceiver();
        registerPiaDramaLocalReceiver();
        BaseGiftLoader liveGiftLoader = LiveGiftLoader.getInstance(LiveGiftLoader.class);
        liveGiftLoader.getGiftInfoCombineLiveData().observe(getViewLifecycleOwner(), new Observer<GiftInfoCombine>() {
            @Override
            public void onChanged(GiftInfoCombine giftInfoCombine) {
                try {
                    IAnchorGiftPanelComponent component = getPresenter().getComponentManager().getComponent(AnchorCompConfig.COMPONENT_GIFT_PANEL);
                    if (component.getGiftDialog() != null) {
                        component.getGiftDialog().updatePackageData(giftInfoCombine);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        if (pkSearchHostViewModel != null) {
            pkSearchHostViewModel.getSearchHistorySuccess().observe(getViewLifecycleOwner(), livePkSearchRecordModels -> {
                if (pkSearchHostView != null) {
                    pkSearchHostView.setPkSearchRecordData(livePkSearchRecordModels);
                }
            });

            pkSearchHostViewModel.getSearchHistoryFailed().observe(getViewLifecycleOwner(), show -> {
                if (pkSearchHostView != null && show) {
                    pkSearchHostView.searchRecordEmptyView();
                }
            });

            pkSearchHostViewModel.getClearSearchHistoryResult().observe(getViewLifecycleOwner(), result -> {
                if (pkSearchHostView != null && result) {
                    pkSearchHostView.searchRecordEmptyView();
                }
            });
        }
        if (mLiveHostMicViewModel != null) {
            mLiveHostMicViewModel.isMicPkEnterShow().observe(getViewLifecycleOwner(), isShow -> {
                // 告知底部控件
                IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
                if (null != component) {
                    component.setMicPkWhiteList(isShow);
                }
            });
        }
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void onDestroyView() {
        unregisterLocalReceiver();
        unregisterPiaDramaLocalReceiver();

        if (getAvService() != null) {
            getAvService().stopLocalPreview();
            getAvService().leaveRoom(true, false);
            getAvService().unInit();
        }

        //增加数据存储
        VideoLiveBeautifyCache.getInstance().saveLocalBeautifyInfoToSp();

        reportBeautifySetting(false);

        AIGiftShowManager.getInstance().closeHostRoom();

        super.onDestroyView();

        LiveCommonPopQueueManager.getInstance().leaveHost();

        LivePhotoService.get().stopWatchGalleryChange();
    }

    private void registerLocalReceiver() {
        if (mLocalBroadcastReceiver == null) {
            mLocalBroadcastReceiver = new LocalBroadcastReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(ILiveFragmentAction.LOCAL_BROADCAST_ACTION_OPEN_LISTEN_AWARD);
            intentFilter.addAction(CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_SEND_MESSAGE);
            intentFilter.addAction(CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_DISMISS_AI_DOT);
            LocalBroadcastManager.getInstance(mContext).registerReceiver(mLocalBroadcastReceiver, intentFilter);
        }
    }

    private void unregisterLocalReceiver() {
        if (mLocalBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mLocalBroadcastReceiver);
            mLocalBroadcastReceiver = null;
        }
    }

    public void registerPiaDramaLocalReceiver() {
        if (mPiaDramaLocalBroadcastReceiver == null) {
            mPiaDramaLocalBroadcastReceiver = new LocalPiaDramaBroadcastReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(PiaConstantKt.LOCAL_BROADCAST_ACTION_SELECT_PIA_SCRIPT);
            LocalBroadcastManager.getInstance(mContext).registerReceiver(
                    mPiaDramaLocalBroadcastReceiver, intentFilter
            );
        }
    }

    public void unregisterPiaDramaLocalReceiver() {
        if (mPiaDramaLocalBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mPiaDramaLocalBroadcastReceiver);
            mPiaDramaLocalBroadcastReceiver = null;
        }
    }

    @Override
    public boolean hasDialogShowing() {
        boolean dialogShowing = LiveHostCommonUtil.hasDialogOrDialogFragmentShowing();
        boolean keyBoardShow = false;
        try {
            IInputPanelComponent component = getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_INPUT_PANEL);
            keyBoardShow = component.isKeyboardPanelShowed();
        } catch (Exception e) {
            e.printStackTrace();
        }
        boolean isUserPopShow = mUserPop != null && mUserPop.isShowing();
        boolean isIdle = !isFragmentScrollStateIdle();
        return dialogShowing
                || keyBoardShow
                || isUserPopShow
                || isIdle;
    }


    public void onRtcRoomDisconnect() {
        if (!canUpdateUi()) {
            return;
        }

        showPublishError();

        Logger.i(TAG, "onDisconnect");
    }


    public void onJoinRtcRoom(int stateCode) {
        if (!canUpdateUi()) {
            return;
        }

        if (stateCode == 0) {
            handPublishStart();
        } else {
            CustomToast.showDebugFailToast("推流失败 onStartResult  success = false" +
                    " stateCode = " + stateCode);
            showPublishError();
        }

    }


    public void onMixStreamFailed(int stateCode) {
        if (stateCode != 0) {
            CustomToast.showDebugFailToast("混流失败 onStartResult  success = false" +
                    " stateCode = " + stateCode);
            showPublishError();
        }
    }

    public void onRtcRoomKickOut() {
        //防止onExitBtnPress 弹出结束直播对话框
        hasStarted = false;
        isKicked = true;

        releaseResource(true);
        if (canUpdateUi()) {
            dismissAllDialog();
            finishFragment();
            CustomToast.showToast("该账号已在其它设备登录，请到新设备上直播");
        }
    }


    public void onMicViewStatusChanged(boolean isShow) {

        if (!canUpdateUi()) {
            return;
        }

        isAudiMicViewShow = isShow;

        IAVChatListComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.updateRightMargin(needChatListBigRightMargin());
        }
    }

    @Override
    public void onGoodsOperationViewShow(boolean isShow) {
        if (!canUpdateUi()) {
            return;
        }

        isGoodsOperationViewShow = isShow;

        IAVChatListComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.updateRightMargin(needChatListBigRightMargin());
        }
    }

    @Override
    public void onTopOperationViewShow(boolean isShow) {

        if (!canUpdateUi()) {
            return;
        }

        isTopOperationViewShow = isShow;

        IAVChatListComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.updateRightMargin(needChatListBigRightMargin());
        }
    }

    @Override
    public void onBottomOperationViewShow(boolean isShow) {

        if (!canUpdateUi()) {
            return;
        }

        isBottomOperationViewShow = isShow;

        IAVChatListComponent comp = getComponentSafety(IBaseRoomCompConfig.COMPONENT_CHAT_LIST);
        if (comp != null) {
            comp.updateRightMargin(needChatListBigRightMargin());
        }
    }

    @Override
    public void onClearSearchHistory() {
        if (pkSearchHostViewModel != null) {
            pkSearchHostViewModel.clearSearchHistory();
        }
    }

    @Override
    public void getSearchHistory() {
        if (pkSearchHostViewModel != null) {
            pkSearchHostViewModel.getSearchHistory();
        }
    }

    /**
     * 聊天列表组件是否需要右侧大间距
     */
    private boolean needChatListBigRightMargin() {
        return isAudiMicViewShow || isGoodsOperationViewShow ||
                isTopOperationViewShow || isBottomOperationViewShow;
    }

    final class LocalBroadcastReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null || !canUpdateUi() || !isResumed()) {
                return;
            }

            if (CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_SEND_MESSAGE.equals(intent.getAction())) {
                //h5调原生发言
                String content = intent.getStringExtra(CommonXmlObjcJsCall.KEY_SEND_MESSAGE);
                if (!TextUtils.isEmpty(content)) {
                    sendMessage(content);
                }
            } else if (CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_DISMISS_AI_DOT.equals(intent.getAction())) {
                String content = intent.getStringExtra(CommonXmlObjcJsCall.KEY_AI_DOT_URL);
                IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
                if (null != component) {
                    component.dismissAiDot(content);
                }
            }
        }
    }

    final class LocalPiaDramaBroadcastReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) return;

            if (PiaConstantKt.LOCAL_BROADCAST_ACTION_SELECT_PIA_SCRIPT.equals(intent.getAction())) {
                // 选择 Pia 戏剧本，清空直播间之上页面
                clearTopPage();
                PiaDramaModel model = intent.getParcelableExtra(BundleKeyConstantsInLive.SOURCE);
                IPiaModeComponent piaModeComponent = getComponentSafety(IBaseRoomCompConfig.COMPONENT_PIA_PANEL);
                if (null != piaModeComponent && null != model) {
                    piaModeComponent.chooseDrama(model);
                }
            }
        }
    }

    public LoveModeMicStateManager.MicStateObserver getMicStateObserver() {
        return mHostRoomImplHelper.getHostRoomFragment();
    }

    public IRoomModeFragment getRoomModeFragment() {
        return mHostRoomImplHelper.getRoomModeFragment();
    }

    @Override
    public void showPkSearchHostView() {
        int mediaType = LiveMediaType.TYPE_AUDIO;
        if (mRoomDetail != null) {
            mediaType = mRoomDetail.getMediaType();
        }
        pkSearchHostView = new PKSearchHostView(getContext(), mediaType);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT);
        mRootView.addView(pkSearchHostView, params);
        getSearchHistory();
        pkSearchHostView.setPKSearchRecordClickListener(this);
    }

    @Override
    public void showStarCraftBoxAnimate(String path, long templateId) {

    }

    @Override
    public boolean onBackPressed() {
        return super.onBackPressed();
    }

    @Override
    public void onChatRoomJoinResult(boolean success, int code, String msg) {
        super.onChatRoomJoinResult(success, code, msg);

        mLoveModeAnchor.initAfterJoinChatRoom();
    }

    @Override
    public void onReceivedQueryRoomModeRsp(CommonChatQueryRoomModeRsp queryRoomModeRsp) {
        super.onReceivedQueryRoomModeRsp(queryRoomModeRsp);

        if (queryRoomModeRsp == null
                || queryRoomModeRsp.mResultCode != BaseCommonProtoConstant.CommonResultCode.RESULT_OK) {
            return;
        }

        if (queryRoomModeRsp.mRoomId != mRoomId) {
            return;
        }

        String lastModeString = RoomModeManager.getInstance().getModeString();
        Logger.i(TAG, "current mode: " + lastModeString + ", new mode: " + queryRoomModeRsp.mModeList);

        boolean needTips = RoomModeManager.needModeChangeTips(queryRoomModeRsp.mModeList);

        if (RoomModeManager.sameMode(queryRoomModeRsp)) {
            return;
        }

        if (RoomModeManager.isFriendsMode() && LoveModeManager.getInstance().notInjectManager()) {
            if (getRoomCore() != null) {
                LoveModeManagerInjectUtil.injectLoveMessageManager(getRoomCore().getLoveMessageManager());
                LoveModeManagerInjectUtil.injectLoveMessageDispatcherManager(getRoomCore().getLoveMessageDispatcherManager());
            }
            if (mLoveModeAnchor != null) {
                mLoveModeAnchor.initAfterJoinChatRoom();
            }
        } else if (RoomModeManager.isPkMode() && LivePkHelper.getInstance().notInjectManager()) {
            PkModeManagerInjectUtil.injectPkMessageManager(mPkMessageManager);
            PkModeManagerInjectUtil.injectPkDispatcherManager(mPkMessageDispatcherManager);
        }

        RoomModeManager.getInstance().onReceivedQueryRoomModeRspMessage(queryRoomModeRsp, needTips);

        if (mLoveModeAnchor != null) {
            mLoveModeAnchor.onReceivedQueryRoomModeRsp(queryRoomModeRsp);
        }
    }

    @Override
    public void onReceiveRoomLoveValueChange(CommonChatRoomLoveValueChangeMessage loveValueChangeMessage) {
        super.onReceiveRoomLoveValueChange(loveValueChangeMessage);
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.onAnchorRankChanged(loveValueChangeMessage);
        }
    }

    @Override
    public void onReceiveQueryTrafficCardInfoMessage() {
        super.onReceiveQueryTrafficCardInfoMessage();
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.handleTrafficCardInfo();
        }
    }

    @Override
    public void onReceiveGoShoppingMessage(CommonGoShoppingMessage goShoppingMessage) {
        super.onReceiveGoShoppingMessage(goShoppingMessage);
        try {
            IShoppingFloatComponent component = getPresenter().getComponentManager().getComponent(AudienceCompConfig.COMPONENT_SHOPPING_FLOAT);
            component.receiveGoShoppingMessage(goShoppingMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void showBottomBarGiftRedPoint(@Nullable PackageInfo.RedPoint redPoint) {
        super.showBottomBarGiftRedPoint(redPoint);
    }

    @Override
    public void onNetWorkChanged(boolean networkAvailable, boolean isWifi) {
        super.onNetWorkChanged(networkAvailable, isWifi);
        if (!canUpdateUi()) return;

        if (!networkAvailable) startLoading();
    }

    @Override
    protected void handleUserCardJumpOtherPage() {
        if (mOnlineNobleWrapper != null && mOnlineNobleWrapper.isShowing()) {
            mOnlineNobleWrapper.dismiss();
        }
    }

    private LiveWishFinishDialog mLiveWishFinishDialog;

    @Override
    public void onReceiveCompleteWishListMessage(CommonChatRoomCompleteWishListMessage message) {
        super.onReceiveCompleteWishListMessage(message);

        if (message != null && canUpdateUi()) {
            FragmentTransaction fragmentTransaction = getChildFragmentManager().beginTransaction();
            mLiveWishFinishDialog = (LiveWishFinishDialog) getChildFragmentManager().findFragmentByTag(LiveWishFinishDialog.TAG);
            if (mLiveWishFinishDialog != null) {
                fragmentTransaction.remove(mLiveWishFinishDialog);
            }
            mLiveWishFinishDialog = new LiveWishFinishDialog();
            mLiveWishFinishDialog.setWishCount(String.valueOf(message.cnt));
            mLiveWishFinishDialog.show(fragmentTransaction, LiveWishFinishDialog.TAG);
        }
    }

    @Override
    public void onReceiveMyInfoUpdateMessage() {
        super.onReceiveMyInfoUpdateMessage();
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.loadMoreMenuData();
        }
    }

    @Override
    public void onReceivedHeadLinesMessage(HeadAnchorInfo message) {
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.receiveTopHeadlinesMsg(message);
        }
    }

    @Override
    public void onReceiveHostOnlineListMessage(CommonChatRoomOnlineUserListMsg msg) {
        LiveHelper.Log.i(TAG, "onReceiveHostOnlineCountMessage");
        super.onReceiveHostOnlineListMessage(msg);
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.receiveHostOnlineListMessage(msg);
        }
    }

    @Override
    public void showCommonModeUI() {
        super.showCommonModeUI();
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.updateOpenCallUIByOpenCallUIEnableState(true);
            component.onFriendModeUISwitch(!RoomModeManager.isPkMode());
        }
    }

    @Override
    public void initFriendModeUI() {
        super.initFriendModeUI();
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.updateOpenCallUIByOpenCallUIEnableState(false);
            component.onFriendModeUISwitch(false);
        }
    }

    @Override
    public void releaseFriendModeUI() {
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.onFriendModeUISwitch(true);
        }
    }

    @Override
    public void initPkModeUI() {
        super.initPkModeUI();
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.onFriendModeUISwitch(false);
        }
    }


    @Override
    public void releasePkModeUI() {
        showNormalBackground();
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.onFriendModeUISwitch(true);
        }
    }

    public void releasePkMicUI() {
        IHostMicComponent micComponent = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
        if (micComponent != null) {
            micComponent.releaseMicPkUI();
        }

        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.releaseMicPkUI();
        }
    }

    public void releaseMicUI() {
        IHostMicComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
        if (component != null) {
            component.releaseMicUI();
        }
    }


    /**
     * 收到主播强提示消息，空实现，目前只有LamiaHost有这个需求
     *
     * @param warningMessage 心愿单完成消息
     */
    @Override
    public void onReceiveAnchorVerifyWarningMessage(CommonChatRoomAnchorVerifyWarningMessage warningMessage) {
        if (mWarningDialogFactory == null) {
            mWarningDialogFactory = new WarningDialogFactory();
        }
        mWarningDialogFactory.getWarningDialog(warningMessage).showNow(getChildFragmentManager(), LiveWarningDialog.class.getName());
    }

    public boolean isOnMicStatus() {
        IHostMicComponent micComponent = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
        if (micComponent != null) {
            return micComponent.isOnHostMicConnected() || micComponent.getOnlineAudiencesCount() > 0;
        }

        return false;
    }

    @Override
    public boolean isOnMicPKStatus() {
        IHostMicComponent micComponent = getComponentSafety(AudienceCompConfig.COMPONENT_HOST_MIC);
        if (micComponent != null) {
            return micComponent.isOnHostMicPkConnected();
        }
        return false;
    }

    @Override
    public void onReceiveCarouselRankMessage(LiveCarouselRankNotify message) {
        IHostHeaderComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_HEADER);
        if (component != null) {
            component.updateCarouselRankMsg(message);
        }
    }

    @Override
    public void onFansRemindMessage(LiveFansRemindMessage fansRemindMsg) {
        // no-op，主播收到粉丝团开通提醒消息，由于现在主播默认开通粉丝团，已经不存在这个推送场景
    }

    @Override
    public void onReceiveNotifyBottomButton(CommonChatRoomNotifyBottomButtonMsg msg) {
        Logger.i(TAG, "onReceiveNotifyBottomButton, msg = " + msg);
        super.onReceiveNotifyBottomButton(msg);
        if (msg == null) {
            return;
        }
        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.requestBottomButtonList(msg.position, true);
            component.loadMoreMenuData();
        }
    }

    @Override
    public void onReceivedHotTopicMessage(CommonChatHotTopicMessage message) {
        if (message == null || mRoomDetail == null || mRoomDetail.getLiveRecordInfo() == null) {
            return;
        }
        super.onReceivedHotTopicMessage(message);

        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createHotTopicMsg(
                mRoomDetail, message
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    public void onEntryAddFragment(Fragment fragment) {
        super.onEntryAddFragment(fragment);

        dismissOnlinePageH5Dialog();

        IHostBottomComponent component = getComponentSafety(IBaseRoomCompConfig.COMPONENT_BOTTOM_BAR);
        if (null != component) {
            component.dismissBottomTips();
        }
    }

    private void reportBeautifySetting(boolean isOpen) {
        if (mLiveMediaType == LiveMediaType.TYPE_VIDEO) {
            VideoLiveBeautifyCache.getInstance()
                    .reportLiveBeautifySetting(true, isOpen ? "开播" : "关播",
                            getLiveId());

            VideoLiveBeautifyCache.getInstance()
                    .reportLiveBeautifySetting(false, "使用道具",
                            getLiveId());
        }
    }

    @Override
    public void onReceiveAnchorTaskMsg(LiveAnchorTaskMessage msg) {
        super.onReceiveAnchorTaskMsg(msg);
        ITaskComponent hostTaskComponent = getComponentSafety(COMPONENT_HOST_TASK);
        if (hostTaskComponent != null) {
            List<LiveAnchorTaskMessage.AnchorTaskOrWish> tasks = msg.getAnchorTaskVos();
            if (tasks != null && !tasks.isEmpty() && msg.getType() == TYPE_ANCHOR_TASK && msg.getLevel() > 0) {
                for (int i = 0; i < tasks.size(); i++) {
                    LiveAnchorTaskMessage.AnchorTaskOrWish anchorTask = tasks.get(i);
                    anchorTask.setLevel(msg.getLevel());
                }
            }
            hostTaskComponent.onReceivedAnchorTaskMessage(msg);
        }
    }

    @Override
    public void onHostTaskCompleteNotifyMessage(LiveTaskNotify message) {
        LamiaHelper.Log.i("主播任务：长链-完成", message.toString());
        IAVChatListGuideComponent comp = getComponentSafety(COMPONENT_CHAT_LIST_GUIDE);
        if (comp != null) comp.showAnchorTaskGuide(message);
    }

    @Override
    public void onReceiveAiSoundPenaltyMessage(@NonNull LiveAiSoundPenalty message) {
        ILiveAiPropComponent component = getComponentSafety(COMPONENT_AI_PENALTY);
        if (component != null) {
            component.onReceiveAiPropMessage(message);
        }
    }

    /**
     * 主播收到im
     *
     * @param message LiveImAiHelperMsg
     */
    @Override
    public void onHostChatAiHelperNotifyMessage(LiveImAiHelperMsg message) {
        super.onHostChatAiHelperNotifyMessage(message);
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createChatAiHelperMsg(message);
        if (msg != null) onReceiveChatMessage(msg);
    }

    /**
     * 主播收到Ai礼物
     *
     * @param message 礼物消息内容
     */
    @Override
    public void onReceiveAiGiftShowMsg(LiveGetAiGiftMsg message) {

        //SystemMsg中接收到ai礼物
        AIGiftShowManager.getInstance().onHostRecieveAiGiftMsg(message);

    }

    @Override
    public void onReceiveAuditMsg(LiveAuditNotify message) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createAuditMsg(message);
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    protected void postMiniRoomRecord(BaseVirtualRoom room) {

    }

    @Override
    protected void postViewRoomRecord() {

    }

    @Override
    protected void updatePremiereInfo(PremiereInfo premiereInfo) {
        super.updatePremiereInfo(premiereInfo);
        if (premiereInfo != null) {
            IPremierePopComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_PREMIERE_SOME_POP);
            if (null != component) {
                component.onPremiereUpdate();
            }
        }
    }

    @Override
    protected void handPublishStart() {
        super.handPublishStart();
        if (mRoomDetail == null || !mRoomDetail.isPremiereFlag()) {
            return;
        }
        IPremierePopComponent component = getComponentSafety(AudienceCompConfig.COMPONENT_PREMIERE_SOME_POP);
        if (null != component) {
            component.handPublishStart();
        }
    }

    @Override
    public void addAudienceMessage(CommonChatAudienceMessage audienceMsg) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.createAudienceMsg(
                mRoomDetail, audienceMsg, true
        );
        if (msg != null) onReceiveChatMessage(msg);
    }

    @Override
    public void onReceiveShareRoomLiveMessage(CommonChatShareLiveRoomMessage message) {
        CommonChatMessage msg = AVChatListMsgProducer.INSTANCE.produceMessage(
                ChatItemViewType.TYPE_COMMON_SHARE_ROOM, message
        );
        if (msg != null) onReceiveChatMessage(msg);
    }
}
