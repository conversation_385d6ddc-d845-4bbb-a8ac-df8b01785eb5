// 下面的配置一个字母都不能错，包括大小写，bundle 名称必须和configure.java 类中定义的一致。
ext {
    PRE_INSTALL_BUNDLES = "record,live,chat,video,main,reactnative,music"
    // 这里面的也相当于是buildin的，因此buildin ；线上打包build in也会走这里，所以不要轻易修改！！
    // 注意，这里不影响local.properties 中的配置，两个分别是线上和本地

    Properties localProperties = new Properties()
    try {
        localProperties.load(file('local.properties').newDataInputStream())
    } catch (Exception e) {
        def rootDir = project.rootProject.getProjectDir()
        if (rootDir.path.contains("/buildSrc")) {
            rootDir = project.rootProject.getProjectDir().getParent()
        }
        localProperties.load(new File(rootDir, 'local.properties').newDataInputStream())
    }

    def excludeUnImportantPluginApk = localProperties.getProperty("EXCLUDE_UNIMPORTANT_PLUGIN_APK", "false")
    if (excludeUnImportantPluginApk.toBoolean()) {
        HOST_APK_BUNDLES = "main,live,video,reactnative,feed,search,login,discover,mylisten,vip,commercial"
    } else {
        HOST_APK_BUNDLES = "main,live,video,reactnative,feed,search,login,discover,mylisten,vip,commercial"
    }

    // 正式环境构建时，打成so 的方下面这里
    if (excludeUnImportantPluginApk.toBoolean()) {
        BUILD_TO_SO_BUNDLES = "chat,record,zone,shoot,elderly,supportchat,speechrecognition,rnunionpay,livesupport,readsupport,minidrama,liveasr,radio,kid,myclub,read,tecentvideo,cronet,ad"
    } else {
        BUILD_TO_SO_BUNDLES = "aliauth,radio,gamead,chat,record,zone,music,smartdevice,car,shoot,elderly,supportchat,speechrecognition,rnunionpay,loan,livesupport,readsupport,minidrama,liveasr,kid,myclub,read,tecentvideo,cronet,ad"
    }

    println "是否排除不重要的pluginAPK=$excludeUnImportantPluginApk"
    println "HOST_APK_BUNDLES=$HOST_APK_BUNDLES"
    println "BUILD_TO_SO_BUNDLES=$BUILD_TO_SO_BUNDLES"

    dispatch_bundle = [
            bundle : 'dispatch_bundle',
            version: '485.0',
    ]

    // 所有子bundle 剔除 host 和Xframework
    // 子bundle 不单独配置版本，打包时使用host 的版本，若需要单独给子bundle 提升版本，则打包参数加BUNDLE_VERSION_{bundle 名称大写}
    // 下面的bundle 有增加的话,请到 XAndroidFramework/AndroidStudioGradleRelyFile/safeProduction/pre-push这个文件下,
    // 将新增的bundle配置到 bundle_map, 并将hook-version.txt 里面的版本升级下
    plugin_paths = [
            aliauth    : [
                    bundle: 'AliAuthBundle',
                    module: 'AliAuthModule',
                    app   : 'AliAuthModuleApplication',
            ],
            car        : [
                    bundle: 'CarBundle',
                    module: 'CarModule',
                    app   : 'CarModuleApplication',
            ],
//            cartoon    : [
//                    bundle: 'CartoonBundle',
//                    module: 'CartoonModule',
//                    app   : 'CartoonModuleApplication',
//            ],
            chat       : [
                    bundle: 'ChatBundle',
                    module: 'ChatModule',
                    app   : 'ChatModuleApplication',
            ],
            supportchat: [
                    bundle: 'ChatSupportBundle',
                    module: 'ChatSupportModule',
                    app   : 'ChatSupportModuleApplication',
            ],
            feed       : [
                    bundle: 'FeedBundle',
                    module: 'FeedModule',
                    app   : 'FeedModuleApplication',
            ],
            gamead     : [
                    bundle: 'GameAdBundle',
                    module: 'GameAdModule',
                    app   : 'GameAdModuleApplication',
            ],
//            kids       : [
//                    bundle: 'KidsBundle',
//                    module: 'KidsModule',
//                    app   : 'KidsModuleApplication',
//            ],
            kid       : [
                    bundle: 'KidBundle',
                    module: 'KidModule',
                    app   : 'KidModuleApplication',
            ],
//            liteapp    : [
//                    bundle: 'LiteAppBundle',
//                    module: 'LiteAppModule',
//                    app   : 'LiteAppModuleApplication',
//            ],
            live       : [
                    bundle: 'LiveBundle',
                    module: 'LiveModule',
                    app   : 'LiveModuleApplication',
                    more  : ['CommonBiz',
                             'LiveCommon:common',
                             'LiveEntHall',
                             'LiveHost',
                             'LiveCourse',
                             'Anchor',
                             'LiveComponent',
                             'LiveHome',
                             'LiveAdapterInterface',
                             'LiveMainAppAdapterImpl',
                             'LiveAudience']
            ],
            login      : [
                    bundle: 'LoginBundle',
                    module: 'LoginModule',
                    app   : 'LoginModuleApplication',
            ],
            main       : [
                    bundle: 'MainBundle',
                    module: 'MainModule',
                    app   : 'MainModuleApplication',
            ],
            music      : [
                    bundle: 'MusicBundle',
                    module: 'MusicModule',
                    app   : 'MusicModuleApplication',
            ],
            radio      : [
                    bundle: 'RadioBundle',
                    module: 'RadioModule',
                    app   : 'RadioModuleApplication',
            ],
            read       : [
                    bundle: 'ReadBundle',
                    module: 'ReadModule',
                    app   : 'ReadModuleApplication',
            ],
            record     : [
                    bundle: 'RecordBundle',
                    module: 'RecordModule',
                    app   : 'RecordModuleApplication',
            ],
            reactnative: [
                    bundle: 'RnBundle',
                    module: 'RnModule',
                    app   : 'RnModuleApplication',
            ],
            rnunionpay : [
                    bundle: 'RnUnionpayBundle',
                    module: 'RnUnionpayModule',
                    app   : 'RnUnionpayModuleApplication',
            ],
//            sea        : [
//                    bundle: 'SeaBundle',
//                    module: 'SeaModule',
//                    app   : 'SeaModuleApplication',
//            ],
            search     : [
                    bundle: 'SearchBundle',
                    module: 'SearchModule',
                    app   : 'SearchModuleApplication',
            ],
            shoot      : [
                    bundle: 'ShootBundle',
                    module: 'ShootModule',
                    app   : 'ShootModuleApplication',
            ],
            smartdevice: [
                    bundle: 'SmartDeviceBundle',
                    module: 'SmartDeviceModule:SmartDevice',
                    app   : 'SmartDeviceApplication',
                    more  : ['SmartDeviceModule:DLNA',
                             'SmartDeviceModule:SmartDeviceFramework',
                             'SmartDeviceModule:WiFiControlLibrary']
            ],

            video      : [
                    bundle: 'VideoBundle',
                    module: 'VideoModule',
                    app   : 'VideoModuleApplication',
                    more  : ['Xmplayer4Enterprise', 'XmplayerSdk']
            ],
//            weike      : [
//                    bundle: 'WeikeBundle',
//                    module: 'WeikeModule',
//                    app   : 'WeikeModuleApplication',
//            ],
            zone       : [
                    bundle: 'ZoneBundle',
                    module: 'ZoneModule',
                    app   : 'ZoneModuleApplication',
            ],
            elderly    : [
                    bundle: 'ElderlyBundle',
                    module: 'ElderlyModule',
                    app   : 'ElderlyModuleApplication',
            ],
            discover         : [
                    bundle: 'DiscoverBundle',
                    module: 'DiscoverModule',
                    app   : 'DiscoverModuleApplication',
            ],
            mylisten         : [
                    bundle: 'MyListenBundle',
                    module: 'MyListenModule',
                    app   : 'MyListenModuleApplication'
            ],
            speechrecognition: [
                    bundle: 'SpeechRecognitionBundle',
                    module: 'SpeechRecognitionModule',
                    app   : 'SpeechRecognitionModuleApplication'
            ],
            vip        : [
                    bundle: 'VipBundle',
                    module: 'VipModule',
                    app   : 'VipModuleApplication'
            ],
            loan        : [
                    bundle: 'LoanBundle',
                    module: 'LoanModule',
                    app   : 'LoanModuleApplication'
            ],
            myclub    : [
                    bundle: 'MyclubBundle',
                    module: 'MyclubModule',
                    app   : 'MyclubModuleApplication'
            ],
            commercial    : [
                    bundle: 'CommercialBundle',
                    module: 'CommercialModule',
                    app   : 'CommercialModuleApplication',
                    more  : ['UniversalComponentSdk',
                             'DslComponentSdk',
                             'NativeComponentSdk',
                             'ComponentElementarySdk']
            ],
            livesupport       : [
                    bundle: 'LiveSupportBundle',
                    module: 'LiveSupportModule',
                    app   : 'LiveSupportModuleApplication',
            ],
            readsupport       : [
                    bundle: 'ReadSupportBundle',
                    module: 'ReadSupportModule',
                    app   : 'ReadSupportModuleApplication',
            ],
            ad       : [
                    bundle: 'AdBundle',
                    module: 'AdModule',
                    app   : 'AdModuleApplication',
                    more  : ['BaiDu']
            ],
            minidrama         : [
                    bundle: 'MiniDramaBundle',
                    module: 'MiniDramaModule',
                    app   : 'MiniDramaModuleApplication',
            ],
            liveasr         : [
                    bundle: 'LiveASRBundle',
                    module: 'LiveASRModule',
                    app   : 'LiveASRModuleApplication',
            ],
            tecentvideo         : [
                    bundle: 'TecentVideoBundle',
                    module: 'TecentVideoModule',
                    app   : 'TecentVideoModuleApplication',
            ],
            cronet         : [
                    bundle: 'CronetBundle',
                    module: 'CronetModule',
                    app   : 'CronetModuleApplication',
            ],
    ]
}