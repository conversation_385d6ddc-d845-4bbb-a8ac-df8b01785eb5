package com.ximalaya.ting.android.ad.baidu.model;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import androidx.annotation.Nullable;
import com.baidu.mobads.sdk.api.INativeVideoListener;
import com.baidu.mobads.sdk.api.NativeResponse;
import com.baidu.mobads.sdk.api.ShakeViewContainer;
import com.baidu.mobads.sdk.api.XNativeView;
import com.ximalaya.ting.android.ad.baidu.R;
import com.ximalaya.ting.android.ad.manager.AdStateReportManager;
import com.ximalaya.ting.android.ad.baidu.manager.BaiduSdkManager;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd;
import com.ximalaya.ting.android.ad.model.thirdad.VideoParamModel;
import com.ximalaya.ting.android.adsdk.base.log.AdLogger;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.host.data.model.ad.thirdad.AdVideoStateManager;
import com.ximalaya.ting.android.host.data.model.ad.thirdad.IThirdAdStatueCallBack;
import com.ximalaya.ting.android.host.data.model.ad.thirdad.IThirdAdStatueHasNoRecordCallBack;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.view.ad.advideo.IAdVideoCloseHandler;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmutil.Logger;
import java.util.List;

public class BaiduNativeThirdAd extends AbstractThirdAd<NativeResponse> {

    private int curAppDownloadStatus = APP_STATUS_NO_DOWNLOADED;
    private int curAppDownloadProgress;
    private IThirdAdStatueCallBack mThirdAdStatueCallBack;
    private boolean isFristSetRegister;
    private Runnable mCheckIsExposedRunnable;
    private XNativeView videoView;

    public BaiduNativeThirdAd(Advertis advertis, NativeResponse nativeResponse, String dspPositionId) {
        super(advertis, nativeResponse, dspPositionId);
    }

    private void adStatusChange() {
        if (mThirdAdStatueCallBack != null) {
            mThirdAdStatueCallBack.onADStatusChanged();
        }
    }

    @Override
    public String getImgUrl() {
        if (getAdData() != null) {
            return getAdData().getImageUrl();
        }
        return null;
    }

    @Nullable
    @Override
    public String getAdIcon() {
        if (getAdData() != null) {
            return getAdData().getIconUrl();
        }
        return null;
    }

    @Override
    public int getType() {
        return THIRD_AD_BAIDU_NATIVE;
    }

    @Override
    public String getTitle() {
        if (getAdData() != null) {
            return getAdData().getTitle();
        }
        return null;
    }

    @Override
    public String getDesc() {
        if (getAdData() != null) {
            return getAdData().getDesc();
        }
        return null;
    }

    @Override
    public String getButtonText() {
        if (getAdData() != null) {
            if (!TextUtils.isEmpty(getAdData().getActButtonString())) {
                return getAdData().getActButtonString();
            } else {
                return "立即查看";
            }
        }
        return null;
    }

    @Override
    public void setAdMark(ImageView imageView, int defaultAdTag) {
        if (imageView != null) {
            imageView.setVisibility(View.VISIBLE);
            imageView.setImageResource(R.drawable.baidu_ad_tag);
        }
    }

    @Override
    public int getPicHeight() {
        if (getAdData() != null) {
            return getAdData().getMainPicHeight();
        }
        return -1;
    }

    @Override
    public int getPicWidth() {
        if (getAdData() != null) {
            return getAdData().getMainPicWidth();
        }
        return -1;
    }

    @Override
    public String getLogoUrl() {
        if (getAdData() != null) {
            return getAdData().getBaiduLogoUrl();
        }
        return null;
    }

    @Override
    public boolean isAppAd() {
        if (getAdData() != null) {
            return getAdData().getAdActionType() == NativeResponse.ACTION_TYPE_APP_DOWNLOAD
                    && !TextUtils.isEmpty(getAdData().getAppVersion()) && !TextUtils.isEmpty(getAdData().getPublisher())
                    && !TextUtils.isEmpty(getAdData().getAppPrivacyLink()) && !TextUtils.isEmpty(getAdData().getAppPermissionLink());
        }
        return false;
    }

    @Override
    public int getAPPStatus() {
        return curAppDownloadStatus;
    }

    @Override
    public int getProgress() {
        return curAppDownloadProgress;
    }

    @Override
    public void negativeFeedback() {
    }

    @Override
    public void setShakeViewDismiss(boolean dismiss) {
        Logger.log("BaiduNativeThirdAd : shakeViewDismiss ");
        if (mShakeViewContainer != null) {
            mShakeViewContainer.destroy();
        }
    }

    @Override
    public void bindAdToView(Context context, ViewGroup viewGroup, List<View> clickViews,
                             FrameLayout.LayoutParams layoutParams,
                             @Nullable VideoParamModel videoParamModel,
                             IThirdAdStatueCallBack thirdAdStatueCallBack) {
        super.bindAdToView(context, viewGroup, clickViews, layoutParams, videoParamModel,
                thirdAdStatueCallBack);

        if (getAdData() == null || ToolUtil.isEmptyCollects(clickViews)) {
            return;
        }

        mThirdAdStatueCallBack = thirdAdStatueCallBack;
        videoView = null;
        if (getImageMode() == ITEM_VIEW_TYPE_VIDEO
                && videoParamModel != null) {
            videoParamModel.setAdVideoCloseHandler(new IAdVideoCloseHandler() {
                @Override
                public void close() {
                    onVideoComplete(videoParamModel, thirdAdStatueCallBack);
                }

                @Override
                public void videoReset() {
                    onVideoStop(videoParamModel);
                }
            });

            videoParamModel.setPlayMuteCallBack(new VideoParamModel.IOnPlayMuteChange() {
                @Override
                public void setPlayMute(boolean playMute, boolean is) {
                    if (videoView != null) {
                        videoView.setVideoMute(playMute);
                    }
                    HandlerManager.postOnUIThreadDelay(new Runnable() {
                        @Override
                        public void run() {
                            AdVideoStateManager.getInstance().onVideoPlayMuteChange(BaiduNativeThirdAd.this
                                    , playMute, videoParamModel, thirdAdStatueCallBack);
                        }
                    }, 100);
                }
            });
            videoView = BaiduSdkManager.addMediaViewToView(videoParamModel.getVideoLay(),
                    videoParamModel.getVideoCover());
            if (videoView != null) {
                videoView.setNativeItem(getAdData());
                videoView.setVideoMute(videoParamModel.isPlayMute());
                videoView.setNativeVideoListener(new INativeVideoListener() {
                    @Override
                    public void onRenderingStart() {
                        Logger.log("BaiduNativeThirdAd : onRenderingStart");
                        AdVideoStateManager.getInstance().onVideoStart(BaiduNativeThirdAd.this,
                                videoParamModel, thirdAdStatueCallBack);
                    }

                    @Override
                    public void onPause() {
                        Logger.log("BaiduNativeThirdAd : onVideoAdPaused ");
                        AdVideoStateManager.getInstance().onVideoPause(BaiduNativeThirdAd.this, videoParamModel, thirdAdStatueCallBack);
                    }

                    @Override
                    public void onResume() {
                        Logger.log("BaiduNativeThirdAd : onVideoAdResume ");
                        AdVideoStateManager.getInstance().onVideoResume(BaiduNativeThirdAd.this,
                                videoParamModel, thirdAdStatueCallBack);
//                        videoView.render();
                    }

                    @Override
                    public void onCompletion() {
                        Logger.log("BaiduNativeThirdAd : onVideoAdComplete ");
                        onVideoComplete(videoParamModel, thirdAdStatueCallBack);
                    }

                    @Override
                    public void onError() {
                        Logger.log("BaiduNativeThirdAd : onVideoError ");
                        AdVideoStateManager.getInstance().onVideoPlayError(BaiduNativeThirdAd.this, 0,
                                "", videoParamModel, thirdAdStatueCallBack);
                    }
                });
            }

            AdVideoStateManager.getInstance().onVideoInit(BaiduNativeThirdAd.this, videoParamModel,
                    thirdAdStatueCallBack);
        }


        if (thirdAdStatueCallBack instanceof IThirdAdStatueHasNoRecordCallBack) {
            // 延时检测广告是否正式上报了
            delayCheckAdRecord(viewGroup, thirdAdStatueCallBack);
        }
        curAppDownloadStatus = getDownloadAppStatus();
        adStatusChange();
        getAdData().registerViewForInteraction(viewGroup, clickViews, null, new NativeResponse.AdInteractionListener() {
            @Override
            public void onAdClick() {
                if (thirdAdStatueCallBack != null) {
                    thirdAdStatueCallBack.onADClicked();
                }
            }

            @Override
            public void onADExposed() {
                if (thirdAdStatueCallBack != null) {
                    thirdAdStatueCallBack.onADExposed();
                }

                Advertis advertis = getAdvertis();
                if (advertis != null) {
                    AdStateReportManager.getInstance().onShowSuccess(advertis, false,
                            false, advertis.getClientRequestTime(),
                            AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId()));
                }
                HandlerManager.removeCallbacks(mCheckIsExposedRunnable);
            }

            @Override
            public void onADExposureFailed(int i) {
                Logger.e("BaiduNativeThirdAd", "onADExposureFailed reason =" + i);
            }

            @Override
            public void onADStatusChanged() {
                curAppDownloadStatus = getDownloadAppStatus();
                adStatusChange();
            }

            @Override
            public void onAdUnionClick() {

            }
        });
        getAdData().setAdPrivacyListener(new NativeResponse.AdDownloadWindowListener() {
            @Override
            public void onADPrivacyClick() {
            }

            @Override
            public void onADFunctionClick() {
            }

            @Override
            public void onADPermissionShow() {

            }

            @Override
            public void onADPermissionClose() {
            }

            @Override
            public void adDownloadWindowShow() {
            }

            @Override
            public void adDownloadWindowClose() {

            }
        });
        if (videoView != null) {
            videoView.render();
        }
    }

    private void onVideoComplete(@Nullable VideoParamModel videoParamModel, IThirdAdStatueCallBack thirdAdStatueCallBack) {
        AdVideoStateManager.getInstance().onVideoCompleted(BaiduNativeThirdAd.this,
                videoParamModel, thirdAdStatueCallBack);
    }

    private void onVideoStop(@Nullable VideoParamModel videoParamModel) {
        AdVideoStateManager.getInstance().onVideoStop(BaiduNativeThirdAd.this, videoParamModel);
        if (videoParamModel != null) {
            videoParamModel.setSetVideoState(false);
        }
    }

    private void delayCheckAdRecord(ViewGroup viewGroup, IThirdAdStatueCallBack thirdAdStatueCallBack) {
        mCheckIsExposedRunnable = new Runnable() {
            @Override
            public void run() {
                boolean isRealShowing = AdManager.viewIsRealShowing(viewGroup);

                Advertis advertis = getAdvertis();
                if (advertis != null) {
                    setRecordonTimeOutNoRecord(true);
                    AdStateReportManager.getInstance().onShowFail(advertis,
                            isRealShowing
                                    ? AdStateReportManager.STATUS_MATERIALS_NO_RECORD_ON_SHOWED_AD_CALLBACK
                                    : AdStateReportManager.STATUS_MATERIALS_NO_RECORD_CALLBACK, 0,
                            AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId()), null);
                }


                if (thirdAdStatueCallBack instanceof IThirdAdStatueHasNoRecordCallBack) {
                    ((IThirdAdStatueHasNoRecordCallBack) thirdAdStatueCallBack).onTimeOutNoRecord(isRealShowing);
                }
            }
        };

        if (!isFristSetRegister) {
            isFristSetRegister = true;
            HandlerManager.postOnUIThreadDelay(mCheckIsExposedRunnable,
                    ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                            CConstants.Group_ad.ITEM_CHECK_SDK_RECORD, 1200));
        }
    }

    @Override
    public void onDestroy() {
        if (mCheckIsExposedRunnable != null) {
            HandlerManager.removeCallbacks(mCheckIsExposedRunnable);
            mCheckIsExposedRunnable = null;
        }
        videoView = null;
        Logger.log("BaiduNativeThirdAd : onDestroy ");
        if (mShakeViewContainer != null) {
            mShakeViewContainer.destroy();
        }
    }

    @Override
    public void onResume() {
    }

    @Override
    public void onPause() {
    }

    private ShakeViewContainer mShakeViewContainer;
    public void renderNativeShakeView(ViewGroup parentLayout,ViewGroup shakeView, IAbstractAd ad) {
        if (shakeView == null || ad == null) {
            Log.d("baiduShakeViewHelper", "baiduShakeViewHelper null");
            return ;
        }
        if(ad instanceof BaiduNativeThirdAd){
            Log.d("baiduShakeViewHelper", "baiduShakeViewHelper 摇一摇广告是百度侧");
            BaiduNativeThirdAd baiduNativeThirdAd = (BaiduNativeThirdAd) ad;
            NativeResponse response = baiduNativeThirdAd.getAdData();
            mShakeViewContainer= null;
            if (response != null && response.renderShakeViewContainer() != null) {
                ShakeViewContainer shakeViewContainer = response.renderShakeViewContainer();
                RelativeLayout innerContainer = shakeViewContainer.getContainer();
                if (innerContainer != null) {
                    innerContainer.removeAllViews();
                    //必须使用百度的容器把自定义的容器包裹起来，触发摇一摇
                    innerContainer.getLayoutParams().width = ViewGroup.LayoutParams.WRAP_CONTENT;
                    innerContainer.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
                    innerContainer.addView(shakeView);
                    parentLayout.addView(innerContainer);
                    mShakeViewContainer = shakeViewContainer;
                    mShakeViewContainer.resume();
                }
            }
        }
    }

    @Override
    public int getImageMode() {
        NativeResponse ad = getAdData();
        if (ad == null) {
            return ITEM_VIEW_TYPE_NORMAL;
        } else if (ad.getMaterialType() == NativeResponse.MaterialType.VIDEO) {
            return ITEM_VIEW_TYPE_VIDEO;
        }
        return super.getImageMode();
    }



    private int getDownloadAppStatus() {
        if (getAdData() == null) {
            return APP_STATUS_NO_DOWNLOADED;
        }
        int status = getAdData().getDownloadStatus();
//        -1：未下载
//        0~100：下载中，数值代表进度
//        101：下载完成
//        102：暂停/取消下载
//        103：已安装
//        104：下载失败
        if (status >= 0 && status <= 100) {
            curAppDownloadProgress = status;
            return APP_STATUS_DOWNLOADING;
        } else if (status == 101) {
            return APP_STATUS_DOWNLOADED;
        } else if (status == 102) {
            return APP_STATUS_DOWNLOAD_PAUSED;
        } else if (status == 103) {
            return APP_STATUS_INSTALLED;
        } else if (status == 104) {
            return APP_STATUS_DOWNLOAD_FAIL;
        } else {
            return APP_STATUS_NO_DOWNLOADED;
        }
    }

    @Override
    public double getRtbPrice() {
        try {
            if (getAdvertis() != null && !getAdvertis().isMobileRtb() && getAdvertis().getPrice() > 0) {
                return getAdvertis().getPrice();
            }
            if (getAdData() != null) {
                String price = getAdData().getECPMLevel();
                if (!TextUtils.isEmpty(price)) {
                    AdLogger.eToApm("------msg_rtb_async_splash", "BaiduNativeAd 获取 实时竞价价格  ---实时价格为 -----===" + price);
                    return Double.parseDouble(price) / 100d;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }
}
