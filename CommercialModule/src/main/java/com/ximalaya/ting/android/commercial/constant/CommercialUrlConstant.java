package com.ximalaya.ting.android.commercial.constant;

import com.ximalaya.ting.android.host.util.constant.UrlConstants;

import java.util.Locale;

/**
 * Created by 5Greatest on 2021.10.28
 *
 * <AUTHOR>
 * On 2021/10/28
 */
public class CommercialUrlConstant extends UrlConstants {
    private static final CommercialUrlConstant INSTANCE = new CommercialUrlConstant();

    public static CommercialUrlConstant getInstance() {
        return INSTANCE;
    }

    private CommercialUrlConstant() { }

    /**
     * 商品信息查询跳转地址
     * */
    public String getUniversalProductJumpUrl() {
        return "v1/route";
    }

    /**
     * 领券接口（2020.09.22）
     * 文档：http://gitlab.ximalaya.com/business/business-promotion-coupon/wikis/http#%25E6%2589%25B9%25E9%2587%258F%25E9%25A2%2586%25E5%2588%25B8%25E6%258E%25A5%25E5%258F%25A3
     */
    public String getRequestCouponUrl() {
        return getMNetAddressHost() + "promotion/coupon/user/allocate";
    }

    /**
     * 批量领券接口
     */
    public String getMultiRequestCouponUrl() {
        return getMNetAddressHost() + "promotion/coupon/user/multiAllocate";
    }

    public String getTrainingCampClockActivityCheckUrl() {
        return getMNetAddressHost() + "business-trainingcamp-mobile-web/client/clock/queryByContent";
    }

    // 获取单集专辑价格信息
    public String getSingleAlbumPrice(long albumId) {
        return getServerNetAddressHost() + "product/promotion/v1/single/track/" + albumId + "/price/dynamic/ts-" + System.currentTimeMillis();
    }

    /**
     * 重置当前用户对专辑的未读声音数
     * */
    public String getResetFeedUnreadTrackUrl() {
        return getServerNetSAddressHost() + "mobile-album/album/resetFeedUnread/ts-" + System.currentTimeMillis();
    }

    /**
     * 查询获取自制页的反馈页面信息
     * */
    public String getCustomAlbumFeedBackInfoUrl() {
        return getServerNetSAddressHost() + "product/detail/v1/questionnaire/ts-" + System.currentTimeMillis();
    }

    /**
     * 自制页曝光接口
     * */
    public String getCustomAlbumExposureUrl() {
        return getServerNetSAddressHost() + "business-sale-promotion-guide-mobile-web/exposure/v1";
    }

    public String getCustomAlbumSingleBuyStatusCheckUrl(long albumId) {
        return String.format(Locale.getDefault(), "%sproduct/promotion/v1/purchase/notes/purchase_entrance/%d/ts-%d", getServerNetAddressHost(), albumId, System.currentTimeMillis());
    }

}
