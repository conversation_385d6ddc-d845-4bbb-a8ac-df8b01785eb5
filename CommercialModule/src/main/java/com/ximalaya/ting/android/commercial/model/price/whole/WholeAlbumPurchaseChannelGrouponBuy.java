package com.ximalaya.ting.android.commercial.model.price.whole;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class WholeAlbumPurchaseChannelGrouponBuy implements Serializable {
    @SerializedName("behavior")
    public GrouponBuyBehavior behavior;
    @SerializedName("price")
    public WholeAlbumPurchasePrice price;

    public static class GrouponBuyBehavior extends WholeAlbumPurchaseBehavior {
        @SerializedName("isAttending")
        public boolean isAttending;//是否参团中
        @SerializedName("promotionId")
        public long promotionId; //促销id，发起拼团用
        @SerializedName("availableQuantity")
        public int availableQuantity;//差几人成团（参团中才有）
        @SerializedName("deadline")
        public long deadline;//截止时间
        @SerializedName("shareInfo")
        public ShareInfo shareInfo;
    }

    public static class ShareInfo {
        @SerializedName("icon")
        public String icon;
        @SerializedName("title")
        public String title;
        @SerializedName("subTitle")
        public String subTitle;
        @SerializedName("url")
        public String url;
    }
}
