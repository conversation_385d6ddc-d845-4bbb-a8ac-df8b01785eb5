package com.ximalaya.ting.android.live.biz.pia.entity;

import androidx.annotation.Keep;

import java.util.ArrayList;

/**
 * Created by zoey on 2021/9/8.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13037236220
 */
@Keep
public class PiaDramaFilterItems {

    // 分类
    public ArrayList<PiaFilterCategory> category;

    // 标签
    public ArrayList<PiaFilterTag> tag;

    // 排序方式
    public ArrayList<PiaFilterSort> sort;

    // 字数限制
    public ArrayList<PiaFilterWord> wordNum;

    @Keep
    public static class PiaFilterBase {
        public int id;
        public String name;
        public ArrayList<Integer> selectIds;

        // 本地自定义字段
        public boolean customChecked;
        public boolean customDisabled;
    }

    @Keep
    public static class PiaFilterCategory extends PiaFilterBase {}

    @Keep
    public static class PiaFilterTag extends PiaFilterBase {}

    @Keep
    public static class PiaFilterSort {
        public int id;
        public String name;

        // 本地自定义字段
        public boolean customChecked;
    }

    @Keep
    public static class PiaFilterWord extends PiaFilterBase {
        public Integer min;
        public Integer max;
    }
}
