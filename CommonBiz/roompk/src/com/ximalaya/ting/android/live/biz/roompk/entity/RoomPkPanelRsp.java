package com.ximalaya.ting.android.live.biz.roompk.entity;



import RM.Base.ResultCode;

/**
 * Created by qianmenchao on 2022/6/2.
 *
 * <AUTHOR>
 */
public class RoomPkPanelRsp  extends RoomBasePanelMsg{

    /**
     * 业务 code
     */
    public int mResultCode;
    /**
     * 错误原因，mResultCode != 0 && !TextUtil.isEmpty(mReason)，Toast 展示
     */
    public String mReason;
    /**
     * 消息唯一标识
     */
    public long mUniqueId;
    /**
     * 时间戳
     */
    public long mTimeStamp;
    /**
     * mResultCode == 0 时的提示消息
     */
    public String mTips;

    public boolean isSuccess() {
        return mResultCode == ResultCode.RESULT_CODE_OK.getValue();
    }

    @Override
    public String toString() {
        return "BaseCommonChatRsp{" +
                "mResultCode=" + mResultCode +
                ", mReason='" + mReason + '\'' +
                ", mUniqueId=" + mUniqueId +
                ", mTimeStamp=" + mTimeStamp +
                ", mTips='" + mTips + '\'' +
                '}';
    }

}
