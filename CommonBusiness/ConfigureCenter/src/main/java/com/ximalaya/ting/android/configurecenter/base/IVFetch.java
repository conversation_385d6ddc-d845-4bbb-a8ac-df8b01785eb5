package com.ximalaya.ting.android.configurecenter.base;

import com.ximalaya.ting.android.configurecenter.model.Item;

import java.util.List;
import java.util.Map;

/**
 * Created by LuHang on 2018/8/22
 *
 * <AUTHOR>
 * Email: <EMAIL>
 * Tel:15918812121
 */
public interface IVFetch {
    public void update(Item newItem) throws Exception;

    public boolean getBool(boolean defaultValue) throws Exception;

    public int getInt(int defaultValue) throws Exception;

    public float getFloat(float defaultValue) throws Exception;

    public String getString(String defaultValue) throws Exception;

    public Enum getEnum() throws Exception;

    public List getList() throws Exception;

    public Map getMap() throws Exception;

    public String getJson() throws Exception;
}
