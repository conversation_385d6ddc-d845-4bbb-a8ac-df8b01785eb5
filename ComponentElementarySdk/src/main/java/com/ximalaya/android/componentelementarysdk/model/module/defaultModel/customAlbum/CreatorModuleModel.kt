package com.ximalaya.android.componentelementarysdk.model.module.defaultModel.customAlbum

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import com.ximalaya.android.componentelementarysdk.model.module.BaseModuleModel

/**
 * Created by WolfXu on 2023/12/19.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
data class CreatorModuleModel(
    @SerializedName("creativeTeam")
    var creativeTeam: List<AnchorInfo?>? = null,
    @SerializedName("characterInfos")
    var characterInfos: List<CharacterInfo?>? = null,
) : BaseModuleModel() {
    override fun parseDataFromJson(jsonObject: JsonObject?): BaseModuleModel {
        try {
            return Gson().fromJson(jsonObject.toString(), CreatorModuleModel::class.java)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return this
    }

    fun isValid(): Boolean {
        return !this.creativeTeam.isNullOrEmpty() || !this.characterInfos.isNullOrEmpty()
    }

    data class AnchorInfo(
        @SerializedName("nickName", alternate = ["nickname"]) var nickName: String?,
        @SerializedName("logoPic") var logoPic: String?,
        @SerializedName("role") var role: String?,
        @SerializedName("displayName") var displayName: String?,
        @SerializedName("mainPageUrl") var mainPageUrl: String?,
        @SerializedName("userId") var userId: Long,
        @SerializedName("medalInfo") var medalInfo: MedalInfo?,
        @SerializedName("vLogoType") var vLogoType: Int,
        @SerializedName("isVerified") var isVerified: Boolean,
        @SerializedName("isSubscribed") var isSubscribed: Boolean
    )

    data class CharacterInfo(
        @SerializedName("characterName") val name: String?,
        @SerializedName("anchorInfo") val anchorInfo: AnchorInfo?,
        @SerializedName("characterHeaderImg") val characterHeaderImg: String?,
        @SerializedName("lovePoint") var lovePoint: Int,
        @SerializedName("characterId") val characterId: Long,
        @SerializedName("hasConfession") var hasConfession: Boolean,
        @SerializedName("characterTopic") val characterTopic: String?,
        @SerializedName("unitName") val unitName: String? // 单位名称
    )
}



