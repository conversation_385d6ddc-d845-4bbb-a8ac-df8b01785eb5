package com.ximalaya.ting.android.elderly.data.model;

import org.json.JSONObject;

/**
 * Author: <PERSON>
 * Email: <EMAIL>
 * Date: 2021/1/14
 * Description：
 */
public class ElderlyMetaDataModel {
    private long metadataId;
    private long metadataValueId;
    private String metadataValueName;
    private String metadataValueNum;

    public ElderlyMetaDataModel(JSONObject jsonObject) {
        if (jsonObject.has("metadataId")) {
            metadataId = jsonObject.optLong("metadataId");
        }
        if (jsonObject.has("metadataValueId")) {
            metadataValueId = jsonObject.optLong("metadataValueId");
        }
        if (jsonObject.has("metadataValueName")) {
            metadataValueName = jsonObject.optString("metadataValueName");
        }
        if (jsonObject.has("metadataValueNum")) {
            metadataValueNum = jsonObject.optString("metadataValueNum");
        }
    }


    public long getMetadataId() {
        return metadataId;
    }

    public void setMetadataId(long metadataId) {
        this.metadataId = metadataId;
    }

    public long getMetadataValueId() {
        return metadataValueId;
    }

    public void setMetadataValueId(long metadataValueId) {
        this.metadataValueId = metadataValueId;
    }

    public String getMetadataValueName() {
        return metadataValueName;
    }

    public void setMetadataValueName(String metadataValueName) {
        this.metadataValueName = metadataValueName;
    }

    public String getMetadataValueNum() {
        return metadataValueNum;
    }

    public void setMetadataValueNum(String metadataValueNum) {
        this.metadataValueNum = metadataValueNum;
    }
}
