package com.ximalaya.ting.android.liveaudience.adapter.home.mine;

import static com.ximalaya.ting.android.liveaudience.data.model.home.MineCenterModel.TYPE_ITING;
import static com.ximalaya.ting.android.liveaudience.data.model.home.MineCenterModel.TYPE_URL;

import android.app.Activity;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil;
import com.ximalaya.ting.android.liveaudience.data.model.home.MineCenterModel;
import com.ximalaya.ting.android.liveaudience.util.LiveUserTrackUtil;

/**
 * Created by zhixin.he on 2022/10/18.
 *
 * @desc 组员item
 * @email <EMAIL>
 * @phone 15026804470
 */
public class ChildVH extends RecyclerView.ViewHolder implements View.OnClickListener {

    private Activity mActivity;
    private ImageView mIv;
    private View mDot;
    private TextView mTv;
    private OnItemClickListener mOnItemClickListener;
    private MineCenterModel mMineCenterModel;

    public ChildVH(@NonNull View itemView, Activity activity) {
        super(itemView);
        mActivity = activity;
        mIv = itemView.findViewById(R.id.live_mine_child_iv);
        mDot = itemView.findViewById(R.id.live_mine_child_dot);
        mTv = itemView.findViewById(R.id.lvie_mine_child_tv);
        itemView.setOnClickListener(this);
    }

    public void onBindViewHolder(MineCenterModel mineCenterModel, OnItemClickListener onItemClickListener) {
        if (mineCenterModel == null || mActivity == null) {
            return;
        }
        mMineCenterModel = mineCenterModel;
        mOnItemClickListener = onItemClickListener;
        if (mIv == null || mDot == null || mTv == null) {
            return;
        }
        ImageManager.from(mActivity).displayImage(mIv, mineCenterModel.iconUrl, 0);
        mTv.setText(mineCenterModel.name);
        if (mineCenterModel.redPoint) {
            mDot.setVisibility(View.VISIBLE);
        } else {
            mDot.setVisibility(View.GONE);
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.live_mine_child_rl) {
            if (mMineCenterModel == null || mActivity == null) {
                return;
            }
            if (TYPE_URL == mMineCenterModel.urlType && !TextUtils.isEmpty(mMineCenterModel.url)) {
                NativeHybridFragment.start((MainActivity) MainApplication.getMainActivity()
                        , mMineCenterModel.url, true);
            } else if (TYPE_ITING == mMineCenterModel.urlType && !TextUtils.isEmpty(mMineCenterModel.url)) {
                LiveCommonITingUtil.handleITing(mActivity, mMineCenterModel.url);
            }
            LiveUserTrackUtil.trackMineCenterItemClick(mMineCenterModel.name);
            if (mOnItemClickListener == null) {
                return;
            }
            mOnItemClickListener.onItemClick(mMineCenterModel);
        }
    }

    public interface OnItemClickListener {
        void onItemClick(MineCenterModel mineCenterModel);
    }
}
