package com.ximalaya.ting.android.liveaudience.components.header;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.live.common.lib.base.mvp.IBaseModel;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.manager.LiveFollowInfoManager;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.lang.ref.WeakReference;

/**
 * 音视频直播间听众端头部.
 *
 * <AUTHOR>
 */
public class AudienceHeaderPresenter implements IAudienceHeaderComponent.IPresenter {
    public final String TAG = "LamiaHeaderPresenter";

    private final IAudienceHeaderComponent mView;
    private final WeakReference<BaseFragment2> mAttachFragment;

    public AudienceHeaderPresenter(@NonNull IAudienceHeaderComponent view, @NonNull BaseFragment2 attachFragment) {
        mView = view;
        mAttachFragment = new WeakReference<>(attachFragment);
    }

    @Override
    public void requestFollow(final PersonLiveDetail roomDetail) {
        if (roomDetail != null && !roomDetail.isFollowed() && canUpdateUi()) {
            String specificParams = LiveFollowInfoManager.getInstance().getFollowParams();
            AnchorFollowManage.followV3(
                    mAttachFragment.get().getActivity(), roomDetail.getHostUid(), false,
                    AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_ROOM,
                    AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_ROOM_HEADER, specificParams,
                    new IDataCallBack<Boolean>() {
                        @Override
                        public void onSuccess(@Nullable Boolean aBoolean) {
                            if (!canUpdateUi() || aBoolean == null || mView == null)
                                return;
                            roomDetail.setFollowed(aBoolean);
                            mView.onFollowSuccess(aBoolean);
                        }

                        @Override
                        public void onError(int code, String message) {
                            if (!canUpdateUi()) {
                                return;
                            }
                            mView.updateFollowedStatus();
                        }
                    }, true, true
            );
        }
    }

    private boolean canUpdateUi() {
        return mAttachFragment != null && mAttachFragment.get() != null && mAttachFragment.get().canUpdateUi();
    }


    @Override
    public IBaseModel getModel() {
        return null;
    }

    @Override
    public void init(Context context) {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void onSwitchRoom() {

    }
}
