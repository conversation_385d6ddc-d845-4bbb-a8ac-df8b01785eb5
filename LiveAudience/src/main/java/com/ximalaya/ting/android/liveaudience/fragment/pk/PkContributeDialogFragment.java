package com.ximalaya.ting.android.liveaudience.fragment.pk;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.BaseVerticalSlideContentFragment;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.view.other.MyViewPager;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.lib.base.adapter.BaseViewPagerAdapter;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.view.widget.LiveTabIndicator;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Arrays;
import java.util.List;

/**
 * PK 贡献榜弹窗页面
 *
 * <AUTHOR>
 */
public class PkContributeDialogFragment extends BaseVerticalSlideContentFragment implements View.OnClickListener {

    private static final String[] TAB_TITLE = new String[]{"我方贡献", "对方贡献"};

    public static final int TYPE_MINE = 1;
    public static final int TYPE_OTHER = 2;

    private static final List<Integer> REQUEST_CONTRIBUTE_ITEM = Arrays.asList(TYPE_MINE, TYPE_OTHER);

    private LiveTabIndicator mTabIndicator;
    private MyViewPager mViewPager;
    private ImageView mBgTopIv;
    private BaseViewPagerAdapter mAdapter;
    private TextView mHelpTv;

    private long mPkId;
    private boolean anonymousRoomFlag;
    private long mAnchorUid;
    private long mMatchAnchorUid;
    private boolean mIsAudience;
    private boolean mIsMatchHost;
    private long mCollectGiftId;
    private boolean mHideBtn;
    private boolean mIsRealShow;

    public static PkContributeDialogFragment newInstance(
            long pkId,
            long anchorUid,
            long matchAnchorUid,
            boolean isAudience,
            boolean isMatchHost,
            long collectGiftId,
            boolean hideBtn
    ) {
        PkContributeDialogFragment fragment = new PkContributeDialogFragment();
        fragment.mPkId = pkId;
        fragment.mAnchorUid = anchorUid;
        fragment.mMatchAnchorUid = matchAnchorUid;
        fragment.mIsAudience = isAudience;
        fragment.mIsMatchHost = isMatchHost;
        fragment.mCollectGiftId = collectGiftId;
        fragment.mHideBtn = hideBtn;
        return fragment;
    }

    public void setAnonymousRoomFlag(boolean anonymousRoomFlag) {
        this.anonymousRoomFlag = anonymousRoomFlag;
    }

    @Override
    protected String getPageLogicName() {
        return "PkContributeDialogFragment";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mTabIndicator = findViewById(R.id.live_pk_contribute_indicator);
        mBgTopIv = findViewById(R.id.live_iv_top_bg);
        mViewPager = findViewById(R.id.live_pk_contribute_view_pager);
        mHelpTv = findViewById(R.id.live_tv_pk_help);
        mHelpTv.setOnClickListener(this);
        setHelpBtnVisible(true);

        mViewPager.setOffscreenPageLimit(TAB_TITLE.length);
        mTabIndicator.setTitles(TAB_TITLE);
        mTabIndicator.setOnTabClickListener(new LiveTabIndicator.ITabClickListener() {

            @Override
            public void onTabClicked(int lastPosition, int clickPosition) {
                if (lastPosition == clickPosition) {
                    return;
                }

                if (mViewPager != null) {
                    mViewPager.setCurrentItem(clickPosition);
                }
            }
        });

        mAdapter = new BaseViewPagerAdapter<PkContributeItemFragment, Integer>(getChildFragmentManager());
        mAdapter.setFragmentCreator(new BaseViewPagerAdapter.IFragmentCreator<PkContributeItemFragment, Integer>() {
            @Override
            public PkContributeItemFragment newInstanceWithData(Integer type, int position) {
                Logger.d("pkContributeList", "newInstanceWithData: " + position + ", " + type);
                PkContributeItemFragment fragment;
                if (type == TYPE_OTHER) {
                    fragment = PkContributeItemFragment.newInstance(mPkId, mMatchAnchorUid, type, mIsRealShow);
                    fragment.setAnonymousRoomFlag(anonymousRoomFlag);
                } else {
                    fragment = PkContributeItemFragment.newInstance(mPkId, mAnchorUid, type, mIsRealShow);
                    fragment.setAnonymousRoomFlag(anonymousRoomFlag);
                }
                fragment.setAudience(mIsAudience);
                return fragment;
            }
        });
        mAdapter.setData(REQUEST_CONTRIBUTE_ITEM);
        mViewPager.addOnPageChangeListener(mPageChangeListener);
        if (mViewPager != null) {
            mViewPager.setAdapter(mAdapter);
        }

        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                if (mIsMatchHost) {
                    bindScrollViewForVerticalSlideDismiss(1);
                } else {
                    bindScrollViewForVerticalSlideDismiss(0);
                }
            }
        }, 500);

        if (mIsMatchHost) {
            mViewPager.post(new Runnable() {
                @Override
                public void run() {
                    mViewPager.setCurrentItem(1);
                }
            });
        }
    }

    @Override
    protected void loadData() {

    }

    private final ViewPager.OnPageChangeListener mPageChangeListener = new ViewPager.OnPageChangeListener() {
        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        }

        @Override
        public void onPageSelected(final int position) {
            int imgRes = position == 0 ? com.ximalaya.ting.android.live.host.R.drawable.live_bg_dialog_pk_contribute_top_mine : com.ximalaya.ting.android.live.host.R.drawable.live_bg_dialog_pk_contribute_top_other;
            mBgTopIv.setBackgroundResource(imgRes);
            int color = position == 0 ? com.ximalaya.ting.android.live.common.R.color.live_color_FF5E65 : com.ximalaya.ting.android.live.common.R.color.live_color_2ECEF8;
            setHelpBtnVisible(position == 0);
            mTabIndicator.setIndicatorColor(ContextCompat.getColor(mContext, color));
            mTabIndicator.setCurrentPosition(position, true);
            bindScrollViewForVerticalSlideDismiss(position);
        }

        @Override
        public void onPageScrollStateChanged(int state) {
        }
    };

    private void setHelpBtnVisible(boolean show) {
        mIsRealShow = show && showHelpBtn();
        UIStateUtil.showViewsIfTrue(mIsRealShow, mHelpTv);
    }

    private boolean showHelpBtn() {
        return mIsAudience && !mHideBtn;
    }

    //滑动、切换后重新绑定下拉消失的布局
    private void bindScrollViewForVerticalSlideDismiss(int position) {
        if (mViewPager.getAdapter() instanceof FragmentStatePagerAdapter) {
            FragmentStatePagerAdapter adapter = (FragmentStatePagerAdapter) mViewPager.getAdapter();
            Fragment fragment = adapter.getItem(position);
            if (fragment instanceof PkContributeItemFragment) {
                PkContributeItemFragment rankItemFragment = (PkContributeItemFragment) fragment;
                View scrollView = rankItemFragment.getRecyclerView();
                if (scrollView != null) {
                    bindSubScrollerView(scrollView);
                }
            }
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.liveaudience_fra_pk_contribute_list;
    }

    @Override
    public void onDestroyView() {
        if (mViewPager != null) {
            mViewPager.removeOnPageChangeListener(mPageChangeListener);
        }
        super.onDestroyView();
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (id == R.id.live_tv_pk_help) {
            dismiss();
            try {
                Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().openGiftPanel(getActivity(), 0, 0, 0,
                        mCollectGiftId, ParamsConstantsInLive.ITingOpenLiveType.TYPE_AUDIO);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
