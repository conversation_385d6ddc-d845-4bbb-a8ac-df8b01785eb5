package com.ximalaya.ting.android.liveaudience.manager;

import com.ximalaya.ting.android.live.common.lib.utils.LiveGsonUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.host.data.LiveMediaSideInfo;
import com.ximalaya.ting.android.live.lib.stream.medainfo.impl.MediaSideInfoManager;

/**
 * 个播媒体次要信息管理器。
 *
 * <AUTHOR>
 */
public class LiveMediaSideInfoManager extends MediaSideInfoManager<LiveMediaSideInfo> {

    @Override
    public LiveMediaSideInfo fromJson(String json) {

        try {
            return LiveGsonUtils.sGson.fromJson(json, LiveMediaSideInfo.class);
        } catch (Exception e) {
            LiveHelper.crashIfDebug(e);
        }
        return null;
    }

    @Override
    public String toJson(LiveMediaSideInfo mediaSideInfo) {
        try {
            return LiveGsonUtils.sGson.toJson(mediaSideInfo);
        } catch (Exception e) {
            LiveHelper.crashIfDebug(e);
            return "";
        }
    }

}
