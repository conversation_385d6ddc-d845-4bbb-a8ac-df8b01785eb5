package com.ximalaya.ting.android.liveaudience.net.impl;

import android.text.TextUtils;

import com.squareup.wire.Message;
import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.live.lib.chatroom.ChatRoomConnectionManager;
import com.ximalaya.ting.android.live.lib.chatroom.net.INetMessageDispatcher;
import com.ximalaya.ting.android.liveaudience.net.parser.PkProtoParser;
import com.ximalaya.ting.android.liveim.chatroom.IChatRoomMessageListener;
import com.ximalaya.ting.android.liveim.chatroom.entity.base.CacheMessage;
import com.ximalaya.ting.android.liveim.chatroom.entity.base.ChatMessage;
import com.ximalaya.ting.android.liveim.chatroom.entity.chat.CustomMessage;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import PK.XChat.MicPkInviteeSyncMsg;
import PK.XChat.MicPkInviterResultMsg;
import PK.XChat.MicPkPanelScore;
import PK.XChat.MicPkPanelSyncRsp;
import PK.XChat.MicPkResult;

/**
 * 长连接 PK 消息网络协议层分发。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @since 2019/4/5
 */
public class NetPkMessageDispatcherImpl implements INetMessageDispatcher {

    private static final String TAG = "INetPkMessageDispatcher";

    private static final String PROTO_PREFIX_PK = "PK";

    private final ChatRoomConnectionManager mChatRoomService;

    private final List<INetDispatchMessageListener> mListeners = new CopyOnWriteArrayList<>();

    private PkMessageReceiveListener mPkMessageReceiveListener;

    public NetPkMessageDispatcherImpl(ChatRoomConnectionManager chatRoomService) {
        mChatRoomService = chatRoomService;
    }

    @Override
    public void onStart() {
        mPkMessageReceiveListener = new PkMessageReceiveListener();
        mChatRoomService.registerChatMessageListener(mPkMessageReceiveListener);
    }

    @Override
    public void onStop() {
        mChatRoomService.unregisterChatMessageListener(mPkMessageReceiveListener);
    }

    @Override
    public void addListener(INetDispatchMessageListener l) {
        if (l == null || mListeners.contains(l)) {
            return;
        }

        mListeners.add(l);
    }

    @Override
    public void removeListener(INetDispatchMessageListener l) {
        if (l == null) {
            return;
        }

        mListeners.remove(l);
    }

    private void dispatch(Object message) {
        for (INetDispatchMessageListener l : mListeners) {
            l.dispatchMessage(message);
        }
    }

    private class PkMessageReceiveListener implements IChatRoomMessageListener {

        @Override
        public void onGetPushChatMsg(Message msg, String name) {
            if (msg == null || TextUtils.isEmpty(name) || !name.startsWith(PROTO_PREFIX_PK)) {
                return;
            }

            CommonLiveLogger.i(TAG, "PkMessageReceiveListener receive message, name = " + name + ", origin proto message = " + msg);

            Object message = null;

            if (msg instanceof PK.XChat.StartMatchRsp) {
                message = PkProtoParser.convert((PK.XChat.StartMatchRsp) msg);
            } else if (msg instanceof PK.XChat.CancelMatchRsp) {
                message = PkProtoParser.convertCommonPkRsp(msg);
            } else if (msg instanceof PK.XChat.OverPkRsp) {
                message = PkProtoParser.convertCommonPkRsp(msg);
            } else if (msg instanceof PK.XChat.QuitPkRsp) {
                message = PkProtoParser.convertCommonPkRsp(msg);
            } else if (msg instanceof PK.XChat.PanelSyncRsp) {
                message = PkProtoParser.convert((PK.XChat.PanelSyncRsp) msg);
            }  else if (msg instanceof PK.XChat.PanelSyncRspV2) {
                message = PkProtoParser.convert((PK.XChat.PanelSyncRspV2) msg);
            } else if (msg instanceof PK.XChat.PanelScore) {
                message = PkProtoParser.convert((PK.XChat.PanelScore) msg);
            } else if (msg instanceof PK.XChat.PkResult) {
                message = PkProtoParser.convert((PK.XChat.PkResult) msg);
            } else if (msg instanceof PK.XChat.MicStatusSyncRsp) {
                message = PkProtoParser.convert((PK.XChat.MicStatusSyncRsp) msg);
            } else if (msg instanceof PK.XChat.PkRankChange) {
                message = PkProtoParser.convert((PK.XChat.PkRankChange) msg);
            } else if (msg instanceof PK.XChat.PropPanel) {
                message = PkProtoParser.convert((PK.XChat.PropPanel) msg);
            } else if (msg instanceof PK.XChat.InviteeSyncMsg) {
                message = PkProtoParser.convert((PK.XChat.InviteeSyncMsg) msg);
            } else if (msg instanceof PK.XChat.InviterResultMsg) {
                message = PkProtoParser.convert((PK.XChat.InviterResultMsg) msg);
            } else if (msg instanceof PK.XChat.InviteeResultMsg) {
                message = PkProtoParser.convert((PK.XChat.InviteeResultMsg) msg);
            } else if (msg instanceof PK.XChat.MuteRsp) {
                message = PkProtoParser.convertCommonPkRsp(msg);
            } else if (msg instanceof PK.XChat.PanelAnimate) {
                message = PkProtoParser.convert((PK.XChat.PanelAnimate) msg);
            } else if (msg instanceof PK.XChat.JoinStarCraftRsp) {
                message = PkProtoParser.convertCommonPkRsp(msg);
            } else if (msg instanceof PK.XChat.MicPkInviteeSyncMsg) {
                message = PkProtoParser.convert((MicPkInviteeSyncMsg) msg);
            } else if (msg instanceof PK.XChat.MicPkInviterResultMsg) {
                message = PkProtoParser.convert((MicPkInviterResultMsg) msg);
            } else if (msg instanceof PK.XChat.MicPkPanelSyncRsp) {
                message = PkProtoParser.convert((MicPkPanelSyncRsp) msg);
            } else if (msg instanceof PK.XChat.MicPkPanelScore) {
                message = PkProtoParser.convert((MicPkPanelScore) msg);
            } else if (msg instanceof PK.XChat.MicPkResult) {
                message = PkProtoParser.convert((MicPkResult)msg);
            }

            if (message == null) {
                return;
            }

            dispatch(message);

            Logger.i(TAG, "receive message, name = " + name + ", parsed message = " + message);
        }

        @Override
        public void onChatMessageReceived(ChatMessage chatMessage) {

        }

        @Override
        public void onCustomMessageReceived(CustomMessage customMessage) {

        }

        @Override
        public void onCacheMessageReceived(CacheMessage cacheMessage) {

        }
    }


}
