package com.ximalaya.ting.android.liveaudience.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArraySet;

import java.util.Set;

/**
 * Created by 王旭 on 2018/12/14.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13162538925
 */
@SuppressLint("CustomViewStyleable")
public class HollowLayout extends FrameLayout {
    private Bitmap mBitmap;
    private Canvas mCanvas;
    private Paint mEraserPaint;
    private Context mContext;
    public int mBackgroundColor;

    private Set<Round> mHollowList = new ArraySet<>();

    public HollowLayout(@NonNull Context context) {
        super(context);
        mContext = context;
        init();
    }

    public HollowLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;

        TypedArray ta = context.obtainStyledAttributes(attrs, com.ximalaya.ting.android.live.host.R.styleable.LiveHollowLayout);
        mBackgroundColor = ta.getColor(com.ximalaya.ting.android.live.host.R.styleable.LiveHollowLayout_live_background_color, -1);
        ta.recycle();
        init();
    }

    public HollowLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    public void setBackgroundColor(int backgroundColor) {
        this.mBackgroundColor = backgroundColor;
        init();
        invalidate();
    }

    @SuppressWarnings("unused")
    public void addHollow(Round round) {
        mHollowList.add(round);
    }

    public void clear() {
        removeAllViews();

        if (mCanvas != null) {
            Paint paint = new Paint();
            paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
            mCanvas.drawPaint(paint);
        }
    }

    private void init() {
        setWillNotDraw(false);

        mBackgroundColor = mBackgroundColor != -1 ?
                mBackgroundColor : Color.parseColor("#8C000000");

        mEraserPaint = new Paint();
        mEraserPaint.setColor(0xFFFFFFFF);
        mEraserPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
        mEraserPaint.setFlags(Paint.ANTI_ALIAS_FLAG);
    }

    @SuppressLint("DrawAllocation")
    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);

        // 避免全面屏屏幕高度缺失
        mBitmap = Bitmap.createBitmap(getWidth()
                , getHeight(), Bitmap.Config.ARGB_8888);

        mCanvas = new Canvas(mBitmap);

        mBitmap.eraseColor(Color.TRANSPARENT);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        mCanvas.drawColor(mBackgroundColor);

        for (int i = 0; i < getChildCount(); i++) {
            View child = getChildAt(i);
            LayoutParams layoutParams = (LayoutParams) child.getLayoutParams();
            if (layoutParams.radius > 0) {
                mCanvas.drawCircle(child.getX(), child.getY(), layoutParams.radius, mEraserPaint);
            }
        }

        for (Round round : mHollowList) {
            mCanvas.drawCircle(round.x, round.y, round.radius, mEraserPaint);
        }

        canvas.drawBitmap(mBitmap, 0, 0, null);
    }

    @Override
    protected ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams lp) {
        return new LayoutParams(lp);
    }

    @Override
    public FrameLayout.LayoutParams generateLayoutParams(AttributeSet attrs) {
        return new LayoutParams(getContext(), attrs);
    }

    @Override
    protected boolean checkLayoutParams(ViewGroup.LayoutParams p) {
        return p instanceof LayoutParams;
    }

    public static class Round {
        int x;
        int y;
        int radius;

        Round(int x, int y, int radius) {
            this.x = x;
            this.y = y;
            this.radius = radius;
        }
    }

    public static class LayoutParams extends FrameLayout.LayoutParams {
        public float radius;

        public LayoutParams(@NonNull Context c, @Nullable AttributeSet attrs) {
            super(c, attrs);
            TypedArray ta = c.obtainStyledAttributes(attrs, com.ximalaya.ting.android.live.host.R.styleable.LiveHollowLayout_Layout);
            radius = ta.getDimension(com.ximalaya.ting.android.live.host.R.styleable.LiveHollowLayout_Layout_live_hollow_radius, -1);
            ta.recycle();
        }

        public LayoutParams(int width, int height) {
            super(width, height);
        }

        public LayoutParams(@NonNull ViewGroup.LayoutParams source) {
            super(source);
        }
    }
}