package com.ximalaya.ting.android.liveaudience.view;

import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.shareservice.base.IShareDstType;

import java.util.Arrays;
import java.util.List;

/**
 * 平铺的分享列表，点击分享到对应渠道。
 *
 * <AUTHOR>
 */
public class ShareView extends LinearLayout {
    public static final List<String> DEFAULT_SHARE_DST_LIST = Arrays.asList(
            IShareDstType.SHARE_TYPE_WX_CIRCLE,
            IShareDstType.SHARE_TYPE_WX_FRIEND,
            IShareDstType.SHARE_TYPE_SINA_WB,
            IShareDstType.SHARE_TYPE_QQ,
            IShareDstType.SHARE_TYPE_QZONE
    );

    private List<String> mShareDstTypeList = DEFAULT_SHARE_DST_LIST;
    private LinearLayout mContainerLayout;

    private Activity mActivity;
    private ShareWrapContentModel mShareWrapContentModel;
    private ItemUIChanger mItemUIChanger;

    private ITrackTraceEventListener mTraceEventListener;

    public ShareView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public ShareView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public void setTraceEventListener(ITrackTraceEventListener traceEventListener) {
        mTraceEventListener = traceEventListener;
    }

    private void init(Context context) {
        LayoutInflater layoutInflater = LayoutInflater.from(context);

        mContainerLayout = new LinearLayout(context);
        addView(mContainerLayout,
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
        );

        LayoutParams marginLp = new LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.MATCH_PARENT
        );
        int margin = BaseUtil.dp2px(context, 6);
        marginLp.setMargins(margin, 0, margin, 0);
        for (final String type : mShareDstTypeList) {
            View itemView = layoutInflater.inflate(R.layout.liveaudience_item_share, mContainerLayout, false);
            ImageView iconIv = itemView.findViewById(R.id.live_type_icon_iv);
            TextView nameIv = itemView.findViewById(R.id.live_type_name_iv);

            setItemUi(iconIv, nameIv);
            int iconId = -1;
            String typeName = "";
            if (IShareDstType.SHARE_TYPE_WX_CIRCLE.equals(type)) {
                iconId = com.ximalaya.ting.android.live.host.R.drawable.live_ic_share_wx_group;
                typeName = "微信朋友圈";
            } else if (IShareDstType.SHARE_TYPE_WX_FRIEND.equals(type)) {
                iconId = com.ximalaya.ting.android.live.host.R.drawable.live_ic_share_wx;
                typeName = "微信好友";
            } else if (IShareDstType.SHARE_TYPE_SINA_WB.equals(type)) {
                iconId = com.ximalaya.ting.android.live.host.R.drawable.live_ic_share_sina;
                typeName = "新浪微博";
            } else if (IShareDstType.SHARE_TYPE_QQ.equals(type)) {
                iconId = com.ximalaya.ting.android.live.host.R.drawable.live_ic_share_qq;
                typeName = "QQ";
            } else if (IShareDstType.SHARE_TYPE_QZONE.equals(type)) {
                iconId = com.ximalaya.ting.android.live.host.R.drawable.live_ic_share_q_zone;
                typeName = "QQ空间";
            }

            if (iconId != -1) {
                iconIv.setImageResource(iconId);
            }
            nameIv.setText(typeName);
            final String channelName = typeName;
            itemView.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (OneClickHelper.getInstance().onClick(v)) {
                        shareToDst(type);

                        if (mTraceEventListener != null) {
                            mTraceEventListener.onTrackClickShareChannelEvent(channelName);
                        }
                    }
                }
            });
            mContainerLayout.addView(itemView, marginLp);
        }

        mContainerLayout.setGravity(Gravity.CENTER);
    }

    /**
     * 支持外部动态修改 item ui
     */
    private void setItemUi(ImageView iconIv, TextView nameIv) {
        if (mItemUIChanger != null) {
            mItemUIChanger.changeItemUI(iconIv, nameIv);
        }
    }

    private void shareToDst(String shareDst) {
        if (mActivity == null) {
            return;
        }
        if (mShareWrapContentModel == null) {
            CustomToast.showFailToast("分享数据异常");
            return;
        }

        mShareWrapContentModel.shareDstName = shareDst;
        new ShareManager(mActivity, mShareWrapContentModel).getShareContent(mShareWrapContentModel);
    }

    public List<String> getShareDstTypeList() {
        return mShareDstTypeList;
    }

    public ShareView setShareDstTypeList(List<String> shareDstTypeList) {
        mShareDstTypeList = shareDstTypeList;
        return this;
    }

    public ShareView setActivity(Activity activity) {
        mActivity = activity;
        return this;
    }

    public ShareView setShareContentModel(ShareWrapContentModel shareWrapContentModel) {
        mShareWrapContentModel = shareWrapContentModel;
        return this;
    }

    public void setGravity(int gravity) {
        if (mContainerLayout != null) {
            mContainerLayout.setGravity(gravity);
        }
    }

    public ItemUIChanger getItemUIChanger() {
        return mItemUIChanger;
    }

    public ShareView setItemUIChanger(ItemUIChanger itemUIChanger) {
        mItemUIChanger = itemUIChanger;
        return this;
    }

    public interface ItemUIChanger {
        void changeItemUI(ImageView iconIv, TextView nameIv);
    }

    public interface ITrackTraceEventListener {

        void onTrackClickShareChannelEvent(String channelName);

    }
}
