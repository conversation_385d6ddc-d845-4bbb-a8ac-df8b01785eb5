<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/live_item_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="12dp">

    <TextView
        android:id="@+id/live_tv_index"
        android:layout_width="29dp"
        android:layout_height="24dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="12dp"
        android:gravity="center"
        android:textColor="@color/live_color_BFCADA"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="2" />

    <com.ximalaya.ting.android.live.common.view.widget.ClipCornerRelativeLayout
        android:id="@+id/live_rl_avatar"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="42dp"
        android:background="@drawable/live_ic_pk_contribute_item_second"
        app:live_root_corner_dimen="40dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/live_iv_avatar"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_centerInParent="true"
            app:corner_radius="40dp"
            android:scaleType="centerCrop"
            tools:src="@drawable/live_default_avatar_88" />

    </com.ximalaya.ting.android.live.common.view.widget.ClipCornerRelativeLayout>

    <TextView
        android:id="@+id/live_tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="96dp"
        android:textColor="@color/live_color_333333"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="艾雪爱" />

    <ImageView
        android:id="@+id/live_iv_first_kill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="4dp"
        tools:src="@drawable/live_pk_ic_first_kill_left"
        android:layout_toRightOf="@id/live_tv_name" />

    <TextView
        android:id="@+id/live_tv_score"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="16dp"
        android:textColor="@color/live_color_F54C6E"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="999，999" />

</RelativeLayout>