<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="CircularScrollView">
        <attr name="recycler_interval" format="integer" />

        <attr name="recycler_showIndicator" format="boolean" />
        <attr name="recycler_indicator_select_color" format="color" />
        <attr name="recycler_indicator_unselect_color" format="color" />
        <attr name="recycler_indicator_item_margin" format="dimension" />
        <attr name="recycler_indicator_edge_margin" format="dimension" />
        <attr name="recycler_indicator_radius" format="dimension" />
        <attr name="recycler_indicator_width" format="dimension" />
        <attr name="recycler_indicator_height" format="dimension" />

        <attr name="recycler_indicator_gravity" format="enum" >
            <enum name="recycler_gravity_center_horizontal" value="0" />
            <enum name="recycler_gravity_left" value="1" />
            <enum name="recycler_gravity_right" value="2" />
        </attr>

        <attr name="recycler_orientation" format="enum" >
            <enum name="recycler_horizontal" value="0" />
            <enum name="recycler_vertical" value="1" />
        </attr>
        <attr name="recycler_autoPlaying" format="boolean" />
        <attr name="recycler_itemSpace" format="integer" />
        <attr name="recycler_centerScale" format="float" />
        <attr name="recycler_moveSpeed" format="float" />
    </declare-styleable>
</resources>