<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/live_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:cardBackgroundColor="@color/live_white"
    app:cardCornerRadius="10dp">

    <com.ximalaya.ting.android.host.view.tbs.PullToRefreshX5WebView xmlns:ptr="http://schemas.android.com/apk/res-auto"
        android:id="@+id/live_webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/host_progress_bar"
        android:focusable="true"
        android:focusableInTouchMode="true"
        ptr:ptrScrollingWhileRefreshingEnabled="false" /><!--ptrScrollingWhileRefreshingEnabled是否支持 刷新中滑动页面-->

    <ProgressBar
        android:id="@+id/live_progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:max="100"
        android:progressDrawable="@drawable/component_progress"
        android:visibility="gone" />


    <include layout="@layout/livecomm_dialog_error_action" />

</androidx.cardview.widget.CardView>