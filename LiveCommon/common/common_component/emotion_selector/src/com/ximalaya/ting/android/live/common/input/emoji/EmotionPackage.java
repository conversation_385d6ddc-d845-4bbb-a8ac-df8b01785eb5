package com.ximalaya.ting.android.live.common.input.emoji;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.live.common.R;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * desc: 一套表情
 *
 * <AUTHOR> (zhangshixin)
 * @email <EMAIL>
 * @phoneNumber 18789440700
 * @since 2019/6/4.
 */
public class EmotionPackage implements IEmojiTab {
    public static final String SP_EMOJI_TAB = "live_emoji_tab";

    /**
     * emojis : [{"bgImagePath":"http://fdfs.test.ximalaya.com/group1/M00/5C/38/wKgDplzjpx-AUMHaABreLMmlVVw785.gif","childRank":1,"iconPath":"http://fdfs.test.ximalaya.com/group1/M00/05/1D/wKgD3lzjmqCAZpXXAACPs4WrvuU429.png","id":1110,"parentId":1107,"subEmojis":[{"childRank":1,"id":1117,"parentId":1110,"type":8},{"bgImagePath":"http://fdfs.test.ximalaya.com / group1 / M00 / 05 / AB / wKgD3lzwzU6AaXQQAACPs4WrvuU971.png ","childRank":2,"iconPath":"http://fdfs.test.ximalaya.com / group1 / M00 / 5 C / C6 / wKgDplzw2euAUe8lAACPs4WrvuU949.png ","id":1116,"parentId":1110,"type":8}],"type":8}]
     * groupId : 1107
     * headCoverPath : http://fdfs.test.ximalaya.com/group1/M01/05/0E/wKgD3lzjWomAKeIlAAAPZZHSMUw267.png
     * rank : 2
     */

    /**
     * //组templateid
     */
    private int groupId;
    /**
     * 表情包头图
     */
    private String headCoverPath;
    /**
     * //顺序
     */
    private int rank;
    private List<EmojiBean> emojis;

    private int iconResId;
    private int startPosition;
    public boolean showRedDot = false;
    private int positionInTab;
    private int pageCount;

    public static IEmojiTab newDefaultTab() {
        EmotionPackage emotionPackage = new EmotionPackage();
        emotionPackage.startPosition = 0;
        emotionPackage.positionInTab = 0;
        emotionPackage.iconResId = R.drawable.live_icon_emoj_default;
        return emotionPackage;
    }

    public static void saveRedPointIdToLocal(long id, boolean showRedDot) {
        LiveHelper.Log.i("saveRedPointIdToLocal: " + id + ", " + showRedDot);
        MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                .saveBoolean(EmotionPackage.SP_EMOJI_TAB + id, showRedDot);
    }

    public static boolean getRedPointIdFromLocal(long id) {
        boolean showRedDot = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
                .getBooleanCompat(EmotionPackage.SP_EMOJI_TAB + id);
        LiveHelper.Log.i("getRedPointIdFromLocal: " + id + ", " + showRedDot);

        return showRedDot;
    }

    public int getGroupId() { return groupId;}

    public void setGroupId(int groupId) { this.groupId = groupId;}

    public String getHeadCoverPath() { return headCoverPath;}

    public void setHeadCoverPath(String headCoverPath) { this.headCoverPath = headCoverPath;}

    public int getRank() { return rank;}

    public void setRank(int rank) { this.rank = rank;}

    public List<EmojiBean> getEmojis() { return emojis;}

    public void setEmojis(List<EmojiBean> emojis) { this.emojis = emojis;}

    @Override
    public long getId() {
        return groupId;
    }

    @Override
    public int getIconResId() {
        return iconResId;
    }

    @Override
    public String getIconUrl() {
        return headCoverPath;
    }

    @Override
    public int getStartPosition() {
        return startPosition;
    }

    @Override
    public int getPositionInTab() {
        return positionInTab;
    }

    @Override
    public boolean showRedDot() {
        return showRedDot;
    }

    @Override
    public void showRedDot(boolean show) {
        showRedDot = show;
    }

    @Override
    public int getPageCount() {
        return pageCount;
    }

    @Override
    public void setPageCount(int pageCount) {
        this.pageCount = pageCount;
    }

    public EmotionPackage setIconResId(int iconResId) {
        this.iconResId = iconResId;
        return this;
    }

    public EmotionPackage setStartPosition(int startPosition) {
        this.startPosition = startPosition;
        return this;
    }

    public EmotionPackage setPositionInTab(int positionInTab) {
        this.positionInTab = positionInTab;
        return this;
    }

    public IEmojiItem getItem(int itemId) {
        if (ToolUtil.isEmptyCollects(getEmojis())) {
            return null;
        }
        for (EmojiBean emoji : emojis) {
            if (emoji.id == itemId) {
                return emoji;
            }
        }
        return null;
    }

    public IEmojiItem getSubItem(int itemId) {
        if (ToolUtil.isEmptyCollects(getEmojis())) {
            return null;
        }
        for (EmojiBean emoji : emojis) {
            if (!ToolUtil.isEmptyCollects(emoji.getSubEmojis())) {
                for (EmojiBean subEmoji : emoji.getSubEmojis()) {
                    if (subEmoji.getEmotionId() == itemId) {
                        return subEmoji;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 某一种表情
     */
    public static class EmojiBean implements IEmojiItem {
        /**
         * bgImagePath : http://fdfs.test.ximalaya.com/group1/M00/5C/38/wKgDplzjpx-AUMHaABreLMmlVVw785.gif
         * childRank : 1
         * iconPath : http://fdfs.test.ximalaya.com/group1/M00/05/1D/wKgD3lzjmqCAZpXXAACPs4WrvuU429.png
         * id : 1110
         * parentId : 1107
         * subEmojis : [{"childRank":1,"id":1117,"parentId":1110,"type":8},{"bgImagePath":"http://fdfs.test.ximalaya.com / group1 / M00 / 05 / AB / wKgD3lzwzU6AaXQQAACPs4WrvuU971.png ","childRank":2,"iconPath":"http://fdfs.test.ximalaya.com / group1 / M00 / 5 C / C6 / wKgDplzw2euAUe8lAACPs4WrvuU949.png ","id":1116,"parentId":1110,"type":8}]
         * type : 8
         */

        /**
         * 表情动图
         */
        private String bgImagePath;

        /**
         * 接口携带信息，表情宽高信息，本地默认为正方形
         */
        private int bgImageWidth;
        private int bgImageHeight;

        /**
         * 顺序
         */
        private int childRank;
        /**
         * 表情静图
         */
        private String iconPath;
        /**
         * templateid
         */
        private int id;
        /**
         * 父templateid
         */
        private int parentId;
        /**
         * 表情类型
         */
        private int type;
        /**
         * 是否是付费表情
         */
        private boolean chargeFlag;
        /**
         * 是否是限时表情
         */
        private boolean limitTimeFlag;
        /**
         * 限时剩余时间
         */
        private String remainTimeDesc;

        /**
         * 某一种表情可能对应多个 gif，比如骰子
         */
        private List<EmojiBean> subEmojis;

        /**
         * 表情描述
         */
        private String emotionDescription;

        /**
         * 引导链接
         */
        private String referUrl;

        /**
         * 本地字段，布局需要的尺寸参数
         */
        private int width = 30, height = 30;

        /**
         * 表情名称
         */
        private String name;

        private boolean isRandom;

        /**
         * 麦上表情使用人群，1是全部，2是黄金守护
         */
        public int allowedUserType;

        public void setEmotionDescription(String emotionDescription) {
            this.emotionDescription = emotionDescription;
        }

        public void setReferUrl(String referUrl) {
            this.referUrl = referUrl;
        }

        @Override
        public String getEmotionDescription() {
            return emotionDescription;
        }

        @Override
        public String getReferUrl() {
            return referUrl;
        }


        public String getBgImagePath() { return bgImagePath;}

        public void setBgImagePath(String bgImagePath) { this.bgImagePath = bgImagePath;}

        public int getBgImageWidth() { return bgImageWidth; }

        public void setBgImageWidth(int bgImageWidth) {
            this.bgImageWidth = bgImageWidth;
        }

        public int getBgImageHeight() { return bgImageHeight; }

        public void setBgImageHeight(int bgImageHeight) {
            this.bgImageHeight = bgImageHeight;
        }

        @Override
        public float getBgImageAspectRatio() {
            return getBgImageWidth() == 0 ? 1 : (getBgImageHeight() / (getBgImageWidth() * 1.0F));
        }

        public int getChildRank() { return childRank;}

        public void setChildRank(int childRank) { this.childRank = childRank;}

        public String getIconPath() { return iconPath;}

        public void setIconPath(String iconPath) { this.iconPath = iconPath;}

        public int getId() { return id;}

        public void setId(int id) { this.id = id;}

        public int getParentId() { return parentId;}

        public void setParentId(int parentId) { this.parentId = parentId;}

        public int getType() { return type;}

        public void setType(int type) { this.type = type;}

        public List<EmojiBean> getSubEmojis() { return subEmojis;}

        public void setSubEmojis(List<EmojiBean> subEmojis) { this.subEmojis = subEmojis;}

        @Override
        public String getEmotionStaticPicUrl() {
            return iconPath;
        }

        @Override
        public String getEmotionGifUrl() {
            return bgImagePath;
        }

        @Override
        public int getEmotionId() {
            return id;
        }

        @Override
        public int getGroupId() {
            return parentId;
        }

        @Override
        public int getWidth() {
            return width;
        }

        @Override
        public int getHeight() {
            return height;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public boolean isRandomGif() {
            return isRandom;
        }

        @Override
        public void isRandomGift(boolean random) {
            isRandom = random;
        }

        public boolean isChargeFlag() {
            return chargeFlag;
        }

        public void setChargeFlag(boolean chargeFlag) {
            this.chargeFlag = chargeFlag;
        }

        public boolean isLimitTimeFlag() {
            return limitTimeFlag;
        }

        public void setLimitTimeFlag(boolean limitTimeFlag) {
            this.limitTimeFlag = limitTimeFlag;
        }

        public String getRemainTimeDesc() {
            return remainTimeDesc;
        }

        public void setRemainTimeDesc(String remainTimeDesc) {
            this.remainTimeDesc = remainTimeDesc;
        }

        public boolean isRandom() {
            return isRandom;
        }

        public void setRandom(boolean random) {
            isRandom = random;
        }


        public void setAllowedUserType(int allowedUserType) {
            this.allowedUserType = allowedUserType;
        }

        public int getAllowedUserType() {
            return allowedUserType;
        }

        @Override
        public IEmojiItem getRandomEmoji() {
            if (isRandomGif() && !ToolUtil.isEmptyCollects(getSubEmojis())) {

                int size = getSubEmojis().size();
                Random random = new Random();
                int i = random.nextInt(size);

                LiveHelper.Log.i("getRandomEmoji: " + i);
                if (i >= 0 && i < size) {
                    return getSubEmojis().get(i);
                } else {
                    return null;
                }
            }
            return this;
        }

        @Override
        public int showType() {
            return allowedUserType;
        }

        @Override
        public int getEmojiType() {
            return type;
        }

        public EmojiBean setSize(int size) {
            this.width = size;
            this.height = size;
            return this;
        }

        public EmojiBean setWidth(int width) {
            this.width = width;
            return this;
        }

        public EmojiBean setHeight(int height) {
            this.height = height;
            return this;
        }

        public EmojiBean setName(String name) {
            this.name = name;
            return this;
        }
    }

    @Nullable
    public static List<EmotionPackage> parseMyEmojisFromJson(String json) {
        if (TextUtils.isEmpty(json)) return null;

        try {
            JSONObject jsonObject = new JSONObject(json);

            if (jsonObject.optInt("ret") != 0 || !jsonObject.has("data")) {
                return null;
            }

            JSONObject data = jsonObject.optJSONObject("data");
            if (data == null) return null;

            // 红点信息
            List<Long> redPointIdSet = null;
            if (data.has("redpointSet")) {
                JSONArray redpointSet = data.optJSONArray("redpointSet");
                if (redpointSet != null) {
                    int length = redpointSet.length();
                    redPointIdSet = new ArrayList<>(length);
                    for (int i = 0; i < length; i++) {
                        long id = redpointSet.optLong(i);
                        saveRedPointIdToLocal(id, true);
                        redPointIdSet.add(id);
                    }
                }
            }

            List<EmotionPackage> emotionPackages;

            if (!data.has("emoticonList")) return null;
            JSONArray emoticonList = data.optJSONArray("emoticonList");
            if (emoticonList == null) return null;

            int length = emoticonList.length();
            if (length == 0) return null;

            emotionPackages = new ArrayList<>(length);
            EmotionPackage emotionPackage;
            List<EmotionPackage.EmojiBean> emojiBeans;

            for (int i = 0; i < length; i++) {
                JSONObject emotionJb = emoticonList.optJSONObject(i);
                emotionPackage = new EmotionPackage();
                if (emotionJb.has("groupId")) {
                    emotionPackage.setGroupId(emotionJb.optInt("groupId"));
                    emotionPackage.showRedDot(getRedPointIdFromLocal(emotionPackage.getGroupId()));
                }
                if (emotionJb.has("headCoverPath")) {
                    emotionPackage.setHeadCoverPath(emotionJb.optString("headCoverPath"));
                }

                if (!emotionJb.has("childList")) {
                    continue;
                }

                JSONArray childList = emotionJb.optJSONArray("childList");
                if (childList == null) {
                    emotionPackage.setEmojis(new ArrayList<>());
                    emotionPackages.add(emotionPackage);
                    continue;
                }

                emojiBeans = new ArrayList<>(childList.length());
                for (int j = 0; j < childList.length(); j++) {
                    EmotionPackage.EmojiBean emojiBean = new EmotionPackage.EmojiBean();
                    emojiBean.setParentId(emotionPackage.getGroupId());
                    JSONObject child = childList.optJSONObject(j);
                    if (child.has("bgImagePath")) {
                        emojiBean.setBgImagePath(child.optString("bgImagePath"));
                    }
                    if (child.has("bgImageWidth")) {
                        emojiBean.setBgImageWidth(child.optInt("bgImageWidth"));
                    }
                    if (child.has("bgImageHeight")) {
                        emojiBean.setBgImageHeight(child.optInt("bgImageHeight"));
                    }
                    if (child.has("iconPath")) {
                        emojiBean.setIconPath(child.optString("iconPath"));
                    }
                    if (child.has("templateId")) {
                        emojiBean.setId(child.optInt("templateId"));
                    }
                    if (child.has("name")) {
                        emojiBean.setName(child.optString("name"));
                    }
                    if (child.has("remainTimeDesc")) {
                        emojiBean.setRemainTimeDesc(child.optString("remainTimeDesc"));
                    }
                    if (child.has("chargeFlag")) {
                        emojiBean.setChargeFlag(child.optBoolean("chargeFlag"));
                    }
                    if (child.has("limitTimeFlag")) {
                        emojiBean.setLimitTimeFlag(child.optBoolean("limitTimeFlag"));
                    }
                    if (child.has("allowedUserType")) {
                        emojiBean.setAllowedUserType(child.optInt("allowedUserType"));
                    }
                    if (child.has("subEmoticons")) {
                        JSONArray subEmoticons = child.optJSONArray("subEmoticons");
                        if (subEmoticons == null) {
                            emojiBean.setSubEmojis(new ArrayList<>());
                            emojiBean.isRandomGift(false);
                        } else {
                            int subEmotionLength = subEmoticons.length();
                            List<EmotionPackage.EmojiBean> subEmojis = new ArrayList<>(
                                    subEmotionLength
                            );
                            for (int k = 0; k < subEmotionLength; k++) {
                                int id = subEmoticons.optInt(k);
                                EmotionPackage.EmojiBean subEmoji = new EmotionPackage.EmojiBean();
                                subEmoji.setId(id);
                                subEmoji.isRandomGift(true);
                                subEmoji.setAllowedUserType(emojiBean.getAllowedUserType());
                                subEmojis.add(subEmoji);
                            }
                            emojiBean.setSubEmojis(subEmojis);
                            // 有子表情就视为随机表情
                            emojiBean.isRandomGift(!ToolUtil.isEmptyCollects(subEmojis));
                        }
                    }
                    emojiBeans.add(emojiBean);
                }

                emotionPackage.setEmojis(emojiBeans);
                emotionPackages.add(emotionPackage);
            }

            return emotionPackages;
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }
    }
}
