package com.ximalaya.ting.android.live.common.input.model;

import androidx.annotation.NonNull;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 贵族弹幕余额相关信息
 *
 * <AUTHOR>
 */
public class NobleBulletInfo {
    /**
     * 是否有权限
     */
    public int checkRes;

    /**
     * 剩余弹幕数
     */
    public int remain;

    /**
     * 模板id
     */
    public int templateId;

    public static NobleBulletInfo parse(String json) {
        NobleBulletInfo bulletInfo = new NobleBulletInfo();
        try {
            JSONObject jsonObject = new JSONObject(json);
            int ret = jsonObject.optInt("ret");
            if (ret != 0) {
                return bulletInfo;
            }

            JSONObject data = jsonObject.optJSONObject("data");
            if (data == null) {
                return bulletInfo;
            }

            if (data.has("checkRes")) {
                bulletInfo.checkRes = data.optInt("checkRes");
            }
            if (data.has("remain")) {
                bulletInfo.remain = data.optInt("remain");
            }
            if (data.has("templateId")) {
                bulletInfo.templateId = data.optInt("templateId");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return bulletInfo;
    }

    @NonNull
    @Override
    public String toString() {
        return "NobleBulletInfo{" +
                "checkRes=" + checkRes +
                ", remain=" + remain +
                ", templateId=" + templateId +
                '}';
    }

    /**
     * 是否为开通状态
     *
     * @return true 开通 false 未开通
     */
    public boolean isOpen() {
        return checkRes == NobleBulletInfo.INoblePermission.HAVE_OPEN;
    }

    /**
     * 贵族权限
     */
    public interface INoblePermission {
        //无权限
        int NONE = 0;
        //有权限、已开通
        int HAVE_OPEN = 1;
        //有权限、已关闭
        int HAVE_CLOSE = 2;
        //权益不存在
        int NO_BENEFIT = 3;
    }
}
