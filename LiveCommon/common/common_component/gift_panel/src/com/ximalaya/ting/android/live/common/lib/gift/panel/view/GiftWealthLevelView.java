package com.ximalaya.ting.android.live.common.lib.gift.panel.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.live.common.R;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.BaseItem;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftWealthProgressModel;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.PackageInfo;
import com.ximalaya.ting.android.live.common.lib.icons.LiveIconsManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveMathUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.lib.utils.WealthIconCacheUtil;

import java.text.DecimalFormat;

/**
 * desc：礼物面板-财富等级特权-财富等级View
 * Created by xuxinkai on 2020-06-18
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17602190319
 */
public class GiftWealthLevelView extends RelativeLayout {

    private Context mContext;

    private ImageView mWealthLevelIv;
    private ProgressBar mWealthProgress;
    private TextView mWealthContentTv;
    private TextView mWealthCountTv;
    private TextView mWealthMaxTv;

    private GiftWealthProgressModel mGiftWealthModel;
    private boolean mShowGiftWealth;
    private BaseItem mSelectGift;
    private int mGiftNum;
    private DecimalFormat mDecimalFormat;
    //最低进度
    private final int MIN_PROGRESS = 1;

    public GiftWealthLevelView(Context context) {
        super(context);
        init(context);
    }

    public GiftWealthLevelView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public GiftWealthLevelView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mContext = context;
        mDecimalFormat = new DecimalFormat(",###");
        LayoutInflater.from(context).inflate(R.layout.live_layout_gift_wealth_level, this);
        mWealthLevelIv = findViewById(R.id.live_iv_gift_wealth_icon);
        mWealthProgress = findViewById(R.id.live_pb_gift_wealth_progress);
        mWealthContentTv = findViewById(R.id.live_tv_gift_wealth_content);
        mWealthCountTv = findViewById(R.id.live_tv_gift_wealth_count);
        mWealthMaxTv = findViewById(R.id.live_tv_gift_wealth_max_level);
    }

    public void setWealthInfo(GiftWealthProgressModel model, boolean update) {
        mGiftWealthModel = model;
        if (update) {
            //送礼完成之后的刷新
            setSelectGift(mSelectGift, mGiftNum);
        } else if (!mShowGiftWealth && mSelectGift != null) {
            //首次初始化
            setSelectGift(mSelectGift, mGiftNum);
        }
    }

    /**
     * 更新进度值
     */
    public void setSelectGift(BaseItem baseInfo, int giftNum) {
        mSelectGift = baseInfo;
        mGiftNum = giftNum;
        if (mGiftWealthModel == null) {
            return;
        }
        mShowGiftWealth = true;

        if (mGiftWealthModel.grade < 0) {
            mWealthLevelIv.setVisibility(View.GONE);
            return;
        }
        setWealthIcon();

        int maxProgress = mWealthProgress.getMax();
        if (mGiftWealthModel.isMaxLevel()) {
            //满级了
            UIStateUtil.showViews(mWealthMaxTv);
            mWealthContentTv.setText("超出 " + mDecimalFormat.format(mGiftWealthModel.overflow) + " 财富值");
            mWealthProgress.setProgress(maxProgress);
            mWealthProgress.setSecondaryProgress(0);

            if (baseInfo instanceof GiftInfoCombine.GiftInfo) {
                GiftInfoCombine.GiftInfo info = (GiftInfoCombine.GiftInfo) baseInfo;
                double finalValue = LiveMathUtil.mul(info.xiDiamondWorth, mGiftWealthModel.acceleration) * mGiftNum;
                mWealthCountTv.setText("+" + mDecimalFormat.format(new Double(finalValue).longValue()));
            } else if (baseInfo == null || baseInfo instanceof PackageInfo.Item) {
                //背包仅展示
                mWealthCountTv.setText("");
            }
        } else {
            UIStateUtil.hideViews(mWealthMaxTv);
            mWealthContentTv.setText("距离 " + (mGiftWealthModel.grade + 1) + " 级还差 " + mDecimalFormat.format(mGiftWealthModel.difference) + " 财富值");

            long threshold = mGiftWealthModel.threshold;
            long currentWealthValue = threshold - mGiftWealthModel.difference;
            int currentProgress = (int) (currentWealthValue * maxProgress / threshold);
            //设置最低进度
            currentProgress = currentProgress < MIN_PROGRESS ? MIN_PROGRESS : currentProgress;
            mWealthProgress.setProgress(currentProgress);

            if (baseInfo instanceof GiftInfoCombine.GiftInfo) {
                GiftInfoCombine.GiftInfo info = (GiftInfoCombine.GiftInfo) baseInfo;
                double finalValue = LiveMathUtil.mul(info.xiDiamondWorth, mGiftWealthModel.acceleration) * mGiftNum;
                long result = new Double(currentWealthValue + finalValue).longValue() * maxProgress / threshold;
                int giftProgress = result > maxProgress ? maxProgress : (int) result;
                if (giftProgress - currentProgress < MIN_PROGRESS) {
                    //设置最低进度
                    if (giftProgress == 0) {
                        giftProgress = MIN_PROGRESS + MIN_PROGRESS;
                    } else {
                        giftProgress = giftProgress + MIN_PROGRESS;
                    }
                }
                mWealthProgress.setSecondaryProgress(giftProgress);
                mWealthCountTv.setText("+" + mDecimalFormat.format(new Double(finalValue).longValue()));
            } else if (baseInfo == null || baseInfo instanceof PackageInfo.Item) {
                //背包仅展示
                mWealthProgress.setSecondaryProgress(currentProgress);
                mWealthCountTv.setText("");

            }
        }
    }

    private void setWealthIcon() {
        final ViewGroup.LayoutParams layoutParams = mWealthLevelIv.getLayoutParams();
        final int rawHeight = layoutParams.height;

        final String iconPath = LiveIconsManager.getInstance().getWealthIconPathByGrade(mGiftWealthModel.grade);
        if (!TextUtils.isEmpty(iconPath)) {
            Bitmap bitmap = WealthIconCacheUtil.getCache(iconPath);
            if (bitmap != null && bitmap.getWidth() > 0) {
                UIStateUtil.showViews(mWealthLevelIv);
                layoutParams.width = (int) (rawHeight * bitmap.getWidth() * 1.0f / bitmap.getHeight());
                mWealthLevelIv.setImageBitmap(bitmap);
            } else {
                // 财富等级图标不能裁剪，裁剪会导致变形 http://imagev2.xmcdn.com/group76/M09/01/9A/wKgO3l54v8DSiwU1AAAEC_u--lk678.png!op_type=3&rows=54&columns=126&strip=1&magick=webp
                ImageManager.from(mContext).displayImage(mWealthLevelIv, iconPath, R.drawable.livecomm_img_tag_wealth_default, 0, 0, new ImageManager.DisplayCallback() {
                    @Override
                    public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                        if (bitmap == null) {
                            return;
                        }
                        if (bitmap.getWidth() > 0 && bitmap.getHeight() > 0) {
                            layoutParams.width = (int) (rawHeight * bitmap.getWidth() * 1.0f / bitmap.getHeight());
                            WealthIconCacheUtil.putCache(iconPath, bitmap);
                        } else {
                            layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT;
                        }
                        mWealthLevelIv.setLayoutParams(layoutParams);
                    }
                });
            }
        }
    }
}
