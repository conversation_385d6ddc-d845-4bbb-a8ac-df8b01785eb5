package com.ximalaya.ting.android.live.lib.redenvelope.view;


import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatTimedRedPacketMessage;
import com.ximalaya.ting.android.live.lib.redenvelope.model.AlbumPacketInfo;
import com.ximalaya.ting.android.live.lib.redenvelope.model.ExtraRedPacketData;
import com.ximalaya.ting.android.live.lib.redenvelope.model.IRedPacketMessage;
import com.ximalaya.ting.android.live.lib.redenvelope.model.RedPackModel;

/**
 * 红包数据类.
 *
 * <AUTHOR>
 */
public class TimedRedPacketMessageImpl extends CommonChatTimedRedPacketMessage implements IRedPacketMessage {

    boolean isRedPacketDeleted;

    private ExtraRedPacketData extraRedPacketData;

    private AlbumPacketInfo albumPacketInfo;

    private RedPackModel redPackModel;

    private boolean isSendGiftClick;

    private String xmRequestId = "";

    @Override
    public long getRedPacketId() {
        return mRedPacketId;
    }

    @Override
    public int getRedPacketType() {
        return mRedPacketType;
    }

    @Override
    public boolean isRedPacketFollowType() {
        return followTypes.contains(mRedPacketType);
    }

    @Override
    public boolean isSuperRedPacket() {
        return mIsSuperRedPacket;
    }

    @Override
    public void setSuperRedPacket(boolean superRedPacket) {
        this.mIsSuperRedPacket = superRedPacket;
    }

    @Override
    public boolean isRedPacketUserFollowType() {
        return mRedPacketType == RED_PACKET_TYPE_ATTENTION ||
                mRedPacketType == RED_PACKET_TYPE_PGC_FOLLOW_USER;
    }

    @Override
    public boolean isRedPacketSubscribeAlbumType() {
        return mRedPacketType == RED_PACKET_SUBSCRIBE_ALBUM;
    }

    @Override
    public boolean isRedPacketRoomFollowType() {
        return mRedPacketType == RED_PACKET_TYPE_PGC_FOLLOW_ROOM;
    }

    @Override
    public int getAmount() {
        return mAmount;
    }

    @Override
    public long getCountdownTime() {
        return mCountdownTime;
    }

    @Override
    public long geTotalTime() {
        return mTotalTime;
    }

    @Override
    public IRedPacketMessage setCountdownTime(long countdownTime) {
        this.mCountdownTime = countdownTime;
        return this;
    }

    @Nullable
    @Override
    public String getNickName() {
        if (mUserInfo != null && !TextUtils.isEmpty(mUserInfo.mNickname)) {
            return mUserInfo.mNickname;
        }
        return "";
    }

    @Override
    public long getChatUserUid() {
        if (mUserInfo != null) {
            return mUserInfo.mUid;
        }
        return 0;
    }

    @Override
    public String getHostName() {
        return hostName;
    }

    @Override
    public long getHostUid() {
        return hostUid;
    }

    @Override
    public String getPacketToken() {
        return mPacketToken;
    }

    @Override
    public boolean getPacketTokenStatus() {
        return mPacketTokenStatus;
    }

    @Override
    public void setPacketTokenStatus(boolean status) {
        this.mPacketTokenStatus = status;
    }

    @Override
    public boolean getBanStatus() {
        return isBan;
    }

    @Override
    public void setBanStatus(boolean ban) {
        this.isBan = ban;
    }

    @Override
    public String getBanHintMsg() {
        return banHintMsg;
    }

    @Override
    public void setBanHintMsg(String banHintMsg) {
        this.banHintMsg = banHintMsg;
    }

    @Override
    public void setIsFollowTarget(boolean isFollowTarget) {
        this.isFollowTarget = isFollowTarget;
    }

    @Override
    public boolean isFollowTarget() {
        return isFollowTarget;
    }

    @Override
    public void setIsFollowRoom(boolean followRoom) {
        this.isFollowRoom = followRoom;
    }

    @Override
    public boolean isFollowRoom() {
        return isFollowRoom;
    }

    @Override
    public void setTemplateId(long templateId) {
        this.mTemplateId = templateId;
    }

    @Override
    public void setRedPacketDeleted(boolean redPacketDeleted) {
        isRedPacketDeleted = redPacketDeleted;
    }

    @Override
    public boolean isRedPacketDeleted() {
        return isRedPacketDeleted;
    }

    @Override
    public long getTemplateId() {
        return mTemplateId;
    }

    @Override
    public long getOpenTime() {
        return mStartTime + mTotalTime;
    }

    @Override
    public int compareTo(IRedPacketMessage o) {
        if (null == o) {
            return 1;
        }

        return Long.compare(this.getOpenTime(), o.getOpenTime());
    }

    @Override
    public ExtraRedPacketData getExtraRedPacketData() {
        return extraRedPacketData;
    }

    @Override
    public void setExtraRedPacketData(ExtraRedPacketData extraRedPacketData) {
        this.extraRedPacketData = extraRedPacketData;
    }

    @Override
    @Nullable
    public AlbumPacketInfo getAlbumPacketInfo() {
        return albumPacketInfo;
    }

    @Override
    public void setAlbumPacketInfo(AlbumPacketInfo albumPacketInfo) {
        this.albumPacketInfo = albumPacketInfo;
    }

    @Override
    public void setRedPacketResultMode(RedPackModel resultMode) {
        this.redPackModel = resultMode;
    }

    @Override
    public RedPackModel getRedPacketMode() {
        return redPackModel;
    }

    @Override
    public void setSendGiftClick(boolean sendGiftClick) {
        isSendGiftClick = sendGiftClick;
    }

    @Override
    public boolean isSendGiftClick() {
        return isSendGiftClick;
    }

    @Override
    public void setXmRequestId(String xmRequestId) {
        this.xmRequestId = xmRequestId;
    }

    @Override
    public String getXmRequestId() {
        return xmRequestId;
    }
}
