<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/livecomm_bg_bottom_menu_dialog">

    <TextView
        android:id="@+id/live_title_tv"
        android:layout_width="fill_parent"
        android:layout_height="50dp"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:text="@string/live_select_action"
        android:textColor="@color/live_orange_f86442"
        android:textSize="15sp"
        android:visibility="gone" />

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/live_color_e8e8e8_2a2a2a"
        android:visibility="gone"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ListView
            android:id="@+id/live_listview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:cacheColorHint="@color/live_transparent_00000000"
            android:background="@color/live_transparent"
            android:listSelector="@color/live_transparent"
            android:divider="@null" />

        <ProgressBar
            android:id="@+id/live_selection_progress"
            style="@android:style/Widget.Holo.Light.ProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:visibility="gone" />
    </RelativeLayout>

    <View
        android:layout_width="fill_parent"
        android:layout_height="1px"
        android:background="@color/live_color_e8e8e8_2a2a2a" />

    <TextView
        android:id="@+id/live_close_btn"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:text="@string/live_cancel"
        android:textColor="@color/live_color_333333_cfcfcf"
        android:textSize="16sp" />
</LinearLayout>
