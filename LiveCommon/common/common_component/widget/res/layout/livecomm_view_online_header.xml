<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    tools:background="@color/live_color_AB55E7"
    tools:parentTag="android.widget.LinearLayout">

    <LinearLayout
        android:id="@+id/live_ll_online_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal" />

    <TextView
        android:id="@+id/live_tv_online_count"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="3dp"
        android:background="@drawable/livecomm_bg_online_count_round"
        android:gravity="center"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:textColor="@color/live_color_white_80"
        android:textSize="11sp"
        android:textStyle="bold"
        tools:text="100" />

</merge>