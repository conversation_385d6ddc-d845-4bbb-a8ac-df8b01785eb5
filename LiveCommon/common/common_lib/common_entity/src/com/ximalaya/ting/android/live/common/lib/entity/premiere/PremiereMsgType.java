package com.ximalaya.ting.android.live.common.lib.entity.premiere;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * Created by zhixin.he on 2023/5/26.
 *
 * @desc 倒计时type
 * @email <EMAIL>
 * @phone 15026804470
 */

@IntDef
@Retention(RetentionPolicy.SOURCE)
public @interface PremiereMsgType {
    /*
     * 倒计时type
     * */
    //1表示带文本消息
    int TYPE_HAS_TEXT = 1;
    //2表示不带文本消息
    int TYPE_NO_TEXT = 2;
}
