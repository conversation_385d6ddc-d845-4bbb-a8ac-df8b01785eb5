package com.ximalaya.ting.android.live.lib.stream.util;

import android.text.TextUtils;

import com.ximalaya.ting.android.live.common.lib.utils.Base64;

import java.nio.charset.Charset;

/**
 * XmAvKeyUtil
 *
 * <AUTHOR> (zhangshixin)
 * @email <EMAIL>
 * @phoneNumber 18789440700
 * @since 2020/5/18.
 */
public class XmAvKeyUtil {

    public static Charset sConvertAppKeyUseCharset = Charset.forName("ISO-8859-1");

    /**
     * decryptSignKey
     */
    public static byte[] decryptSignKey(String encryptedSignKey) {
        if (TextUtils.isEmpty(encryptedSignKey)) {
            return null;
        }
        byte[] key = Base64.decode(encryptedSignKey);
        if (key == null || key.length < 4) {
            return null;
        }

        swapArray(key, 1, key.length - 2);
        swapArray(key, 3, key.length - 4);

        return key;
    }

    /**
     * encryptSignKey
     */
    public static String encryptSignKey(byte[] encryptedSignKey) {
        if (encryptedSignKey == null || encryptedSignKey.length < 4) {
            return null;
        }

        swapArray(encryptedSignKey, 1, encryptedSignKey.length - 2);
        swapArray(encryptedSignKey, 3, encryptedSignKey.length - 4);

        return Base64.encode(encryptedSignKey);
    }

    private static void swapArray(byte[] key, int positionA, int positionB) {
        byte b1 = key[positionA];
        key[positionA] = key[positionB];
        key[positionB] = b1;
    }
}
