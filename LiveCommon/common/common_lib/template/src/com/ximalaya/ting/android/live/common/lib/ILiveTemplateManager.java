package com.ximalaya.ting.android.live.common.lib;

import com.ximalaya.ting.android.live.common.lib.model.LiveTemplateModel;

/**
 * 直播模版管理器接口。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2020/6/10
 */
interface ILiveTemplateManager extends ITemplateManager {

    /**
     * 根据模板 id 查询模板信息
     *
     * @param id 模版id
     * @return 模版信息
     */
    LiveTemplateModel.TemplateDetail getTemplateById(String id);

}
