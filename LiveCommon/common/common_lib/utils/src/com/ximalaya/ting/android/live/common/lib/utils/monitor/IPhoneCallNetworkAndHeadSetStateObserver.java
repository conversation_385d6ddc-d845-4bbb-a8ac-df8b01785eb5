package com.ximalaya.ting.android.live.common.lib.utils.monitor;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;


/**
 * 电话状态监听，包括电话监听、耳机监听、网络监听。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2020/3/17
 */
public interface IPhoneCallNetworkAndHeadSetStateObserver extends LifecycleObserver {

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    void registerReceiver();

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    void unregisterReceiver();

}
