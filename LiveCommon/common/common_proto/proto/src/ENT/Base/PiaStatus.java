// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/nali/work2024/XmMainApp2024/LiveBundle/LiveCommon/common/common_proto/pb/ENT.Base.proto at 62:1
package ENT.Base;

import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.WireEnum;
import java.lang.Override;

public enum PiaStatus implements WireEnum {
  PIA_STATUS_UN_CHOOSE(0),

  PIA_STATUS_UN_STARTED(1),

  PIA_STATUS_STARTED(2),

  PIA_STATUS_END(3);

  public static final ProtoAdapter<PiaStatus> ADAPTER = ProtoAdapter.newEnumAdapter(PiaStatus.class);

  private final int value;

  PiaStatus(int value) {
    this.value = value;
  }

  /**
   * Return the constant for {@code value} or null.
   */
  public static PiaStatus fromValue(int value) {
    switch (value) {
      case 0: return PIA_STATUS_UN_CHOOSE;
      case 1: return PIA_STATUS_UN_STARTED;
      case 2: return PIA_STATUS_STARTED;
      case 3: return PIA_STATUS_END;
      default: return null;
    }
  }

  @Override
  public int getValue() {
    return value;
  }
}
