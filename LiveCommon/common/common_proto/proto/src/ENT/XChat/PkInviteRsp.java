// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/lewis/project/live/LiveBundle/LiveCommon/common/common_proto/pb/ENT.XChat.proto at 589:1
package ENT.XChat;

import com.squareup.wire.FieldEncoding;
import com.squareup.wire.Message;
import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.ProtoReader;
import com.squareup.wire.ProtoWriter;
import com.squareup.wire.WireField;
import com.squareup.wire.internal.Internal;
import java.io.IOException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import okio.ByteString;

public final class PkInviteRsp extends Message<PkInviteRsp, PkInviteRsp.Builder> {
  public static final ProtoAdapter<PkInviteRsp> ADAPTER = new ProtoAdapter_PkInviteRsp();

  private static final long serialVersionUID = 0L;

  public static final Integer DEFAULT_RESULTCODE = 0;

  public static final String DEFAULT_REASON = "";

  public static final Long DEFAULT_UNIQUEID = 0L;

  /**
   * 邀请响应，成功仅代表服务端接收请求；
   */
  @WireField(
      tag = 1,
      adapter = "com.squareup.wire.ProtoAdapter#INT32",
      label = WireField.Label.REQUIRED
  )
  public final Integer resultCode;

  /**
   * 结果描述
   */
  @WireField(
      tag = 2,
      adapter = "com.squareup.wire.ProtoAdapter#STRING"
  )
  public final String reason;

  /**
   * 请求标识ID
   */
  @WireField(
      tag = 3,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64"
  )
  public final Long uniqueId;

  public PkInviteRsp(Integer resultCode, String reason, Long uniqueId) {
    this(resultCode, reason, uniqueId, ByteString.EMPTY);
  }

  public PkInviteRsp(Integer resultCode, String reason, Long uniqueId, ByteString unknownFields) {
    super(ADAPTER, unknownFields);
    this.resultCode = resultCode;
    this.reason = reason;
    this.uniqueId = uniqueId;
  }

  @Override
  public Builder newBuilder() {
    Builder builder = new Builder();
    builder.resultCode = resultCode;
    builder.reason = reason;
    builder.uniqueId = uniqueId;
    builder.addUnknownFields(unknownFields());
    return builder;
  }

  @Override
  public boolean equals(Object other) {
    if (other == this) return true;
    if (!(other instanceof PkInviteRsp)) return false;
    PkInviteRsp o = (PkInviteRsp) other;
    return unknownFields().equals(o.unknownFields())
        && resultCode.equals(o.resultCode)
        && Internal.equals(reason, o.reason)
        && Internal.equals(uniqueId, o.uniqueId);
  }

  @Override
  public int hashCode() {
    int result = super.hashCode;
    if (result == 0) {
      result = unknownFields().hashCode();
      result = result * 37 + resultCode.hashCode();
      result = result * 37 + (reason != null ? reason.hashCode() : 0);
      result = result * 37 + (uniqueId != null ? uniqueId.hashCode() : 0);
      super.hashCode = result;
    }
    return result;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append(", resultCode=").append(resultCode);
    if (reason != null) builder.append(", reason=").append(reason);
    if (uniqueId != null) builder.append(", uniqueId=").append(uniqueId);
    return builder.replace(0, 2, "PkInviteRsp{").append('}').toString();
  }

  public static final class Builder extends Message.Builder<PkInviteRsp, Builder> {
    public Integer resultCode;

    public String reason;

    public Long uniqueId;

    public Builder() {
    }

    /**
     * 邀请响应，成功仅代表服务端接收请求；
     */
    public Builder resultCode(Integer resultCode) {
      this.resultCode = resultCode;
      return this;
    }

    /**
     * 结果描述
     */
    public Builder reason(String reason) {
      this.reason = reason;
      return this;
    }

    /**
     * 请求标识ID
     */
    public Builder uniqueId(Long uniqueId) {
      this.uniqueId = uniqueId;
      return this;
    }

    @Override
    public PkInviteRsp build() {
      if (resultCode == null) {
        throw Internal.missingRequiredFields(resultCode, "resultCode");
      }
      return new PkInviteRsp(resultCode, reason, uniqueId, super.buildUnknownFields());
    }
  }

  private static final class ProtoAdapter_PkInviteRsp extends ProtoAdapter<PkInviteRsp> {
    ProtoAdapter_PkInviteRsp() {
      super(FieldEncoding.LENGTH_DELIMITED, PkInviteRsp.class);
    }

    @Override
    public int encodedSize(PkInviteRsp value) {
      return ProtoAdapter.INT32.encodedSizeWithTag(1, value.resultCode)
          + (value.reason != null ? ProtoAdapter.STRING.encodedSizeWithTag(2, value.reason) : 0)
          + (value.uniqueId != null ? ProtoAdapter.UINT64.encodedSizeWithTag(3, value.uniqueId) : 0)
          + value.unknownFields().size();
    }

    @Override
    public void encode(ProtoWriter writer, PkInviteRsp value) throws IOException {
      ProtoAdapter.INT32.encodeWithTag(writer, 1, value.resultCode);
      if (value.reason != null) ProtoAdapter.STRING.encodeWithTag(writer, 2, value.reason);
      if (value.uniqueId != null) ProtoAdapter.UINT64.encodeWithTag(writer, 3, value.uniqueId);
      writer.writeBytes(value.unknownFields());
    }

    @Override
    public PkInviteRsp decode(ProtoReader reader) throws IOException {
      Builder builder = new Builder();
      long token = reader.beginMessage();
      for (int tag; (tag = reader.nextTag()) != -1;) {
        switch (tag) {
          case 1: builder.resultCode(ProtoAdapter.INT32.decode(reader)); break;
          case 2: builder.reason(ProtoAdapter.STRING.decode(reader)); break;
          case 3: builder.uniqueId(ProtoAdapter.UINT64.decode(reader)); break;
          default: {
            FieldEncoding fieldEncoding = reader.peekFieldEncoding();
            Object value = fieldEncoding.rawProtoAdapter().decode(reader);
            builder.addUnknownField(tag, fieldEncoding, value);
          }
        }
      }
      reader.endMessage(token);
      return builder.build();
    }

    @Override
    public PkInviteRsp redact(PkInviteRsp value) {
      Builder builder = value.newBuilder();
      builder.clearUnknownFields();
      return builder.build();
    }
  }
}
