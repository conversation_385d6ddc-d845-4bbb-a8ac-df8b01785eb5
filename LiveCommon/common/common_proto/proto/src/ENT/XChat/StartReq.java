// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/lewis/project/live/LiveBundle/LiveCommon/common/common_proto/pb/ENT.XChat.proto at 212:1
package ENT.XChat;

import com.squareup.wire.FieldEncoding;
import com.squareup.wire.Message;
import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.ProtoReader;
import com.squareup.wire.ProtoWriter;
import com.squareup.wire.WireField;
import com.squareup.wire.internal.Internal;
import java.io.IOException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import okio.ByteString;

public final class StartReq extends Message<StartReq, StartReq.Builder> {
  public static final ProtoAdapter<StartReq> ADAPTER = new ProtoAdapter_StartReq();

  private static final long serialVersionUID = 0L;

  public static final Integer DEFAULT_ENTMODE = 0;

  public static final Long DEFAULT_UNIQUEID = 0L;

  @WireField(
      tag = 1,
      adapter = "com.squareup.wire.ProtoAdapter#INT32",
      label = WireField.Label.REQUIRED
  )
  public final Integer entMode;

  @WireField(
      tag = 2,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64"
  )
  public final Long uniqueId;

  public StartReq(Integer entMode, Long uniqueId) {
    this(entMode, uniqueId, ByteString.EMPTY);
  }

  public StartReq(Integer entMode, Long uniqueId, ByteString unknownFields) {
    super(ADAPTER, unknownFields);
    this.entMode = entMode;
    this.uniqueId = uniqueId;
  }

  @Override
  public Builder newBuilder() {
    Builder builder = new Builder();
    builder.entMode = entMode;
    builder.uniqueId = uniqueId;
    builder.addUnknownFields(unknownFields());
    return builder;
  }

  @Override
  public boolean equals(Object other) {
    if (other == this) return true;
    if (!(other instanceof StartReq)) return false;
    StartReq o = (StartReq) other;
    return unknownFields().equals(o.unknownFields())
        && entMode.equals(o.entMode)
        && Internal.equals(uniqueId, o.uniqueId);
  }

  @Override
  public int hashCode() {
    int result = super.hashCode;
    if (result == 0) {
      result = unknownFields().hashCode();
      result = result * 37 + entMode.hashCode();
      result = result * 37 + (uniqueId != null ? uniqueId.hashCode() : 0);
      super.hashCode = result;
    }
    return result;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append(", entMode=").append(entMode);
    if (uniqueId != null) builder.append(", uniqueId=").append(uniqueId);
    return builder.replace(0, 2, "StartReq{").append('}').toString();
  }

  public static final class Builder extends Message.Builder<StartReq, Builder> {
    public Integer entMode;

    public Long uniqueId;

    public Builder() {
    }

    public Builder entMode(Integer entMode) {
      this.entMode = entMode;
      return this;
    }

    public Builder uniqueId(Long uniqueId) {
      this.uniqueId = uniqueId;
      return this;
    }

    @Override
    public StartReq build() {
      if (entMode == null) {
        throw Internal.missingRequiredFields(entMode, "entMode");
      }
      return new StartReq(entMode, uniqueId, super.buildUnknownFields());
    }
  }

  private static final class ProtoAdapter_StartReq extends ProtoAdapter<StartReq> {
    ProtoAdapter_StartReq() {
      super(FieldEncoding.LENGTH_DELIMITED, StartReq.class);
    }

    @Override
    public int encodedSize(StartReq value) {
      return ProtoAdapter.INT32.encodedSizeWithTag(1, value.entMode)
          + (value.uniqueId != null ? ProtoAdapter.UINT64.encodedSizeWithTag(2, value.uniqueId) : 0)
          + value.unknownFields().size();
    }

    @Override
    public void encode(ProtoWriter writer, StartReq value) throws IOException {
      ProtoAdapter.INT32.encodeWithTag(writer, 1, value.entMode);
      if (value.uniqueId != null) ProtoAdapter.UINT64.encodeWithTag(writer, 2, value.uniqueId);
      writer.writeBytes(value.unknownFields());
    }

    @Override
    public StartReq decode(ProtoReader reader) throws IOException {
      Builder builder = new Builder();
      long token = reader.beginMessage();
      for (int tag; (tag = reader.nextTag()) != -1;) {
        switch (tag) {
          case 1: builder.entMode(ProtoAdapter.INT32.decode(reader)); break;
          case 2: builder.uniqueId(ProtoAdapter.UINT64.decode(reader)); break;
          default: {
            FieldEncoding fieldEncoding = reader.peekFieldEncoding();
            Object value = fieldEncoding.rawProtoAdapter().decode(reader);
            builder.addUnknownField(tag, fieldEncoding, value);
          }
        }
      }
      reader.endMessage(token);
      return builder.build();
    }

    @Override
    public StartReq redact(StartReq value) {
      Builder builder = value.newBuilder();
      builder.clearUnknownFields();
      return builder.build();
    }
  }
}
