// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/zoey/Work/XimalayaApp/LiveBundle/LiveCommon/common/common_proto/pb/LOVE.XChat.proto at 429:1
package LOVE.XChat;

import LOVE.Base.VersionInfo;
import com.squareup.wire.FieldEncoding;
import com.squareup.wire.Message;
import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.ProtoReader;
import com.squareup.wire.ProtoWriter;
import com.squareup.wire.WireField;
import com.squareup.wire.internal.Internal;
import java.io.IOException;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import okio.ByteString;

public final class ExtendPkTimeReq extends Message<ExtendPkTimeReq, ExtendPkTimeReq.Builder> {
  public static final ProtoAdapter<ExtendPkTimeReq> ADAPTER = new ProtoAdapter_ExtendPkTimeReq();

  private static final long serialVersionUID = 0L;

  public static final VersionInfo DEFAULT_VERSIONINFO = VersionInfo.VERSION_01;

  public static final Long DEFAULT_ROOMID = 0L;

  public static final Long DEFAULT_USERID = 0L;

  public static final Long DEFAULT_UNIQUEID = 0L;

  @WireField(
      tag = 1,
      adapter = "LOVE.Base.VersionInfo#ADAPTER"
  )
  public final VersionInfo versionInfo;

  @WireField(
      tag = 2,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64",
      label = WireField.Label.REQUIRED
  )
  public final Long roomId;

  @WireField(
      tag = 3,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64",
      label = WireField.Label.REQUIRED
  )
  public final Long userId;

  @WireField(
      tag = 4,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64"
  )
  public final Long uniqueId;

  public ExtendPkTimeReq(VersionInfo versionInfo, Long roomId, Long userId, Long uniqueId) {
    this(versionInfo, roomId, userId, uniqueId, ByteString.EMPTY);
  }

  public ExtendPkTimeReq(VersionInfo versionInfo, Long roomId, Long userId, Long uniqueId, ByteString unknownFields) {
    super(ADAPTER, unknownFields);
    this.versionInfo = versionInfo;
    this.roomId = roomId;
    this.userId = userId;
    this.uniqueId = uniqueId;
  }

  @Override
  public Builder newBuilder() {
    Builder builder = new Builder();
    builder.versionInfo = versionInfo;
    builder.roomId = roomId;
    builder.userId = userId;
    builder.uniqueId = uniqueId;
    builder.addUnknownFields(unknownFields());
    return builder;
  }

  @Override
  public boolean equals(Object other) {
    if (other == this) return true;
    if (!(other instanceof ExtendPkTimeReq)) return false;
    ExtendPkTimeReq o = (ExtendPkTimeReq) other;
    return unknownFields().equals(o.unknownFields())
        && Internal.equals(versionInfo, o.versionInfo)
        && roomId.equals(o.roomId)
        && userId.equals(o.userId)
        && Internal.equals(uniqueId, o.uniqueId);
  }

  @Override
  public int hashCode() {
    int result = super.hashCode;
    if (result == 0) {
      result = unknownFields().hashCode();
      result = result * 37 + (versionInfo != null ? versionInfo.hashCode() : 0);
      result = result * 37 + roomId.hashCode();
      result = result * 37 + userId.hashCode();
      result = result * 37 + (uniqueId != null ? uniqueId.hashCode() : 0);
      super.hashCode = result;
    }
    return result;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    if (versionInfo != null) builder.append(", versionInfo=").append(versionInfo);
    builder.append(", roomId=").append(roomId);
    builder.append(", userId=").append(userId);
    if (uniqueId != null) builder.append(", uniqueId=").append(uniqueId);
    return builder.replace(0, 2, "ExtendPkTimeReq{").append('}').toString();
  }

  public static final class Builder extends Message.Builder<ExtendPkTimeReq, Builder> {
    public VersionInfo versionInfo;

    public Long roomId;

    public Long userId;

    public Long uniqueId;

    public Builder() {
    }

    public Builder versionInfo(VersionInfo versionInfo) {
      this.versionInfo = versionInfo;
      return this;
    }

    public Builder roomId(Long roomId) {
      this.roomId = roomId;
      return this;
    }

    public Builder userId(Long userId) {
      this.userId = userId;
      return this;
    }

    public Builder uniqueId(Long uniqueId) {
      this.uniqueId = uniqueId;
      return this;
    }

    @Override
    public ExtendPkTimeReq build() {
      if (roomId == null
          || userId == null) {
        throw Internal.missingRequiredFields(roomId, "roomId",
            userId, "userId");
      }
      return new ExtendPkTimeReq(versionInfo, roomId, userId, uniqueId, super.buildUnknownFields());
    }
  }

  private static final class ProtoAdapter_ExtendPkTimeReq extends ProtoAdapter<ExtendPkTimeReq> {
    ProtoAdapter_ExtendPkTimeReq() {
      super(FieldEncoding.LENGTH_DELIMITED, ExtendPkTimeReq.class);
    }

    @Override
    public int encodedSize(ExtendPkTimeReq value) {
      return (value.versionInfo != null ? VersionInfo.ADAPTER.encodedSizeWithTag(1, value.versionInfo) : 0)
          + ProtoAdapter.UINT64.encodedSizeWithTag(2, value.roomId)
          + ProtoAdapter.UINT64.encodedSizeWithTag(3, value.userId)
          + (value.uniqueId != null ? ProtoAdapter.UINT64.encodedSizeWithTag(4, value.uniqueId) : 0)
          + value.unknownFields().size();
    }

    @Override
    public void encode(ProtoWriter writer, ExtendPkTimeReq value) throws IOException {
      if (value.versionInfo != null) VersionInfo.ADAPTER.encodeWithTag(writer, 1, value.versionInfo);
      ProtoAdapter.UINT64.encodeWithTag(writer, 2, value.roomId);
      ProtoAdapter.UINT64.encodeWithTag(writer, 3, value.userId);
      if (value.uniqueId != null) ProtoAdapter.UINT64.encodeWithTag(writer, 4, value.uniqueId);
      writer.writeBytes(value.unknownFields());
    }

    @Override
    public ExtendPkTimeReq decode(ProtoReader reader) throws IOException {
      Builder builder = new Builder();
      long token = reader.beginMessage();
      for (int tag; (tag = reader.nextTag()) != -1;) {
        switch (tag) {
          case 1: {
            try {
              builder.versionInfo(VersionInfo.ADAPTER.decode(reader));
            } catch (ProtoAdapter.EnumConstantNotFoundException e) {
              builder.addUnknownField(tag, FieldEncoding.VARINT, (long) e.value);
            }
            break;
          }
          case 2: builder.roomId(ProtoAdapter.UINT64.decode(reader)); break;
          case 3: builder.userId(ProtoAdapter.UINT64.decode(reader)); break;
          case 4: builder.uniqueId(ProtoAdapter.UINT64.decode(reader)); break;
          default: {
            FieldEncoding fieldEncoding = reader.peekFieldEncoding();
            Object value = fieldEncoding.rawProtoAdapter().decode(reader);
            builder.addUnknownField(tag, fieldEncoding, value);
          }
        }
      }
      reader.endMessage(token);
      return builder.build();
    }

    @Override
    public ExtendPkTimeReq redact(ExtendPkTimeReq value) {
      Builder builder = value.newBuilder();
      builder.clearUnknownFields();
      return builder.build();
    }
  }
}
