// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/zoey/Work/XimalayaApp/LiveBundle/LiveCommon/common/common_proto/pb/LOVE.XChat.proto at 403:1
package LOVE.XChat;

import LOVE.Base.VersionInfo;
import com.squareup.wire.FieldEncoding;
import com.squareup.wire.Message;
import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.ProtoReader;
import com.squareup.wire.ProtoWriter;
import com.squareup.wire.WireField;
import com.squareup.wire.internal.Internal;
import java.io.IOException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import okio.ByteString;

public final class StartPkRsp extends Message<StartPkRsp, StartPkRsp.Builder> {
  public static final ProtoAdapter<StartPkRsp> ADAPTER = new ProtoAdapter_StartPkRsp();

  private static final long serialVersionUID = 0L;

  public static final VersionInfo DEFAULT_VERSIONINFO = VersionInfo.VERSION_01;

  public static final Integer DEFAULT_RESULTCODE = 0;

  public static final Long DEFAULT_UNIQUEID = 0L;

  public static final Long DEFAULT_TIMESTAMP = 0L;

  public static final String DEFAULT_REASON = "";

  @WireField(
      tag = 1,
      adapter = "LOVE.Base.VersionInfo#ADAPTER"
  )
  public final VersionInfo versionInfo;

  @WireField(
      tag = 2,
      adapter = "com.squareup.wire.ProtoAdapter#INT32",
      label = WireField.Label.REQUIRED
  )
  public final Integer resultCode;

  @WireField(
      tag = 3,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64"
  )
  public final Long uniqueId;

  @WireField(
      tag = 4,
      adapter = "com.squareup.wire.ProtoAdapter#UINT64"
  )
  public final Long timeStamp;

  @WireField(
      tag = 5,
      adapter = "com.squareup.wire.ProtoAdapter#STRING"
  )
  public final String reason;

  public StartPkRsp(VersionInfo versionInfo, Integer resultCode, Long uniqueId, Long timeStamp, String reason) {
    this(versionInfo, resultCode, uniqueId, timeStamp, reason, ByteString.EMPTY);
  }

  public StartPkRsp(VersionInfo versionInfo, Integer resultCode, Long uniqueId, Long timeStamp, String reason, ByteString unknownFields) {
    super(ADAPTER, unknownFields);
    this.versionInfo = versionInfo;
    this.resultCode = resultCode;
    this.uniqueId = uniqueId;
    this.timeStamp = timeStamp;
    this.reason = reason;
  }

  @Override
  public Builder newBuilder() {
    Builder builder = new Builder();
    builder.versionInfo = versionInfo;
    builder.resultCode = resultCode;
    builder.uniqueId = uniqueId;
    builder.timeStamp = timeStamp;
    builder.reason = reason;
    builder.addUnknownFields(unknownFields());
    return builder;
  }

  @Override
  public boolean equals(Object other) {
    if (other == this) return true;
    if (!(other instanceof StartPkRsp)) return false;
    StartPkRsp o = (StartPkRsp) other;
    return unknownFields().equals(o.unknownFields())
        && Internal.equals(versionInfo, o.versionInfo)
        && resultCode.equals(o.resultCode)
        && Internal.equals(uniqueId, o.uniqueId)
        && Internal.equals(timeStamp, o.timeStamp)
        && Internal.equals(reason, o.reason);
  }

  @Override
  public int hashCode() {
    int result = super.hashCode;
    if (result == 0) {
      result = unknownFields().hashCode();
      result = result * 37 + (versionInfo != null ? versionInfo.hashCode() : 0);
      result = result * 37 + resultCode.hashCode();
      result = result * 37 + (uniqueId != null ? uniqueId.hashCode() : 0);
      result = result * 37 + (timeStamp != null ? timeStamp.hashCode() : 0);
      result = result * 37 + (reason != null ? reason.hashCode() : 0);
      super.hashCode = result;
    }
    return result;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    if (versionInfo != null) builder.append(", versionInfo=").append(versionInfo);
    builder.append(", resultCode=").append(resultCode);
    if (uniqueId != null) builder.append(", uniqueId=").append(uniqueId);
    if (timeStamp != null) builder.append(", timeStamp=").append(timeStamp);
    if (reason != null) builder.append(", reason=").append(reason);
    return builder.replace(0, 2, "StartPkRsp{").append('}').toString();
  }

  public static final class Builder extends Message.Builder<StartPkRsp, Builder> {
    public VersionInfo versionInfo;

    public Integer resultCode;

    public Long uniqueId;

    public Long timeStamp;

    public String reason;

    public Builder() {
    }

    public Builder versionInfo(VersionInfo versionInfo) {
      this.versionInfo = versionInfo;
      return this;
    }

    public Builder resultCode(Integer resultCode) {
      this.resultCode = resultCode;
      return this;
    }

    public Builder uniqueId(Long uniqueId) {
      this.uniqueId = uniqueId;
      return this;
    }

    public Builder timeStamp(Long timeStamp) {
      this.timeStamp = timeStamp;
      return this;
    }

    public Builder reason(String reason) {
      this.reason = reason;
      return this;
    }

    @Override
    public StartPkRsp build() {
      if (resultCode == null) {
        throw Internal.missingRequiredFields(resultCode, "resultCode");
      }
      return new StartPkRsp(versionInfo, resultCode, uniqueId, timeStamp, reason, super.buildUnknownFields());
    }
  }

  private static final class ProtoAdapter_StartPkRsp extends ProtoAdapter<StartPkRsp> {
    ProtoAdapter_StartPkRsp() {
      super(FieldEncoding.LENGTH_DELIMITED, StartPkRsp.class);
    }

    @Override
    public int encodedSize(StartPkRsp value) {
      return (value.versionInfo != null ? VersionInfo.ADAPTER.encodedSizeWithTag(1, value.versionInfo) : 0)
          + ProtoAdapter.INT32.encodedSizeWithTag(2, value.resultCode)
          + (value.uniqueId != null ? ProtoAdapter.UINT64.encodedSizeWithTag(3, value.uniqueId) : 0)
          + (value.timeStamp != null ? ProtoAdapter.UINT64.encodedSizeWithTag(4, value.timeStamp) : 0)
          + (value.reason != null ? ProtoAdapter.STRING.encodedSizeWithTag(5, value.reason) : 0)
          + value.unknownFields().size();
    }

    @Override
    public void encode(ProtoWriter writer, StartPkRsp value) throws IOException {
      if (value.versionInfo != null) VersionInfo.ADAPTER.encodeWithTag(writer, 1, value.versionInfo);
      ProtoAdapter.INT32.encodeWithTag(writer, 2, value.resultCode);
      if (value.uniqueId != null) ProtoAdapter.UINT64.encodeWithTag(writer, 3, value.uniqueId);
      if (value.timeStamp != null) ProtoAdapter.UINT64.encodeWithTag(writer, 4, value.timeStamp);
      if (value.reason != null) ProtoAdapter.STRING.encodeWithTag(writer, 5, value.reason);
      writer.writeBytes(value.unknownFields());
    }

    @Override
    public StartPkRsp decode(ProtoReader reader) throws IOException {
      Builder builder = new Builder();
      long token = reader.beginMessage();
      for (int tag; (tag = reader.nextTag()) != -1;) {
        switch (tag) {
          case 1: {
            try {
              builder.versionInfo(VersionInfo.ADAPTER.decode(reader));
            } catch (ProtoAdapter.EnumConstantNotFoundException e) {
              builder.addUnknownField(tag, FieldEncoding.VARINT, (long) e.value);
            }
            break;
          }
          case 2: builder.resultCode(ProtoAdapter.INT32.decode(reader)); break;
          case 3: builder.uniqueId(ProtoAdapter.UINT64.decode(reader)); break;
          case 4: builder.timeStamp(ProtoAdapter.UINT64.decode(reader)); break;
          case 5: builder.reason(ProtoAdapter.STRING.decode(reader)); break;
          default: {
            FieldEncoding fieldEncoding = reader.peekFieldEncoding();
            Object value = fieldEncoding.rawProtoAdapter().decode(reader);
            builder.addUnknownField(tag, fieldEncoding, value);
          }
        }
      }
      reader.endMessage(token);
      return builder.build();
    }

    @Override
    public StartPkRsp redact(StartPkRsp value) {
      Builder builder = value.newBuilder();
      builder.clearUnknownFields();
      return builder.build();
    }
  }
}
