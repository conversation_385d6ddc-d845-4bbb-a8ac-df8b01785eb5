// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/lewis/project/live/LiveBundle/LiveCommon/common/common_proto/pb/PK.Base.proto at 153:1
package PK.Base;

import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.WireEnum;
import java.lang.Override;

public enum PropStatusV2 implements WireEnum {
  /**
   * 不展示
   */
  PROP_V2_STATUS_NONE(0),

  /**
   * 缓冲阶段
   */
  PROP_V2_STATUS_PROP_EXPLAIN(1),

  /**
   * 首送加成提示阶段
   */
  PROP_V2_STATUS_FIRST_DELIVERY_HINT(2),

  /**
   * 首送加成阶段
   */
  PROP_V2_STATUS_FIRST_DELIVERY(3),

  /**
   * 首送加成结果阶段
   */
  PROP_V2_STATUS_FIRST_DELIVERY_RESULT(4),

  /**
   * 助力任务提示阶段
   */
  PROP_V2_STATUS_ASSIST_TASK_HINT(5),

  /**
   * 助力任务收集阶段
   */
  PROP_V2_STATUS_ASSIST_TASK(6),

  /**
   * 助力任务失败阶段
   */
  PROP_V2_STATUS_ASSIST_TASK_FAIL(7),

  /**
   * 助力任务成功阶段
   */
  PROP_V2_STATUS_ASSIST_TASK_SUCCESS(8),

  /**
   * 助力加成阶段
   */
  PROP_V2_STATUS_ASSIST_BONUS(9),

  /**
   * 助力加成结果展示阶段
   */
  PROP_V2_STATUS_ASSIST_BONUS_RESULT(10),

  /**
   * 斩杀时刻提示阶段
   */
  PROP_V2_STATUS_KILL_HINT(11),

  /**
   * 斩杀时刻阶段
   */
  PROP_V2_STATUS_KILL(12),

  /**
   * 斩杀失败
   */
  PROP_V2_STATUS_KILL_FAIL(13),

  /**
   * pk战报展示阶段
   */
  PROP_V2_STATUS_PK_REPORT(14);

  public static final ProtoAdapter<PropStatusV2> ADAPTER = ProtoAdapter.newEnumAdapter(PropStatusV2.class);

  private final int value;

  PropStatusV2(int value) {
    this.value = value;
  }

  /**
   * Return the constant for {@code value} or null.
   */
  public static PropStatusV2 fromValue(int value) {
    switch (value) {
      case 0: return PROP_V2_STATUS_NONE;
      case 1: return PROP_V2_STATUS_PROP_EXPLAIN;
      case 2: return PROP_V2_STATUS_FIRST_DELIVERY_HINT;
      case 3: return PROP_V2_STATUS_FIRST_DELIVERY;
      case 4: return PROP_V2_STATUS_FIRST_DELIVERY_RESULT;
      case 5: return PROP_V2_STATUS_ASSIST_TASK_HINT;
      case 6: return PROP_V2_STATUS_ASSIST_TASK;
      case 7: return PROP_V2_STATUS_ASSIST_TASK_FAIL;
      case 8: return PROP_V2_STATUS_ASSIST_TASK_SUCCESS;
      case 9: return PROP_V2_STATUS_ASSIST_BONUS;
      case 10: return PROP_V2_STATUS_ASSIST_BONUS_RESULT;
      case 11: return PROP_V2_STATUS_KILL_HINT;
      case 12: return PROP_V2_STATUS_KILL;
      case 13: return PROP_V2_STATUS_KILL_FAIL;
      case 14: return PROP_V2_STATUS_PK_REPORT;
      default: return null;
    }
  }

  @Override
  public int getValue() {
    return value;
  }
}
