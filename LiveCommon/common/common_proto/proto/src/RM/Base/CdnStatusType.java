// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: RM.Base.Ext.proto at 100:1
package RM.Base;

import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.WireEnum;
import java.lang.Override;

public enum CdnStatusType implements WireEnum {
  CDN_STATUS_START(1),

  CDN_STATUS_STOP(2);

  public static final ProtoAdapter<CdnStatusType> ADAPTER = ProtoAdapter.newEnumAdapter(CdnStatusType.class);

  private final int value;

  CdnStatusType(int value) {
    this.value = value;
  }

  /**
   * Return the constant for {@code value} or null.
   */
  public static CdnStatusType fromValue(int value) {
    switch (value) {
      case 1: return CDN_STATUS_START;
      case 2: return CDN_STATUS_STOP;
      default: return null;
    }
  }

  @Override
  public int getValue() {
    return value;
  }
}
