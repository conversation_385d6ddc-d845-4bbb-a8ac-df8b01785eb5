package com.ximalaya.ting.android.live.hall.components2;

import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.live.hall.entity.RoomPlayRuleInfo;
import com.ximalaya.ting.android.live.hall.fragment.IEntHallRoom;
import com.ximalaya.ting.android.live.host.components.IBaseLiveHostInteraction;
import com.ximalaya.ting.android.live.lib.chatroom.manager.IManager;

/**
 * 组件基础交互接口，
 */
public interface IBaseEntHostInteraction extends IBaseLiveHostInteraction {
      IManager getManager(String name);
      IEntHallRoom.IView getRootComponent();

      void followRoom();

      void showOpenNotifyDialog();

      /**
       * 打开房间详情弹框
       */
      void showRoomInfoDialog(RoomPlayRuleInfo roomPlayRuleInfo);

      Fragment getFragment();
}
