package com.ximalaya.ting.android.live.hall.entity;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.live.lib.liveroomalbum.bean.raw.RoomAlbumRawPhotoInfo;
import com.ximalaya.ting.android.live.lib.liveroomalbum.bean.raw.RoomAlbumRawWonderfulMoments;

import java.util.List;

/**
 * Created by qianmenchao on 2022/7/28.
 *
 * <AUTHOR>
 */
@Keep
public class EnterRoomRawModel {

    @Nullable
    public String tips;

    @Nullable
    public RoomPhotoAlbumResp roomPhotoAlbumResp;

    @Keep
    public static class RoomPhotoAlbumResp {

        public int pictureCount;

        public String photoAlbumTitle;

        @Nullable
        public List<RoomAlbumRawPhotoInfo> pictureList;

        @Nullable
        public RoomAlbumRawWonderfulMoments wonderfulMomentResp;
    }
}