package com.ximalaya.ting.android.live.hall.entity.pk;

import java.util.List;

/**
 * desc: 跨房 PK 房间搜索返回结果
 *
 * Created by zoey on 2022/6/2.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class RoomPKSearchResult {

    // PK 可选分钟数
    public List<Integer> pkTimeMinuteLevels;

    // 房间信息
    public RoomInfo roomInfo;

    public static class RoomInfo {
        public int categoryId = 0;
        public long chatId;
        public String coverPath;
        public long fmId;
        public String largeCoverUrl;
        public String middleCoverUrl;
        public long roomId;
        public long roomUid;
        public String smallCoverUrl;
        public String title;
    }
}
