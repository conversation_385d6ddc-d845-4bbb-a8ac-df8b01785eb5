package com.ximalaya.ting.android.live.hall.view.gift;

import android.app.Activity;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.ximalaya.ting.android.live.biz.mode.data.CommonProtoConstant;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.entity.LiveUserInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.SendGiftDialog;
import com.ximalaya.ting.android.live.common.lib.gift.panel.multireceiver.MultiReceiverGiftDialog;

/**
 * 娱乐派对送礼面板.
 *
 * <AUTHOR>
 * 接口文档:http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/treasure/v1/treasure-package-web-api.md
 */
public class HallGiftDialog extends MultiReceiverGiftDialog<HallGiftLoader> {

    /**
     * 获取娱乐派对的玩法模式
     */
    private OnGetEntMode mOnGetEntMode;

    @Keep
    protected HallGiftDialog(Activity context) {
        super(context, STYLE_TRANSPARENT);
    }


    @Override
    protected boolean isLiveTypeGift() {
        return false;
    }

    @Override
    protected boolean isDarkMode() {
        return true;
    }

    @Override
    protected boolean canUseNobleDiamond() {
        return true;
    }

    @Override
    protected boolean canShowReminderView() {
        // PGC 只显示麦上主播列表，不显示开通提示
        return true;
    }


    @Override
    protected boolean canBaseGiftDialogUploadTabIndicatorChangedUserTracking() {
        return false;
    }

    public static class SendBuilder extends SendGiftDialog.SendBuilder<HallGiftDialog> {
        private final long mRoomId;
        private final long mRoomUid;
        private OnShowUserInfo mOnShowUserInfo;
        private OnGetEntMode mOnGetEntMode;

        public SendBuilder(Activity mActivity, @NonNull Long roomId, @NonNull Long roomUid) {
            super(mActivity);
            mRoomId = roomId;
            mRoomUid = roomUid;
        }

        public SendBuilder setOnShowUserInfo(OnShowUserInfo mOnShowUserInfo) {
            this.mOnShowUserInfo = mOnShowUserInfo;
            return this;
        }

        public SendBuilder setCurrentUserInfo(LiveUserInfo currentUserInfo) {
            this.setMyUserInfo(currentUserInfo);
            return this;
        }

        public SendBuilder setCurrentHostUid(long hostUid) {
            this.setHostUid(hostUid);
            return this;
        }

        public SendBuilder setOnGetEntMode(OnGetEntMode onGetEntMode) {
            mOnGetEntMode = onGetEntMode;
            return this;
        }

        @Override
        public HallGiftDialog build() {
            HallGiftDialog dialog = super.build();

            if (null != dialog) {
                dialog.mRoomId = mRoomId; // must
                dialog.mRoomUid = mRoomUid; // must
                dialog.mOnShowUserInfo = mOnShowUserInfo;
                dialog.mOnGetEntMode = mOnGetEntMode;
                dialog.mSendType = ParamsConstantsInLive.SEND_GIFT_TYPE_HALL;
            }

            return dialog;
        }
    }


    /**
     * 获取房间玩法模式
     */
    public interface OnGetEntMode {
        /**
         * 获取房间玩法模式
         */
        @CommonProtoConstant.EntMode
        int getEntMode();
    }

    public int getEntMode() {
        if (mOnGetEntMode == null) {
            return CommonProtoConstant.EntMode.ENT_MODE_NONE;
        }

        return mOnGetEntMode.getEntMode();
    }
}
