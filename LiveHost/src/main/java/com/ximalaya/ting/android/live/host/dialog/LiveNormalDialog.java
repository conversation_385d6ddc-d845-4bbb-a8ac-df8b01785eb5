package com.ximalaya.ting.android.live.host.dialog;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.common.dialog.base.LiveXmBaseDialog;
import com.ximalaya.ting.android.live.host.R;

/**
 * Created by zhixin.he on 2022/1/10.
 *
 * @desc 常用确认弹窗
 * @email <EMAIL>
 * @phone 15026804470
 */
public class LiveNormalDialog extends LiveXmBaseDialog {

    private OnClickListener mClickListener;
    private TextView mTvCancel;
    private TextView mTvOk;
    private TextView mTvContent;
    private TextView mTvTitle;
    private LinearLayout mBottomBackground;
    private View mViewLine;

    public LiveNormalDialog(@NonNull Context context) {
        this(context, 0);
    }

    public LiveNormalDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        init();
    }

    private void init() {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            int screenWidth = BaseUtil.getScreenWidth(getContext());
            lp.width = screenWidth - BaseUtil.dp2px(getContext(), 100);
            lp.gravity = Gravity.CENTER;
            window.setAttributes(lp);
            window.setBackgroundDrawableResource(android.R.color.transparent);
        }
        setCanceledOnTouchOutside(true);
        setCancelable(true);

        View view = View.inflate(getContext(), R.layout.livehost_layout_normal_dialog, null);
        setContentView(view);

        mBottomBackground = findViewById(R.id.bottom_background);
        mViewLine = findViewById(R.id.view_line);
        mTvCancel = view.findViewById(R.id.live_dialog_normal_cancle);
        mTvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mClickListener != null) {
                    mClickListener.onClickCancel();
                }
                dismiss();
            }
        });
        mTvOk = view.findViewById(R.id.live_dialog_normal_ok);
        mTvOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mClickListener != null) {
                    mClickListener.onClickOK();
                }
                dismiss();
            }
        });
        mTvTitle = view.findViewById(R.id.live_dialog_normal_title_tv);
        mTvContent = view.findViewById(R.id.live_dialog_normal_content_tv);


    }

    public LiveNormalDialog setTitle(String title) {
        mTvTitle.setText(title);
        return this;
    }

    public LiveNormalDialog setTitleSize(int size) {
        mTvTitle.setTextSize(size);
        return this;
    }

    public LiveNormalDialog setContent(String content) {
        mTvContent.setText(content);
        return this;
    }

    public LiveNormalDialog setOk(String ok) {
        mTvOk.setText(ok);
        return this;
    }

    public LiveNormalDialog setCancel(String cancel) {
        mTvCancel.setText(cancel);
        return this;
    }

    /**
     * 外部需要调整ok按钮的颜色
     *
     * @param color color
     * @return LiveNormalDialog
     */
    public LiveNormalDialog setTvOkTextColor(int color) {
        mTvOk.setTextColor(color);
        return this;
    }

    /**
     * 外部需要调整ok按钮的背景
     *
     * @param drawable Drawable
     * @return LiveNormalDialog
     */
    public LiveNormalDialog setTvOkBackgroundColor(Drawable drawable) {
        mTvOk.setBackground(drawable);
        return this;
    }

    /**
     * 设置底部背景
     *
     * @param drawable Drawable
     * @return LiveNormalDialog
     */
    public LiveNormalDialog setBottomBackgroundColor(Drawable drawable) {
        mBottomBackground.setBackground(drawable);
        mViewLine.setVisibility(View.GONE);
        return this;
    }

    /**
     * 设置隐藏取消按钮
     *
     * @return LiveNormalDialog
     */
    public LiveNormalDialog hideCancelBtn() {
        ViewStatusUtil.setVisible(View.GONE, mTvCancel, mViewLine);
        return this;
    }

    /**
     * 设置隐藏Title
     *
     * @return LiveNormalDialog
     */
    public LiveNormalDialog hideTitle() {
        ViewStatusUtil.setVisible(View.GONE, mTvTitle);
        return this;
    }

    /**
     * 设置隐藏Content
     *
     * @return LiveNormalDialog
     */
    public LiveNormalDialog hideContent() {
        ViewStatusUtil.setVisible(View.GONE, mTvContent);
        return this;
    }

    /**
     * 设置点击返回是否可以关闭
     *
     * @param able ture: 可以关闭, false: 不可以关闭
     * @return LiveNormalDialog
     */
    public LiveNormalDialog setClickBack(boolean able) {
        setCancelable(able);
        return this;
    }

    /**
     * 设置点击空白区域是否可以关闭
     *
     * @param able ture: 可以关闭, false: 不可以关闭
     * @return LiveNormalDialog
     */
    public LiveNormalDialog setClickOnTouchOutside(boolean able) {
        setCanceledOnTouchOutside(able);
        return this;
    }


    public LiveNormalDialog setOnClickListener(OnClickListener clickListener) {
        mClickListener = clickListener;
        return this;
    }

    public interface OnClickListener {
        void onClickOK();

        void onClickCancel();
    }

}
