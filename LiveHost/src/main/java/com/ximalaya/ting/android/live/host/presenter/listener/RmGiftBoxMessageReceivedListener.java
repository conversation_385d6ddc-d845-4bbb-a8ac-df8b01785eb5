package com.ximalaya.ting.android.live.host.presenter.listener;

import com.ximalaya.ting.android.live.host.fragment.room.IBaseRoom;
import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonGiftBoxListMessage;
import com.ximalaya.ting.android.live.lib.chatroom.manager.IRmMessageDispatcherManager;

/**
 * 宝箱礼物消息、上上签礼物消息。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @since 2020-02-04
 */
public class RmGiftBoxMessageReceivedListener implements IRmMessageDispatcherManager.IRmMessageReceivedListener.IGiftBoxMessageReceivedListener {
    private final IBaseRoom.IView mBaseRoomComponent;

    public RmGiftBoxMessageReceivedListener(IBaseRoom.IView baseRoomComponent) {
        mBaseRoomComponent = baseRoomComponent;
    }

    @Override
    public void onGiftBoxListMessageReceived(CommonGiftBoxListMessage msg) {
        if (mBaseRoomComponent == null || !mBaseRoomComponent.canUpdateUi()) {
            return;
        }
        mBaseRoomComponent.onReceiverOpenGiftListMessage(msg);
    }
}
