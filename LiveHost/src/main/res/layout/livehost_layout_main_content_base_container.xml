<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="150dp"
    android:background="@drawable/livehost_bg_custom_gradient_background">

    <!-- 左侧固定区域：头像和声波 -->
    <FrameLayout
        android:id="@+id/live_left_content_container"
        android:layout_width="128dp"
        android:layout_height="138dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 默认内容：原有的声音波形、头像、动画等 -->
        <FrameLayout
            android:id="@+id/live_left_default_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.ximalaya.ting.android.live.common.view.widget.LiveSoundWaveView
                android:id="@+id/live_sound_wave_view"
                android:layout_width="128dp"
                android:layout_height="128dp"
                android:layout_gravity="center"
                app:avatarSize="85dp" />

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/live_avatar_image_view"
                android:layout_width="85dp"
                android:layout_height="85dp"
                android:layout_gravity="center"
                android:scaleType="centerCrop"
                app:corner_radius="85dp" />

            <com.ximalaya.ting.android.live.common.lib.gift.anim.svg.SVGAView
                android:id="@+id/live_main_content_svga_big"
                android:layout_width="128dp"
                android:layout_height="128dp"
                android:layout_gravity="center"
                android:visibility="gone"/>

            <com.ximalaya.ting.android.live.common.lib.gift.anim.svg.SVGAView
                android:id="@+id/live_main_content_svga_right"
                android:layout_width="128dp"
                android:layout_height="128dp"
                android:layout_gravity="center"
                android:visibility="gone"/>
        </FrameLayout>
    </FrameLayout>

    <!-- 右侧内容区域 -->
    <FrameLayout
        android:id="@+id/live_right_content_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/live_left_content_container"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>