package com.ximalaya.ting.android.main;

import android.content.Context;
import android.os.Handler;
import android.text.Layout;
import android.text.SpannableString;
import android.text.StaticLayout;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.base.IConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.core.Warehouse;
import com.ximalaya.ting.android.framework.arouter.facade.template.IInterceptorGroup;
import com.ximalaya.ting.android.framework.arouter.facade.template.IProviderGroup;
import com.ximalaya.ting.android.framework.arouter.facade.template.IRouteRoot;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.data.request.HomePageTabRequestTask;
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager;
import com.ximalaya.ting.android.host.manager.PlanTerminateManager;
import com.ximalaya.ting.android.host.manager.PlanTerminateManagerForQuickListen;
import com.ximalaya.ting.android.host.manager.ad.BackgroundListenerAdManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication;
import com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.GameAdActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager;
import com.ximalaya.ting.android.host.manager.play.PlayPageAdFreeManager;
import com.ximalaya.ting.android.host.manager.statistic.PlayStatisticsUploaderFactory;
import com.ximalaya.ting.android.host.view.text.TextRemoteImageManager;
import com.ximalaya.ting.android.main.activity.test.TestActivity;
import com.ximalaya.ting.android.main.adModule.manager.PlayAdStateRecordManager;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.PushSettingFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.HttpProxyControllerToggle;
import com.ximalaya.ting.android.main.listener.WXMiniProgramBindAuthObservable;
import com.ximalaya.ting.android.main.manager.MainActionImpl;
import com.ximalaya.ting.android.main.manager.MainActivityActionImpl;
import com.ximalaya.ting.android.main.manager.MainFragmentActionImpl;
import com.ximalaya.ting.android.main.manager.listentask.ListenTaskManager;
import com.ximalaya.ting.android.main.playModule.statistics.PlayStatisticsUploaderFactoryInMain;
import com.ximalaya.ting.android.main.util.StableUtil;
import com.ximalaya.ting.android.main.view.text.StaticLayoutView;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.httputil.Config;
import com.ximalaya.ting.android.opensdk.httputil.HttpUrlUtil;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.statistic.IPlayStatisticsUploaderFactory;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayStatisticsUploaderManager;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.route.handle.XmUriRouterManager;
import com.ximalaya.ting.android.wxcallback.wxsharelogin.WXAuthCodeObservable;
import com.ximalaya.ting.android.xmutil.Logger;


/**
 * <AUTHOR>
 * <AUTHOR> on 2017/1/16.
 */

public class MainApplication implements IApplication<MainActionRouter> {
    private Context mContext;
    private Handler mHandler;


    public static final String APPLICATION_PACKAGE_NAME = "com.ximalaya.ting.android.main";
    public static final String APP_ROUTER_ROOT_CLASS_NAME = "ARouter$$Root$$MainModule";
    public static final String APP_ROUTER_PROVIDERS_CLASS_NAME = "ARouter$$Providers$$MainModule";
    public static final String APP_ROUTER_INTERCEPTORS_CLASS_NAME = "ARouter$$Interceptors$$MainModule";

    @Override
    public void attachBaseContext(Context context) {
        mContext = context;
        mHandler = HandlerManager.obtainMainHandler();
        if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedPreLoadFromLocal()) {
            RecommendFragmentNetManager.Companion.getInstance().preLoadDataFromLocal();
        }
    }

    @NonNull
    @Override
    public Class<MainActionRouter> onCreateAction() {
        return MainActionRouter.class;
    }

    @Override
    public void onCreate(MainActionRouter actionRouter) {
        try {
            actionRouter.addMainAction(RouterConstant.FRAGMENT_ACTION, new MainFragmentActionImpl());
            actionRouter.addMainAction(RouterConstant.FUNCTION_ACTION, new MainActionImpl());
            actionRouter.addMainAction(RouterConstant.ACTIVITY_ACTION, new MainActivityActionImpl());
        } catch (Exception e) {
            CrashReport.postCatchedException(e);
        }

        try{
            String rootClassName = APPLICATION_PACKAGE_NAME + "." + APP_ROUTER_ROOT_CLASS_NAME;
            ((IRouteRoot) (Class.forName(rootClassName).getConstructor().newInstance())).loadInto(Warehouse.groupsIndex);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            String providersClassName = APPLICATION_PACKAGE_NAME + "." + APP_ROUTER_PROVIDERS_CLASS_NAME;
            ((IProviderGroup) (Class.forName(providersClassName).getConstructor().newInstance())).loadInto(Warehouse.providersIndex);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            String interceptorsClassName = APPLICATION_PACKAGE_NAME + "." + APP_ROUTER_INTERCEPTORS_CLASS_NAME;
            ((IInterceptorGroup) (Class.forName(interceptorsClassName).getConstructor().newInstance())).loadInto(Warehouse.interceptorsIndex);
        } catch (Exception e) {
            e.printStackTrace();
        }

        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Class.forName("com.ximalaya.ting.android.main.fragment.find.HomePageFragment");
                    Class.forName("com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentNew");
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }
        });

        if (ConstantsOpenSdk.isDebug) {
            HttpProxyControllerToggle.initOnCreate(mContext);
            Logger.log("MainApplication : initOnCreate 8");
        }

        if (BaseUtil.isMainProcess(mContext)) {
            XmPlayerManager.getInstance(mContext).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                @Override
                public void onConnected() {
                    XmPlayerManager.getInstance(mContext).setHttpConfig(HttpUrlUtil.mConfig);
                }
            });

            XmPlayerManager.getInstance(mContext).addAdsStatusListener(
                    BackgroundListenerAdManager.getInstance());
            XmPlayerManager.getInstance(mContext).addPlayerStatusListener(
                    BackgroundListenerAdManager.getInstance());
            PlanTerminateManager.release();
            PlanTerminateManagerForQuickListen.release();
        }

        XmUriRouterManager.getInstance().addBundleRouteHandler(Configure.mainBundleModel.bundleName, new MainRouterHandler());
        StableUtil.init();
    }

    @Override
    public void initApp() {
        if (ConstantsOpenSdk.isDebug
                && SharedPreferencesUtil.getInstance(mContext).contains(PreferenceConstantsInOpenSdk.MAIN_SAVE_PROXY_HOST)) {
            // 设置mock代理
            Config config = new Config();
            config.useProxy = true;
            config.proxyHost = SharedPreferencesUtil.getInstance(mContext).getString(PreferenceConstantsInOpenSdk.MAIN_SAVE_PROXY_HOST);
            config.proxyPort = 47777;
            BaseCall.getInstanse().setHttpConfig(config);
            TestActivity.isUsingProxy = true;
        }

        String taskCenterConfigStr = ConfigureCenter.getInstance().getJsonString("toc", "taskCenterConfig", "");
        boolean isAgreed =
                MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getBooleanCompat(
                PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PRIVACY_POLICY_AGREED_NEW);

        if (isAgreed) {
            ListenTaskManager.getInstance().initLocalTime(mContext);
        }

        Logger.log("MainApplication : initApp taskCenterConfigStr = " + taskCenterConfigStr);

        if (TextUtils.isEmpty(taskCenterConfigStr)) {
            ConfigureCenter.getInstance().registerConfigFetchCallback(new IConfigureCenter.ConfigFetchCallback() {
                @Override
                public void onUpdateSuccess() {
                    ConfigureCenter.getInstance().unRegisterConfigFetchCallback(this);
                    ListenTaskManager.getInstance().initLocalTime(mContext);
                    ListenTaskManager.getInstance().onConfigureCallBack(mContext);
                }

                @Override
                public void onRequestError() {
                    ConfigureCenter.getInstance().unRegisterConfigFetchCallback(this);
                    ListenTaskManager.getInstance().initLocalTime(mContext);
                    ListenTaskManager.getInstance().onConfigureCallBack(mContext);
                }
            });
        } else {
            ListenTaskManager.getInstance().onConfigureCallBack(mContext);
        }

        //同步推送
        new MyAsyncTask<Void, Void, Void>() {
            @Override
            protected Void doInBackground(Void... params) {
                if (mContext != null) {
                    PushSettingFragment.syncPushSettingToServer(mContext);
                }
                return null;
            }
        }.myexec();

        PlayStatisticsUploaderManager.getInstance().init(mContext);
        IPlayStatisticsUploaderFactory factory = new PlayStatisticsUploaderFactoryInMain();
        PlayStatisticsUploaderManager.getInstance().addUploaderFactory(factory);
        IPlayStatisticsUploaderFactory factory1 = new PlayStatisticsUploaderFactory();
        PlayStatisticsUploaderManager.getInstance().addUploaderFactory(factory1);
        PlayStatisticsUploaderManager.getInstance().restoreUploaderInBackground();

        PlayAdStateRecordManager.getInstance().startListener();

        WXMiniProgramBindAuthObservable miniProgramBindAuthObservable = new WXMiniProgramBindAuthObservable();
        WXAuthCodeObservable.getInstance().registerObserver(miniProgramBindAuthObservable);

        if(MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getBooleanCompat(
                PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PRIVACY_POLICY_AGREED_NEW)) {
            // 预加载FindingTab
            new HomePageTabRequestTask().myexec();
            AdMakeVipLocalManager.getInstance().requestIsTargetUser();
        }

        TextRemoteImageManager.addViewAutoSetMethod(new TextRemoteImageManager.IViewAutoSetMethod() {

            @Override
            public int getMethodType() {
                return 1;
            }

            @Override
            public boolean isMyType(@NonNull View view) {
                return view instanceof StaticLayoutView;
            }

            @Override
            public void setView(@NonNull View view, @NonNull SpannableString newContent) {
                if (view instanceof StaticLayoutView) {
                    Layout oldLayout = ((StaticLayoutView) view).getLayout();
                    if (oldLayout != null) {
                        StaticLayout newLayout = new StaticLayout(newContent, oldLayout.getPaint(),
                                oldLayout.getWidth(), oldLayout.getAlignment(),
                                oldLayout.getSpacingMultiplier(), oldLayout.getSpacingAdd(), true);
                        ((StaticLayoutView) view).setLayout(newLayout);
                        view.invalidate();
                    }
                }
            }

            @Nullable
            @Override
            public CharSequence getViewText(@NonNull View view) {
                if (view instanceof StaticLayoutView) {
                    Layout oldLayout = ((StaticLayoutView) view).getLayout();
                    if (oldLayout instanceof StaticLayout) {
                        return oldLayout.getText();
                    }
                }
                return null;
            }
        });

        PlayPageAdFreeManager.checkUpdate();
    }

    @Override
    public void exitApp() {
        PlayAdStateRecordManager.getInstance().removeListener();

        try {
            if (Router.<GameAdActionRouter>getActionRouter(Configure.BUNDLE_GAMEAD) != null) {
                Router.<GameAdActionRouter>getActionRouter(Configure.BUNDLE_GAMEAD).getFunctionAction().release();
            }
            PlanTerminateManager.release();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
