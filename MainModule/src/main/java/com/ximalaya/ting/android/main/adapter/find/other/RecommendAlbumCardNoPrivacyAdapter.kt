package com.ximalaya.ting.android.main.adapter.find.other

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.dialog.OpenNormalModeDialogFragment
import com.ximalaya.ting.android.host.model.RecommendAlbumCard
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.view.LocalImageUtil
import com.ximalaya.ting.android.host.view.layout.FlowLayout
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.mine.extension.visible
import com.ximalaya.ting.android.main.playpage.playx.view.cover.XAudioAnimateCoverView

/**
 * Created by WolfXu on 2023/5/24.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class RecommendAlbumCardNoPrivacyAdapter(private val mFragment: Fragment,
                                         private val mThemeColorCallback: (RecommendAlbumCard) -> Unit) :
    RecyclerView.Adapter<RecommendAlbumCardNoPrivacyAdapter.RecommendAlbumCardHolder>() {

    companion object {
        private val TAG = RecommendAlbumCardNoPrivacyAdapter::class.java.simpleName
    }

    val cards: MutableList<RecommendAlbumCard> = mutableListOf()
    private val mContext = mFragment.context
    private var trackTitle: String = ""
    private var mIsSmallDevice: Boolean = false

    fun setCurTrackTitle(title: String) {
        this.trackTitle = title
        notifyDataSetChanged()
    }

    fun setIsSmallDevice(isSmallDevice: Boolean) {
        mIsSmallDevice = isSmallDevice
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecommendAlbumCardHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.main_layout_recommend_album_card_item_no_privacy, parent, false)
        return RecommendAlbumCardHolder(view, mIsSmallDevice)
    }

    override fun onBindViewHolder(holder: RecommendAlbumCardHolder, position: Int) {
        val recommendAlbumCard = cards.getOrNull(position) ?: return
        val album = recommendAlbumCard.albumM ?: return
        val nextAlbum = cards.getOrNull(position + 1)?.albumM
        if (holder.curCard != recommendAlbumCard) {
            bindTags(holder, album)
        }
        holder.curCard = recommendAlbumCard
        bindCover(holder, recommendAlbumCard, album)
        holder.tvRecommendReason?.text = album.intro
        holder.tvAlbumTitle?.text = album.albumTitle
        "（共${album.includeTrackCount}集）".also { holder.tvAlbumTrackCount?.text = it }
        holder.tvListenLater?.setTextColor(recommendAlbumCard.cachedThemeColor)
        holder.tvTrackTitle?.text = trackTitle
        val nextAlbumTrackTitle = nextAlbum?.firstTrackTitle
        if (nextAlbumTrackTitle != null) {
            holder.tvNextAlbumTrackTitle.visible(View.VISIBLE)
            "下一集：$nextAlbumTrackTitle".also { holder.tvNextAlbumTrackTitle?.text = it }
        } else {
            holder.tvNextAlbumTrackTitle.visible(View.GONE)
        }
        holder.tvListenLater.visible(if (album.isFavorite) View.INVISIBLE else View.VISIBLE)
        setClickListener(album, holder)
    }

    private fun bindCover(holder: RecommendAlbumCardHolder, recommendAlbumCard: RecommendAlbumCard, albumM: AlbumM) {
        val coverUrl = albumM.validCover
        val coverView = holder.cover ?: return
        val context = coverView.context
        coverView.audioTopCover.setTag(
            com.ximalaya.ting.android.framework.R.id.framework_img_load_istran, false)
        coverView.setCoverBitmap(ImageManager.from(context).getFromMemCache(coverUrl))
        ImageManager.from(context).displayImage(
            coverView.audioTopCover, coverUrl,
            R.drawable.main_default_album_x) { lastUrl, bitmap ->
            if (bitmap != null) {
                coverView.setCoverBitmap(bitmap)
                if (!recommendAlbumCard.hasSetCachedThemeColor()) {
                    LocalImageUtil.getDomainColor(bitmap, Color.BLACK) { color: Int ->
                        recommendAlbumCard.setHasSetCachedThemeColor(true)
                        recommendAlbumCard.cachedThemeColor =
                            ColorUtil.covertColorToFixedSaturationAndLightness(
                                color, 0.25f, 0.39f)
                        if (recommendAlbumCard == holder.curCard) {
                            notifyItemChanged(holder.bindingAdapterPosition)
                            mThemeColorCallback(recommendAlbumCard)
                        }
                    }
                }
            }
        }

        coverView.updateShadow(
            ColorUtil.covertColorToFixedSaturationAndLightness(recommendAlbumCard.cachedThemeColor, 0.45f, 0.25f)
        )

        val playCount = albumM.playCount
        if (playCount > 0) {
            coverView.audioTopCover.playCountStr = StringUtil.getFriendlyNumStr(playCount)
        } else {
            coverView.audioTopCover.playCountStr = ""
        }
    }

    private fun bindTags(holder: RecommendAlbumCardHolder, albumM: AlbumM) {
        val flowLayoutTags = holder.flowLayoutTags ?: return
        flowLayoutTags.removeAllViews()
        flowLayoutTags.line = 1
        val rankLabel = albumM.albumRankInfo?.showLabel
        if (!rankLabel.isNullOrEmpty()) {
            flowLayoutTags.addView(holder.vgRankInfo)
            holder.vgRankInfo.visible(View.VISIBLE)
            val slices = rankLabel.split(" ")
            val rankPosition = slices.getOrNull(0)?.takeIf { it.startsWith("NO") }
            var rankInfo = ""
            if (rankPosition.isNullOrEmpty()) {
                holder.tvRankPosition.visible(View.GONE)
                rankInfo = slices.joinToString("")
            } else {
                holder.tvRankPosition.visible(View.VISIBLE)
                holder.tvRankPosition?.text = rankPosition
                rankInfo = slices.subList(1, slices.size).joinToString("")
            }
            holder.tvRank?.text = rankInfo
        }

        val tags = albumM.tagResults
        if (tags.isNullOrEmpty().not()) {
            tags.forEach {
                if (it?.tagName.isNullOrEmpty().not()) {
                    flowLayoutTags.addView(buildAlbumTagView(it.tagName), getLayoutParams())
                }
            }
        }
    }

    private fun buildAlbumTagView(category: String): View {
        val tv = TextView(mContext)
        tv.text = category
        tv.textSize = 12f
        tv.setTextColor(Color.WHITE)
//        tv.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
        tv.setBackgroundResource(R.drawable.main_recommend_album_card_tag_bg_2023)
        tv.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.main_ic_recommend_album_card_arrow, 0)
        tv.gravity = Gravity.CENTER
        return tv
    }

    private fun getLayoutParams(): FlowLayout.LayoutParams {
        val lp = FlowLayout.LayoutParams(
            FlowLayout.LayoutParams.WRAP_CONTENT,
            22.dp)
        lp.rightMargin = BaseUtil.dp2px(mContext, 12f)
        return lp
    }

    private fun setClickListener(album: AlbumM, holder: RecommendAlbumCardHolder) {
        val onClickListener = View.OnClickListener {
            if (OneClickHelper.getInstance().onClick(it)) {
                when (it) {
                    holder.tvViewAlbum -> {
                        startAlbumFragment()
                    }
                    holder.tvAlbumTitle -> {
                        startAlbumFragment()
                    }
                    holder.tvListenLater -> {
                        doSubscribe()
                    }
                }
            }
        }
        holder.tvViewAlbum?.setOnClickListener(onClickListener)
        holder.tvAlbumTitle?.setOnClickListener(onClickListener)
        holder.tvListenLater?.setOnClickListener(onClickListener)
    }

    private fun startAlbumFragment() {
        OpenNormalModeDialogFragment(mFragment, mContext?.resources?.getString(R.string.host_base_mode_dialog_one)).show(mFragment.childFragmentManager, "")
    }

    private fun doSubscribe() {
        OpenNormalModeDialogFragment(mFragment, mContext?.resources?.getString(R.string.host_base_mode_dialog_one)).show(mFragment.childFragmentManager, "")
    }

    override fun getItemCount(): Int {
        return cards.size
    }

    class RecommendAlbumCardHolder(itemView: View, isSmallDevice: Boolean) : RecyclerView.ViewHolder(itemView) {
        val cover: XAudioAnimateCoverView? = itemView.findViewById(R.id.main_cover)
        val tvViewAlbum: TextView? = itemView.findViewById(R.id.main_tv_view_album)
        val tvRecommendReason: TextView? = itemView.findViewById(R.id.main_tv_recommend_reason)
        val flowLayoutTags: FlowLayout? = itemView.findViewById(R.id.main_flow_layout_tags)
        val tvAlbumTitle: TextView? = itemView.findViewById(R.id.main_tv_album_title)
        val tvAlbumTrackCount: TextView? = itemView.findViewById(R.id.main_tv_album_track_count)
        val tvListenLater: TextView? = itemView.findViewById(R.id.main_tv_listen_later)
        val tvTrackTitle: TextView? = itemView.findViewById(R.id.main_tv_track_title)
        val tvNextAlbumTrackTitle: TextView? = itemView.findViewById(R.id.main_tv_next_album_track_title)
        val vgRankInfo: ViewGroup? = itemView.findViewById(R.id.main_vg_rank_info)
        val tvRank: TextView? = itemView.findViewById(R.id.main_tv_rank)
        val tvRankPosition: TextView? = itemView.findViewById(R.id.main_tv_rank_position)
        val vRecommendReasonBg: View? = itemView.findViewById(R.id.main_v_recommend_reason_bg)
        var curCard: RecommendAlbumCard? = null

        companion object {
            private val rankBackgroundColors = intArrayOf(0x80afa9f9.toInt(), 0x99c0a0f6.toInt(), 0x80ffa6b9.toInt(), 0x66ffd19b.toInt())
            private val cornerRadius = 1.dpFloat
            private val rankBackgroundCornerRadius = floatArrayOf(0f, 0f, cornerRadius, cornerRadius, cornerRadius, cornerRadius, 0f, 0f)
        }

        init {
            val drawable = GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, rankBackgroundColors)
            drawable.cornerRadii = rankBackgroundCornerRadius
            tvRank?.background = drawable
            if (isSmallDevice) {
                adaptForSmallDevice()
            }
        }

        private fun adaptForSmallDevice() {
            tvRecommendReason?.maxLines = 2
            cover?.let {
                val layoutParams = it.layoutParams as? ViewGroup.MarginLayoutParams
                if (layoutParams != null) {
                    layoutParams.bottomMargin = 4.dp
                    it.layoutParams = layoutParams
                }
            }
            tvViewAlbum?.let {
                val layoutParams = it.layoutParams as? ViewGroup.MarginLayoutParams
                if (layoutParams != null) {
                    layoutParams.bottomMargin = 16.dp
                    it.layoutParams = layoutParams
                }
            }
            vRecommendReasonBg?.let {
                val layoutParams = it.layoutParams as? ViewGroup.MarginLayoutParams
                if (layoutParams != null) {
                    layoutParams.bottomMargin = 16.dp
                    it.layoutParams = layoutParams
                }
            }
        }
    }
}