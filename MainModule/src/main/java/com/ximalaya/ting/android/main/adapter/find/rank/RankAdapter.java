package com.ximalaya.ting.android.main.adapter.find.rank;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.host.adapter.BaseAdapterWithTitle;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.model.ranks.Rank;
import com.ximalaya.ting.android.opensdk.model.ranks.RankItem;

import java.util.List;

/**
 * Created by xmly on 10/08/2018.
 *
 * <AUTHOR>
 */
public class RankAdapter extends BaseAdapterWithTitle<Object> {

    private BaseFragment mFragment;

    public RankAdapter(BaseFragment fragment, Context context, List<Object> listData) {
        super(context, listData);
        mFragment = fragment;
    }

    @Override
    public int getItemViewType(int position) {
        Object obj = listData.get(position);
        if (obj instanceof String) {
            return TYPE_TITLE;
        }
        return TYPE_CONTENT;
    }

    @Override
    public int getTitleViewId() {
        return R.layout.main_view_list_header;
    }

    @Override
    public void bindTitleViewDatas(BaseViewHolder holder, int position) {
        TitleViewHolder titleHolder = (TitleViewHolder) holder;
        Object o = getItem(position);
        if (o instanceof String) {
            titleHolder.titleView.setText((String) o);
        }

        if (position != 0) {
            titleHolder.borderTopViwe.setVisibility(View.VISIBLE);
        } else {
            titleHolder.borderTopViwe.setVisibility(View.GONE);
        }
    }

    @Override
    public BaseViewHolder buildTitleViewHolder(View convertView) {
        convertView.findViewById(R.id.main_btn_more).setVisibility(View.GONE);
        TitleViewHolder titleViewHolder = new TitleViewHolder();
        titleViewHolder.titleView = (TextView) convertView.findViewById(R.id.main_title_tv);
        titleViewHolder.borderTopViwe = convertView.findViewById(R.id.main_border_top);
        return titleViewHolder;
    }

    @Override
    public void onClick(View view, Object o, int position, BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_rank;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        ViewHolder holder = new ViewHolder();
        holder.cover = (ImageView) convertView.findViewById(R.id.main_rank_img);
        holder.root = convertView;
        holder.title = (TextView) convertView.findViewById(R.id.main_item_rank_title);
        holder.firstTitle = (TextView) convertView
                .findViewById(R.id.main_first_title);
        holder.secondTitle = (TextView) convertView
                .findViewById(R.id.main_second_title);
        holder.border = convertView.findViewById(R.id.main_rank_divider);
        return holder;
    }

    @Override
    public void bindViewDatas(BaseViewHolder h, Object o, int position) {
        Rank model = (Rank) o;
        ViewHolder holder = (ViewHolder) h;

        ViewUtil.buildAlbumItemSpace(context, holder.root, position == 0,
                position + 1 == getCount()
                        || getItemViewType(position + 1) == TYPE_TITLE, 81);

        ImageManager.from(context).displayImage(mFragment, holder.cover,
                model.getCoverUrl(), com.ximalaya.ting.android.host.R.drawable.host_default_album);
        holder.title.setText(model.getRankTitle() == null ? "" : model
                .getRankTitle());

        List<RankItem> mFirstResults = model.getRankItemList();
        if (mFirstResults != null && mFirstResults.size() > 0) {
            holder.firstTitle.setText(getFriendlyRank(1, mFirstResults.get(0)
                    .getTitle()));
            if (model.getRankItemList().size() > 1) {
                holder.secondTitle.setText(getFriendlyRank(2, mFirstResults
                        .get(1).getTitle()));
            } else {
                holder.secondTitle.setText("");
            }
        } else {
            holder.firstTitle.setText("");
            holder.secondTitle.setText("");
        }
    }

    private String getFriendlyRank(int position, String title) {
        if (TextUtils.isEmpty(title)) {
            return "";
        }
        return position + " " + title;
    }

    protected static class ViewHolder extends BaseViewHolder {
        ImageView cover;
        TextView title;
        TextView firstTitle, secondTitle;
        View border, root;
    }

    class TitleViewHolder extends BaseViewHolder {
        TextView titleView;
        View borderTopViwe;
    }
}