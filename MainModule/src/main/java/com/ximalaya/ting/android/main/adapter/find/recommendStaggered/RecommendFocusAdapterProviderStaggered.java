package com.ximalaya.ting.android.main.adapter.find.recommendStaggered;

import static android.widget.RelativeLayout.CENTER_HORIZONTAL;
import static com.ximalaya.ting.android.host.view.BannerView.RECOMMEND_CATEGORY_ID;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Rect;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.ximalaya.ting.android.adsdk.base.util.AdPhoneData;
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.BaseHomePageTabFragment;
import com.ximalaya.ting.android.host.fragment.ad.SplashUnitAdUtil;
import com.ximalaya.ting.android.host.manager.OnlyUseMainProcessSharePreUtil;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.dazzling.DazzlingData;
import com.ximalaya.ting.android.host.manager.ad.dazzling.DazzlingScreenView;
import com.ximalaya.ting.android.host.manager.ad.dazzling.IDazzlingHandler;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.view.BannerVerticalView;
import com.ximalaya.ting.android.host.view.BannerView;
import com.ximalaya.ting.android.host.view.BaseBannerView;
import com.ximalaya.ting.android.host.view.ad.ShowReversePairImageView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adModule.view.BigVideoAdView;
import com.ximalaya.ting.android.main.adapter.find.recommend.IFragmentProvider;
import com.ximalaya.ting.android.main.adapter.find.util.BigScreenBoxHeightConstants;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataWithLifecircleStaggered;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.MulitViewTypeAdapter;
import com.ximalaya.ting.android.main.fragment.find.child.BigScreenAdManager;
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentAdUtil;
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentNew;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentAdNewUtil;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggered;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter;
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager;
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew;
import com.ximalaya.ting.android.main.model.rec.RecommendModuleItem;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.List;

/**
 * <AUTHOR> on 2016/11/29.
 * <AUTHOR>
 */

public class RecommendFocusAdapterProviderStaggered implements
        IMulitViewTypeViewAndDataWithLifecircleStaggered<RecommendFocusAdapterProviderStaggered.FocusHolder, ItemModel<List<BannerModel>>>, IFragmentProvider {
    private static final int DARK_MODE_COLOR = 0xff131313;
    private final RecommendFragmentStaggered baseFragment;
    private Context context;
    private WeakReference<BannerView> mBannerView;
    private WeakReference<RelativeLayout> mRelativeLay;
    private WeakReference<RelativeLayout> mBigVideoContainer;
    private BannerView.OnBannerItemClickListener mOnBannerItemClickListener;
    private RecommendFragmentStaggeredAdapter.IDataAction mRemover;
    private int mPosition;
    // 是否有焦点图数据
    private boolean mHasBannerModel;
    private boolean isShowingBigAd;
    private long lastBigAdShowTime;
    private ObjectAnimator mAnimator;
    private boolean mAnimationRunning;
    private RecommendFragmentAdNewUtil mAdUtil;
    private boolean isFragmentPause = false;
    private long fragmentResumeTime;
    private int mBgColor = BaseHomePageTabFragment.INVALID_COLOR;

    private IBigScreenStateChange mBigScreenStateChange;

    private boolean isAdBoxShowing = false;

    private View mConvertView;
    private ImageView mBgBottomImg;

    private boolean isShowDoubleBanner = false; // 是否展示双列竖版banner
    private BaseBannerView.IScrollableView mMixScrollableView;
    private  boolean isViewAdded;

    @Override
    public void onResume() {
        onFragmentResume();
    }

    @Override
    public void onPause() {
        onFragmentPause();
    }

    @Override
    public void onDestroyView() {
    }

    public interface IBigScreenStateChange {
        void onBigScreenShow();

        void onBigScreenHide(boolean hasBanner);

        void onBigScreenHideAnimatorOver();

        void onTouchImgAnimatorOver(ImageView mTouchImage);
    }

    public RecommendFocusAdapterProviderStaggered(RecommendFragmentStaggered baseFragment
            , BannerView.OnBannerItemClickListener onBannerItemClickListener,
                                                  RecommendFragmentAdNewUtil adUtil,
                                                  IBigScreenStateChange bigScreenStateChange,
                                                  BaseBannerView.IScrollableView mixScrollView,
                                                  RecommendFragmentStaggeredAdapter.IDataAction remover) {
        this.baseFragment = baseFragment;
        this.context = MainApplication.getMyApplicationContext();
        mOnBannerItemClickListener = onBannerItemClickListener;
        mBigScreenStateChange = bigScreenStateChange;
        mAdUtil = adUtil;
        mMixScrollableView = mixScrollView;
        mRemover = remover;
    }

    public void setBgColor(int bgColor) {
        if (isAdBoxShowing) {
            if (mBgBottomImg != null) {
                mBgBottomImg.setColorFilter(new PorterDuffColorFilter(bgColor != BaseHomePageTabFragment.INVALID_COLOR ? bgColor :
                        BaseFragmentActivity.sIsDarkMode ? DARK_MODE_COLOR : Color.WHITE, PorterDuff.Mode.SRC_IN));
            }
            if (mConvertView != null) {
                mConvertView.setBackgroundColor(Color.TRANSPARENT);
            }
        } else {
            if (mConvertView != null) {
                if (bgColor != BaseHomePageTabFragment.INVALID_COLOR &&
                        (!BaseFragmentActivity.sIsDarkMode || isPromotionOperationModuleShow())) {
                    mConvertView.setBackgroundColor(bgColor);
                } else {
                    mConvertView.setBackgroundColor(Color.TRANSPARENT);
                }
            }
        }
        this.mBgColor = bgColor;
    }

    @Override
    public void bindViewHolder(FocusHolder holder, int position, ItemModel<List<BannerModel>> t, View convertView) {
        if (holder == null || t == null) {
            return;
        }
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "onBindViewHolder position =" + position);
        isViewAdded = true;
        mConvertView = holder.convertView;
        mPosition = position;
        if (isAdBoxShowing) {
            holder.convertView.setBackgroundColor(Color.TRANSPARENT);
        } else {
            holder.convertView.setBackgroundColor(mBgColor != BaseHomePageTabFragment.INVALID_COLOR &&
                    (!BaseFragmentActivity.sIsDarkMode || isPromotionOperationModuleShow()) ? mBgColor : Color.TRANSPARENT);
        }

        mHasBannerModel = !ToolUtil.isEmptyCollects(t.getObject());
        // 如果有数据 需要设置这个tag为false，如果每次都有数据，那就复用^_^
        if(mHasBannerModel) {
            holder.convertView.setTag(MulitViewTypeAdapter.NO_USE_CACHE_FLAG, false);
        }

        View viewById = convertView.findViewById(R.id.main_big_ad_view);
        if(mHasBannerModel) {
            if(viewById != null && viewById.getVisibility() == View.VISIBLE) {
                holder.focusImageView.setVisibility(View.INVISIBLE);
            } else {
                holder.focusImageView.setVisibility(View.VISIBLE);
            }
        } else {
            holder.focusImageView.setVisibility(View.GONE);
        }

        isShowDoubleBanner = MmkvCommonUtil.getInstance(ToolUtil.getCtx())
                .getBoolean(PreferenceConstantsInOpenSdk.ITEM_BANNER_LAYOUT_MODE_TYPE_IS_DOUBLE, false);
        // 根据服务端下发的数据，判断是否展示了双列竖版banner， 如果展示了， 则不展示巨幕、联合霸屏、 炫屏等广告
        if (!isShowDoubleBanner) {
            BigScreenAdManager.getInstance().setBigScreenListener(mBigScreenStateChangeListener);

            HandlerManager.postOnUIThread(new Runnable() {
                @Override
                public void run() {
                    if (BigScreenAdManager.getInstance().mSplashUnitAd != null) {
                        BigScreenAdManager.getInstance().showUnitLoadingBigAd(BigScreenAdManager.getInstance().mSplashUnitAd);
                    } else {
                        BigScreenAdManager.getInstance().showBigScreenAd(new WeakReference<>(baseFragment));
                    }
                }
            });
        }

        holder.focusImageView.setData(t.getObject());
        if (fragmentResumeTime == 0) {
            fragmentResumeTime = System.currentTimeMillis();
        }
        if (!mHasBannerModel) {
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "checkHasUnitLoadingBigScreenAd onBindViewHolder");
            checkHasUnitLoadingBigScreenAd();
        }
    }

    private BigScreenAdManager.IBigScreenStateChangeListener mBigScreenStateChangeListener = new BigScreenAdManager.IBigScreenStateChangeListener() {
        @Override
        public boolean showBigVideoScreenAd(Advertis advertis, String videoPath) {
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "showBigVideoScreenAd");
            return addUnitLoadingOrBigScreen(advertis, videoPath, null);
        }

        @Override
        public boolean showBigImgScreenAd(Advertis advertis, String imgUrl) {
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "showBigImageScreenAd");
            return addUnitLoadingOrBigScreen(advertis, null, imgUrl);
        }

        @Override
        public void removeBigScreenAd(boolean hasAnimator) {
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "removeBigAd removeBigScreenAd");
            removeBigAd(hasAnimator, true);
        }
    };

    private final BaseBannerView.BannerDataChangeByMcRefreshListener mDataChangeRefreshListener = new BannerVerticalView.BannerDataChangeByMcRefreshListener() {
        @Override
        public void onMixFocusDataChanged(List<BannerModel> focusList) {
            if (focusList == null || focusList.isEmpty()) {
                return;
            }
            if (baseFragment != null) {
                List<RecommendItemNew> headerList = baseFragment.getHeaderData();
                if (headerList != null) {
                    try {
                        for (RecommendItemNew recommendItem : headerList) {
                            RecommendModuleItem moduleItem = (RecommendModuleItem) recommendItem.getItem();
                            if (moduleItem != null) {
                                if (RecommendModuleItem.RECOMMEND_TYPE_FOCUS.equals(moduleItem.getModuleType())) {
                                    moduleItem.getList().clear();
                                    moduleItem.getList().addAll(focusList);
                                    return;
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        Logger.e("---------msg_b", "  焦点图数据更新 ，error:  " + e);
                    }
                }
            }
        }
    };

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        BannerView focusImageView = new BannerView(baseFragment != null ? baseFragment.getActivity() : MainApplication.getTopActivity());
        if (mOnBannerItemClickListener != null) {
            focusImageView.setOnBannerItemClickListener(mOnBannerItemClickListener);
        }

        int[] customBannerWidthAndHeight;
        if (AdManager.isUerNewSmallBanner()) {
            customBannerWidthAndHeight = BannerView.getNewRecBannerWidthAndHeight(context);
        } else {
            customBannerWidthAndHeight = BannerView.getCustomBannerWidthAndHeight(context);
        }
        int topMargin = BannerView.getTopMargin(context);
        int bottomMargin = BaseUtil.dp2px(context, 3);
        if (BaseBannerView.sIsHasNewZoneStyleBanner) {
            bottomMargin = 0;
        }
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                customBannerWidthAndHeight[1] + topMargin + bottomMargin);
        focusImageView.setLayoutParams(lp);
        if (AdManager.isUerNewSmallBanner()) {
            lp.setMargins(BaseUtil.dp2px(context, 16), 0, BaseUtil.dp2px(context, 16), 0);
        }
        focusImageView.setDefultCornerRadius(BaseUtil.dp2px(context, 8));
        focusImageView.setPadding(0, topMargin, 0, bottomMargin);
        focusImageView.setTag(com.ximalaya.ting.android.host.R.id.host_recommend_focus_id_is_new_recommend, true);
        focusImageView.init(baseFragment, RECOMMEND_CATEGORY_ID);
        focusImageView.setId(R.id.main_recommend_focus_id);
        focusImageView.setVisibility(View.GONE);

        RelativeLayout relativeLayout = new RelativeLayout(context);
        relativeLayout.setLayoutParams(new StaggeredGridLayoutManager.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        View fakeView = new View(context); // 添加占位view，解决无法刷新问题
        RelativeLayout.LayoutParams paddingViewlp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, BaseUtil.dp2px(context, 1));
        fakeView.setLayoutParams(paddingViewlp);
        relativeLayout.addView(fakeView);
        relativeLayout.addView(focusImageView);

        RelativeLayout bigVideoContainer = new RelativeLayout(context);
        bigVideoContainer.setId(R.id.main_recommend_big_video_container_id);
        RelativeLayout.LayoutParams bigVideoContainerLp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,  ViewGroup.LayoutParams.WRAP_CONTENT);
        bigVideoContainer.setLayoutParams(bigVideoContainerLp);
        relativeLayout.addView(bigVideoContainer);

        focusImageView.setScrollableView(mMixScrollableView);
        focusImageView.setDataRefreshListener(mDataChangeRefreshListener); // 焦点图单独刷新时更新原生流数据
        focusImageView.setCurFragment(new BannerView.ICurFragment() {
            @Override
            public BaseFragment2 getCurFragment() {
                return baseFragment;
            }
        });
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "getView relativeLayout  =" + relativeLayout);
        return relativeLayout;
    }

    @Override
    public FocusHolder createViewHolder(View convertView) {
        FocusHolder focusHolder = new FocusHolder(convertView);
        mBannerView = new WeakReference<BannerView>(focusHolder.focusImageView);
        mRelativeLay = new WeakReference<RelativeLayout>((RelativeLayout) convertView);
        mBigVideoContainer = new WeakReference<>(focusHolder.bigVideoContainer);
        isViewAdded = true;
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "createViewHolder covertView =" + convertView);
        return focusHolder;
    }

    public void stopAutoSwapFocusImage() {
        if (mBannerView != null) {
            BannerView bannerView = mBannerView.get();
            if (bannerView != null) {
                bannerView.stopAutoSwapFocusImage();
            }
        }
    }

    public void startAutoSwapFocusImage() {
        checkHasUnitLoadingBigScreenAd();
        if (mBannerView != null) {
            BannerView bannerView = mBannerView.get();
            if (bannerView != null) {
                bannerView.startAutoSwapFocusImage();
            }
        }
    }

    public void release() {
        if (mBannerView != null) {
            BannerView bannerView = mBannerView.get();
            if (bannerView != null) {
                bannerView.removeAllListener();
                bannerView.stopAutoSwapFocusImage();
            }
        }
    }

    public void clearAutoRefreshRunnable() {
        if (mBannerView != null) {
            BannerView bannerView = mBannerView.get();
            if (bannerView != null) {
                bannerView.clearAutoRefreshRunnable();
            }
        }
    }

    public void setShowing(boolean isShowing) {
        if (mBannerView != null) {
            BannerView bannerView = mBannerView.get();
            if (bannerView != null) {
                bannerView.setShowing(isShowing);
            }
        }
    }

    public static class FocusHolder extends RecyclerView.ViewHolder {
        View convertView;
        BannerView focusImageView;
        RelativeLayout bigVideoContainer;

        private FocusHolder(View convertView) {
            super(convertView);
            this.convertView = convertView;
            focusImageView = convertView.findViewById(R.id.main_recommend_focus_id);
            bigVideoContainer = convertView.findViewById(R.id.main_recommend_big_video_container_id);
        }
    }

    private void checkHasUnitLoadingBigScreenAd() {

        // 根据服务端下发的数据，判断是否展示了双列竖版banner， 如果展示了， 则不展示巨幕、联合霸屏、 炫屏等广告
        if (isShowDoubleBanner) {
            Logger.i("-------msg", "RecommendFocusAdapterProviderStaggered ------- 222 show double banner  --------------------- isShowDoubleBanner = " + isShowDoubleBanner);
            return;
        }
        Advertis splashUnitAd = BigScreenAdManager.getInstance().mSplashUnitAd;
        Advertis bigScreenAd = BigScreenAdManager.getInstance().requestBigScreenAd;

        Logger.i("-------RecommendFocusAdapterProviderStaggered", "checkHasUnitLoadingBigScreenAd splashAd=" + splashUnitAd + " bigScreenAd =" + bigScreenAd);
        if (splashUnitAd != null) {
            if (new File(AdManager.getSavePath(splashUnitAd.getGiantVideoCover())).exists()) {
                addUnitLoadingOrBigScreen(splashUnitAd,
                        AdManager.getSavePath(splashUnitAd.getGiantVideoCover()), null);
            } else if (!TextUtils.isEmpty(splashUnitAd.getGiantCover())) {
                addUnitLoadingOrBigScreen(splashUnitAd, null, splashUnitAd.getGiantCover());
            }
        } else if(bigScreenAd != null) {
            if (bigScreenAd.getShowstyle() != Advertis.SHOW_TYPE_BIG_SCREEN_IMG && new File(AdManager.getSavePath(bigScreenAd.getVideoCover())).exists()) {
                addUnitLoadingOrBigScreen(bigScreenAd,
                        AdManager.getSavePath(bigScreenAd.getVideoCover()), null);
            } else if (!TextUtils.isEmpty(bigScreenAd.getImageUrl())) {
                addUnitLoadingOrBigScreen(bigScreenAd, null, bigScreenAd.getImageUrl());
            }
        }
    }

    private ViewUtil.IOnDialogShowStateChange mStateChange = new ViewUtil.IOnDialogShowStateChange() {
        @Override
        public boolean dialogShowStateChange(boolean hasShow) {
            if (!hasShow) {
                checkHasUnitLoadingBigScreenAd();
                return true;
            }
            return false;
        }
    };

    private Bitmap mDefaultCoverBitmap;

    // 显示联合霸屏的巨幕广告或者单纯的巨幕广告
    private boolean addUnitLoadingOrBigScreen(final Advertis advertis,
                                           @Nullable String videoFilePath,
                                           @Nullable String imageUrl) {
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "addUnitLoadingOrBigScreen 00 advertis =" + advertis);
        if (isShowDoubleBanner) {
            Logger.i("-------msg", "RecommendFocusAdapterProviderStaggered ------- 333 show double banner  --------------------- isShowDoubleBanner = " + isShowDoubleBanner);
            return false;
        }
        if(ConstantsOpenSdk.isDebug) {
            Logger.log("RecommendFocusAdapterProviderStaggered : addUnitLoadingOrBigScreen " + advertis + "   " + videoFilePath + "   " + imageUrl + "   " + Log.getStackTraceString(new Throwable()));
        }

        if (advertis == null) {
            removeBigAd(false, true);
            return false;
        }

        if (isFragmentPause) {
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "addUnitLoadingOrBigScreen return isFragmentPause advertis =" + advertis);
            return false;
        }

        if (baseFragment != null && mRelativeLay != null && mRelativeLay.get() != null) {
            if (!baseFragment.firstViewShowingAndIsTop(mRelativeLay.get())) {
                Logger.i("-------RecommendFocusAdapterProviderStaggered", "addUnitLoadingOrBigScreen firstViewShowingAndIsTop false");
                if (!advertis.isPreviewAd() && !RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
                    return false;
                } else {
                    if (mAdUtil != null) {
                        // 展示时滑动首页到顶部
                        mAdUtil.scrollToTop();
                    }
                }
            }
        }

        boolean hasDialog = false;

        Activity optActivity = MainApplication.getOptActivity();
        if (optActivity instanceof FragmentActivity) {
            hasDialog = ViewUtil.haveDialogIsShowing((FragmentActivity) optActivity);
        }

        Logger.i("-------RecommendFocusAdapterProviderStaggered", "addUnitLoadingOrBigScreen hasDialog =" + hasDialog + " advertis = " + advertis);
        if (mBannerView == null
                || mBannerView.get() == null
                || hasDialog) {

            if (hasDialog) {
                ViewUtil.addDialogShowStateChange(mStateChange);
            }

            if(advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_UNITED_SCREEN) {
                BigScreenAdManager.getInstance().mSplashUnitAd = advertis;
            } else {
                BigScreenAdManager.getInstance().requestBigScreenAd = advertis;
            }
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "addUnitLoadingOrBigScreen return mBannerView =" + mBannerView + " hasDialog=" + hasDialog);
            return false;
        }

        mDefaultCoverBitmap = null;

        boolean needDefaultCover = false;
        String defaultCover = null;
        if (advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_UNITED_SCREEN) {
            if (!TextUtils.isEmpty(advertis.getGiantCover())) {
                needDefaultCover = true;
                defaultCover = advertis.getGiantCover();
            }
        } else if (advertis.getShowstyle() != Advertis.SHOW_TYPE_BIG_SCREEN_IMG){
            if (!TextUtils.isEmpty(advertis.getVideoFirstFrame())) {
                needDefaultCover = true;
                defaultCover = advertis.getVideoFirstFrame();
            }
        }

        Logger.i("-------RecommendFocusAdapterProviderStaggered", "addUnitLoadingOrBigScreen 11");
        if (needDefaultCover) {
            ImageManager.from(ToolUtil.getCtx()).downloadBitmap(defaultCover,
                    new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    mDefaultCoverBitmap = bitmap;
                    loadBoxAndBigScreen(advertis, videoFilePath, imageUrl);
                }
            }, false);
        } else {
            loadBoxAndBigScreen(advertis, videoFilePath, imageUrl);
        }

        return true;
    }

    private void loadBoxAndBigScreen(Advertis advertis, @Nullable String videoFilePath,
                                        @Nullable String imageUrl) {
        if (isShowDoubleBanner) {
            Logger.i("-------msg", "RecommendFocusAdapterProviderStaggered ------- 4444 show double banner  --------------------- isShowDoubleBanner = " + isShowDoubleBanner);
            return;
        }
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "loadBoxAndBigScreen 00 advertis =" + advertis);
        final RelativeLayout bigVideoContainer = mBigVideoContainer.get();
        View viewById = bigVideoContainer.findViewById(R.id.main_big_ad_view);
        if(viewById != null) {
            // 说明现在正在有巨幕广告正在显示
            Logger.i("-------RecommendFocusAdapterProviderStaggered", " loadBoxAndBigScreen return for already showing advertis =" + advertis);
            return;
        }
        if(mAdUtil != null) {
            if (advertis.isBox() && !TextUtils.isEmpty(BigScreenAdManager.getBoxMuteCover(advertis))) {
                boolean isBigScreenNoAnimation = mHasBannerModel || (advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_UNITED_SCREEN && !advertis.isNeedDoUnitScreenAdAnimation());
                mAdUtil.showPageBox(advertis, isBigScreenNoAnimation, new RecommendFragmentAdNewUtil.IPageBoxShowSuccess() {
                    @Override
                    public void onPageBoxShowSuccess() {
                        Logger.i("-------RecommendFocusAdapterProviderStaggered", "loadBoxAndBigScreen 11 advertis =" + advertis);
                        isAdBoxShowing = true;

                        if (mConvertView != null) {
                            mConvertView.setBackgroundColor(Color.TRANSPARENT);
                        }

                        showBigScreenInner(advertis, videoFilePath, imageUrl);
                    }
                });
                return;
            } else {
                mAdUtil.removePageBox(false, false, null);
            }
        }
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "loadBoxAndBigScreen 22 advertis =" +advertis);
        showBigScreenInner(advertis, videoFilePath, imageUrl);
    }

    private void showBigScreenInner(Advertis advertis, @Nullable String videoFilePath,
                                       @Nullable String imageUrl) {

        Logger.i("-------RecommendFocusAdapterProviderStaggered", "showBigScreenInner 00 advertis =" + advertis);
        if (isShowDoubleBanner) {
            Logger.i("-------msg", "RecommendFocusAdapterProviderStaggered ------- 55555 show double banner  --------------------- isShowDoubleBanner = " + isShowDoubleBanner);
            return;
        }
        showBigAded = true;

        Logger.log("RecommendFragmentAdUtil : bigAd  3");

        ViewUtil.removeDialogShowStateChange(mStateChange);

        ViewUtil.setHasDialogShow(true);

        long lastShowTime = OnlyUseMainProcessSharePreUtil.getInstance(MainApplication.getMyApplicationContext()).getLong(PreferenceConstantsInHost.KEY_BIG_SCREEN_SHOW_SUCCESS_TIME);
        if (DateTimeUtil.isAnotherDay(lastShowTime)) {
            OnlyUseMainProcessSharePreUtil.getInstance(MainApplication.getMyApplicationContext()).saveInt(PreferenceConstantsInHost.KEY_BIG_SCREEN_SHOW_COUNT_ONE_DAY, 1);
        } else {
            int showCount = OnlyUseMainProcessSharePreUtil.
                    getInstance(MainApplication.getMyApplicationContext())
                    .getInt(PreferenceConstantsInHost.KEY_BIG_SCREEN_SHOW_COUNT_ONE_DAY, 0);
            OnlyUseMainProcessSharePreUtil.getInstance(MainApplication.getMyApplicationContext()).saveInt(PreferenceConstantsInHost.KEY_BIG_SCREEN_SHOW_COUNT_ONE_DAY, showCount + 1);
        }
        if (advertis.getShowstyle() != Advertis.IMG_SHOW_TYPE_UNITED_SCREEN && !advertis.isPreviewAd()) {
            OnlyUseMainProcessSharePreUtil.getInstance(MainApplication.getMyApplicationContext())
                    .saveLong(PreferenceConstantsInHost.KEY_BIG_SCREEN_SHOW_SUCCESS_TIME, System.currentTimeMillis());
        }

        if (mRelativeLay != null && mRelativeLay.get() != null) {
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "showBigScreenInner 11 advertis =" + advertis);
            if (mBannerView != null && mBannerView.get() != null) {
                mBannerView.get().setTag(com.ximalaya.ting.android.host.R.id.main_banner_no_check_visable, true);
                if (mHasBannerModel) {
                    mBannerView.get().setVisibility(View.INVISIBLE);
                }
            }
            final RelativeLayout bigVideoContainer = mBigVideoContainer.get();
            bigVideoContainer.setClipChildren(false);
            View viewById = bigVideoContainer.findViewById(R.id.main_big_ad_view);
            if(viewById != null) {
                // 说明现在正在有巨幕广告正在显示
                Logger.i("-------RecommendFocusAdapterProviderStaggered", "showBigScreenInner 22 advertis =" + advertis);
                return;
            }
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "showBigScreenInner 33 advertis =" + advertis);

            isShowingBigAd = true;
            lastBigAdShowTime = System.currentTimeMillis();
            BigScreenAdManager.getInstance().mSplashUnitAd = null;
            BigScreenAdManager.getInstance().requestBigScreenAd = null;

            // 视频控件
            final BigVideoAdView videoView = createVideoView(advertis);
            bigVideoContainer.addView(videoView);
            if (mHasBannerModel || (advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_UNITED_SCREEN && !advertis.isNeedDoUnitScreenAdAnimation())) {
                Logger.i("-------RecommendFocusAdapterProviderStaggered", "showBigScreenInner direct advertis =" + advertis);
                createBottomBoxView(advertis, bigVideoContainer, false);
                videoView.setVisibility(View.VISIBLE);
                showDazzling(advertis, advertis.getShowstyle() != Advertis.IMG_SHOW_TYPE_UNITED_SCREEN);
            } else {
                createBottomBoxView(advertis, bigVideoContainer, true);
                videoView.setVisibility(View.GONE);
                Logger.i("-------RecommendFocusAdapterProviderStaggered", "showBigScreenInner doAnimation advertis =" + advertis);
                showAnimatorForNoBanner(videoView, advertis, bigVideoContainer);
            }

            viewById = videoView;

            if (viewById instanceof BigVideoAdView) {
                BigVideoAdView mVideoView = (BigVideoAdView) viewById;

                if (!TextUtils.isEmpty(videoFilePath) && new File(videoFilePath).exists()) {
                    mVideoView.setAdvertis(advertis, Advertis.SHOW_TYPE_VIDEO);
                    mVideoView.setMediaPlayer(videoFilePath, mDefaultCoverBitmap);
                } else {
                    BannerView.settingCanScroll = false;
                    mVideoView.setAdvertis(advertis, Advertis.SHOW_TYPE_STATIC);
                    ImageManager.from(MainApplication.getMyApplicationContext()).downloadBitmap(imageUrl, new ImageManager.DisplayCallback() {
                        @Override
                        public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                            if(bitmap != null) {
                                mVideoView.setAdImg(bitmap);
                            }
                        }
                    }, false);
                }
            }
        }

        if (mBigScreenStateChange != null) {
            mBigScreenStateChange.onBigScreenShow();
        }
    }

    private void createBottomBoxView(Advertis advertis, RelativeLayout relativeLayout, boolean isNeedHideTemp) {
        if (isShowDoubleBanner) {
            Logger.i("-------msg", "RecommendFocusAdapterProviderStaggered ------- 66666 show double banner  --------------------- isShowDoubleBanner = " + isShowDoubleBanner);
            return;
        }
        View boxView = relativeLayout.findViewById(R.id.main_unit_pack_box);
        if (boxView != null) {
            relativeLayout.removeView(boxView);
        }

        View bgBottomView = relativeLayout.findViewById(R.id.main_unit_pack_box_bottom);
        if (bgBottomView != null) {
            relativeLayout.removeView(bgBottomView);
        }

        if(advertis.isBox() && !TextUtils.isEmpty(BigScreenAdManager.getBoxMuteCover(advertis))) {
            ImageView mBottomBox = new ImageView(context);
            mBottomBox.setScaleType(ImageView.ScaleType.FIT_XY);
            mBottomBox.setId(R.id.main_unit_pack_box);
            RelativeLayout.LayoutParams relateParams;
            if (RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
                relateParams = new RelativeLayout.LayoutParams(BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_WIDTH_NEW), BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_HEIGHT_NEW));
                relateParams.topMargin = BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_TOP_MARGIN_NEW);
                relateParams.bottomMargin = BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_BOTTOM_MARGIN_NEW);
            } else {
                relateParams = new RelativeLayout.LayoutParams(BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_WIDTH), BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_HEIGHT));
                relateParams.topMargin = BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_TOP_MARGIN);
                relateParams.bottomMargin = BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_BOTTOM_MARGIN);
            }
            relateParams.addRule(RelativeLayout.BELOW, R.id.main_big_ad_view);
            relateParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
            mBottomBox.setLayoutParams(relateParams);
            relativeLayout.addView(mBottomBox);
            ImageManager.from(context).displayImage(mBottomBox, advertis.getBoxCover(), -1);

            ImageView bgBottomImg = new ImageView(context);
            bgBottomImg.setId(R.id.main_unit_pack_box_bottom);
            bgBottomImg.setScaleType(ImageView.ScaleType.FIT_XY);
            if (RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
                bgBottomImg.setImageResource(R.drawable.main_box_bg_bottom_line_new);
                bgBottomImg.setColorFilter(new PorterDuffColorFilter(mBgColor != BaseHomePageTabFragment.INVALID_COLOR ? mBgColor :
                        BaseFragmentActivity.sIsDarkMode ? DARK_MODE_COLOR : context.getResources().getColor(R.color.main_color_f8f8f8_000000), PorterDuff.Mode.SRC_IN));
            } else {
                bgBottomImg.setImageResource(R.drawable.main_box_bg_bottom_line);
                bgBottomImg.setColorFilter(new PorterDuffColorFilter(mBgColor != BaseHomePageTabFragment.INVALID_COLOR ? mBgColor :
                        BaseFragmentActivity.sIsDarkMode ? DARK_MODE_COLOR : Color.WHITE, PorterDuff.Mode.SRC_IN));
            }
            mBgBottomImg = bgBottomImg;
            RelativeLayout.LayoutParams bgBottomRelateParams =
                    new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            bgBottomRelateParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
            bgBottomImg.setLayoutParams(bgBottomRelateParams);
            relativeLayout.addView(bgBottomImg);

            if (isNeedHideTemp) {
                mBottomBox.setAlpha(0f);
                bgBottomImg.setAlpha(0f);
            } else {
                mBottomBox.setAlpha(1.0f);
                bgBottomImg.setAlpha(1.0f);
            }
            relativeLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onAdClick(advertis, Advertis.SHOW_TYPE_STATIC);
                }
            });

            if (mBannerView != null && mBannerView.get() != null) {
                mBannerView.get().setTag(com.ximalaya.ting.android.host.R.id.main_banner_no_check_visable, null);
            }
        }
    }

    @NonNull
    private BigVideoAdView createVideoView(Advertis advertis) {
        final BigVideoAdView videoView = new BigVideoAdView(context);
        videoView.setId(R.id.main_big_ad_view);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            videoView.setTranslationZ(BaseUtil.dp2px(context, 10));
            videoView.setOutlineProvider(null);
        }
        int[] customBannerWidthAndHeight = BannerView.getCustomBannerWidthAndHeight(context);
        int adWidth = customBannerWidthAndHeight[0];
        int adHeight = (int) (adWidth * 9 * 1.0f / 16); // 宽高比 = 16 ： 9
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(adWidth, adHeight);
        params.addRule(RelativeLayout.CENTER_HORIZONTAL);
        if (mHasBannerModel) {
            params.setMargins(0, BannerView.getTopMargin(context), 0, BannerView.getTopMargin(context));
        } else {
            params.setMargins(0, BaseUtil.dp2px(context, 16), 0, BaseUtil.dp2px(context, 8));
        }
        videoView.setLayoutParams(params);
        videoView.setVideoViewSize(adWidth, adHeight);

        videoView.setOnCompletionListener(new IHandleOk() {
            @Override
            public void onReady() {
                removeBigAd(true, true);
            }
        });

        videoView.setOnPlayStartListener(new IHandleOk() {
            @Override
            public void onReady() {
                // 开始播放
                BannerView.settingCanScroll = false;
//                videoView.setVisibility(View.VISIBLE);
            }
        });

        videoView.setOnPauseListener(new IHandleOk() {
            @Override
            public void onReady() {
                // 播放暂停

            }
        });

        videoView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onAdClick(advertis, videoView.getShowType() == Advertis.SHOW_TYPE_VIDEO ? Advertis.SHOW_TYPE_VIDEO : Advertis.SHOW_TYPE_STATIC);
            }
        });

        videoView.setCloseListener(new IHandleOk() {
            @Override
            public void onReady() {
                videoView.setCloseListener(null);
                removeBigAd(true, true);
            }
        });

        videoView.setDestoryListener(new IHandleOk() {
            @Override
            public void onReady() {
                if (advertis.isPreviewAd()) {
                    return;
                }
                Logger.i("-------RecommendFocusAdapterProviderStaggered", "Destroy  timeInterval = "+ (System.currentTimeMillis() - fragmentResumeTime));
                if (System.currentTimeMillis() - fragmentResumeTime < 800) {
                    Logger.i("-------RecommendFocusAdapterProviderStaggered", "Destroy return");
                    return;
                }
                Logger.i("-------RecommendFocusAdapterProviderStaggered", "Destroy  showtimeInterval = "+ (System.currentTimeMillis() - lastBigAdShowTime));
                if (System.currentTimeMillis() - lastBigAdShowTime < 800) {
                    Logger.i("-------RecommendFocusAdapterProviderStaggered", "Destroy return");
                    return;
                }
                Logger.i("-------RecommendFocusAdapterProviderStaggered", "removeBigAd for destroy");
                videoView.setDestoryListener(null);
                removeBigAd(false, true);
            }
        });
        return videoView;
    }

    private Runnable mShowDazzlingRunnable;
    private Runnable mHideDazzlingRunnable;
    // 显示炫屏
    // isOnlyBigScreen 是否是单独巨幕
    private void showDazzling(Advertis advertis, boolean isOnlyBigScreen) {
        if (advertis == null) {
            return;
        }
        if (TextUtils.isEmpty(advertis.getDazzleType())
                || advertis.getDazzleType().equals(IAdConstants.IDazzlingAnimationType.NO)) {
            return;
        }
        if (IAdConstants.IDazzlingAnimationType.BIG_SCREEN.equals(advertis.getDazzleType())) {
            if (RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
                // 旧首页不支持超凡大屏动效
                showBigScreenDazzle(advertis);
            }
            return;
        }
        int showCount;
        if(isOnlyBigScreen) {
            showCount = BigScreenAdManager.getInstance().getShowCount(advertis);

            // 超过了一天最大的此样式的展示次数
            if(BigScreenAdManager.getInstance().isOverMaxShowCount(showCount, advertis)) {
                Logger.i("-------RecommendFocusAdapterProviderStaggered", "showDazzling max count return");
                return;
            }
        } else {
            showCount = SplashUnitAdUtil.getInstance().getShowCount(advertis);

            // 超过了一天最大的此样式的展示次数
            if(SplashUnitAdUtil.getInstance().isOverMaxShowCount(showCount, advertis)) {
                Logger.i("-------RecommendFocusAdapterProviderStaggered", "showDazzling max count return");
                return;
            }
        }

        DazzlingScreenView dazzlingScreenView = new DazzlingScreenView(context);
        mShowDazzlingRunnable = new Runnable() {
            @Override
            public void run() {
                if(mRelativeLay != null && mRelativeLay.get() != null) {
                    View videoView = mRelativeLay.get().findViewById(R.id.main_big_ad_view);
                    if(videoView == null) {
                        return;
                    }

                    DazzlingData dazzlingData;
                    if(isOnlyBigScreen) {
                        dazzlingData =
                                BigScreenAdManager.getInstance().createDazzlingData(advertis);

                        if(dazzlingData == null) {
                            return;
                        }

                        BigScreenAdManager.getInstance().onDazzlingShow(advertis, showCount);
                    } else {
                         dazzlingData =
                                SplashUnitAdUtil.getInstance().createDazzlingData(advertis);

                        if(dazzlingData == null) {
                            return;
                        }

                        SplashUnitAdUtil.getInstance().onDazzlingShow(advertis, showCount);
                    }

                    Rect rect = new Rect();
                    videoView.getLocalVisibleRect(rect);

                    if (rect.bottom < BaseUtil.dp2px(ToolUtil.getCtx(), 20) || rect.height() <= BaseUtil.dp2px(ToolUtil.getCtx(), 20)) {
                        videoView.post(new Runnable() {
                            @Override
                            public void run() {
                                videoView.getLocalVisibleRect(rect);
                                showDazzlingView(dazzlingData, rect, dazzlingScreenView, advertis);
                            }
                        });
                    } else {
                        showDazzlingView(dazzlingData, rect, dazzlingScreenView, advertis);
                    }
                }
            }
        };

        dazzlingScreenView.setId(R.id.main_dazzling_view_id);
        if (baseFragment != null && baseFragment.getView() instanceof ViewGroup) {
            ((ViewGroup) baseFragment.getView()).addView(dazzlingScreenView,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
        }
        if (mAdUtil != null) {
            mAdUtil.setListenerScrollView(dazzlingScreenView);
        }

        int delayTime = Math.max((int) (advertis.getDazzleStartTime() * 1000), 300);
        HandlerManager.postOnUIThreadDelay(mShowDazzlingRunnable, delayTime);
    }

    private void showDazzlingView(DazzlingData dazzlingData, Rect rect, DazzlingScreenView dazzlingScreenView, Advertis advertis) {
        int topMarge = mHasBannerModel ? BannerView.getTopMargin(ToolUtil.getCtx()) : BaseUtil.dp2px(context, 16);
        dazzlingData.updateShowViewBottomAndReferViewHeight(rect.bottom + topMarge, rect.height());

        dazzlingScreenView.setData(dazzlingData, new IDazzlingHandler() {
            @Override
            public boolean hasBox() {
                return advertis.isBox();
            }

            @Override
            public void onDazzlingClick(View view) {
                onAdClick(advertis, Advertis.SHOW_TYPE_STATIC);
            }

            @Override
            public void animationOver(Bitmap bitmap) {
                RelativeLayout relativeLayout = mRelativeLay.get();
                if(relativeLayout != null) {
                    View viewById = relativeLayout.findViewById(R.id.main_big_ad_view);

                    if(viewById instanceof BigVideoAdView) {

                        int smallIconAnimatorType = BigVideoAdView.ICON_ANIMATION_NORMAL;
                        if(TextUtils.equals(advertis.getDazzleType(), IAdConstants.IDazzlingAnimationType.RIGHT)) {
                            smallIconAnimatorType = BigVideoAdView.ICON_ANIMATION_SHAKE;
                        }

                        ((BigVideoAdView) viewById).showActionImg(bitmap, smallIconAnimatorType);
                    }
                }

                if(baseFragment != null && baseFragment.getView() instanceof ViewGroup) {
                    ((ViewGroup) baseFragment.getView()).removeView(dazzlingScreenView);
                }
            }
        });
    }

    // 展示超凡大屏动效
    private void showBigScreenDazzle(Advertis advertis) {
        mShowDazzlingRunnable = new Runnable() {
            @Override
            public void run() {
                if(mRelativeLay != null && mRelativeLay.get() != null) {
                    View videoView = mRelativeLay.get().findViewById(R.id.main_big_ad_view);
                    if(videoView == null) {
                        return;
                    }
                    if (mAdUtil != null && mAdUtil.getScrollY() != 0) {
                        // 有滑动时，不展示动效
                        return;
                    }
                    doDazzleShowAnimation((BigVideoAdView) videoView, advertis);
                }
            }
        };

        int delayTime = 1000;
        if (advertis.getDazzleStartTime() > 0) {
            delayTime = (int) advertis.getDazzleStartTime() * 1000;
        }
        HandlerManager.postOnUIThreadDelay(mShowDazzlingRunnable, delayTime);
    }

    private void doDazzleShowAnimation(BigVideoAdView videoView, Advertis advertis) {
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "doDazzleShowAnimation mAnimationRunning =" + mAnimationRunning);
        int allAnimationTime;
        if (advertis.getDazzleEndTime() > 0 && advertis.getDazzleEndTime() > advertis.getDazzleStartTime()) {
            allAnimationTime = (int) ((advertis.getDazzleEndTime() - advertis.getDazzleStartTime()) * 1000);
        } else {
            // 动效默认展示3秒
            allAnimationTime = 3000;
        }
        mHideDazzlingRunnable = new Runnable() {
            @Override
            public void run() {
                if(mRelativeLay != null && mRelativeLay.get() != null) {
                    View videoView = mRelativeLay.get().findViewById(R.id.main_big_ad_view);
                    if(videoView == null) {
                        return;
                    }
                    doDazzleHideAnimation((BigVideoAdView) videoView, advertis);
                }
            }
        };
        if (!mAnimationRunning) {
            int[] customBannerWidthAndHeight = BannerView.getCustomBannerWidthAndHeight(context);
            int adWidth = customBannerWidthAndHeight[0];
            int originVideoWidth = adWidth;
            int originVideoHeight = (int) (adWidth * 9 * 1.0f / 16);

            int bigVideoWidth = adWidth;
            int bigVideoHeight = getBigVideoHeight();

            ValueAnimator valueAnimator = ValueAnimator.ofFloat(0, 1);
            valueAnimator.setDuration(280);
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    if (!showBigAded) {
                        // 在动效执行过程中巨幕已经关闭
                        return;
                    }
                    ViewGroup.LayoutParams layoutParams = videoView.getLayoutParams();
                    float value = (float) animation.getAnimatedValue();
                    layoutParams.width = originVideoWidth + (int) ((bigVideoWidth - originVideoWidth) * value);
                    int destHeight = originVideoHeight + (int) ((bigVideoHeight - originVideoHeight) * value);
                    layoutParams.height = destHeight;
                    videoView.setLayoutParams(layoutParams);

                    // 视频等比缩放直到高度为bigVideoHeight
                    videoView.setVideoViewSize((int) (originVideoWidth * destHeight / originVideoHeight), destHeight);
                }
            });

            valueAnimator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    mAnimationRunning = true;
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    mAnimationRunning = false;
                    HandlerManager.postOnUIThreadDelay(mHideDazzlingRunnable, allAnimationTime - 280 * 2);
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    mAnimationRunning = false;
                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            valueAnimator.start();
        }
    }

    private void doDazzleHideAnimation(BigVideoAdView videoView, Advertis advertis) {
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "doDazzleHideAnimation mAnimationRunning =" + mAnimationRunning);
        if (!mAnimationRunning) {
            int[] customBannerWidthAndHeight = BannerView.getCustomBannerWidthAndHeight(context);
            int adWidth = customBannerWidthAndHeight[0];
            int originVideoWidth = adWidth;
            int originVideoHeight = (int) (adWidth * 9 * 1.0f / 16);

            int bigVideoWidth = adWidth;
            int bigVideoHeight = getBigVideoHeight();

            ValueAnimator valueAnimator = ValueAnimator.ofFloat(1, 0);
            valueAnimator.setDuration(280);
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    if (!showBigAded) {
                        // 在动效执行过程中巨幕已经关闭
                        return;
                    }
                    ViewGroup.LayoutParams layoutParams = videoView.getLayoutParams();
                    float value = (float) animation.getAnimatedValue();
                    int destHeight = originVideoHeight + (int) ((bigVideoHeight - originVideoHeight) * value);
                    layoutParams.width = originVideoWidth + (int) ((bigVideoWidth - originVideoWidth) * value);
                    layoutParams.height = destHeight;
                    videoView.setLayoutParams(layoutParams);

                    // 视频等比缩放直到高度为bigVideoHeight
                    videoView.setVideoViewSize((int) (originVideoWidth * destHeight / originVideoHeight), destHeight);
                }
            });
            valueAnimator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    mAnimationRunning = true;
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    mAnimationRunning = false;
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    mAnimationRunning = false;
                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            valueAnimator.start();
        }
    }

    private int getBigVideoHeight() {
        int screenHeight = AdPhoneData.getScreenHeight(context);
        int screenWidth = AdPhoneData.getScreenWidth(context);
        float ratio = screenHeight * 1.0f / screenWidth;
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "getBigVideoHeight height =" + screenHeight + " width =" + screenWidth + " ratio =" + ratio);
        if (ratio >= 2.0) {
            return BaseUtil.dp2px(context, 431);
//            return (int) (BaseUtil.dp2px(context, 431) * (1.0f * screenWidth / BaseUtil.dp2px(context, 375)));
        } else {
            return BaseUtil.dp2px(context, 291);
//            return (int) (BaseUtil.dp2px(context, 291) * (1.0f * screenWidth / BaseUtil.dp2px(context, 375)));
        }
    }

    private boolean isAdClicked;
    private void onAdClick(Advertis advertis, int showTypeStatic) {
        isAdClicked = true;
        AdReportModel.Builder builder1 =
                AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                        advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_UNITED_SCREEN ?
                                AppConstants.AD_POSITION_NAME_LOADING :
                                AppConstants.AD_POSITION_NAME_GIANT_SCREEN)
                        .showType(showTypeStatic)
                        .showStyle(advertis.getShowstyle() + "")
                        .playMode(advertis.getPlayMode());
        if (advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_UNITED_SCREEN) {
            builder1.loadingGiantStatus(1);
        }
        AdManager.handlerAdClick(context, advertis,
                builder1.build());

        if (advertis.getShowstyle() != Advertis.IMG_SHOW_TYPE_UNITED_SCREEN) {
            // 新首页-巨幕  点击事件
            new XMTraceApi.Trace()
                    .click(40830)
                    .put("url", advertis.getRealLink())
                    .put("contentType", AdManager.converContentType(advertis.getItingType()))
                    .put("contentId", advertis.getAdid() + "")
                    .put("currPage", "newHomePage")
                    .createTrace();
        }

//        HandlerManager.postOnUIThreadDelay(new Runnable() {
//            @Override
//            public void run() {
//                removeBigAd(false, false);
//            }
//        }, 300);
    }

    private boolean showBigAded;

    public void removeBigAd(boolean hasAnimator, boolean isNeedRemoveItem) {
        if(!showBigAded) {
            return;
        }
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "removeBigAd");
        showBigAded = false;
        if (ConstantsOpenSdk.isDebug) {
            Logger.log("RecommendFocusAdapterProviderStaggered : removeBigAd " + Log.getStackTraceString(new Throwable()));
        }

        HandlerManager.removeCallbacks(mShowDazzlingRunnable);
        HandlerManager.removeCallbacks(mHideDazzlingRunnable);
        if(mBannerView != null && mBannerView.get() != null) {
            mBannerView.get().setTag(com.ximalaya.ting.android.host.R.id.main_banner_no_check_visable, null);
        }
        SplashUnitAdUtil.getInstance().removeAllBitmap();
        BigScreenAdManager.getInstance().removeAllBitmap();
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                ViewUtil.setHasDialogShow(false);
                if(baseFragment != null && baseFragment.getView() instanceof ViewGroup) {
                    View dazzlingView = baseFragment.getView().findViewById(R.id.main_dazzling_view_id);
                    if(dazzlingView instanceof DazzlingScreenView) {
                        ((DazzlingScreenView) dazzlingView).cancelAnimator();
                    }
                    ((ViewGroup) baseFragment.getView()).removeView(dazzlingView);
                    mAdUtil.removeListenerView();
                }

                boolean hasJointTouch = false;
                View packBoxBottom = null;
                if (mRelativeLay != null && mRelativeLay.get() != null) {
                    RelativeLayout bigVideoContainer = mBigVideoContainer.get();
                    BigVideoAdView videoView = bigVideoContainer.findViewById(R.id.main_big_ad_view);
                    View unitPackBox = bigVideoContainer.findViewById(R.id.main_unit_pack_box);
                    packBoxBottom = bigVideoContainer.findViewById(R.id.main_unit_pack_box_bottom);
                    boolean isBoxShowing = unitPackBox != null && unitPackBox.getVisibility() == View.VISIBLE;

                    // 隐藏包框部分
                    hideBox(packBoxBottom, unitPackBox, hasAnimator);
                    BigScreenAdManager.getInstance().setBigScreenListener(null);

                    if (videoView instanceof BigVideoAdView) {
                        ((BigVideoAdView) videoView).recordVideoTime(isAdClicked);
                        if ((SplashUnitAdUtil.getInstance().hasSplashUnitChainTouch() || BigScreenAdManager.getInstance().hasUnitChainTouch())
                                && baseFragment != null && baseFragment.findViewById(R.id.main_home_new_user_gift_floating) == null) {
                            // 联合touch的动画
                            if(hasAnimator) {
                                hideHasTouchAnimator(bigVideoContainer, videoView);
                            } else {
                                if (mBigScreenStateChange != null) {
                                    mBigScreenStateChange.onBigScreenHideAnimatorOver();
                                }

                                if (mBigScreenStateChange != null) {
                                    mBigScreenStateChange.onTouchImgAnimatorOver(null);
                                }

                                mAnimationRunning = false;
                                ((BigVideoAdView) videoView).release();
                                bigVideoContainer.removeView(videoView);
                                isShowingBigAd = false;
                                removeViewWithBannerCheck();
                            }
                            hasJointTouch = true;
                        } else {
                            if(hasAnimator) {
                                // 普通的隐藏的动画
                                if (mHasBannerModel) {
                                    hideNormalAnimator(bigVideoContainer, videoView);
                                } else {
                                    hideAnimatorForNoBanner(bigVideoContainer, videoView);
                                }
                            } else {
                                if (mBigScreenStateChange != null) {
                                    mBigScreenStateChange.onBigScreenHideAnimatorOver();
                                }

                                mAnimationRunning = false;
                                ((BigVideoAdView) videoView).release();
                                bigVideoContainer.removeView(videoView);
                                isShowingBigAd = false;
                                if (isNeedRemoveItem) {
                                    removeViewWithBannerCheck();
                                }
                            }
                        }

                        if (!mHasBannerModel && mAdUtil != null) {
                            // 更新recyclerView的滑动距离
                            int[] customBannerWidthAndHeight = BannerView.getCustomBannerWidthAndHeight(context);
                            int adHeight = (int) (customBannerWidthAndHeight[0] * 9 * 1.0f / 16);
                            int totalHeight = adHeight + BaseUtil.dp2px(context, 16) + BaseUtil.dp2px(context, 8);
                            if (isBoxShowing) {
                                totalHeight += BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_HEIGHT_NEW);
                            }
                            mAdUtil.updateScrollYWhenBigAdHide(totalHeight);
                        }
                        BannerView.sendColorChangeBroad(Color.TRANSPARENT,
                                ToolUtil.getCtx(),
                                RecommendFragmentNew.class.getSimpleName());
                    }

                    if (mBannerView != null && mBannerView.get() != null) {
                        BannerView bannerView = mBannerView.get();
                        if (mHasBannerModel) {
                            bannerView.setVisibility(View.VISIBLE);
                        } else {
                            bannerView.setVisibility(View.GONE);
                        }
                    }
                }

                if(mAdUtil != null) {
                    mAdUtil.removePageBox(hasAnimator, hasJointTouch, packBoxBottom);
                }
                BannerView.settingCanScroll = true;
            }
        });

        isAdBoxShowing = false;

        if (mConvertView != null) {
            mConvertView.setBackgroundColor(mBgColor != BaseHomePageTabFragment.INVALID_COLOR &&
                    (!BaseFragmentActivity.sIsDarkMode || isPromotionOperationModuleShow()) ? mBgColor : Color.TRANSPARENT);
        }

        if (mBigScreenStateChange != null) {
            mBigScreenStateChange.onBigScreenHide(mHasBannerModel);
        }
    }

    private void removeViewWithBannerCheck() {
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "removeViewWithBannerCheck isViewAdded ="+ isViewAdded);
        if (!mHasBannerModel && mRemover != null && isViewAdded) {
            isViewAdded = false;
            Logger.i("-------RecommendFocusAdapterProviderStaggered", "removeViewWithBannerCheck remove");
            mRemover.remove(mPosition, false);
        }
    }

    private void hideBox(View packBoxBottom, View unitPackBox, boolean hasAnimator) {
        ShowReversePairImageView showReversePairImageView = null;

        if (packBoxBottom != null) {
            packBoxBottom.setVisibility(View.GONE);
        }
        // 包框部分
        if(!hasAnimator) {
            if(unitPackBox != null) {
                unitPackBox.setVisibility(View.GONE);
            }
        } else {
            if(baseFragment != null && baseFragment.getActivity() != null) {
                showReversePairImageView = baseFragment.getActivity().findViewById(com.ximalaya.ting.android.main.R.id.main_unit_package_box);
            }
            if(unitPackBox != null) {
                ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(unitPackBox, View.ALPHA, 1.0f, 0);
                alphaAnimator.setDuration(300);

                int bottomMargin = 0;
                ViewGroup.LayoutParams layoutParams = unitPackBox.getLayoutParams();
                if(layoutParams instanceof ViewGroup.MarginLayoutParams) {
                    bottomMargin = ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin;
                }

                int finalBottomMargin = bottomMargin;
                int unitBoxHeight;
                if (RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
                    unitBoxHeight = BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_HEIGHT_NEW);
                } else {
                    unitBoxHeight = BaseUtil.dp2px(context, BigScreenBoxHeightConstants.UNIT_PACK_BOX_IMAGE_HEIGHT);
                }
                ShowReversePairImageView finalShowReversePairImageView =
                        showReversePairImageView;
                alphaAnimator.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        unitPackBox.setVisibility(View.GONE);
                    }
                });
                alphaAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    boolean isSetGone = false;
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        ViewGroup.LayoutParams layoutParams = unitPackBox.getLayoutParams();
                        if(layoutParams instanceof ViewGroup.MarginLayoutParams) {
                            float boxHeight =
                                    (unitBoxHeight + finalBottomMargin) * animation.getAnimatedFraction();
                            ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin = (int) (finalBottomMargin - boxHeight);
                            unitPackBox.setLayoutParams(layoutParams);

                            if(finalShowReversePairImageView != null) {
                                finalShowReversePairImageView.setBoxHeight(
                                        (int) ((unitBoxHeight + finalBottomMargin) * (1 - animation.getAnimatedFraction())));
                            }
                        }

                        if (!isSetGone && animation.getAnimatedFraction() > 0.3f) {
                            unitPackBox.setVisibility(View.INVISIBLE);
                            if(layoutParams instanceof ViewGroup.MarginLayoutParams) {
                                ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin = finalBottomMargin;
                                unitPackBox.setLayoutParams(layoutParams);
                            }

                            isSetGone = true;
                        }
                    }
                });
                alphaAnimator.start();
            }
        }
    }

    private void hideHasTouchAnimator(RelativeLayout relativeLayout, BigVideoAdView videoView) {
        if (!mAnimationRunning) {
            videoView.beginChange(false, new IHandleOk() {
                @Override
                public void onReady() {
                    if (mBigScreenStateChange != null) {
                        mBigScreenStateChange.onBigScreenHideAnimatorOver();
                    }

                    if(baseFragment != null && baseFragment.getView() instanceof ViewGroup) {
                        ImageView mTouchImage = new ImageView(ToolUtil.getCtx());
                        int touchWidth = baseFragment.getResourcesSafe().getDimensionPixelOffset(R.dimen.main_recommend_touch_width);
                        mTouchImage.setVisibility(View.INVISIBLE);
                        mTouchImage.setPivotY(touchWidth * 1.0f / 2);
                        mTouchImage.setPivotX(touchWidth * 1.0f / 2);
                        ((ViewGroup) baseFragment.getView()).addView(mTouchImage, touchWidth, touchWidth);

                        Bitmap bitmap = SplashUnitAdUtil.getInstance().getTouchCoverBitmap();
                        if (bitmap == null) {
                            bitmap = BigScreenAdManager.getInstance().getTouchCoverBitmap();
                        }

                        mTouchImage.setImageBitmap(bitmap);

                        ViewGroup.LayoutParams layoutParams = mTouchImage.getLayoutParams();
                        int topMarge = mHasBannerModel ? BannerView.getTopMargin(ToolUtil.getCtx()) : BaseUtil.dp2px(context, 16);
                        int centerPoint = topMarge + videoView.getHeight() / 2;
                        int topMargin = (int) (centerPoint - touchWidth * 1.0f / 2);
                        if(layoutParams instanceof RelativeLayout.LayoutParams) {
                            ((RelativeLayout.LayoutParams) layoutParams).topMargin = topMargin;
                            ((RelativeLayout.LayoutParams) layoutParams).addRule(CENTER_HORIZONTAL);
                            mTouchImage.setLayoutParams(layoutParams);
                        } else if(layoutParams instanceof FrameLayout.LayoutParams) {
                            ((FrameLayout.LayoutParams) layoutParams).topMargin = topMargin;
                            ((FrameLayout.LayoutParams) layoutParams).gravity = Gravity.CENTER_HORIZONTAL;
                            mTouchImage.setLayoutParams(layoutParams);
                        }


                        ObjectAnimator tranY = ObjectAnimator.ofFloat(mTouchImage, View.TRANSLATION_Y,
                                -topMargin);
                        tranY.addListener(new AnimatorListenerAdapter() {
                            @Override
                            public void onAnimationStart(Animator animation) {
                                super.onAnimationStart(animation);
                                videoView.setVisibility(View.INVISIBLE);
                                mTouchImage.setVisibility(View.VISIBLE);
                            }
                        });

                        AnimatorSet goTopAnimatorSet = new AnimatorSet();
                        goTopAnimatorSet.setDuration(RecommendFragmentAdUtil.HIDE_BG_TIME);
                        goTopAnimatorSet.playTogether(tranY);

                        ObjectAnimator tranX = ObjectAnimator.ofFloat(mTouchImage, View.TRANSLATION_X,
                                (BaseUtil.getScreenWidth(ToolUtil.getCtx()) * 1.0f / 2 - touchWidth * 1.0f / 2 - BaseUtil.dp2px(ToolUtil.getCtx(), 12)));
                        // 75是 HightLightAdsorbAdView.getBottomMarginDp 返回的高度 15 是HightLightAdsorbAdView paddingBottom的高度
                        ObjectAnimator downAnimator = ObjectAnimator.ofFloat(mTouchImage, View.TRANSLATION_Y,
                                baseFragment.getView().getHeight() - BaseUtil.dp2px(ToolUtil.getCtx(), 75 + 15) - touchWidth - topMargin);
                        int videoHeight = videoView.getHeight();
                        downAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                            @Override
                            public void onAnimationUpdate(ValueAnimator animation) {
                                ViewGroup.LayoutParams layoutParams = videoView.getLayoutParams();
                                float animatedFraction = 1 - animation.getAnimatedFraction();
                                layoutParams.height = (int) (videoHeight * animatedFraction);
                                if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                                    ((ViewGroup.MarginLayoutParams) layoutParams).topMargin =
                                            (int) (topMarge * animatedFraction);
                                    ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin =
                                            (int) (topMarge * animatedFraction);
                                }
                                videoView.setLayoutParams(layoutParams);
                            }
                        });

                        AnimatorSet downAnimatorSet = new AnimatorSet();
                        downAnimatorSet.setDuration(400);
                        downAnimatorSet.playTogether(tranX, downAnimator);
                        downAnimatorSet.setInterpolator(new AccelerateDecelerateInterpolator());

                        AnimatorSet animatorAll = new AnimatorSet();
                        animatorAll.playSequentially(goTopAnimatorSet, downAnimatorSet);
                        animatorAll.addListener(new AnimatorListenerAdapter() {
                            @Override
                            public void onAnimationEnd(Animator animation) {
                                super.onAnimationEnd(animation);
                                if (mBigScreenStateChange != null) {
                                    mBigScreenStateChange.onTouchImgAnimatorOver(mTouchImage);
                                }

                                mAnimationRunning = false;
                                ((BigVideoAdView) videoView).release();
                                relativeLayout.removeView(videoView);
                                isShowingBigAd = false;
                                removeViewWithBannerCheck();
                            }
                        });
                        animatorAll.start();
                    }

                }
            });


            ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(videoView, View.SCALE_X, 1f, 0.2f);
            ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(videoView, View.SCALE_Y, 1f, 0.2f);
            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.setDuration(300);
            animatorSet.playTogether(scaleXAnimator, scaleYAnimator);

            animatorSet.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    mAnimationRunning = true;
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    if (mBigScreenStateChange != null) {
                        mBigScreenStateChange.onBigScreenHideAnimatorOver();
                    }



                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    mAnimationRunning = false;
                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });


        }
    }

    public boolean isPromotionOperationModuleShow(){
        return baseFragment.hasPromotionOperationModule();
    }

    public boolean isBigAdShowing() {
        return isShowingBigAd;
    }

    // 隐藏巨幕视频的界面
    private void hideNormalAnimator(RelativeLayout relativeLayout, View videoView) {
        if (!mAnimationRunning) {
            ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(videoView, View.ALPHA, 1f, 0);
            alphaAnimator.setDuration(200);
            AnimatorSet allAnimator = new AnimatorSet();

            int videoHeight = videoView.getHeight();
            int topMarge = BannerView.getTopMargin(ToolUtil.getCtx());

            ValueAnimator valueAnimator = ValueAnimator.ofFloat(1, 0);
            valueAnimator.setDuration(250);
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    ViewGroup.LayoutParams layoutParams = videoView.getLayoutParams();
                    layoutParams.height = (int) (videoHeight * (float)animation.getAnimatedValue());
                    if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                        ((ViewGroup.MarginLayoutParams) layoutParams).topMargin =
                                (int) (topMarge * (float) animation.getAnimatedValue());
                        ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin =
                                (int) (topMarge * (float) animation.getAnimatedValue());
                    }
                    videoView.setLayoutParams(layoutParams);
                }
            });

            allAnimator.playSequentially(alphaAnimator, valueAnimator);
            allAnimator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    mAnimationRunning = true;
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    if (mBigScreenStateChange != null) {
                        mBigScreenStateChange.onBigScreenHideAnimatorOver();
                    }

                    mAnimationRunning = false;
                    ((BigVideoAdView) videoView).release();
                    relativeLayout.removeView(videoView);
                    isShowingBigAd = false;
                    removeViewWithBannerCheck();
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    mAnimationRunning = false;
                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            allAnimator.start();
        }
    }

    private void showAnimatorForNoBanner(BigVideoAdView videoView, Advertis advertis, RelativeLayout relativeLayout) {
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "showAnimatorForNoBanner mAnimationRunning =" + mAnimationRunning);
        if (!mAnimationRunning) {
            View packBox = relativeLayout.findViewById(R.id.main_unit_pack_box);
            View packBoxBottom = relativeLayout.findViewById(R.id.main_unit_pack_box_bottom);
            int videoHeight;
            if (videoView.getLayoutParams() != null && videoView.getLayoutParams().height != 0) {
                videoHeight = videoView.getLayoutParams().height;
            } else {
                int[] customBannerWidthAndHeight = BannerView.getCustomBannerWidthAndHeight(context);
                int adWidth = customBannerWidthAndHeight[0];
                videoHeight = (int) (adWidth * 9 * 1.0f / 16); // 宽高比 = 16 ： 9
            }

            int topMargin = BaseUtil.dp2px(context, 16);
            int bottomMargin = BaseUtil.dp2px(context, 8);

            ValueAnimator valueAnimator = ValueAnimator.ofFloat(0, 1);
            valueAnimator.setDuration(400);
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    ViewGroup.LayoutParams layoutParams = videoView.getLayoutParams();
                    layoutParams.height = (int) (videoHeight * (float)animation.getAnimatedValue());
                    if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                        ((ViewGroup.MarginLayoutParams) layoutParams).topMargin =
                                (int) (topMargin * (float) animation.getAnimatedValue());
                        ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin =
                                (int) (bottomMargin * (float) animation.getAnimatedValue());
                    }
                    videoView.setLayoutParams(layoutParams);
                }
            });

            valueAnimator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    doBoxShowAnimation(packBox);
                    mAnimationRunning = true;
                    videoView.setVisibility(View.VISIBLE);
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    mAnimationRunning = false;
                    showDazzling(advertis, advertis.getShowstyle() != Advertis.IMG_SHOW_TYPE_UNITED_SCREEN);
                    // 设置圆弧切图可见
                    if (packBoxBottom != null) {
                        packBoxBottom.setAlpha(1.0f);
                    }
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    mAnimationRunning = false;
                    showDazzling(advertis, advertis.getShowstyle() != Advertis.IMG_SHOW_TYPE_UNITED_SCREEN);
                    // 设置圆弧切图可见
                    if (packBoxBottom != null) {
                        packBoxBottom.setAlpha(1.0f);
                    }
                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            valueAnimator.start();
            HandlerManager.postOnUIThreadDelay(new Runnable() {
                @Override
                public void run() {
                    if (videoView.getMeasuredWidth() == 0) {
                        Logger.i("-------RecommendFocusAdapterProviderStaggered", "showAnimatorForNoBanner remove bigAd");
                        valueAnimator.cancel();
                        removeBigAd(false, true);
                    }
                }
            }, 400);
        }
    }

    private void doBoxShowAnimation(View packBox) {
        if (packBox == null) {
            return;
        }
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(packBox, View.ALPHA, 0f, 1.0f);
        alphaAnimator.setDuration(280);
        alphaAnimator.setStartDelay(240);
        alphaAnimator.start();
    }

    // 无焦点图时隐藏巨幕动效
    private void hideAnimatorForNoBanner(RelativeLayout relativeLayout, BigVideoAdView videoView) {
        if (!mAnimationRunning) {
            ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(videoView, View.ALPHA, 1f, 0);
            alphaAnimator.setDuration(300);
            alphaAnimator.setStartDelay(100);
            AnimatorSet allAnimator = new AnimatorSet();

            int videoHeight = videoView.getHeight();
            int topMarge = BaseUtil.dp2px(context, 16);

            ValueAnimator valueAnimator = ValueAnimator.ofFloat(1, 0);
            valueAnimator.setDuration(400);
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    ViewGroup.LayoutParams layoutParams = videoView.getLayoutParams();
                    layoutParams.height = (int) (videoHeight * (float)animation.getAnimatedValue());
                    if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                        ((ViewGroup.MarginLayoutParams) layoutParams).topMargin =
                                (int) (topMarge * (float) animation.getAnimatedValue());
                        ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin =
                                (int) (topMarge * (float) animation.getAnimatedValue());
                    }
                    videoView.setLayoutParams(layoutParams);
                }
            });

            allAnimator.playTogether(alphaAnimator, valueAnimator);
            allAnimator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {
                    mAnimationRunning = true;
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    if (mBigScreenStateChange != null) {
                        mBigScreenStateChange.onBigScreenHideAnimatorOver();
                    }

                    mAnimationRunning = false;
                    ((BigVideoAdView) videoView).release();
                    relativeLayout.removeView(videoView);
                    isShowingBigAd = false;
                    removeViewWithBannerCheck();
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    mAnimationRunning = false;
                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            allAnimator.start();
        }
    }

    @Override
    public void onFragmentPause() {
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "onFragmentPause");
        if (!RecommendFragmentAdUtil.isGotoCheckPermission) {
            removeBigAd(false, false);
        }
        isFragmentPause = true;
        if (mBannerView != null) {
            BannerView bannerView = mBannerView.get();
            if (bannerView != null) {
                bannerView.onFragmentPause();
            }
        }
    }

    @Override
    public void onFragmentResume() {
        Logger.i("-------RecommendFocusAdapterProviderStaggered", "onFragmentResume");
        isFragmentPause = false;
        fragmentResumeTime = System.currentTimeMillis();
        if (!isViewAdded) {
            return;
        }
        checkHasUnitLoadingBigScreenAd();
        if (mBannerView != null) {
            BannerView bannerView = mBannerView.get();
            if (bannerView != null) {
                bannerView.onFragmentResume();
            }
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser, boolean isResumed) {
        if (isResumed && !isVisibleToUser) {
            removeBigAd(false, true);
        }
    }
}
