package com.ximalaya.ting.android.main.adapter.find.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.TextView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.inter.IFreeListenerTimeCallBack
import com.ximalaya.ting.android.main.adapter.find.util.FreeListenerTimeUtil
import com.ximalaya.ting.android.main.adapter.find.util.FreeListenerUtils
import com.ximalaya.ting.android.read.utils.checkActivity
import java.util.Calendar
import java.util.Locale

class FreeListenerTimeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr), IFreeListenerTimeCallBack {

    private val tvDay: TextView
    private val tvDayEnd: TextView
    private val tvHour: TextView
    private val tvMinute: TextView
    private val tvSecond: TextView

    private var mFreeListenerTimeUtil: FreeListenerTimeUtil? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.main_item_free_listener_time_view, this)

        tvDay = findViewById(R.id.tv_day)
        tvDayEnd = findViewById(R.id.tv_day_end)
        tvHour = findViewById(R.id.tv_hour)
        tvMinute = findViewById(R.id.tv_minute)
        tvSecond = findViewById(R.id.tv_second)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        FreeListenerUtils.addIFreeListenFunc(mFreeListenerTimeUtil)
        mFreeListenerTimeUtil?.addTimeCallBack(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        FreeListenerUtils.removeIFreeListenFunc(mFreeListenerTimeUtil)
        mFreeListenerTimeUtil?.removeTimeCallBack()
    }

    override fun onTime(day: Int, hours: Int, minutes: Int, seconds: Int) {
        if (!context.checkActivity()) {
            return
        }

        if (day <= 0) {
            tvDay.visibility = GONE
            tvDayEnd.visibility = GONE
        } else {
            tvDay.visibility = visibility
            tvDayEnd.visibility = visibility
            val formattedDays = String.format(Locale.getDefault(), "%02d", day)
            tvDay.text = formattedDays
        }

        val formattedHours = String.format(Locale.getDefault(), "%02d", hours)
        val formattedMinutes = String.format(Locale.getDefault(), "%02d", minutes)
        val formattedSeconds = String.format(Locale.getDefault(), "%02d", seconds)

        tvHour.text = formattedHours
        tvMinute.text = formattedMinutes
        tvSecond.text = formattedSeconds
    }

    override fun onTimeFinish() {
        mFinishTimeCallBack?.onTimeFinish()
    }

    private var xmRequestId: String? = null
    private var mModulePosition: Int? = null
    private var mEndTime: Long? = null
    private var mFinishTimeCallBack: IFinishTimeCallBack? = null

    fun setData(
        requestId: String?,
        modulePosition: Int,
        endTime: Long? = null,
        finishTimeCallBack: IFinishTimeCallBack? = null
    ) {
        if (xmRequestId == requestId && mModulePosition == modulePosition && mEndTime == endTime) {
            return
        }
        xmRequestId = requestId
        mModulePosition = modulePosition
        mEndTime = endTime
        lastStartTime = -1L
        mFinishTimeCallBack = finishTimeCallBack

        mFreeListenerTimeUtil?.removeTimeCallBack()
        mFreeListenerTimeUtil = object : FreeListenerTimeUtil() {
            override fun getStartTime(): Long {
                if (mEndTime != null && mEndTime!! > 0L) {
                    return mEndTime!!
                }
                return getLastStartTime()
            }
        }
        mFreeListenerTimeUtil?.modulePosition = modulePosition
        FreeListenerUtils.addIFreeListenFunc(mFreeListenerTimeUtil)
        mFreeListenerTimeUtil?.addTimeCallBack(this)
    }

    private var lastStartTime: Long = -1

    private fun getLastStartTime(): Long {
        if (lastStartTime == -1L) {
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.HOUR_OF_DAY, 24)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            lastStartTime = calendar.timeInMillis
            return lastStartTime
        }
        return lastStartTime
    }

    interface IFinishTimeCallBack {
        fun onTimeFinish()
    }
}