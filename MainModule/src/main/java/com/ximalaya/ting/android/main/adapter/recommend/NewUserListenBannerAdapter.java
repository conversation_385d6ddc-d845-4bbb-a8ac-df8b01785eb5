package com.ximalaya.ting.android.main.adapter.recommend;

import android.graphics.Bitmap;
import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.model.recommend.newuserlisten.NewUserListenSquare;

import java.util.List;

/**
 * Created by WolfXu on 2019/3/15.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class NewUserListenBannerAdapter extends PagerAdapter {

    private List<NewUserListenSquare> mData;
    private IBannerImageLoadedCallback mBannerImageLoadedCallback;
    private View mCurrentPage;

    @Override
    public int getCount() {
        if (mData != null) {
            return mData.size();
        }
        return 0;
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        ImageView ivBanner = (ImageView) LayoutInflater.from(container.getContext()).inflate(R.layout.main_item_new_user_listen_banner, container, false);
        container.addView(ivBanner);
        if (mData != null && position >= 0 && position < mData.size()) {
            final NewUserListenSquare itemData = mData.get(position);
            ImageManager.from(container.getContext()).displayImage(ivBanner, itemData.getHeadPic(), -1
                    , new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                     if (mBannerImageLoadedCallback != null) {
                         mBannerImageLoadedCallback.onBannerImageLoaded(itemData, bitmap);
                     }
                }
            });
        }
        return ivBanner;
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        if (object instanceof View) {
            container.removeView((View) object);
        }
    }

    @Override
    public void setPrimaryItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        super.setPrimaryItem(container, position, object);
        if (object instanceof View) {
            mCurrentPage = (View) object;
        }
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    public void setData(List<NewUserListenSquare> data) {
        mData = data;
    }

    public List<NewUserListenSquare> getData() {
        return mData;
    }

    @Override
    public float getPageWidth(int position) {
        return 1f;
    }

    public void setBannerImageLoadedCallback(IBannerImageLoadedCallback bannerImageLoadedCallback) {
        mBannerImageLoadedCallback = bannerImageLoadedCallback;
    }

    public View getCurrentPage() {
        return mCurrentPage;
    }

    public interface IBannerImageLoadedCallback {
        void onBannerImageLoaded(NewUserListenSquare square, Bitmap bitmap);
    }
}
