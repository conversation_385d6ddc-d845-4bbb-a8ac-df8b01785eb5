package com.ximalaya.ting.android.main.albumModule.album.album3;

import static android.view.View.FOCUS_UP;
import static android.widget.LinearLayout.HORIZONTAL;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.ProgressDialog;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RatingBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.GlobalGrayManager;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.constant.SharedConstant;
import com.ximalaya.ting.android.host.constants.TingListConstants;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.LuckyDrawManager;
import com.ximalaya.ting.android.host.manager.ShareResultManager;
import com.ximalaya.ting.android.host.manager.UserSwitchManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RecordActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.pay.PayManager;
import com.ximalaya.ting.android.host.manager.pay.PaySignatureUtil;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.manager.share.ShareTraceUtilKt;
import com.ximalaya.ting.android.host.manager.share.biz.ClaCUserUtils;
import com.ximalaya.ting.android.host.manager.share.customsharetype.ShareAddToDesktop;
import com.ximalaya.ting.android.host.manager.tinglist.ITingListManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.dialog.BaseDialogModel;
import com.ximalaya.ting.android.host.systrace.TraceTag;
import com.ximalaya.ting.android.host.util.AlbumColorUtil;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.MyListenRouterUtil;
import com.ximalaya.ting.android.host.util.ReportUtil;
import com.ximalaya.ting.android.host.util.ShareUtils;
import com.ximalaya.ting.android.host.util.ShortcutUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.LottieUrlConstants;
import com.ximalaya.ting.android.host.util.other.ScoreAbTestUtil;
import com.ximalaya.ting.android.host.util.view.AccessibilityClassNameUtil;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.albumModule.album.ShareTipDailogFragment;
import com.ximalaya.ting.android.main.albumModule.album.singleAlbum.IAlbumSubViewLifecycleListener;
import com.ximalaya.ting.android.main.albumModule.other.SimilarRecommendFragment;
import com.ximalaya.ting.android.main.constant.MainUrlConstants;
import com.ximalaya.ting.android.main.downloadModule.other.BatchDownloadFragment;
import com.ximalaya.ting.android.main.fragment.find.other.recommend.ReportFragment;
import com.ximalaya.ting.android.main.manager.albumFragment.AlbumFragmentMarkPointManager;
import com.ximalaya.ting.android.main.payModule.AlbumAutoBuyConfirmDialog;
import com.ximalaya.ting.android.main.payModule.refund.AlbumRefundInfoFragment;
import com.ximalaya.ting.android.main.payModule.refund.RefundFragment;
import com.ximalaya.ting.android.main.playModule.view.SkipHeadTailDialog;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.util.other.BuyAndPresentUtil;
import com.ximalaya.ting.android.main.util.other.ShareUtilsInMain;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.main.view.QuestRewardUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.shareservice.AbstractShareType;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ZhuPeipei on 2021/3/31 17:40.
 */
public class AlbumTitleView3 implements IAlbumSubViewLifecycleListener {
    private static final String TAG = "AlbumTitleView3";
//    private static final org.slf4j.Logger log = LoggerFactory.getLogger(AlbumTitleView3.class);
    private final Activity mActivity;
    private final AlbumFragmentNew3 mFragment;
    private final AlbumFragmentPresenter3 mPresenter;
    private final boolean mChildProtectOpen;

    List<BaseDialogModel> models = new ArrayList<>();
    private SkipHeadTailDialog mSkipHeadTailDialog;
    private PopupWindow mMoreActionDialog;
    private LinearLayout mPopupView;
    private HorizontalScrollView mMoreScrollView;
    private ITingListManager mTingListManager;
    private boolean mIsTitleBarShow = false;
    Animation fadeInAnimationTitle;
    Animation fadeInAnimationSubscribe;

    ////////////////////////////////// title views //////////////////////////////////
    private View vTitleBar;
    private ImageView mBackButton;
    private ImageView mMoreButton;
    private RelativeLayout vPageTitleArea;
    private TextView vPageTitle;
    private RatingBar vTitleRating;
    private TextView vTitlePoint;
    private XmLottieAnimationView mIvSwitchShare;
    private TextView mTvRiskMsg;
    private FrameLayout vFlSingle;
    private View mActionsGroup;
    private boolean mTitleBarIconWhiteMode = false;
    private int mLastAlpha = 0;
    private int mLastColor = -1;

    ////////////////////////////////// title views //////////////////////////////////

    public AlbumTitleView3(@NonNull Activity activity, @NonNull AlbumFragmentNew3 fragment, @NonNull AlbumFragmentPresenter3 presenter) {
        mActivity = activity;
        mFragment = fragment;
        mPresenter = presenter;
        mChildProtectOpen = ChildProtectManager.isChildProtectOpen(activity);
        fadeInAnimationTitle = AnimationUtils.loadAnimation(activity, com.ximalaya.ting.android.host.R.anim.host_view_fade_in);
        fadeInAnimationSubscribe = AnimationUtils.loadAnimation(activity, com.ximalaya.ting.android.host.R.anim.host_view_fade_in);
    }

    @Override
    public void onAlbumPageScrollStop(int contentTopY, boolean isTopHidden) {

    }

    @Override
    public void onAlbumPageMyResume() {

    }

    @Override
    public void onAlbumPagePause() {
        Logger.d(TAG, TAG + " onPause");
    }

    @Override
    public void onAlbumPageDestroy() {
        Logger.d(TAG, TAG + " onDestroy");
        mIvSwitchShare.removeAllLottieOnCompositionLoadedListener();
    }

    public void initTitleBar(View rootView) {
        TraceTag.i();
        vTitleBar = rootView.findViewById(R.id.main_title_bar);
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            int statusBarHeight = BaseUtil.getStatusBarHeight(mActivity);
            int titleBarHeight = (int) mActivity.getResources().getDimension(com.ximalaya.ting.android.host.R.dimen.host_title_bar_height);
            ViewGroup.LayoutParams titleBarLayoutParams = vTitleBar.getLayoutParams();
            if (titleBarLayoutParams != null) {
                titleBarLayoutParams.height = titleBarHeight + statusBarHeight;
                vTitleBar.setLayoutParams(titleBarLayoutParams);
                vTitleBar.setPadding(0, statusBarHeight, 0, 0);
            }
        }
        if (vTitleBar.getBackground() != null) {
            vTitleBar.getBackground().mutate().setAlpha(0);
        }
        mBackButton = rootView.findViewById(R.id.main_album_back_btn);
        mBackButton.setOnClickListener(v -> {
            mFragment.mHasScrollOrTouch = true;
            if (!mFragment.isStickyTopStatus()) {
                mFragment.finishFragment();
            } else {
                Album3MarkPointManager.Companion.trackOnBackToTopBtnClick(mPresenter.getAlbum());
                mFragment.scrollContentView(true, false);
            }
        });
        mMoreButton = rootView.findViewById(R.id.main_album_more_btn);
        vPageTitleArea = rootView.findViewById(R.id.main_album_single_page_title_area);
        vPageTitle = rootView.findViewById(R.id.main_album_single_page_title);
        vTitleRating = rootView.findViewById(R.id.main_album_single_page_rating);
        View vRatingLayout = rootView.findViewById(R.id.main_album_title_rating_layout);
        ViewStatusUtil.setVisible(ScoreAbTestUtil.INSTANCE.isNeedHideScore() ? View.GONE : View.VISIBLE, vRatingLayout);
        vTitlePoint = rootView.findViewById(R.id.main_album_single_page_point);
        mTvRiskMsg = rootView.findViewById(R.id.main_album_single_risk);
        vFlSingle = rootView.findViewById(R.id.main_album_fl_single);
        mIvSwitchShare = rootView.findViewById(R.id.main_iv_album_share);
        mActionsGroup = rootView.findViewById(R.id.main_album3_title_action_group);
        vTitleRating.setClickable(false);
        vFlSingle.setOnClickListener(v -> {
            if (mFragment.isStickyTopStatus()) {
                mFragment.scrollContentView(true, false);
            }
        });
        TraceTag.o();
    }

    public void setTitleView(boolean shouldPostTrace) {
        if (mPresenter.getAlbum() == null) {
            return;
        }
        vPageTitle.setText(mPresenter.getAlbum().getAlbumTitle());
        if (mPresenter.getAlbum().getScore() > 0 && mPresenter.getAlbum().getScoresCount() >= 10) {
            vTitleRating.setRating((float) mPresenter.getAlbum().getScore() / 2);
            vTitlePoint.setText("" + mPresenter.getAlbum().getScore());
            vTitlePoint.setContentDescription("评分 " + mPresenter.getAlbum().getScore());
            ViewStatusUtil.setVisible(View.VISIBLE, vTitleRating, vTitlePoint);
        } else {
            ViewStatusUtil.setVisible(View.GONE, vTitleRating, vTitlePoint);
        }
        mMoreButton.setVisibility(View.VISIBLE);
        mMoreButton.setOnClickListener(new TitleMoreClickListener());

        if (mPresenter.getAlbum().isNoCopyright() || mChildProtectOpen) {
            ViewStatusUtil.setVisible(View.GONE, mIvSwitchShare);
        } else {
            ViewStatusUtil.setVisible(View.VISIBLE, mIvSwitchShare);
            if (ClaCUserUtils.isValidUser()) {
                mIvSwitchShare.setScaleType(ImageView.ScaleType.FIT_CENTER);
                if (Album3Utils.INSTANCE.isBigIpStyleAlbum(mPresenter.getAlbum()) || BaseFragmentActivity.sIsDarkMode) {
                    mIvSwitchShare.setAnimationFromUrl(LottieUrlConstants.LOTTIE_PATH_RED_POCKET_DARK);
                } else {
                    mIvSwitchShare.setAnimationFromUrl(LottieUrlConstants.LOTTIE_PATH_RED_POCKET_DAY);
                }
                mIvSwitchShare.addLottieOnCompositionLoadedListener(composition -> {
                    if (canUpdateUi()) {
                        mIvSwitchShare.playAnimation();
                    }
                });
            } else {
                mIvSwitchShare.setScaleType(ImageView.ScaleType.CENTER);
                mIvSwitchShare.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_ic_standard_title_share);
                mIvSwitchShare.setImageTintList(
                        ColorStateList.valueOf(mFragment.getResourcesSafe().getColor(com.ximalaya.ting.android.host.R.color.host_color_131313_ffffff)));
            }
            mIvSwitchShare.setOnClickListener(new TitleShareClickListener());
        }
        checkAlbumSingleShow(true);
    }

    void changeBackBtnIcon(boolean isSticky) {
        mBackButton.setImageResource(isSticky ? R.drawable.main_album3_go_top_arrow_icon : R.drawable.host_ic_standard_back_arrow_left);
    }

    private void checkAlbumSingleShow(boolean canShowRiskView) {
        if (mPresenter.getAlbum() == null) {
            vFlSingle.setVisibility(View.GONE);
            return;
        }
        if (canShowRiskView) {
            boolean isMyAlbum =
                    mPresenter.getAlbum().getUid() != 0 &&
                            UserInfoMannage.getUid() == mPresenter.getAlbum().getUid();
            boolean showAlbumVerify = isMyAlbum && mPresenter.getAlbum().getStatus() == AlbumM.STATUS_ING;
            // 主播自己作品的审核状态title
            if (showAlbumVerify) {
                ViewStatusUtil.setVisible(View.VISIBLE, mTvRiskMsg);
                ViewStatusUtil.setText(mTvRiskMsg, "审核中");
            }
            // FIXME if (mPresenter.getAlbum() != null && mPresenter.getAlbum().getFinancialStatus() == 1)
            else if (!TextUtils.isEmpty(mPresenter.getAlbum().getWarningMsg())) {
                ViewStatusUtil.setVisible(View.VISIBLE, mTvRiskMsg);
                mTvRiskMsg.setText(mPresenter.getAlbum().getWarningMsg());
            } else {
                ViewStatusUtil.setVisible(View.GONE, mTvRiskMsg);
            }
        } else {
            ViewStatusUtil.setVisible(View.GONE, mTvRiskMsg);
        }
        if (mTvRiskMsg.getVisibility() != View.VISIBLE && vPageTitleArea.getVisibility() != View.VISIBLE) {
            vFlSingle.setVisibility(View.GONE);
        } else {
            vFlSingle.setVisibility(View.VISIBLE);
        }
    }

    public void bgColorChange(@ColorInt int color) {
        if (color != -1) {
            mLastColor = color;
        }
        if (mLastColor == -1) {
            return;
        }
        if (Album3Utils.INSTANCE.isBigIpStyleAlbum(mPresenter.getAlbum())) {
            if (BaseFragmentActivity.sIsDarkMode && mFragment.isStickyTopStatus()) {
                vTitleBar.setBackgroundColor(Color.parseColor("#131313"));
            } else {
                vTitleBar.setBackgroundColor(mLastColor);
            }
            vTitleBar.getBackground().mutate().setAlpha(mLastAlpha);
        }
    }

    public View provideTitleBar() {
        return vTitleBar;
    }

    public void onScroll(int totalScrollY, int scrollY) {
        if (vTitleBar != null) {
            int len = totalScrollY * 2 / 3;
            if (scrollY >= len) {
                if (vTitleBar.getBackground() != null) {
                    mLastAlpha = 0xFF;
                    vTitleBar.getBackground().mutate().setAlpha(0xFF);
                }
                if (Album3Utils.INSTANCE.isBigIpStyleAlbum(mPresenter.getAlbum()) && mActionsGroup.getBackground() != null) {
                    mActionsGroup.setBackground(null);
                }
//                changeBtnColor(false);
            } else {
                if (vTitleBar.getBackground() != null) {
                    mLastAlpha = scrollY * 0xFF / len;
                    vTitleBar.getBackground().mutate().setAlpha(scrollY * 0xFF / len);
                }
                if (Album3Utils.INSTANCE.isBigIpStyleAlbum(mPresenter.getAlbum()) && mActionsGroup.getBackground() == null) {
                    mActionsGroup.setBackgroundResource(R.drawable.main_16corner_26000000);
                }
//                changeBtnColor(true);
            }
            if (mIsTitleBarShow && scrollY < len) {
                mIsTitleBarShow = false;
                vPageTitleArea.setVisibility(View.INVISIBLE);
                // FIXME mPresenter.getAlbum().getFinancialStatus() == 1
                checkAlbumSingleShow(true);
            } else if (!mIsTitleBarShow && scrollY >= len) {
                mIsTitleBarShow = true;
                vPageTitleArea.setAnimation(fadeInAnimationTitle);
                vPageTitleArea.startAnimation(fadeInAnimationTitle);
                ViewStatusUtil.setVisible(View.VISIBLE, vPageTitleArea);
                mTvRiskMsg.setVisibility(View.INVISIBLE);
                checkAlbumSingleShow(false);
            }
        }
    }

    public void initAlbumData() {
        changeBtnColor(true);
    }


    private void changeBtnColor(boolean iconWhite) {
        if (mPresenter == null || mPresenter.getAlbum() == null || !Album3Utils.INSTANCE.isBigIpStyleAlbum(mPresenter.getAlbum())) {
            return;
        }
        if (mTitleBarIconWhiteMode == iconWhite) {
            return;
        }
        mTitleBarIconWhiteMode = iconWhite;
        if (iconWhite) {
            StatusBarManager.setStatusBarColor(mFragment.getWindow(), false);
            mActionsGroup.setBackgroundResource(R.drawable.main_16corner_26000000);
        } else {
            StatusBarManager.setStatusBarColor(mFragment.getWindow(), !BaseFragmentActivity.sIsDarkMode);
            mActionsGroup.setBackground(null);
        }
        int color = mFragment.getResources().getColor(iconWhite ? R.color.main_color_ffffff : R.color.main_color_131313_ffffff);
        mBackButton.setColorFilter(color);
        vPageTitle.setTextColor(color);
        mTvRiskMsg.setTextColor(Album3Utils.INSTANCE.getColorWithAlpha(0.6f, color));
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mTvRiskMsg.setCompoundDrawableTintList(ColorStateList.valueOf(Album3Utils.INSTANCE.getColorWithAlpha(0.6f, color)));
        }
        mIvSwitchShare.setColorFilter(color);
        mMoreButton.setColorFilter(color);
    }

    AdapterView.OnItemClickListener mPanelListener = (parent, view, position, id) -> {
        hideMoreOperationPanel();
        onItemClickImpl(parent, view, position, id);
    };

    private void onItemClickImpl(AdapterView<?> parent, View view, int position, long id) {
        final BaseDialogModel model = models.get(position);
        switch (model.position) {
            case 0://下载全部
                if (mPresenter.getAlbum().isNoCopyright()) {
                    CustomToast.showFailToast("版权方要求，该资源在该地区无法下载");
                    return;
                }
                if (UserInfoMannage.hasLogined()) {
                    if (mPresenter.getAlbum() != null) {
                        if (mPresenter.getAlbum().isOfflineHidden()) {
                            CustomToast.showFailToast("亲，该专辑已下架");
                        } else if (!mPresenter.getAlbum().isAuthorized() && mPresenter.getAlbum().getPriceTypeEnum() == PayManager.PAY_ALBUM_MEMBER) {
                            CustomToast.showFailToast("暂无可下载声音");
                        } else {
                            mFragment.startFragment(BatchDownloadFragment.newInstance(
                                    BatchDownloadFragment.ACTION_DOWNLOAD_BUY, mPresenter.getAlbum().getId()));
                        }
                    }
                } else {
                    UserInfoMannage.gotoLogin(mActivity);
                }
                break;
            case 1://我要评价
                break;
            case 2://咨询客服
                mFragment.startFragment(NativeHybridFragment.newInstance(MainUrlConstants.getInstanse().getCustomServiceForBoughtAlbum(), true));
                break;
            case 3://编辑专辑
                if (mPresenter.getAlbum() != null) {
                    if (mPresenter.getAlbum().getStatus() == 2) {
                        CustomToast.showFailToast("亲，该专辑已下架");
                    } else {
                        Router.getActionByCallback(Configure.BUNDLE_RECORD, new Router.IBundleInstallCallback() {

                            @Override
                            public void onInstallSuccess(BundleModel bundleModel) {
                                if (bundleModel == Configure.recordBundleModel) {
                                    try {
                                        BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newCreateAlbumFragment(mPresenter.getAlbum());
                                        mFragment.startFragment(fragment);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            }

                            @Override
                            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                            }

                            @Override
                            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                            }
                        });
                    }
                }
                break;
            case 4:
                if (mPresenter.getAlbum() != null) {
                    AlbumFragmentMarkPointManager.Companion.markPointOnClickFindSimilar(mPresenter.getAlbum().getId());
                    mFragment.startFragment(SimilarRecommendFragment.newInstanceByAlbumId(mPresenter.getAlbum().getId(), "相似推荐"));
                }
                break;
            case 5://举报专辑
                if (mPresenter.getAlbum() != null && mPresenter.getAlbum().getStatus() == 2) {
                    CustomToast.showFailToast("亲，该专辑已下架");
                } else if (UserInfoMannage.hasLogined()) {
                    int albumAgeLevel = 0;
                    if (mPresenter.getAlbum() != null) {
                        albumAgeLevel = mPresenter.getAlbum().getAgeLevel();
                    }
                    ReportFragment reportFragment = ReportFragment.newInstanceByAlbum(mPresenter.getAlbumId(), albumAgeLevel, mPresenter.getAlbum() != null ? mPresenter.getAlbum().getUid() : 0);
                    mFragment.startFragment(reportFragment);
                    if (mPresenter.getAlbum() != null) {
                        AlbumFragmentMarkPointManager.Companion.markPointOnClickReportAlbum(mPresenter.getAlbum().getId());
                    }
                } else {
                    UserInfoMannage.gotoLogin(mActivity);
                }
                break;
            case 6://发红包
                if (mPresenter.getAlbum() != null && mPresenter.getAlbum().getStatus() == 2) {
                    CustomToast.showFailToast("亲，该专辑已下架");
                } else {
                    Bundle bundle = new Bundle();
                    bundle.putString(BundleKeyConstants.KEY_EXTRA_URL,
                            MainUrlConstants.getInstanse().getWebOfRedEnvelopeList());
                    mFragment.startFragment(NativeHybridFragment.class, bundle);
                    if (mPresenter.getAlbum() != null) {
                        AlbumFragmentMarkPointManager.Companion.markPointOnClickSendRedPackage(mPresenter.getAlbum().getId());
                    }
                }
                break;
            case 7: // 申请退款
                // 先请求接口获取"用户是否已申请退款"（获取专辑退款id接口）
                loadRefundData();
                break;
            case 8: // 分享
                if (null != mIvSwitchShare) {
                    mIvSwitchShare.callOnClick();
                }
                break;
            case 10://返回首页
                if (mActivity instanceof MainActivity) {
                    ((MainActivity) mActivity).goHome();
                }
                break;
//            case 11: // 搜索
//                onSearchClick();
//                break;
            case 12: // 播放自动买
                changeAutoBuy();
                break;
            case 13:
                mSkipHeadTailDialog = new SkipHeadTailDialog(mActivity, mPresenter.getAlbumId());
                mSkipHeadTailDialog.toggle();
                AlbumFragmentMarkPointManager.Companion.markPointOnSkip(mPresenter.getAlbumId());
                break;
            case 14: // 添加到桌面
                if (mPresenter.getAlbum() != null) {
                    Bitmap icon = mFragment.getAlbumCoverImage();
                    final String iTingUrl = ShareAddToDesktop.OPEN_ALBUM_AND_PLAY_ITING + mPresenter.getAlbum().getId();
                    if (ShortcutUtil.isShortCutExist(mActivity, mPresenter.getAlbum().getAlbumTitle())) {
                        if (icon != null) {
                            ShortcutUtil.showAddDesktopShortCutSuccess(mActivity, mFragment.getView(), icon);
                        } else if (!TextUtils.isEmpty(mPresenter.getAlbum().getValidCover())) {
                            ShortcutUtil.showAddDesktopShortCutSuccess(mActivity, mFragment.getView(), mPresenter.getAlbum().getValidCover());
                        }
                    } else {
                        if (icon == null && !(TextUtils.isEmpty(mPresenter.getAlbum().getValidCover()))) {
                            ImageManager.Options options = new ImageManager.Options();
                            if (mActivity != null) {
                                ActivityManager activityManager = SystemServiceManager.getActivityManager(mActivity);
                                if (activityManager != null) {
                                    int iconSize = activityManager.getLauncherLargeIconSize();
                                    options.targetWidth = iconSize;
                                    options.targetHeight = iconSize;
                                }
                            }
                            ImageManager.from(mActivity).downloadBitmap(mPresenter.getAlbum().getValidCover(), options, new ImageManager.DisplayCallback() {
                                @Override
                                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                                    ShortcutUtil.createShortCut(mActivity, bitmap, iTingUrl, mPresenter.getAlbum().getAlbumTitle());
                                    checkDesktopShortCutAdded(mPresenter.getAlbum().getAlbumTitle(), bitmap);
                                }
                            });
                        } else {
                            ShortcutUtil.createShortCut(mActivity, icon, iTingUrl, mPresenter.getAlbum().getAlbumTitle());
                            checkDesktopShortCutAdded(mPresenter.getAlbum().getAlbumTitle(), icon);
                        }
                    }
                    AlbumFragmentMarkPointManager.Companion.markPointOnClickAddToDeskTop(mPresenter.getAlbum().getId());
                }
                break;
            case 15:
                showTingListDialog();
                break;
            case 16:
                model.checked = !model.checked;
                updateValue(mPanelListener);
                UserSwitchManager.getInstance().setPrivacyListen(mPresenter.getAlbumId(), model.checked, new IDataCallBack<String>() {
                    @Override
                    public void onSuccess(@Nullable String msg) {
                        CustomToast.showSuccessToast(msg);
                        mPresenter.getAlbum().setPrivateListen(model.checked);
                    }

                    @Override
                    public void onError(int code, String message) {
                        CustomToast.showFailToast(message);
                        model.checked = !model.checked;
                    }
                });
                AlbumFragmentMarkPointManager.Companion.markPointOnPrivacyListenSet(mPresenter.getAlbumId(), model.checked);
                break;
            case 17: //版权申诉
                if (UserInfoMannage.hasLogined()) {
                    AlbumFragmentMarkPointManager.Companion.markPointOnClickReclaimCopyRight(mPresenter.getAlbumId());
                    try {
                        String url = ConfigureCenter.getInstance()
                                .getString("tob", "tort_url") + "?albumId=" + mPresenter.getAlbumId();
                        mFragment.startFragment(NativeHybridFragment.newInstance(url, true));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    UserInfoMannage.gotoLogin(mActivity);
                }
                break;
            case 18: //买赠
                BuyAndPresentUtil.enterBuyAndPresentFragment(mFragment, mPresenter.getAlbum(), mPresenter.getAlbum().getBuyPresentData(), null);
                break;
            case 19: // 意见反馈
                if (mActivity instanceof MainActivity) {
                    String url = ConfigureCenter.getInstance().getString("toc", "album3_more_feed_back_url", "");
                    if (!url.contains("?")) {
                        url = url + "?";
                    } else {
                        url = url + "&";
                    }
                    url = url + "album_id=" + mPresenter.getAlbumId();
                    if (UserInfoMannage.hasLogined()) {
                        url = url + "&uid=" + UserInfoMannage.getUid();
                    }
                    NativeHybridFragment.start((MainActivity) mActivity, url, true);
                }
                break;
            case 99:
                if (mActivity instanceof MainActivity) {
                    ToolUtil.clickUrlAction(((MainActivity) mActivity), ClaCUserUtils.getLanding(), null);
                }
                break;
            default:
                break;
        }
        AlbumFragmentMarkPointManager.Companion.markPointOnShareOrMore(mPresenter.getAlbum(), model.title, mPresenter.getPageRequestId());
    }

    private void showTingListDialog() {
        MyListenRouterUtil.getMyListenBundle(bundleModel -> {
            IMyListenFunctionAction funAction = MyListenRouterUtil.getFunAction();
            if (funAction != null && canUpdateUi()) {
                if (mTingListManager == null) {
                    mTingListManager = funAction.newTingListManager(mFragment);
                }
                mTingListManager.showTingList(TingListConstants.TYPE_ALBUM, mPresenter.getAlbumId());
                AlbumFragmentMarkPointManager.Companion.markPointOnAddToTingList(mPresenter.getAlbumId());
            }
        });
    }

    /**
     * 获取退款id
     */
    private void loadRefundData() {
        AlbumFragmentMarkPointManager.Companion.markPointOnLoadRefundData(mPresenter.getAlbumId());
        final ProgressDialog mDialog = ToolUtil.createProgressDialog(mActivity, "正在获取数据");
        mDialog.show();
        if (mPresenter.getAlbum() != null && mPresenter.getAlbum().isTrainingCampAlbum()) {
            MainCommonRequest.getTrainingRefundStatus(mPresenter.getAlbum().getId(), mPresenter.getAlbum().getRefundStatusId(), new IDataCallBack<Integer>() {
                @Override
                public void onSuccess(@Nullable Integer object) {
                    if (!canUpdateUi()) return;
                    if (object != null) {
                        int refundStatusId = object;
                        //如果是0表明不是训练营专辑
                        if (refundStatusId == AppConstants.REFOUND_ERROE) {
                            paidAlbumRefund(mDialog);
                            return;
                        }
                        mPresenter.getAlbum().setRefundId(refundStatusId);
                        if (refundStatusId == AppConstants.REFOUND_OUT_OF_DATE) {
                            dismissDialog(mDialog);
                            CustomToast.showFailToast("退款已超出退款时间");
                        } else if (refundStatusId == AppConstants.REFOUND_SUPPORT_REFOUND) {
                            dismissDialog(mDialog);
                            AlbumRefundInfoFragment fragment = AlbumRefundInfoFragment.getTrainingCampInstance(mPresenter.getAlbumId(), 0);
                            if (fragment != null) {
                                mFragment.startFragment(fragment);
                            }
                        } else if (refundStatusId == AppConstants.REFOUND_REFUNDED) {
                            dismissDialog(mDialog);
                            CustomToast.showFailToast("已退款完成");
                        }
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (!canUpdateUi()) return;
                    dismissDialog(mDialog);
                    CustomToast.showFailToast(message);
                }
            });
        } else {
            paidAlbumRefund(mDialog);
        }
    }

    private void dismissDialog(ProgressDialog dialog) {
        if (dialog != null) {
            dialog.dismiss();
        }
    }

    private void backgroundAlpha(float bgAlpha) {
        if (mActivity == null) {
            return;
        }
        WindowManager.LayoutParams lp = mActivity.getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        mActivity.getWindow().setAttributes(lp);
    }

    private void checkDesktopShortCutAdded(final String title, final Bitmap bitmap) {
        mFragment.postOnUiThreadDelayed(() -> {
            if (!canUpdateUi()) {
                return;
            }
            if (ShortcutUtil.isShortCutExist(mActivity, title)) {
                ShortcutUtil.showAddDesktopShortCutSuccess(mActivity, mFragment.getView(), bitmap);
            } else {
                ShortcutUtil.showAddDesktopShortCutFail(mActivity, mFragment.getView());
            }
        }, 200);
    }

    private void changeAutoBuy() {
        String status;
        if (mPresenter.getAlbum().isAutoBuy()) {
            MainCommonRequest.closeAlbumAutoBuy(mPresenter.getAlbumId(), new IDataCallBack<JSONObject>() {
                @Override
                public void onSuccess(@Nullable JSONObject object) {
                    mPresenter.getAlbum().setAutoBuy(false);
                    CustomToast.showSuccessToast("自动购买已关闭");
                }

                @Override
                public void onError(int code, String message) {
                    mPresenter.getAlbum().setAutoBuy(true);
                    if (TextUtils.isEmpty(message)) {
                        message = "自动购买关闭失败";
                    }
                    CustomToast.showFailToast(message);
                }
            });
            status = "off";
        } else {
            AlbumAutoBuyConfirmDialog dialog = AlbumAutoBuyConfirmDialog.getInstance(mPresenter.getAlbumId());
            if (dialog != null) {
                dialog.show(mFragment.getFragmentManager(), "dialogTagAlbumAutoBuyConfirm");
                dialog.setResultListener(new AlbumAutoBuyConfirmDialog.AutoBuyResultListener() {
                    @Override
                    public void onSuccessResult() {
                        mPresenter.getAlbum().setAutoBuy(true);
                    }

                    @Override
                    public void onFailResult() {
                        mPresenter.getAlbum().setAutoBuy(false);
                    }
                });
            }
            status = "on";
        }
        AlbumFragmentMarkPointManager.Companion.markPointOnChangeAutoBuy(null == mPresenter.getAlbum() ? 0 : mPresenter.getAlbum().getId(), status);
    }

    private void paidAlbumRefund(final ProgressDialog dialog) {
        Map<String, String> params = new HashMap<>();
        params.put(BundleKeyConstants.KEY_ALBUM_ID, String.valueOf(mPresenter.getAlbumId()));
        params.put("signature", PaySignatureUtil.getSignature(mActivity, new HashMap<String, String>()));
        MainCommonRequest.getAlbumRefundId(params, new IDataCallBack<JSONObject>() {
            @Override
            public void onSuccess(@Nullable final JSONObject object) {
                if (!canUpdateUi()) return;
                if (null != object) {
                    int refundId = object.optInt("data", -1);
                    dismissDialog(dialog);
                    // 如果退款id<=0，表示未发起过退款(跳退款中间页)，否则跳退款详情页
                    if (refundId <= 0) {
                        // 未发起退款
                        AlbumRefundInfoFragment fragment = AlbumRefundInfoFragment.getInstance(mPresenter.getAlbumId(), refundId);
                        if (fragment != null) {
                            mFragment.startFragment(fragment);
                        }
                    } else {
                        // 已发起退款
                        RefundFragment refundFragment = RefundFragment.newInstance(refundId, mPresenter.getAlbumId());
                        if (refundFragment != null) {
                            mFragment.startFragment(refundFragment);
                        }
                    }
                }
            }

            @Override
            public void onError(int code, final String message) {
                if (!canUpdateUi()) return;
                dismissDialog(dialog);
                CustomToast.showFailToast(message);
            }
        });
    }

    private ShareResultManager.ShareListener mShareListener = new ShareResultManager.ShareListener() {
        @Override
        public void onShareSuccess(String thirdName) {
            if (mActivity != null) {
                ShareResultManager.getInstance().clearShareFinishListener();
            }
            if (mPresenter.getAlbum() == null || mPresenter.getAlbum().getId() < 1 || TextUtils.isEmpty(thirdName)) {
                return;
            }


            boolean isOtherThirdName = "weixin".equals(thirdName) || "weixinGroup".equals(thirdName)
                    || "dingTalk".equals(thirdName) || "qq".equals(thirdName);
            if ("tSina".equals(thirdName)) {
                thirdName = "weibo";
            } else if ("qzone".equals(thirdName)) {
                thirdName = "qqZone";
            } else if ("community".equals(thirdName)) {
                thirdName = "circle";
            } else if (!isOtherThirdName) {
                tryShowShareSuccessDialog(thirdName);
                return;
            }
            boolean resume = QuestRewardUtil.showFirstShareToast(mFragment, QuestRewardUtil.TYPE_SHARE);
            if (!resume) {
                tryShowShareSuccessDialog(thirdName);
            }
            AlbumFragmentMarkPointManager.Companion.markPointOnShareSuccess(null == mPresenter.getAlbum() ? 0 : mPresenter.getAlbum().getId(), thirdName);
        }

        @Override
        public void onShareFail(String thirdName) {
            if (mActivity != null) {
                ShareResultManager.getInstance().clearShareFinishListener();
            }
        }
    };

    private void tryShowShareSuccessDialog(String thirdName) {
        if (mPresenter.getAlbum() != null && mPresenter.getAlbum().getAnnouncer() != null) {
            ShareUtils.showShareSuccessDialog(mFragment,
                    mPresenter.getAlbum().getAnnouncer().getAvatarUrl(), mPresenter.getAlbum().getAnnouncer().getNickname(), thirdName);
        }
    }

    public View getTitleShare() {
        return mIvSwitchShare;
    }

    static class ShareCallback implements ShareManager.Callback {
        private WeakReference<AlbumTitleView3> mRef;
        private boolean isFromMore;

        ShareCallback(AlbumTitleView3 titleView, boolean more) {
            mRef = new WeakReference<>(titleView);
            isFromMore = more;
        }

        @Override
        public void onShare(AbstractShareType shareType) {
            if (mRef != null && mRef.get() != null && mRef.get().canUpdateUi() && shareType != null) {
                if (isFromMore) {
                    mRef.get().toggleMoreOperationPanel();
                    AlbumFragmentMarkPointManager.Companion.markPointOnShareOrMore(mRef.get().mPresenter.getAlbum(),
                            shareType.getTitle(), mRef.get().mPresenter.getPageRequestId());
                } else {
                    String title = mRef.get().mPresenter.getAlbumTitle();
                    if (mRef.get().mPresenter.getAlbum() != null && mRef.get().mPresenter.getAlbum().getAlbumTitle() != null) {
                        title = mRef.get().mPresenter.getAlbum().getAlbumTitle();
                        AlbumFragmentMarkPointManager.Companion.markPointOnShareResult(mRef.get().mPresenter.getAlbumId(),
                                shareType.getTitle(), title, mRef.get().mPresenter.getAlbum().getFamilyVipInfo() != null,
                                mRef.get().mPresenter.getAlbum(), mRef.get().mPresenter.getPageRequestId());
                    }
                }
            }
        }
    }

    class TitleShareClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            if (mFragment != null) {
                mFragment.mHasScrollOrTouch = true;
            }
            if (mPresenter.getAlbum() == null || mActivity == null) {
                ShareTraceUtilKt.traceDebugAlbumClick("没有专辑信息");
                CustomToast.showFailToast("亲，没有专辑信息哦~");
                return;
            }
            Album3MarkPointManager.Companion.markPointOnAlbumShareClick(mPresenter.getAlbum());
//            AlbumFragmentMarkPointManager.Companion.markPointOnAlbumTitleShareClick(mPresenter.getAlbum().getId(), mPresenter.getAlbum().getUid(), mPresenter.getPageRequestId());
            if (mPresenter.getAlbum().isPublic()) {
                if (mPresenter.getAlbum().getShareSupportType() == 1) {
                    ShareTraceUtilKt.traceDebugAlbumClick("独家售卖，不支持分享");
                    ShareTipDailogFragment.newInstance().show(mFragment.getChildFragmentManager(), ShareTipDailogFragment.TAG);
                    return;
                }
                boolean hasFamilyInfo = mPresenter.getAlbum().getFamilyVipInfo() != null;
                ShareUtilsInMain.shareAlbum(mActivity, mPresenter.getAlbum(),
                        mPresenter.getAlbum().isCpsProductExist() ? ICustomShareContentType.SHARE_TYPE_ALBUM_EARN :
                                ICustomShareContentType.SHARE_TYPE_ALBUM, hasFamilyInfo, new AlbumTitleView3.ShareCallback2(AlbumTitleView3.this, false));
                ShareResultManager.getInstance().setShareFinishListener(mShareListener);
                AlbumFragmentMarkPointManager.Companion.markPointOnShareDialogShow(mPresenter.getAlbumId(), hasFamilyInfo, mPresenter.getAlbum(), mPresenter.getPageRequestId());
            } else {
                ShareTraceUtilKt.traceDebugAlbumClick("私密专辑不能分享");
                CustomToast.showFailToast("亲，私密专辑不能分享哦~");
            }
            LuckyDrawManager.getInstance().setSharedToday();
        }
    }

    static class ShareCallback2 implements ShareManager.Callback {
        private WeakReference<AlbumTitleView3> mRef;
        private boolean isFromMore;

        ShareCallback2(AlbumTitleView3 titleView, boolean more) {
            mRef = new WeakReference<>(titleView);
            isFromMore = more;
        }

        @Override
        public void onShare(AbstractShareType shareType) {
            if (mRef != null && mRef.get() != null && mRef.get().canUpdateUi() && shareType != null) {
                if (isFromMore) {
                    mRef.get().toggleMoreOperationPanel();
                    AlbumFragmentMarkPointManager.Companion.markPointOnShareOrMore(mRef.get().mPresenter.getAlbum(),
                            shareType.getTitle(), mRef.get().mPresenter.getPageRequestId());
                } else {
                    String title = mRef.get().mPresenter.getAlbumTitle();
                    if (mRef.get().mPresenter.getAlbum() != null && mRef.get().mPresenter.getAlbum().getAlbumTitle() != null) {
                        title = mRef.get().mPresenter.getAlbum().getAlbumTitle();
                        AlbumFragmentMarkPointManager.Companion.markPointOnShareResult(mRef.get().mPresenter.getAlbumId(),
                                shareType.getTitle(), title, mRef.get().mPresenter.getAlbum().getFamilyVipInfo() != null,
                                mRef.get().mPresenter.getAlbum(), mRef.get().mPresenter.getPageRequestId());
                    }
                }
            }
        }
    }

    class TitleMoreClickListener implements View.OnClickListener {

        @Override
        public void onClick(final View v) {
            if (mFragment != null) {
                mFragment.mHasScrollOrTouch = true;
            }
            if (mPresenter.getAlbum() == null) {
                return;
            }
            Album3MarkPointManager.Companion.markPointOnAlbumTitleMoreClick(mPresenter.getAlbum().getId(),
                    mPresenter.getAlbum().getUid(), mPresenter.getPageRequestId(), mPresenter.getAlbum().getAlbumStyle());
            models.clear();

            if( ClaCUserUtils.isValidUser()) {
                models.add(new BaseDialogModel(R.drawable.main_action_sheet_share_ic_red, "会员红包", 99));
            } else if (mPresenter.getAlbum().getBuyPresentData() != null && !TextUtils.isEmpty(mPresenter.getAlbum().getBuyPresentData().getEntranceUrl())) {
                String title = TextUtils.isEmpty(mPresenter.getAlbum().getBuyPresentData().getTitle()) ? "买赠" : mPresenter.getAlbum().getBuyPresentData().getTitle();
                models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_gift_n_n_line_regular_24, title, 18));
            }
            if (!(!UserInfoMannage.hasLogined()
                    || mPresenter.getAlbum().isVipFree()
                    || !isSingleTrackBuyAlbum(mPresenter.getAlbum())
                    || (mPresenter.getAlbum().getVipFreeType() == 1 && UserInfoMannage.isVipUser())
                    || mPresenter.getAlbum().isTracksAllPurchased())) {
                String extra = mPresenter.getAlbum().isAutoBuy() ? "已开启" : "已关闭";
                models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_autobuy_n_n_line_regular_28, "播放自动买", 12, extra));
            }
            // 搜索
//            models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_search_n_n_line_regular_28, "搜索声音", 11));
            if (mPresenter.getAlbum().isPaid()) {
                if (mPresenter.getAlbum().isHasRedPack()) {
                    int count = mPresenter.getAlbum().getRedPackCount();
                    String extra = "";
                    if (count > 0) {
                        extra = "还剩" + count + "个";
                    } else if (count == 0) {
                        extra = "暂无红包可发";
                    }
                    models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_redenvelope_n_n_line_regular_28, "送好友红包", 6, extra));
                }
            }
            if (UserInfoMannage.getUid() != 0 && UserInfoMannage.getUid() == mPresenter.getAlbum().getUid() && !mPresenter.getAlbum().isPaid() &&
                    (mPresenter.getAlbum().getType() != AlbumM.TYPE_A_PLUS) && (mPresenter.getAlbum().getType() != AlbumM.TYPE_A_PLUS_TTS)) {   //是我的专辑并且不是付费专辑
                models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_edit_n_n_line_regular_28, "编辑专辑", 3));
            }
            models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_simliar_n_n_line_regular_28, "找相似", 4));
            models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_skip_n_n_line_regular_28, "跳过头尾", 13));
            if (ConfigureCenter.getInstance().getBool("tob", "tort", false)) {
                String text = ConfigureCenter.getInstance().getString("tob", "tort_text", "版权申诉");
                models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_copyright_n_n_line_regular_28, text, 17));
            }
//            models.add(new BaseDialogModel(R.drawable.main_ic_album_more_share, "分享", 8));
            String feedConfig = ConfigureCenter.getInstance().getString("toc", "album3_more_feed_back_url", "");
            if (!TextUtils.isEmpty(feedConfig) && feedConfig.contains("http")) {
                models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_feed_back_n_n_line_regular_28, "意见反馈", 19));
            }
            if (mPresenter.getAlbum().isPaid()) {
                models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_service_n_n_line_regular_28, "咨询客服", 2));
            }
            models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_report_n_n_line_regular_28,
                    ReportUtil.getReportTitleByType(SharedConstant.REPORT_TYPE_ALBUM), 5));
            models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_add_n_n_line_regular_28, "添加到听单", 15));
            models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_add_n_n_line_regular_28, "添加到桌面", 14));

            if (UserInfoMannage.hasLogined() && !mPresenter.getAlbum().isGlobalPrivateListen()) {
                BaseDialogModel privacyModel = new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_unlock_n_n_line_regular_28, "私密收听", 16,
                        (mPresenter.getAlbum().isPrivateListen() ? "已开启" : "已关闭"));
                privacyModel.checkedResId = com.ximalaya.ting.android.host.R.drawable.host_ic_lock_n_n_line_regular_28;
                privacyModel.checkedTitle = "私密收听";
                privacyModel.checked = mPresenter.getAlbum().isPrivateListen();
                models.add(privacyModel);
            }

            // 退款入口
            if (mPresenter.getAlbum().getRefundSupportType() == 1
                    && mPresenter.getAlbum().isPaid()
                    && mPresenter.getAlbum().isAuthorized()
                    && mPresenter.getAlbum().getPriceTypeEnum() == PayManager.PAY_ALBUM_WHOLE) {
                String refundStatusTips = "申请退款";
                models.add(new BaseDialogModel(com.ximalaya.ting.android.host.R.drawable.host_ic_refund_n_n_line_regular_28, refundStatusTips, 7));
            }

            if (mActivity == null) return;

            showMoreOperationPanel();

            if (mPresenter.getAlbum().isAuthorized() && !mPresenter.getAlbum().isCommented()
                    && mPresenter.getAlbum().isShowCommentAlert() && mPresenter.getLastPlayedOrWillPlayTrack() != null) {
                Map<String, String> map = new HashMap<>();
                map.put("albumId", mPresenter.getAlbum().getId() + "");
                MainCommonRequest.clickCommentAlert(map, new IDataCallBack<JSONObject>() {
                    @Override
                    public void onSuccess(JSONObject object) {
                        if (canUpdateUi() && object != null && object.optInt("ret", -1) == 0 && mPresenter.getAlbum() != null) {
                            mPresenter.getAlbum().setShowCommentAlert(false);
                            if (mMoreButton != null) {
                                mMoreButton.setImageResource(R.drawable.main_album_more_icon_new2);
                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });
            }
        }
    }

    private void showMoreOperationPanel() {
        if (mMoreActionDialog == null) {
            mPopupView = (LinearLayout) LayoutInflater.from(mActivity).inflate(R.layout.main_more_panel_album3, null);
            GlobalGrayManager.globalGray(mPopupView);
            mMoreActionDialog = new PopupWindow(mPopupView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
            mMoreActionDialog.setTouchable(true);
            mMoreActionDialog.setOutsideTouchable(true);
            mMoreActionDialog.setBackgroundDrawable(new BitmapDrawable(mActivity.getResources(), (Bitmap) null));
            if (BaseFragmentActivity2.sIsDarkMode) {
                int color = ColorUtil.getShareDarkPanelColor(AlbumColorUtil.getColorByAlbumId(mPresenter.getAlbumId()));
                if (color != -1) {
                    mPopupView.setBackgroundTintList(ColorStateList.valueOf(color));
                }
            }

            mMoreActionDialog.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    backgroundAlpha(1f);
                }
            });

            View morePanel = initHorizontalView(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                    hideMoreOperationPanel();
                    onItemClickImpl(parent, view, position, id);
                }
            });
            ViewGroup moreContainer = mPopupView.findViewById(R.id.main_panel_container);

            moreContainer.addView(morePanel);
            mPopupView.findViewById(R.id.main_dismiss).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    hideMoreOperationPanel();
                }
            });
            AutoTraceHelper.bindData(mPopupView.findViewById(R.id.main_dismiss), "");
        } else {
            updateValue(mPanelListener);
        }
        if (mActivity != null && mActivity.getWindow() != null) {
            ToolUtil.showPopWindow(mMoreActionDialog,
                    mActivity.getWindow().getDecorView(), Gravity.BOTTOM, 0, 0);
            backgroundAlpha(0.5f);
        }
        AlbumFragmentMarkPointManager.Companion.markPointOnShowMoreOperationPanel(mPresenter.getAlbum(), mPresenter.getPageRequestId());
    }

    private boolean isSingleTrackBuyAlbum(AlbumM albumM) {
        int priceType = albumM.getPriceTypeEnum();
        return priceType == PayManager.PAY_ALBUM_PART
                || priceType == PayManager.PAY_ALBUM_MEMBER_PART;

    }

    private View initHorizontalView(final AdapterView.OnItemClickListener listener) {
        mMoreScrollView = new HorizontalScrollView(mActivity);
        mMoreScrollView.setHorizontalScrollBarEnabled(false);
        final LinearLayout container = new LinearLayout(mActivity);
        mMoreScrollView.addView(container);
        container.setPadding(BaseUtil.dp2px(container.getContext(), 8), 0, BaseUtil.dp2px(container.getContext(), 4), 0);
        container.setOrientation(HORIZONTAL);
        updateValue(listener);
        return mMoreScrollView;
    }

    private void updateValue(final AdapterView.OnItemClickListener listener) {
        if (mMoreScrollView != null) {
            mMoreScrollView.fullScroll(FOCUS_UP);
            LinearLayout container = (LinearLayout) mMoreScrollView.getChildAt(0);
            if (container != null) {
                container.removeAllViews();
                for (int i = 0; i < models.size(); i++) {
                    final BaseDialogModel model = models.get(i);
                    final View view = LayoutInflater.from(mActivity).inflate(com.ximalaya.ting.android.host.R.layout.host_item_more, null);
                    ImageView iv = view.findViewById(com.ximalaya.ting.android.host.R.id.host_iv_more_share);
                    iv.setBackgroundResource(R.drawable.host_bg_share_icon_dync);
                    iv.setImageResource(model.checked ? model.checkedResId : model.resId);
                    TextView tv = view.findViewById(com.ximalaya.ting.android.host.R.id.host_tv_more_share);
                    String title = model.checked ? model.checkedTitle : model.title;
                    tv.setText(title);
                    TextView tvExtra = view.findViewById(com.ximalaya.ting.android.host.R.id.host_tv_more_share_extra);
                    if (model.extra instanceof String && !TextUtils.isEmpty((String) model.extra)) {
                        tvExtra.setText((String) model.extra);
                    }
                    view.setContentDescription(title);
                    AccessibilityClassNameUtil.setAccessibilityClassName(view, AccessibilityClassNameUtil.VIEW_TYPE_BUTTON);
                    container.addView(view);
                    final int finalI = i;
                    view.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (listener != null) {
                                listener.onItemClick(null, view, finalI, 0);
                            }
                        }
                    });
                    AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, "");
                    if (model.resId == com.ximalaya.ting.android.host.R.drawable.host_ic_copyright_n_n_line_regular_28) {
                        AlbumFragmentMarkPointManager.Companion.markPointOnShowCopyRightReclaimPage();
                    }
                }
            }
        }
    }

    public void toggleMoreOperationPanel() {
        if (mMoreActionDialog != null && mMoreActionDialog.isShowing()) {
            hideMoreOperationPanel();
        } else {
            showMoreOperationPanel();
        }
    }

    public int getTitleBarBottomY() {
        if (vTitleBar == null) {
            return 0;
        }
        return vTitleBar.getBottom();
    }

    private void hideMoreOperationPanel() {
        if (mMoreActionDialog == null) {
            return;
        }
        mMoreActionDialog.dismiss();
    }

    public SkipHeadTailDialog getSkipHeadTailDialog() {
        return mSkipHeadTailDialog;
    }

    private boolean canUpdateUi() {
        return mFragment.canUpdateUi();
    }
}
