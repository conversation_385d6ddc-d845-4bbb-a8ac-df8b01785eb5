package com.ximalaya.ting.android.main.albumModule.album.singleAlbum.model;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

import java.util.List;

public class AlbumFraNewUniversalAlbumCheckInModel {
    public static final int AWARD_STATUS_NONE = 1;//礼品状态: 1未符合条件
    public static final int AWARD_STATUS_STAGE_UNGET = 2;//礼品状态: 2中间奖励未领取
    public static final int AWARD_STATUS_STAGE_GOT = 3;//礼品状态: 3中间奖励已领取
    public static final int AWARD_STATUS_FINAL_UNGET = 4;//礼品状态: 4最终奖励未领取
    public static final int AWARD_STATUS_FINAL_GOT = 5;//礼品状态: 5最终奖励已领取

    public static final int AWARD_TYPE_HB = 1;//红包
    public static final int AWARD_TYPE_ALBUM = 2;//专辑收听时长
    public static final int AWARD_TYPE_GOODS = 3;//实物商品
    public static final int AWARD_TYPE_FX = 4;//返现
    public static final int AWARD_TYPE_PACKAGE = 5; //礼包

    @SerializedName("isCheck")
    private boolean isCheck;    //是否参与打卡:true -> 专辑配置了打卡而且用户参与了打卡活动
    @SerializedName("afterSaleTitle")
    private String moduleTitle;//售后页打卡提示文字(打卡赢好书/打卡成功)
    @SerializedName("checkedTrackCountsToday")
    private int checkedTrackCountsToday; //今日的收听声音数
    @SerializedName("dailyTrackCount")
    private int dailyTrackCount;     //每日打卡需要的总声音数
    @SerializedName("checkedDays")
    private int checkedDays;            //我的累计打卡总数
    @SerializedName("checkInDays")
    private int checkInDays;            //总打卡天数
    @SerializedName("expireTime")
    private long expireTime;            //打卡活动剩余时间
    @SerializedName("ruleDesc")
    private String ruleDesc;            //打卡规则
    @SerializedName("checkInAwards")
    private List<CheckInAwards> checkInAwards;

    public boolean isCheck() {
        return isCheck;
    }

    public String getModuleTitle() {
        return moduleTitle;
    }

    public int getCheckedTrackCountsToday() {
        return checkedTrackCountsToday;
    }

    public int getDailyTrackCount() {
        return dailyTrackCount;
    }

    public int getCheckedDays() {
        return checkedDays;
    }

    public int getCheckInDays() {
        return checkInDays;
    }

    public long getExpireTime() {
        return expireTime;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public List<CheckInAwards> getCheckInAwards() {
        return checkInAwards;
    }


    public long albumId;
    public String albumName;


    public void setLogDat(long albumId, String albumName) {
        this.albumId = albumId;
        this.albumName = albumName;
        if (!ToolUtil.isEmptyCollects(checkInAwards)) {
            for (CheckInAwards awards : checkInAwards) {
                awards.albumId = albumId;
                awards.albumName = albumName;
            }
        }
    }


    public static class CheckInAwards implements Comparable<CheckInAwards> {

        @SerializedName("checkInAwardId")
        private long checkInAwardId;//奖励ID
        @SerializedName("dayNo")
        private int dayNo;//对应的奖励天数
        @SerializedName("status")
        private int status;//礼品状态: 1未符合条件2中间奖励未领取 3中间奖励已领取 4最终奖励未领取 5最终奖励已领取
        @SerializedName("awardType")
        private int awardType;//奖励类型 1喜点红包 2付费专辑 3实物商品 4喜点返现
        @SerializedName("awardName")
        private String awardName;

        public long albumId;
        public String albumName;

        public long getCheckInAwardId() {
            return checkInAwardId;
        }

        public int getDayNo() {
            return dayNo;
        }

        public int getStatus() {
            return status;
        }

        public boolean setAwardGot() {
            if (status == 2) {
                status = 3;
                return true;
            } else if (status == 4) {
                status = 5;
                return true;
            }
            return false;
        }

        public int getAwardType() {
            return awardType;
        }

        public String getAwardName() {
            return awardName;
        }

        @Override
        public int compareTo(@NonNull CheckInAwards o) {
            return this.dayNo - o.dayNo;
        }


    }
}
