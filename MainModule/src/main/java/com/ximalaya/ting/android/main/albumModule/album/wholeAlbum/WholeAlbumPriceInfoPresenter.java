package com.ximalaya.ting.android.main.albumModule.album.wholeAlbum;

import android.content.Context;
import android.os.AsyncTask;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.google.gson.Gson;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.main.model.pay.WholeAlbumPriceInfo;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmabtest.ABTest;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.Map;

public class WholeAlbumPriceInfoPresenter {
    public static final String SOURCE_TYPE_PRESALE = "presale";//售前页
    public static final String SOURCE_TYPE_PLAY = "play";//播放页
    public static final String SOURCE_TYPE_PURCHASED = "purchased";//已购页
    public static final String SOURCE_TYPE_TOAST = "playToast"; // 试听结束半浮框

    private static final String ERROR_REQUESTED = "ERROR_REQUESTED";
    private final String ERROR_MSG = "请求失败";

    private WeakReference<BaseFragment2> fragmentReference;
    private String[] resultJsonArray;

    @Nullable
    private BaseFragment2 getFragment() {
        if (fragmentReference != null)
            return fragmentReference.get();
        return null;
    }

    public void loadData(BaseFragment2 fragment, final long albumId, final String source, @NonNull final DataCallback dataCallback) {
        loadData(fragment, albumId, null, source, dataCallback);
    }

    public void loadData(BaseFragment2 fragment, final long albumId, final String activityParam, final String source, @NonNull final DataCallback dataCallback) {
        final Context context = BaseApplication.getMyApplicationContext();
        fragmentReference = new WeakReference<>(fragment);
        resultJsonArray = new String[2];

        // 请求AB,命中就走小会员购买
        // 儿童会员的展示优先级：0：大会员+儿童会员同时存在时优先大会员，1：大会员+儿童会员同时存在时优先儿童会员
        boolean isABChild = ABTest.getString("childVIPlaunchVIP", "").contains("B");
        Map<String, String> params = new ArrayMap<>(3);
        params.put("albumId", String.valueOf(albumId));
        params.put("version", DeviceUtil.getVersion(context));
        params.put("source", source);
        params.put("showChildrenVipType", String.valueOf(isABChild ? 1 : 0));
        if (null != activityParam) {
            try {
                params.put("context", URLEncoder.encode(activityParam, "utf-8"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        MainCommonRequest.wholeAlbumPriceInfoDetail(albumId, params, new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String object) {
                BaseFragment2 fra = getFragment();
                if (fra != null && fra.canUpdateUi()) {
                    if (TextUtils.isEmpty(object)) {
                        CustomToast.showFailToast(ERROR_MSG);
                        dataCallback.onError(false);
                    } else {
                        resultJsonArray[0] = object;
                        loadDynamicData(context, albumId, activityParam, source, dataCallback);
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                BaseFragment2 fra = getFragment();
                if (fra != null && fra.canUpdateUi()) {
                    if (!(code == -1 || code == 3)) {
                        dataCallback.onError(true);
                        if (TextUtils.isEmpty(message))
                            message = ERROR_MSG;
                        CustomToast.showFailToast(message);
                    } else {
                        dataCallback.onError(false);
                    }
                }
            }
        });
    }

    private void loadDynamicData(Context context, long albumId, final String activityParam
            , String source, @NonNull final DataCallback dataCallback) {
        Map<String, String> params = new ArrayMap<>(3);
        // 请求AB,命中就走小会员购买
        // 儿童会员的展示优先级：0：大会员+儿童会员同时存在时优先大会员，1：大会员+儿童会员同时存在时优先儿童会员
        boolean isABChild = ABTest.getString("childVIPlaunchVIP", "").contains("B");
        params.put("albumId", String.valueOf(albumId));
        params.put("version", DeviceUtil.getVersion(context));
        params.put("source", source);
        params.put("showChildrenVipType", String.valueOf(isABChild ? 1 : 0));
        if (null != activityParam) {
            try {
                params.put("context", URLEncoder.encode(activityParam, "utf-8"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        MainCommonRequest.wholeAlbumPriceInfoDynamic(albumId, params, new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String object) {
                BaseFragment2 fra = getFragment();
                if (fra != null && fra.canUpdateUi()) {
                    if (TextUtils.isEmpty(object)) {
                        resultJsonArray[1] = ERROR_REQUESTED;
                    } else {
                        resultJsonArray[1] = object;
                    }
                    parseData(dataCallback);
                }
            }

            @Override
            public void onError(int code, String message) {
                BaseFragment2 fra = getFragment();
                if (fra != null && fra.canUpdateUi()) {
                    if (code == -1 || code == 3 || code == 76) {
                        resultJsonArray[1] = ERROR_REQUESTED;
                        parseData(dataCallback);
                    } else {
                        dataCallback.onError(true);
                        if (TextUtils.isEmpty(message))
                            message = ERROR_MSG;
                        CustomToast.showFailToast(message);
                    }
                }
            }
        });
    }

    private void parseData(DataCallback dataCallback) {
        DataParseTask task = new DataParseTask(dataCallback);
        task.execute(resultJsonArray);
    }


    static class DataParseTask extends AsyncTask<String, Void, WholeAlbumPriceInfo> {
        private DataCallback dataCallback;

        public DataParseTask(DataCallback dataCallback) {
            this.dataCallback = dataCallback;
        }

        @Override
        protected void onPostExecute(WholeAlbumPriceInfo data) {
            if (data != null) {
                dataCallback.onSuccess(data);
            } else {
                dataCallback.onError(false);
            }
        }

        @Override
        protected WholeAlbumPriceInfo doInBackground(String... strings) {
            if (strings == null || strings.length != 2
                    || TextUtils.isEmpty(strings[0])
                    || TextUtils.isEmpty(strings[1])) {
                return null;
            }

            try {
                JSONObject detailJsonObject = new JSONObject(strings[0]);
                detailJsonObject = detailJsonObject.getJSONObject("data");
                if (!ERROR_REQUESTED.equals(strings[1])) {
                    try {
                        JSONObject dynamicJsonObject = new JSONObject(strings[1]);
                        dynamicJsonObject = dynamicJsonObject.getJSONObject("data");
                        Iterator<String> iterator = dynamicJsonObject.keys();
                        while (iterator.hasNext()) {
                            String key = iterator.next();
                            Object valueObject = dynamicJsonObject.opt(key);
                            detailJsonObject.put(key, valueObject);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                Gson gson = new Gson();
                WholeAlbumPriceInfo priceInfo = gson.fromJson(detailJsonObject.toString(), WholeAlbumPriceInfo.class);
                if (priceInfo.purchaseChannelsJsonArray != null) {
                    WholeAlbumPriceInfo.parsePurchaseChannels(priceInfo, priceInfo.purchaseChannelsJsonArray.toString());
                }
                return priceInfo;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }


    public interface DataCallback {
        void onSuccess(WholeAlbumPriceInfo data);

        /**
         * @param isBusinessException 是否是业务异常，是业务异常是，不进程兜底逻辑处理
         */
        void onError(boolean isBusinessException);
    }
}
