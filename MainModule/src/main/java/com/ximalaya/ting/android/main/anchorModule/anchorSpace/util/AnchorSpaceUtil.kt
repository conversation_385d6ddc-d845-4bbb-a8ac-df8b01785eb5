@file:JvmMultifileClass
@file:JvmName("AnchorSpaceUtil")

package com.ximalaya.ting.android.main.anchorModule.anchorSpace.util

import android.app.Activity
import android.content.ComponentName
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Typeface
import android.text.TextUtils
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.TextView
import androidx.collection.ArrayMap
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.model.account.UserInfoModel
import com.ximalaya.ting.android.host.util.anchor.AnchorAbUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.anchorModule.AnchorSpaceTraceUtils
import com.ximalaya.ting.android.main.mine.extension.bindData
import com.ximalaya.ting.android.main.mine.extension.onSingleClick
import com.ximalaya.ting.android.main.model.anchor.AnchorSpaceHomeModel
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.xmabtest.ABTest

/**
 * Created by dekai.liu on 5/6/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber ***********
 */

typealias OnTrace = () -> Unit

fun useNewLiveMode(): Boolean {
    if (ConstantsOpenSdk.isDebug) {
        val debugInt = BaseUtil.getIntSystemProperties("debug.anchor.live")
        if (debugInt > 0) {
            return true
        }
        if (debugInt < 0) {
            return false
        }
    }
    return ABTest.getString("anchor_config", "base").equals("test")
}

@JvmOverloads
fun newAnchorSpaceFragment(
    uid: Long,
    playSource: Int = 0,
    videoFirst: Boolean = false
): BaseFragment2 {
    return com.ximalaya.ting.android.main.anchorModule.v5.AnchorSpaceFragment.newInstance(
        uid,
        playSource
    )
}

fun getLargeAvatar(userInfo: UserInfoModel?): String {
    if (userInfo == null) {
        return ""
    }
    return if (!userInfo.mobileLargeLogo.isNullOrEmpty()) {
        userInfo.mobileLargeLogo
    } else if (!userInfo.largeLogo.isNullOrEmpty()) {
        userInfo.largeLogo
    } else if (!userInfo.mobileMiddleLogo.isNullOrEmpty()) {
        userInfo.mobileMiddleLogo
    } else if (!userInfo.middleLogo.isNullOrEmpty()) {
        userInfo.middleLogo
    } else if (!userInfo.mobileSmallLogo.isNullOrEmpty()) {
        userInfo.mobileSmallLogo
    } else if (!userInfo.smallLogo.isNullOrEmpty()) {
        userInfo.smallLogo
    } else {
        ""
    }
}

const val LABEL_KEY_TITLE = "title" // 标签title
const val LABEL_KEY_TYPE = "type" // 标签跳转类型
const val LABEL_KEY_SEX = "sex" // 性别信息
const val LABEL_KEY_ID = "id" // id信息

enum class LabelType(val title: String = "") {
    COOPERATION("找我合作"), // 找我合作
    SEX_ADDRESS, // 性别+地区
    CONSTELLATION, // 星座
    WEBSITE("官网链接"), // 官网链接
    COPYRIGHT("版权方"), // 版权方
    WEIBO("微博"), // 微博
    MCN_TAG, // MCN机构账号
    LIKE // 兴趣
}

/**
 * 构建标签view
 */
fun buildLabelItemView(
    fragment: BaseFragment2?,
    item: ArrayMap<String, Any>,
    model: AnchorSpaceHomeModel?
): View? {
    val title = item[LABEL_KEY_TITLE] as? String
    val type = item[LABEL_KEY_TYPE] as? LabelType
    if (type == null || model == null || fragment?.context == null || title?.contains("#") != false) {
        return null
    }
    val textView = TextView(fragment.context)
    textView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
    textView.setBackgroundResource(R.drawable.main_anchor_space_label_normal_bg)
    textView.maxLines = 1
    textView.maxEms = 13
    textView.gravity = Gravity.CENTER_VERTICAL
    textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, BaseUtil.sp2px(fragment.context, 9f).toFloat())
    textView.ellipsize = TextUtils.TruncateAt.END
    textView.typeface = Typeface.create("PingFangSC-Regular", Typeface.NORMAL)
    textView.setTextColor(0xFF797F89.toInt())
    textView.includeFontPadding = false
    textView.text = title
    when (type) {
        LabelType.COOPERATION -> {
            textView.setBackgroundResource(R.drawable.main_anchor_space_cooperation_label_bg)
            textView.setTextColor(0xFFF8725C.toInt())
            textView.setPadding(6.dp, 0, 6.dp, 0)
        }

        LabelType.SEX_ADDRESS -> {
            val dataMap = mutableMapOf<String, String>()
            val sex = item[LABEL_KEY_SEX] as? Int
            if (sex == null || sex == 0) {
                textView.setPadding(6.dp, 0, 6.dp, 0)
            } else {
                val sexResId = if (sex == 1) {
                    dataMap["sex"] = "男"
                    R.drawable.main_ic_male
                } else {
                    dataMap["sex"] = "女"
                    R.drawable.main_ic_female
                }
                textView.setCompoundDrawablesWithIntrinsicBounds(sexResId, 0, 0, 0)
                if (title.isNullOrEmpty()) {
                    textView.compoundDrawablePadding = 0
                    textView.setPadding(10.dp, 0, 10.dp, 0)
                } else {
                    textView.compoundDrawablePadding = 2.dp
                    textView.setPadding(4.dp, 0, 6.dp, 0)
                    dataMap["address"] = title
                }
            }
            if (dataMap.isNotEmpty()) {
                textView.bindData(data = dataMap)
            }
        }

        LabelType.CONSTELLATION,
        LabelType.LIKE -> {
            textView.setPadding(6.dp, 0, 6.dp, 0)
        }

        LabelType.MCN_TAG -> {
            textView.setCompoundDrawablesWithIntrinsicBounds(R.drawable.main_ic_mc_label, 0, 0, 0)
            textView.compoundDrawablePadding = 2.dp
            textView.setPadding(4.dp, 0, 6.dp, 0)
        }

        LabelType.WEBSITE -> {
            textView.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.main_ic_anchor_space_user_dialog_website,
                0,
                0,
                0
            )
            textView.compoundDrawablePadding = 2.dp
            textView.setPadding(4.dp, 0, 6.dp, 0)
        }

        LabelType.COPYRIGHT -> {
            textView.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.main_ic_anchor_space_label_copyright,
                0,
                0,
                0
            )
            textView.compoundDrawablePadding = 2.dp
            textView.setPadding(4.dp, 0, 6.dp, 0)
        }

        LabelType.WEIBO -> {
            textView.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.main_ic_anchor_space_label_weibo,
                0,
                0,
                0
            )
            textView.compoundDrawablePadding = 2.dp
            textView.includeFontPadding = false
            textView.setPadding(4.dp, 0, 6.dp, 0)
        }
    }
    textView.setOnClickListener {
        it.onSingleClick {
            when (type) {
                LabelType.COOPERATION -> clickCooperation(fragment, model)
                LabelType.WEBSITE -> clickWebsite(fragment, model)
                LabelType.COPYRIGHT -> clickCopyright(fragment, model)
                LabelType.WEIBO -> clickWB(fragment, model)
                else -> {
                    // else不需要处理
                }
            }
        }
    }
    textView.bindData(data = title)
    return textView
}

private fun clickWebsite(fragment: BaseFragment2?, model: AnchorSpaceHomeModel?) {
    dealLink(fragment?.activity, model?.officalWebsiteUrl)
}

private fun clickCopyright(fragment: BaseFragment2?, model: AnchorSpaceHomeModel?) {
    dealLink(fragment?.activity, model?.copyrightDetailH5Url)
}

private fun clickCooperation(fragment: BaseFragment2?, model: AnchorSpaceHomeModel?) {
    dealLink(fragment?.activity, model?.officialCooperationUrl)
}

private fun dealLink(activity: Activity?, link: String?) {
    if (activity is MainActivity && !link.isNullOrEmpty()) {
        NativeHybridFragment.start(activity, link, true)
    }
}

private fun clickWB(fragment: BaseFragment2?, model: AnchorSpaceHomeModel?) {
    if (fragment?.context == null || !fragment.canUpdateUi()) {
        return
    }

    if (model?.sinaLoginUid.isNullOrEmpty()) {
        return
    }

    val intent = Intent()
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    val cmp = ComponentName(
        "com.sina.weibo",
        "com.sina.weibo.page.ProfileInfoActivity"
    )
    intent.component = cmp
    intent.putExtra("luicode", "10000360")
    intent.putExtra("lfid", "OP_36370827")
    intent.putExtra("uid", model?.sinaLoginUid)
    intent.putExtra("wm", "90069_90001")
    val packageManager: PackageManager = fragment.context?.packageManager ?: return
    val info = packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)
    if (info?.activityInfo != null) {
        try {
            fragment.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    } else {
        val url =
            "http://m.weibo.cn/u/${model?.sinaLoginUid}?luicode=10000360&lfid=OP_36370827&featurecode=10000360_-_OP_36370827&wm=90069_90001"
        dealLink(fragment.activity, url)
    }
    AnchorSpaceTraceUtils.trackingWeiBo(model?.uid ?: 0)
}

private val mTTSWhiteList: List<String> by lazy {
    ConfigureCenter.getInstance()
        .getString(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.ITEM_GRYTTSBMD, "")
        .split(",")
}

fun isInTTSWhiteList(uid: Long): Boolean {
    return uid.toString() in mTTSWhiteList
}

fun getAnchorGradeIconList(): List<Int> {
    return listOf(
        -1,
        R.drawable.main_anchor_space_grade_user_1,
        R.drawable.main_anchor_space_grade_user_2,
        R.drawable.main_anchor_space_grade_user_3,
        R.drawable.main_anchor_space_grade_user_4,
        R.drawable.main_anchor_space_grade_user_5,
        R.drawable.main_anchor_space_grade_user_6,
        R.drawable.main_anchor_space_grade_user_7,
        R.drawable.main_anchor_space_grade_user_8,
        R.drawable.main_anchor_space_grade_user_9,
        R.drawable.main_anchor_space_grade_user_10,
        R.drawable.main_anchor_space_grade_user_11,
        R.drawable.main_anchor_space_grade_user_12,
        R.drawable.main_anchor_space_grade_user_13,
        R.drawable.main_anchor_space_grade_user_14,
        R.drawable.main_anchor_space_grade_user_15,
        R.drawable.main_anchor_space_grade_user_16
    )
}