package com.ximalaya.ting.android.main.categoryModule.categorycontent;

import android.content.Context;
import androidx.recyclerview.widget.GridLayoutManager;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.categoryModule.adapter.CategoryKidAgeInModuleAdapter;
import com.ximalaya.ting.android.main.categoryModule.adapter.CategoryRecommendNewAdapter;
import com.ximalaya.ting.android.main.model.category.CategoryKidAgeWithChildInfo;
import com.ximalaya.ting.android.main.model.category.ChildInfo;
import com.ximalaya.ting.android.main.model.kid.KidAge;
import com.ximalaya.ting.android.main.view.GridItemDecoration;
import com.ximalaya.ting.android.main.view.RecyclerViewCanDisallowIntercept;

import java.util.List;

/**
 * Created by marco 2019/5/16
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13816107216
 */
public class CategoryKidAgeInfoModuleAdapterProvider implements IMulitViewTypeViewAndData {
    private static final int SPAN_COUNT = 2;
    private BaseFragment2 mFragment;
    private IExtraDataProvider mExtraDataProvider;
    private String mCategoryId;

    public CategoryKidAgeInfoModuleAdapterProvider(BaseFragment2 fragment, IExtraDataProvider extraDataProvider) {
        mFragment = fragment;
        mExtraDataProvider = extraDataProvider;
    }

    private String getCategoryId() {
        if (TextUtils.isEmpty(mCategoryId)) {
            if (mExtraDataProvider != null) {
                Object id = mExtraDataProvider.getExtraData(CategoryRecommendNewAdapter.EXTRA_CATE_ID);
                if (id instanceof String) {
                    try {
                        mCategoryId = (String) id;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return mCategoryId;
    }

    @Override
    public void bindViewDatas(HolderAdapter.BaseViewHolder baseViewHolder, ItemModel t, View convertView, int
            position) {
        if (baseViewHolder == null || t == null || t.getObject() == null) {
            return;
        }

        final CategoryKidAgeInfoModuleAdapterProvider.ViewHolder holder = (CategoryKidAgeInfoModuleAdapterProvider.ViewHolder) baseViewHolder;
        if (t.getObject() instanceof CategoryKidAgeWithChildInfo) {
            final CategoryKidAgeWithChildInfo module = (CategoryKidAgeWithChildInfo) t.getObject();
            holder.tvTitle.setText(module.getTitle());
            holder.tvMore.setVisibility(View.INVISIBLE);
            if (!ToolUtil.isEmptyCollects(module.getKidAgeList())) {
                holder.adapter.setKidAgeList(module.getKidAgeList());
                holder.adapter.notifyDataSetChanged();
            }
            holder.adapter.setChildInfo(module.getChildInfo());
            holder.adapter.setCategoryId(getCategoryId());
            statModuleViewed(module.getKidAgeList(), module.getChildInfo());
        }
    }

    private void statModuleViewed(List<KidAge> list, ChildInfo childInfo) {
        if (list == null || list.size() == 0)
            return;

        StringBuilder moduleList = new StringBuilder();
        KidAge kidAge;
        for (int i = 0; i < list.size(); i++) {
            kidAge = list.get(i);
            if (i == list.size() - 1) {
                if (childInfo == null) {
                    moduleList.append("设置宝宝信息");
                } else {
                    moduleList.append(kidAge.getDisplayName());
                }
            } else {
                moduleList.append(kidAge.getDisplayName()).append(",");
            }
        }

        new UserTracking()
                .setModuleType("ageUnit")
                .setSrcPage("category")
                .setSrcPageId(getCategoryId())
                .setId("7113")
                .setModuleList(moduleList.toString())
                .statIting(XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
    }

    @Override
    public View getView(LayoutInflater layoutInflater, int position, ViewGroup parent) {
        return layoutInflater.inflate(R.layout.main_item_category_recommend_normal_module, parent, false);
    }

    @Override
    public HolderAdapter.BaseViewHolder buildHolder(View convertView) {
        CategoryKidAgeInfoModuleAdapterProvider.ViewHolder viewHolder = new CategoryKidAgeInfoModuleAdapterProvider.ViewHolder(convertView);
        initRvSpecial(viewHolder);
        return viewHolder;
    }

    private void initRvSpecial(CategoryKidAgeInfoModuleAdapterProvider.ViewHolder holder) {
        Context context = BaseApplication.getTopActivity();
        holder.rvSpecial.setLayoutManager(new GridLayoutManager(context, SPAN_COUNT));
        int itemWidth = (BaseUtil.getScreenWidth(context) - BaseUtil.dp2px(context, 8 * (SPAN_COUNT + 1) + 8 * 2)) / SPAN_COUNT;
        holder.adapter = new CategoryKidAgeInModuleAdapter(mFragment, itemWidth);
        holder.rvSpecial.setAdapter(holder.adapter);
        int spacing = BaseUtil.dp2px(context, 8);
        holder.rvSpecial.addItemDecoration(new GridItemDecoration(spacing, SPAN_COUNT));
        int paddingLeftAndRight = BaseUtil.dp2px(context, 8);
        holder.rvSpecial.setPadding(paddingLeftAndRight, BaseUtil.dp2px(context, 12), paddingLeftAndRight, 0);
    }

    private static final class ViewHolder extends HolderAdapter.BaseViewHolder {

        private TextView tvTitle;
        private RecyclerViewCanDisallowIntercept rvSpecial;
        private TextView tvMore;
        private CategoryKidAgeInModuleAdapter adapter;

        public ViewHolder(View rootView) {
            tvTitle = rootView.findViewById(R.id.main_tv_title);
            rvSpecial = rootView.findViewById(R.id.main_rv_items);
            tvMore = rootView.findViewById(R.id.main_tv_more);
        }
    }

}
