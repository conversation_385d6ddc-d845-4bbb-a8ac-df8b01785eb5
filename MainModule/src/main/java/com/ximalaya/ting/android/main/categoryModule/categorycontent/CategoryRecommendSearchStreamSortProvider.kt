package com.ximalaya.ting.android.main.categoryModule.categorycontent

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.ximalaya.ting.android.framework.adapter.HolderAdapter
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndData
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryRecommendFragment
import com.ximalaya.ting.android.main.model.album.MainAlbumMList
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper

/**
 * Created by changle.fang on 2021/4/15.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
class CategoryRecommendSearchStreamSortProvider(val fragment: BaseFragment2) : IMulitViewTypeViewAndData<CategoryRecommendSearchStreamSortProvider.ViewHolder, MainAlbumMList> {

    override fun bindViewDatas(holder: ViewHolder?, t: ItemModel<MainAlbumMList>?, convertView: View?, position: Int) {
        holder ?: return
        setClickListener(holder.tvHot, holder, CAL_DIMEN_HOT)
        setClickListener(holder.tvClassic, holder, CAL_DIMEN_CLASSIC)
        setClickListener(holder.tvRecent, holder, CAL_DIMEN_RECENT)
    }

    private fun setClickListener(view: TextView, holder: ViewHolder, sortType: String) {
        view.setOnClickListener {
            select(it, holder)
            (fragment as? CategoryRecommendFragment)?.reloadSearchStreamData(sortType)
        }
        AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, "")
    }

    private fun select(view: View, holder: ViewHolder?) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return
        }
        holder ?: return
        if (view != holder.lastSelView) {
            holder.lastSelView?.isSelected = false
            holder.lastSelView?.background = null
            view.isSelected = true
            view.setBackgroundResource(R.drawable.main_bg_category_metadata_item)
            holder.lastSelView = view
        }
    }

    override fun getView(layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?): View? {
        return layoutInflater?.inflate(R.layout.main_item_category_search_stream_sort, parent, false)
    }

    override fun buildHolder(convertView: View?): ViewHolder? {
        convertView?.apply {
            return ViewHolder(this)
        }
        return null
    }

    class ViewHolder(val view: View) : HolderAdapter.BaseViewHolder() {
        val tvHot: TextView = view.findViewById(R.id.main_tv_hot)
        val tvClassic: TextView = view.findViewById(R.id.main_tv_classic)
        val tvRecent: TextView = view.findViewById(R.id.main_tv_recent)
        var lastSelView: View? = null

        init {
            tvHot.isSelected = true
            tvHot.setBackgroundResource(R.drawable.main_bg_category_metadata_item)
            lastSelView = tvHot
        }
    }

    companion object {
        const val CAL_DIMEN_RECENT = "recent"
        const val CAL_DIMEN_HOT = "hot"
        const val CAL_DIMEN_CLASSIC = "classic"
    }
}