package com.ximalaya.ting.android.main.categoryModule.page.item.cell

import android.text.TextUtils
import android.view.View
import com.tmall.wireless.tangram.dataparser.concrete.IStyleProvider
import com.tmall.wireless.tangram.dataparser.concrete.Style
import com.tmall.wireless.tangram.structure.BaseCell
import com.tmall.wireless.tangram.structure.model.Trace
import com.tmall.wireless.tangram.structure.viewcreator.ViewHolderCreator
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.categoryModule.page.item.viewholder.ChannelFocusImgMidViewHolder
import com.ximalaya.ting.android.main.categoryModule.util.CommonUtil
import org.json.JSONArray
import org.json.JSONObject

/**
 * Author: <PERSON>
 * Email: <EMAIL>
 * Date: 2022/7/14
 * Description：header-playall
 */
class ChannelFocusImgMidCell : BaseCell<View>(), IStyleProvider {
    override fun postBindView(view: View) {
        super.postBindView(view)
        val holder = ViewHolderCreator.getViewHolderFromView(view) as? ChannelFocusImgMidViewHolder
            ?: return
        val data = optParam("data") as? JSONObject ?: return
        val cover = data.optString("cover")
        val landingPage = data.optString("landingPage")
        val id = data.optLong("id")
        ImageManager.from(mContext).displayImage(holder.ivImage, cover, R.drawable.main_daily_news3_manuscript_default_bg)
        view.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            if (TextUtils.isEmpty(landingPage)) {
                return@setOnClickListener
            }
            // 分类_推荐-运营位(活动)  点击事件
            val trace = Trace()
                .click(16504) // 用户点击时上报
                .put("operationId", id.toString())
                .put("materialId", id.toString())
                .put("url", landingPage)
                .put("positionNew", mTracePositionNew.toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", mTraceModuleType)
            sendTrace(trace)
            NativeHybridFragment.start(BaseApplication.getMainActivity() as? MainActivity, landingPage, true)
        }
        // 分类_推荐-运营位(活动)  控件曝光
        Trace()
            .setMetaId(16505)
            .setServiceId("slipPage")
            .put("operationId", id.toString())
            .put("materialId", id.toString())
            .put("url", landingPage)
            .put("positionNew", mTracePositionNew.toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put("moduleName", mTraceModuleType)
            .bindTrace(view)
    }

    override fun styleForCard(): JSONObject {
        return JSONObject().apply {
            var paddings = JSONArray()
            paddings.put("${CommonUtil.getDimenPxForAutoSize(com.ximalaya.ting.android.host.R.dimen.host_y12)}px")
            paddings.put("${CommonUtil.getDimenPxForAutoSize(com.ximalaya.ting.android.host.R.dimen.host_x16)}px")
            paddings.put("${CommonUtil.getDimenPxForAutoSize(com.ximalaya.ting.android.host.R.dimen.host_y12)}px")
            paddings.put("${CommonUtil.getDimenPxForAutoSize(com.ximalaya.ting.android.host.R.dimen.host_x16)}px")
            put(Style.KEY_PADDING, paddings)
        }
    }
}