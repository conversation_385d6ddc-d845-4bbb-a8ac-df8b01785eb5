package com.ximalaya.ting.android.main.categoryModule.page.item.cell

import android.graphics.Typeface
import android.text.TextUtils
import android.view.View
import com.google.gson.Gson
import com.tmall.wireless.tangram.dataparser.concrete.IStyleProvider
import com.tmall.wireless.tangram.dataparser.concrete.Style
import com.tmall.wireless.tangram.structure.BaseCell
import com.tmall.wireless.tangram.structure.model.Trace
import com.tmall.wireless.tangram.structure.viewcreator.ViewHolderCreator
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.categoryModule.page.dialog.ChannelSelectAgeDialog
import com.ximalaya.ting.android.main.categoryModule.page.item.viewholder.ChannelSelectAgeViewHolder
import com.ximalaya.ting.android.main.categoryModule.util.CommonUtil
import com.ximalaya.ting.android.main.model.channel.ChannelSelectAgeModel
import org.json.JSONArray
import org.json.JSONObject

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2022/8/4
 * Description：
 */
class ChannelSelectAgeCell : BaseCell<View>(), IStyleProvider {

    private var mAgeModel: ChannelSelectAgeModel? = null
    override fun postBindView(view: View) {
        super.postBindView(view)
        val holder = ViewHolderCreator.getViewHolderFromView(view) as? ChannelSelectAgeViewHolder
            ?: return
        val data = optParam("data") as? JSONObject ?: return
        val title = data.optString("title")
        val summary = data.optString("summary")
        val extString = data.optString("ext")
        if (!TextUtils.isEmpty(extString)) {
            mAgeModel = Gson().fromJson(extString, ChannelSelectAgeModel::class.java)
        }

        holder.tvTitle?.text = title
        holder.tvSubTitle?.text = summary
        if (mAgeModel == null || mAgeModel!!.gender == -1) {
            holder.ivEdit?.visibility = View.VISIBLE
            holder.ivGender?.visibility = View.GONE
            holder.tvSubTitle?.setTextColor(mContext.resources.getColor(R.color.main_color_666666_8d8d91))
            holder.tvSubTitle?.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
        } else if (mAgeModel != null) {
            holder.ivEdit?.visibility = View.GONE
            holder.ivGender?.visibility = View.VISIBLE
            holder.tvSubTitle?.setTextColor(mContext.resources.getColor(R.color.main_color_333333_dcdcdc))
            holder.tvSubTitle?.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
            holder.ivGender?.setImageResource(if (mAgeModel!!.gender == 1) R.drawable.main_channel_ic_children_boy else R.drawable.main_channel_ic_children_girl)
        }

        view.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(view.context)
                return@setOnClickListener
            }
            if (mAgeModel != null && curFragment is BaseFragment2) {
                val selectFra = ChannelSelectAgeDialog.newInstance(mAgeModel, categoryId, mTraceModuleType)
                selectFra.setOnSaveSuccessListener { model ->
                    if (model != null && canUpdateUi()) {
                        refreshData()
                    }
                }
                selectFra.show(curFragment.childFragmentManager, "ChannelSelectAgeDialog")
            }
            // 分类_推荐-儿童年龄填写组件  点击事件
            sendTrace(
                Trace()
                    .click(50323) // 用户点击时上报
                    .put("isLogIn", if (UserInfoMannage.hasLogined()) "是" else "否")
                    .put("age", mAgeModel?.ageDesc)
                    .put("sex", mAgeModel?.genderDesc)
                    .put("vipStatus", if (UserInfoMannage.isVipUser()) "是" else "否")
                    .put("currPage", "categoryRecommend")
            )
        }
        // 分类_推荐-儿童年龄填写组件  控件曝光
        Trace()
            .setMetaId(50324)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("isLogIn", if (UserInfoMannage.hasLogined()) "是" else "否")
            .put("age", mAgeModel?.ageDesc)
            .put("sex", mAgeModel?.genderDesc)
            .put("vipStatus", if (UserInfoMannage.isVipUser()) "是" else "否")
            .put("currPage", "categoryRecommend")
            .put("exploreType", "categoryRecommend") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
            .bindTrace(view)
    }

    override fun styleForCard(): JSONObject {
        val array = JSONArray()
        array.put(0)
        array.put(0)
        array.put(0)
        array.put(0)
        val paddings = JSONArray()
        paddings.put(0)
        paddings.put("${CommonUtil.getDimenPxForAutoSize(com.ximalaya.ting.android.host.R.dimen.host_x16)}px")
        paddings.put("${CommonUtil.getDimenPxForAutoSize(com.ximalaya.ting.android.host.R.dimen.host_y10)}px")
        paddings.put("${CommonUtil.getDimenPxForAutoSize(com.ximalaya.ting.android.host.R.dimen.host_x16)}px")
        return JSONObject().put(Style.KEY_MARGIN, array).put(Style.KEY_PADDING, paddings)
    }
}