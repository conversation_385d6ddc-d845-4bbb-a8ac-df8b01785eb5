package com.ximalaya.ting.android.main.categoryModule.page.item.headerCell

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.tmall.wireless.tangram.structure.viewcreator.ViewHolderCreator
import com.ximalaya.ting.android.main.R

/**
 * Author: <PERSON>
 * <PERSON>: <EMAIL>
 * Date: 2022/8/1
 * Description：
 */
class ChannelHeaderTagGroupViewHolder(context: Context?) : ViewHolderCreator.ViewHolder(context) {
    var tvTitle: TextView? = null
    var recyclerView: RecyclerView? = null

    override fun onRootViewCreated(view: View?) {
        tvTitle = view?.findViewById(R.id.main_title_tv)
        recyclerView = view?.findViewById(R.id.main_recycler)
    }
}