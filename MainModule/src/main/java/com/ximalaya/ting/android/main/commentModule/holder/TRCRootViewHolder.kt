package com.ximalaya.ting.android.main.commentModule.holder

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Space
import android.widget.TextView
import com.airbnb.lottie.LottieAnimationView
import com.ximalaya.ting.android.framework.adapter.HolderAdapter
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.util.view.VIEW_TYPE_BUTTON
import com.ximalaya.ting.android.host.util.view.setAccessibilityClassName
import com.ximalaya.ting.android.host.view.CommonIPLayout
import com.ximalaya.ting.android.host.view.EllipsizeLayout
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.view.text.StaticLayoutView

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2022/7/28
 */
class TRCRootViewHolder(var convertView: View) : HolderAdapter.BaseViewHolder() {
    val topSpace: Space = convertView.findViewById(R.id.main_top_space)
    val topDivider: View = convertView.findViewById(R.id.main_top_divider)
    val ivSkin: ImageView = convertView.findViewById(R.id.main_iv_comment_skin)
    val spaceForSkin: Space = convertView.findViewById(R.id.main_space_for_skin_area)
    val ellipsizeLayoutAuthor: EllipsizeLayout = convertView.findViewById(R.id.main_ll_author)
    val tvLikeInfo: TextView = convertView.findViewById(R.id.main_tv_like_info)
    val vPic: LinearLayout = convertView.findViewById(R.id.main_v_pic)
    val layoutLike: View = convertView.findViewById<View>(R.id.main_layout_like).apply {
        setAccessibilityClassName(VIEW_TYPE_BUTTON)
    }
    val ivLike: ImageView = layoutLike.findViewById(R.id.main_iv_like)
    val lottieLike: LottieAnimationView = layoutLike.findViewById<LottieAnimationView>(R.id.main_lottie_like).apply {
        setFailureListener {}
        addAnimatorListener(object : AnimatorListener {
            override fun onAnimationStart(animation: Animator?) {
            }

            override fun onAnimationEnd(animation: Animator?) {
                ivLike.visibility = View.VISIBLE
                <EMAIL> = View.GONE
            }

            override fun onAnimationCancel(animation: Animator?) {
                ivLike.visibility = View.VISIBLE
                <EMAIL> = View.GONE
            }

            override fun onAnimationRepeat(animation: Animator?) {
            }
        })
    }
    val tvLikeCount: TextView = convertView.findViewById(R.id.main_tv_like_count)
    val ivHate: ImageView = convertView.findViewById(R.id.main_iv_hate)
    val ivAvatar: RoundImageView = convertView.findViewById(R.id.main_comment_image)
    val ivAvatarBox: ImageView = convertView.findViewById(R.id.main_iv_avatar_box)
    val tvFollowStatus: TextView = convertView.findViewById(R.id.main_comment_follow_icon)
    val ivTag: ImageView = convertView.findViewById(R.id.main_iv_tag)
    val tvName: TextView = convertView.findViewById(R.id.main_comment_name)
    val tvComment: StaticLayoutView = convertView.findViewById(R.id.main_comment)
    val tvDate: TextView = convertView.findViewById(R.id.main_create_time)
    val vIPDivider: View = convertView.findViewById(R.id.main_v_ip_divider)
    val tvIP: TextView = convertView.findViewById(R.id.main_tv_ip)
    val tvPkChoice: TextView = convertView.findViewById(R.id.main_tv_pk_choice)
    val ivGoodLogoBg: ImageView = convertView.findViewById(R.id.main_iv_good_comment_bg)
    val ivGoodLogo: ImageView = convertView.findViewById(R.id.main_iv_good_comment_logo)
}