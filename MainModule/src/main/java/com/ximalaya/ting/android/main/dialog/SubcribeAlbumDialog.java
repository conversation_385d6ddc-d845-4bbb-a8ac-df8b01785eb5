package com.ximalaya.ting.android.main.dialog;

import android.content.DialogInterface;
import android.os.Bundle;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.main.R;

/**
 * <AUTHOR>
 * @date 17/12/18
 */

public class SubcribeAlbumDialog extends BaseDialogFragment implements View.OnClickListener {
    public static final String TAG = SubcribeAlbumDialog.class.getSimpleName();

    private AlbumM albumM;
    private String mMessage;
    private String mTitle;

    public void setResultListener(IOnSubscribeResult mListener) {
        this.mListener = mListener;
    }

    private IOnSubscribeResult mListener;

    public static SubcribeAlbumDialog newInstance(AlbumM albumM, String title, String msg){
        Bundle args = new Bundle();
        args.putParcelable(BundleKeyConstants.KEY_ALBUM, albumM);
        args.putString("title", title);
        args.putString("message", msg);
        SubcribeAlbumDialog dialog = new SubcribeAlbumDialog();
        dialog.setArguments(args);
        return dialog;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(com.ximalaya.ting.android.host.R.style.host_share_dialog, com.ximalaya.ting.android.host.R.style.host_share_dialog);
        if(getArguments() != null){
            albumM = getArguments().getParcelable(BundleKeyConstants.KEY_ALBUM);
            mMessage = getArguments().getString("message");
            mTitle = getArguments().getString("title");
        }
        if (albumM==null) {
            return;
        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        final Window window = getDialog().getWindow();
        if (window == null) {
            dismissAllowingStateLoss();
            return null;
        }
        View v  = inflater.inflate(R.layout.main_dialog_subcribe_album, ((ViewGroup) window.findViewById(android.R.id.content)), false);
        window.setLayout(BaseUtil.dp2px(window.getContext(), 250), ViewGroup.LayoutParams.WRAP_CONTENT);

        TextView tvTitle = v.findViewById(R.id.main_dialog_pay_comment_title);
        TextView tvMessage = v.findViewById(R.id.main_content);

        if (!TextUtils.isEmpty(mTitle)) {
            tvTitle.setText(mTitle);
        }
        if (!TextUtils.isEmpty(mMessage)) {
            tvMessage.setText(mMessage);
        }
        v.findViewById(R.id.main_positive_button).setOnClickListener(this);
        v.findViewById(R.id.main_negative_button).setOnClickListener(this);
        return v;
    }

    @Override
    public void onClick(View v) {
        if (OneClickHelper.getInstance().onClick(v)) {
            if (v.getId()== R.id.main_positive_button) {
                dismiss();
                if (mListener!=null) {
                    mListener.onClickSubScripteButton();
                }
            }else if(v.getId()== R.id.main_negative_button){
                dismiss();
            }
        }
    }

    @Override
    public void dismiss() {
        if (mListener!=null) {
            mListener.onDismiss();
        }
        super.dismiss();
    }

    @Override
    public void onCancel(DialogInterface dialog) {
        if (mListener!=null) {
            mListener.onDismiss();
        }
        super.onCancel(dialog);
    }

    public interface IOnSubscribeResult{
        void onClickSubScripteButton();
        void onDismiss();
    }
}
