package com.ximalaya.ting.android.main.dialog;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants.Group_tob;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.model.anchor.AnchorShop;

import java.util.List;

/**
 * 主播商品弹窗 用于播放页口播商品
 *
 * <AUTHOR>
 */
public class TrackProduceDialog extends BaseDialogFragment {

    private static final String TAG = "TrackProduceDialog";
    private static final String EXT_X = "x value";
    private static final String EXT_Y = "y value";
    private int x;
    private int y;
    private ImageView mProduceImgIv;
    private TextView mProduceDescTv;
    private TextView mLookCountTv;
    private ImageView mLookAvatarIv1;
    private ImageView mLookAvatarIv2;
    private ImageView mLookAvatarIv3;
    private View mProductArrowView;
    View rootView;

    public static TrackProduceDialog newInstance(int x, int y) {
        TrackProduceDialog fragment = new TrackProduceDialog();
        Bundle ext = new Bundle();
        ext.putInt(EXT_X, x);
        ext.putInt(EXT_Y, y);
        fragment.setArguments(ext);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle args = getArguments();
        if (args != null) {
            x = args.getInt(EXT_X, 0);
            y = args.getInt(EXT_Y, 0);
        } else {
            x = 0;
            y = 0;
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.main_play_track_produce_pop, container, false);
        initView(rootView);
        if (getDialog() != null) {
            setCancelable(true);
            getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
            getDialog().setCanceledOnTouchOutside(true);
            final Window window = getDialog().getWindow();
            if (window != null) {
                window.setBackgroundDrawableResource(android.R.color.transparent);
                window.getDecorView().setPadding(0, 0, 0, 0);
                WindowManager.LayoutParams wlp = window.getAttributes();
                wlp.width = WindowManager.LayoutParams.MATCH_PARENT;
                wlp.height = WindowManager.LayoutParams.WRAP_CONTENT;
                wlp.dimAmount = 0f;
                // wlp.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;
                //  wlp.flags |= WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION;
                wlp.gravity = Gravity.TOP;//必须为 TOP，否则定位不准确
                wlp.y = y - BaseUtil.dp2px(getContext(), 88) - BaseUtil.dp2px(getContext(), 5);//配合 Gravity.TOP 才能准确定位
                Context context = getDialog().getContext();
                int divierId = context.getResources().getIdentifier("android:id/titleDivider", null, null);
                View divider = getDialog().findViewById(divierId);
                if (divider != null) {
                    divider.setVisibility(View.GONE);
                }
                window.setAttributes(wlp);
            }
        }

        return rootView;
    }

    private View mProduceLayout;

    public void notifyView(int height) {
        Window window = getDialog().getWindow();
        if (window != null) {
            WindowManager.LayoutParams wlp = window.getAttributes();
            wlp.y = height - BaseUtil.dp2px(getContext(), 88) - BaseUtil.dp2px(getContext(), 5);
            window.setAttributes(wlp);
        }
    }

    private void initView(final View rootView) {
        mProduceImgIv = rootView.findViewById(R.id.main_play_track_produce_img_iv);
        mProduceLayout = rootView.findViewById(R.id.main_track_produce_pop_ll);
        mProduceDescTv = rootView.findViewById(R.id.main_play_track_produce_desc_tv);
        mLookCountTv = rootView.findViewById(R.id.main_produce_look_count_tv);
        mLookAvatarIv1 = rootView.findViewById(R.id.main_produce_look_riv1);
        mLookAvatarIv2 = rootView.findViewById(R.id.main_produce_look_riv2);
        mLookAvatarIv3 = rootView.findViewById(R.id.main_produce_look_riv3);
        mProductArrowView = rootView.findViewById(R.id.main_product_arrow);
        mProduceLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mListener.onClick(v);
            }
        });
        initData();
    }

    private AnchorShop mAnchorShop;
    private String mNickName;

    public void setDialogData(AnchorShop anchorShop, String nickName) {
        mAnchorShop = anchorShop;
        mNickName = nickName;

    }

    private void initData() {
        AnchorShop.ViewData viewData = mAnchorShop.getViewData();
        if (viewData != null) {
            if (!TextUtils.isEmpty(viewData.getViewNum()) && !TextUtils.equals(viewData.getViewNum(), "0")) {
                mLookCountTv.setText(viewData.getViewNum() + "人看过");
            }
            List<String> icons = viewData.getIcons();
            if (!ToolUtil.isEmptyCollects(icons)) {
                for (int i = 0; i < icons.size(); ++i) {
                    if (i == 0) {
                        ImageManager.from(getContext()).displayImage(mLookAvatarIv1, icons
                                .get(0), -1);
                    } else if (i == 1) {
                        ImageManager.from(getContext()).displayImage(mLookAvatarIv2, icons
                                .get(1), -1);
                    } else if (i == 2) {
                        ImageManager.from(getContext()).displayImage(mLookAvatarIv3, icons
                                .get(2), -1);
                    }
                }
            }
        }
        if (!TextUtils.isEmpty(mAnchorShop.getTitle())) {
            //商品简介
            mProduceDescTv.setText(mAnchorShop.getTitle());
        }
        if (!TextUtils.isEmpty(mAnchorShop.getPic())) {
            //商品图片
            ImageManager.from(getContext()).displayImage(mProduceImgIv, mAnchorShop.getPic(),
                    -1);
        }

        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mProductArrowView.getLayoutParams();
        if(params != null) {
            params.leftMargin = x + BaseUtil.dp2px(getContext(), 10);
        }
        mProductArrowView.setLayoutParams(params);
    }

    public void show(FragmentManager manager, String tag, long id) {
        this.show(manager, tag);
        new UserTracking().setModuleType("主播商品弹层").setSrcPage("track").setTrackId(id)
                .setSrcModule("播放功能区").setProductId(mAnchorShop.getGid()).setId(5817)
                .setPId(mAnchorShop.getPid())
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil
                        .SERVICE_DYNAMIC_MODULE);
        int delayTime = ConfigureCenter.getInstance().getInt(Group_tob.GROUP_NAME, Group_tob.ITEM_GOODS_CARD_TIME, 5);
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                if (isVisible()) {
                    //超过时间，自动消失
                    dismiss();
                }
            }
        }, delayTime * 1000);
    }

    public interface IOnDialogClickListener {
        void onClick(View view);
    }

    private IOnDialogClickListener mListener;

    public void setOnDialogClickListener(IOnDialogClickListener listener) {
        mListener = listener;
    }

}
