package com.ximalaya.ting.android.main.dialog.wholeAlbum;

import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.other.BaseLoadDialogFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.setting.WholeAlbumVipButtonSource;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.albumModule.album.wholeAlbum.WholeAlbumFragmentNew;
import com.ximalaya.ting.android.main.constant.MainUrlConstants;
import com.ximalaya.ting.android.main.manager.wholeAlbum.WholeAlbumMarkPointManager;
import com.ximalaya.ting.android.main.model.pay.Coupon;
import com.ximalaya.ting.android.main.model.pay.PromotionLabel;
import com.ximalaya.ting.android.main.model.pay.WholeAlbumPriceInfo;
import com.ximalaya.ting.android.main.util.other.CouponUtil;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Created by 5Greatest on 2020.09.17
 *
 * <AUTHOR>
 * On 2020-09-17
 */
public class WholeAlbumPromotionDialog extends BaseLoadDialogFragment implements View.OnClickListener {
    private static final long HINT_DURATION = 2000L;

    public static WholeAlbumPromotionDialog newInstance(BaseFragment2 fragment, AlbumM album, WholeAlbumPriceInfo priceInfo) {
        if (null == priceInfo) {
            return null;
        }
        WholeAlbumPromotionDialog dialog = new WholeAlbumPromotionDialog();
        if (null != priceInfo.promotionLabel && null != priceInfo.promotionLabel.labels) {
            dialog.labels.addAll(priceInfo.promotionLabel.labels);
        }
        dialog.mFragmentReference = new WeakReference<>(fragment);
        dialog.mAlbum = album;
        if (null != album) {
            dialog.mAlbumId = album.getId();
        }
        dialog.coupons = priceInfo.coupons;
        return dialog;
    }

    private WeakReference<BaseFragment2> mFragmentReference;

    private View mCloseBtn;

    private LinearLayout mPromotionLabelsHolder;
    private TextView mCouponHolderTitle;
    private LinearLayout mCouponsHolder;

    private View mGotCouponHint;

    private XmBaseDialog vipCouponGuideDialog;

    private List<PromotionLabel.LabelItem> labels = new ArrayList<>();
    private List<Coupon> coupons;
    private AlbumM mAlbum;
    private long mAlbumId;

    private WholeAlbumPromotionDialog() {
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_whole_album_promotion;
    }

    @Override
    protected void initUi(View view, Bundle savedInstanceState) {
        mCloseBtn = view.findViewById(R.id.main_whole_album_title_close);
        mPromotionLabelsHolder = view.findViewById(R.id.main_whole_album_promotions_area);
        mCouponHolderTitle = view.findViewById(R.id.main_whole_album_coupons);
        mCouponsHolder = view.findViewById(R.id.main_whole_album_coupons_area);
        mGotCouponHint = view.findViewById(R.id.main_whole_album_got_hint);

        ViewStatusUtil.setOnClickListener(mCloseBtn, this);
    }

    private void parseArgs() {
        Bundle args = getArguments();
        if (null != args) {
            mAlbumId = args.getLong(BundleKeyConstants.KEY_ALBUM_ID);
        }
    }

    @Override
    protected void loadData() {
        // 添加标签的内容
        if (!ToolUtil.isEmptyCollects(labels)) {
            ViewStatusUtil.setVisible(View.VISIBLE, mPromotionLabelsHolder);
            for (PromotionLabel.LabelItem label : labels) {
                View view = createLabelView(label);
                if (null != view) {
                    mPromotionLabelsHolder.addView(view);
                }
            }
        } else {
            ViewStatusUtil.setVisible(View.GONE, mPromotionLabelsHolder);
        }
        // 添加优惠券啊的内容
        if (!ToolUtil.isEmptyCollects(coupons)) {
            ViewStatusUtil.setVisible(View.VISIBLE, mCouponsHolder);
            ViewStatusUtil.setText(mCouponHolderTitle, String.format(Locale.getDefault(), "优惠券 (%d)", coupons.size()));
            for (Coupon coupon : coupons) {
                View view = createCouponView(coupon);
                if (null != view) {
                    mCouponsHolder.addView(view);
                }
            }
            WholeAlbumMarkPointManager.Companion.markPointOnShowPromotionDialog(mAlbum, coupons.get(0).getCouponId());
        } else {
            ViewStatusUtil.setVisible(View.GONE, mCouponsHolder);
            WholeAlbumMarkPointManager.Companion.markPointOnShowPromotionDialog(mAlbum, -1);
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, com.ximalaya.ting.android.host.R.style.host_share_dialog);
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.gravity = Gravity.BOTTOM;
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.WRAP_CONTENT;
            window.setWindowAnimations(com.ximalaya.ting.android.host.R.style.host_popup_window_from_bottom_animation);
            window.setAttributes(params);
        }
    }

    @Override
    public void onClick(View v) {
        if (null == v || !OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (R.id.main_whole_album_title_close == id) {
            dismiss();
        }
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
    }

    /**
     * 生成促销标签的view
     */
    private View createLabelView(PromotionLabel.LabelItem label) {
        if (null == label || (null == label.popupLabel && null == label.desc)) {
            return null;
        }
        View view = LayoutInflater.from(getContext()).inflate(R.layout.main_item_whole_album_promotion_label, null, false);
        TextView head = view.findViewById(R.id.main_whole_album_promotion_title);
        TextView desc = view.findViewById(R.id.main_whole_album_promotion_description);
        ViewStatusUtil.setText(head, label.popupLabel);
        ViewStatusUtil.setText(desc, label.desc);
        return view;
    }

    /**
     * 生成优惠券的view
     */
    private View createCouponView(final Coupon coupon) {
        if (null == coupon) {
            return null;
        }
        View view = LayoutInflater.from(getContext()).inflate(R.layout.main_item_whole_album_coupon, null, false);
        TextView value = view.findViewById(R.id.main_whole_album_coupon_value);
        TextView name = view.findViewById(R.id.main_whole_album_coupon_name);
        TextView validation = view.findViewById(R.id.main_whole_album_coupon_validation);

        if (Coupon.DISCOUNT_TYPE_RATE.equals(coupon.getDiscountType())) {
            SpannableString spannableString = new SpannableString(((int) (coupon.getPlusRate() / 10)) + "折");
            spannableString.setSpan(new AbsoluteSizeSpan(BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 13)), spannableString.length() - 1, spannableString.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            ViewStatusUtil.setText(value, spannableString);
        }
        if (Coupon.DISCOUNT_TYPE_VALUE.equals(coupon.getDiscountType())) {
            SpannableString spannableString = new SpannableString(coupon.getCouponValue() + "喜点");
            spannableString.setSpan(new AbsoluteSizeSpan(BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 13)), spannableString.length() - 2, spannableString.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            ViewStatusUtil.setText(value, spannableString);
        }
        ViewStatusUtil.setText(name, coupon.getName());
        ViewStatusUtil.setText(validation, coupon.getValidDateDesc());

        View gotBtn = view.findViewById(R.id.main_whole_album_coupon_btn_got);
        if (coupon.isHasGet()) {
            ViewStatusUtil.setVisible(View.VISIBLE, gotBtn);
        } else {
            View btn = view.findViewById(R.id.main_whole_album_coupon_btn_require);
            ViewStatusUtil.setVisible(View.VISIBLE, btn);
            ViewStatusUtil.setOnClickListener(btn, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (null == coupon) {
                        return;
                    }
                    WholeAlbumMarkPointManager.Companion.markPointOnClickGetCoupon(mAlbum, coupon.getCouponId());
                    if (coupon.isPaid()) {
                        WholeAlbumMarkPointManager.Companion.markPointOnClickGetLimitCoupon(mAlbumId);
                        // 限购券
                        if (CouponUtil.requestLimitCoupon(getFragment(), coupon)) {
                            dismiss();
                        }
                    } else {
                        WholeAlbumMarkPointManager.Companion.markPointOnClickGetNormalCoupon(mAlbumId, coupon.getCouponId());
                        CouponUtil.requestCoupon(coupon, new CouponUtil.CouponRequestCallBack() {
                            @Override
                            public void onSuccess() {
                                if (null != coupon) {
                                    coupon.setHasGet(true);
                                    String toastContent = null;
                                    if (coupon.isAvailable() && !TextUtils.isEmpty(coupon.getSuccessAlertText())) {
                                        toastContent = coupon.getSuccessAlertText();
                                    } else if (!coupon.isAvailable() && !TextUtils.isEmpty(coupon.getUnavailableAlertText())) {
                                        toastContent = coupon.getUnavailableAlertText();
                                    }
                                    if (!TextUtils.isEmpty(toastContent)) {
                                        CustomToast.showToast(toastContent);
                                    }
                                    doAutoSubscribeDuring123Activity();
                                }
                                if (WholeAlbumPromotionDialog.this.isVisible()) {
                                    ViewStatusUtil.setVisible(View.VISIBLE, gotBtn);
                                    ViewStatusUtil.setVisible(View.GONE, btn);
                                }
                                if (null != getFragment() && getFragment() instanceof WholeAlbumFragmentNew) {
                                    ((WholeAlbumFragmentNew) getFragment()).updateUi(WholeAlbumFragmentNew.MSG_UPDATE_UI_ON_UPDATE_BOTTOM_BUTTON_TEXT);
                                }
                            }

                            @Override
                            public void onFail(int code, String msg) {
                                if (code == 6) {
                                    showVipCouponGuideDialog();
                                    dismiss();
                                    return;
                                }
                                String failMessage = msg;
                                if (504 == code) {
                                    failMessage = "你们太热情了，请稍后再试~";
                                }
                                if (WholeAlbumPromotionDialog.this.isVisible() && !StringUtil.isEmpty(failMessage)) {
                                    CustomToast.showFailToast(failMessage);
                                }
                            }
                        });
                    }
                }
            });
        }
        return view;
    }

    private void doAutoSubscribeDuring123Activity() {
        boolean isDuring123Activity = ConfigureCenter.getInstance().getBool(
                "fufei", "group-subscribe-toast", false);
        if (isDuring123Activity) {
            BaseFragment2 fragment = getFragment();
            if (fragment instanceof WholeAlbumFragmentNew) {
                ((WholeAlbumFragmentNew) fragment).subscribeAlbum();
            }
        }
    }

    /**
     * 显示会员专享优惠券的提示
     */
    private void showVipCouponGuideDialog() {
        if (null != getFragment()) {
            if (null != mAlbum && null != mAlbum.getWholeAlbumVipButtonSource()) {
                final WholeAlbumVipButtonSource source = mAlbum.getWholeAlbumVipButtonSource();
                View contentView = LayoutInflater.from(BaseApplication.getMyApplicationContext())
                        .inflate(R.layout.main_dialog_whole_album_vip_coupon_guide, null);
                Button button = contentView.findViewById(R.id.main_vip_coupon_guide_btn);
                View closeView = contentView.findViewById(R.id.main_vip_coupon_guide_close);
                button.setOnClickListener(v -> {
                    vipCouponGuideDialog.dismiss();
                    String url = source.getVipCouponButtonUrl();
                    if (TextUtils.isEmpty(url)) {
                        url = MainUrlConstants.getInstanse().getVipProductPageUrl();
                    }
                    NativeHybridFragment.Builder builder = new NativeHybridFragment.Builder();
                    builder.url(url).showTitle(true);
                    if (null != getFragment()) {
                        getFragment().startFragment(builder.builder());
                    }
                });
                closeView.setOnClickListener(v -> vipCouponGuideDialog.dismiss());
                String btnContent = source.getVipCouponButtonText();
                if (TextUtils.isEmpty(btnContent)) {
                    btnContent = "立即开通会员";
                }
                button.setText(btnContent);
                vipCouponGuideDialog = new XmBaseDialog(BaseApplication.getMyApplicationContext());
                vipCouponGuideDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
                vipCouponGuideDialog.setContentView(contentView);
                Window window = vipCouponGuideDialog.getWindow();
                if (window == null) return;
                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                vipCouponGuideDialog.show();
            }
        }
    }

    private BaseFragment2 getFragment() {
        if (null == mFragmentReference
                || null == mFragmentReference.get()
                || !mFragmentReference.get().canUpdateUi()) {
            return null;
        }
        return mFragmentReference.get();
    }
}
