package com.ximalaya.ting.android.main.fragment.comment;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.fragment.feedback.FeedBackMainFragment;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

/**
 * <AUTHOR> on 2016/12/2.
 */

public class FeedBackDialogFragment extends BaseDialogFragment implements View.OnClickListener {
    public static final String TAG = "FeedBackDialogFragment";
    private ImageView ivClose;
    private TextView btn;
    private TextView subBtn;
    private View.OnClickListener mClickListener;

    public FeedBackDialogFragment() {
    }

    public FeedBackDialogFragment(View.OnClickListener listener) {
        this.mClickListener = listener;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setCancelable(false);
    }

    @Override
    @Nullable
    public View onCreateView(LayoutInflater inflater,
                             @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        final Window window = getDialog().getWindow();
        View mView = inflater.inflate(R.layout.main_dialog_feed_back, ((ViewGroup) window.findViewById(android.R.id.content)), false);//此处必须是android.R.id.content
        window.setLayout(BaseUtil.dp2px(window.getContext(), 280), ViewGroup.LayoutParams.WRAP_CONTENT);
        return mView;
    }

    @Override
    public void onActivityCreated(Bundle arg0) {
        super.onActivityCreated(arg0);
        initUi();
    }

    private void initUi() {
        ivClose = (ImageView) findViewById(R.id.main_iv_close);
        btn = (TextView) findViewById(R.id.main_tv_contact);
        subBtn = (TextView) findViewById(R.id.main_textView5);

        ivClose.setOnClickListener(this);
        btn.setOnClickListener(this);
        subBtn.setOnClickListener(this);
        AutoTraceHelper.bindData(ivClose,"");
        AutoTraceHelper.bindData(btn,"");
        AutoTraceHelper.bindData(subBtn,"");
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.main_iv_close) {
            dismiss();
        } else if (v.getId() == R.id.main_tv_contact) {
            if (getActivity() != null && getActivity() instanceof MainActivity) {
                ((MainActivity) getActivity()).startFragment(new FeedBackMainFragment());
                dismiss();
            }
        } else if (v.getId() == R.id.main_textView5) {
            if (mClickListener != null) {
                mClickListener.onClick(v);
            }
            dismiss();
        }
    }

    @Override
    public void dismiss() {
        if (mClickListener != null) {
            mClickListener = null;
        }
        super.dismiss();
    }

    @Override
    public void onResume() {
        tabIdInBugly = 38402;
        super.onResume();
    }
}
