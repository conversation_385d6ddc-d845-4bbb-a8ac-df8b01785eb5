package com.ximalaya.ting.android.main.fragment.myspace.child;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.ImageSpan;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.ViewFlipper;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.facebook.rebound.SimpleSpringListener;
import com.facebook.rebound.Spring;
import com.facebook.rebound.SpringConfig;
import com.facebook.rebound.SpringSystem;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.SharedConstant;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.IWebFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.base.IAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFragmentRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.record.IRecordFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.ChatActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.FeedActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RecordActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants.Group_tob;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.account.AnchorCreditInfo;
import com.ximalaya.ting.android.host.model.account.HomePageModel;
import com.ximalaya.ting.android.host.model.anchor.AnchorManualInfo;
import com.ximalaya.ting.android.host.model.anchor.AnchorPromotionInfo;
import com.ximalaya.ting.android.host.model.anchor.AnchorValueInfo;
import com.ximalaya.ting.android.host.model.anchor.PromotionIncomeInfo;
import com.ximalaya.ting.android.host.util.AnchorVAuthenticationUtil;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.database.SharedPreferencesUserUtil;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.ui.DrawableUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.view.CustomTipsView;
import com.ximalaya.ting.android.host.view.RedDotView;
import com.ximalaya.ting.android.host.view.RoundBottomRightCornerView;
import com.ximalaya.ting.android.host.view.layout.ShadowLayout;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.anchorspace.PopularizeAdapter;
import com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment;

import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.AnchorSpaceUtil;
import com.ximalaya.ting.android.main.constant.MainUrlConstants;
import com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain;
import com.ximalaya.ting.android.main.dialog.anchor.AnchorPromotionDialogFragment;
import com.ximalaya.ting.android.main.dialog.anchor.AnchorRankDialogFragment;
import com.ximalaya.ting.android.main.dialog.anchor.AnchorTitleSelectDialogFragment;
import com.ximalaya.ting.android.main.fragment.find.other.anchor.MyAttentionFragmentNew;
import com.ximalaya.ting.android.main.model.Announce;
import com.ximalaya.ting.android.main.model.anchor.AnchorHolderItems;
import com.ximalaya.ting.android.main.model.anchor.AnchorMissionProcessInfo;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.view.CustomTypefaceSpan;
import com.ximalaya.ting.android.main.view.customview.AnchorGradeToast;
import com.ximalaya.ting.android.main.view.customview.PopularizeRecyclerView;
import com.ximalaya.ting.android.main.view.other.MagneticView;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.AsyncGson;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

//import android.webkit.WebView;
//import android.webkit.WebViewClient;


/**
 * 主播工作台
 */
public class ManageCenterFragment extends BaseFragment2 implements View.OnClickListener {
    public static final String HOME_PAGE_JSON = "home_page_json";
    public static final String KEY_HOME_PAGE_MODEL = "key_home_page_model";
    private static final String URL_COMMERCE_TOOL="https://m.ximalaya.com/anchor-sell/addGoods/xmly?source= track&_full_with_bar=1";

    private final long uid = UserInfoMannage.getUid();
    private final String token = UserInfoMannage.getInstance().getUser().getToken();
    private HomePageModel mHomePageModel;
    private List<Announce> mAnnounceList;

    private Typeface newTypeface;
    private List<AnchorHolderItems> gifInfo;
    private AnchorMissionProcessInfo anchorMissionProcessInfo;
    private AnchorTitleSelectDialogFragment anchorTitleSelectDialogFragment;
    private boolean mIsLoading;

    //***************************** USER VIEWS *****************************//
    private RoundBottomRightCornerView vUserPortrait;
    private ShadowLayout vUserPortraitShadow;
    private TextView vUserName;
    private ImageView mVGradeView;
//    private TextView vUserGrade;
    private RelativeLayout vRankLay;
    private TextView vUserGradeTask, vUserGradePlus;
    private ImageView vUserRankImg;
    private TextView vUserTaskFinish;
    private TextView vUserGradeAuthorization;
    private ImageView vGotoVerifyBg;
    private LinearLayout vLlGradeAuthorizationAndValue;
    private TextView vTvCreditScore;
    //***************************** USER VIEWS *****************************//

    //***************************** 我的 VIEWS *****************************//
    private TextView vMyWorksCount;
    private RedDotView vMyWorksDot;
    private TextView vRecord;
    private TextView mMyLivingTv;
    private TextView vStartLiving;
    //***************************** 我的 VIEWS *****************************//


    //***************************** 主播头条 VIEWS *****************************//
    private ViewGroup vNoticeItem;
    private View vNoticeIcon;
    private ViewFlipper vNoticeFlipper;
    //***************************** 主播头条 VIEWS *****************************//

    //***************************** 我的数据 VIEWS *****************************//
    private TextView vDataTitle;
    private TextView vYstdPlay;
    private TextView vYstdSubs;
    private TextView vTotalPlay;
    private TextView vTotalSbus;
    private TextView vHintToLike;
    private LinearLayout mDataCloseLl;
    private LinearLayout mDataOpenLl;
    private LinearLayout mDataPlayLl;
    private TextView mMyDataYesterdayPlayTv;
    private TextView mMyDataTotalPlayTv;
    private LinearLayout mDataSubscribeLl;
    private TextView mMyDataYesterdaySubscribeTv;
    private TextView mMyDataTotalSubscribeTv;
    private LinearLayout mDataShareLl;
    private TextView mMyDataYesterdayShareTv;
    private TextView mMyDataTotalShareTv;
    //***************************** 我的数据 VIEWS *****************************//


    //***************************** 我的收益 VIEWS *****************************//
    private TextView vProfitModuleTitle;
    private TextView vAccountProfit;
    private View vProfitCenterGroup;
    private TextView vProfitCenterTitle;
    private View vAnswerGroup;
    private TextView vAnswerTitle;
    private View vAudioPlusGroup;
    private TextView vAudioPlusTitle;
    private View mProfitMicTaskLl;
    private ImageView mProfitExpandIv;
    private RelativeLayout mProfitShopLl;
    private ImageView mAnchorShopIv;
    private TextView mShopTitleTv;

    //***************************** 我的收益 VIEWS *****************************//


    //***************************** 社群管理 VIEWS *****************************//
    private TextView vSocialTitle;
    private TextView vFunsTitle;
    private RedDotView vFunsDot;
    private TextView vFunsSubtitle;

    private TextView vChatGroupSubtitle;

    private TextView vTrackCommentTitle;
    private RedDotView vTrackCommentDot;
    private TextView vTrackCommentSubtitle;
    private TextView vCircleTitle;
    private RedDotView vCircleDot;
    private TextView vCircleSubtitle;
    //***************************** 社群管理 VIEWS *****************************//

    //***************************** 我要推广 VIEWS *****************************//

    private TextView mPopularizeTitleTv;
    private PopularizeRecyclerView mPopularizeRv;

    //***************************** 我要推广 VIEWS *****************************//

    //***************************** 付费管理 VIEWS *****************************//
    private ViewGroup vPaymentGroup;
    private TextView vPaymentTitle;
    private TextView vAlbumRefund;
    private RedDotView vRefundDot;
    private TextView vAlbumRefundSub;
    private TextView vAlbumComment;
    private RedDotView vAlbumCommentDot;
    private TextView vAlbumCommentSub;
    //***************************** 付费管理 VIEWS *****************************//

    //***************************** 主播手册 VIEWS *****************************//
    private TextView mManualTitleTv;
    private TextView mManualSubtitleTv;
    private LinearLayout mManualTitleLl;
    private RelativeLayout mManualGroupRl1;
    private TextView mManualTitleTv1;
    private ImageView mManualIv1;
    private TextView mManualSubtitleTv1;
    private RelativeLayout mManualGroupRl2;
    private TextView mManualTitleTv2;
    private ImageView mManualIv2;
    private TextView mManualSubtitleTv2;
    private RelativeLayout mManualGroupRl3;
    private TextView mManualTitleTv3;
    private ImageView mManualIv3;
    private TextView mManualSubtitleTv3;
    private RelativeLayout mManualGroupRl4;
    private TextView mManualTitleTv4;
    private ImageView mManualIv4;
    private TextView mManualSubtitleTv4;
    private LinearLayout mFirstManualRowLl;
    private LinearLayout mSecondManualRowLl;
    private LinearLayout mManualContainerLl;
    //***************************** 主播手册 VIEWS *****************************//

    //***************************** 我的信用分 VIEWS *****************************//
    private TextView vCreditSubtitle;
    private boolean mOpenMyDataCenter;
    private CustomTipsView mDataCenterWindowTips;
    private CustomTipsView tipsView;
    private MagneticView mMagneticView;
    //***************************** 我的信用分 VIEWS *****************************//


    public static ManageCenterFragment newInstance(HomePageModel homePageModel) {
        ManageCenterFragment fra = new ManageCenterFragment();
        Bundle args = new Bundle();
        args.putSerializable(KEY_HOME_PAGE_MODEL, homePageModel);
        fra.setArguments(args);
        return fra;
    }

    public ManageCenterFragment() {
        super(AppConstants.isPageCanSlide, null);
    }

    @Override
    public void onCreate(@Nullable final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        newTypeface = Typeface.createFromAsset(getResourcesSafe().getAssets(), "fonts/XmlyNumberV1.0-Regular.otf");
    }

    /**
     * 主播是否认证
     *
     * @return
     */
    private boolean isAnchorVerified() {
        return mHomePageModel != null && mHomePageModel.getVerifyType() != 0;
    }

    /**
     * 收益是否开通
     *
     * @return
     */
    private boolean isProfitOpened() {
        return mHomePageModel != null && mHomePageModel.getIncomeIdentifier() != 0;
    }


    @Override
    protected String getPageLogicName() {
        return "主播工作台";
    }


    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("主播工作台");
        mPopups = new ArrayList<>();
        initUserViews();
        initUserGradeTaskViews();
        initMyViews();
        initNoticeViews();
        initMyDataViews();
        initProfitViews();
        initSocialViews();
        initPopularizeViews();
        initPaymentViews();
        initManualViews();
        initCopyrightViews();
        initCreditViews();

        //setViews();
        showNoticePop();
        if(!ViewUtil.haveDialogIsShowing(getActivity())){
            showMyLivingTips();
        }

        //进入页面，增加无网络提示
        if (!NetworkUtils.isNetworkAvaliable(mContext)) {
            CustomToast.showFailToast("无可用网络");
        }

        //加载上个页面HomePageModel的缓存
        Bundle args = getArguments();
        if (args != null && args.containsKey(KEY_HOME_PAGE_MODEL) && mHomePageModel==null) {
            mHomePageModel= (HomePageModel) args.getSerializable(KEY_HOME_PAGE_MODEL);
            if (mHomePageModel!=null){
                setViews();
            }
        }
    }


    private void setViews() {
        setUserViews();
        setMyViews();
        setMyDataViews();
        setProfitViews();
        setSocialViews();
        setPopularizeViews();//推广拉新
        setPaymentViews();
        setManualViews();//主播手册
        setCreditViews();
    }

    private void initUserViews() {
        vUserPortrait = findViewById(R.id.main_iv_head1);
        vUserPortraitShadow = (ShadowLayout) findViewById(R.id.main_iv_head);
        vUserName = (TextView) findViewById(R.id.main_anchor_title);
        mVGradeView = findViewById(R.id.main_anchor_grade);
        vUserGradeAuthorization = (TextView) findViewById(R.id.main_anchor_tv_grade_authorization);
        DrawableUtil.setBackground(DrawableUtil.newGradientDrawable(Color.parseColor("#FFFAF7"),
                BaseUtil.dp2px(mContext, 18), 1, Color.parseColor("#FFD3C1"))
                , vUserGradeAuthorization);
//        vUserGrade = (TextView) findViewById(R.id.main_anchor_grade_value);
        vUserPortrait.setOnClickListener(this);

        AutoTraceHelper.bindData(vUserPortrait, "");
        AutoTraceHelper.bindData(vRankLay, mHomePageModel);
        AutoTraceHelper.bindData(vUserGradeAuthorization,AutoTraceHelper.MODULE_DEFAULT,"");

    }

    private void initUserGradeTaskViews() {
        vRankLay = (RelativeLayout) findViewById(R.id.main_anchor_task_layout);
        vUserRankImg = (ImageView) findViewById(R.id.main_anchor_img);
        vUserGradeTask = (TextView) findViewById(R.id.main_anchor_grade_task);
        vUserGradePlus = (TextView) findViewById(R.id.main_anchor_grade_plus);
        vUserTaskFinish = (TextView) findViewById(R.id.main_anchor_grade_task_finish);
        vGotoVerifyBg = (ImageView) findViewById(R.id.main_anchor_goto_verify_bg);
        vLlGradeAuthorizationAndValue = (LinearLayout) findViewById(R.id.main_ll_anchor_grade_authorization_and_value);
        vTvCreditScore = (TextView) findViewById(R.id.main_tv_credit_score);
        vUserGradeTask.setOnClickListener(this);
        vUserTaskFinish.setOnClickListener(this);
        vGotoVerifyBg.setOnClickListener(this);
        vLlGradeAuthorizationAndValue.setOnClickListener(this);
        vTvCreditScore.setOnClickListener(this);

        AutoTraceHelper.bindData(vUserGradeTask,AutoTraceHelper.MODULE_DEFAULT,"");
        AutoTraceHelper.bindData(vUserTaskFinish,AutoTraceHelper.MODULE_DEFAULT,"");
        AutoTraceHelper.bindData(vGotoVerifyBg,AutoTraceHelper.MODULE_DEFAULT,"");

    }

    private void initMyViews() {
        vMyWorksCount = (TextView) findViewById(R.id.main_tv_track_count);
        vMyWorksDot = new RedDotView(getContext());
        vMyWorksDot.setTargetView(vMyWorksCount);
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) vMyWorksDot.getLayoutParams();
        params.topMargin = BaseUtil.dp2px(mContext, 14);
        params.rightMargin = BaseUtil.dp2px(mContext, 14);
        vMyWorksDot.setVisibility(View.INVISIBLE);

        vRecord = (TextView) findViewById(R.id.main_tv_record);

        mMyLivingTv = (TextView) findViewById(R.id.main_tv_my_living);


        vStartLiving = (TextView) findViewById(R.id.main_tv_live);

        vMyWorksCount.setOnClickListener(this);
        vRecord.setOnClickListener(this);
        mMyLivingTv.setOnClickListener(this);
        vStartLiving.setOnClickListener(this);
        AutoTraceHelper.bindData(vMyWorksCount, "");
        AutoTraceHelper.bindData(vRecord, "");
        AutoTraceHelper.bindData(mMyLivingTv, "");
        AutoTraceHelper.bindData(vStartLiving, "");
    }

    private void initNoticeViews() {
        vNoticeItem = (ViewGroup) findViewById(R.id.main_notice_item);
        vNoticeIcon = findViewById(R.id.main_notice_icon);
        vNoticeFlipper = (ViewFlipper) findViewById(R.id.main_notice);

        vNoticeIcon.setOnClickListener(this);
        AutoTraceHelper.bindData(vNoticeIcon, "");
    }

    private void initMyDataViews() {


        vDataTitle = (TextView) findViewById(R.id.main_data_title);
        mDataCloseLl = (LinearLayout) findViewById(R.id.main_anchor_data_close_ll);
        mDataOpenLl = (LinearLayout) findViewById(R.id.main_anchor_data_open_ll);

        mDataPlayLl = (LinearLayout) findViewById(R.id.main_anchor_data_play_ll);
        mDataPlayLl.setOnClickListener(this);
        mMyDataYesterdayPlayTv = (TextView) findViewById(R.id.main_mydata_yesterday_play_tv);
        mMyDataTotalPlayTv = (TextView) findViewById(R.id.main_mydata_total_play_tv);

        mDataSubscribeLl = (LinearLayout) findViewById(R.id.main_anchor_data_subscribe_ll);
        mDataSubscribeLl.setOnClickListener(this);
        mMyDataYesterdaySubscribeTv = (TextView) findViewById(R.id.main_mydata_yesterday_subscribe_tv);
        mMyDataTotalSubscribeTv = (TextView) findViewById(R.id.main_mydata_total_subscribe_tv);
        mDataShareLl = (LinearLayout) findViewById(R.id.main_anchor_data_share_ll);
        mDataShareLl.setOnClickListener(this);
        mMyDataYesterdayShareTv = (TextView) findViewById(R.id.main_mydata_yesterday_share_tv);
        mMyDataTotalShareTv = (TextView) findViewById(R.id.main_mydata_total_share_tv);

        vDataTitle.setText(ModuleTitle.MY_DATA.getTitle());
        vYstdPlay = (TextView) findViewById(R.id.main_mydata_yesterday_play);
        vYstdSubs = (TextView) findViewById(R.id.main_mydata_ystd_subs);
        vTotalPlay = (TextView) findViewById(R.id.main_mydata_total_play);
        vTotalSbus = (TextView) findViewById(R.id.main_mydata_total_subs);
        View playItem = findViewById(R.id.main_play_item);
        playItem.setOnClickListener(this);
        AutoTraceHelper.bindData(playItem, "");

        View subscribeItem = findViewById(R.id.main_subscribe_item);
        subscribeItem.setOnClickListener(this);
        AutoTraceHelper.bindData(subscribeItem, "");

    }


    private void initProfitViews() {
        vProfitModuleTitle = (TextView) findViewById(R.id.main_my_profit_title);
        vProfitModuleTitle.setText(ModuleTitle.MY_PROFIT.getTitle());
        vAccountProfit = (TextView) findViewById(R.id.main_profit_account);
        vProfitCenterGroup = findViewById(R.id.main_profit_group);
        mProfitMicTaskLl = findViewById(R.id.main_mic_task_rl);
        vProfitCenterTitle = (TextView) findViewById(R.id.main_profit_title);
        vProfitCenterTitle.setText(PermissionMenu.PROFIT_CENTER.getTitle());
        vAnswerGroup = findViewById(R.id.main_answer_group);
        vAnswerTitle = (TextView) findViewById(R.id.main_answer_title);
        vAnswerTitle.setText(NormalMenu.QUESTION_ANSWER.getTitle());
        vAudioPlusGroup = findViewById(R.id.main_audio_plus_group);
        vAudioPlusTitle = (TextView) findViewById(R.id.main_audio_plus_title);
        vAudioPlusTitle.setText(NormalMenu.AUDIO_PLUS.getTitle());
        mProfitExpandIv=(ImageView)findViewById(R.id.main_anchor_manage_profit_expand_iv);
        mAnchorShopIv=(ImageView)findViewById(R.id.main_anchor_shop_iv);
        mShopTitleTv=(TextView)findViewById(R.id.main_shop_title);
        mProfitShopLl=(RelativeLayout)findViewById(R.id.main_anchor_shop_group);

        vProfitCenterGroup.setOnClickListener(this);
        vAnswerGroup.setOnClickListener(this);
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) vAnswerGroup.getLayoutParams();
        layoutParams.width = BaseUtil.getScreenWidth(mContext) / 4;
        vAccountProfit.setOnClickListener(this);
        vAudioPlusGroup.setOnClickListener(this);
        mProfitMicTaskLl.setOnClickListener(this);
        mProfitExpandIv.setOnClickListener(this);
        mProfitShopLl.setOnClickListener(this);
        if(ConfigureCenter.getInstance().getBool("tob","e_commerce_tool",false)){
            //是否展示主播电商入口
            mProfitExpandIv.setVisibility(View.VISIBLE);
            ViewUtil.expandClickArea(mContext,mProfitExpandIv,15,10,15,10);//增加mProfitExpandIv的点击区域
        }else {
            mProfitExpandIv.setVisibility(View.GONE);
            mAnchorShopIv.setBackground(getResourcesSafe().getDrawable(R.drawable.main_anchor_manage_answer_icon));
            mAnchorShopIv.setContentDescription("我的问答");
            mShopTitleTv.setText("我的问答");
        }
        AutoTraceHelper.bindData(vProfitCenterGroup, "");
        AutoTraceHelper.bindData(vAnswerGroup, "");
        AutoTraceHelper.bindData(vAccountProfit, "");
        AutoTraceHelper.bindData(vAudioPlusGroup, "");
        AutoTraceHelper.bindData(mProfitMicTaskLl, "");
        AutoTraceHelper.bindData(mProfitShopLl, "");
    }

    private void initSocialViews() {
        vSocialTitle = (TextView) findViewById(R.id.main_my_social_title);
        vSocialTitle.setText(ModuleTitle.COMMUNITY.getTitle());

        findViewById(R.id.main_funs_group).setOnClickListener(this);
        vFunsTitle = (TextView) findViewById(R.id.main_funs_title);
        vFunsDot = new RedDotView(getContext());
        vFunsDot.setTargetView(vFunsTitle);
        vFunsDot.setVisibility(View.INVISIBLE);
        vFunsSubtitle = (TextView) findViewById(R.id.main_funs_subtitle);

        findViewById(R.id.main_chat_group).setOnClickListener(this);
        vChatGroupSubtitle = (TextView) findViewById(R.id.main_chat_group_subtitle);


        findViewById(R.id.main_track_comment_group).setOnClickListener(this);
        vTrackCommentTitle = (TextView) findViewById(R.id.main_comment_title);
        vTrackCommentDot = new RedDotView(getContext());
        vTrackCommentDot.setTargetView(vTrackCommentTitle);
        vTrackCommentDot.setVisibility(View.INVISIBLE);
        vTrackCommentSubtitle = (TextView) findViewById(R.id.main_track_comment_subtitle);

        findViewById(R.id.main_track_free_album_comment_group).setOnClickListener(this);

        findViewById(R.id.main_circle_group).setOnClickListener(this);
        vCircleTitle = (TextView) findViewById(R.id.main_circle_title);
        vCircleDot = new RedDotView(getContext());
        vCircleDot.setTargetView(vCircleTitle);
        vCircleDot.setVisibility(View.INVISIBLE);
        vCircleSubtitle = (TextView) findViewById(R.id.main_circle_subtitle);
        AutoTraceHelper.bindData(findViewById(R.id.main_funs_group), "");
        AutoTraceHelper.bindData(findViewById(R.id.main_chat_group), "");
        AutoTraceHelper.bindData(findViewById(R.id.main_track_comment_group), "");
        AutoTraceHelper.bindData(findViewById(R.id.main_circle_group), "");
    }

    //初始化推广拉新
    private void initPopularizeViews() {

        mPopularizeTitleTv = (TextView) findViewById(R.id.main_my_popularize_title);
        mPopularizeRv = (PopularizeRecyclerView) findViewById(R.id.popularize_rv);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        mPopularizeRv.setLayoutManager(linearLayoutManager);
        mPopularizeRv.setNestedScrollingEnabled(false);
        mPopularizeRv.setVisibility(View.GONE);
    }
    private void initPaymentViews() {
        vPaymentGroup = (ViewGroup) findViewById(R.id.main_payment_group);
        vPaymentTitle = (TextView) findViewById(R.id.main_pay_title);

        findViewById(R.id.main_refund_group).setOnClickListener(this);
        vAlbumRefund = (TextView) findViewById(R.id.main_refund_title);
        vRefundDot = new RedDotView(getContext());
        vRefundDot.setTargetView(vAlbumRefund);
        vRefundDot.setVisibility(View.INVISIBLE);
        vAlbumRefundSub = (TextView) findViewById(R.id.main_refund_subtitle);

        findViewById(R.id.main_album_comment_group).setOnClickListener(this);
        vAlbumComment = (TextView) findViewById(R.id.main_album_comment_title);
        vAlbumCommentDot = new RedDotView(getContext());
        vAlbumCommentDot.setTargetView(vAlbumComment);
        vAlbumCommentDot.setVisibility(View.INVISIBLE);
        vAlbumCommentSub = (TextView) findViewById(R.id.main_album_comment_subtitle);
        AutoTraceHelper.bindData(findViewById(R.id.main_refund_group), "");
        AutoTraceHelper.bindData(findViewById(R.id.main_album_comment_group), "");
    }

    private void initManualViews() {
        mFirstManualRowLl = (LinearLayout) findViewById(R.id.main_manual_first_row_ll);
        mSecondManualRowLl = (LinearLayout) findViewById(R.id.main_manual_second_row_ll);
        mManualContainerLl = (LinearLayout) findViewById(R.id.main_anchor_manual_container_ll);

        mManualTitleLl = (LinearLayout) findViewById(R.id.main_manual_ll);
        mManualTitleTv = (TextView) findViewById(R.id.main_my_manual_title);
        mManualSubtitleTv = (TextView) findViewById(R.id.main_my_manual_subtitle);
        mManualSubtitleTv.setOnClickListener(this);


        mManualGroupRl1 = (RelativeLayout) findViewById(R.id.main_new_driver_group);
        mManualTitleTv1 = (TextView) findViewById(R.id.main_new_driver_title);
        mManualIv1 = (ImageView) findViewById(R.id.main_new_driver_icon);
        mManualSubtitleTv1 = (TextView) findViewById(R.id.main_new_driver_subtitle);

        mManualGroupRl2 = (RelativeLayout) findViewById(R.id.main_record_manual_group);
        mManualTitleTv2 = (TextView) findViewById(R.id.main_record_title);
        mManualIv2 = (ImageView) findViewById(R.id.main_record_icon);
        mManualSubtitleTv2 = (TextView) findViewById(R.id.main_record_subtitle);

        mManualGroupRl3 = (RelativeLayout) findViewById(R.id.main_profit_manual_group);
        mManualTitleTv3 = (TextView) findViewById(R.id.main_manual_profit_title);
        mManualIv3 = (ImageView) findViewById(R.id.main_manual_profit_icon);
        mManualSubtitleTv3 = (TextView) findViewById(R.id.main_manual_profit_subtitle);

        mManualGroupRl4 = (RelativeLayout) findViewById(R.id.main_living_manual_group);
        mManualTitleTv4 = (TextView) findViewById(R.id.main_manual_living_title);
        mManualIv4 = (ImageView) findViewById(R.id.main_manual_living_icon);
        mManualSubtitleTv4 = (TextView) findViewById(R.id.main_manual_living_subtitle);
        mFirstManualRowLl.setVisibility(View.GONE);
        mSecondManualRowLl.setVisibility(View.GONE);
        AutoTraceHelper.bindData(findViewById(R.id.main_new_driver_group), AutoTraceHelper.MODULE_DEFAULT, mHomePageModel);
        AutoTraceHelper.bindData(findViewById(R.id.main_record_manual_group),AutoTraceHelper.MODULE_DEFAULT,  mHomePageModel);
        AutoTraceHelper.bindData(findViewById(R.id.main_profit_manual_group), AutoTraceHelper.MODULE_DEFAULT, mHomePageModel);
        AutoTraceHelper.bindData(findViewById(R.id.main_living_manual_group), AutoTraceHelper.MODULE_DEFAULT, mHomePageModel);
        AutoTraceHelper.bindData(mManualSubtitleTv, AutoTraceHelper.MODULE_DEFAULT, "");
    }

    private void initCopyrightViews() {
        ViewGroup vCopyrightTitleItem = (ViewGroup) findViewById(R.id.main_anchor_copyright_protection_title);
        if(!ConfigureCenter.getInstance().getBool("tob", "tort_anchor", false)) {
            vCopyrightTitleItem.setVisibility(View.GONE);
            return;
        }
        vCopyrightTitleItem.setOnClickListener(this);
        AutoTraceHelper.bindData(vCopyrightTitleItem, AutoTraceHelper.MODULE_DEFAULT, "");
    }

    private void initCreditViews() {
        ViewGroup vCreditTitleItem = (ViewGroup) findViewById(R.id.main_anchor_my_credit_title);
        vCreditSubtitle = (TextView) findViewById(R.id.main_anchor_my_credit_subtitle);
        vCreditTitleItem.setOnClickListener(this);
    }


    private void setUserViews() {
        if (mHomePageModel != null || vUserName == null) {
            if (vUserPortrait != null) {
                ImageManager.from(mContext).displayImage(vUserPortrait, mHomePageModel.getMobileSmallLogo(), com.ximalaya.ting.android.host.R.drawable.host_default_avatar_132);
                vUserPortrait.setDrawableIdToCornerBitmap(AnchorVAuthenticationUtil.getAvatarVDrawableId(mHomePageModel.getvLogoType()));
            }
            if(vUserPortraitShadow != null) {
                vUserPortraitShadow.setIsShadowed(true);
                vUserPortraitShadow.setShadowAngle(90);
                vUserPortraitShadow.setShadowRadius(10);
                vUserPortraitShadow.setShadowDistance(10);
                vUserPortraitShadow.setShadowColor(Color.parseColor("#33000000"));
            }
            if (vUserName != null)
                vUserName.setText(mHomePageModel.getNickname());
            if (isAnchorVerified()) {
//                vUserGradeAuthorization.setText("V" + mHomePageModel.getAnchorGrade());
                vUserGradeAuthorization.setVisibility(View.VISIBLE);
                mVGradeView.setVisibility(View.VISIBLE);
                LocalImageUtil.setAnchorVGradeBackGround(mVGradeView, mHomePageModel.getAnchorGrade(), this);
//                String score="等级分 " + StringUtil.getFriendlyNumWithoutDecimal(mHomePageModel.getAnchorScore());
//                vUserGrade.setText(score);
            } else {
//                vUserGrade.setText("未认证");
//                //未认证去掉箭头
//                vUserGrade.setCompoundDrawablePadding(0);
//                vUserGrade.setCompoundDrawables(null,null, null,null);
                vUserGradeAuthorization.setVisibility(View.GONE);
                mVGradeView.setVisibility(View.GONE);
                vRankLay.setVisibility(View.GONE);
                vGotoVerifyBg.setVisibility(View.VISIBLE);
            }
            vTvCreditScore.setText("信用分 "+mHomePageModel.getCreditScore());

//            vUserGrade.setVisibility(View.VISIBLE);
//            if (vUserGradeAuthorization.getVisibility()==View.GONE){
//                //如果特权view没有展示，需要重新设置等级分的pading，解决只有一个view时候无法居中问题
//                int dp4=BaseUtil.dp2px(getContext(),4);
//                int dp8=dp4*2;
//                vUserGrade.setPadding(dp8,dp4,dp8,dp4);
//            }
        }
    }

    private void setMyViews() {
        if (mHomePageModel != null) {
            if (vMyWorksCount != null)
                vMyWorksCount.setText(String.valueOf(mHomePageModel.getTracks()));
            Router.getActionByCallback(Configure.BUNDLE_FEED, new Router.IBundleInstallCallback() {

                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (bundleModel == Configure.recordBundleModel) {
                        try {
                            Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFunctionAction().getDraftNumbers(new IAction.ICallback<Integer>() {
                                @Override
                                public void dataCallback(Integer integer) {
                                    if (vMyWorksDot != null) {
                                        boolean showRedDot = (integer != null && integer > 0);
                                        vMyWorksDot.setVisibility(showRedDot ? View.VISIBLE : View.INVISIBLE);
                                    }
                                }
                            });
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        }
    }

    private void setNoticeViews() {
        if (ToolUtil.isEmptyCollects(mAnnounceList)) {
            vNoticeItem.setVisibility(View.GONE);
        } else {
            vNoticeItem.setVisibility(View.VISIBLE);
            for (final Announce a : mAnnounceList) {
                ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                TextView textView = new TextView(mActivity);
                textView.setTextColor(getResourcesSafe().getColor(R.color.main_color_666666_888888));
                textView.setTextSize(14f);
                textView.setSingleLine(true);
                textView.setEllipsize(TextUtils.TruncateAt.END);
                textView.setGravity(Gravity.CENTER_VERTICAL);
                textView.setText(a.title);
                textView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        startFragment(new XiMaNoticeFragment());
                        new UserTracking().setSrcPage("主播工作台").setSrcModule("官方公告").setItem("button")
                                .setItemId(a.id).statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                    }
                });
                vNoticeFlipper.addView(textView, lp);
                AutoTraceHelper.bindData(textView, new AutoTraceHelper.DataWrap(mAnnounceList.indexOf(a), a));
            }

            vNoticeFlipper.setInAnimation(mContext, R.anim.main_news_in);
            vNoticeFlipper.setOutAnimation(mContext, R.anim.main_news_out);
            vNoticeFlipper.setAutoStart(true);
            vNoticeFlipper.setFlipInterval(4000);
            vNoticeFlipper.startFlipping();
        }
    }

    private void setTitleBarViews() {
        if (ToolUtil.isEmptyCollects(gifInfo)) {
            return;
        }
        FrameLayout fl = (FrameLayout) findViewById(R.id.main_title_bar);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(BaseUtil.dp2px(getContext(), 94), BaseUtil.dp2px(getContext(), 34));
        params.gravity = Gravity.RIGHT;
        params.setMargins(0, BaseUtil.dp2px(getContext(), 8), 0, BaseUtil.dp2px(getContext(), 8));
        ImageView iv = null;
        try {
            iv = new ImageView(getContext());
        } catch (Exception e) {
            e.printStackTrace();
            //解决miui bug https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/11244571?pid=1
            return;
        }
        iv.setLayoutParams(params);
        iv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Bundle bundle = new Bundle();
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, gifInfo.get(0).linkUrl);
                startFragment(NativeHybridFragment.class, bundle, null);
                new UserTracking().setSrcPage("主播工作台").setSrcModule("rootTool").setItem("button").setItemId(gifInfo.get(0).id)
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }
        });
        AutoTraceHelper.bindData(iv, AutoTraceHelper.MODULE_DEFAULT, "");
        ImageManager.from(getContext()).displayImage(iv, gifInfo.get(0).banner, -1, true);
        fl.addView(iv);
    }

    private void setAnchorMissionViews() {
        if(anchorMissionProcessInfo == null) {
            return;
        }
        if (mHomePageModel != null) {
            if(isAnchorVerified()) {
                vRankLay.setVisibility(View.VISIBLE);
                ImageManager.from(getContext()).displayImage(vUserRankImg, anchorMissionProcessInfo.icon, -1, false);
                vUserGradeTask.setText(anchorMissionProcessInfo.content);
                if (mHomePageModel.getAnchorGrade() == 0) {
                    vUserGradePlus.setVisibility(View.GONE);
                } else {
                    vUserGradePlus.setText("+" + anchorMissionProcessInfo.score + "分");
                    vUserGradePlus.setVisibility(View.VISIBLE);
                }
                if(anchorMissionProcessInfo.status == 1001) { //上传或录音
                    String str = anchorMissionProcessInfo.buttonContent.substring(1)
                            + " " + anchorMissionProcessInfo.doneCount + "/" + anchorMissionProcessInfo.count;
                    SpannableString spannableString = new SpannableString(str);
                    spannableString.setSpan(new AbsoluteSizeSpan(BaseUtil.sp2px(getContext(), 10)), 3, str.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    vUserTaskFinish.setText(spannableString);
                } else if(anchorMissionProcessInfo.status == 1002) { //领奖励
                    vUserTaskFinish.setText("领奖励");
                }
            }
        }
    }

    private CharSequence getCustomizeNum(long num) {
        String s = StringUtil.getFriendlyNumStr(num);
        SpannableString ss = new SpannableString(s);
        int endIndex = s.length();

        if (num >= 10000) {
            endIndex = s.length() - 1;
        }
        CustomTypefaceSpan typefaceSpan = new CustomTypefaceSpan("XmlyNumberV1.0-Regular", newTypeface);
        ss.setSpan(typefaceSpan, 0, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(18, true);
        ss.setSpan(sizeSpan, 0, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        if(num >= 10000){
            AbsoluteSizeSpan textSizeSpan = new AbsoluteSizeSpan(11, true);
            ss.setSpan(textSizeSpan, endIndex, s.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            ForegroundColorSpan textColorSpan = new ForegroundColorSpan(getResourcesSafe().getColor(R.color.main_color_666666_888888));
            ss.setSpan(textColorSpan, endIndex, s.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(getResourcesSafe().getColor(R.color.main_color_black));
        ss.setSpan(colorSpan, 0, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return ss;
    }

    private void setMyDataViews() {
        if (mHomePageModel != null) {

            //获取后台配置，是否展示分享数据的开关
            boolean isShowShareDataSetting = ConfigureCenter.getInstance().getBool(Group_tob.GROUP_NAME, Group_tob.ITEM_SHARE_DATA_SWITCH,
                false);
            mOpenMyDataCenter = ConfigureCenter.getInstance().getBool("tob", "data_center", false);
            if (mOpenMyDataCenter) {
                vHintToLike = (TextView) findViewById(R.id.main_album_promotion);
                vHintToLike.setOnClickListener(this);
                vHintToLike.setVisibility(View.VISIBLE);
                vHintToLike.setText(isAnchorVerified() ? R.string.main_verified_see_more : R.string.main_verified_first);
                AutoTraceHelper.bindData(vHintToLike, "");

                if(!ViewUtil.haveDialogIsShowing(getActivity())){
                    showPopupWindow("popup_window_manage_center_my_data", vHintToLike, R.string.main_my_data_popup_hint);
                }
            }
            if (isShowShareDataSetting) {
                mDataOpenLl.setVisibility(View.VISIBLE);
                mDataCloseLl.setVisibility(View.GONE);
                if (mHomePageModel.getStateInfoIdentifier() == 0) {
                    mMyDataYesterdayPlayTv.setText("计算中...");
                    mMyDataYesterdayPlayTv.setTextSize(11);
                    mMyDataYesterdaySubscribeTv.setText("计算中...");
                    mMyDataYesterdaySubscribeTv.setTextSize(11);
                    mMyDataYesterdayShareTv.setTextSize(11);
                    mMyDataYesterdayShareTv.setText("计算中...");
                } else {
                    mMyDataYesterdayPlayTv.setText(getCustomizeNum(mHomePageModel.getLastDayPlayedNum()));
                    mMyDataYesterdaySubscribeTv.setText(getCustomizeNum(mHomePageModel.getLastDaySubcribed()));
                    mMyDataYesterdayShareTv.setText(getCustomizeNum(mHomePageModel.getLastDaySharedNum()));
                }
                mMyDataTotalPlayTv.setText(getCustomizeNum(mHomePageModel.getTotalPlayedNum()));
                mMyDataTotalSubscribeTv.setText(getCustomizeNum(mHomePageModel.getTotalSubcribed()));
                mMyDataTotalShareTv.setText(getCustomizeNum(mHomePageModel.getTotalSharedNum()));
            } else {
                mDataOpenLl.setVisibility(View.GONE);
                mDataCloseLl.setVisibility(View.VISIBLE);
                if (vYstdPlay != null) {
                    if (mHomePageModel.getStateInfoIdentifier() == 0) {
                        vYstdPlay.setText("计算中...");
                        mMyDataYesterdayPlayTv.setTextSize(11);

                    } else {
                        vYstdPlay.setText(getCustomizeNum(mHomePageModel.getLastDayPlayedNum()));
                    }
                }
                if (vYstdSubs != null) {
                    if (mHomePageModel.getStateInfoIdentifier() == 0) {
                        vYstdSubs.setText("计算中...");
                        mMyDataYesterdayPlayTv.setTextSize(11);

                    } else {
                        vYstdSubs.setText(getCustomizeNum(mHomePageModel.getLastDaySubcribed()));
                    }
                }
                if (vTotalPlay != null) {
                    vTotalPlay.setText(getCustomizeNum(mHomePageModel.getTotalPlayedNum()));
                }
                if (vTotalSbus != null) {
                    vTotalSbus.setText(getCustomizeNum(mHomePageModel.getTotalSubcribed()));
                }
            }
            if (vHintToLike != null) {
                vHintToLike.setText(isAnchorVerified() ? R.string.main_verified_see_more : R.string.main_verified_first);
            }

        }
    }

    private void setProfitViews() {
        if (mHomePageModel != null) {
            if (vAccountProfit != null) {
                if (!isProfitOpened()) {
                    //收益 未开通
                    vAccountProfit.setText("立即认证获得收入");
                } else if (mHomePageModel.getCurrentMonthTotalIncome() != 0) {
                    vAccountProfit.setText("本月收入" + StringUtil.getFriendlyNumStr(mHomePageModel.getCurrentMonthTotalIncome()) + "元");
                } else {
                    vAccountProfit.setText("累计收入" + StringUtil.getFriendlyNumStr(mHomePageModel.getTotalIncome()) + "元");
                }
            }
          /*  if (vProfitCenterSubtitle != null) {
                vProfitCenterSubtitle.setText(isProfitOpened()
                        ? PermissionMenu.PROFIT_CENTER.getRights()
                        : PermissionMenu.PROFIT_CENTER.getNoRights());
            }*/
        }
    }

    private void setSocialViews() {
        if (mHomePageModel != null) {
            if (mHomePageModel.getNoReadFollowers() == 0) {
                if (vFunsDot != null)
                    vFunsDot.setVisibility(View.INVISIBLE);
                if (vFunsSubtitle != null)
                    vFunsSubtitle.setText(StringUtil.getFriendlyNumStr(mHomePageModel.getFollowers()) + "个粉丝");

            } else {
                //有新粉丝
                if (vFunsDot != null)
                    vFunsDot.setVisibility(View.VISIBLE);
                if (vFunsSubtitle != null)
                    vFunsSubtitle.setText(mHomePageModel.getNoReadFollowers() + "个新粉丝");
            }
            if (vChatGroupSubtitle != null) {
                vChatGroupSubtitle.setText(mHomePageModel.getGroupCount() + "个群聊");
            }


            if (mHomePageModel.getNewComments() == 0) {
                if (vTrackCommentSubtitle != null) {
                    vTrackCommentSubtitle.setText("常互动拉好感");
                }
                if (vTrackCommentDot != null) {
                    vTrackCommentDot.setVisibility(View.GONE);
                }
            } else {
                if (vTrackCommentSubtitle != null) {
                    vTrackCommentSubtitle.setText(mHomePageModel.getNewComments() + "个新评论");
                }
                if (vTrackCommentDot != null) {
                    vTrackCommentDot.setVisibility(View.VISIBLE);
                }
            }

            int newTingCounts = mHomePageModel.getNewTingPraises() + mHomePageModel.getNewTingComments();
            if (newTingCounts == 0) {
                if (vCircleSubtitle != null) {
                    vCircleSubtitle.setText("今天发动态了吗");
                }
                if (vCircleDot != null) {
                    vCircleDot.setVisibility(View.INVISIBLE);
                }
            } else {
                if (vCircleSubtitle != null) {
                    vCircleSubtitle.setText(newTingCounts + "条新消息");
                }
                if (vCircleDot != null) {
                    vCircleDot.setVisibility(View.VISIBLE);
                }
            }
        }
    }


    private void setPopularizeViews() {
        if (mHomePageModel != null) {
            String configTitle = ConfigureCenter.getInstance().getString("tob", "spread", "");
            if (!TextUtils.isEmpty(configTitle)) {
                mPopularizeTitleTv.setText(configTitle);
            }
            AnchorPromotionInfo anchorHolderInfo = mHomePageModel.getAnchorPromotionInfo();


            if (anchorHolderInfo != null) {
                PopularizeAdapter popularizeAdapter = new PopularizeAdapter( mHomePageModel);
                mPopularizeRv.setVisibility(View.VISIBLE);
                popularizeAdapter.setOnPopItemClickListener(new PopularizeAdapter.IOnPopItemClickListener() {
                    @Override
                    public void onClick(String linkUrl) {
                        startWebPageOfPopularize(linkUrl);
                    }
                });
                mPopularizeRv.setAdapter(popularizeAdapter);

            } else {
                mPopularizeRv.setVisibility(View.GONE);
            }
        } else {
            mPopularizeRv.setVisibility(View.GONE);
        }
    }

    private void setPaymentViews() {
        if (mHomePageModel != null) {
            if (mHomePageModel.hasAlbumCommentPannel()) {
                if (vPaymentGroup != null)
                    vPaymentGroup.setVisibility(View.VISIBLE);
                if (vPaymentTitle != null)
                    vPaymentTitle.setText(ModuleTitle.PAY_MANAGEMENT.getTitle());
                if (vAlbumRefund != null)
                    vAlbumRefund.setText(NormalMenu.ALBUM_REFUND.getTitle());
                if (mHomePageModel.getRefundCount() > 0) {
                    if (vRefundDot != null)
                        vRefundDot.setVisibility(View.VISIBLE);
                    if (vAlbumRefundSub != null)
                        vAlbumRefundSub.setText(mHomePageModel.getRefundCount() + "条新退款");
                } else {
                    if (vRefundDot != null)
                        vRefundDot.setVisibility(View.INVISIBLE);
                    if (vAlbumRefundSub != null)
                        vAlbumRefundSub.setText(NormalMenu.ALBUM_REFUND.getSubtitle());
                }

                if (vAlbumComment != null)
                    vAlbumComment.setText(NormalMenu.ALBUM_COMMENT.getTitle());
                if (mHomePageModel.getAlbumNewCommentCount() > 0) {
                    if (vAlbumCommentDot != null)
                        vAlbumCommentDot.setVisibility(View.VISIBLE);
                    if (vAlbumCommentSub != null)
                        vAlbumCommentSub.setText(mHomePageModel.getAlbumNewCommentCount() + "条新评价");
                } else {
                    if (vAlbumCommentDot != null)
                        vAlbumCommentDot.setVisibility(View.INVISIBLE);
                    if (vAlbumCommentSub != null)
                        vAlbumCommentSub.setText(NormalMenu.ALBUM_COMMENT.getSubtitle());
                }
            } else {
                if (vPaymentGroup != null)
                    vPaymentGroup.setVisibility(View.GONE);
            }
        }
    }

    private List<AnchorManualInfo.AnchorManualItem> mAnchorManualItems;

    /**
     * 展示主播手册item
     */
    private void setManualViews() {
        if (mHomePageModel != null) {
            AnchorManualInfo anchorManualInfo = mHomePageModel.getAnchorManualInfo();
            if (anchorManualInfo != null) {
                //对List以sort字段进行降序排序
                List<AnchorManualInfo.AnchorManualItem> anchorManualItems = anchorManualInfo.getAnchorManualItems();
                if (!ToolUtil.isEmptyCollects(anchorManualItems)) {
                    mManualContainerLl.setVisibility(View.VISIBLE);
                    Collections.sort(anchorManualItems, new Comparator<AnchorManualInfo.AnchorManualItem>() {
                        @Override
                        public int compare(AnchorManualInfo.AnchorManualItem item1, AnchorManualInfo.AnchorManualItem item2) {
                            return item2.getSort() - item1.getSort();
                        }
                    });
                    mAnchorManualItems = anchorManualItems;
                    int length = mAnchorManualItems.size();
                    if (length >= 4) {
                        showManualFourItems();
                    } else if (length == 3) {
                        showManualThreeItems();
                    } else if (length == 2) {
                        showManualTwoItems();
                    } else if (length == 1) {
                        showManualOneItem();
                    } else {
                        mManualContainerLl.setVisibility(View.GONE);
                    }
                }else {
                    mManualContainerLl.setVisibility(View.GONE);
                }

            }else {
                mManualContainerLl.setVisibility(View.GONE);
            }
        }

    }

    private void showManualOneItem() {
        final AnchorManualInfo.AnchorManualItem manualItem = mAnchorManualItems.get(0);
        if (manualItem != null) {
            mManualGroupRl1.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(manualItem.getContent())) {
                mManualTitleTv1.setText(manualItem.getContent());
            } else {
                mManualTitleTv1.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(manualItem.getRemark())) {
                mManualSubtitleTv1.setText(manualItem.getRemark());
            } else {
                mManualSubtitleTv1.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(manualItem.getBanner())) {
                ImageManager.from(mContext).displayImage(mManualIv1, manualItem.getBanner(), -1);
            } else {
                mManualIv1.setVisibility(View.GONE);
            }
            mManualGroupRl1.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!TextUtils.isEmpty(manualItem.getLinkUrl())) {
                        startRnPageWebOfAnchorManual(manualItem.getLinkUrl());

                        new UserTracking("主播工作台", "button").setSrcModule("主播手册").setItemId(manualItem.getContent())
                                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                    }
                }
            });
        }
    }

    private void showManualTwoItems() {
        showManualOneItem();
        final AnchorManualInfo.AnchorManualItem manualItem = mAnchorManualItems.get(1);
        if (manualItem != null) {
            mFirstManualRowLl.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(manualItem.getContent())) {
                mManualTitleTv2.setText(manualItem.getContent());
            } else {
                mManualTitleTv2.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(manualItem.getRemark())) {
                mManualSubtitleTv2.setText(manualItem.getRemark());
            } else {
                mManualSubtitleTv2.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(manualItem.getBanner())) {
                ImageManager.from(mContext).displayImage(mManualIv2, manualItem.getBanner(), -1);
            } else {
                mManualIv2.setVisibility(View.GONE);
            }
            mManualGroupRl2.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!TextUtils.isEmpty(manualItem.getLinkUrl())) {
                        startRnPageWebOfAnchorManual(manualItem.getLinkUrl());

                        new UserTracking("主播工作台", "button").setSrcModule("主播手册").setItemId(manualItem.getContent())
                                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                    }
                }
            });
        }
    }

    private void showManualThreeItems() {
        showManualTwoItems();
        final AnchorManualInfo.AnchorManualItem manualItem = mAnchorManualItems.get(2);
        if (manualItem != null) {
            mSecondManualRowLl.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(manualItem.getContent())) {
                mManualTitleTv3.setText(manualItem.getContent());
            } else {
                mManualTitleTv3.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(manualItem.getRemark())) {
                mManualSubtitleTv3.setText(manualItem.getRemark());
            } else {
                mManualSubtitleTv3.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(manualItem.getBanner())) {
                ImageManager.from(mContext).displayImage(mManualIv3, manualItem.getBanner(), -1);
            } else {
                mManualIv3.setVisibility(View.GONE);
            }
            mManualGroupRl3.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!TextUtils.isEmpty(manualItem.getLinkUrl())) {
                        startRnPageWebOfAnchorManual(manualItem.getLinkUrl());

                        new UserTracking("主播工作台", "button").setSrcModule("主播手册").setItemId(manualItem.getContent())
                                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                    }
                }
            });
        }
    }

    private void showManualFourItems() {
        showManualThreeItems();
        final AnchorManualInfo.AnchorManualItem manualItem = mAnchorManualItems.get(3);
        if (manualItem != null) {
            mSecondManualRowLl.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(manualItem.getContent())) {
                mManualTitleTv4.setText(manualItem.getContent());
            } else {
                mManualTitleTv4.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(manualItem.getRemark())) {
                mManualSubtitleTv4.setText(manualItem.getRemark());
            } else {
                mManualSubtitleTv4.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(manualItem.getBanner())) {
                ImageManager.from(mContext).displayImage(mManualIv4, manualItem.getBanner(), -1);
            } else {
                mManualIv4.setVisibility(View.GONE);
            }
            mManualGroupRl4.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!TextUtils.isEmpty(manualItem.getLinkUrl())) {
                        startRnPageWebOfAnchorManual(manualItem.getLinkUrl());
                        new UserTracking("主播工作台", "button").setSrcModule("主播手册").setItemId(manualItem.getContent())
                                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                    }
                }
            });
        }
    }

    private void setCreditViews() {
        if (vCreditSubtitle != null) {
            if (mHomePageModel == null) {
                vCreditSubtitle.setText("");
            } else {
                AnchorCreditInfo creditInfo = mHomePageModel.getCreditInfo();
                if (creditInfo != null) {
                    String subtitle = "%d条待处理违规";
                    vCreditSubtitle.setText(String.format(Locale.CHINA,subtitle, creditInfo.getPendingViolationCount()));
                }
            }
        }
    }


    @Override
    protected void loadData() {

    }


    @Override
    public void onMyResume() {
        tabIdInBugly = 38477;
        super.onMyResume();

        requestPageInfo();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mDataCenterWindowTips != null) {
            mDataCenterWindowTips.dismissTips();
        }
        if (tipsView != null) {
            tipsView.dismissTips();
        }
        hidePopups();
        if (mCreateDynamicTip != null) {
            HandlerManager.obtainMainHandler().removeCallbacks(mCreateDynamicTip);
            mCreateTipPosting = false;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        closeNoticePop();
        if (mProfitExpandIv != null) {
            mProfitExpandIv.clearAnimation();
        }
        if (mCreateDynamicTip != null) {
            HandlerManager.obtainMainHandler().removeCallbacks(mCreateDynamicTip);
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_manage_center1;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    protected boolean isShowPlayButton() {
        return true;
    }

    private void requestPageInfo() {
        Map<String, String> params = new ArrayMap<>(3);
        params.put("uid", String.valueOf(UserInfoMannage.getUid()));
        params.put("device", "android");
        params.put("announceCount", "50");
        MainCommonRequest.getAnchorDesk(params, new IDataCallBack<String>() {
            @Override
            public void onSuccess(final String object) {
                if (!canUpdateUi()) return;

                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        AsyncGson<HomePageModel> gson = new AsyncGson<>();
                        gson.fromJson(object, HomePageModel.class, new AsyncGson.IResult<HomePageModel>() {
                            @Override
                            public void postResult(HomePageModel result) {
                                if (result != null) {
                                    if (!canUpdateUi()) {
                                        return;
                                    }
                                    mHomePageModel = result;
                                    setViews();
                                    if (mHomePageModel != null && mHomePageModel.getAnchorIncreasedRank() > 0) {
                                        showRankChangeDialog(mHomePageModel);
                                    }

                                    if (mHomePageModel != null) {
                                        PromotionIncomeInfo incomeInfo = mHomePageModel.getPromotionIncomeInfo();
                                        if (incomeInfo != null) {
                                            boolean showDialog = ConfigureCenter.getInstance().getBool("tob", "Promotion_balance", false);
                                            if (showDialog && incomeInfo.showPromotionDialog) {
                                                showPromotionDialog(incomeInfo);
                                            }
                                        }
                                    }

                                    if (mHomePageModel != null && mHomePageModel.getAnchorValueInfo() != null) {
                                        AnchorValueInfo anchorValueInfo = mHomePageModel.getAnchorValueInfo();
                                        boolean isShowDialog = ConfigureCenter.getInstance().getBool("tob", "talent_anchor",false);
                                        if (isShowDialog && anchorValueInfo.getShowFlag() && !ViewUtil.haveDialogIsShowing(getActivity())) {
                                            String certificationType = mHomePageModel.getVerifyType() == 1 ? "personal" : "organization";
                                            showAnchorTitleDialog(anchorValueInfo,mHomePageModel.getNickname(),mHomePageModel.getMobileSmallLogo(),certificationType);
                                            new UserTracking()
                                                    .setSrcPage("主播工作台")
                                                    .setModuleType("主播认证说明弹窗")
                                                    .setId("5967")
                                                    .setCertificationType(certificationType)
                                                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_DYNAMIC_MODULE);
                                        }
                                    }
                                }
                            }

                            @Override
                            public void postException(Exception e) {
                                mHomePageModel = null;
                            }
                        });

                        try {
                            JSONObject jo = new JSONObject(object);
                            if (jo.has("anchorAnnounces")) {
                                AsyncGson<List<Announce>> gson1 = new AsyncGson<>();
                                gson1.fromJson(jo.optJSONObject("anchorAnnounces").optString("list"),
                                        new TypeToken<List<Announce>>() {
                                        }.getType(),
                                        new AsyncGson.IResult<List<Announce>>() {
                                            @Override
                                            public void postResult(List<Announce> result) {
                                                if (result != null) {
                                                    mAnnounceList = result;
                                                    setNoticeViews();
                                                }
                                            }

                                            @Override
                                            public void postException(Exception e) {

                                            }
                                        });
                            }
                            if (ToolUtil.isEmptyCollects(gifInfo) && jo.has("anchorOperationInfo")) {
                                AsyncGson<List<AnchorHolderItems>> gson2 = new AsyncGson<>();
                                gson2.fromJson(jo.optJSONObject("anchorOperationInfo").optString("anchorHolderItems"),
                                        new TypeToken<List<AnchorHolderItems>>() {
                                        }.getType(),
                                        new AsyncGson.IResult<List<AnchorHolderItems>>() {
                                            @Override
                                            public void postResult(List<AnchorHolderItems> result) {
                                                if (result != null) {
                                                    gifInfo = result;
                                                    setTitleBarViews();
                                                }
                                            }

                                            @Override
                                            public void postException(Exception e) {

                                            }
                                        });
                            }

                            anchorMissionProcessInfo = null;
                            if(jo.has("anchorMissionProcessInfo")) {
                                AsyncGson<AnchorMissionProcessInfo> gson3 = new AsyncGson<>();
                                gson3.fromJson(jo.optString("anchorMissionProcessInfo"),
                                        AnchorMissionProcessInfo.class,
                                        new AsyncGson.IResult<AnchorMissionProcessInfo>() {
                                            @Override
                                            public void postResult(AnchorMissionProcessInfo result) {
                                                anchorMissionProcessInfo = result;
                                                setAnchorMissionViews();
                                            }

                                            @Override
                                            public void postException(Exception e) {

                                            }
                                        });
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }

                });
            }

            @Override
            public void onError(int code, String message) {
                mHomePageModel = null;
            }
        });
    }

    private void showRankChangeDialog(HomePageModel homePageModel) {
        if (homePageModel == null || homePageModel.getAnchorIncreasedRank() == 0) {
            return;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateNowStr = sdf.format(System.currentTimeMillis());

        if (TextUtils.equals(dateNowStr, SharedPreferencesUtil.getInstance(mContext).getString(PreferenceConstantsInMain.KEY_LAST_DATE_SHOW_ANCHOR_RANK))) {
            return;
        }

        SharedPreferencesUtil.getInstance(mContext).saveString(PreferenceConstantsInMain.KEY_LAST_DATE_SHOW_ANCHOR_RANK, dateNowStr);

        AnchorRankDialogFragment anchorRankDialogFragment = new AnchorRankDialogFragment();
        Bundle args = new Bundle();
        args.putBoolean(AnchorRankDialogFragment.ANCHOR_RANK_IS_UP, homePageModel.getAnchorIncreasedRank() > 0);
        args.putInt(AnchorRankDialogFragment.ANCHOR_RANK_CHANGE_NUM, Math.abs(homePageModel.getAnchorIncreasedRank()));
        anchorRankDialogFragment.setArguments(args);
        anchorRankDialogFragment.show(getFragmentManager(), "AnchorRankDialogFragment");
    }

    //展示推广金弹框
    private void showPromotionDialog(final PromotionIncomeInfo incomeInfo) {
        if (!canUpdateUi()) {
            return;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateNowStr = sdf.format(System.currentTimeMillis());
        boolean isDataNotShow=TextUtils.equals(dateNowStr, SharedPreferencesUtil.getInstance(mContext).getString(PreferenceConstantsInMain.KEY_LAST_DATE_SHOW_PROMOTION_INCOME));
        boolean isSameUser=(uid+"").equals(SharedPreferencesUtil.getInstance(mContext).getString(PreferenceConstantsInMain.KEY_LAST_DATE_SHOW_PROMOTION_USER_ID));
        if (isDataNotShow&&isSameUser) {
            return;
        }

        SharedPreferencesUtil.getInstance(mContext).saveString(PreferenceConstantsInMain.KEY_LAST_DATE_SHOW_PROMOTION_INCOME, dateNowStr);
        SharedPreferencesUtil.getInstance(mContext).saveString(PreferenceConstantsInMain.KEY_LAST_DATE_SHOW_PROMOTION_USER_ID, uid+"");

        AnchorPromotionDialogFragment dialogFragment = new AnchorPromotionDialogFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable("incomeInfo", incomeInfo);
        dialogFragment.setArguments(bundle);
        dialogFragment.setOnPromotionLookListener(new AnchorPromotionDialogFragment.IPromotionLookListener() {
            @Override
            public void onPromotionLook() {
                if (!TextUtils.isEmpty(incomeInfo.linkUrl)) {
                    startWebPageOfPopularize(incomeInfo.linkUrl);
                }
            }
        });
        dialogFragment.show(getFragmentManager(), "AnchorPromotionDialogFragment");

    }

    private void showAnchorTitleDialog(AnchorValueInfo anchorValueInfo,String nickName,String logo,String certificationType) {
        if (anchorTitleSelectDialogFragment == null) {
            anchorTitleSelectDialogFragment = new AnchorTitleSelectDialogFragment();
        }
        if (anchorTitleSelectDialogFragment.getDialog() != null && anchorTitleSelectDialogFragment.getDialog().isShowing())
            return;
        Bundle bundle = new Bundle();
        bundle.putSerializable(AnchorTitleSelectDialogFragment.ANCHOR_VALUE_INFO, anchorValueInfo);
        bundle.putString(AnchorTitleSelectDialogFragment.ANCHOR_NICKNAME,nickName);
        bundle.putString(AnchorTitleSelectDialogFragment.ANCHOR_LOGO_URL,logo);
        bundle.putString(AnchorTitleSelectDialogFragment.ANCHOR_CERTIFICATION_TYPE,certificationType);
        anchorTitleSelectDialogFragment.setArguments(bundle);
        anchorTitleSelectDialogFragment.show(getFragmentManager(), "AnchorTitleSelectDialogFragment");
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_iv_head1) {
            //头像
            startFragment(AnchorSpaceUtil.newAnchorSpaceFragment(UserInfoMannage.getUid()), v);
        } else if (id == R.id.main_ll_anchor_grade_authorization_and_value) {
            if (isAnchorVerified()){
                Bundle bundle = new Bundle();
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, MainUrlConstants.getInstanse().getVAuthenticationUrl());
                startFragment(NativeHybridFragment.class, bundle, v);
                if (mHomePageModel != null) {
                    String anchorLevel = "V" + mHomePageModel.getAnchorGrade();
                    new UserTracking("主播工作台", "button").setSrcModule("主播等级").setItemId("当前等级")
                            .setAnchorLevel(anchorLevel).statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                }
            }
        } else if (id == R.id.main_anchor_goto_verify_bg) {
            if (BaseApplication.getTopActivity() instanceof MainActivity) {
                UserInfoMannage.goToAnchorVeriry((MainActivity) BaseApplication.getTopActivity());
            }
        } else if (id == R.id.main_anchor_grade_task){
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, MainUrlConstants.getInstanse().getUserGradeTaskListUrl());
            startFragment(NativeHybridFragment.class, bundle, v);
        } else if(id == R.id.main_anchor_grade_task_finish){
            if(anchorMissionProcessInfo != null) {
                if(anchorMissionProcessInfo.status == 1001) {
                    Bundle bundle = new Bundle();
                    bundle.putString(BundleKeyConstants.KEY_EXTRA_URL,anchorMissionProcessInfo.buttonLink);
                    startFragment(NativeHybridFragment.class, bundle, v);
                    new UserTracking("主播工作台", "button").setSrcModule("等级任务")
                            .setItemId(anchorMissionProcessInfo.buttonContent)
                            .setTaskId(anchorMissionProcessInfo.missionId)
                            .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                } else if (anchorMissionProcessInfo.status == 1002) {
                    Map<String, String> params = new ArrayMap<>(1);
                    params.put("missionId", String.valueOf(anchorMissionProcessInfo.missionId));
                    final int score = anchorMissionProcessInfo.score;
                    MainCommonRequest.getAnchorMissionScore(params, new IDataCallBack<String>() {
                        @Override
                        public void onSuccess(@Nullable String object) {
                            if (!canUpdateUi()){
                                return;
                            }
                            if(!TextUtils.isEmpty(object)) {
                                if("SUCCESS".equals(object)) {
                                    requestPageInfo();
                                    AnchorGradeToast.getInstanse().showToast(getContext(), null, "+" + score);
                                    setTranslateAnimation();
                                } else {
                                    CustomToast.showFailToast(object);
                                    requestPageInfo();
                                    setTranslateAnimation();
                                }
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            CustomToast.showFailToast(message);
                        }
                    });
                    new UserTracking("主播工作台", "button").setSrcModule("等级任务").setItemId("领奖励")
                            .setTaskId(anchorMissionProcessInfo.missionId)
                            .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                }
            }
        } else if (id == R.id.main_tv_track_count) {
            //我的作品
            new UserTracking().setSrcPage("主播工作台").setSrcModule("顶部功能栏").setItem("button").setItemId("我的作品").statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            if (UserInfoMannage.getInstance().getUser() != null) {
                try {
                    BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newMyProgramsFragmentNew(IRecordFragmentAction.TAB_ALBUM);
                    if (fragment != null) {
                        startFragment(fragment);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else if (id == R.id.main_tv_record) {
            //我要录音
            new UserTracking().setSrcPage("主播工作台").setSrcModule("顶部功能栏").setItem("button").setItemId("我要录音").statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            try {
                ((MainActivity) mActivity).startFragment(Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newFragmentByFid(Configure.RecordFragmentFid.RECORD_TRACK_FRAGMENT));
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (id == R.id.main_tv_my_living) {
            //我的直播
            new UserTracking().setSrcPage("主播工作台").setSrcModule("顶部功能栏").setItem("button").setItemId("我的直播").statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            try {
                Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFragmentAction().startMyLivesFragment((MainActivity) getActivity());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (id == R.id.main_tv_live) {
            //我要直播
            new UserTracking().setSrcPage("主播工作台").setSrcModule("顶部功能栏").setItem("button").setItemId("我要直播").statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);

            if (!NetworkUtils.isNetworkAvaliable(mContext)) {
                CustomToast.showFailToast("网络不可用，请检查网络设置");
                return;
            }
            Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (bundleModel != null && TextUtils.equals(bundleModel.bundleName, Configure.liveBundleModel.bundleName)) {
                        

                        try {
                            LiveActionRouter actionRouter = Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE);

                            if (actionRouter != null) {
                                actionRouter.getFunctionAction().openCreateLiveSelectTypeDialog(mActivity,
                                        new IDataCallBack<Integer>() {
                                            @Override
                                            public void onSuccess(@Nullable Integer resNum) {

                                            }

                                            @Override
                                            public void onError(int code, String message) {


                                            }
                                        });
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            CustomToast.showFailToast("加载直播模块出现异常，请稍后重试");
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });
        } else if (id == R.id.main_notice_icon) {
            //主播头条
            closeNoticePop();
            startFragment(new XiMaNoticeFragment());
        } else if (id == R.id.main_profit_account) {
            //我的收益
            if (mHomePageModel == null) return;

            if (!isProfitOpened()) {
                if (BaseApplication.getTopActivity() instanceof MainActivity) {
                    UserInfoMannage.goToAnchorVeriry((MainActivity) BaseApplication.getTopActivity());
                }

                new UserTracking("主播工作台", "button").setSrcModule("我的收益").setItemId("立即认证")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            } else {
                Bundle bundle = new Bundle();
                String url = UrlConstants.getInstanse().getBusinessHost()
                        + "revenue/user/" + uid + "?timestamp=" + System.currentTimeMillis();
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
                startFragment(NativeHybridFragment.class, bundle, v);

                new UserTracking("主播工作台", "button").setSrcModule("我的收益").setItemId("本月收入")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }
        } else if (id == R.id.main_profit_group) {
            //收益中心
            if (mHomePageModel == null) return;

            if (!isProfitOpened()) {
                if (BaseApplication.getTopActivity() instanceof MainActivity) {
                    UserInfoMannage.goToAnchorVeriry((MainActivity) BaseApplication.getTopActivity());
                }

                new UserTracking("主播工作台", "button").setSrcModule("我的收益").setItemId("我的收益认证")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            } else {
                Bundle bundle = new Bundle();
                String url = MainUrlConstants.getInstanse().getWebOfAnchorProfit() + UserInfoMannage.getUid();
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);

                final NativeHybridFragment webFragment = new NativeHybridFragment();
                webFragment.setArguments(bundle);
                webFragment.setCustomWebClient(new WebViewClient() {
                    @Override
                    public void onPageStarted(WebView view, final String url, Bitmap favicon) {
                        super.onPageStarted(view, url, favicon);
                        //请求需要展示"主播技能入口"的H5页面urls
                        MainCommonRequest.getUrlsOfShowAnchorSkillEntry(new IDataCallBack<List<String>>() {
                            @Override
                            public void onSuccess(@Nullable List<String> object) {
                                if (!ToolUtil.isEmptyCollects(object)) {
                                    boolean isMatch = false;//匹配上的展示入口
                                    for (String u : object) {
                                        if (url.startsWith(u)) {
                                            isMatch = true;
                                            break;
                                        }
                                    }
                                    if (isMatch) {
                                        mMagneticView = new MagneticView(mContext, Configure.AnchorSkillEntranceId.PROFIT);
                                        mMagneticView.show(webFragment, null);
                                    } else {
                                        if (mMagneticView != null) {
                                            mMagneticView.dismiss(webFragment);
                                        }
                                    }
                                } else {
                                    if (mMagneticView != null) {
                                        mMagneticView.dismiss(webFragment);
                                    }
                                }
                            }

                            @Override
                            public void onError(int code, String message) {
                                CustomToast.showFailToast(message);
                            }
                        });
                    }
                });
                startFragment(webFragment, v);

                new UserTracking("主播工作台", "page").setSrcModule("我的收益").setItemId("收益中心")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }
        } else if (id == R.id.main_anchor_manage_profit_expand_iv) {
            //点击展开或收起 我的收益
            expandMyProfit();

        } else if (id == R.id.main_anchor_shop_group) {
            if(ConfigureCenter.getInstance().getBool(Group_tob.GROUP_NAME,Group_tob.ITEM_E_COMMERCE_TOOL,false)){
                //是否存在主播电商的入口
                String shopUrl=ConfigureCenter.getInstance().getString(Group_tob.GROUP_NAME,Group_tob.ITEM_E_COMMERCE_TOOL_URL,
                    URL_COMMERCE_TOOL);
                    ToolUtil.clickUrlAction(ManageCenterFragment.this,shopUrl,v);
                    new UserTracking("主播工作台", "button").setSrcModule("我的收益").setItemId("电商助手")
                            .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);

            }else {
                //跳转我的问答
                String url = MainUrlConstants.getInstanse().getAnchorHotLine(uid);
                Bundle bundle = new Bundle();
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
                startFragment(NativeHybridFragment.class, bundle, v);
                new UserTracking("主播工作台", "button").setSrcModule("我的收益").setItemId("我的问答")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }

        } else if (id == R.id.main_mic_task_rl) {
            //微任务
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, MainUrlConstants.getInstanse().getMicTaskUrl());
            startFragment(NativeHybridFragment.class, bundle, v);

            new UserTracking("主播工作台", "button").setSrcModule("我的收益").setItemId("蜜声")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        } else if (id == R.id.main_answer_group) {
            //问答
            String url = MainUrlConstants.getInstanse().getAnchorHotLine(uid);
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
            startFragment(NativeHybridFragment.class, bundle, v);

            new UserTracking("主播工作台", "button").setSrcModule("我的收益").setItemId("我的问答")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        } else if (id == R.id.main_audio_plus_group) {
            //有声化平台
            String url = UrlConstants.getInstanse().getAudioPlusHost();
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
            startFragment(NativeHybridFragment.class, bundle, v);

            new UserTracking("主播工作台", "button").setSrcModule("我的收益").setItemId("有声化平台")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        }  else if (id == R.id.main_refund_group) {
            //专辑退款
            String url = MainUrlConstants.getInstanse().getRefundWebUrl();
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
            startFragment(NativeHybridFragment.class, bundle, v);
        } else if (id == R.id.main_album_comment_group) {
            //专辑评价
            BaseFragment fragment = AlbumListFragment.newInstanceByPayComment(UserInfoMannage.getUid(), ConstantsOpenSdk.PLAY_FROM_OTHER);
            startFragment(fragment, v);
        }  else if (id == R.id.main_my_manual_subtitle) {
            //主播手册
            toWebOfAnchorManual();

            new UserTracking("主播工作台", "button").setSrcModule("主播手册").setItemId("小锦囊")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        } else if (id == R.id.main_funs_group) {
            //我的粉丝
            BaseFragment fra = MyAttentionFragmentNew.Companion.newInstance(uid, SharedConstant.FLAG_FANS,
                    SharedConstant.MY_ATTENTION_FRAGMENT_FROM_MY_SPACE);
            startFragment(fra);

            new UserTracking("主播工作台", "button").setSrcModule("社群管理").setItemId("我的粉丝")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        } else if (id == R.id.main_chat_group) {
            //我的群组
            try {
                BaseFragment fra = Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newGroupListFragment(true, -1, -1, false);
                startFragment(fra);
            } catch (Exception e) {
                e.printStackTrace();
            }

            new UserTracking("主播工作台", "button").setSrcModule("社群管理").setItemId("我的群组")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        } else if (id == R.id.main_track_comment_group) {
            //声音评论
            try {
                BaseFragment fragment = Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newFragmentByFid(Configure.ChatFragmentFid.COMMON_NOTICE_FRAGMENT);
                if (fragment != null)
                    startFragment(fragment, v);
            } catch (Exception e) {
                e.printStackTrace();
            }

            new UserTracking("主播工作台", "button").setSrcModule("社群管理").setItemId("声音评论")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        } else if (id == R.id.main_circle_group) {
            //听友圈消息
            Router.getActionByCallback(Configure.BUNDLE_FEED, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (bundleModel != null && bundleModel == Configure.feedBundleModel) {
                        
                        if (canUpdateUi()) {
                            try {
                                BaseFragment2 fragment = Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFragmentAction().newListenerGroupMessageFragment();
//                                BaseFragment2 fragment = Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newListenerGroupMessageFragment();
                                if (fragment != null)
                                    startFragment(fragment);
                            } catch (Exception e) {
                                e.printStackTrace(); }
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });

            new UserTracking("主播工作台", "button").setSrcModule("社群管理").setItemId("听友圈消息")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
        } else if (id == R.id.main_track_free_album_comment_group) {
            if(!UserInfoMannage.hasLogined()){
                UserInfoMannage.gotoLogin(mContext);
                return;
            }
            // 我的全部免费专辑
            startFragment(new MyAllAlbumCommentsFragment());
        } else if (id == R.id.main_anchor_copyright_protection_title) {
            if(!UserInfoMannage.hasLogined()){
                UserInfoMannage.gotoLogin(mContext);
                return;
            }
            //版权保护
            String url = ConfigureCenter.getInstance().getString("tob", "tort_anchor_url", null);
            if(!TextUtils.isEmpty(url)) {
                startFragment(NativeHybridFragment.newInstance( url, true));
            }
        } else if (id == R.id.main_anchor_my_credit_title) {
            //我的信用分
           openAnchorCreditPage();
        } else if (id==R.id.main_tv_credit_score){
            //头部我的信用分按钮
            openAnchorCreditPage();
            new UserTracking("主播工作台", "button").setSrcModule("主播信用分").setItemId("当前信用分")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);

        } else if (id == R.id.main_album_promotion) {
            new UserTracking().setSrcPage("主播工作台").setSrcModule("我的数据").setItem("button").setItemId("查看更多数据").statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            gotoUserDataCenter(1);
        } else if (id == R.id.main_play_item || id == R.id.main_anchor_data_play_ll) {
            new UserTracking().setSrcPage("主播工作台").setSrcModule("我的数据").setItem("button").setItemId("播放").statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            gotoUserDataCenter(1);
        } else if (id == R.id.main_subscribe_item || id == R.id.main_anchor_data_subscribe_ll) {
            new UserTracking().setSrcPage("主播工作台").setSrcModule("我的数据").setItem("button").setItemId("订阅").statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            gotoUserDataCenter(2);
        } else if (id == R.id.main_anchor_data_share_ll) {
            new UserTracking().setSrcPage("主播工作台").setSrcModule("我的数据").setItem("button").setItemId("分享").statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            gotoUserDataCenter(3);

        }
    }

    /**
     *展开或收起我的收益模块
     * */
    private void expandMyProfit() {
        if (vAnswerGroup != null) {
            if (vAnswerGroup.getVisibility() == View.GONE) {
                vAnswerGroup.setVisibility(View.VISIBLE);
                Animation anim = new RotateAnimation(0f, 180f, Animation.RELATIVE_TO_SELF,
                        0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
                anim.setFillAfter(true);
                anim.setDuration(300);
                mProfitExpandIv.startAnimation(anim);
            } else {
                vAnswerGroup.setVisibility(View.GONE);
                Animation anim = new RotateAnimation(180f, 0f, Animation.RELATIVE_TO_SELF,
                        0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
                anim.setFillAfter(true);
                anim.setDuration(300);
                mProfitExpandIv.startAnimation(anim);
            }
        }
    }

    private void gotoUserDataCenter(int centerHome) {
        //centerHome   1:播放  2:订阅   3:分享

        if (isAnchorVerified()) {
            String webUrl = MainUrlConstants.getInstanse().getAnchorDataCenter(centerHome);
            NativeHybridFragment webFragment = new NativeHybridFragment();
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, webUrl);
            webFragment.setArguments(bundle);
            startFragment(webFragment);
        } else {
            if (BaseApplication.getTopActivity() instanceof MainActivity) {
                UserInfoMannage.goToAnchorVeriry((MainActivity) BaseApplication.getTopActivity());
            }
        }

    }

    private void toWebOfAnchorManual() {
        String url = MainUrlConstants.getInstanse().getWebOfSkillIndex();
        startRnPageWebOfAnchorManual(url);
    }

    private void setTranslateAnimation() {
        WindowManager wm = SystemServiceManager.getWindowManager(getContext());
        DisplayMetrics dm = new DisplayMetrics();
        if(wm != null && wm.getDefaultDisplay() != null) {
            wm.getDefaultDisplay().getMetrics(dm);
        }
        final int width = dm.widthPixels;

        Spring spring = SpringSystem.create().createSpring();
        spring.setSpringConfig(SpringConfig.fromOrigamiTensionAndFriction(20, 5));
        spring.addListener(new SimpleSpringListener(){
            @Override
            public void onSpringUpdate(Spring spring) {
                float value = (float) spring.getCurrentValue();
                vRankLay.setTranslationX(value);
            }

            @Override
            public void onSpringAtRest(Spring spring) {
                if(anchorMissionProcessInfo != null) {
                    Spring spring2 = SpringSystem.create().createSpring();
                    spring2.setSpringConfig(SpringConfig.fromOrigamiTensionAndFriction(80, 5));
                    spring2.addListener(new SimpleSpringListener() {
                        @Override
                        public void onSpringUpdate(Spring spring) {
                            float value = (float) spring.getCurrentValue();
                            vRankLay.setTranslationX(value);
                        }
                    });
                    spring2.setCurrentValue(width);
                    spring2.setEndValue(0);  //使任务栏从屏幕右侧滑入
                } else {
                    vRankLay.setVisibility(View.GONE);
                }
            }
        });
        spring.setCurrentValue(0);
        spring.setEndValue(-width); //使任务栏左滑出去
        spring.setRestSpeedThreshold(1000); //使左滑出去的动画尽快进入rest状态
        spring.setRestDisplacementThreshold(10);
    }

    /**
     * 打开推广页面
     */
    private void startWebPageOfPopularize(String url){
        NativeHybridFragment webFragment = new NativeHybridFragment();
        Bundle bundleH5 = new Bundle();
        bundleH5.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
        bundleH5.putBoolean(IWebFragment.CUSTOM_SHARE_BUTTON, true);
        webFragment.setArguments(bundleH5);
        startFragment(webFragment);
    }

    /**
     * 打开主播技能页面---H5
     */
    private void startWebPageWebOfAnchorManual(final String url){
        NativeHybridFragment webFragment = new NativeHybridFragment();
        Bundle bundleH5 = new Bundle();
        bundleH5.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
        bundleH5.putBoolean(IWebFragment.CUSTOM_SHARE_BUTTON, true);
        webFragment.setArguments(bundleH5);
        startFragment(webFragment);
        final WeakReference<NativeHybridFragment> reference = new WeakReference(webFragment);
        MainCommonRequest.getSkillEntrySetting(new IDataCallBack<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean object) {
                if (object != null && object && reference.get() != null) {
                    TitleBar.ActionType actionType = new TitleBar.ActionType(
                            "tagSetting", TitleBar.RIGHT, R.string.main_setup, 0, 0, TextView.class);
                    actionType.setFontSize(14);
                    actionType.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            startFragment(new AnchorSkillSettingFragment());
                        }
                    });
                    reference.get().addTitleBarAction(actionType);
                }
            }

            @Override
            public void onError(int code, String message) {
            }
        });
    }
    /**
     * 打开主播技能页面---Rn
     */
    private void startRnPageWebOfAnchorManual(final String url) {
        //优先打开rn页面，打开失败使用H5
        try {
            Bundle bundleRn = new Bundle();
            bundleRn.putString("bundle", "RNskill");
            //url给rn，rn会解析h5的参数
            bundleRn.putString("h5Url", url);
            final BaseFragment fragment = Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).getFragmentAction().newRNFragment("rn", bundleRn, new IRNFragmentRouter.ILoadBundleErrorInterceptor() {
                @Override
                public boolean onLoadError(BaseFragment fgm) {
                    startWebPageWebOfAnchorManual(url);
                    if (fgm instanceof BaseFragment2) {
                        ((BaseFragment2) fgm).finish();
                    }
                    return true;
                }
            });
            // fragment 如果为空 降级
            if (fragment != null) {
                startFragment(fragment);
            } else {
                startWebPageWebOfAnchorManual(url);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private PopupWindow noticePop;
    private ArrayList<WeakReference<PopupWindow>> mPopups;

    private void showNoticePop() {

    }

    private void showPopupWindow(final String key, final View attach, final int hintResId) {
        if (!canUpdateUi()) {
            return;
        }
        boolean needShow = SharedPreferencesUserUtil.getInstance(mContext).getBoolean(key, true);
        if (!needShow) {
            return;
        }
        try {
            List<CustomTipsView.Tips> tipsList = new ArrayList<>(1);
            CustomTipsView.Tips myLiveTip = new CustomTipsView.Tips(
                    getStringSafe(hintResId), attach, CustomTipsView.DOWN, key);
            tipsList.add(myLiveTip);
            mDataCenterWindowTips = new CustomTipsView(getActivity());
            mDataCenterWindowTips.setTipsMap(tipsList);
            attach.post(new Runnable() {
                @Override
                public void run() {
                    if (mDataCenterWindowTips != null) {
                        mDataCenterWindowTips.showAllTips();
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 展示"我的直播"首次进入Tips
     */
    private void showMyLivingTips() {
        if (!mCreateTipPosting) {
            HandlerManager.postOnUIThreadDelay(getCreateDynamicTip(), 500);
            mCreateTipPosting = true;
        }


    }

    private Runnable mCreateDynamicTip;
    private boolean mCreateTipPosting;

    private Runnable getCreateDynamicTip() {
        if (mCreateDynamicTip == null) {
            mCreateDynamicTip = new Runnable() {
                @Override
                public void run() {
                    if (!canUpdateUi()) {
                        return;
                    }
                    try {
                        doAfterAnimation(new IHandleOk() {
                            @Override
                            public void onReady() {
                                List<CustomTipsView.Tips> tipsList = new ArrayList<>(1);
                                CustomTipsView.Tips myLiveTip = new CustomTipsView.Tips(
                                        getStringSafe(R.string.main_my_living_more), mMyLivingTv, CustomTipsView.DOWN, CustomTipsView.KEY_TIPS_MY_LIVING);
                                tipsList.add(myLiveTip);
                                tipsView = new CustomTipsView(getActivity());
                                tipsView.setTipsMap(tipsList);
                                mMyLivingTv.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        tipsView.showAllTips();
                                        mCreateDynamicTip = null;
                                    }
                                });
                            }
                        });

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            };

        }
        return mCreateDynamicTip;
    }


    private void closeNoticePop() {
        if (noticePop != null && noticePop.isShowing()) {
            noticePop.dismiss();
            noticePop = null;
        }
    }

    private void hidePopupsDelay(int delay) {
        if (delay > 0 && mContainerView != null) {
            mContainerView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    hidePopups();
                }
            }, delay);
        }
    }

    private void hidePopups() {
        if (mPopups == null || mPopups.isEmpty()) return;
        for (int index = 0; index < mPopups.size(); index++) {
            WeakReference<PopupWindow> soft = mPopups.get(index);
            if (soft == null || soft.get() == null) continue;
            PopupWindow popupWindow = soft.get();
            if (popupWindow.isShowing()) {
                popupWindow.dismiss();
            }
        }
    }

    enum ModuleTitle {
        MY_DATA("mydata", "我的数据"),
        MY_PROFIT("my_income", "我的收益"),
        COMMUNITY("my_community ", "社群管理"),
        SPREAD("spread", "我要推广"),
        PAY_MANAGEMENT("pay_management", "付费管理"),
        MANUAL("manual", "主播手册");

        private String configName;
        private String defaultTitle;

        ModuleTitle(String configName, String defaultTitle) {
            this.configName = configName;
            this.defaultTitle = defaultTitle;
        }

        public String getTitle() {
            return ConfigureCenter.getInstance().getString("tob", configName, defaultTitle);
        }
    }

    enum PermissionMenu {
        PROFIT_CENTER("income_center", "收益中心", "开通享受广告分成", "设置个性赞赏语"),
        SHARE_ACHIEVEMENT("my_achievement", "晒我的成就", "认证升级秀成就", "酒香也怕巷子深");

        private String defTitle;
        private String defNoRights;
        private String defRights;

        private String title;
        private String rights;
        private String noRights;

        PermissionMenu(String configName, String defTitle, String defNoRights, String defRights) {
            this.defTitle = defTitle;
            this.defNoRights = defNoRights;
            this.defRights = defRights;
            String s = ConfigureCenter.getInstance().getString("tob", configName, null);
            if (!TextUtils.isEmpty(s)) {
                String[] strings = s.split(";");
                if (strings.length == 3) {
                    title = strings[0];
                    noRights = strings[1];
                    rights = strings[2];
                }
            }
        }

        public String getTitle() {
            return !TextUtils.isEmpty(title) ? title : defTitle;
        }

        public String getRights() {
            return !TextUtils.isEmpty(rights) ? rights : defRights;
        }

        public String getNoRights() {
            return !TextUtils.isEmpty(noRights) ? noRights : defNoRights;
        }
    }

    enum NormalMenu {
        QUESTION_ANSWER("my_question", "我的问答", "答疑解惑得报酬"),
        AUDIO_PLUS("Audio+", "喜配音", "朗读书籍拿工资"),
        TRACK_COMMENT("track_comment", "声音评论", "常互动拉好感"),
        CIRCLE_MSG("myLive_mes", "听友圈消息", "今天发动态了吗"),
        POPULARIZE("my_application", "自主投放推广", "让专辑上首页"),
        ALBUM_REFUND("album_refund", "专辑退款", "暂无新退款"),
        ALBUM_COMMENT("album_comment", "付费专辑评价", "多回复销量高"),
        NEW_DRIVER("freshman_manual", "新手主播攻略", "1分钟打好基本功"),
        RECORD_MANUAL("recording_manual", "手机录音教程", "这样录音声音更动听"),
        PROFIT_MANUAL("income_manual", "主播收益", "填满你的小金库"),
        LIVING_MANUAL("live_manual", "直播节目手册", "零门槛玩转直播");

        private String defTitle;
        private String defSubtitle;

        private String title;
        private String subtitle;

        NormalMenu(String configName, String defTitle, String defSubtitle) {
            this.defTitle = defTitle;
            this.defSubtitle = defSubtitle;
            String s = ConfigureCenter.getInstance().getString("tob", configName, null);
            if (!TextUtils.isEmpty(s)) {
                String[] strings = s.split(";");
                if (strings.length == 2) {
                    title = strings[0];
                    subtitle = strings[1];
                }
            }
        }

        public String getTitle() {
            return !TextUtils.isEmpty(title) ? title : defTitle;
        }

        public String getSubtitle() {
            return !TextUtils.isEmpty(subtitle) ? subtitle : defSubtitle;
        }
    }

    public static class SkillThemeId {
        @SerializedName("id")
        private int id;
        @SerializedName("name")
        private String name;
    }

    /**
     * 打开我的信用分页面
     */
    private void openAnchorCreditPage(){
        String webUrl;
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            //线上环境
            webUrl = MainUrlConstants.getInstanse().getWebOfAnchorCredit();
        } else {
            //测试环境
            webUrl = "http://m.test.ximalaya.com/credit/h5/violations/ts_" + System.currentTimeMillis();
        }
        NativeHybridFragment webFragment = new NativeHybridFragment();
        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, webUrl);
        webFragment.setArguments(bundle);
        startFragment(webFragment);
    }

    public static class VerticalImageSpanNew extends ImageSpan {
        int topoffset = 0;
        int leftOffset = 0;

        public VerticalImageSpanNew(Context context, Bitmap bitmap, int topoffset, int leftOffset) {
            super(context, bitmap);
            this.topoffset = topoffset;
            this.leftOffset = leftOffset;
        }

        public void draw(Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, Paint paint) {
            Drawable drawable = this.getDrawable();
            canvas.save();
            int transY = (bottom - top - drawable.getBounds().bottom) / 2 + top;
            canvas.translate(x + leftOffset, (float) transY + topoffset);
            drawable.draw(canvas);
            canvas.restore();
        }
    }
}
