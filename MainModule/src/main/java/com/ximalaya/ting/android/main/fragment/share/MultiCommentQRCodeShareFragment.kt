package com.ximalaya.ting.android.main.fragment.share

import android.Manifest
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.RectF
import android.graphics.Paint
import android.graphics.Canvas
import android.graphics.PorterDuff
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Environment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.ScrollView
import android.widget.TextView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.ShareResultManager
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction.IPermissionListener
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType
import com.ximalaya.ting.android.host.manager.share.ShareWay
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel
import com.ximalaya.ting.android.host.manager.share.SharePanelView
import com.ximalaya.ting.android.host.manager.share.ShareManager.OnShareDstTypeSelectListener
import com.ximalaya.ting.android.host.manager.share.ShareConstants
import com.ximalaya.ting.android.host.manager.share.assist.ShareProcessStatics
import com.ximalaya.ting.android.host.manager.share.panel.SharePanelType
import com.ximalaya.ting.android.host.model.play.CommentModel
import com.ximalaya.ting.android.host.model.play.CommentModelWrapper
import com.ximalaya.ting.android.host.model.share.ShareContentModel
import com.ximalaya.ting.android.host.model.share.ShareUbtData
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.QRImageUtil
import com.ximalaya.ting.android.host.util.ShareUtils
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.other.PermissionManage
import com.ximalaya.ting.android.host.util.view.LocalImageUtil
import com.ximalaya.ting.android.host.util.view.TitleBar
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.commentModule.holder.NormalViewHolder
import com.ximalaya.ting.android.main.fragment.dialog.QuestRewardFragment
import com.ximalaya.ting.android.main.model.QuestReward
import com.ximalaya.ting.android.main.model.comment.TrackCommentShareLabel
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.util.FixedThreadPool
import com.ximalaya.ting.android.main.util.TrackCommentShareUtil
import com.ximalaya.ting.android.main.view.QuestRewardUtil
import com.ximalaya.ting.android.main.view.text.staticlayoutview.StaticLayoutManagerNew
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.shareservice.AbstractShareType
import com.ximalaya.ting.android.shareservice.base.IShareDstType
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import kotlin.collections.set

/**
 * Created by WolfXu on 6/30/21.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
open class MultiCommentQRCodeShareFragment : BaseFragment2(AppConstants.isPageCanSlide, null) {

    companion object {
        const val ITEM_TYPE_NORMAL = 0
        const val ITEM_TYPE_HEAD = 1
        const val BUNDLE_KEY_HEAD_COMMENT = "head_comment"
        const val BUNDLE_KEY_REPLY_COMMENTS = "reply_comments"
        const val BUNDLE_KEY_ALL_COMMENTS_COUNT = "all_comments_count"
        const val SHARE_SRC_TYPE = 7
        const val SHARE_SUB_TYPE = 1103

        @JvmStatic
        fun newInstance(bundle: Bundle): MultiCommentQRCodeShareFragment {
            val fragment = MultiCommentQRCodeShareFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    private var mShareContentModel: ShareContentModel? = null
    private var mShareWrapContentModel: ShareWrapContentModel? = null
    private var mScrollView: ScrollView? = null
    private var mVgPosterContent: ViewGroup? = null
    private var mLlList: LinearLayout? = null

    private var mCommentModel: CommentModel? = null
    private var mAllCommentModels: MutableList<CommentModel>? = ArrayList()
    private var mBgColor = -0x676768 // 增加默认背景色
    private var mTvShareLayoutTitle: TextView? = null

    override fun getPageLogicName(): String {
        return "MultiCommentQRCodeShareFragment"
    }

    override fun onDestroyView() {
        super.onDestroyView()
        StatusBarManager.hideStatusBar(window, false)
    }

    override fun onDestroy() {
        super.onDestroy()
        ShareResultManager.getInstance().clearShareFinishListener()
    }

    override fun initUi(savedInstanceState: Bundle?) {
        val top =
            if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) BaseUtil.getStatusBarHeight(mContext) else 0
        val mTvTrackTitle = findViewById(R.id.main_tv_track_title) as? TextView
        val tvAlbumTitle = findViewById(R.id.main_tv_album_title) as? TextView
        val rlScrollContainer = findViewById(R.id.main_rl_scroll_container) as? RelativeLayout
        mScrollView = findViewById(R.id.main_share_poster_scroll_view) as? ScrollView
        mVgPosterContent = findViewById(R.id.main_rl_content) as? ViewGroup
        mLlList = findViewById(R.id.main_ll_list) as? LinearLayout
        mTvShareLayoutTitle = findViewById(R.id.main_tv_share_panel_title)
        rlScrollContainer?.setPadding(0, top, 0, 0)

        arguments?.let {
            mCommentModel = it.getSerializable(BUNDLE_KEY_HEAD_COMMENT) as CommentModel?
            val commentModelWrapper =
                it.getSerializable(BUNDLE_KEY_REPLY_COMMENTS) as CommentModelWrapper?
            val replyCommentModels = commentModelWrapper?.commentModels
            val allCommentsCount = it.getInt(BUNDLE_KEY_ALL_COMMENTS_COUNT)

            if (mCommentModel != null) {
                val allHeaderModel = CommentModel()
                allHeaderModel.groupType = ITEM_TYPE_HEAD
                allHeaderModel.content = String.format(
                    "%s 精彩声音评论",
                    StringUtil.getCommentCountFriendlyStr(allCommentsCount)
                )
                mAllCommentModels!!.add(allHeaderModel)
                mCommentModel!!.groupType = ITEM_TYPE_NORMAL
                mAllCommentModels!!.add(mCommentModel!!)

                if (!ToolUtil.isEmptyCollects(replyCommentModels)) {
                    val replyAllHeaderModel = CommentModel()
                    replyAllHeaderModel.groupType = ITEM_TYPE_HEAD
                    replyAllHeaderModel.content = String.format("%s条回复", mCommentModel!!.replyCount)
                    mAllCommentModels!!.add(replyAllHeaderModel)
                    mAllCommentModels!!.addAll(replyCommentModels!!)
                }
            }
            if (TrackCommentShareUtil.isPlayFollowGuideDialogFragmentShownAvailable() &&
                TrackCommentShareUtil.getTrackCommentShareLabel() != null
            ) {
                mTvShareLayoutTitle?.text = TrackCommentShareUtil.getTrackCommentShareLabel().label
            } else {
                mTvShareLayoutTitle?.text = "分享至"
            }
            mTvTrackTitle?.text = mCommentModel?.trackTitle
            tvAlbumTitle?.text = mCommentModel?.albumTitle
            queryShareTrackData(mCommentModel?.trackId)
        }

        ShareResultManager.getInstance().setShareFinishListener(object :
            ShareResultManager.ShareListener {
            override fun onShareSuccess(thirdName: String) {
                openQuestRewardFragment()
            }

            override fun onShareFail(thirdName: String) {
                // 不需要此回调
            }
        })

        // 当高度比较小时，让海报能显示在中间
        mScrollView?.let {
            it.post {
                val contentHeight = mVgPosterContent?.height
                val scrollViewHeight = mScrollView?.height
                if (contentHeight != null && scrollViewHeight != null && contentHeight < scrollViewHeight) {
                    it.layoutParams?.height = ViewGroup.LayoutParams.WRAP_CONTENT
                    it.layoutParams = it.layoutParams
                }
            }
        }
    }

    private fun openQuestRewardFragment() {
        if (TrackCommentShareUtil.isPlayFollowGuideDialogFragmentShownAvailable()) {
            val trackCommentShareLabel: TrackCommentShareLabel =
                TrackCommentShareUtil.getTrackCommentShareLabel() ?: return
            val params = hashMapOf<String, Int>()
            params["aid"] = trackCommentShareLabel.aid
            params["taskId"] = trackCommentShareLabel.taskId
            CommonRequestM.refreshClientTaskV2(params, null)

            TrackCommentShareUtil.savePlayFollowGuideDialogFragmentShown()

            val fragment = QuestRewardFragment()
            val bundle = Bundle()
            val questReward = QuestReward()
            questReward.award = trackCommentShareLabel.count
            questReward.content = trackCommentShareLabel.toast
            bundle.putInt("type", 0)
            bundle.putParcelable("reward", questReward)
            bundle.putInt("actionType", QuestRewardUtil.TYPE_COMMENT)
            bundle.putInt("from", QuestRewardUtil.FROM_MULTI_COMMENT_PAGE)
            fragment.arguments = bundle
            fragment.show(getChildFragmentManager(), "")
            HandlerManager.obtainMainHandler().postDelayed({
                if (fragment.canUpdateUi()) {
                    fragment.dismiss()
                }
            }, 5000)
        } else {
            CustomToast.showSuccessToast("分享成功！")
        }
    }

    override fun loadData() {
        // 计算高度总是有问题....
//        mLlList?.let {
//            it.addOnLayoutChangeListener(object : View.OnLayoutChangeListener {
//                override fun onLayoutChange(
//                    v: View?,
//                    left: Int,
//                    top: Int,
//                    right: Int,
//                    bottom: Int,
//                    oldLeft: Int,
//                    oldTop: Int,
//                    oldRight: Int,
//                    oldBottom: Int
//                ) {
//                    it.removeOnLayoutChangeListener(this)
//
//                    var tempSize = it.height
//                    if (tempSize > 3500) {
//                        for (i in it.childCount - 1 downTo 0) {
//                            val childView = it.getChildAt(i)
//                            tempSize -= childView.height
//                            it.removeView(childView)
//                            if (tempSize <= 3500) {
//                                break
//                            }
//                        }
//                    }
//                }
//            })
//        }
        val staticLayoutManagerNew = StaticLayoutManagerNew()
        staticLayoutManagerNew.initLayout(
            context,
            BaseUtil.getScreenWidth(context) - BaseUtil.dp2px(context, 100f)
        )

        for (i in 0 until mAllCommentModels!!.size) {
            val commentModel: CommentModel = mAllCommentModels!!.get(i)
            val view: View
            if (commentModel.groupType == ITEM_TYPE_HEAD) {
                view = LayoutInflater.from(mContext)
                    .inflate(R.layout.main_item_multiple_comment_header, null)
                view.findViewById<TextView>(R.id.main_tv_header)?.text = commentModel.content
                mLlList?.addView(view)
            } else {
                view = LayoutInflater.from(mContext)
                    .inflate(R.layout.main_item_multi_track_comment, null)
                val normalViewHolder = NormalViewHolder(view, staticLayoutManagerNew)
                normalViewHolder.bindViewDatas(commentModel, i)
                mLlList?.addView(normalViewHolder.convertView)
            }
        }
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_multiple_comment_qrcode_share
    }

    private fun queryShareTrackData(trackId: Long?) {
        if (trackId!! <= 0) {
            return
        }
        val params: MutableMap<String, String> = HashMap()
        params["srcType"] = "7"
        params["subType"] = "4"
        params["trackId"] = trackId.toString()
        params["commentId"] = mCommentModel!!.id.toString()
        params["srcId"] = mCommentModel!!.id.toString()
        getShareContent(params)
    }

    private fun getShareContent(params: Map<String, String>) {
        MainCommonRequest.getShareContentNew(params, object : IDataCallBack<ShareContentModel?> {
            override fun onSuccess(model: ShareContentModel?) {
                if (model == null) {
                    return
                }
                if (!canUpdateUi()) {
                    return
                }
                mShareContentModel = model
                updateCommentCount()
                fillQrCode()
//                showSharePanel()
                showSharePanelNew()
                updateBackground(model.picUrl)
                updateTrackCover(model.picUrl)
            }

            override fun onError(code: Int, message: String) {
                CustomToast.showFailToast(message)
            }
        })
    }

    private fun updateTrackCover(url: String?) {
        val ivTrackCover = findViewById(R.id.main_iv_album_cover) as? ImageView
        ImageManager.from(context).displayImage(
            ivTrackCover,
            url,
            com.ximalaya.ting.android.host.R.drawable.host_default_album
        )
    }

    private fun updateBackground(url: String?) {
        FixedThreadPool.execute {
            try {
                ImageManager.from(mContext)
                    .downloadBitmap(url) { lastUrl: String?, bitmap: Bitmap? ->
                        ColorUtil.getDefaultAlbumCoverDomainColor(
                            bitmap, mBgColor
                        ) { color: Int ->
                            mBgColor = color
                            onColorGot()
                        }
                    }
            } catch (e: Exception) {
                e.printStackTrace()
                Logger.d("MultiCommentQRCodeShareFragment", "gotMainColor  fail" + e.message)
                onColorGot()
            }
        }
    }

    private fun onColorGot() {
        postOnUiThread {
            val ivBackground = findViewById(R.id.main_share_poster_background) as? ImageView
            ivBackground?.setImageDrawable(ColorDrawable(mBgColor))
            ivBackground?.setBackground(null)
        }
    }

    private fun updateCommentCount() {
        if (mLlList?.getChildAt(0) is LinearLayout) {
            val firstLl = mLlList?.getChildAt(0)
            var tTContent = firstLl?.findViewById<TextView>(R.id.main_tv_header)
            val str =
                if ((mShareContentModel?.commentCount)!! >= 999) "999+" else (mShareContentModel?.commentCount)!!.toInt()
            tTContent?.text = String.format("%s 精彩声音评论", str)
        }
    }

    private fun fillQrCode() {
        val url = mShareContentModel?.url ?: return
        val bitmap = QRImageUtil.createQRBitmap(url, 48.dp, 48.dp, 0, null)
        (findViewById(R.id.main_iv_qr_code) as? ImageView)?.let {
            it.setImageBitmap(bitmap)
            it.visibility = View.VISIBLE
        }
    }

    private fun statShareBtnClick(btnName: String?) {
        // 声音评论二级页面-分享面板按钮点击  点击事件
        XMTraceApi.Trace()
            .click(39515)
            .put("currPage", "trackCommentSecondLevelPage")
            .put("Item", btnName)
            .createTrace()
    }

    private fun showSharePanelNew() {
        val sharePanelView = findViewById<SharePanelView>(R.id.main_share_panel)
        sharePanelView.setOnCancelListener(object : SharePanelView.OnCancelListener {
            override fun clickCancel() {
                finish()
            }
        })
        val contentModel = ShareWrapContentModel(
            SharePanelType.PICTURE_1, "声音评论详情页", ShareUbtData(
                "声音评论详情页", mCommentModel?.id?.toString() ?: "", "声音评论"
            ),
            ICustomShareContentType.SHARE_TYPE_POSTER
        )
        sharePanelView.init(requireActivity(), contentModel,
            OnShareDstTypeSelectListener { shareType: AbstractShareType ->
                when (shareType.enName) {
                    IShareDstType.SHARE_TYPE_WX_CIRCLE -> {
                        generateSharePosterAndDoSomething(IShareDstType.SHARE_TYPE_WX_CIRCLE) { poster ->
                            ShareUtils.shareImageToWx(
                                activity,
                                mShareContentModel,
                                mShareWrapContentModel
                            )
                        }
                        statShareBtnClick(shareType.title)
                    }
                    IShareDstType.SHARE_TYPE_WX_FRIEND -> {
                        generateSharePosterAndDoSomething(IShareDstType.SHARE_TYPE_WX_FRIEND) { poster ->
                            ShareUtils.shareImageToWx(
                                activity,
                                mShareContentModel,
                                mShareWrapContentModel
                            )
                        }
                        statShareBtnClick(shareType.title)
                    }
                    IShareDstType.SHARE_TYPE_QQ -> {
                        generateSharePosterAndDoSomething(IShareDstType.SHARE_TYPE_QQ) { poster ->
                            val fileName =
                                "xmly_comments_poster" + System.currentTimeMillis() + ".jpg"
                            val dir = MainApplication.getMyApplicationContext()
                                .getExternalFilesDir(Environment.DIRECTORY_PICTURES)
                            if (dir == null) {
                                CustomToast.showFailToast("分享失败！")
                            } else {
                                val path = "$dir/喜马拉雅/$fileName"
                                ShareUtils.shareImage2QQ(
                                    activity,
                                    path,
                                    poster,
                                    mShareWrapContentModel,
                                    mShareContentModel
                                )
                            }
                        }
                        statShareBtnClick(shareType.title)
                    }
                    IShareDstType.SHARE_TYPE_SINA_WB -> {
                        generateSharePosterAndDoSomething(IShareDstType.SHARE_TYPE_SINA_WB) { poster ->
                            ShareUtils.shareBitmapToWB(
                                activity,
                                poster,
                                mShareContentModel,
                                mShareWrapContentModel
                            )
                        }
                        statShareBtnClick(shareType.title)
                    }
                    ShareConstants.SHARE_TYPE_SAVE_PICTURE -> {
                        generateSharePosterAndDoSomething(null, this::savePoster)
                    }
                }
            })
    }

//    private fun showSharePanel() {
//        val vSharePanelBg: View? = findViewById(R.id.main_v_share_panel_bg)
//        val vCancelBtn: View? = findViewById(R.id.main_tv_cancel_btn)
//        val vSaveBtn = findViewById(R.id.main_tv_save_album_btn) as? TextView
//        val vShareToWechatFriendBtn =
//            findViewById(R.id.main_tv_share_to_wechat_friend_btn) as? TextView
//        val vShareToWechatCircleBtn =
//            findViewById(R.id.main_tv_share_to_wechat_circle_btn) as? TextView
//        val vShareToWeiboBtn = findViewById(R.id.main_tv_share_to_weibo_btn) as? TextView
//        val vShareToQQBtn = findViewById(R.id.main_tv_share_to_qq_btn) as? TextView
//        val onClickListener = View.OnClickListener {
//            if (OneClickHelper.getInstance().onClick(it)) {
//                when (it) {
//                    vSharePanelBg -> {
//                        // 不做任何处理，设置点击事件是为了当点击分享面板没有按钮的区域时弹窗不消失
//                    }
//                    vSaveBtn -> {
//                        generateSharePosterAndDoSomething(null, this::savePoster)
//                    }
//                    vShareToWechatFriendBtn -> {
//                        generateSharePosterAndDoSomething(IShareDstType.SHARE_TYPE_WX_FRIEND) { poster ->
//                            ShareUtils.shareImageToWx(
//                                activity,
//                                mShareContentModel,
//                                mShareWrapContentModel
//                            )
//                        }
//                        statShareBtnClick(vShareToWechatFriendBtn?.text.toString())
//                    }
//                    vShareToWechatCircleBtn -> {
//                        generateSharePosterAndDoSomething(IShareDstType.SHARE_TYPE_WX_CIRCLE) { poster ->
//                            ShareUtils.shareImageToWx(
//                                activity,
//                                mShareContentModel,
//                                mShareWrapContentModel
//                            )
//                        }
//                        statShareBtnClick(vShareToWechatCircleBtn?.text.toString())
//                    }
//                    vShareToWeiboBtn -> {
//                        generateSharePosterAndDoSomething(IShareDstType.SHARE_TYPE_SINA_WB) { poster ->
//                            ShareUtils.shareBitmapToWB(
//                                activity,
//                                poster,
//                                mShareContentModel,
//                                mShareWrapContentModel
//                            )
//                        }
//                        statShareBtnClick(vShareToWeiboBtn?.text.toString())
//                    }
//                    vShareToQQBtn -> {
//                        generateSharePosterAndDoSomething(IShareDstType.SHARE_TYPE_QQ) { poster ->
//                            val fileName =
//                                "xmly_comments_poster" + System.currentTimeMillis() + ".jpg"
//                            val dir = MainApplication.getMyApplicationContext()
//                                .getExternalFilesDir(Environment.DIRECTORY_PICTURES)
//                            if (dir == null) {
//                                CustomToast.showFailToast("分享失败！")
//                            } else {
//                                val path = "$dir/喜马拉雅/$fileName"
//                                ShareUtils.shareImage2QQ(
//                                    activity,
//                                    path,
//                                    poster,
//                                    mShareWrapContentModel,
//                                    mShareContentModel
//                                )
//                            }
//                        }
//                        statShareBtnClick(vShareToQQBtn?.text.toString())
//                    }
//                }
//            }
//        }
//        vSharePanelBg?.setOnClickListener(onClickListener)
//        vCancelBtn?.setOnClickListener(onClickListener)
//        vSaveBtn?.setOnClickListener(onClickListener)
//        vShareToWechatFriendBtn?.setOnClickListener(onClickListener)
//        vShareToWechatCircleBtn?.setOnClickListener(onClickListener)
//        vShareToWeiboBtn?.setOnClickListener(onClickListener)
//        vShareToQQBtn?.setOnClickListener(onClickListener)
//    }

    private fun savePoster(poster: Bitmap) {
        val mainActivity = BaseApplication.getMainActivity() as? MainActivity
        if (mainActivity == null) {
            CustomToast.showFailToast("保存海报失败!")
            return
        }
        PermissionManage.checkPermission(
            mainActivity,
            mainActivity,
            object : LinkedHashMap<String?, Int?>() {
                init {
                    put(
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        com.ximalaya.ting.android.host.R.string.host_sdcard_write_permission_to_save_poster
                    )
                }
            },
            object : IPermissionListener {
                override fun havedPermissionOrUseAgree() {
                    savePosterToSDCard(poster)
                }

                override fun userReject(noRejectPermiss: Map<String, Int>) {
                    CustomToast.showFailToast("保存海报失败!")
                }
            })
    }

    /**
     * 保存分享海报到本地
     */
    private fun savePosterToSDCard(bitmap: Bitmap) {
        val fileName = "xmly_comments_poster" + System.currentTimeMillis() + ".jpg"
        LocalImageUtil.saveBitmap2SysGallery(
            bitmap,
            null,
            fileName,
            object : IDataCallBack<Boolean> {
                override fun onSuccess(result: Boolean?) {
                    if (result == true) {
                        CustomToast.showSuccessToast("保存成功")
                    } else {
                        CustomToast.showFailToast("保存失败，请重试")
                    }
                }

                override fun onError(code: Int, message: String?) {
                    CustomToast.showFailToast("保存失败，请重试")
                }
            })
    }

    private fun generateSharePosterAndDoSomething(
        shareDstName: String?,
        something: (Bitmap) -> Unit
    ) {
        val bitmap = generateSharePoster()
        if (bitmap == null) {
            CustomToast.showFailToast("生成海报失败!")
        } else {
            if (shareDstName == null) {
                something.invoke(bitmap)
            } else {
                ShareUtils.getShareContentForPoster(
                    shareDstName,
                    mCommentModel?.trackId.toString(),
                    SHARE_SRC_TYPE.toString(),
                    SHARE_SUB_TYPE.toString(),
                    object : IDataCallBack<ShareContentModel> {
                        override fun onSuccess(model: ShareContentModel?) {
                            model?.let {
                                mShareContentModel?.rowKey = model.rowKey
                                mShareContentModel?.content = model.content
                                mShareContentModel?.commandShareId = model.commandShareId
                                mShareContentModel?.thirdPartyName = shareDstName
                                mShareContentModel?.shareFrom =
                                    ICustomShareContentType.SHARE_TYPE_POSTER
                                mShareWrapContentModel =
                                    ShareWrapContentModel(
                                        ICustomShareContentType.SHARE_TYPE_POSTER,
                                        shareDstName
                                    )
                                mShareWrapContentModel?.let { wrapContentModel ->
                                    wrapContentModel.paramType = SHARE_SRC_TYPE
                                    wrapContentModel.paramSubType = SHARE_SUB_TYPE
                                    wrapContentModel.bitmap = bitmap
                                    wrapContentModel.mShowSuccessDialog = false
                                }
                            }
                            something.invoke(bitmap)
                            ShareProcessStatics.startShareProcess(
                                activity,
                                SHARE_SRC_TYPE.toString(),
                                SHARE_SUB_TYPE.toString(),
                                mCommentModel?.trackId.toString(),
                                mShareContentModel,
                                ShareWay.SYS_POSTER,
                                true
                            )
                        }

                        override fun onError(code: Int, message: String?) {
                            something.invoke(bitmap)
                        }
                    })
            }
        }
    }

    /**
     * 截屏生成分享海报
     */
    private fun generateSharePoster(): Bitmap? {
        val scrollView = mScrollView ?: return null
        val contentView = mVgPosterContent ?: return null
        val width = contentView.width
        val height = contentView.height
        val bitmap = LocalImageUtil.takeLongScreenShot(scrollView, 0, 0, width, height)
        if (bitmap != null) {
            val screenWidth = BaseUtil.getScreenWidth(mContext)
            val rectBg = RectF(0f, 0f, screenWidth.toFloat(), bitmap.height.toFloat())
            val bitmapTemp =
                Bitmap.createBitmap(screenWidth, bitmap.height, Bitmap.Config.ARGB_8888)
            if (bitmapTemp != null) {
                val c = Canvas(bitmapTemp)
                val paint = Paint()
                paint.isAntiAlias = true
                paint.color = mBgColor
                paint.style = Paint.Style.FILL
                c.drawRoundRect(rectBg, 0f, 0f, paint)
                paint.reset()
                paint.isAntiAlias = true
                paint.style = Paint.Style.FILL
                c.drawBitmap(bitmap, 0f, 0f, paint)
            }
            return bitmapTemp
        }
        return null
    }

    override fun isShowPlayButton(): Boolean {
        return false
    }

    override fun setTitleBar(titleBar: TitleBar) {
        super.setTitleBar(titleBar)
        val back = titleBar.back
        if (back is ImageView) {
            back.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_icon_back_white)
            back.drawable.mutate().setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_IN)
        }
        val title = titleBar.title
        if (title is TextView) {
            title.setTextColor(Color.WHITE)
        }
        setTitle("")
        titleBar.titleBar.background = null
        titleBar.update()
    }

    override fun getTitleBarResourceId(): Int {
        return R.id.main_title_bar
    }

    override fun hideStatusBar(): Boolean {
        return true
    }
}