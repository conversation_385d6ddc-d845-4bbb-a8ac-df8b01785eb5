package com.ximalaya.ting.android.main.manager.homepage;

import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.main.constant.MainUrlConstants;
import com.ximalaya.ting.android.main.model.family.membermark.FamilyMemberMarkModel;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by 5Greatest on 2021.05.31
 *
 * <AUTHOR>
 * On 2021/5/31
 */
public class HomePageNetManager {

    public static void requestFamilyMemberDialogData(IDataCallBack<FamilyMemberMarkModel> callBack) {
        Map<String, String> param = new HashMap<>();
        CommonRequestM.baseGetRequest(MainUrlConstants.getInstanse().getFamilyMemberUrl(), param, callBack, new CommonRequestM.IRequestCallBack<FamilyMemberMarkModel>() {
            @Override
            public FamilyMemberMarkModel success(String content) throws Exception {
                return FamilyMemberMarkModel.parse(content);
            }
        });
    }
}
