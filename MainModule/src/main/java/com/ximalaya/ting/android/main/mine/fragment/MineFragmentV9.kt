package com.ximalaya.ting.android.main.mine.fragment

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.app.Activity
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewStub
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.viewpager.widget.ViewPager
import com.astuetz.PagerSlidingTabStrip
import com.handmark.pulltorefresh.library.PullToRefreshBase
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.firework.FireworkApi
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelper
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelperManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.constants.MyListenTabEnum
import com.ximalaya.ting.android.host.constants.SwitchSettingsConstants
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.MainBottomTabProvider
import com.ximalaya.ting.android.host.imchat.unread.IMUnreadMsgManager
import com.ximalaya.ting.android.host.listener.IChatMessageMineCallback
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener
import com.ximalaya.ting.android.host.listener.IMineAd
import com.ximalaya.ting.android.host.manager.EmergencyPlanManager
import com.ximalaya.ting.android.host.manager.SharePosterManager
import com.ximalaya.ting.android.host.manager.TraceManager
import com.ximalaya.ting.android.host.manager.UIConsistencyManager
import com.ximalaya.ting.android.host.manager.account.NoReadManage
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ad.RewardAgainAdManager
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.chat.IChatFunctionAction.IUnreadMsgUpdateListener
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFragmentAction
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.ChatActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router.IBundleInstallCallback
import com.ximalaya.ting.android.host.manager.chat.ChatInternalServiceManager
import com.ximalaya.ting.android.host.manager.chat.ChatUnreadManager
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.feedback.CustomerFeedBackManager
import com.ximalaya.ting.android.host.manager.freeListen.ListenTaskMsgManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.manager.statistic.OneDayListenTimeManager
import com.ximalaya.ting.android.host.manager.statistic.UserOneDateListenDuration
import com.ximalaya.ting.android.host.mine.IMineWoTingTabFragment
import com.ximalaya.ting.android.host.model.account.HomePageModel
import com.ximalaya.ting.android.host.model.account.HomePageModelNew
import com.ximalaya.ting.android.host.model.user.HomeUnRead
import com.ximalaya.ting.android.host.model.user.NoReadModel
import com.ximalaya.ting.android.host.util.BarUtils
import com.ximalaya.ting.android.host.util.MyListenRouterUtil
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.feed.DyncAbTestUtil
import com.ximalaya.ting.android.host.util.safeAs
import com.ximalaya.ting.android.host.util.updateUiAfterAnimation
import com.ximalaya.ting.android.host.view.PullToRefreshStickyLayout
import com.ximalaya.ting.android.host.view.StickyNavLayout
import com.ximalaya.ting.android.host.xnps.XNpsManager
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.newAnchorSpaceFragment
import com.ximalaya.ting.android.main.firework.SatisfactionFireWorkInterceptor
import com.ximalaya.ting.android.main.fragment.child.callback.IGoTopListener
import com.ximalaya.ting.android.main.fragment.mylisten.IPageSucessRateTrace
import com.ximalaya.ting.android.main.fragment.mylisten.MySubscribeListFragmentNew
import com.ximalaya.ting.android.main.fragment.myspace.child.SettingFragment
import com.ximalaya.ting.android.main.manager.LevelAwardManager
import com.ximalaya.ting.android.main.manager.SettingSwitchManager
import com.ximalaya.ting.android.main.manager.firework.FireWorkMainManager
import com.ximalaya.ting.android.main.mine.adapter.MineTabAdapter
import com.ximalaya.ting.android.main.mine.component.MineTitleComponent
import com.ximalaya.ting.android.main.mine.extension.visible
import com.ximalaya.ting.android.main.mine.manager.MineAdManager
import com.ximalaya.ting.android.main.mine.manager.MineDataManager
import com.ximalaya.ting.android.main.mine.manager.MineTopViewManager
import com.ximalaya.ting.android.main.mine.manager.MineTraceManagerV9
import com.ximalaya.ting.android.main.mine.manager.OnTrace
import com.ximalaya.ting.android.main.mine.util.MineTopUtil
import com.ximalaya.ting.android.main.mine.util.eTracePurchaseTabShow
import com.ximalaya.ting.android.main.mine.util.eTraceRealNameGuideDialogShow
import com.ximalaya.ting.android.main.mine.util.eTraceRealNameGuideShow
import com.ximalaya.ting.android.main.mine.util.traceOnMineExit
import com.ximalaya.ting.android.main.mine.util.traceOnMineShow
import com.ximalaya.ting.android.main.mine.util.traceRealNameGuideClick
import com.ximalaya.ting.android.main.mine.util.traceRealNameGuideDialogClick
import com.ximalaya.ting.android.main.util.MyListenAbUtil
import com.ximalaya.ting.android.main.util.MyListenGuideUtil
import com.ximalaya.ting.android.main.util.MyListenGuideUtil.showSideBarGuide
import com.ximalaya.ting.android.main.util.other.SatisfactionSurveyUtil
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.main.view.other.EmergencyAnnouncementView
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import kotlin.math.max
import kotlin.math.min

/**
 * Created by dekai.liu on 2021/9/13.
 *
 * 9.0 账号页，包含我的和我听
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18392566603
 */
class MineFragmentV9 : AbsMineFragmentV9(false, null),
    IMainFunctionAction.IClickRefresh, IPageSucessRateTrace, IChatMessageMineCallback, IMineAd,
    MainBottomTabProvider, NoReadManage.INoReadUpdateListener {

    companion object {
        private const val TAG = "MineFragmentV9"
        private const val KEY_MESSAGE_TAB_HAS_SHOW = "key_message_tab_guide_has_show"
        private const val KEY_PURCHASED_TAB_HAS_SHOW = "key_purchased_tab_guide_has_show"
        private const val KEY_LAST_TAB_POSITION = "key_last_tab_position"

        const val IDENTIFY_USER = 111
        const val IDENTIFY_ANCHOR = 222
    }

    private val openAd by lazy {
        if (ConstantsOpenSdk.isDebug) {
            val debugInt = BaseUtil.getIntSystemProperties("debug.mine.ad")
            if (debugInt > 0) return@lazy true
            if (debugInt < 0) return@lazy false
        }
        if (MyListenAbUtil.isKeepSubscribeInFeedNoAd() && ConfigureCenter.getInstance()
                .getBool(CConstants.Group_toc.GROUP_NAME, "close_subscribe_minepage_ads", true)) {
            return@lazy false
        }
        ConfigureCenter.getInstance()
            .getBool(CConstants.Group_toc.GROUP_NAME, "minepage_ads", false)
    }
    private val useSplitTopBg by lazy {
        ConfigureCenter.getInstance()
            .getBool(CConstants.Group_android.GROUP_NAME, "key_mine_top_bg_split", true)
    }
    private val DEFAULT_AD_HEIGHT by lazy {
        (BaseUtil.getScreenWidth(context) * 9.0f / 16).toInt()
    }
    private val mRootView: View by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<View>(R.id.main_mine_root_view)
    }
    private val mIvTopBg: ImageView by lazy(LazyThreadSafetyMode.NONE) {
        findViewById(R.id.main_iv_top_bg)
    }
    private val mIvTopBgVip: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById(R.id.main_iv_bg_vip)
    }
    private val mTitleBar: View by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<ViewGroup>(R.id.main_scroll_title_bar).apply {
//            if (MineTopUtil.isVipNewStyle()) {
//                layoutParams?.height = 36.dp
//            }
            LayoutInflater.from(context).inflate(
                if (MyListenAbUtil.hasSlideBar()) {
                    R.layout.main_mine_scroll_title_bar_vip_new_slidebar
                } else if (MineTopUtil.isVipNewStyle()) {
                    R.layout.main_mine_scroll_title_bar_vip_new
                } else if (MineTopUtil.isUseVipInfoCard()) {
                    R.layout.main_mine_scroll_title_bar_x
                } else {
                    R.layout.main_mine_scroll_title_bar
                }, this
            )
        }
    }
    private val mFixTitleBar: View by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<ViewGroup>(R.id.main_title_bar).apply {
            LayoutInflater.from(context).inflate(
                if (MyListenAbUtil.hasSlideBar()) {
                    R.layout.main_mine_head_title_bar_vip_new_slidebar
                } else if (MineTopUtil.isVipNewStyle()) {
                    R.layout.main_mine_head_title_bar_vip_new
                } else if (MineTopUtil.isUseVipInfoCard()) {
                    R.layout.main_mine_head_title_bar_x
                } else {
                    R.layout.main_mine_head_title_bar
                }, this
            )
        }
    }
    private var mEmergencyAnnouncementView: EmergencyAnnouncementView? = null
    private val mStickyNavLayout: PullToRefreshStickyLayout by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<PullToRefreshStickyLayout>(R.id.main_mine_sticky_layout)
    }
    private val mTopView: ViewGroup by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<ViewGroup>(R.id.host_id_stickynavlayout_topview)
    }
    private val mTopSkeletonView: ImageView by lazy(LazyThreadSafetyMode.NONE) {
        findViewById(R.id.main_iv_skeleton)
    }
    private val mTopViewManager: MineTopViewManager by lazy(LazyThreadSafetyMode.NONE) {
        MineTopViewManager(mTopView, flSwitchRectL, this)
    }
    private val mTab: PagerSlidingTabStrip by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<PagerSlidingTabStrip>(R.id.main_mine_tab)
    }
    private val mViewPager: ViewPager by lazy(LazyThreadSafetyMode.NONE) {
        findViewById(R.id.host_id_stickynavlayout_content)
    }
    private val flSwitchRectL: FrameLayout? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById(R.id.main_fl_switch_rect)
    }
    private val flIndicatorContainer: View by lazy {
        findViewById(R.id.host_id_stickynavlayout_indicator)
    }
    private val titleComponent: MineTitleComponent by lazy {
        MineTitleComponent(
            this,
            mFixTitleBar,
            mTitleBar,
            findViewById<RoundImageView>(R.id.main_iv_animate_avatar),
            findViewById<ImageView>(R.id.main_iv_animate_avatar_kk)
        )
    }
    private var mAdapter: TabCommonAdapter? = null

    private var curIdentifyMode: Int = IDENTIFY_USER
    private var hasUnReadMsg = false

    private var mHasInit = false
    private var mChildProtect = false
    private val mIsLogin: Boolean
        get() = UserInfoMannage.hasLogined()
    private var mShouldNotSaveLastPosition = false
    private var mCurPosition = 0
    private val mFragmentHolders = ArrayList<TabCommonAdapter.FragmentHolder>()
    private val mIsDarkMode: Boolean
        get() = BaseFragmentActivity2.sIsDarkMode
    private var mIsDarkPage: Boolean = false
    private var mScrollRange: Int = 0

    private var mIsFirstVisible = true
    private var mIsPaused = false
    private var mIsScrolling = false

    private val mTopTraceHelper = TraceHelper("我的-账号")
    private val mPagerTraceHelper = TraceHelper("新版我的")

    private var fireWorkInterceptor: SatisfactionFireWorkInterceptor? = null
    private var mLoginStatusChangeListener: ILoginStatusChangeListener? = null
    private var msgRedCount = 0
    private var isFirstMessage = true
    private val headAdView: FrameLayout by lazy {
        findViewById(R.id.main_fl_head_ad)
    }
    private val bannerAdView: FrameLayout by lazy {
        findViewById<FrameLayout?>(R.id.main_fl_banner_ad)
    }
    private val realNameCardView: ViewGroup by lazy {
        findViewById(R.id.main_card_real_name)
    }
    private val llToolsBottom: ViewGroup by lazy {
        findViewById(R.id.main_ll_tools_bottom)
    }
    private var mHideAdAnimPlaying = false
    private var mShowAdAnimPlaying = false

    private val DEFAULT_AD_ANIM_DURATION = 250L
    private var mMineAdManager: MineAdManager? = null
    private var mIsNeedLoadAd = false
    private var mIsStick = false
    private var mIsChildMode = false
    private var homePageModel: HomePageModel? = null

    override fun getPageLogicName(): String = TAG

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        TraceHelperManager.setTraceHelper(TraceHelperManager.TraceHelperName.MINE, mTopTraceHelper)
        mTopTraceHelper.postPageStartNode()
        mPagerTraceHelper.postPageStartNode()
        mIsChildMode = ChildProtectManager.isChildProtectOpen(context)

        MyListenRouterUtil.getChatFragAction()
        if (!MyListenAbUtil.hasSlideBar()) {
            ChatUnreadManager.unRegisterChatSession()
        }
    }

    override fun initUi(savedInstanceState: Bundle?) {
        titleComponent.init()
        initView()
        initTabs()

        mHasInit = true

        when (mCurPosition) {
//            MyListenTabEnum.TAB_EVERYDAY_UPDATE.position -> {
//                mPagerTraceHelper.modifyTraceName("我的-追更")
//            }

            MyListenTabEnum.TAB_MY_SUBSCRIBE.position -> {
                mPagerTraceHelper.modifyTraceName("我的-订阅")
            }

            MyListenTabEnum.TAB_MY_MESSAGE.position -> {
                mPagerTraceHelper.modifyTraceName("我的-消息")
            }

            MyListenTabEnum.TAB_HISTORY.position -> {
                mPagerTraceHelper.modifyTraceName("我的-历史")
            }

            MyListenTabEnum.TAB_DOWNLOAD.position -> {
                mPagerTraceHelper.modifyTraceName("我的-下载")
            }

            MyListenTabEnum.TAB_COLLECT.position -> {
                mPagerTraceHelper.modifyTraceName("我的-收藏")
            }

            MyListenTabEnum.TAB_PURCHASED.position -> {
                mPagerTraceHelper.modifyTraceName("我的-已购")
            }
        }

        registerLoginListener()
        NoReadManage.getInstance(context).addNoReadUpdateListenerListener(this)

        ChatInternalServiceManager.registerService(IChatMessageMineCallback::class.java, this)
        // 首次加载我页初始化消息Tab数据并移除底Tab红点
        val msgCount = NoReadManage.getInstance(context).noReadModel?.unReadMessageCount ?: 0
        if (msgCount > 0) {
            postOnUiThreadDelayed({
                onUnreadMessageAdd(msgCount, true)
                if (MyListenAbUtil.getSlideExperienceFlag() != 3 && MyListenAbUtil.getSlideExperienceFlag() != 4) {
                    updateMineRedDot(0)
                }
            }, 1000)
        }
        msgRedCount = msgCount
//        adaptFoldScreen()
    }

    private val mRefreshLoadMoreListener = object : IRefreshLoadMoreListener {
        override fun onRefresh() {
            if (!canUpdateUi()) {
                return
            }
            Logger.d(TAG, "onRefresh")
            loadHomePageData()
            refreshTabFragment()
        }

        override fun onMore() {
            // 不需要此回调
        }
    }

    private val mScrollListener = object : StickyNavLayout.ScrollListener {
        override fun onScrollStop(orient: Int, scrollY: Int, totalScrollY: Int) {
            Logger.d(TAG, "StickyNavLayout onScrollStop")
            if (mIsScrolling) {
                mIsScrolling = false
                traceShowInner(TraceManager.SCROLL_SHOW)
            }
            if (scrollY > 10) {
                ViewStatusUtil.setVisible(View.INVISIBLE, flSwitchRectL)
            } else {
                ViewStatusUtil.setVisible(View.VISIBLE, flSwitchRectL)
            }
            hideHeadAd(DEFAULT_AD_HEIGHT, scrollY)
        }

        override fun onScrollToEdge(scrollY: Int, totalScrollY: Int) {
            // 暂不需要此回调
        }

        override fun onScroll(scrollY: Int, totalScrollY: Int) {
            Logger.d(
                TAG,
                "StickyNavLayout onScroll, scrollY: $scrollY, totalScrollY: $totalScrollY ${mStickyNavLayout.stickyNavLayout.translationY}"
            )
            if (scrollY >= DEFAULT_AD_HEIGHT) {
                hideHeadAd(DEFAULT_AD_HEIGHT, scrollY)
            }
            if (mTopSkeletonView.visibility != View.VISIBLE) {
                titleComponent.animateAvatar(scrollY)
            }
            titleComponent.updateTitleBarBackgroundAlpha(scrollY)
            mIsScrolling = true
        }

        override fun onStateChange(isStick: Boolean) {
            // 暂不需要此回调
            mIsStick = isStick
            // 非吸顶状态时重新请求广告
            if (openAd && mIsNeedLoadAd && !mIsStick) {
                mIsNeedLoadAd = false
                mMineAdManager?.loadAd(headAdView, bannerAdView)
            }
        }
    }
    private val mOnLayoutChangeListener = View.OnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
        mScrollRange = mTopViewManager.getScrollRange()
    }

    private fun initView() {
        val statusBarHeight = if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            BaseUtil.getStatusBarHeight(mContext)
        } else {
            0
        }
        mStickyNavLayout.stickyNavLayout.setDealMoveEvent(true)
        mStickyNavLayout.setHeadLoadingViewTopMargin(statusBarHeight + 20.dp)
        mStickyNavLayout.setOnScrollChangedListener { _, t, _, _ ->
//            mIvTopBg.translationY = -t.toFloat()
            Logger.d(TAG, "mStickyNavLayout scrollY: $t")
        }
        mStickyNavLayout.setOnRefreshLoadMoreListener(mRefreshLoadMoreListener)
        mStickyNavLayout.mode = PullToRefreshBase.Mode.PULL_FROM_START
        mStickyNavLayout.refreshableView?.let {
            it.setPadding(0, titleComponent.getStickTopPadding(), 0, 0)
            it.setScrollListener(mScrollListener)
            it.setScrollViewNeedCache(false)
        }
        mTopView.addOnLayoutChangeListener(mOnLayoutChangeListener)
        mTopViewManager.initView()

        val vCreationLayout = view?.findViewById<View>(R.id.main_mine_creation_layout)
        val userInfoLayout = findViewById<View?>(R.id.main_mine_user_info_layout)

        if (MineTopUtil.isUseVipInfoCard()) {
            if (MineTopUtil.getCardStyle() == MineTopUtil.CARD_STYLE_VIP_NEW) {
                vCreationLayout?.visibility = View.INVISIBLE
            }
//            userInfoLayout?.post {
//                mIvTopBg.layoutParams = mIvTopBg.layoutParams.apply {
//                    height =
//                        (userInfoLayout.measuredHeight + userInfoLayout.top + BarUtils.getStatusBarHeight()) + 50f.dp
//                }
//            }
        } else {
            vCreationLayout?.post {
                mIvTopBg.layoutParams = mIvTopBg.layoutParams.apply {
                    height =
                        (vCreationLayout.measuredHeight + vCreationLayout.top + BarUtils.getStatusBarHeight()) + 50f.dp
                }
            }
            vCreationLayout?.visibility = View.VISIBLE
        }
    }

//    /**
//     * 展示消息Tab引导
//     * */
//    private fun checkShowMessageTabGuide() {
//        if (!isRealVisable || !canUpdateUi() || !MineDataManager.canShowMineGuide(
//                KEY_MESSAGE_TAB_HAS_SHOW
//            ) || !hasAddMessageTab()
//        ) {
//            return
//        }
//        if (ChildProtectManager.isChildMode(BaseApplication.getMyApplicationContext())) {
//            return
//        }
//        val activity = BaseApplication.getTopActivity()
//        if (activity == null || activity !is MainActivity) {
//            return
//        }
//        if (ViewUtil.haveDialogIsShowing(activity)) {
//            return
//        }
//        val hasShow = MMKVUtil.getInstance()
//            .getBoolean(KEY_MESSAGE_TAB_HAS_SHOW, false)
//        if (hasShow) {
//            return
//        }
//        val messageTab =
//            (mTab.getChildAt(0) as? LinearLayout)?.getChildAt(MyListenTabEnum.TAB_MY_MESSAGE.position)
//                ?: return
//        if (!com.ximalaya.ting.android.host.util.view.ViewStatusUtil.viewIsRealShowing(messageTab)) {
//            return
//        }
//
//        val sv = ShadowView(activity)
//        val popView = View.inflate(activity, R.layout.main_popup_mine_message_tab_guide, null)
//        val popWindow = CustomPopWindow.PopupWindowBuilder(activity)
//            .enableOutsideTouchableDissmiss(true)
//            .setView(popView)
//            .setOnDissmissListener {
//                ViewUtil.setHasDialogShow(
//                    false
//                )
//                (activity.window?.decorView as? ViewGroup)?.removeView(sv)
//            }
//            .create()
//        sv.setMode(ShadowView.MODE_FLAT)
//        val location = IntArray(2)
//        messageTab.getLocationOnScreen(location)
//        val focus: ShadowView.Focus = sv.Focus(
//            ShadowView.Focus.SHAPE_ROUND_RECT,
//            location[0] + messageTab.measuredWidth / 2f,
//            location[1] + messageTab.measuredHeight / 2f,
//            messageTab.measuredWidth,
//            messageTab.measuredHeight
//        )
//        focus.radius = 12f.dp
//        sv.addFocus(focus)
//        val view = activity.window?.decorView ?: return
//        (view as ViewGroup).addView(sv)
//        popWindow.showAsDropDown(
//            messageTab,
//            messageTab.measuredWidth / 2 + location[0] - 6f.dp,
//            6f.dp,
//            Gravity.START
//        )
//        ViewUtil.setHasDialogShow(true)
//        MMKVUtil.getInstance().saveBoolean(KEY_MESSAGE_TAB_HAS_SHOW, true)
//    }

//    private fun checkShowPurchasedTabGuide() {
//        if (!isRealVisable || !canUpdateUi() || !MineDataManager.canShowMineGuide(
//                KEY_PURCHASED_TAB_HAS_SHOW
//            )
//        ) {
//            return
//        }
//        if (ChildProtectManager.isChildMode(BaseApplication.getMyApplicationContext())) {
//            return
//        }
//        val activity = BaseApplication.getTopActivity()
//        if (activity == null || activity !is MainActivity) {
//            return
//        }
//        if (ViewUtil.haveDialogIsShowing(activity)) {
//            return
//        }
//        val hasShow = MMKVUtil.getInstance()
//            .getBoolean(KEY_PURCHASED_TAB_HAS_SHOW, false)
//        if (hasShow) {
//            return
//        }
//        val purchaseTab =
//            (mTab.getChildAt(0) as? LinearLayout)?.getChildAt(MyListenTabEnum.TAB_PURCHASED.position)
//                ?: return
//        if (!com.ximalaya.ting.android.host.util.view.ViewStatusUtil.viewIsRealShowing(purchaseTab)) {
//            return
//        }
//
//        val sv = ShadowView(activity)
//        val popView = View.inflate(activity, R.layout.main_popup_mine_purchase_tab_guide, null)
//        val popWindow = CustomPopWindow.PopupWindowBuilder(activity)
//            .enableOutsideTouchableDissmiss(true)
//            .setView(popView)
//            .setOnDissmissListener {
//                ViewUtil.setHasDialogShow(
//                    false
//                )
//                (activity.window?.decorView as? ViewGroup)?.removeView(sv)
//            }
//            .create()
//        sv.setMode(ShadowView.MODE_FLAT)
//        val location = IntArray(2)
//        purchaseTab.getLocationOnScreen(location)
//        val focus: ShadowView.Focus = sv.Focus(
//            ShadowView.Focus.SHAPE_ROUND_RECT,
//            location[0] + purchaseTab.measuredWidth / 2f,
//            location[1] + purchaseTab.measuredHeight / 2f,
//            purchaseTab.measuredWidth,
//            purchaseTab.measuredHeight
//        )
//        focus.radius = 12f.dp
//        sv.addFocus(focus)
//        val view = activity.window?.decorView ?: return
//        (view as ViewGroup).addView(sv)
//        val xOff = -popView.measuredWidth - purchaseTab.measuredWidth / 2 - 4f.dp
//        popWindow.showAsDropDown(
//            purchaseTab,
//            xOff,
//            6f.dp,
//            Gravity.END
//        )
//        ViewUtil.setHasDialogShow(true)
//        MMKVUtil.getInstance().saveBoolean(KEY_PURCHASED_TAB_HAS_SHOW, true)
//    }

    private fun showTabRed() {
        if (DyncAbTestUtil.isFeed2Subscribe()) {
            return
        }
        if (!UserInfoMannage.hasLogined()) {
            mTab.hideRedDot(MyListenTabEnum.TAB_MY_SUBSCRIBE.position)
            return
        }
//        val subscribeCount = NoReadManage.getInstance(mContext).homeUnRead?.subscribeHasNew ?: false
        val noReadCount = NoReadManage.getInstance(context).homeUnRead?.unreadSubscribeCountV2 ?: 0
        if (mCurPosition != MyListenTabEnum.TAB_MY_SUBSCRIBE.position && (noReadCount > 0 || NoReadManage.getInstance(mContext).albumAndTingListSubscribeRedPoint)) {
            if (noReadCount > 0) {
                mTab.showRedDot(
                    MyListenTabEnum.TAB_MY_SUBSCRIBE.position,
                    noReadCount
                )
            } else if (NoReadManage.getInstance(mContext).albumAndTingListSubscribeRedPoint) {
                mTab.showRedDot(
                    MyListenTabEnum.TAB_MY_SUBSCRIBE.position
                )
            }
        }
//        CommonRequestM.getInstanse().getUpdateUnReadMsg(object : IDataCallBack<NoReadModel?> {
//            override fun onSuccess(tNoReadModel: NoReadModel?) {
//                if (tNoReadModel == null || !canUpdateUi()) {
//                    return
//                }
//                if (mCurPosition != MyListenTabEnum.TAB_EVERYDAY_UPDATE.position && tNoReadModel.unreadTrackFeedCount > 0) {
//                    mTab.showRedDot(MyListenTabEnum.TAB_EVERYDAY_UPDATE.position)
//                }
//                if (mCurPosition != MyListenTabEnum.TAB_MY_SUBSCRIBE.position && tNoReadModel.unreadSubscribeCount > 0) {
//                    mTab.showRedDot(
//                        MyListenTabEnum.TAB_MY_SUBSCRIBE.position,
//                        tNoReadModel.unreadSubscribeCount
//                    )
//                }
//            }
//
//            override fun onError(code: Int, message: String) {}
//        })
    }

    private fun showTopSkeletonViewState(visible: Boolean) {
        if (visible) {
            mStickyNavLayout.refreshableView?.let {
                it.setPadding(0, 0, 0, 0)
            }
            flIndicatorContainer.setPadding(0, titleComponent.getStickTopPadding(), 0, 0)
            mTopViewManager.setTopCardVisible(View.INVISIBLE)
            titleComponent.showTopSkeletonViewState()
            ViewStatusUtil.setVisible(View.VISIBLE, mTopSkeletonView)
//            mStickyNavLayout.stickyNavLayout.isCanScrollVertical = false
            mStickyNavLayout.mode = PullToRefreshBase.Mode.DISABLED
            titleComponent.updateTitleBarBackgroundAlpha(mStickyNavLayout.refreshableView.scrollY)
        } else {
            mStickyNavLayout.refreshableView?.let {
                it.setPadding(0, titleComponent.getStickTopPadding(), 0, 0)
            }
            flIndicatorContainer.setPadding(0, 0, 0, 0)
            mTopViewManager.setTopCardVisible(View.VISIBLE)
            ViewStatusUtil.setVisible(View.GONE, mTopSkeletonView)
//            mStickyNavLayout.stickyNavLayout.isCanScrollVertical = true
            mStickyNavLayout.mode = PullToRefreshBase.Mode.PULL_FROM_START
        }
    }

    private fun initTabs() {
        // mTab.setDefaultIndicatorStyle()
        if (MineTopUtil.isVipNewStyle()) {
            (mTab.layoutParams as? MarginLayoutParams)?.apply {
                topMargin = 6.dp
            }
        }
        UIConsistencyManager.getInstance().setTabUIConsistency(context, mTab)
        MineTraceManagerV9.addTrace(mTab) {
            if (!hasAddPurchase()) {
                return@addTrace
            }
            val purChaseTabView =
                (mTab.getChildAt(0) as? ViewGroup)?.getChildAt(MyListenTabEnum.TAB_PURCHASED.position)
            if (com.ximalaya.ting.android.host.util.view.ViewStatusUtil.viewIsRealShowing(
                    purChaseTabView
                )
            ) {
                eTracePurchaseTabShow()
            }
        }

        MyListenRouterUtil.getMyListenBundle { updateUiAfterAnimation(::initViewPager) }
    }

    private fun hasAddPurchase(): Boolean {
        for (tab in mFragmentHolders) {
            if (tab.title == MyListenTabEnum.TAB_PURCHASED.tabName) {
                return true
            }
        }
        return false
    }

    fun hasAddMessageTab(): Boolean {
        if (mFragmentHolders.isEmpty()) {
            return false
        }
        for (tab in mFragmentHolders) {
            if (tab.title == MyListenTabEnum.TAB_MY_MESSAGE.tabName) {
                return true
            }
        }
        return false
    }

    private fun initViewPager() {
        mFragmentHolders.clear()
//        val msgHolder: TabCommonAdapter.FragmentHolder? = getMessageFh()
        val fragAct = MyListenRouterUtil.getFragAction()
//        if (!ChildProtectManager.isChildProtectOpen(mContext)) {
//            if (DyncAbTestUtil.canShowMessageTab()) {
//                if (msgHolder != null) {
//                    mFragmentHolders.add(msgHolder)
//                } else {
//                    preLoadChatBundleAndAdd()
//                }
//            }
//        }
        if (fragAct != null) {
            if (!ChildProtectManager.isChildProtectOpen(mContext) && MyListenAbUtil.getSlideExperienceFlag() != 3 && MyListenAbUtil.getSlideExperienceFlag() != 4) {
                mFragmentHolders.add(getMessageFh())
            }
            mFragmentHolders.add(getHistoryFh(fragAct))
            if (DyncAbTestUtil.isFeed2Subscribe().not()) {
                // 实验二
                mFragmentHolders.add(getSubscribeFh())
            }
            if (!MyListenAbUtil.isInABExperimentGroup()) {
                mFragmentHolders.add(getCollectFh(fragAct))
            }
            mFragmentHolders.add(getDownloadFh(fragAct))
            if (!ChildProtectManager.isChildProtectOpen(context)) {
                getPurchasedFh()?.let {
                    mFragmentHolders.add(it)
                }
            }
        }
        mAdapter = MineTabAdapter(
            childFragmentManager,
            mFragmentHolders,
            FragmentStatePagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
        )
        mViewPager.adapter = mAdapter
        mViewPager.offscreenPageLimit = mFragmentHolders.size
        mTab.setViewPager(mViewPager)
        mTab.setOnTabClickListener {
            if (hasAddPurchase() && it == MyListenTabEnum.TAB_PURCHASED.position) {
                // 我的-已购  点击事件
                XMTraceApi.Trace()
                    .click(55498) // 用户点击时上报
                    .put("currPage", "mySpace9.0")
                    .put(
                        XmRequestIdManager.XM_REQUEST_ID,
                        XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_MINE)
                    )
                    .createTrace()
            }
        }
        mCurPosition = getDefaultPage()
        if (MyListenAbUtil.isKeepSubscribeInMine() && UserInfoMannage.hasLogined().not()) {
            mCurPosition =  MyListenTabEnum.TAB_HISTORY.position
            mShouldNotSaveLastPosition = true
        }
        mViewPager.currentItem = mCurPosition
        mViewPager.addOnPageChangeListener(mPageChangeListener)
        HandlerManager.postOnUIThreadDelay({
            if (MineTopUtil.isVipNewStyle()) {
                mTab.setTabTextSizeScaleRate(1f)
                if (BaseFragmentActivity.sIsDarkMode) {
                    mTab.setActivateTextColor(Color.parseColor("#e6FFFFFF"))
                    mTab.setDeactivateTextColor(Color.parseColor("#99FFFFFF"))
                } else {
                    mTab.setActivateTextColor(Color.parseColor("#2C2C3C"))
                    mTab.setDeactivateTextColor(Color.parseColor("#992C2C3C"))
                }
            } else {
                mTab.textColor = getColorSafe(R.color.main_color_111111_dcdcdc)
                mTab.setDeactivateTextColor(getColorSafe(R.color.main_color_333333_dcdcdc))
            }
        }, 50)

        titleComponent.updateMessageView()
        showTabRed()
//        if (DyncAbTestUtil.canShowMessageTab()) {
//            checkShowMessageTabGuide()
//        }
    }

//    private fun getEveryDayUpdateFh(fragAction: IMyListenFragmentAction): TabCommonAdapter.FragmentHolder {
//        val args = Bundle()
//        args.putBoolean(BundleKeyConstants.KEY_IS_SHOW_TITLE, false)
//        args.putBoolean(BundleKeyConstants.KEY_IS_RESIZE_LOADINGVIEW, false)
//        args.putBoolean(
//            BundleKeyConstants.KEY_AUTO_PLAY,
//            arguments?.getBoolean(BundleKeyConstants.KEY_AUTO_PLAY, false) ?: false
//        )
//        return TabCommonAdapter.FragmentHolder(
//            fragAction.everyDayUpdateFragmentClazzNew.safeAs(),
//            MyListenTabEnum.TAB_EVERYDAY_UPDATE.tabName,
//            args, MyListenTabEnum.TAB_EVERYDAY_UPDATE.position.toString()
//        )
//    }

    private fun getSubscribeFh(): TabCommonAdapter.FragmentHolder {
        val args = Bundle()
        args.putBoolean(BundleKeyConstants.KEY_IS_SHOW_TITLE, false)
        args.putBoolean(BundleKeyConstants.KEY_IS_RESIZE_LOADINGVIEW, false)
        if (true == arguments?.containsKey(BundleKeyConstants.KEY_SECOND_DESTINATION)) {
            try {
                args.putString(
                    BundleKeyConstants.KEY_SECOND_DESTINATION,
                    arguments?.getString(BundleKeyConstants.KEY_SECOND_DESTINATION, "")
                )
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return TabCommonAdapter.FragmentHolder(
            if (MyListenAbUtil.isInABExperimentGroup()) {
                MyListenRouterUtil.getFragAction().mySubscribeFragmentClass
            } else {
                MySubscribeListFragmentNew::class.java
            }, MyListenTabEnum.TAB_MY_SUBSCRIBE.tabName,
            args,
            MyListenTabEnum.TAB_MY_SUBSCRIBE.position.toString()
        )
    }

    private fun getCollectFh(fragAction: IMyListenFragmentAction): TabCommonAdapter.FragmentHolder {
        val args = Bundle()
        return TabCommonAdapter.FragmentHolder(
            fragAction.collectFragmentClazz.safeAs(),
            MyListenTabEnum.TAB_COLLECT.tabName,
            args,
            MyListenTabEnum.TAB_COLLECT.position.toString()
        )
    }

    private fun getMessageFh(): TabCommonAdapter.FragmentHolder {
        val fragAction = MyListenRouterUtil.getFragAction()
        val args = Bundle()
        return TabCommonAdapter.FragmentHolder(
            fragAction.newsCenterContainerFragmentClass,
            MyListenTabEnum.TAB_MY_MESSAGE.tabName,
            args,
            MyListenTabEnum.TAB_MY_MESSAGE.position.toString()
        )
    }

    private fun getPurchasedFh(): TabCommonAdapter.FragmentHolder? {
        val args = Bundle()
        val fragAction: IMyListenFragmentAction = MyListenRouterUtil.getFragAction() ?: return null
        return TabCommonAdapter.FragmentHolder(
            fragAction.purchaseFragmentClazz.safeAs(),
            MyListenTabEnum.TAB_PURCHASED.tabName,
            args,
            MyListenTabEnum.TAB_PURCHASED.position.toString()
        )
    }

    private fun getHistoryFh(fragAction: IMyListenFragmentAction): TabCommonAdapter.FragmentHolder {
        val needShowRecently = MyListenAbUtil.isInABExperimentGroup()
        if (needShowRecently) {
            val args = Bundle()
            args.putBoolean(BundleKeyConstants.KEY_IS_PAGE_IN_TAB, true)
            val clazz = fragAction.recentlyFragmentClazz
            return TabCommonAdapter.FragmentHolder(
                clazz.safeAs(),
                MyListenTabEnum.TAB_HISTORY.tabName,
                args,
                MyListenTabEnum.TAB_HISTORY.position.toString()
            )
        } else {
            val args = Bundle()
            args.putBoolean(BundleKeyConstants.KEY_IS_PAGE_IN_TAB, true)
            val clazz = fragAction.historyFragmentClazzNew
            return TabCommonAdapter.FragmentHolder(
                clazz.safeAs(),
                MyListenTabEnum.TAB_HISTORY.tabName,
                args,
                MyListenTabEnum.TAB_HISTORY.position.toString()
            )
        }
    }

    private fun getDownloadFh(fragAction: IMyListenFragmentAction): TabCommonAdapter.FragmentHolder {
        val args = Bundle()
        args.putBoolean(BundleKeyConstants.KEY_IS_PAGE_IN_TAB, true)
        args.putBoolean(BundleKeyConstants.KEY_IS_SHOW_TITLE, false)
        val clazz = fragAction.downloadFragmetClazzNew
        return TabCommonAdapter.FragmentHolder(
            clazz.safeAs(),
            MyListenTabEnum.TAB_DOWNLOAD.tabName,
            args,
            MyListenTabEnum.TAB_DOWNLOAD.position.toString()
        )
    }

    private val mPageChangeListener: ViewPager.OnPageChangeListener =
        object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                // 暂不需要此回调
            }

            override fun onPageScrollStateChanged(state: Int) {
                // 暂不需要此回调
            }

            override fun onPageSelected(position: Int) {
                mPagerTraceHelper.abandon()
                mCurPosition = position
                if (!mShouldNotSaveLastPosition &&
                    (mCurPosition == MyListenTabEnum.TAB_HISTORY.position || mCurPosition == MyListenTabEnum.TAB_MY_SUBSCRIBE.position)) {
                    MMKVUtil.getInstance().saveInt(KEY_LAST_TAB_POSITION, mCurPosition)
                }
                mShouldNotSaveLastPosition = false
                if (mCurPosition == MyListenTabEnum.TAB_MY_SUBSCRIBE.position) {
                    mTab.hideRedDot(
                        MyListenTabEnum.TAB_MY_SUBSCRIBE.position
                    )
                }
                updateTitleBar()
                // 我的-tab切换  点击事件
                XMTraceApi.Trace()
                    .click(37261)
                    .put("tabName", mFragmentHolders.getOrNull(position)?.title)
                    .put("currPage", "mySpace9.0")
                    .createTrace()
                if (hasAddMessageTab() && mCurPosition == MyListenTabEnum.TAB_MY_MESSAGE.position) {
                    mTab.hideRedDot(mCurPosition)
                    hasUnReadMsg = false
                    // 我的-消息tab  点击事件
                    XMTraceApi.Trace()
                        .click(55496) // 用户点击时上报
                        .put("currPage", "mySpace9.0")
                        .createTrace()
                }
                postOnUiThread { traceViewPagerItemShow(TraceManager.BACK_VISIBLE_SHOW) }
            }
        }
    private var mFirstItingGotIn = true

    private fun dealWithIting() {
        HandlerManager.obtainMainHandler()
            .postDelayed(Runnable { // onMyResume会先于Iting解析触发 所以这里要延时否则拿不到信息
                arguments?.let {
                    val pageIndex = it.getInt(BundleKeyConstants.KEY_DESTINATION, -1)
                    if (pageIndex < 0) {
                        return@Runnable
                    }
                    it.putInt(BundleKeyConstants.KEY_DESTINATION, -1)
                    Logger.i(TAG, "dealWithIting: pageIndex: $pageIndex")
                    if (mAdapter == null) {
                        return@Runnable
                    }
                    mViewPager.currentItem = pageIndex
                    doAfterAnimation {
                        if (canUpdateUi()) {
                            mStickyNavLayout.stickyNavLayout.smoothScrollToNav()
                        }
                        if (mFirstItingGotIn) {
                            mFirstItingGotIn = false
                            HandlerManager.obtainMainHandler().postDelayed({
                                if (canUpdateUi()) {
                                    mStickyNavLayout.stickyNavLayout.smoothScrollToNav()
                                }
                            }, 2000)
                        }
                    }
                }
            }, 200)
    }

    override fun loadData() {}

    override fun getContainerLayoutId(): Int = R.layout.main_fra_mine_v9

    private fun clickAvatar() {
        if (!mIsLogin) {
            UserInfoMannage.gotoLogin(mContext)
            return
        }
        if (ChildProtectManager.isChildProtectOpen(mContext)) {
            ChildProtectManager.showFeatureCannotUseToast()
            return
        }

        startFragment(newAnchorSpaceFragment(UserInfoMannage.getUid()))
    }

    // 回到初始状态
    private fun reset() {
        mStickyNavLayout.refreshableView?.smoothScrollToTop()
        titleComponent.animateAvatar(0)
        scrollToTop(false, false)
    }

//    override fun onConfigurationChanged(newConfig: Configuration) {
//        super.onConfigurationChanged(newConfig)
//        adaptFoldScreen()
//    }
//
//    private fun adaptFoldScreen() {
//        if (BaseUtil.isFoldScreen(context)) {
//            val lp = mIvTopBg.layoutParams
//            if (BaseUtil.isCollapsibleScreenOnPortraitExpandMode(context)) {
//                lp.height = (BaseUtil.getScreenWidth(context) * 140f / 375f).toInt()
//            } else {
//                lp.height = (BaseUtil.getScreenWidth(context) * 280f / 375f).toInt()
//            }
//            mIvTopBg.layoutParams = lp
//        }
//    }

    private fun clickMessage() {
        if (mChildProtect) {
            ChildProtectManager.showFeatureCannotUseToast()
            return
        }

        if (!mIsLogin) {
            UserInfoMannage.gotoLogin(mContext)
            return
        }

        // 触发chat模块下载安装
        Router.getActionByCallback(Configure.BUNDLE_CHAT, object : IBundleInstallCallback {
            override fun onInstallSuccess(bundleModel: BundleModel) {
                if (Configure.chatBundleModel.bundleName == bundleModel.bundleName) {
                    try {
                        Router.getActionRouter<ChatActionRouter>(Configure.BUNDLE_CHAT)?.fragmentAction
                            ?.newFragmentByFid(Configure.ChatFragmentFid.NEWS_CENTER_FRAGMENT)
                            ?.let {
                                startFragment(it)
                            }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }

            override fun onLocalInstallError(t: Throwable, bundleModel: BundleModel) {
                Logger.d(TAG, "Install Chat Failed! onLocalInstallError" + t.message)
            }

            override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {
                Logger.d(TAG, "Install Chat Failed! onRemoteInstallError " + t.message)
            }
        }, true, BundleModel.DOWNLOAD_ASK_USER)
    }

    private fun clickKefu() {
        val transformTsUrl =
            "https://mobile.ximalaya.com/abtest-jump/jump/urlRedirect/contact_customer_service?_fix_keyboard=1"
        val xmFeedBackFragment = CustomerFeedBackManager.createXmFeedBackFragment(transformTsUrl)
        startFragment(xmFeedBackFragment)
    }

    private fun clickSetting() {
        startFragment(SettingFragment())
    }

    override fun onMyResume() {
        super.onMyResume()
        // 解决onHiddenChanged，在initUI之前触发的问题，解决view没有被初始化此处就被调用了造成空指针异常
        if (!mHasInit) {
            return
        }
        if (!canUpdateUi()) {
            return
        }
        MyListenGuideUtil.isMinePageShow = true
        showSideBarGuide()
        if (mIsChildMode != ChildProtectManager.isChildProtectOpen(context)) {
            mIsChildMode = ChildProtectManager.isChildProtectOpen(context)
            initViewPager()
        }

        if (mMineAdManager == null && openAd) {
            mMineAdManager = MineAdManager(this, openAd)
        }
        showEmergencyAnnouncementIfNeeded()
        checkChildProtect()
        titleComponent?.myResume()
        titleComponent?.updateMessageView()
        mIsNeedLoadAd = true
        if (openAd && !mIsStick) {
            mIsNeedLoadAd = false
            mMineAdManager?.loadAd(headAdView, bannerAdView)
        }
        loadHomePageData()

        dealWithIting()

        updateTitleBar()

        // 开启推送功能的弹屏
        FireWorkMainManager.getInstance()
            .setFireWork(this, FireWorkMainManager.TYPE_OPEN_PUSH_SERVICE, null)
        LevelAwardManager.uploadListenDuration(this)
        traceOnMineShow(this)

        UserOneDateListenDuration.forceUpdateServerTime(mContext, null)
        OneDayListenTimeManager.updateServiceTimeAndListenTime(true)
        if (fireWorkInterceptor == null) {
            fireWorkInterceptor = object : SatisfactionFireWorkInterceptor() {
                override fun show() {
                    SatisfactionSurveyUtil.showOrHideSatisfactionSurvey(
                        findViewById(R.id.main_iv_satisfaction_survey),
                        firework,
                        this,
                        "",
                        "mySpace9.0"
                    )
                }
            }
        }
        FireworkApi.getInstance().addFireworkShowingInterceptor(fireWorkInterceptor)
        mTopViewManager.onResume()
        if (hasAddMessageTab()) {
            postOnUiThreadDelayed({
//                checkShowMessageTabGuide()
                // 我的-消息tab  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(55497)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "mySpace9.0")
                    .put("isRedPoint", if (hasUnReadMsg) "true" else "false") // ture｜false
                    .put(XmRequestIdManager.IS_DUPLICATE_VIEW, "2")
                    .createTrace()
            }, 2000)
        }
//        if (DyncAbTestUtil.isFeed2Subscribe()) {
//            postOnUiThreadDelayed({
//                checkShowPurchasedTabGuide()
//            }, 2000)
//        }
        mMineAdManager?.onResume()
        showTabRed()

        postOnUiThreadDelayedAndRemovedOnPause(1000) {
            if (canUpdateUi() && isRealVisable) {
                XNpsManager.showXNpsDialog(this, "myPage", "mySpace9.0")
            }
        }
        RewardAgainAdManager.onPageResume(RewardAgainAdManager.PAGE_SOURCE_MINE)
//        (BaseApplication.getMainActivity() as? MainActivity)?.checkShowExp1GuideDialog()
    }

    private fun registerLoginListener() {
        if (mLoginStatusChangeListener == null) {
            mLoginStatusChangeListener = object : ILoginStatusChangeListener {
                override fun onLogout(olderUser: LoginInfoModelNew?) {
                    if (hasAddMessageTab()) {
                        onUnreadMessageAdd(0, true)
                    }
//                    removePurchaseTab()
                }

                override fun onLogin(model: LoginInfoModelNew?) {
                    if (canUpdateUi()) {
                        mIsNeedLoadAd = true
                        reset()
                    }
                }

                override fun onUserChange(
                    oldModel: LoginInfoModelNew?,
                    newModel: LoginInfoModelNew?
                ) {
                }
            }
        }
        UserInfoMannage.getInstance().addLoginStatusChangeListener(mLoginStatusChangeListener)
    }

    private fun unregisterLoginListener() {
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(mLoginStatusChangeListener)
    }

    private fun showEmergencyAnnouncementIfNeeded() {
        val emergencyAnnouncement = EmergencyPlanManager.getInstance()
            .getEmergencyAnnouncement(EmergencyPlanManager.POSITION_ACCOUNT_PAGE)
        if (emergencyAnnouncement != null && !EmergencyPlanManager.getInstance()
                .hasAnnouncementClosed(
                    EmergencyPlanManager.POSITION_ACCOUNT_PAGE,
                    emergencyAnnouncement
                )
        ) {
            if (mEmergencyAnnouncementView == null) {
                val viewStub = findViewById<ViewStub>(R.id.main_vs_emergency_announcement)
                mEmergencyAnnouncementView =
                    EmergencyAnnouncementView.newInstance(viewStub)?.apply {
                        setEventListener { announcement ->
                            EmergencyPlanManager.getInstance().markAnnouncementClosed(
                                EmergencyPlanManager.POSITION_ACCOUNT_PAGE, announcement
                            )
                        }
                    }
            }
            mEmergencyAnnouncementView?.show(emergencyAnnouncement)
        } else {
            mEmergencyAnnouncementView?.hide()
        }
    }

    private fun checkChildProtect() {
        val childProtect = ChildProtectManager.isChildProtectOpen(mContext)
        if (childProtect != mChildProtect) {
            mChildProtect = childProtect
            mTopViewManager.notifyChildProtectChange()
            titleComponent.notifyChildProtectChange()
        }
    }

    private val mUnreadMsgUpdateListener = IUnreadMsgUpdateListener { model ->
        if (model == null || mChildProtect || !mIsLogin) {
            return@IUnreadMsgUpdateListener
        }
        if (hasAddMessageTab()) {
            return@IUnreadMsgUpdateListener
        }
        val hasUnreadMsg = model.totalUnreadCount() - model.mGroupQuietUnreadCount > 0
        titleComponent?.setRdMessageVisible(if (hasUnreadMsg) View.VISIBLE else View.GONE)
    }

//    private fun preLoadChatBundleAndAdd() {
//        XDCSCollectUtil.statErrorToXDCS(TAG, "do install chat")
//        // 触发chat模块下载安装
//        Router.getActionByCallback(Configure.BUNDLE_CHAT, object : IBundleInstallCallback {
//            override fun onInstallSuccess(bundleModel: BundleModel) {
//                if (Configure.chatBundleModel.bundleName == bundleModel.bundleName) {
//                    try {
//                        val functionAction =
//                            Router.getActionRouter<ChatActionRouter>(Configure.BUNDLE_CHAT)?.functionAction
//                        if (functionAction != null) {
//                            CustomToast.showDebugFailToast("Install Chat Done!!")
//                        }
//                        if (DyncAbTestUtil.canShowMessageTab() && !hasAddMessageTab()) {
//                            getMessageFh()?.let {
//                                mFragmentHolders.add(0, it)
//                                mAdapter = MineTabAdapter(
//                                    childFragmentManager, mFragmentHolders,
//                                    FragmentStatePagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
//                                )
//                                mViewPager.adapter = mAdapter
//                                mViewPager.currentItem = ++mCurPosition
//                                mTab.notifyDataSetChanged()
//                                titleComponent.updateMessageView()
//                                XDCSCollectUtil.statErrorToXDCS(TAG, "install chat success")
//                                Logger.logToFile("$TAG Install Chat Success! ${mFragmentHolders.size} ${mAdapter?.count}")
//                            }
//                        }
//                    } catch (e: Exception) {
//                        e.printStackTrace()
//                    }
//                }
//            }
//
//            override fun onLocalInstallError(t: Throwable, bundleModel: BundleModel) {
//                Logger.d(TAG, "Install Chat Failed! onLocalInstallError" + t.message)
//                XDCSCollectUtil.statErrorToXDCS(
//                    TAG,
//                    "Install Chat Failed! onLocalInstallError" + t.message
//                )
//            }
//
//            override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {
//                XDCSCollectUtil.statErrorToXDCS(
//                    TAG,
//                    "Install Chat Failed! onRemoteInstallError " + t.message
//                )
//                Logger.d(TAG, "Install Chat Failed! onRemoteInstallError " + t.message)
//            }
//        }, true, BundleModel.DOWNLOAD_IN_BACKGROUND)
//    }

    private fun addArgumentsToFragment(arguments: Bundle?, fragment: Fragment?) {
        try {
            if (fragment != null && arguments != null) {
                if (fragment.arguments != null) {
                    fragment.arguments?.putAll(arguments)
                } else {
                    fragment.arguments = arguments
                }
            }
        } catch (e: Exception) {
            Logger.e(e)
        }
    }

    /**
     * 注册未读消息红点监听
     */
    private fun registerUnreadMsgUpdateCallback() {
        Logger.i("IMUnreadMsgManager", "注册未读消息红点监听")
        IMUnreadMsgManager.getInstance(mContext)
            .registerUnreadMsgUpdateListener(mUnreadMsgUpdateListener)

        // 触发chat模块下载安装
        Router.getActionByCallback(Configure.BUNDLE_CHAT, object : IBundleInstallCallback {
            override fun onInstallSuccess(bundleModel: BundleModel) {
                if (Configure.chatBundleModel.bundleName == bundleModel.bundleName) {
                    try {
                        val functionAction =
                            Router.getActionRouter<ChatActionRouter>(Configure.BUNDLE_CHAT)?.functionAction
                        if (functionAction != null) {
                            CustomToast.showDebugFailToast("Install Chat Done!!")
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }

            override fun onLocalInstallError(t: Throwable, bundleModel: BundleModel) {
                Logger.d(TAG, "Install Chat Failed! onLocalInstallError" + t.message)
            }

            override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {
                Logger.d(TAG, "Install Chat Failed! onRemoteInstallError " + t.message)
            }
        }, true, BundleModel.DOWNLOAD_IN_BACKGROUND)
    }

    private fun unregisterUnreadMsgUpdateCallback() {
        try {
            IMUnreadMsgManager.getInstance(mContext)
                .unregisterUnreadMsgUpdateListener(mUnreadMsgUpdateListener)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onPause() {
        super.onPause()
        mTopViewManager.onPause()
        traceOnMineExit(this)
        mIsPaused = true
        mTopTraceHelper.abandon()
        mPagerTraceHelper.abandon()
        fireWorkInterceptor?.onClose()
        FireworkApi.getInstance().removeFireworkShowingInterceptor(fireWorkInterceptor)
        mMineAdManager?.onPause()
        MyListenGuideUtil.isMinePageShow = false
        RewardAgainAdManager.onPagePause(RewardAgainAdManager.PAGE_SOURCE_MINE)
    }

    override fun onStop() {
        super.onStop()
        mTopViewManager.onStop()
    }

    override fun onDestroyView() {
        unregisterUnreadMsgUpdateCallback()
        unregisterLoginListener()
        super.onDestroyView()
        MineDataManager.release()
        NoReadManage.getInstance(mContext).removeNoReadUpdateListenerListener(this)
        ChatInternalServiceManager.unRegisterService(IChatMessageMineCallback::class.java)
        try {
            mTopView.removeOnLayoutChangeListener(mOnLayoutChangeListener)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        MineTraceManagerV9.release()
        mMineAdManager?.onDestroy()
    }

    fun loadHomePageData() {
        if (!mIsLogin) {
            updateNoLoginView()
        }
        mTopTraceHelper.postNodeOnlyLocalDebug("开始加载数据")
        MineDataManager.loadData(this, mHomePageDataCallback)
    }

    private val mHomePageDataCallback = object : IDataCallBack<HomePageModelNew?> {
        override fun onSuccess(model: HomePageModelNew?) {
            if (model == null) {
                mTopTraceHelper.notifyPageFailed()
                return
            }
            homePageModel = model
            mTopViewManager.topInfoCard.domainCardConfig.update()
            ListenTaskMsgManager.getInstance(mContext).updateNeedShowMineRedDot(model.serviceModule)
            updateUiAfterAnimation {
                if (UserInfoMannage.hasLogined() && model.useMultiMode == true) {
                    val curMode = MMKVUtil.getInstance().getInt(
                        PreferenceConstantsInHost.KEY_MINE_MODE_SWITCH_STATUS + "_" + UserInfoMannage.getUid(),
                        -1
                    )
                    if (curMode == -1) {
                        onIdentifyChange(IDENTIFY_ANCHOR, false)
                    } else {
                        onIdentifyChange(curMode, false)
                    }
                } else {
                    onIdentifyChange(IDENTIFY_USER, false)
                }
                showTopSkeletonViewState(false)
                titleComponent.setHasKK(!TextUtils.isEmpty(model.avatarKKUrl))
                titleComponent.updateFreeListenerInfo(model.freeListenV2)
                bindTopInfo(model)
                if (mIsLogin) {
                    titleComponent?.bindTitleData(model)
                    MineDataManager.restoreUser(model)
                    MineDataManager.saveUserInfo(model)
                    SharePosterManager.getInstance().setHomePageModel(model)
                    mStickyNavLayout.onRefreshComplete()
                }
                updateTitleBar()
                mTopViewManager.bindData(model, mIsLogin)
                mStickyNavLayout.onRefreshComplete()
//                checkAddPurchaseTab(model)
//                checkShowKefuGuide()
//                mockShowChaseGuide()
                traceShow()
                view?.let {
                    mTopTraceHelper.postPageEndNodeAfterRenderComplete(it)
                }
            }
        }

        override fun onError(code: Int, message: String?) {
            updateUiAfterAnimation {
//                CustomToast.showFailToast(message)
                mStickyNavLayout.onRefreshComplete()
                updateTitleBar()
                traceShow()
                mTopTraceHelper.notifyPageFailed()
                if (code == 603) {
                    showTopSkeletonViewState(true)
                }
            }
        }
    }

    private fun updateNoLoginView() {
        titleComponent.bindTitleData()
        mTopViewManager.bindData(isLogin = false)
    }

    private fun bindTopInfo(model: HomePageModelNew) {
        mIvTopBgVip.visible(View.INVISIBLE)
        if (MineTopUtil.isVipNewStyle()) {
            mIvTopBg.setImageResource(mTopViewManager.topInfoCard.domainCardConfig.cardBgResId)
        } else if (BaseFragmentActivity2.sIsDarkMode && homePageModel?.isVip != true) {
            if (MineTopUtil.isUseVipInfoCard().not()) {
                mIvTopBg.setImageDrawable(
                    ColorDrawable(
                        Color.parseColor(
                            if (model.topPicInfo?.color?.isEmpty() == true) "#1e1e1e" else model.topPicInfo?.color
                        )
                    )
                )
            }
        } else {
            if (MineTopUtil.isUseVipInfoCard().not()) {
                if (useSplitTopBg && model.topPicInfo?.foregroundPicUrl.isNullOrEmpty().not()) {
                    ImageManager.from(mContext)
                        .displayImage(mIvTopBg, model.topPicInfo?.backgroundPicUrl, -1)
                    if (model.vipInfo.isVip) {
                        ImageManager.from(mContext)
                            .displayImage(mIvTopBgVip, model.topPicInfo?.foregroundPicUrl, -1)
                        mIvTopBgVip.visible(View.VISIBLE)
                    }
                } else {
                    ImageManager.from(mContext).displayImage(mIvTopBg, model.topPicInfo?.picUrl, -1)
                }
            }
        }
        val titleColor = if (MineTopUtil.isVipNewStyle()) {
            mTopViewManager.topInfoCard.domainCardConfig.topBgTitleColorViewColor
        } else if (MineTopUtil.isUseVipInfoCard()) {
            ContextCompat.getColor(mContext, R.color.main_color_ececf1_212326)
        } else {
            try {
                if (model.topPicInfo?.color.isNullOrEmpty()) {
                    0
                } else {
                    Color.parseColor(model.topPicInfo?.color)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                0
            }
        }
        if (titleColor != 0) {
            mRootView.background?.mutate()?.colorFilter =
                PorterDuffColorFilter(titleColor, PorterDuff.Mode.SRC_IN)
            mFixTitleBar.background?.mutate()?.colorFilter =
                PorterDuffColorFilter(titleColor, PorterDuff.Mode.SRC_IN)
        }
        initPageColor()
        if (model.isShowRealNameGuide) {
            MineTraceManagerV9.addTrace(realNameCardView, object : OnTrace {
                override fun invoke(p1: Int) {
                    if (com.ximalaya.ting.android.host.util.view.ViewStatusUtil.viewIsRealShowing(
                            realNameCardView
                        )
                    ) {
                        eTraceRealNameGuideShow()
                    }
                }
            })
            changeToolsBottomVisible(realNameCardView, true)
            realNameCardView.setOnClickListener {
                traceRealNameGuideClick()
                eTraceRealNameGuideDialogShow()
                DialogBuilder<DialogBuilder<*>>(activity)
                    .setTitle("前台实名提醒")
                    .setMessage("根据平台规则，50万以上粉丝账号需要前台展示实名认证信息。对于同意前台展示的账号，我们将优先提供流量扶持。对于不同意前台展示的账号，我们将逐步采取相关限制措施。请确认是否在个人页展示实名信息？")
                    .setOkBtn("确认展示") {
                        traceRealNameGuideDialogClick(true)
                        changeToolsBottomVisible(realNameCardView, false)
                        SettingSwitchManager.switchSetting(
                            SwitchSettingsConstants.SWITCH_REAL_NAME,
                            "1",
                            object : IDataCallBack<String?> {
                                override fun onSuccess(data: String?) {
                                    CustomToast.showToast("实名信息已在个人页展示")
                                }

                                override fun onError(code: Int, message: String?) {
                                }
                            }
                        )
                    }.setCancelBtn("接受限制") { traceRealNameGuideDialogClick(false) }
                    .showConfirm()
            }
        } else {
            changeToolsBottomVisible(realNameCardView, false)
        }
    }

    private fun initPageColor() {
        if (MineTopUtil.isVipNewStyle().not()) {
            return
        }
        flIndicatorContainer?.setBackgroundColor(getColorSafe(MineTopUtil.getPageBgColor()))
        mViewPager?.setBackgroundColor(getColorSafe(MineTopUtil.getPageBgColor()))
    }

    private fun changeToolsBottomVisible(dest: View, visible: Boolean) {
        ViewStatusUtil.setVisible(if (visible) View.VISIBLE else View.GONE, dest)
        ViewStatusUtil.setVisible(if (llToolsBottom.children.any {
                it.visibility == View.VISIBLE
            }) View.VISIBLE else View.GONE, llToolsBottom)
    }

    override fun getBaseFragment2(): BaseFragment2 = this

    private fun refreshTabFragment() {
        (mAdapter?.getFragmentAtPosition(mCurPosition) as? IMineWoTingTabFragment)?.onRefresh()
        traceViewPagerItemShow(TraceManager.SCROLL_SHOW)
    }

    private fun updateTitleBar() {
        var isDark =
            if (MineTopUtil.isVipNewStyle()) {
                MineTopUtil.isDarkMode()
            } else if (MineTopUtil.isUseVipInfoCard()) {
                BaseFragmentActivity.sIsDarkMode
            } else {
                UserInfoMannage.isVipUser() || BaseFragmentActivity.sIsDarkMode
            }
        if (headAdView.visibility == View.VISIBLE) {
            StatusBarManager.setStatusBarColor(window, false)
            if (android.os.Build.VERSION.SDK_INT >= 21) {
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                window.statusBarColor = Color.BLACK
            }
        } else {
            StatusBarManager.setStatusBarColor(window, !isDark)
        }
        if (MineTopUtil.isVipNewStyle()) {
            mStickyNavLayout.setRefreshHeaderBackgroundColor(mTopViewManager.topInfoCard.domainCardConfig.refreshViewBgColor)
        }
        mStickyNavLayout.setAllViewColor(if (isDark) 0x80FFFFFF.toInt() else Color.BLACK)
        titleComponent.updateTitleColor(isDark)
        mIsDarkPage = isDark
    }

    override fun darkStatusBar(): Boolean = !mIsDarkPage

    fun smoothScrollToNav() {
        mStickyNavLayout.stickyNavLayout.smoothScrollToNav()
    }

    fun setStickyNavLayoutShouldIgnore(shouldIgnore: Boolean) {
        mStickyNavLayout.stickyNavLayout.setShouldIgnore(shouldIgnore)
    }

    private fun getTitleBarAlpha(scrollY: Int): Int {
        var fraction = 1f
        if (mScrollRange > 0 && scrollY < mScrollRange) {
            fraction = scrollY * 1.0f / mScrollRange
        }
        val fra: Float = min(max(fraction, 0f), 1f)
        val interpolator = DecelerateInterpolator(1.5f)
        return (255 * interpolator.getInterpolation(fra)).toInt()
    }

    private fun traceShow() {
        if (mIsFirstVisible) {
            mIsFirstVisible = false
            postOnUiThreadDelayed({
                traceShowInner(TraceManager.FIRST_VISIBLE_SHOW)
            }, 100)
        } else if (mIsPaused) {
            mIsPaused = false
            postOnUiThread { traceShowInner(TraceManager.BACK_VISIBLE_SHOW) }
        }
    }

    private fun traceShowInner(@TraceManager.TraceExploreType exploreType: Int) {
        MineTraceManagerV9.traceShow(exploreType)
        if (exploreType != TraceManager.FIRST_VISIBLE_SHOW) {
            traceViewPagerItemShow(exploreType)
        }
    }

    private fun traceViewPagerItemShow(@TraceManager.TraceExploreType exploreType: Int) {
        (mAdapter?.getFragmentAtPosition(mCurPosition) as? IMineWoTingTabFragment)?.traceShow(
            exploreType
        )
    }

    override fun getPageScrollY(): Int {
        return mStickyNavLayout.stickyNavLayout?.scrollY ?: 0
    }

    fun scrollToTop(smooth: Boolean, doRefresh: Boolean) {
        if (mViewPager != null && mAdapter != null) {
            // val index = mViewPager.currentItem
            val count = mAdapter!!.count
            for (index in 0 until count) {
                val fragment = mAdapter!!.getFragmentAtPosition(index)
                if (fragment is IGoTopListener) {
                    (fragment as IGoTopListener).onGoTop(false, doRefresh)
                }
            }
        }
    }

    override fun clickRefresh() {
        showTabRed()
        // 点击tab以后将页面移到顶部
        scrollToTop(false, true)
    }

    override fun notifyTraceSucess() {
        view?.let { mPagerTraceHelper.postPageEndNodeAfterRenderComplete(it) }
    }

    override fun notifyTraceFailed() {
        mPagerTraceHelper.notifyPageFailed()
    }

    override fun isUserMode(): Boolean {
        return curIdentifyMode == IDENTIFY_USER
    }

    override fun onIdentifyChange(mode: Int, save: Boolean) {
        if (!UserInfoMannage.hasLogined()) {
            return
        }
        curIdentifyMode = mode
        if (isAnchorMode()) {
            mMineAdManager?.onEnterAnchorMode()
        }
        if (!isUserMode) {
            hideHeadAd(BaseUtil.getScreenWidth(ToolUtil.getCtx()) * 9 / 16, -1)
        }
        if (save) {
            MMKVUtil.getInstance()
                .saveInt(
                    PreferenceConstantsInHost.KEY_MINE_MODE_SWITCH_STATUS + "_" + UserInfoMannage.getUid(),
                    mode
                )
        }
        MineTraceManagerV9.traceShow(TraceManager.BACK_VISIBLE_SHOW)
    }

    override fun openDressUp() {
        ToolUtil.clickUrlAction(this, homePageModel?.userDecoratorInfo?.imageBoxUrl ?: "", null)
    }

    override fun enableAdapt(): Boolean {
        return true
    }

    private fun updateMineRedDot(count: Int) {
        val activity: Activity = (activity as? MainActivity) ?: return
        val noReadModel = NoReadManage.getInstance(activity).noReadModel ?: return
        noReadModel.unReadMessageCount = count
        if (MyListenAbUtil.getSlideExperienceFlag() != 3 || MyListenAbUtil.getSlideExperienceFlag() != 4) {
            titleComponent.updateSlideMsgCount()
        }
        (activity as MainActivity).updateMineListenTabRedDot(false, false)
    }

    /**
     * @param isNewMessage 长链新消息或者自定义清除红点消息
     * */
    override fun onUnreadMessageAdd(count: Int, isNewMessage: Boolean) {
        Logger.i(TAG, "onUnreadMessageAdd $count $isNewMessage")
        // 使用首次数据以规避Main页面监听被反注册后消息数可能为0的情况
        if (isFirstMessage && msgRedCount == 0 && !isNewMessage && count > 0) {
            isFirstMessage = false
            onUnreadMessageAdd(count, true)
        }
        if (!isNewMessage) {
            return
        }
        if (!UserInfoMannage.hasLogined()) {
            updateMineRedDot(0)
            hasUnReadMsg = false
            mTab.showRedDot(MyListenTabEnum.TAB_MY_MESSAGE.position)
            mTab.hideRedDot(MyListenTabEnum.TAB_MY_MESSAGE.position)
            return
        }
        if (!isRealVisable) {
            updateMineRedDot(count)
        } else if (MyListenAbUtil.hasSlideBar()) {
            titleComponent.updateSlideMsgCount()
        }
        if (mCurPosition == MyListenTabEnum.TAB_MY_MESSAGE.position) {
            Logger.i(TAG, "正在显示消息页...忽略顶Tab红点")
            hasUnReadMsg = false
            mTab.hideRedDot(mCurPosition)
            return
        }
        msgRedCount = count
        hasUnReadMsg = count > 0

        if (hasAddMessageTab()) {
            mTab.showRedDot(MyListenTabEnum.TAB_MY_MESSAGE.position, count)
        }
    }

    fun changeAvatarVisible(isVisible: Boolean) {
        mTopViewManager.changeAvatarVisible(isVisible)
    }

    fun setHasKK(hasKK: Boolean) {
        mTopViewManager.setHasKK(hasKK)
    }

    fun switchTitleMode(isFix: Boolean) {
        titleComponent.switchMode(isFix)
        mStickyNavLayout.refreshableView?.let {
            it.setPadding(0, titleComponent.getStickTopPadding(), 0, 0)
        }
    }

    override fun showHeadAd(adHeight: Int) {
        if (mShowAdAnimPlaying || adHeight <= 0 || !openAd || headAdView.visibility == View.VISIBLE) {
            return
        }
        mShowAdAnimPlaying = true
        val objectAnimator = ValueAnimator.ofInt(
            adHeight,
            0
        ).setDuration(DEFAULT_AD_ANIM_DURATION)
        objectAnimator.addUpdateListener {
            mStickyNavLayout.stickyNavLayout.scrollY = it.getAnimatedValue() as Int
        }
        objectAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator?) {
                super.onAnimationStart(animation)
                switchTitleMode(false)
                mStickyNavLayout.mode = PullToRefreshBase.Mode.DISABLED
                headAdView.visibility = View.VISIBLE
            }

            override fun onAnimationEnd(animation: Animator?) {
                super.onAnimationEnd(animation)
                mShowAdAnimPlaying = false
                updateTitleBar()
            }

            override fun onAnimationCancel(animation: Animator?) {
                super.onAnimationCancel(animation)
                onAnimationEnd(animation)
            }
        })
        objectAnimator.start()
    }

    override fun hideHeadAd(adHeight: Int, scrollY: Int) {
        if (!openAd || headAdView.visibility != View.VISIBLE || mHideAdAnimPlaying || (scrollY != -1 && scrollY < headAdView.measuredHeight / 2)
            || mShowAdAnimPlaying || adHeight <= 0
        ) {
            return
        }
        if (scrollY > adHeight) {
            switchTitleMode(true)
            mStickyNavLayout.mode = PullToRefreshBase.Mode.PULL_FROM_START
            mStickyNavLayout.stickyNavLayout.scrollY = scrollY - adHeight
            headAdView.visibility = View.GONE
            mMineAdManager?.onTopAdRealClose()
            updateTitleBar()
//            mStickyNavLayout.stickyNavLayout.postInvalidate()
        } else if (scrollY == -1 || scrollY >= adHeight / 2 && scrollY <= adHeight) {
            mHideAdAnimPlaying = true
            val realScrollY = mStickyNavLayout.stickyNavLayout.scrollY
            val objectAnimator = ValueAnimator.ofInt(
                realScrollY,
                adHeight
            ).setDuration((DEFAULT_AD_ANIM_DURATION).toLong())
            objectAnimator.addUpdateListener {
                mStickyNavLayout.stickyNavLayout.scrollY = it.getAnimatedValue() as Int
            }
            objectAnimator.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator?) {
                    mHideAdAnimPlaying = false
                    headAdView.visibility = View.GONE
                    mMineAdManager?.onTopAdRealClose()
                    mStickyNavLayout.stickyNavLayout.scrollY = 0
                    switchTitleMode(true)
                    mStickyNavLayout.mode = PullToRefreshBase.Mode.PULL_FROM_START
                    updateTitleBar()
                }

                override fun onAnimationCancel(animation: Animator?) {
                    onAnimationEnd(animation)
                }
            })
            objectAnimator.start()
        }
    }

    override fun isAnchorMode(): Boolean {
        return !isUserMode
    }

    override fun isHasGuideNeedShow(): Boolean {
        if (ViewUtil.haveDialogIsShowing(activity)) {
            if (!MMKVUtil.getInstance()
                    .getBoolean(KEY_MESSAGE_TAB_HAS_SHOW) || !MMKVUtil.getInstance()
                    .getBoolean(KEY_PURCHASED_TAB_HAS_SHOW)
            ) {
                return true
            }
        }
        return false
    }

    override fun getCurrentScrollY(): Int {
        return mStickyNavLayout.stickyNavLayout.scrollY
    }

    override fun showBannerAd() {
        if (!openAd) {
            return
        }
        changeToolsBottomVisible(bannerAdView, true)
    }

    override fun hideBannerAd() {
        if (!openAd) {
            return
        }
        if (bannerAdView.visibility == View.GONE) {
            return
        }
        changeToolsBottomVisible(bannerAdView, false)
    }

    fun useLightTopBg(): Boolean {
        return MineTopUtil.isDarkMode().not()
//        return BaseFragmentActivity2.sIsDarkMode.not() && homePageModel?.isVip != true
    }

    private fun getDefaultPage(): Int {
        return MMKVUtil.getInstance().getInt(KEY_LAST_TAB_POSITION, MyListenTabEnum.TAB_HISTORY.position)
    }

    override fun update(noReadModel: NoReadModel?) {

    }

    override fun update(homeUnRead: HomeUnRead?) {
        showTabRed()
    }
}