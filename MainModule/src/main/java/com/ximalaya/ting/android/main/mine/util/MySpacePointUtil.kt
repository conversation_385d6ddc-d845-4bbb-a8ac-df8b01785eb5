package com.ximalaya.ting.android.main.mine.util

import android.widget.TextView
import com.ximalaya.ting.android.host.util.extension.dp
import kotlin.math.pow

/**
 * Created by dekai.liu on 4/9/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18392566603
 */
private const val TEXT_SIZE_16 = 16
private const val TEXT_SIZE_20 = 20

fun getTextSizeSp(count: Long): Int {
    if (count == 0L) {
        return TEXT_SIZE_16
    }

    return if (count / 10.0.pow(6.0) > 1) {
        TEXT_SIZE_16
    } else {
        TEXT_SIZE_20
    }
}

fun TextView?.setTextPadding(sp: Int) {
    this?.apply {
        if (sp == TEXT_SIZE_16) {
            setPadding(0, 0, 0, 0)
        } else {
            setPadding(0, 3.dp, 0, 0)
        }
    }
}

fun getListenedDurationPair(listenedHours: Long?, listenedMinutes: Long?): Pair<Long, String> {
    return when {
        listenedHours != null && listenedHours > 0L -> {
            listenedHours to "小时"
        }
        listenedMinutes != null && listenedMinutes > 0L -> {
            listenedMinutes to "分钟"
        }
        else -> {
            0L to "分钟"
        }
    }
}

fun getWeekListenedDurationStr(listenedMinutes: Long?): String {
    if (listenedMinutes == null || listenedMinutes == 0L) {
        return "本周 0 分钟"
    }

    return "本周 " + if (listenedMinutes < 60) {
        "${listenedMinutes}分钟"
    } else {
        var durationStr = "${listenedMinutes / 60}小时"
        if (listenedMinutes % 60 != 0L) {
            durationStr += "${listenedMinutes % 60}分钟"
        }
        durationStr
    }
}