package com.ximalaya.ting.android.main.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 任务奖励
 *
 * <AUTHOR>
 */

public class QuestReward implements Parcelable {
    private boolean status;
    private int award;
    private String recommend;
    private String content;
    private String url;

    public QuestReward() {

    }

    protected QuestReward(Parcel in) {
        status = in.readByte() != 0;
        award = in.readInt();
        recommend = in.readString();
        content = in.readString();
        url = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte((byte) (status ? 1 : 0));
        dest.writeInt(award);
        dest.writeString(recommend);
        dest.writeString(content);
        dest.writeString(url);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<QuestReward> CREATOR = new Creator<QuestReward>() {
        @Override
        public QuestReward createFromParcel(Parcel in) {
            return new QuestReward(in);
        }

        @Override
        public QuestReward[] newArray(int size) {
            return new QuestReward[size];
        }
    };

    public boolean getStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public int getAward() {
        return award;
    }

    public void setAward(int award) {
        this.award = award;
    }

    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
