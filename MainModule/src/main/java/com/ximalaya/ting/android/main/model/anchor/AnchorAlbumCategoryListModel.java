package com.ximalaya.ting.android.main.model.anchor;

import com.google.gson.Gson;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.opensdk.model.album.Album;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhifu.zhang on 2019-08-27.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18362080562
 * @desc 当前类属个人页的专辑列表数据，根据userType分为普通用户和mcn用户，这两种类型数据结构不一致，解析时需特别注意
 * @warn 当前类不适合Gson等自动解析，需手动解析
 * @接口文档 http://ops.ximalaya.com/api-manager-backend/home#/myProjectApiLook?id=14546&isEdit=true&projectId=88
 * @server_author linwenbo
 */
public class AnchorAlbumCategoryListModel {


    /**
     * 普通用户数据结构
     * {
     * "msg":"0"
     * "ret":0
     * "maxPageId":1
     * "pageSize":15
     * "userType":-1
     * "list":[...]
     * "pageId":1    对应pageNum字段
     * "totalCount":14
     * }
     */

    /**
     * mcn用户数据结构
     * {
     * "msg":"0"
     * "ret":0
     * "pages":20
     * "pageSize":5
     * "totals":100
     * "list":[...]
     * "pageNum":1
     * }
     */

    private int pages;//共有多少页
    private int pageSize;//每页有多少条数据
    private int pageNum;//当前页码
    private int userType;//用户类型 3:mcn other:普通用户
    private int totalCount;//总共有多少条数据
    private List<AnchorAlbumCategoryModel> categoryAlbumList;//mcn用户的list
    private List<AlbumM> oriAlbumList;//普通用户的list


    public void parse(JSONObject jsonObject) throws Exception{
        setPageSize(jsonObject.optInt("pageSize",0));
        int type = jsonObject.optInt("userType", -1);
        setUserType(type);
        JSONArray jsonArray = jsonObject.optJSONArray("list");
        if (type == 3){//mcn用户
            setPages(jsonObject.optInt("pages",0));
            setPageNum(jsonObject.optInt("pageNum",0));
            if (jsonArray != null && jsonArray.length() > 0){
                categoryAlbumList = new ArrayList<>();
                for (int i = 0; i < jsonArray.length(); i++){
                    JSONObject catItem = jsonArray.getJSONObject(i);
                    if (catItem != null){
                        AnchorAlbumCategoryModel model = new AnchorAlbumCategoryModel();
                        model.parse(catItem);
                        categoryAlbumList.add(model);
                    }
                }
            }
        } else {//普通用户
            setPageNum(jsonObject.optInt("pageId",0));
            setTotalCount(jsonObject.optInt("totalCount",0));
            if (jsonArray != null && jsonArray.length() > 0){
                oriAlbumList = new ArrayList<>();
                for (int i = 0; i < jsonArray.length(); i++){
                    String oriJsonStr = jsonArray.optString(i);
                    AlbumM obj = null;
                    try {
                        Constructor<AlbumM> constructor = AlbumM.class
                                .getDeclaredConstructor(new Class[] { String.class });
                        obj = constructor.newInstance(new Object[] { oriJsonStr });
                    } catch (Exception e){
                        try {
                            obj = new Gson().fromJson(oriJsonStr,AlbumM.class);
                        } catch (Exception e1){
                            e1.printStackTrace();
                        }
                    }
                    if (obj != null){
                        oriAlbumList.add(obj);
                    }
                }
            }
        }
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<AnchorAlbumCategoryModel> getCategoryAlbumList() {
        return categoryAlbumList;
    }

    public void setCategoryAlbumList(List<AnchorAlbumCategoryModel> categoryAlbumList) {
        this.categoryAlbumList = categoryAlbumList;
    }

    public List<AlbumM> getOriAlbumList() {
        return oriAlbumList;
    }

    public void setOriAlbumList(List<AlbumM> oriAlbumList) {
        this.oriAlbumList = oriAlbumList;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public static class AnchorAlbumCategoryModel {
        /**
         * id : 10
         * title : hello
         * albums : [{"albumId":1000,"title":"专辑","coverSmall":"http://photocdn.sohu
         * .com/20130925/Img387224863.jpg","coverMiddle":"http://photocdn.sohu
         * .com/20130925/Img387224863.jpg","coverLarge":"http://photocdn.sohu
         * .com/20130925/Img387224863.jpg","isDraft":false,"unReadAlbumCommentCount":0}]
         */

        private int id;
        private String title;
        private List<AlbumM> albums;
        private int totalSize;

        public void parse(JSONObject jsonObject) throws Exception{
            setId(jsonObject.optInt("id"));
            setTitle(jsonObject.optString("title"));
            if (jsonObject.has("totalSize")) {
                setTotalSize(jsonObject.optInt("totalSize", 0));
            }
            JSONArray jsonArray = jsonObject.optJSONArray("albums");
            if (jsonArray != null && jsonArray.length() > 0){
                albums = new ArrayList<>();
                for (int i = 0; i < jsonArray.length(); i++){
                    JSONObject album = jsonArray.getJSONObject(i);
                    if (album != null){
                        AlbumM albumM = new AlbumM();
                        albumM.parseAlbum(album);
                        albums.add(albumM);
                    }
                }
            }
        }

        public int getTotalSize() {
            return totalSize;
        }

        public void setTotalSize(int totalSize) {
            this.totalSize = totalSize;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public List<AlbumM> getAlbums() {
            return albums;
        }

        public void setAlbums(List<AlbumM> albums) {
            this.albums = albums;
        }
    }
}
