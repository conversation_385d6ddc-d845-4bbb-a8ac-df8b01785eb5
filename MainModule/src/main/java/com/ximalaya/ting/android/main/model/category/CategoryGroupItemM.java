package com.ximalaya.ting.android.main.model.category;

import androidx.annotation.Nullable;

/**
 * <AUTHOR> on 2017/10/18.
 */

public class CategoryGroupItemM {
    public static final int ITEM_TYPE_CATEGORY = 0;
    public static final int ITEM_TYPE_TAG = 1;

    public int itemType; // 类目的类型：【分类：0，热词：1】

    @Nullable
    public CategoryGroupItemDetailM itemDetail;

    public boolean isDisplayCornerMark;   // 是否显示角标 【显示：true，不显示：false】

    @Nullable
    public String cornerMark;    // 角标文字,实际上是枚举类型

    @Nullable
    public String coverPath;
}
