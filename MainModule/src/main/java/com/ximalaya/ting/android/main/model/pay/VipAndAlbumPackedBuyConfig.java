package com.ximalaya.ting.android.main.model.pay;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.List;

public class VipAndAlbumPackedBuyConfig implements Serializable {
    public static final int CHANNEL_ALI_PAY = 2;
    public static final int CHANNEL_WECHAT_PAY = 3;
    public static final int CHANNEL_UNION_PAY = 5;
    @SerializedName("returnUrl")
    public String returnUrl;
    @SerializedName("supportChannels")
    public List<SupportChannels> supportChannels;

    public static VipAndAlbumPackedBuyConfig parse(String jsonString) {
        try {
            VipAndAlbumPackedBuyConfig result = new VipAndAlbumPackedBuyConfig();
            JSONObject jsonObject = new JSONObject(jsonString);
            jsonObject = jsonObject.getJSONObject("data");
            result.returnUrl = jsonObject.optString("returnUrl");
            String channelsString = jsonObject.optString("supportChannels");
            result.supportChannels = new Gson().fromJson(channelsString,
                    new TypeToken<List<SupportChannels>>(){}.getType());
            return result;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static class SupportChannels {
        @SerializedName("channelId")
        public int channelId;
        @SerializedName("description")
        public String description;
    }
}
