package com.ximalaya.ting.android.main.model.pay.order;

import com.ximalaya.ting.android.host.util.common.StringUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by {jian.kang} on 10/21/21
 *
 * <AUTHOR>
 */
public class MyWalletOrderResources {

    public List<ConfigRet> configRetList;

    public static class ConfigRet {
        public int mConfigRetCode;
        public List<ImgConfig> imgConfigList;
        public LoanConfig loanConfig;

        public List<ImgConfig> localFirstLineImgConfigList = new ArrayList<>();
        public List<ImgConfig> localSecondLineImgConfigList = new ArrayList<>();

        public void format() {
            localFirstLineImgConfigList.clear();
            localSecondLineImgConfigList.clear();

            if (null == imgConfigList) {
                return;
            }
            if (4 >= imgConfigList.size()) {
                localFirstLineImgConfigList.addAll(imgConfigList);
                return;
            }
            for (int i = 0; i < 4; i++) {
                localFirstLineImgConfigList.add(imgConfigList.get(i));
            }
            for (int i = 4; (i < 8 && i < imgConfigList.size()); i++) {
                localSecondLineImgConfigList.add(imgConfigList.get(i));
            }
            if (8 < imgConfigList.size()) {
                for (int i = 8; i < imgConfigList.size(); i++) {
                    ImgConfig temp = imgConfigList.get(i);
                    if (0 == (i % 2)) {
                        localFirstLineImgConfigList.add(temp);
                    } else {
                        localSecondLineImgConfigList.add(temp);
                    }
                }
            }
        }
    }

    public static class ImgConfig {
        public String resUrl;
        public String bizUrl;
        public String title;

        public ImgConfig(JSONObject jsonObject) {
            if (jsonObject != null) {
                if (jsonObject.has("resUrl")) {
                    resUrl = jsonObject.optString("resUrl");
                }
                if (jsonObject.has("bizUrl")) {
                    bizUrl = jsonObject.optString("bizUrl");
                }
                if (jsonObject.has("title")) {
                    title = jsonObject.optString("title");
                }
            }
        }
    }

    public static class LoanConfig {
        public static final int LOAN_CONFIG_CODE = 1104;

        public boolean visible;
        public String resUrl;               // 图片资源链接
        public String bizUrl;               // 业务跳转链接

        public LoanConfig(JSONObject jsonObject) {
            if (jsonObject != null) {
                if (jsonObject.has("resUrl")) {
                    resUrl = jsonObject.optString("resUrl");
                }
                if (jsonObject.has("bizUrl")) {
                    bizUrl = jsonObject.optString("bizUrl");
                }
            }
        }

        public boolean isVisible() {
            return !StringUtil.isEmpty(resUrl);
        }
    }

    public void parseData(JSONObject jsonObject) {
        try {
            String data = jsonObject.optString("data");
            JSONArray jsonArray = new JSONArray(data);
            for (int j = 0; j < jsonArray.length(); j++) {
                JSONObject json = jsonArray.getJSONObject(j);
                if (json.has("resPositionCode") && json.has("data")) {
                    if (configRetList == null) {
                        configRetList = new ArrayList<>();
                    }
                    ConfigRet configRet = new ConfigRet();
                    configRet.mConfigRetCode = json.optInt("resPositionCode");
                    if (configRet.mConfigRetCode == 1102) {
                        if (configRet.imgConfigList == null) {
                            configRet.imgConfigList = new ArrayList<>();
                        }
                        JSONArray array = json.optJSONArray("data");
                        if (array != null && array.length() > 0) {
                            for (int i = 0; i < array.length(); i++) {
                                ImgConfig imgConfig = new ImgConfig(array.getJSONObject(i));
                                configRet.imgConfigList.add(imgConfig);
                            }
                        }
                        configRet.format();
                    } else if (configRet.mConfigRetCode == LoanConfig.LOAN_CONFIG_CODE) {
                        if (configRet.loanConfig == null) {
                            configRet.loanConfig = new LoanConfig(json.optJSONObject("data"));
                        }
                    }
                    configRetList.add(configRet);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
