package com.ximalaya.ting.android.main.model.podcast;

import java.util.List;

/**
 * Created by wenbin.liu on 2021/1/22
 *
 * <AUTHOR>
 */
public class HomeFeedTrackItemVO {

    private String albumTitle;

    private String coverPath;

    private int duration;

    private String introduce;

    private long playCount;

    private List<TagVO> taglist;

    private long trackId;

    private String trackTitle;

    private long albumId;

    private String recSrc;

    private String recTrack;

    public String getRecSrc() {
        return recSrc;
    }

    public void setRecSrc(String recSrc) {
        this.recSrc = recSrc;
    }

    public String getRecTrack() {
        return recTrack;
    }

    public void setRecTrack(String recTrack) {
        this.recTrack = recTrack;
    }

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public String getAlbumTitle() {
        return albumTitle;
    }

    public void setAlbumTitle(String albumTitle) {
        this.albumTitle = albumTitle;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public long getPlayCount() {
        return playCount;
    }

    public void setPlayCount(long playCount) {
        this.playCount = playCount;
    }

    public List<TagVO> getTaglist() {
        return taglist;
    }

    public void setTaglist(List<TagVO> taglist) {
        this.taglist = taglist;
    }

    public long getTrackId() {
        return trackId;
    }

    public void setTrackId(long trackId) {
        this.trackId = trackId;
    }

    public String getTrackTitle() {
        return trackTitle;
    }

    public void setTrackTitle(String trackTitle) {
        this.trackTitle = trackTitle;
    }
}
