package com.ximalaya.ting.android.main.model.rec;

import com.google.gson.Gson;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.RecInfo;
import com.ximalaya.ting.android.main.model.album.AlbumMInMain;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by WolfXu on 2019/2/14.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class RecommendCollectionItem {
    private String title;
    private String subtitle;
    private long specialId;
    private RecInfo recInfo;
    private List<AlbumM> list;

    public RecommendCollectionItem(JSONObject jsonObject) {
        if (jsonObject != null) {
            setTitle(jsonObject.optString("title"));
            setSubtitle(jsonObject.optString("subtitle"));
            setSpecialId(jsonObject.optLong("specialId"));
            if (jsonObject.has("recInfo")) {
                setRecInfo(new Gson().fromJson(jsonObject.optString("recInfo"), RecInfo.class));
            }
            JSONArray jsonArray1 = jsonObject.optJSONArray("list");
            if (jsonArray1 != null) {
                list = new ArrayList<>();
                for (int i = 0; i < jsonArray1.length(); i++) {
                    AlbumMInMain albumM = new AlbumMInMain();
                    albumM.setIndexOfList(i + 1);
                    try {
                        albumM.parseAlbum(jsonArray1.optJSONObject(i));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    list.add(albumM);
                }
            }
        }
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public long getSpecialId() {
        return specialId;
    }

    public void setSpecialId(long specialId) {
        this.specialId = specialId;
    }

    public RecInfo getRecInfo() {
        return recInfo;
    }

    public void setRecInfo(RecInfo recInfo) {
        this.recInfo = recInfo;
    }

    public List<AlbumM> getList() {
        return list;
    }

    public void setList(List<AlbumM> list) {
        this.list = list;
    }
}
