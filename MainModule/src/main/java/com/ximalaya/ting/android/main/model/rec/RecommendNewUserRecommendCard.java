package com.ximalaya.ting.android.main.model.rec;

import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.track.TrackM;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by WolfXu on 2019/5/15.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class RecommendNewUserRecommendCard {

    public static final int MODULE_TYPE_ALBUM = 1;
    public static final int MODULE_TYPE_VIDEO = 2;
    public static final int MODULE_TYPE_TRACK = 3;
    private static final int DEFAULT_SHOW_LINE = 2;

    private int id;
    private String moduleName;
    private int moduleType;
    private String categoryName;
    private String categoryId;
    private int showLine;
    private String bgColor;
    private String imgCover;
    private boolean liked;
    private String iting;
    private int albumSize;
    private String recReason;
    private List<AlbumM> mAlbumList;
    private List<TrackM> mVideoList;
    private List<TrackM> mTrackList;
    private List<Long> mExcludeIds = new ArrayList<>(); // 用于换一批，排除前面显示的

    private RecommendItemNew mRecommendItemBelongTo; // 所在的item，因为服务端给的数据是一个item中包含多个新人推荐卡片，存个item的引用，方便处理。

    public static RecommendNewUserRecommendCard parseJson(JSONObject jsonObject) {
        try {
            RecommendNewUserRecommendCard card = new RecommendNewUserRecommendCard();
            if (jsonObject != null) {
                card.setId(jsonObject.optInt("id"));
                card.setModuleName(jsonObject.optString("moduleName"));
                card.setModuleType(jsonObject.optInt("moduleType"));
                card.setCategoryName(jsonObject.optString("categoryName"));
                card.setShowLine(jsonObject.optInt("showLine"));
                card.setBgColor(jsonObject.optString("bgColor"));
                card.setImgCover(jsonObject.optString("imgCover"));
                card.setLiked(jsonObject.optBoolean("liked"));
                card.setIting(jsonObject.optString("iting"));
                card.setCategoryId(jsonObject.optString("categoryId"));
                card.setAlbumSize(jsonObject.optInt("albumSize"));
                card.setRecReason(jsonObject.optString("recReason"));
                JSONArray jsonArray = jsonObject.optJSONArray("albums");
                if (jsonArray != null) {
                    switch (card.getModuleType()) {
                    case MODULE_TYPE_ALBUM:
                        card.setAlbumList(new ArrayList<AlbumM>());
                        for (int i = 0; i < jsonArray.length(); i++) {
                            card.getAlbumList().add(new AlbumM(jsonArray.optString(i)));
                        }
                        break;
                    case MODULE_TYPE_VIDEO:
                        card.setVideoList(new ArrayList<TrackM>());
                        for (int i = 0; i < jsonArray.length(); i++) {
                            card.getVideoList().add(new TrackM(jsonArray.optString(i)));
                        }
                        break;
                    case MODULE_TYPE_TRACK:
                        card.setTrackList(new ArrayList<TrackM>());
                        for (int i = 0; i < jsonArray.length(); i++) {
                            card.getTrackList().add(new TrackM(jsonArray.optString(i)));
                        }
                        break;
                    default:
                        return null;
                    }
                }
            }
            return card;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public int getModuleType() {
        return moduleType;
    }

    public void setModuleType(int moduleType) {
        this.moduleType = moduleType;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public List<AlbumM> getAlbumList() {
        return mAlbumList;
    }

    public void setAlbumList(List<AlbumM> albumList) {
        mAlbumList = albumList;
    }

    public List<TrackM> getVideoList() {
        return mVideoList;
    }

    public void setVideoList(List<TrackM> videoList) {
        mVideoList = videoList;
    }

    public int getShowLine() {
        if (showLine <= 0) {
            return DEFAULT_SHOW_LINE;
        }
        return showLine;
    }

    public void setShowLine(int showLine) {
        this.showLine = showLine;
    }

    public String getBgColor() {
        return bgColor;
    }

    public void setBgColor(String bgColor) {
        this.bgColor = bgColor;
    }

    public String getImgCover() {
        return imgCover;
    }

    public void setImgCover(String imgCover) {
        this.imgCover = imgCover;
    }

    public boolean isLiked() {
        return liked;
    }

    public void setLiked(boolean liked) {
        this.liked = liked;
    }

    public String getIting() {
        return iting;
    }

    public void setIting(String iting) {
        this.iting = iting;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getRecReason() {
        return recReason;
    }

    public void setRecReason(String recReason) {
        this.recReason = recReason;
    }

    public int getAlbumSize() {
        return albumSize;
    }

    public void setAlbumSize(int albumSize) {
        this.albumSize = albumSize;
    }

    public RecommendItemNew getRecommendItemBelongTo() {
        return mRecommendItemBelongTo;
    }

    public void setRecommendItemBelongTo(RecommendItemNew recommendItemBelongTo) {
        mRecommendItemBelongTo = recommendItemBelongTo;
    }

    public List<TrackM> getTrackList() {
        return mTrackList;
    }

    public void setTrackList(List<TrackM> trackList) {
        mTrackList = trackList;
    }

    public List<Long> getExcludeIds() {
        return mExcludeIds;
    }
}
