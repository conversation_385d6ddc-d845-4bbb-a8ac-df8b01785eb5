package com.ximalaya.ting.android.main.model.rec;

import com.ximalaya.ting.android.host.model.community.RelAlbum;
import com.ximalaya.ting.android.main.model.recommend.DislikeReasonModel;

import java.util.List;

/**
 * Created by chend<PERSON><PERSON> on 2020/9/4
 *
 * @anthor chendekun
 * @email <EMAIL>
 * @phone 13032178206
 * describe:
 **/
public class RecommendQa {
    /**
     * agreeCounts : 0
     * answer : 方法
     * author : {"anchorGrade":1,"avatar":"http://imagev2.test.ximalaya.com/group1/M00/1D/99/wKgD3l0wBliATj7QAABPkv1tH4M411.jpg!op_type=3&columns=86&rows=86","followed":false,"id":204383,"nickname":"1380330hukz","vLogoType":-1,"verified":true}
     * commentCounts : 0
     * dislikeReasonNew : {"default":[{"codeType":"DEFAULT","name":"不感兴趣"}]}
     * id : 482851
     * question : 提问11
     */
    public static final String TYPE_ANSWER = "ANSWER";
    public static final String TYPE_ARTICLE = "ARTICLE";
    private long agreeCounts;
    private String answer;
    private AuthorBean author;
    private long commentCounts;
    private DislikeReasonModel dislikeReasonNew;
    private long id;
    private long questionId;
    private String question;
    private String recSrc;
    private String recTrack;

    private String sourceType;
    private List<RelAlbum> relAlbums;
    private AlbumNode albumNode;

    public long getAgreeCounts() {
        return agreeCounts;
    }

    public void setAgreeCounts(int agreeCounts) {
        this.agreeCounts = agreeCounts;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public AuthorBean getAuthor() {
        return author;
    }

    public void setAuthor(AuthorBean author) {
        this.author = author;
    }

    public long getCommentCounts() {
        return commentCounts;
    }

    public void setCommentCounts(int commentCounts) {
        this.commentCounts = commentCounts;
    }

    public DislikeReasonModel getDislikeReasonNew() {
        return dislikeReasonNew;
    }

    public void setDislikeReasonNew(DislikeReasonModel dislikeReasonNew) {
        this.dislikeReasonNew = dislikeReasonNew;
    }

    public List<RelAlbum> getRelAlbums() {
        return relAlbums;
    }

    public void setRelAlbums(List<RelAlbum> relAlbums) {
        this.relAlbums = relAlbums;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(long questionId) {
        this.questionId = questionId;
    }

    public String getRecSrc() {
        return recSrc;
    }

    public void setRecSrc(String recSrc) {
        this.recSrc = recSrc;
    }

    public String getRecTrack() {
        return recTrack;
    }

    public void setRecTrack(String recTrack) {
        this.recTrack = recTrack;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public AlbumNode getAlbumNode() {
        return albumNode;
    }

    public void setAlbumNode(AlbumNode albumNode) {
        this.albumNode = albumNode;
    }

    public static class AuthorBean {
        /**
         * anchorGrade : 1
         * avatar : http://imagev2.test.ximalaya.com/group1/M00/1D/99/wKgD3l0wBliATj7QAABPkv1tH4M411.jpg!op_type=3&columns=86&rows=86
         * followed : false
         * id : 204383
         * nickname : 1380330hukz
         * vLogoType : -1
         * verified : true
         */

        private int anchorGrade;
        private String avatar;
        private boolean followed;
        private int id;
        private String nickname;
        private int vLogoType;
        private boolean verified;

        public int getAnchorGrade() {
            return anchorGrade;
        }

        public void setAnchorGrade(int anchorGrade) {
            this.anchorGrade = anchorGrade;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public boolean isFollowed() {
            return followed;
        }

        public void setFollowed(boolean followed) {
            this.followed = followed;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public int getVLogoType() {
            return vLogoType;
        }

        public void setVLogoType(int vLogoType) {
            this.vLogoType = vLogoType;
        }

        public boolean isVerified() {
            return verified;
        }

        public void setVerified(boolean verified) {
            this.verified = verified;
        }
    }

    public static class AlbumNode {
        private long id;
        private String title;
        private String coverUrl;
        private String nickName;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getCoverUrl() {
            return coverUrl;
        }

        public void setCoverUrl(String coverUrl) {
            this.coverUrl = coverUrl;
        }

        public String getNickName() {
            return nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }
    }
}
