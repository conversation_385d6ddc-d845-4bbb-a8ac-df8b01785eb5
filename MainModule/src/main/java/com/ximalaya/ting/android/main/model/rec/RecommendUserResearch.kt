package com.ximalaya.ting.android.main.model.rec

import org.json.JSONObject

class RecommendUserResearch(jsonObject: JSONObject?) {
    var coverPath: String? = null
    var subtitle: String? = null
    var title: String? = null
    var url: String? = null
    var content: Int = 0

    init {
        if (jsonObject != null) {
            if (jsonObject.has("coverPath")) {
                coverPath = jsonObject.optString("coverPath")
            }
            if (jsonObject.has("subtitle")) {
                subtitle = jsonObject.optString("subtitle")
            }
            if (jsonObject.has("title")) {
                title = jsonObject.optString("title")
            }
            if (jsonObject.has("url")) {
                url = jsonObject.optString("url")
            }
        }
    }
}