package com.ximalaya.ting.android.main.model.soundPatch;

import com.ximalaya.ting.android.host.manager.soundpatch.SoundPatchController;
import com.ximalaya.ting.android.host.manager.soundpatch.SoundPatchHostManager;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.opensdk.constants.SoundPatchConstants;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.soundpatch.BaseSoundPatch;
import com.ximalaya.ting.android.opensdk.model.soundpatch.ImmediateSoundPatch;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;

import org.json.JSONObject;

import java.util.Map;

/**
 * Created by 5Greatest on 2020.07.21
 *
 * <AUTHOR>
 * On 2020-07-21
 */
public class ShareFreeSoundPatch extends ImmediateSoundPatch {

    private long mTrackId = -100;
    private String mUrl;

    private void setPatchInfo(long trackId, String url) {
        this.mTrackId = trackId;
        this.mUrl = url;
    }


    @Override
    public boolean isAbleToBlockLowPriorities(Map<String, Object> requirementParam) {
        if (null == requirementParam) {
            return false;
        }
        return requirementParam.containsKey(SoundPatchConstants.KEY_SELF);
    }

    @Override
    protected boolean play() {
        PlayableModel playableModel = null == XmPlayerService.getPlayerSrvice() ? null : XmPlayerService.getPlayerSrvice().getCurrPlayModel();
        if (null == playableModel || mTrackId != playableModel.getDataId() || StringUtil.isEmpty(mUrl)) {
            return false;
        }
        // XmPlayerManager.getInstance(mContext).pause();
        playUrl(mUrl);
        return true;
    }

    /**
     * 该类贴片的优先级，数字越大，优先级越高
     */
    @Override
    public int getPriority() {
        return SoundPatchController.PRIORITY_SHARE_FREE;
    }

    /**
     * 判断该类贴片是否在使用
     *
     * @return false：停用该类贴片
     */
    @Override
    public boolean isValid() {
        return true;
    }

    /**
     * 判断是否满足播放本贴片的条件（对内接口）
     * <p>
     * 主要负责判断播放周期
     */
    @Override
    protected boolean checkPlayCyclePeriod() {
        // 分享免费结束后就播放，不做频率判断
        return true;
    }

    /**
     * 判断是否满足播放本贴片的条件（对内接口）
     * <p>
     * 主要负责判断配置中心或写在本地的标志位、是否登录等
     */
    @Override
    protected boolean checkPlayConditionExcludePlayCyclePeriod() {
        return true;
    }

    @Override
    public BaseSoundPatch cloneSoundPatch(String paramsJsonString) {
        long trackId = SoundPatchConstants.DEFAULT_LONG;
        String url = null;

        try {
            JSONObject jsonObject = new JSONObject(paramsJsonString);
            trackId = SoundPatchHostManager.Util.decodeTrackId(jsonObject);
            url = SoundPatchHostManager.Util.decodeUrl(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }

        ShareFreeSoundPatch emptySoundPatch = new ShareFreeSoundPatch();
        emptySoundPatch.setPatchInfo(trackId, url);
        return emptySoundPatch;
    }

    @Override
    public void resetOnRemoveSoundPatch() {

    }
}
