package com.ximalaya.ting.android.main.payModule;


import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.other.BaseLoadDialogFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockPaidManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.pay.AutoRechargeABManager;
import com.ximalaya.ting.android.host.manager.pay.ISingleAlbumPayResultListener;
import com.ximalaya.ting.android.host.manager.pay.PayManager;
import com.ximalaya.ting.android.host.model.ad.AdUnLockPayModel;
import com.ximalaya.ting.android.host.model.ad.VideoUnLockResult;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.BatchChooseTracksAdapterNew;
import com.ximalaya.ting.android.main.albumModule.album.singleAlbum.pagePart.AlbumUnLockPaidHintManager;
import com.ximalaya.ting.android.main.constant.MainUrlConstants;
import com.ximalaya.ting.android.main.fragment.dialog.h5.VipFloatPurchaseDialog;
import com.ximalaya.ting.android.main.model.pay.single.SingleAlbumBehaviorModel;
import com.ximalaya.ting.android.main.model.pay.single.SingleAlbumPriceModel;
import com.ximalaya.ting.android.main.model.pay.single.SingleAlbumPromotionModel;
import com.ximalaya.ting.android.main.model.pay.single.SingleAlbumPurchaseChannelsModel;
import com.ximalaya.ting.android.main.model.pay.single.SingleTrackPromotionPriceModel;
import com.ximalaya.ting.android.main.payModule.single.BundleBuyDialogRestoreData;
import com.ximalaya.ting.android.main.payModule.single.DiscountConfirmBuyDialogRestoreData;
import com.ximalaya.ting.android.main.payModule.single.ISingleAlbumPayActionListener;
import com.ximalaya.ting.android.main.payModule.single.ISingleAlbumPayDialogDataManager;
import com.ximalaya.ting.android.main.payModule.single.SingleAlbumBuyRequestUtil;
import com.ximalaya.ting.android.main.payModule.single.SingleAlbumOrderParams;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 单集付费专辑选集购买弹窗
 */
public class BundleBuyDialogFragment1 extends BaseLoadDialogFragment
        implements View.OnClickListener, PayManager.RechargeCallback {
    public static final String TAG = BundleBuyDialogFragment1.class.getSimpleName();

    public static final String ARGS_SELECT_INDEX = "bundleBuy_select_index";

    private static final String TYPE_BUY_NOW = "buy_now";
    private static final String TYPE_INSUFFICIENT = "insufficient";

    //该页面来自哪
    public static final int FLAG_ALBUM_FRAGMENT_NEW = 10;        // 普通专辑页
    public static final int FLAG_PLAY_FRAGMENT = 11;             // 播放页
    public static final int FLAG_OTHER = 12;                     // 其他

    private int mPageFrom = FLAG_ALBUM_FRAGMENT_NEW; // 默认是来自普通专辑页
    private String descOfBuyItem1 = "1.按选集购买的音频节目，购买成功后不可退款";
    private String descOfBuyItem2 = "2.快捷选集购买已自动跳过你无需购买的节目";

    private View vContentContainer;
    private TextView vModuleBuyTrackTitle;
    private TextView vModuleBuySuffix;
    private GridView vGridView;
    private BatchChooseTracksAdapterNew mGridAdapter;
    private BundleBuyGridItemClickListener mItemClickListener;
    private TextView vAutoRecharge;
    private ViewGroup vBuyButtonGroup;
    private TextView vBuyButtonContent;
    private ProgressBar vBuyButtonProgress;
    private CheckBox vAutoBuyCheckBox;
    private View vAutoBuyGroup;
    private View vBuyDescGroup;
    private ImageView vItemDescIcon;
    private TextView vItemDescText1;
    private TextView vItemDescText2;

    private Group mUnLockAdGroup;
    private TextView vUnLockAdTitle;
    private TextView vUnLockAdSubTitle;
    private TextView vUnLockAdBtn;

    private Track mTrack;
    private ISingleAlbumPayActionListener mPayActionListener;
    private ISingleAlbumPayDialogDataManager mDataManager;
    private ISingleAlbumPayResultListener mPayResultListener;

    private SingleTrackPromotionPriceModel mPromotionModel;
    private long mAlbumId;
    private int mSelectedIndex = 0;
    //    private Track mCurrentTrack;
    private int mBuyType; // 1 单声音；2 全专辑
    private String mAlbumActivityParams;
    private int queryTimes = 0;
    private boolean isFirstLoading = true;
    private boolean isAutoBuy = false;

    private boolean fromAdLock = false;
    private boolean isFromAdChooseDialog = false;
    private AdUnLockPayModel mAdData;

    private boolean mContainAutoRecharge = AutoRechargeABManager.getInstance().containsRechargeButton();
    private boolean mIsInSufficient = false;
    private String mAutoRechargeBtnText = AutoRechargeABManager.getInstance().getBtnText();

    private WeakReference<BundleBuyDialogFragment.IAlbumStatusChangedListener> albumStatusChangedListenerWeakReference;
    private AlbumAutoBuySwitchListener mAutoBuyCheckChangeListener;


    public static void show(int pageFrom,
                            @NonNull FragmentManager fragmentManager,
                            @NonNull Track track, int selectItemIndex,
                            boolean fromAdLock,
                            boolean isFromAdChooseDialog,
                            @Nullable String activityTraceParams,
                            @NonNull ISingleAlbumPayActionListener actionListener,
                            @NonNull ISingleAlbumPayDialogDataManager dataManager,
                            @NonNull ISingleAlbumPayResultListener payResultListener,
                            @Nullable BundleBuyDialogFragment.IAlbumStatusChangedListener albumStatusChangedListener) {
        if (fragmentManager.findFragmentByTag(TAG) == null) {
            BundleBuyDialogFragment1 fragment = new BundleBuyDialogFragment1();
            fragment.mPageFrom = pageFrom;
            fragment.mTrack = track;
            fragment.mSelectedIndex = selectItemIndex;
            fragment.fromAdLock = fromAdLock;
            fragment.isFromAdChooseDialog = isFromAdChooseDialog;
            fragment.mAlbumActivityParams = activityTraceParams;
            fragment.mPayActionListener = actionListener;
            fragment.mDataManager = dataManager;
            fragment.mPayResultListener = payResultListener;
            fragment.albumStatusChangedListenerWeakReference = new WeakReference<>(albumStatusChangedListener);
            fragment.show(fragmentManager, TAG);
        }
    }

    public static void dismiss(@NonNull FragmentManager fragmentManager) {
        Fragment fragment = fragmentManager.findFragmentByTag(TAG);
        if (fragment instanceof DialogFragment) {
            ((DialogFragment) fragment).dismiss();
        }
    }

    public static BundleBuyDialogFragment1 newInstance(Context context, Track track, int flag) {
        BundleBuyDialogFragment1 fragment = new BundleBuyDialogFragment1();
        fragment.mTrack = track;
        fragment.mPageFrom = flag;
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        parentNeedBg = false;
        setStyle(DialogFragment.STYLE_NO_TITLE, com.ximalaya.ting.android.host.R.style.host_share_dialog);
        parseBundle();
        PayManager.getInstance().addRechargeCallback(this);
    }

    private void parseBundle() {
        Bundle args = getArguments();
        if (null != args) {
            mSelectedIndex = args.getInt(ARGS_SELECT_INDEX, 0);
            mPageFrom = args.getInt(BundleKeyConstants.KEY_FLAG, FLAG_ALBUM_FRAGMENT_NEW);
            mAlbumActivityParams = args.getString(BundleKeyConstants.KEY_ALBUM_ACTIVITY_PARAMS);
        }

        new XMTraceApi.Trace()
                .setMetaId(18359)
                .setServiceId("dialogView")
                .put("albumId", getAlbumId() + "")
                .put("trackId", getTrackId() + "")
                .put("currPage", mPageFrom == FLAG_ALBUM_FRAGMENT_NEW ? "album" : "newPlay")
                .put("srcChannel", AdUnLockPaidManager.getUnLockPaidPageSource(getAlbumId()))
                .createTrace();
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_bundle_buy_dialog_2;
    }

    @Override
    protected void initUi(View view, Bundle savedInstanceState) {
        vContentContainer = view.findViewById(R.id.main_ll_content_container);
        vModuleBuyTrackTitle = view.findViewById(R.id.main_bundle_buy_track_title);
        vModuleBuySuffix = view.findViewById(R.id.main_bundle_buy_start_buy);

        vGridView = view.findViewById(R.id.main_gv_bundle_track);
        vBuyButtonGroup = view.findViewById(R.id.main_btn_buy_layout);
        vBuyButtonContent = view.findViewById(R.id.main_tv_buy_now);
        vBuyButtonProgress = view.findViewById(R.id.main_pb_buy_loading);
        vAutoBuyCheckBox = view.findViewById(R.id.main_bundle_buy_album_auto_buy_switch);
        vAutoBuyGroup = view.findViewById(R.id.main_auto_buy_root);
        vItemDescIcon = view.findViewById(R.id.main_bundle_buy_desc_icon);
        vItemDescText1 = view.findViewById(R.id.main_tv_tips1);
        vItemDescText2 = view.findViewById(R.id.main_tv_tips2);

        vBuyDescGroup = view.findViewById(R.id.main_bundle_buy_desc_group);
        mUnLockAdGroup = view.findViewById(R.id.main_unlock_ad_group);

        View unlockBtb = view.findViewById(R.id.main_bundle_buy_track_free_unlock_lay);
        unlockBtb.setOnClickListener(this);
        AutoTraceHelper.bindData(unlockBtb, "");

        vUnLockAdTitle = view.findViewById(R.id.main_unlock_ad_title);
        vUnLockAdSubTitle = view.findViewById(R.id.main_unlock_ad_subtitle);
        vUnLockAdBtn = view.findViewById(R.id.main_unlock_ad_btn);

        mGridAdapter = new BatchChooseTracksAdapterNew(getContext(), null);
        vGridView.setAdapter(mGridAdapter);

        vBuyButtonGroup.setEnabled(false);
        vBuyButtonGroup.setOnClickListener(this);
        vItemDescIcon.setOnClickListener(this);
        vGridView.setOnItemClickListener(mItemClickListener = new BundleBuyGridItemClickListener());
        AutoTraceHelper.bindData(vBuyButtonGroup, "");

        if (mContainAutoRecharge) {
            vAutoRecharge = view.findViewById(R.id.main_btn_auto_recharge);
            vAutoRecharge.setOnClickListener(this);
            vAutoRecharge.setText(mAutoRechargeBtnText);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        if (null != window) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.gravity = Gravity.BOTTOM;
            params.windowAnimations = com.ximalaya.ting.android.host.R.style.host_popup_window_from_bottom_animation;
            window.setAttributes(params);
        }
    }

    @Override
    public void loadData() {
        requestNetData(this);
    }

    private void requestNetData(BundleBuyDialogFragment1 fra) {
        final WeakReference<BundleBuyDialogFragment1> reference = new WeakReference<>(fra);
        if (canUpdateUi() && fra.isFirstLoading) {
            onPageLoadingCompleted(LoadCompleteType.LOADING);
        }
        Map<String, String> params = new HashMap<>();
        params.put("currentTrackId", String.valueOf(getTrackId()));

        long albumId = 0;
        if (mTrack != null && mTrack.getAlbum() != null) {
            albumId = mTrack.getAlbum().getAlbumId();
        }
        MainCommonRequest.getSingleAlbumPromotionMultiPrice(albumId, getTrackId(), getSource(), new IDataCallBack<SingleTrackPromotionPriceModel>() {
            @Override
            public void onSuccess(@Nullable SingleTrackPromotionPriceModel object) {
                BundleBuyDialogFragment1 realFra2 = null;
                if (reference != null) {
                    realFra2 = reference.get();
                }
                if (realFra2 == null)
                    return;

                if (realFra2.canUpdateUi()) {
                    if (null == object && realFra2.isFirstLoading) {
                        realFra2.onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                    } else if (null != object) {
                        if (mTrack != null && mTrack.getAlbum() != null) {
                            object.setAlbumTitle(mTrack.getAlbum().getAlbumTitle());
                        }
                        realFra2.mPromotionModel = object;
                        realFra2.mAlbumId = object.getAlbumId();

                        if (fromAdLock) {

                            BundleBuyDialogFragment1 finalRealFra = realFra2;
                            AlbumUnLockPaidHintManager.requestPayDialogHint(object.getAlbumId(),
                                    new AlbumUnLockPaidHintManager.IPayDialogDataCallBack() {
                                        @Override
                                        public void onDataBack(AdUnLockPayModel dataBean) {
                                            if (canUpdateUi()) {
                                                mAdData = dataBean;

                                                finalRealFra.setDataForView();
                                                onPageLoadingCompleted(LoadCompleteType.OK);
                                                finalRealFra.vContentContainer.setVisibility(View.VISIBLE);
                                            }
                                        }
                                    });

                        } else {
                            realFra2.setDataForView();
                            onPageLoadingCompleted(LoadCompleteType.OK);
                            realFra2.vContentContainer.setVisibility(View.VISIBLE);
                        }
                    }
                    realFra2.isFirstLoading = false;
                }
            }

            @Override
            public void onError(int code, String message) {
                BundleBuyDialogFragment1 realFra3 = null;
                if (reference != null) {
                    realFra3 = reference.get();
                }
                if (realFra3 == null) return;

                if (realFra3.canUpdateUi()) {
                    CustomToast.showFailToast(message);
                    realFra3.onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                    dismiss();
                }
                if (mPayResultListener != null && (code == 10203 || code == 514)) {
                    mPayResultListener.trackPaySuccess(getTrackId(), false);
                }
            }
        });
    }

    private String getSource() {
        if (mPageFrom == FLAG_ALBUM_FRAGMENT_NEW) {
            return "album";
        } else if (mPageFrom == FLAG_PLAY_FRAGMENT) {
            return "play";
        }
        return "album";
    }

    private void setDataForView() {
        if (mPromotionModel == null || ToolUtil.isEmptyCollects(mPromotionModel.getPurchaseChannelBos())) {
            return;
        }
        //整理数据---开始
        List<SingleAlbumPurchaseChannelsModel> list = mPromotionModel.getPurchaseChannelBos();
        if (!UserInfoMannage.isVipUser() && mPromotionModel.isVipFreeAlbum()) {
            //会员畅听的单集购买弹层不出现「自己选」的入口
            Iterator<SingleAlbumPurchaseChannelsModel> iterator = list.iterator();
            while (iterator.hasNext()) {
                SingleAlbumPurchaseChannelsModel tracksModel = iterator.next();
                SingleAlbumBehaviorModel behaviorModel = tracksModel.getBehavior();
                if (behaviorModel != null && SingleAlbumBehaviorModel.TRACK_TYPE_PURCHASE_CHOOSE_BY_SELF.equals(behaviorModel.getTrackBuyType())) {
                    iterator.remove();
                }
            }

            if (!isFromAdChooseDialog) {
                SingleAlbumPurchaseChannelsModel channelsModel = SingleAlbumPurchaseChannelsModel.createVipModel(fromAdLock);
                list.add(0, channelsModel);
            }
        }
        // 加入ximi畅听全集
        if (!mPromotionModel.isXimiUser() && mPromotionModel.isXimiFreeAlbum()) {
            SingleAlbumPurchaseChannelsModel channelsModel = SingleAlbumPurchaseChannelsModel.createXimiModel();
            list.add(0, channelsModel);
        }
        //已更未购，将非vip的部分数据存到vip里，方便传递
        int leftAllNotvipIndex = -1;
        int leftAllVipIndex = -1;
        double noVipDiscountRate = 0;
        String discountDesc = "";
        boolean hasLeftAllVip = false;
        for (int i = 0; i < list.size(); i++) {
            SingleAlbumPurchaseChannelsModel purchaseChannelsModel = list.get(i);
            SingleAlbumBehaviorModel behaviorModel = purchaseChannelsModel.getBehavior();
            SingleAlbumPriceModel priceModel = purchaseChannelsModel.getPrice();
            if (behaviorModel != null && priceModel != null) {
                if (SingleAlbumBehaviorModel.TRACK_TYPE_LEFT_ALL.equals(behaviorModel.getTrackBuyType())) {
                    if (priceModel.isVipType()) {
                        hasLeftAllVip = true;
                        leftAllVipIndex = i;
                    } else {
                        leftAllNotvipIndex = i;
                        noVipDiscountRate = priceModel.getDiscountRate();
                        discountDesc = priceModel.getDiscountDesc();
                    }
                }
            }
        }
        if (hasLeftAllVip && leftAllNotvipIndex >= 0) {
            if (leftAllVipIndex >= 0 && leftAllVipIndex < list.size()) {
                SingleAlbumPurchaseChannelsModel purchaseChannelsModel = list.get(leftAllVipIndex);
                SingleAlbumPriceModel priceModel = purchaseChannelsModel.getPrice();
                if (priceModel != null) {
                    priceModel.setNoVipDiscountRate(noVipDiscountRate);
                    if (!mPromotionModel.isVipUser() && priceModel.getPromotionModel(SingleAlbumPromotionModel.TYPE_TIMED_DISCOUNT) != null) { //有限时折扣优先展示限时折扣
                        priceModel.setDiscountDesc(discountDesc);
                    }
                }
            }
            list.remove(leftAllNotvipIndex);
        }
        //整理数据---结束

        // 来自广告不默认选中第一个
        SingleAlbumPurchaseChannelsModel firstTracksModel;
        if (mSelectedIndex >= 0 && mSelectedIndex < list.size()) {
            firstTracksModel = list.get(mSelectedIndex);
        } else {
            firstTracksModel = list.get(0);
        }
        if(!fromAdLock  || mAdData == null) {
            firstTracksModel.setSelect(true);
            mItemClickListener.onItemClick(firstTracksModel, false);
        } else {
            String trackTitle = getTrackTitle();
            SpannableStringBuilder ssb = new SpannableStringBuilder(String.format("从 %s",
                    trackTitle));
            ssb.setSpan(new ForegroundColorSpan(Color.parseColor("#FA653A")), 2,
                    2 + trackTitle.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            vModuleBuyTrackTitle.setText(ssb);
            vModuleBuySuffix.setText(" 开始购买");
        }

        vAutoBuyCheckBox.setOnCheckedChangeListener(null);
        vAutoBuyCheckBox.setChecked(mPromotionModel.isAutoBuy());
        mAutoBuyCheckChangeListener = new AlbumAutoBuySwitchListener(false, getFragmentManager(),
                mAlbumId, new BundleBuyDialogFragment.IAlbumStatusChangedListener() {
            @Override
            public void onAlbumAutoBuyStatusChanged(boolean isAutoBuy) {
                new XMTraceApi.Trace()
                        .setMetaId(21055)
                        .setServiceId("dialogClick")
                        .put("dialogType", "selectOneAlbum")
                        .put("currPage", mPageFrom == FLAG_ALBUM_FRAGMENT_NEW ? "album" : "newPlay")
                        .put("albumId", getAlbumId() + "")
                        .createTrace();
                if (albumStatusChangedListenerWeakReference != null &&
                        albumStatusChangedListenerWeakReference.get() != null) {
                    albumStatusChangedListenerWeakReference.get().onAlbumAutoBuyStatusChanged(isAutoBuy);
                }

                if (canUpdateUi() && vAutoBuyCheckBox != null) {
                    vAutoBuyCheckBox.setOnCheckedChangeListener(null);
                    vAutoBuyCheckBox.setChecked(isAutoBuy);
                    vAutoBuyCheckBox.setOnCheckedChangeListener(mAutoBuyCheckChangeListener);
                }

                if (canUpdateUi()
                        && mContainAutoRecharge
                        && null != mPromotionModel
                        && isAutoBuy
                        && !mPromotionModel.isAutoPay()
                        && mIsInSufficient) {
                    ViewStatusUtil.setVisible(View.VISIBLE, vAutoRecharge);
                    if (null != vBuyButtonGroup) {
                        vBuyButtonGroup.setBackgroundResource(R.drawable.main_bg_stroke_f86442_corner_22);
                    }
                    if (null != vBuyButtonContent) {
                        vBuyButtonContent.setTextColor(Color.parseColor("#ff4444"));
                    }
                } else {
                    ViewStatusUtil.setVisible(View.GONE, vAutoRecharge);
                    if (null != vBuyButtonGroup) {
                        vBuyButtonGroup.setBackgroundResource(R.drawable.main_bg_bundle_buy_selector);
                    }
                    if (null != vBuyButtonContent) {
                        vBuyButtonContent.setTextColor(Color.parseColor("#FFFFFF"));
                    }
                }
            }
        });
        vAutoBuyCheckBox.setOnCheckedChangeListener(mAutoBuyCheckChangeListener);

        if (mAdData != null) {
            hasAdTipViewStyle();
        }

        mGridAdapter.setIsVipUser(mPromotionModel.isVipUser());
        mGridAdapter.setListData(list);
        mGridAdapter.notifyDataSetChanged();
    }

    private void hasAdTipViewStyle() {
        if (mAdData == null) {
            return;
        }

        // 隐藏自动购买
        if (vAutoBuyGroup != null) {
            vAutoBuyGroup.setVisibility(View.GONE);
        }

        // 隐藏立即购买按钮
        if (vBuyButtonGroup != null) {
            vBuyButtonGroup.setVisibility(View.GONE);
        }

        // 隐藏购买提示
        if (vBuyDescGroup != null) {
            vBuyDescGroup.setVisibility(View.GONE);
        }

        // 将购买提示弹窗向左对齐
        if (vModuleBuyTrackTitle != null) {
            ViewGroup.LayoutParams layoutParams = vModuleBuyTrackTitle.getLayoutParams();
            if (layoutParams instanceof ConstraintLayout.LayoutParams) {
                ((ConstraintLayout.LayoutParams) layoutParams).horizontalBias = 0;
                vModuleBuyTrackTitle.setLayoutParams(layoutParams);
            }
        }

        if (vUnLockAdBtn != null && !TextUtils.isEmpty(mAdData.getGuideCopy())) {
            vUnLockAdBtn.setText(mAdData.getGuideCopy());
            AdUnLockPaidManager.scaleAnimationView(vUnLockAdBtn);
        }

        if (vUnLockAdTitle != null && !TextUtils.isEmpty(mAdData.getMainCopy())) {
            vUnLockAdTitle.setText(mAdData.getMainCopy());
        }

        if (vUnLockAdSubTitle != null && !TextUtils.isEmpty(mAdData.getSecondCopy())) {
            vUnLockAdSubTitle.setText(mAdData.getSecondCopy());
        }

        if (mUnLockAdGroup != null) {
            mUnLockAdGroup.setVisibility(View.VISIBLE);
        }

        if(vGridView != null) {
            ViewGroup.LayoutParams layoutParams = vGridView.getLayoutParams();
            ViewUtil.onlySetViewPaddingOne(vGridView, BaseUtil.dp2px(getContext(), 5),
                    ViewUtil.PADDING_BOTTOM);
            if (layoutParams instanceof ConstraintLayout.MarginLayoutParams) {
                ((ViewGroup.MarginLayoutParams) layoutParams).topMargin =
                        BaseUtil.dp2px(getContext(), 5);
            }
        }
    }

    private void setPageTitle(SingleAlbumBehaviorModel model) {
        if (model == null)
            return;

        if (SingleAlbumBehaviorModel.TRACK_TYPE_VIP.equals(model.getTrackBuyType())) {
            SpannableStringBuilder ssb = new SpannableStringBuilder("全集");
            ssb.setSpan(new ForegroundColorSpan(Color.parseColor("#FA653A")), 0, 2,
                    Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            vModuleBuyTrackTitle.setText(ssb);
            vModuleBuySuffix.setText(" 购买");
        } else {
            String trackTitle = getTrackTitle();
            SpannableStringBuilder ssb = new SpannableStringBuilder(String.format("从 %s",
                    trackTitle));
            ssb.setSpan(new ForegroundColorSpan(Color.parseColor("#FA653A")), 2,
                    2 + trackTitle.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
            vModuleBuyTrackTitle.setText(ssb);
            vModuleBuySuffix.setText(" 开始购买");
        }
    }

    private void setAutoBuyViews(SingleAlbumBehaviorModel model) {
        if (model == null)
            return;

        // 如果是来自广告视频解锁 ,是否出现自动购买不再处理
        if (mAdData != null) {
            return;
        }

        if (SingleAlbumBehaviorModel.TRACK_TYPE_VIP.equals(model.getTrackBuyType())) {
            vAutoBuyGroup.setVisibility(View.GONE);
        } else {
            vAutoBuyGroup.setVisibility(View.VISIBLE);
        }
    }

    private void setBuyButton(SingleAlbumPurchaseChannelsModel channelsModel) {
        if (channelsModel == null || mPromotionModel == null)
            return;

        SingleAlbumBehaviorModel behaviorModel = channelsModel.getBehavior();
        SingleAlbumPriceModel priceModel = channelsModel.getPrice();
        if (behaviorModel == null) {
            return;
        }

        // 恢复按钮样式
        if (null != vBuyButtonGroup) {
            vBuyButtonGroup.setBackgroundResource(R.drawable.main_bg_bundle_buy_selector);
        }
        if (null != vBuyButtonContent) {
            Context context = getContext();
            if (null == context) {
                context = BaseApplication.getMyApplicationContext();
            }
            vBuyButtonContent.setTextColor(context.getResources().getColor(R.color.main_white));
        }
        ViewStatusUtil.setVisible(View.GONE, vAutoRecharge);

        mIsInSufficient = false;
        if (SingleAlbumBehaviorModel.TRACK_TYPE_XIMI.equals(behaviorModel.getTrackBuyType())
                && mPromotionModel != null && mPromotionModel.getXimiGuideButtonMessageVo() != null) {
            vBuyButtonContent.setText(mPromotionModel.getXimiGuideButtonMessageVo().getButtonMessage());
            vBuyButtonGroup.setEnabled(true);
        } else if (SingleAlbumBehaviorModel.TRACK_TYPE_VIP.equals(behaviorModel.getTrackBuyType())
                && mPromotionModel != null && mPromotionModel.getVipGuideButtonMessageVo() != null) {
            vBuyButtonContent.setText(mPromotionModel.getVipGuideButtonMessageVo().getButtonMessage());
            vBuyButtonGroup.setTag(R.id.main_single_album_buy_button_type, TYPE_BUY_NOW);
            vBuyButtonGroup.setEnabled(true);
        } else if (priceModel != null && priceModel.getPromotionModel(SingleAlbumPromotionModel.TYPE_VIP_DISCOUNT) != null
                && !UserInfoMannage.isVipUser()) {
            vBuyButtonGroup.setEnabled(true);
            vBuyButtonContent.setText("立即购买");
        } else {
            if (priceModel != null && mPromotionModel.getBalanceAmount() < priceModel.getPayPrice(mPromotionModel.isVipUser())) {
                mIsInSufficient = true;
                if (mContainAutoRecharge
                        && mPromotionModel.isAutoBuy()
                        && !mPromotionModel.isAutoPay()) {
                    // 已开通自动购买  且  未开通自动充值才显示
                    ViewStatusUtil.setVisible(View.VISIBLE, vAutoRecharge);
                    vBuyButtonGroup.setEnabled(true);
                    vBuyButtonGroup.setBackgroundResource(R.drawable.main_bg_stroke_f86442_corner_22);
                    vBuyButtonContent.setTextColor(getContext().getResources().getColor(R.color.main_color_f96442));
                    vBuyButtonContent.setText("余额不足，先去充值");
                    vBuyButtonGroup.setTag(R.id.main_single_album_buy_button_type, TYPE_INSUFFICIENT);
                } else {
                    vBuyButtonGroup.setEnabled(true);
                    vBuyButtonContent.setText("余额不足，先去充值");
                    vBuyButtonGroup.setTag(R.id.main_single_album_buy_button_type, TYPE_INSUFFICIENT);
                }
            } else {
                vBuyButtonGroup.setEnabled(true);
                vBuyButtonContent.setText("立即购买");
                vBuyButtonGroup.setTag(R.id.main_single_album_buy_button_type, TYPE_BUY_NOW);
            }
        }
    }

    private void setDescriptionViews(SingleAlbumBehaviorModel model) {
        if (model == null)
            return;

        // 如果是来自广告视频解锁 ,是否出现详情不再处理
        if (mAdData != null) {
            return;
        }

        if (SingleAlbumBehaviorModel.TRACK_TYPE_VIP.equals(model.getTrackBuyType())) {
            if (mPromotionModel != null && mPromotionModel.getVipRightsExplanationVo() != null && mPromotionModel.getVipGuideButtonMessageVo() != null) {
                vItemDescText1.setText(mPromotionModel.getVipRightsExplanationVo().getTitle());
                vItemDescText2.setText(mPromotionModel.getVipRightsExplanationVo().getDescription());
                vItemDescIcon.setVisibility(View.VISIBLE);
            } else {
                vItemDescIcon.setVisibility(View.GONE);
                vItemDescText1.setText(descOfBuyItem1);
                vItemDescText2.setText(descOfBuyItem2);
            }
        } else {
            vItemDescIcon.setVisibility(View.GONE);
            vItemDescText1.setText(descOfBuyItem1);
            vItemDescText2.setText(descOfBuyItem2);
        }
    }

    private SingleAlbumPurchaseChannelsModel getSelectedItem() {
        SingleAlbumPurchaseChannelsModel result = null;
        if (mGridAdapter != null) {
            result = mGridAdapter.getSelectedModel();
        }
        return result;
    }

    @Override
    public void onClick(View v) {
        if (OneClickHelper.getInstance().onClick(v)) {
            int id = v.getId();
            if (id == R.id.main_btn_buy_layout) {
                Object tagValue = v.getTag(R.id.main_single_album_buy_button_type);
                if (tagValue instanceof String) {
                    String type = (String) tagValue;
                    String item = vBuyButtonContent != null ? vBuyButtonContent.getText().toString() : "";
                    if (TYPE_BUY_NOW.equals(type)) {
                        new XMTraceApi.Trace()
                                .setMetaId(21057)
                                .setServiceId("dialogClick")
                                .put("dialogType", "selectOneAlbum")
                                .put("Item", item) // 两端上报保持一致
                                .put("currPage", mPageFrom == FLAG_ALBUM_FRAGMENT_NEW ? "album" : "newPlay")
                                .put("albumId", getAlbumId() + "")
                                .createTrace();
                    } else if (TYPE_INSUFFICIENT.equals(type)) {
                        new XMTraceApi.Trace()
                                .setMetaId(21056)
                                .setServiceId("dialogClick")
                                .put("dialogType", "selectOneAlbum")
                                .put("Item", item) // 两端上报保持一致
                                .put("currPage", mPageFrom == FLAG_ALBUM_FRAGMENT_NEW ? "album" : "newPlay")
                                .put("albumId", getAlbumId() + "")
                                .createTrace();
                    }
                }
                onBuyAction(false);
            } else if (id == R.id.main_bundle_buy_desc_icon) {
                if (mPromotionModel != null && mPromotionModel.getVipRightsExplanationVo() != null) {
                    storeData();
                    mPayActionListener.actionWebPage(mPromotionModel.getVipRightsExplanationVo().getVipRightsExplanationUrl());
                    dismiss();
                }
            } else if (id == R.id.main_bundle_buy_track_free_unlock_lay) {    // 看广告解锁付费内容
                if (mAdData == null) {
                    return;
                }

                long trackId = getTrackId();

                new XMTraceApi.Trace()
                        .setMetaId(18360)
                        .setServiceId("dialogClick")
                        .put("Item", vUnLockAdBtn != null ? (vUnLockAdBtn.getText() != null ? vUnLockAdBtn.getText().toString() : null) : null)
                        .put("albumId", mAlbumId + "")
                        .put("trackId", trackId + "")
                        .put("srcChannel", AdUnLockPaidManager.getUnLockPaidPageSource(mAlbumId))
                        .createTrace();

                AdUnLockPaidManager.unlockPaid(mActivity, mAdData, mAlbumId, trackId,
                        new AdUnLockPaidManager.IAdUnLockDataCallBackHasDialogToOtherPage<VideoUnLockResult>() {
                            @Override
                            public void onGotoOtherPage() {
                                dismiss();
                            }

                            @Override
                            public void onSuccess(@NonNull VideoUnLockResult object) {
                                if(isVisible() && mPayResultListener != null) {
                                    dismiss();
                                    mPayResultListener.unLockTrackSuccess(trackId, object);
                                }
                            }

                            @Override
                            public void onError() {

                            }
                        });
            } else if (R.id.main_btn_auto_recharge == id) {
                new XMTraceApi.Trace()
                        .setMetaId(21055)
                        .setServiceId("dialogClick")
                        .put("dialogType", "selectOneAlbum")
                        .put("currPage", mPageFrom == FLAG_ALBUM_FRAGMENT_NEW ? "album" : "newPlay")
                        .put("albumId", getAlbumId() + "")
                        .createTrace();
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(getContext());
                    return;
                }
                String idInfo = "?type=track&id=" + (null == mTrack ? 0 : mTrack.getDataId());
                BaseFragment fragment = NativeHybridFragment.newInstance(MainUrlConstants.getInstanse().getGoToSignAutoChargeUrl() + idInfo, true);
                Activity activity = BaseApplication.getTopActivity();
                if (activity instanceof MainActivity) {
                    ((MainActivity) activity).startFragment(fragment);
                }
                dismiss();
            }
        }
    }

    private void logBuyButtonClick(SingleAlbumPurchaseChannelsModel model) {
        if (model == null || mPromotionModel == null) {
            return;
        }
        SingleAlbumBehaviorModel behaviorModel = model.getBehavior();
        SingleAlbumPriceModel priceModel = model.getPrice();
        if (behaviorModel != null && priceModel != null && mPromotionModel != null) {
            boolean isRecharge = mPromotionModel.getBalanceAmount() < priceModel.getPayPrice(mPromotionModel.isVipUser());
            String discountType = "";
            if (SingleAlbumBehaviorModel.TRACK_TYPE_LEFT_ALL.equals(behaviorModel.getTrackBuyType())) {
                discountType = DiscountTypeUtil.getDiscountType(priceModel);
            }
            new UserTracking().setAlbumId(mAlbumId).setSrcModule("选集购买浮层").setItem("button")
                    .setItemId(isRecharge ? "充值" : "立即购买").setID("5891").setPurchaseContent(behaviorModel.getTrackIdDesc())
                    .setDiscountType(discountType)
                    .setViewStyle(fromAdLock ? 1 : 0)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
        }
    }

    private void logPerformBuy() {
        if (mPageFrom == FLAG_ALBUM_FRAGMENT_NEW) {
            new UserTracking()
                    .setAlbumId(mAlbumId)
                    .setSrcModule("选集购买浮层")
                    .setItem("button")
                    .setItemId("立即购买")
                    .setViewStyle(fromAdLock ? 1 : 0)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT,
                            XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
        } else if (mPageFrom == FLAG_PLAY_FRAGMENT) {
            new UserTracking()
                    .setTrackId(getTrackId())
                    .setSrcModule("选集购买浮层")
                    .setItem("button")
                    .setItemId("立即购买")
                    .setViewStyle(fromAdLock ? 1 : 0)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT,
                            XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
        }
    }


    private final int BUYING = 1;
    private final int BUY_SUCCESS = 2;
    private final int BUY_FAILED = 3;

    private void setBuyButtonStatus(int buyStatus) {
        if (canUpdateUi()) {
            if (buyStatus == BUYING) {
                vBuyButtonGroup.setEnabled(false);
                vBuyButtonProgress.setVisibility(View.VISIBLE);
                vBuyButtonContent.setText("正在购买中");
            } else if (buyStatus == BUY_SUCCESS) {
                vBuyButtonProgress.setVisibility(View.GONE);
                vBuyButtonContent.setText("购买完成");
            } else if (buyStatus == BUY_FAILED) {
                vBuyButtonGroup.setEnabled(true);
                vBuyButtonProgress.setVisibility(View.GONE);
                vBuyButtonContent.setText(R.string.main_buy_now);
            }
        }
    }

    /**
     * @param fromAdLock 如果是从看视频解锁广告过来的,不管是单集还是多集的就直接跳转到下个页面
     */
    private void onBuyAction(boolean fromAdLock) {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(getActivity());
            return;
        }
        if (mPromotionModel == null) {
            return;
        }
        SingleAlbumPurchaseChannelsModel selectedItem = getSelectedItem();
        if (selectedItem == null)
            return;
        logBuyButtonClick(selectedItem);

        SingleAlbumBehaviorModel behaviorModel = selectedItem.getBehavior();
        // 购买ximi
        if (behaviorModel != null && SingleAlbumBehaviorModel.TRACK_TYPE_XIMI.equals(behaviorModel.getTrackBuyType())) {
            if (mPromotionModel.getXimiGuideButtonMessageVo() != null) {
                String url = mPromotionModel.getXimiGuideButtonMessageVo().getVipUrl();
                storeData();
                mPayActionListener.actionWebPage(url);
                dismiss();
            }
            return;
        }
        // 购买vip
        if (behaviorModel != null && SingleAlbumBehaviorModel.TRACK_TYPE_VIP.equals(behaviorModel.getTrackBuyType())) {
            if (mPromotionModel != null) {
                String url = mPromotionModel.getVipDiscountUrl();
                VipFloatPurchaseDialog.VipDialogMaterial material = new VipFloatPurchaseDialog.VipDialogMaterial(url, null);
                material.setIds(mAlbumId, getTrackId());
                storeData();
                VipFloatPurchaseDialog.show(getActivity(), material);
                dismiss();
            }
            return;
        }

        SingleAlbumPriceModel priceModel = selectedItem.getPrice();
        if (priceModel == null) {
            return;
        }
        if (!UserInfoMannage.isVipUser()
                && priceModel.getPromotionModel(SingleAlbumPromotionModel.TYPE_VIP_DISCOUNT) != null) {
            showConfirmBuyDialog(true, fromAdLock);
            storeData();
            dismiss();
            return;
        }

        // 从广告解锁过来的,不进行价格判断,到下个页面再判断
        if (mPromotionModel.getBalanceAmount() < priceModel.getPayPrice(mPromotionModel.isVipUser()) && !fromAdLock) {
            double rechargeAmount = priceModel.getPayPrice(mPromotionModel.isVipUser()) - mPromotionModel.getBalanceAmount();
            mPayActionListener.actionRecharge(rechargeAmount);
            storeData();
            dismiss();
            vBuyButtonGroup.setTag(R.id.main_recharge, true);
            return;
        }

        logPerformBuy();
        if (mBuyType == PayManager.TYPE_BUY_SINGLE) { // 点击购买直接买
            if (fromAdLock) {    // 来自广告解锁的需要直接再进入下个页面
                showConfirmBuyDialog(false, fromAdLock);
                storeData();
                dismiss();
            } else {
                buy(selectedItem);
            }
        } else if (mBuyType == PayManager.TYPE_BUY_BATCH) { // 多集购买条确认页
            showConfirmBuyDialog(true, fromAdLock);
            storeData();
            dismiss();
        } else if (mBuyType == PayManager.TYPE_BUY_PROMOTION) { //已更未购确认页
            showConfirmBuyDialog(true, fromAdLock);
            storeData();
            dismiss();
        }
    }

    private void showConfirmBuyDialog(boolean isPromotion, boolean fromAdLock) {
        FragmentManager fragmentManager = getFragmentManager();
        if (fragmentManager == null)
            return;
        if (fragmentManager.findFragmentByTag(DiscountConfirmBuyDialogFragment.TAG) != null)
            return;
        // 来自广告解锁不进行此校验
        if (mBuyType != PayManager.TYPE_BUY_BATCH && mBuyType != PayManager.TYPE_BUY_PROMOTION && !fromAdLock)
            return;
        SingleAlbumPurchaseChannelsModel selectItem = mGridAdapter.getSelectedModel();
        if (selectItem == null)
            return;
        PageData pageData = PageData.convert(mPromotionModel, selectItem);
        if (pageData == null)
            return;
        pageData.trackId = getTrackId();
        pageData.buyType = mBuyType;
        pageData.mFlag = mPageFrom;
        pageData.albumActivityParams = mAlbumActivityParams;

        int discountConfirmBuyDialogFragmentType = isPromotion ?
                DiscountConfirmBuyDialogFragment.TYPE_PROMOTION :
                DiscountConfirmBuyDialogFragment.TYPE_BATCH;
        DiscountConfirmBuyDialogRestoreData restoreData = new DiscountConfirmBuyDialogRestoreData();
        restoreData.setType(discountConfirmBuyDialogFragmentType);
        restoreData.setPageData(pageData);
        restoreData.setHasBack(true);
        restoreData.setFromAdLock(fromAdLock);
        restoreData.setAlbumActivityParam(mAlbumActivityParams);
        mPayActionListener.actionDiscountConfirmDialog(restoreData);
    }

    private long getTrackId() {
        if (mTrack != null)
            return mTrack.getDataId();
        return 0;
    }

    private long getAlbumId() {
        if (mAlbumId > 0) {
            return mAlbumId;
        } else if (mTrack != null && mTrack.getAlbum() != null) {
            return mTrack.getAlbum().getAlbumId();
        }
        return 0;
    }

    private String getTrackTitle() {
        if (mTrack != null)
            return mTrack.getTrackTitle();
        return "";
    }

    /**
     * 购买
     */
    private void buy(@NonNull final SingleAlbumPurchaseChannelsModel buyTracksModel) {
        SingleAlbumOrderParams orderParams = new SingleAlbumOrderParams();
        orderParams.setBuyType(mBuyType);
        SingleAlbumBehaviorModel behaviorModel = buyTracksModel.getBehavior();
        if (mBuyType == PayManager.TYPE_BUY_SINGLE || mBuyType == PayManager.TYPE_BUY_BATCH) {
            List<Long> trackIdList = null;
            if (behaviorModel != null) {
                trackIdList = behaviorModel.getTrackIds();
            }
            if (!ToolUtil.isEmptyCollects(trackIdList)) {
                Long[] trackIdArray = trackIdList.toArray(new Long[trackIdList.size()]);
                orderParams.setTrackIdArray(trackIdArray);
            }
            orderParams.setAutoBuy(isAutoBuy);

            orderParams.setPayAmount(buyTracksModel.getPayPrice(UserInfoMannage.isVipUser()));

            orderParams.setPromotions(buyTracksModel.getPromotion());
        }
        orderParams.setAlbumId(mAlbumId);
        orderParams.setContextAudioBookOrder(behaviorModel == null ? "" : behaviorModel.getName());
        orderParams.setContextAlbumActivityParam(mAlbumActivityParams);
        setBuyButtonStatus(BUYING);
        SingleAlbumBuyRequestUtil.requestBuyTrack(getContext(), orderParams,
                new ISingleAlbumPayResultListener() {
                    @Override
                    public void albumPaySuccess(long albumId, boolean isAlbumAutoSubscribed) {
                        setBuyButtonStatus(BUY_SUCCESS);
                        dismissAllowingStateLoss();
                        mPayResultListener.albumPaySuccess(albumId, false);
                    }

                    @Override
                    public void unLockTrackSuccess(long trackId, VideoUnLockResult result) {
                        dismissAllowingStateLoss();
                        mPayResultListener.unLockTrackSuccess(trackId, result);
                    }

                    @Override
                    public void trackPaySuccess(long trackId, boolean isAlbumAutoSubscribed) {
                        setBuyButtonStatus(BUY_SUCCESS);
                        dismissAllowingStateLoss();
                        mPayResultListener.trackPaySuccess(trackId, false);
                    }

                    @Override
                    public void tracksPaySuccess(Long[] trackIds, boolean isAlbumAutoSubscribed) {
                        setBuyButtonStatus(BUY_SUCCESS);
                        dismissAllowingStateLoss();
                        mPayResultListener.tracksPaySuccess(trackIds, false);
                    }

                    @Override
                    public void payFailed(String msg) {
                        setBuyButtonStatus(BUY_FAILED);
                        dismissAllowingStateLoss();
                        mPayResultListener.payFailed(msg);
                    }
                });
    }

    private void processBuryingPoint(String itemId) {
        if (mPageFrom == FLAG_ALBUM_FRAGMENT_NEW) {
            new UserTracking()
                    .setAlbumId(mAlbumId)
                    .setSrcModule("选集购买")
                    .setItem("button")
                    .setItemId(itemId)
                    .setViewStyle(fromAdLock ? 1 : 0)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT,
                            XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
        } else if (mPageFrom == FLAG_PLAY_FRAGMENT) {
            new UserTracking()
                    .setTrackId(getTrackId())
                    .setSrcModule("选集购买浮层")
                    .setItem("button")
                    .setItemId(itemId)
                    .setViewStyle(fromAdLock ? 1 : 0)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT,
                            XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
        }
    }


    private static void showDialogOfPlayingAutoBuy(FragmentManager fragmentManager, long mAlbumId,
                                                   BundleBuyDialogFragment.IAlbumStatusChangedListener albumStatusChangedListener) {
        AlbumAutoBuyConfirmDialog dialog = AlbumAutoBuyConfirmDialog.getInstance(mAlbumId);
        if (dialog != null) {
            dialog.show(fragmentManager, "dialogTagAlbumAutoBuyConfirm");
            dialog.setResultListener(new AlbumAutoBuyConfirmDialog.AutoBuyResultListener() {
                @Override
                public void onSuccessResult() {
                    if (albumStatusChangedListener != null)
                        albumStatusChangedListener.onAlbumAutoBuyStatusChanged(true);
                }

                @Override
                public void onFailResult() {
                    if (albumStatusChangedListener != null)
                        albumStatusChangedListener.onAlbumAutoBuyStatusChanged(false);

                }
            });
        }
    }

    private static void requestCloseAlbumAutoBuy(long mAlbumId,
                                                 BundleBuyDialogFragment.IAlbumStatusChangedListener albumStatusChangedListener) {
        MainCommonRequest.closeAlbumAutoBuy(mAlbumId, new IDataCallBack<JSONObject>() {
            @Override
            public void onSuccess(@Nullable JSONObject object) {
                CustomToast.showSuccessToast("自动购买已关闭");
                if (albumStatusChangedListener != null)
                    albumStatusChangedListener.onAlbumAutoBuyStatusChanged(false);
            }

            @Override
            public void onError(int code, String message) {
                if (albumStatusChangedListener != null)
                    albumStatusChangedListener.onAlbumAutoBuyStatusChanged(true);
                if (TextUtils.isEmpty(message))
                    message = "自动购买关闭失败";
                CustomToast.showFailToast(message);
            }
        });
    }

    public void setAlbumStatusChangedListener(BundleBuyDialogFragment.IAlbumStatusChangedListener albumStatusChangedListener) {
        this.albumStatusChangedListenerWeakReference = new WeakReference<>(albumStatusChangedListener);
    }

    @Override
    public void rechargeSuccess(double money) {
        dismiss();
    }

    @Override
    public void rechargeFail(String msg) {

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        PayManager.getInstance().removeRechargeCallback(this);
    }

    private void storeData() {
        BundleBuyDialogRestoreData data = new BundleBuyDialogRestoreData();
        data.setTrack(mTrack);
        data.setPageFrom(mPageFrom);
        data.setSelectItemIndex(mGridAdapter.getSelectedIndex());
        data.setFromAdUnLock(fromAdLock);
        data.setAbTestStatus(0);
        Activity activity = getActivity();
        if (activity instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) activity;
            Fragment fragment = mainActivity.getCurrentFragmentInManage();
            if (fragment != null) {
                data.setShowAtFragmentName(fragment.getClass().getName());
            }
        }
        mDataManager.storeDialogData(data);
    }


    public static class AlbumAutoBuySwitchListener implements CompoundButton.OnCheckedChangeListener {

        private FragmentManager mFragmentManager;
        private long mAlbumId;
        private boolean fromAdLock;
        private BundleBuyDialogFragment.IAlbumStatusChangedListener mAlbumStatusChangedListener;

        public AlbumAutoBuySwitchListener(boolean fromAdLock, FragmentManager fragmentManager,
                                          long mAlbumId,
                                          BundleBuyDialogFragment.IAlbumStatusChangedListener albumStatusChangedListener) {
            this.fromAdLock = fromAdLock;
            this.mFragmentManager = fragmentManager;
            this.mAlbumId = mAlbumId;
            mAlbumStatusChangedListener = albumStatusChangedListener;
        }

        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            if (isChecked) {
                showDialogOfPlayingAutoBuy(mFragmentManager, mAlbumId, mAlbumStatusChangedListener);
            } else {
                requestCloseAlbumAutoBuy(mAlbumId, mAlbumStatusChangedListener);
            }

            if (!fromAdLock) {
                new UserTracking("album", "button").setSrcPageId(mAlbumId)
                        .setSrcModule("选集购买浮层").setItemId("自动购买").setType(isChecked ? "on" : "off")
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT,
                                XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
            }

        }
    }

    class BundleBuyGridItemClickListener implements AdapterView.OnItemClickListener {

        void onItemClick(SingleAlbumPurchaseChannelsModel clickModel, boolean fromUser) {
            if(clickModel == null) {
                return;
            }
            SingleAlbumBehaviorModel behaviorModel = clickModel.getBehavior();
            if (behaviorModel == null || SingleAlbumBehaviorModel.TRACK_TYPE_PURCHASE_CHOOSE_BY_SELF.equals(behaviorModel.getTrackBuyType())) {
                return;
            }
            setPageTitle(behaviorModel);
            setDescriptionViews(behaviorModel);
            setAutoBuyViews(behaviorModel);
            setBuyButton(clickModel);

            if (SingleAlbumBehaviorModel.TRACK_TYPE_THIS_TRACK.equals(behaviorModel.getTrackBuyType())) {
                mBuyType = PayManager.TYPE_BUY_SINGLE;
            } else if (SingleAlbumBehaviorModel.TRACK_TYPE_NEXT_SEVERAL.equals(behaviorModel.getTrackBuyType())) {
                mBuyType = PayManager.TYPE_BUY_BATCH;
            } else if (SingleAlbumBehaviorModel.TRACK_TYPE_LEFT_ALL.equals(behaviorModel.getTrackBuyType())) {
                mBuyType = PayManager.TYPE_BUY_PROMOTION;
            } else {
                mBuyType = 0;
            }

            // 如果是来自视频广告,点击某个选项相当于点击了下面的按钮,不过要处理掉钱不够,单集购买的情况
            if (mAdData != null && fromUser) {
                onBuyAction(true);
            }
        }

        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            SingleAlbumPurchaseChannelsModel channelsModel = (SingleAlbumPurchaseChannelsModel) mGridAdapter.getItem(position);
            if (channelsModel == null) {
                return;
            }
            SingleAlbumBehaviorModel behaviorModel = channelsModel.getBehavior();
            if (behaviorModel == null) {
                return;
            }
            if (!SingleAlbumBehaviorModel.TRACK_TYPE_PURCHASE_CHOOSE_BY_SELF.equals(behaviorModel.getTrackBuyType())) {
                SingleAlbumPurchaseChannelsModel selectedModel = mGridAdapter.getSelectedModel();
                if (selectedModel != null) {
                    selectedModel.setSelect(false);
                }
                channelsModel.setSelect(true);

                mGridAdapter.notifyDataSetChanged();

                // 这里不加可能会导致notifiyDataChanged 不生效,获取不到getSelectedModel
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        onItemClick(channelsModel, true);
                    }
                }, 100);
            } else {
                // 自己选（跳转至 批量购买页）
                if (UserInfoMannage.hasLogined()) {

                    if (mPromotionModel != null && mGridAdapter != null) {
                        SingleAlbumPurchaseChannelsModel selectedModel = mGridAdapter.getSelectedModel();
                        if (selectedModel != null) {
                            selectedModel.setSelect(false);
                        }
                        channelsModel.setSelect(true);

                        mGridAdapter.notifyDataSetChanged();

                        // 这里不加可能会导致notifiyDataChanged 不生效,获取不到getSelectedModel
                        HandlerManager.postOnUIThreadDelay(new Runnable() {
                            @Override
                            public void run() {
                                PageData convert = PageData.convert(mPromotionModel,
                                        mGridAdapter.getSelectedModel());
                                mPayActionListener.actionBatchBuy(mAlbumId, fromAdLock, convert);
                                storeData();
                                dismiss();
                            }
                        }, 100);
                    }
                } else {
                    UserInfoMannage.gotoLogin(getContext());
                }
            }
            processBuryingPoint(behaviorModel.getTrackIdDesc());
            logTracksItemClick(behaviorModel.getTrackIdDesc());
        }

        private void logTracksItemClick(String desc) {
            new UserTracking().setAlbumId(mAlbumId).setSrcModule("选集购买").setItem("button")
                    .setItemId(desc)
                    .setViewStyle(fromAdLock ? 1 : 0)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT,
                            XDCSCollectUtil.SERVICE_ALBUM_PAGE_CLICK);
        }
    }
}
