package com.ximalaya.ting.android.main.payModule.refund;

import android.os.Bundle;
import android.text.TextUtils;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.util.EncryptProxy;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.model.refund.RefundModel;
import com.ximalaya.ting.android.host.model.refund.RefundReasonModel;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR> on 2017/3/7.
 */

public class RefundFragmentPresenter implements RefundContract.Presenter {

    private final RefundContract.View refundView;
    private String refundReason;
    private RefundModel mRefundModel;
    private long albumId;

    public RefundFragmentPresenter(RefundContract.View refundView) {
        this.refundView = refundView;
    }


    @Override
    public void getRefundData(Bundle bundle) {
        if (bundle != null) {
            long refundId = bundle.getLong(RefundFragment.REFUND_ID_KEY);
            String orderNo = bundle.getString(RefundFragment.ORDER_NO_KEY);
            albumId = bundle.getLong(BundleKeyConstants.KEY_ALBUM_ID);
            if (!TextUtils.isEmpty(orderNo)) {
                getRefundDataByOrderNo(orderNo, new IDataCallBack<RefundModel>() {
                    @Override
                    public void onSuccess(RefundModel object) {
                        if (object != null) {
                            mRefundModel = object;
                            refundView.setRequestRefundView(object);
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        CustomToast.showFailToast(message);
                        refundView.hideLoading();

                    }
                });
            } else if (refundId > 0) {
                getRefundDataByRefundId(refundId, new IDataCallBack<RefundModel>() {
                    @Override
                    public void onSuccess(RefundModel object) {
                        if (object != null) {
                            mRefundModel = object;
                            refundView.setRefundStatusView(object);
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        CustomToast.showFailToast(message);
                        refundView.hideLoading();
                    }
                });
            }
        }
    }

    @Override
    public void clickActionTv() {
        if (mRefundModel == null) {
            return;
        }
        switch (mRefundModel.statusId) {
            case AppConstants.REFUND_STATUS_DEFAULT:
                refundReason = refundView.checkSubmitRefundReason();
                if (!TextUtils.isEmpty(refundReason)) {
                    submitRefundRequest();
                }
                break;
            case AppConstants.REFUND_STATUS_REFUNDING:
                refundView.showCancelRefundDialog();
                break;
            default:
                break;
        }
    }

    @Override
    public boolean getRefundReasonCustom(String reason) {
        if (mRefundModel != null && mRefundModel.reasons != null) {
            for (RefundReasonModel refundReasonModel : mRefundModel.reasons) {
                if (!TextUtils.isEmpty(refundReasonModel.reason) && refundReasonModel.reason.equals(reason)) {
                    return refundReasonModel.isCustom;
                }
            }
        }
        return false;
    }

    private void submitRefundRequest() {

        refundView.showLoading();
        Map<String, String> map = new HashMap<>();
        map.put("reason", refundReason);
        map.put("userId", String.valueOf(UserInfoMannage.getUid()));
        map.put("merchantOrderNo", mRefundModel.merchantOrderNo);
        String signature = EncryptProxy.getPaySignature(map);
        map.put("signature", signature);
        MainCommonRequest.postRefundRequestByPayOrderNo(mRefundModel.merchantOrderNo, albumId, map, new IDataCallBack<Long>() {
            @Override
            public void onSuccess(Long object) {
                if (object != null && object > 0) {
                    getRefundDataByRefundId(object, new IDataCallBack<RefundModel>() {
                        @Override
                        public void onSuccess(RefundModel refundModel) {
                            if (refundModel != null) {
                                mRefundModel = refundModel;
                                refundView.setRefundStatusView(mRefundModel);
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            CustomToast.showFailToast(message);
                            refundView.hideLoading();
                        }
                    });
                }
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showFailToast(message);
                refundView.hideLoading();
            }
        });
    }

    @Override
    public void cancelRefundRequest() {
        refundView.showLoading();
        Map<String, String> params = new HashMap<>();
        params.put("refundId", String.valueOf(mRefundModel.refundId));
        params.put("userId", String.valueOf(UserInfoMannage.getUid()));
        String signature = EncryptProxy.getPaySignature(params);
        params.put("signature", signature);
        MainCommonRequest.postCancelRefundRequestByRefundId(String.valueOf(mRefundModel.refundId), params, new IDataCallBack<Integer>() {
            @Override
            public void onSuccess(Integer refundStatusId) {
                mRefundModel.statusId = AppConstants.REFUND_STATUS_REFUND_CANCEL;
                mRefundModel.refundStatusId = AppConstants.REFOUND_SUPPORT_REFOUND;
                mRefundModel.refundId = 0;
                refundView.setRefundStatusView(mRefundModel);
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showFailToast(message);
                refundView.hideLoading();
            }
        });
    }

    @Override
    public void setSubmitRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }

    private void getRefundDataByRefundId(long refundId, IDataCallBack<RefundModel> callBack) {

        refundView.showLoading();
        MainCommonRequest.getRefundDataByRefundId(String.valueOf(refundId), callBack);
    }


    private void getRefundDataByOrderNo(String orderNo, IDataCallBack<RefundModel> callBack) {

        refundView.showLoading();
        MainCommonRequest.getRefundDataByOrderNo(String.valueOf(orderNo), albumId, callBack);
    }

    public RefundModel getmRefundModel() {
        return mRefundModel;
    }

    public void setmRefundModel(RefundModel mRefundModel) {
        this.mRefundModel = mRefundModel;
    }

    public String getRefundReason() {
        return refundReason;
    }

    public void setRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }
}
