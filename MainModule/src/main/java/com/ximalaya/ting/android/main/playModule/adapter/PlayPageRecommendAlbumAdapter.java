package com.ximalaya.ting.android.main.playModule.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.support.rastermill.Helper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.ad.AnchorAlbumAd;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.util.XmRequestPage;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.ui.AlbumTagUtilNew;
import com.ximalaya.ting.android.host.util.ui.AlbumTitleTagUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.xdcs.model.UserTrackCookie;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.manager.playPage.PlayPageMarkPointManager;
import com.ximalaya.ting.android.main.playpage.internalservice.IAudioPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

/**
 * Created by WolfXu on 2020-02-28.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class PlayPageRecommendAlbumAdapter extends AbRecyclerViewAdapter<PlayPageRecommendAlbumAdapter.AlbumViewHolder> {

    private List<AlbumM> mAlbumList;
    private Context mContext;
    private Track mCurTrack;
    private PlayingSoundInfo mSoundInfo;
    private String mModuleName;

    public PlayPageRecommendAlbumAdapter() {
        mContext = BaseApplication.getOptActivity();
        if (mContext == null) {
            mContext = BaseApplication.getMyApplicationContext();
        }
    }

    @Override
    public AlbumM getItem(int position) {
        if (mAlbumList != null && position >= 0 && position < mAlbumList.size()) {
            return mAlbumList.get(position);
        }
        return null;
    }

    @NonNull
    @Override
    public AlbumViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_play_page_recommend_album_new,
                parent, false);
        return new AlbumViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull final AlbumViewHolder holder, int position) {
        if (mAlbumList == null || position < 0 || position >= mAlbumList.size()) {
            return;
        }
        final AlbumM albumM = mAlbumList.get(position);
        if (albumM == null) {
            return;
        }
        holder.itemView.setTag(R.id.main_anchor_ad_view, albumM);

        holder.adTag.setVisibility(View.INVISIBLE);

        ImageManager.from(mContext).displayImage(holder.ivCover, albumM.getValidCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album);
        AlbumTagUtilNew.getInstance().loadImage(holder.ivCoverTag, albumM.getAlbumSubscriptValue());

        int drawable = R.drawable.main_play_count;
        boolean isGif = false;
        if (albumM.getAdInfo() != null && AnchorAlbumAd.PROMOTE_TYPE_LIVE.equals(albumM.getAdInfo().getPromoteType())) {
            drawable = com.ximalaya.ting.android.host.R.raw.host_live_status;
            isGif = true;
        } else if (albumM.getAdInfo() != null && AnchorAlbumAd.PROMOTE_TYPE_MIRCO.equals(albumM.getAdInfo().getPromoteType())) {
            drawable = R.drawable.main_one_key_listen_count;
        }

        if (isGif) {
            Helper.fromRawResource(mContext.getResources(), com.ximalaya.ting.android.host.R.raw.host_live_status, frameSequenceDrawable -> {

                if (frameSequenceDrawable == null) {
                    return;
                }

                int mGiftBound = BaseUtil.dp2px(mContext, 15);
                frameSequenceDrawable.setBounds(0, 0, mGiftBound, mGiftBound);
                holder.tvPlayCount.setCompoundDrawables(frameSequenceDrawable, null, null, null);
            });
        } else {
            holder.tvPlayCount.setCompoundDrawables(LocalImageUtil.getDrawable(mContext, drawable), null, null, null);
        }

        if (albumM.getPlayCountByAdInfo() > 0) {
            holder.tvPlayCount.setText(StringUtil.getFriendlyNumStr(albumM.getPlayCountByAdInfo()));
            holder.tvPlayCount.setVisibility(View.VISIBLE);
        } else {
            holder.tvPlayCount.setVisibility(View.GONE);
        }

        CharSequence title;
        title = AlbumTitleTagUtil.getTitleWithTag(albumM, (int) holder.tvTitle.getTextSize(), Color.WHITE);
        if (albumM.getAdInfo() != null) {
            holder.adTag.setVisibility(View.VISIBLE);
            ImageManager.from(mContext).displayImage(holder.adTag, AdManager.getAnchorAdTag(albumM.getAdInfo().getPositionName())
                    , com.ximalaya.ting.android.host.R.drawable.host_ad_tag_style_4);
        }
        holder.tvTitle.setText(title);
        holder.tvTitle.setBackgroundColor(Color.TRANSPARENT);
        holder.tvTitle.setTextColor(Color.WHITE);

        holder.itemView.setOnClickListener(v -> {
            if (AdManager.checkAnchorAdCanClick(albumM.getAdInfo())) {
                if (mContext != null) {
                    AdManager.handlerAdClick(mContext, albumM.getAdInfo(), albumM.getAdInfo().createAdReportModel(AppConstants
                            .AD_LOG_TYPE_SITE_CLICK, holder.getAdapterPosition()).build());
                }
                traceItemClick(albumM, true, position);
                return;
            }

            UserTrackCookie.getInstance().setXmContent("relationRecommend", "album", null);
            UserTrackCookie.getInstance().setXmRecContent(albumM.getRecommendTrace(), albumM.getRecommentSrc());
            AlbumEventManage.startMatchAlbumFragment(albumM.getId(), AlbumEventManage.FROM_UNDEFINED,
                    ConstantsOpenSdk.PLAY_FROM_ALBUM_RECOMMEND, albumM.getRecommentSrc(), albumM.getRecommendTrace()
                    , -1, BaseApplication.getOptActivity());
            if (mCurTrack != null) {
                new UserTracking()
                        .setSrcPage("track")
                        .setSrcPageId(mCurTrack.getDataId())
                        .setSrcPosition(holder.getAdapterPosition())
                        .setItem("album")
                        .setItemId(albumM.getId())
                        .setSrcModule("相关推荐")
                        .setSrcSubModule("专辑条")
                        .setRecSrc(albumM.getRecommentSrc())
                        .setRecTrack(albumM.getRecTrack())
                        .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);

                traceItemClick(albumM, false, position);
            }
        });
        AutoTraceHelper.bindData(holder.itemView, "播放页", albumM);

        PlayPageMarkPointManager.Companion.markPointOnRecommendAlbumColumnListViewShow(mSoundInfo, albumM, holder.itemView);
    }

    private void traceItemClick(AlbumM album, boolean isAd, int position) {
        long albumId = 0;
        if (mCurTrack != null) {
            albumId = (mCurTrack.getAlbum() != null) ? mCurTrack.getAlbum().getAlbumId() : 0;
        }
        long adId = 0;
        if (isAd) {
            adId = album.getAdInfo() != null ? album.getAdInfo().getAdid() : 0;
        }
        new XMTraceApi.Trace()
                .click(17486)
                .put("albumId", String.valueOf(album.getId()))
                .put("rec_track", album.getRecommendTrace())
                .put("rec_src", album.getRecommentSrc())
                .put("position", String.valueOf(position + 1))
                .put("currTrackId", String.valueOf(mCurTrack != null ? mCurTrack.getDataId() : 0))
                .put("currAlbumId", String.valueOf(albumId))
                .put("currPage", "newPlay")
                .put("isAd", String.valueOf(isAd))
                .put("adId", adId > 0 ? String.valueOf(adId) : "")
                .put("ubtTraceId", album.getTraceId())
                .put("style", "单行横滑")
                .put("positionNew", String.valueOf(position + 1))
                .put("moduleName", mModuleName)
                .put(XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .createTrace();
    }

    @Override
    public int getItemCount() {
        if (mAlbumList != null) {
            return mAlbumList.size();
        }
        return 0;
    }

    public void setAlbumList(List<AlbumM> albumList) {
        mAlbumList = albumList;
    }

    public List<AlbumM> getAlbumList() {
        return mAlbumList;
    }

    public void setCurTrack(Track curTrack) {
        mCurTrack = curTrack;
    }


    public void setSoundInfo(PlayingSoundInfo soundInfo) {
        mSoundInfo = soundInfo;
    }

    public void setModuleName(String moduleName) {
        this.mModuleName = moduleName;
    }

    private Rect mRect = new Rect();

    @Override
    public void onViewAttachedToWindow(@NonNull AlbumViewHolder holder) {
        super.onViewAttachedToWindow(holder);

        // 这里因为声音简介栏目中会动态改变内容的高度,并且是延迟执行的,所以这里检测也是延时的,不然可能在检测的时候是在可视范围然后上报了,突然又看不到了
        HandlerManager.postOnUIThreadDelay(() -> checkAnchorAlbum(holder.itemView, true), 300);
    }

    private int [] tempLocal = new int[2];
    public void checkAnchorAlbum(View albumView, boolean checkLastIsVis) {
        if (albumView.getVisibility() == View.VISIBLE) {
            Object tag = albumView.getTag(R.id.main_play_ad_is_visable);
            boolean showing = false;
            if (tag instanceof Boolean) {
                showing = (Boolean) tag;
            }

            boolean localVisibleRect = albumView.getLocalVisibleRect(mRect);

            if(localVisibleRect) {
                albumView.getLocationInWindow(tempLocal);

                int yellowBarHeight = 0;

                IAudioPlayFragmentService playFragmentService =
                        PlayPageInternalServiceManager.getInstance().getService(IAudioPlayFragmentService.class);
                if (playFragmentService != null) {
                    yellowBarHeight = playFragmentService.getBottomBarHeight();
                }

                Logger.log("PlayPageRecommendAlbumAdapter :  " + tempLocal[1] + "  " + yellowBarHeight + "   " + BaseUtil.getScreenHeight(ToolUtil.getCtx()));
                if(tempLocal[1] <= 0
                        || (tempLocal[1] + yellowBarHeight > BaseUtil.getHasVirtualNavBarScreenHeight(ToolUtil.getCtx()))) {
                    localVisibleRect = false;
                }
            }

            boolean isShowVisibleRect = localVisibleRect &&
                    (checkLastIsVis || (localVisibleRect != showing));
            if (isShowVisibleRect) {
                Object object = albumView.getTag(R.id.main_anchor_ad_view);
                if (object instanceof AlbumM && ((AlbumM) object).getAdInfo() != null) {
                    AnchorAlbumAd adInfo = ((AlbumM) object).getAdInfo();
                    AdManager.adRecord(mContext, adInfo, adInfo.createAdReportModel(AppConstants
                            .AD_LOG_TYPE_SITE_SHOW, ((AlbumM) object).getIndexOfList() - 1).build());
                }
            }
            albumView.setTag(R.id.main_play_ad_is_visable, localVisibleRect);
        }
    }

    static final class AlbumViewHolder extends RecyclerView.ViewHolder {
        private TextView tvTitle;
        private ImageView ivCover;
        private ImageView ivCoverTag;
        private TextView tvPlayCount;
        private ImageView adTag;

        AlbumViewHolder(View itemView) {
            super(itemView);
            ivCover = itemView.findViewById(R.id.main_iv_cover);
            ivCoverTag = itemView.findViewById(R.id.main_iv_cover_tag);
            tvPlayCount = itemView.findViewById(R.id.main_tv_play_count);
            tvTitle = itemView.findViewById(R.id.main_tv_title);
            adTag = itemView.findViewById(R.id.main_ad_tag);
        }
    }
}
