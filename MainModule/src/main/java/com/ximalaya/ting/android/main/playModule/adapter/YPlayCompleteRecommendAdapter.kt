package com.ximalaya.ting.android.main.playModule.adapter

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.adapter.base.BaseProviderMultiAdapter
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback
import com.ximalaya.ting.android.host.manager.PlayCompleteManager
import com.ximalaya.ting.android.host.manager.account.AnchorCollectManage
import com.ximalaya.ting.android.host.manager.tolisten.AutoPlaySwitchManager
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam
import com.ximalaya.ting.android.host.model.play.PlayEndRecAlbum
import com.ximalaya.ting.android.host.model.play.YPlayEndDetailModel
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playModule.fragment.YPlayCompleteRecommendFragment
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager


/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2024/5/8
 */
class YPlayCompleteRecommendAdapter(val fragment: YPlayCompleteRecommendFragment, val model: YPlayEndDetailModel) :
    BaseProviderMultiAdapter<PlayEndRecAlbum>(model.toAlbumList().toMutableList()) {
    private var subscribeLlView: LinearLayout? = null
    private var cslAIRecView: View? = null
    private var subTitleTv: TextView? = null

    fun changeLeftTime(leftTime: Int) {
        if (leftTime == -1) {
            subTitleTv?.visibility = View.GONE
            return
        }
        ViewStatusUtil.setVisible(View.VISIBLE, subTitleTv)
        subTitleTv?.text = "${leftTime}秒后自动关闭"
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        val headView = LayoutInflater.from(context).inflate(
            R.layout.main_item_play_complete_head_default_new, recyclerView, false
        )
        if (fragment.getChildABValue()) {
            subTitleTv = headView.findViewById(R.id.main_tv_play_complete_sub_title)
        }
        addHeaderView(headView)

        val subscribeHeadView = LayoutInflater.from(context).inflate(
            R.layout.main_item_play_complete_subscribe_head, recyclerView, false
        ) as ViewGroup
        initSubscribeOrAIView(subscribeHeadView)
        addHeaderView(subscribeHeadView)
        addItemProvider(YHotRecommendItemProvider())

        setOnItemClickListener { adapter, _, position ->
            val data = adapter.data[position] as? PlayEndRecAlbum ?: return@setOnItemClickListener
            toAlbumPage(data.albumId, position, data.modelTitle)
        }

        setOnItemChildClickListener { adapter, view, position ->
            val album = adapter.data[position] as PlayEndRecAlbum
            if (view.id == R.id.main_tv_subscribe) {
                // 新声音播放页-完播推荐-订阅  点击事件
                XMTraceApi.Trace()
                    .click(63096) // 用户点击时上报
                    .put("currPage", "newPlay")
                    .put("xmRequestId", model.xmRequestId)
                    .put("currTrackId", getCurTrackAndAlbumId().first)
                    .put("currAlbumId", getCurTrackAndAlbumId().second)
                    .put(
                        "positionNew",
                        (position + 1).toString()
                    ) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                    .put("albumId", album.albumId.toString())
                    .createTrace()
                val param = AlbumCollectParam(
                    "AlbumCompletionPage",
                    AnchorCollectManage.SUBSCRIBE_BIZ_TYPE_PLAY_COMPLETE_RECOMMEND_RANK,
                    0,
                    0,
                    album.toAlbumM(), true
                ).apply {
                    showFollowDialog = false
                }
                AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(
                    param,
                    fragment,
                    object : ICollectWithFollowStatusCallback {
                        override fun onCollectSuccess(code: Int, isCollected: Boolean) {
                            album.isSubscribed = isCollected
                            adapter.notifyItemChanged(position + headerLayoutCount)
                        }

                        override fun onError() {}

                        override fun followDialogAction(status: Int) {}
                    },
                    param.currPage
                )
            }
        }
        addChildClickViewIds(R.id.main_tv_subscribe)
    }

    override fun getItemType(data: List<PlayEndRecAlbum>, position: Int): Int {
        return data[position].type
    }

    private fun initSubscribeOrAIView(subscribeHeadView: ViewGroup) {
        val subtitleTv = subscribeHeadView.findViewById<TextView>(R.id.main_tv_auto_play_subtitle)
        if (fragment.getChildABValue()) {
            subtitleTv.text = if (TextUtils.isEmpty(model.autoPlayDesc)) "该自动续播，仅在儿童赛道开启" else model.autoPlayDesc
        }
        val cslPlaySubscribe =
            subscribeHeadView.findViewById<View>(R.id.main_csl_play_subscribe)
        if (model.aiValid() || model.subscribeValid()) {
            cslPlaySubscribe.visibility = View.VISIBLE
            var modelTitle = if (model.aiValid()) model.aiAlbumResult?.title
                ?: "AI智能朗读版 更新更快" else model.subscribeAlbumResult?.title
                ?: "继续播放其他订阅专辑"
            if (fragment.getChildABValue()) {
                modelTitle = if (TextUtils.isEmpty(model.subscribeAlbumResult?.title)) "接下来为你播放" else model.subscribeAlbumResult!!.title!!
            }
            cslPlaySubscribe.findViewById<TextView>(R.id.main_tv_continue_title)?.apply {
                text = modelTitle
            }
            subscribeHeadView.findViewById<TextView>(R.id.main_tv_one_key_play).setOnClickListener {
                PlayCompleteManager.playYModeNextAlbum()
                callBack?.onClickOneKeyPlay()
                if (model.aiValid()) {
                    // 完播浮层- AI抢先听-一键播放  点击事件
                    XMTraceApi.Trace()
                        .click(63938) // 用户点击时上报
                        .put("currPage", "AlbumCompletionPage")
                        .put("xmRequestId", model.xmRequestId)
                        .createTrace()
                } else {
                    // 新声音播放页-完播推荐-一键播放  点击事件
                    XMTraceApi.Trace()
                        .click(63097) // 用户点击时上报
                        .put("currPage", "newPlay")
                        .put("xmRequestId", model.xmRequestId)
                        .put("currTrackId", getCurTrackAndAlbumId().first)
                        .put("currAlbumId", getCurTrackAndAlbumId().second)
                        .put("pageStyle", if (fragment.getChildABValue()) "实验组" else "对照组")
                        .put("categoryId", PlayCompleteManager.getCurTrackCategoryId())
                        .createTrace()
                }
            }
            val singleWidth = (BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext()) - 60.dp) / 3 * 70 / 79
            val llPlayContainer =
                cslPlaySubscribe.findViewById<ViewGroup>(R.id.main_ll_play_container)
            if (model.aiValid()) {
                val aiRecAlbum = model.aiAlbumResult?.recAlbumDto!!
                cslAIRecView =
                    LayoutInflater.from(context).inflate(
                        R.layout.main_item_play_complete_ai_head,
                        llPlayContainer,
                        false
                    ) as ViewGroup
                cslAIRecView?.findViewById<AlbumCoverLayoutView>(R.id.main_iv_album_cover)?.apply {
                    updateSizeAndRequestLayout(singleWidth.toFloat())
                    setAlbumCover(aiRecAlbum.validCover())
                    setAlbumTag(aiRecAlbum.ySubscript?.url)
                }
                cslAIRecView?.findViewById<TextView>(R.id.main_tv_album_title)?.apply {
                    text = aiRecAlbum.title
                }
                cslAIRecView?.findViewById<TextView>(R.id.main_tv_hot_reason)?.apply {
                    text = aiRecAlbum.recReason
                }
                val validWidth = BaseUtil.getScreenWidth(context) - 230.dp
                RecommendShowTagsUtilNew.bindTagsView(
                    cslAIRecView?.findViewById(R.id.main_layout_show_tag),
                    aiRecAlbum.showTagList,
                    validWidth, "", ""
                )
                cslAIRecView?.setOnClickListener {
                    toAlbumPage(aiRecAlbum.albumId, 0, modelTitle)
                    fragment.removeAutoCloseLogic()
                }
                llPlayContainer.addView(cslAIRecView)
            } else if (model.subscribeValid()) {
                subscribeLlView = LinearLayout(context).apply {
                    orientation = LinearLayout.HORIZONTAL
                }
                model.subscribeAlbumResult?.albumList?.take(3)?.forEachIndexed { index, endAlbum ->
                    val llAlbum: ViewGroup = LayoutInflater.from(context).inflate(
                        R.layout.main_item_play_complete_subscribe_head_album,
                        subscribeLlView,
                        false
                    ) as? ViewGroup ?: return@forEachIndexed
                    llAlbum.findViewById<AlbumCoverLayoutView>(R.id.main_iv_subscribe_album).apply {
                        updateSizeAndRequestLayout(singleWidth.toFloat())
                        setAlbumCover(endAlbum.validCover())
                        setAlbumTag(endAlbum.ySubscript?.url)
                    }
                    llAlbum.findViewById<TextView>(R.id.main_tv_subscribe_album).text =
                        endAlbum.title
                    llAlbum.setOnClickListener {
                        toAlbumPage(endAlbum.albumId, index, modelTitle)
                        fragment.removeAutoCloseLogic()
                    }
                    subscribeLlView?.addView(llAlbum)
                }
                llPlayContainer.addView(subscribeLlView, -1, -2)
            }
        } else {
            cslPlaySubscribe.visibility = View.GONE
        }

        subscribeHeadView.findViewById<CheckBox>(R.id.main_cb_auto_play).apply {
            isChecked = AutoPlaySwitchManager.isAutoPlay()
            setOnCheckedChangeListener { _, isChecked ->
                AutoPlaySwitchManager.setAutoPlay(isChecked)
                CustomToast.showToast(if (isChecked) "自动续播已开启,下次将自动为您续播订阅和推荐专辑" else "自动续播已关闭")
                // 新声音播放页-完播推荐-开启自动续播  点击事件
                XMTraceApi.Trace()
                    .click(63093) // 用户点击时上报
                    .put("currPage", "newPlay")
                    .put("xmRequestId", model.xmRequestId)
                    .put("currTrackId", getCurTrackAndAlbumId().first)
                    .put("currAlbumId", getCurTrackAndAlbumId().second)
                    .put("Item", if (isChecked) "打开" else "关闭") // 传点击后的状态
                    .put("pageStyle", if (fragment.getChildABValue()) "实验组" else "对照组")
                    .put("categoryId", PlayCompleteManager.getCurTrackCategoryId())
                    .createTrace()
            }
        }
    }

    private fun toAlbumPage(albumId: Long, position: Int, modelTitle: String) {
        if (model.aiValid()) {
            // 完播浮层- AI抢先听-专辑卡片  点击事件
            XMTraceApi.Trace()
                .click(63939) // 用户点击时上报
                .put("currPage", "AlbumCompletionPage")
                .put("xmRequestId", model.xmRequestId)
                .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("albumId", albumId.toString())
                .createTrace()
        } else {
            // 新声音播放页-完播推荐-专辑卡片  点击事件
            XMTraceApi.Trace()
                .click(63094) // 用户点击时上报
                .put("currPage", "newPlay")
                .put("xmRequestId", model.xmRequestId)
                .put("currTrackId", getCurTrackAndAlbumId().first)
                .put("currAlbumId", getCurTrackAndAlbumId().second)
                .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("albumId", albumId.toString())
                .put("moduleName", modelTitle) // 传模块名称，例如：继续播放其他订阅专辑
                .put("pageStyle", if (fragment.getChildABValue()) "实验组" else "对照组")
                .put("categoryId", PlayCompleteManager.getCurTrackCategoryId())
                .createTrace()
        }
        AlbumEventManage.startMatchAlbumFragment(
            albumId,
            AlbumEventManage.FROM_OTHER,
            0,
            null,
            null,
            0,
            BaseApplication.getMainActivity()
        )
    }

    private fun getCurTrackAndAlbumId(): Pair<String, String> {
        return Pair(
            PlayTools.getCurTrack(fragment.context)?.dataId?.toString() ?: "",
            PlayTools.getCurTrack(fragment.context)?.album?.albumId?.toString() ?: ""
        )
    }

    fun traceItemShow() {
        if (ViewStatusUtil.viewIsRealShowing(subscribeLlView)) {
            model.subscribeAlbumResult?.albumList?.take(3)
                ?.forEachIndexed { index, playEndRecAlbum ->
                    eTraceAlbumItemShow(
                        model.subscribeAlbumResult?.title ?: "",
                        playEndRecAlbum,
                        index
                    )
                }
        }
        if (ViewStatusUtil.viewIsRealShowing(cslAIRecView)) {
            model.aiAlbumResult?.recAlbumDto?.let {
                eTraceAiItemShow(it)
            }
        }
        val layoutManager = recyclerViewOrNull?.layoutManager as? LinearLayoutManager ?: return
        val firstVisiblePos = layoutManager.findFirstVisibleItemPosition() - headerLayoutCount
        val lastVisiblePos = layoutManager.findLastVisibleItemPosition() - headerLayoutCount
        for (i in firstVisiblePos..lastVisiblePos) {
            val album = data.getOrNull(i) ?: continue
            eTraceAlbumItemShow(album.modelTitle, album, i)
        }
    }

    private fun eTraceAlbumItemShow(modelTitle: String, album: PlayEndRecAlbum, position: Int) {
//        Logger.d("YPlayCompleteRecommendAdapter111", "$position ${album.albumId} ${album.title}")
        // 新声音播放页-完播推荐-专辑卡片  控件曝光
        XMTraceApi.Trace()
            .setMetaId(63095)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newPlay")
            .put(XmRequestIdManager.XM_CONT_ID, album.albumId.toString())
            .put(XmRequestIdManager.XM_CONT_TYPE, "album")
            .put(XmRequestIdManager.XM_REQUEST_ID, model.xmRequestId)
            .put("currTrackId", getCurTrackAndAlbumId().first)
            .put("currAlbumId", getCurTrackAndAlbumId().second)
            .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put("albumId", album.albumId.toString())
            .put("moduleName", modelTitle) // 传模块名称，例如：继续播放其他订阅专辑
            .put("pageStyle", if (fragment.getChildABValue()) "实验组" else "对照组")
            .put("categoryId", PlayCompleteManager.getCurTrackCategoryId())
            .createTrace()
    }

    private fun eTraceAiItemShow(album: PlayEndRecAlbum) {
        // 完播浮层- AI抢先听模块  控件曝光
        XMTraceApi.Trace()
            .setMetaId(63937)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "AlbumCompletionPage")
            .put(XmRequestIdManager.XM_CONT_ID, album.albumId.toString())
            .put(XmRequestIdManager.XM_CONT_TYPE, "album")
            .put(XmRequestIdManager.XM_REQUEST_ID, model.xmRequestId)
            .createTrace()
    }

    private var callBack: Callback? = null

    fun setCallback(callBack: Callback) {
        this.callBack = callBack
    }

    interface Callback {
        fun onClickOneKeyPlay()
    }
}