package com.ximalaya.ting.android.main.playModule.view;

//import androidx.lifecycle.Lifecycle.Event;
//import androidx.lifecycle.LifecycleObserver;
//import androidx.lifecycle.OnLifecycleEvent;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.TextSwitcher;
import android.widget.TextView;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;

import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * Created by zhang<PERSON><PERSON> on 2019/4/15.
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @PhoneNumber 15721173906
 */
public class AnchorTipsTextSwitcher extends TextSwitcher implements LifecycleObserver {

    private static int sAnimDuration = 300;
    private boolean isInit;
    private boolean mCancelled;
    private List<String> mTexts;
    private long timeSpan = 3000;
    private float mTextSize = 12.0f;
    private int mTextColor;
    private int index;
    private TextSwitchTask textSwitcherTask;
    public AnchorTipsTextSwitcher(Context context) {
        super(context);
    }
    public AnchorTipsTextSwitcher(Context context, AttributeSet attrs) {
        super(context, attrs);
        textSwitcherTask = new TextSwitchTask(this);
        mTextColor = Color.parseColor(BaseFragmentActivity.sIsDarkMode ? "#888888" : "#cc333333");
    }

    public void setTexts(List<String> texts) {
        index = (int) (System.currentTimeMillis() % texts.size());
        stop();
        this.mTexts = texts;
        start();
    }

    public void start() {
        if (null == mTexts || mTexts.isEmpty()) {
            return;
        }
        mCancelled = false;
        removeCallbacks(textSwitcherTask);
        if (!isInit) {
            setFactory(null);
            setInAnimation(null);
            setOutAnimation(null);
            isInit = true;
        }
        setCurrentText(mTexts.get(index));
        if (mTexts.size() > 1) {
            postDelayed(textSwitcherTask, timeSpan);
        }
    }

    @Override
    public void setInAnimation(Animation inAnimation) {
        if (inAnimation == null) {
            inAnimation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                Animation.RELATIVE_TO_SELF, 1,
                Animation.RELATIVE_TO_PARENT, 0);
            inAnimation.setDuration(sAnimDuration);
            inAnimation.setFillAfter(true);
        }
        super.setInAnimation(inAnimation);
    }

    @Override
    public void setOutAnimation(Animation outAnimation) {
        if (outAnimation == null) {
            outAnimation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                Animation.RELATIVE_TO_SELF, 0,
                Animation.RELATIVE_TO_PARENT, -1);
            outAnimation.setDuration(sAnimDuration);
        }
        super.setOutAnimation(outAnimation);
    }

    @Override
    public void setFactory(ViewFactory factory) {
        if (factory == null) {
            factory = new ViewFactory() {
                @Override
                public View makeView() {
                    TextView textView = new TextView(getContext());
                    textView.setTextColor(mTextColor);
                    textView.setTextSize(mTextSize);
                    textView.setEllipsize(TextUtils.TruncateAt.END);
                    textView.setMaxLines(1);
                    textView.setLayoutParams(
                        new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
                    return textView;
                }
            };
            super.setFactory(factory);
        }
    }

    public void stop() {
        mCancelled = true;
        removeCallbacks(textSwitcherTask);
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    public void onResume() {
        start();
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void onDestroy() {
        stop();
    }

    // MySpaceFragmentNew 的 onPause 时已然stop了
//    @OnLifecycleEvent(Event.ON_PAUSE)
//    public void onPause() {
//        stop();
//    }

    public int getIndex() {
        return index;
    }

    @Override
    protected void onDetachedFromWindow() {
        stop();
        super.onDetachedFromWindow();
    }

    private static class TextSwitchTask implements Runnable {

        private final WeakReference<AnchorTipsTextSwitcher> reference;

        TextSwitchTask(AnchorTipsTextSwitcher textSwitcher) {
            this.reference = new WeakReference<>(textSwitcher);
        }

        @Override
        public void run() {
            AnchorTipsTextSwitcher textSwitcher = reference.get();
            if (textSwitcher != null && !textSwitcher.mCancelled) {
                textSwitcher.showNext();
                textSwitcher.index = (textSwitcher.index + 1) % textSwitcher.mTexts.size();
                textSwitcher.setCurrentText(textSwitcher.mTexts.get(textSwitcher.index));
                textSwitcher.postDelayed(textSwitcher.textSwitcherTask, textSwitcher.timeSpan);
            }
        }
    }
}
