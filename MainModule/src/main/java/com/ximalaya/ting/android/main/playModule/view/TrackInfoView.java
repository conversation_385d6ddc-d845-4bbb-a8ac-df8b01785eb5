package com.ximalaya.ting.android.main.playModule.view;

import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.TransitionDrawable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.core.view.ViewPropertyAnimatorListener;
import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.TempDataManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.X5Util;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.view.ImageViewer;
import com.ximalaya.ting.android.host.view.other.RichWebView;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.listener.IOnSubscribeListener;
import com.ximalaya.ting.android.main.playModule.IBasePlayFragment;
import com.ximalaya.ting.android.main.playModule.IPlayFragment;
import com.ximalaya.ting.android.main.playModule.IPlayFunction;
import com.ximalaya.ting.android.main.playModule.fragment.VideoPlayFragment;
import com.ximalaya.ting.android.main.playModule.presenter.VideoPlayListPresenter;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * 播放页-声音基础信息模块
 *
 * <AUTHOR> on 2017/4/8.
 */

public class TrackInfoView implements IPlayFragment.ITrackInfoView, AlbumEventManage
        .CollectListener {
    private static final String TAG = "<声音基础信息模块>";
    private final IPlayFunction function;
    private boolean isFavorite;
    private Album album;
    private static final float HEIGHT_PROPORTION = 0.6f;//订阅按钮在屏幕纵轴位置比例

    public static final int TYPE_PLAY_FRAGMENT = 1;
    public static final int TYPE_PPT_FRAGMENT = 2;
    public static final int TYPE_VIDEO_FRAGMENT = 3;

    private View vSpaceAlbumInfo;
    private VideoListView mVideoListView;
    private String mRichStr;
    private int WEBVIEW_MAX_HEIGHT = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 400);
    private boolean isAuthorized = false;
    private int mTotalHeight;
    private int mLeft;

    public void setTypeFrom(int typeFrom) {
        this.typeFrom = typeFrom;
    }

    private int typeFrom;//来源页面
    @Nullable
    private IOnSubscribeListener onSubscribeListener;

    @Nullable
    public IOnSubscribeListener getOnSubscribeListener() {
        return onSubscribeListener;
    }

    public void setOnSubscribeListener(@Nullable IOnSubscribeListener listener) {
        this.onSubscribeListener = listener;
    }

    @Override
    public View getBtnSubscribe() {
        return btnSubscribe;
    }

    @Nullable
    private IPlayFragment.ITrackInfoViewEventListener mEventListener;

    private ImageView ivAlbumCover;
    private TextView tvAlbumTitle;
    private TextView tvSubscribeCount;
    private ImageView btnSubscribe;
    private TextView tvLookAll;
    private View vLookAll;
    private TextView tvPlayNumAndTime;
    private RelativeLayout richContainer;
    private RichWebView tvRichContent;

    private boolean waiting = true;//是否等待渲染通知
    private boolean isEnable = true;//模块是否启用
    private long subscribeCount;
    private View border;
    private boolean hasInit;
    private ViewStub viewStub;
    private View collectLoading;
    private boolean isCollectLoading;
    private TextView tvTrackTitle;
    private View ivArrow;
    private boolean hasLookAll;
    IBasePlayFragment mBasePlayFragment;

    public TrackInfoView(IPlayFunction function) {
        this(function, null);
    }

    public TrackInfoView(IPlayFunction function, IPlayFragment.ITrackInfoViewEventListener
            listener) {
        this.function = function;
        mEventListener = listener;
    }

    @Override
    public void showFragment(Fragment fragment) {
        function.startFragment(fragment);
    }

    @Override
    public void setList(List list) {

    }

    @Override
    public void init(IBasePlayFragment fragment) {
        if (hasInit)
            return;

        Logger.i("PlayFragment", "初始化声音信息模块");
        hasInit = true;
        viewStub = fragment.findViewById(R.id.main_view_stub_trackInfo);
        View rootView = viewStub.inflate();
        mBasePlayFragment = fragment;

        ivAlbumCover = fragment.findViewById(R.id.main_header_owner_icon);
        tvAlbumTitle = fragment.findViewById(R.id.main_header_owner_name);
        tvSubscribeCount = fragment.findViewById(R.id.main_header_sub_num);
        btnSubscribe = fragment.findViewById(R.id.main_header_owner_subscribe);
        tvTrackTitle = fragment.findViewById(R.id.main_play_track_title);
        tvPlayNumAndTime = fragment.findViewById(R.id.main_play_num_and_time);
        tvLookAll = fragment.findViewById(R.id.main_tv_look_all);
        vLookAll = fragment.findViewById(R.id.main_v_look_all);
        richContainer = fragment.findViewById(R.id.main_rich_context);
        border = fragment.findViewById(R.id.main_border_track_info);
        vSpaceAlbumInfo = fragment.findViewById(R.id.main_space_album_info);
        collectLoading = fragment.findViewById(R.id.main_iv_collect_loading);
        ivArrow = fragment.findViewById(R.id.main_iv_arrow);

        ElderlyModeManager.getInstance().setElderlyHeight(tvTrackTitle, 70);

        if (fragment instanceof View.OnClickListener) {
            btnSubscribe.setOnClickListener((View.OnClickListener) fragment);
            vSpaceAlbumInfo.setOnClickListener((View.OnClickListener) fragment);
            AutoTraceHelper.bindData(btnSubscribe, AutoTraceHelper.MODULE_DEFAULT, "");
            AutoTraceHelper.bindData(vSpaceAlbumInfo, AutoTraceHelper.MODULE_DEFAULT, "");
        }
        if (ivArrow != null) {
            richContainer.setVisibility(View.GONE);
            vLookAll.setVisibility(View.GONE);
            ivArrow.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (!ivArrow.isSelected()) {
                        ivArrow.setSelected(true);
                        richContainer.setVisibility(View.VISIBLE);
                        vLookAll.setVisibility(hasLookAll ? View.VISIBLE : View.GONE);

                        if (mEventListener != null) {
                            mEventListener.onArrowClick(true);
                        }
                    } else {
                        ivArrow.setSelected(false);
                        richContainer.setVisibility(View.GONE);
                        vLookAll.setVisibility(View.GONE);

                        if (mEventListener != null) {
                            mEventListener.onArrowClick(false);
                        }
                    }
                }
            });
            AutoTraceHelper.bindData(ivArrow, AutoTraceHelper.MODULE_DEFAULT, "");
        }

        if (fragment instanceof VideoPlayFragment) {
            mVideoListView = new VideoListView(function.getContext(), function);
            mVideoListView.init(fragment);
        }
    }

    @Override
    public void gone() {
        if (!function.canUpdateUi() || !hasInit)
            return;

        if (viewStub != null) {
            viewStub.setVisibility(View.GONE);
        }
        ivAlbumCover.setVisibility(View.GONE);
        tvAlbumTitle.setVisibility(View.GONE);
        tvSubscribeCount.setVisibility(View.GONE);
        btnSubscribe.setVisibility(View.GONE);
        tvPlayNumAndTime.setVisibility(View.GONE);
        vLookAll.setVisibility(View.GONE);
        richContainer.setVisibility(View.GONE);
        border.setVisibility(View.GONE);
        collectLoading.setVisibility(View.GONE);

        AlbumEventManage.removeListener(this);
    }

    @Override
    public void visible() {
        if (!canRender())
            return;


        if (viewStub != null) {
            viewStub.setVisibility(View.VISIBLE);
        }
        ivAlbumCover.setVisibility(View.VISIBLE);
        tvAlbumTitle.setVisibility(View.VISIBLE);
        tvSubscribeCount.setVisibility(View.VISIBLE);
        btnSubscribe.setVisibility(View.VISIBLE);
        tvPlayNumAndTime.setVisibility(View.VISIBLE);
        if (ivArrow == null) {
            vLookAll.setVisibility(View.VISIBLE);
            richContainer.setVisibility(View.VISIBLE);
        }
        border.setVisibility(View.VISIBLE);

        AlbumEventManage.addListener(this);
    }

    @Deprecated
    @Override
    public void showToast(String str) {
        CustomToast.showToast(str);
    }

    @Deprecated
    @Override
    public void showToast(int resId) {
        CustomToast.showToast(resId);
    }

    @Override
    public boolean canRender() {
        return function.canUpdateUi() && isEnable && hasInit;
    }

    @Override
    public void disable() {
        isEnable = false;
    }

    @Override
    public void enable() {
        isEnable = true;
    }

    @Override
    public void notifyRender() {
        if (!waiting)
            return;
        Logger.log("PlayFragment" + "渲染框架测试" + "通知渲染声音信息模块");
    }

    public void setMusicInfoList(List<PlayingSoundInfo.MusicInfo> musicInfoList,
                                 boolean isAuthorized) {
        this.isAuthorized = isAuthorized;
        if (TextUtils.isEmpty(mRichStr)) {
            showRelatedMusicView(musicInfoList);
            return;
        }

        if (vLookAll.getVisibility() != View.VISIBLE) {
            showRelatedMusicView(musicInfoList);
        }
    }

    @Override
    public void setRichText(String richStr, boolean isAuthorized) {
        Logger.log("PlayFragment" + "渲染框架测试" + TAG + "富文本" + "请求渲染");
        this.mRichStr = richStr;
        this.isAuthorized = isAuthorized;
        if (!canRender()) {
            waiting = true;
            Logger.log("PlayFragment" + "渲染框架测试" + TAG + "富文本" + "等待通知渲染");
            return;
        }
        waiting = false;
        Logger.log("PlayFragment" + "渲染框架测试" + TAG + "富文本" + "执行渲染");

        vLookAll.setVisibility(View.GONE);

        if (function.getCurTrack() != null) {
            if (ivArrow != null) {
                ivArrow.setVisibility(View.VISIBLE);
            }
            if (tvTrackTitle != null) {
                tvTrackTitle.setText(function.getCurTrack().getTrackTitle());
            }
        }

        if (TextUtils.isEmpty(richStr)) {
            if (tvRichContent != null) {
                tvRichContent.setData("", null);
            }
            richContainer.setVisibility(View.GONE);
            tvPlayNumAndTime.setVisibility(View.GONE);
            if (ivArrow == null) {
                tvTrackTitle.setVisibility(View.GONE);
            } else {
                ivArrow.setVisibility(View.GONE);
            }
            return;
        }
        tvPlayNumAndTime.setVisibility(View.VISIBLE);
        tvTrackTitle.setVisibility(View.VISIBLE);

        Drawable drawable = function.getFragment().getResourcesSafe().getDrawable(R.drawable.main_ic_doc_detail_arrow);
        drawable.setBounds(new Rect(0, 0, BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 16), BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 16)));
        tvLookAll.setCompoundDrawables(null, null, drawable, null);
        if (!isAuthorized) {
            tvLookAll.setText("购买后即可查看全文");
            vLookAll.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (function.getFragment() instanceof VideoPlayFragment) {
                        // ((VideoPlayFragment) function.getFragment()).onBuyClicked();
                    }
                }
            });
        } else {
            tvLookAll.setText("查看全文");
            vLookAll.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    RelativeLayout.LayoutParams lp =
                            (RelativeLayout.LayoutParams) tvRichContent.getLayoutParams();
                    lp.height = mTotalHeight;
                    tvRichContent.setLayoutParams(lp);
                    vLookAll.setVisibility(View.GONE);
                    if (function.getCurTrack() != null) {
                        TempDataManager.getInstance().saveBoolean(TempDataManager.DATA_PLAY_PAGE_DOC_SPREAD + function.getCurTrack().getDataId(), true);
                    }
                }
            });
        }
        tvLookAll.setSelected(!isAuthorized);

        if (richContainer.getVisibility() != View.VISIBLE) {
            vLookAll.setVisibility(View.GONE);
        }

        if (tvRichContent == null) {
            try {
                tvRichContent = new RichWebView(function.getActivity());
                X5Util.setWebViewLayoutParams(tvRichContent);
                tvRichContent.setOnContentChangeListener(new MyOnContentChangedListener(this));
                richContainer.addView(tvRichContent, new ViewGroup.LayoutParams(ViewGroup
                        .LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT));
            } catch (Throwable e) {
                e.printStackTrace();
                return;
            }

            tvRichContent.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    return false;
                }
            });
            tvRichContent.setVerticalScrollBarEnabled(false);
            tvRichContent.setURLClickListener(new MyURLClickListener(this));
            tvRichContent.setOnImageClickListener(new RichWebView.IOnImageClickListener() {
                @Override
                public void onClick(List<ImageViewer.ImageUrl> imgs, int index) {
                    ImageViewer mImageViewer = new ImageViewer(function.getActivity());
                    mImageViewer.setImageUrls(imgs);
                    mImageViewer.show(index, function.getFragment().getView());
                }
            });
            tvRichContent.enableSelectCopy();
        }
        RichWebView.RichWebViewAttr attr = new RichWebView.RichWebViewAttr();
        attr.marginLeft = 15;
        attr.marginRight = 15;
        tvRichContent.post(() -> {
            tvRichContent.resetParams();
            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            tvRichContent.setLayoutParams(lp);
            ToolUtil.setRichContentToWebView(tvRichContent, function.getContext(), mRichStr, attr);
        });
        richContainer.setVisibility(View.VISIBLE);
    }

    private void adjustWebView() {
        int height = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), tvRichContent.getContentHeight());
        boolean spread = false;
        if (function.getCurTrack() != null) {
            spread = TempDataManager.getInstance().getBoolean(TempDataManager.DATA_PLAY_PAGE_DOC_SPREAD + function.getCurTrack().getDataId());
        }
        if (spread) {
            return;
        }

        if (height < WEBVIEW_MAX_HEIGHT) {
            vLookAll.setVisibility(View.GONE);
            hasLookAll = false;
        } else if (height > WEBVIEW_MAX_HEIGHT) {
            mLeft = (int) ((height - WEBVIEW_MAX_HEIGHT) * 100 / (float) height);

            String action = "";
            if (function.getCurTrack() != null) {
                if (function.getCurTrack().getAuthorizedType() == 0) {
                    action = "购买后可查看全文";
                } else {
                    action = "加入会员后可查看全文";
                }
            }

            if (!isAuthorized || mLeft >= 20) {

                if (ivArrow == null || ivArrow.isSelected()) {
                    vLookAll.setVisibility(View.VISIBLE);
                }
                RelativeLayout.LayoutParams lp =
                        (RelativeLayout.LayoutParams) tvRichContent.getLayoutParams();
                lp.height = WEBVIEW_MAX_HEIGHT;
                tvRichContent.setLayoutParams(lp);

                if(!isAuthorized) {
                    tvLookAll.setText("剩余" + mLeft + "%，" + action);
                } else {
                    tvLookAll.setText("剩余" + mLeft + "%，继续阅读");
                }

                hasLookAll = true;

            } else {
                vLookAll.setVisibility(View.GONE);

                hasLookAll = false;
            }

            mTotalHeight = height;
        }
    }

    private void showRelatedMusicView(List<PlayingSoundInfo.MusicInfo> musicInfoList) {

    }

    public void hideRichContext() {
        if (richContainer != null) {
            richContainer.setVisibility(View.GONE);
        }

        if (ivArrow != null) {
            ivArrow.setSelected(false);
        }

        if (vLookAll != null) {
            vLookAll.setVisibility(View.GONE);
        }
    }

    private boolean isBtnSubscribeInBottom() {
        if (canRender() && btnSubscribe != null) {
            int[] location = new int[2];
            btnSubscribe.getLocationOnScreen(location);
            return location[1] >= HEIGHT_PROPORTION * BaseUtil.getScreenHeight(function.getContext());
        }
        return false;
    }

    @Override
    public void setTrackInfo(final Album album, long subscribeCountTemp) {
        Logger.log("PlayFragment" + "渲染框架测试" + TAG + "请求渲染");
        subscribeCount = subscribeCountTemp;
        if (!canRender() || function.getCurTrack() == null || album == null) {
            waiting = true;
            gone();
            Logger.log("PlayFragment" + "渲染框架测试" + TAG + "等待通知渲染");
            return;
        }
        waiting = false;
        visible();
        Logger.log("PlayFragment" + "渲染框架测试" + TAG + "执行渲染");


        ImageManager.from(function.getContext()).displayImage(function.getFragment(),
                ivAlbumCover, album.getValidCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album);
        tvAlbumTitle.setText(album.getAlbumTitle());
        tvSubscribeCount.setText(StringUtil.getFriendlyNumStrAndCheckIsZero(subscribeCountTemp,
                function.getFragment().getStringSafe(R.string.main_num_people_sub)));
        if (function.getCurTrack() instanceof TrackM && ((TrackM) function.getCurTrack()).isDraft()) {
            StringBuilder result = new StringBuilder();
            result.append("<img src=\"").append(com.ximalaya.ting.android.host.R.drawable.host_tag_draft1).append("\">  ");
            result.append(function.getCurTrack().getTrackTitle());
        }
        tvPlayNumAndTime.setText(StringUtil.getFriendlyNumStrAndCheckIsZero(function.getCurTrack
                ().getPlayCount(), function.getFragment().getStringSafe(R.string.main_num_play))
                + StringUtil.getFriendlyDataStr(function.getCurTrack().getCreatedAt()));

        this.album = album;
        setSubscribeStatusAndOnClickListener(album);
        AutoTraceHelper.bindData(vSpaceAlbumInfo, "播放页", this.album);
        AutoTraceHelper.bindData(btnSubscribe, "播放页", this.album);
    }

    @Override
    public void release() {
        AlbumEventManage.removeListener(this);
        onSubscribeListener = null;
        if (mVideoListView != null) {
            mVideoListView.release();
        }
    }

    private void setSubscribeStatusAndOnClickListener(final Album album) {
        if (album == null) {
            return;
        }
        int collectResId = (typeFrom == TYPE_PLAY_FRAGMENT) ? R.drawable.main_play_btn_collect :
                R.drawable.main_play_btn_collect_album;
        AlbumEventManage.setCollectStatus(album, function.getFragment(), btnSubscribe, collectResId,
                R.drawable.main_btn_collected, new AlbumEventManage.IQueryResultCallBack() {
                    @Override
                    public void onQueryResult(boolean isFavority) {
                        isFavorite = isFavority;
                        if (btnSubscribe != null) {
                            btnSubscribe.setContentDescription(isFavority ? "已订阅" : "订阅");
                        }
                    }
                });

        long trackId = 0;
        if (function.getCurTrack() != null) {
            trackId = function.getCurTrack().getDataId();
        }
        final long finalTrackId = trackId;
        AlbumEventManage.setCollectImageClickAndStatus(function.getFragment(), btnSubscribe, album,
                collectResId, R.drawable.main_btn_collected, new AlbumEventManage.ICollect() {
                    @Override
                    public boolean prepare() {
                        if (album instanceof AlbumM && mEventListener != null) {
                            if (((AlbumM) album).isFavorite()) {
                                mEventListener.onSubscribeClick(false);
                            } else {
                                mEventListener.onSubscribeClick(true);
                            }
                        }

                        if (album instanceof AlbumM && ((AlbumM) album).isFavorite())
                            return false;

                        if (isCollectLoading)
                            return true;

                        isCollectLoading = true;

                        ViewCompat.animate(btnSubscribe)
                                .scaleX(0.95f)
                                .scaleY(0.95f)
                                .setDuration(200)
                                .setListener(new ViewPropertyAnimatorListener() {
                                    @Override
                                    public void onAnimationStart(View view) {

                                    }

                                    @Override
                                    public void onAnimationEnd(View view) {
                                        ViewCompat.animate(btnSubscribe)
                                                .scaleX(1)
                                                .scaleY(1)
                                                .setDuration(200)
                                                .start();
                                    }

                                    @Override
                                    public void onAnimationCancel(View view) {

                                    }
                                })
                                .start();
                        return true;
                    }

                    @Override
                    public void success(boolean collect) {
                        isCollectLoading = false;
                        if (getOnSubscribeListener() != null) {
                            getOnSubscribeListener().onSuccess(collect);
                        }
                        if (mEventListener != null && isBtnSubscribeInBottom()) {
                            mEventListener.onSubscribeButtonClick();
                        }
                        isFavorite = collect;


                        subscribeCount += collect ? 1 : -1;
                        if (mEventListener == null) {  //埋点事件由listener上报
                            new UserTracking().setSrcPage("track").setItem("track").
                                    setSrcPageId(finalTrackId).
                                    setItemId(finalTrackId).
                                    statIting(XDCSCollectUtil.APP_NAME_EVENT, collect ?
                                            XDCSCollectUtil.SERVICE_COLLECT : XDCSCollectUtil
                                            .SERVICE_UNCOLLECT);
                        }


                        int collectResId = (typeFrom == TYPE_PLAY_FRAGMENT) ? R.drawable
                                .main_play_btn_collect : R.drawable.main_play_btn_collect_album;
                        if (!collect) {
                            btnSubscribe.setImageResource(collectResId);
                        }

                        AnimationUtil.stopAnimation(collectLoading);
                        collectLoading.setVisibility(View.GONE);
                        TransitionDrawable td = null;
                        if (isFavorite) {
                            td = new TransitionDrawable(new Drawable[]{function.getFragment()
                                    .getResourcesSafe().getDrawable(R.drawable
                                    .main_semicircle_rectangle_ffece8),
                                    function.getFragment().getResourcesSafe().getDrawable(R
                                            .drawable.main_btn_collected)});
                        } else {
                            td = new TransitionDrawable(new Drawable[]{function.getFragment()
                                    .getResourcesSafe().getDrawable(R.drawable
                                    .main_semicircle_rectangle_ffece8),
                                    function.getFragment().getResourcesSafe().getDrawable
                                            (collectResId)});
                        }
                        td.startTransition(200);
                        btnSubscribe.setImageDrawable(td);
                        tvSubscribeCount.setText(StringUtil.getFriendlyNumStrAndCheckIsZero
                                (subscribeCount, function.getFragment().getStringSafe(R.string
                                        .main_num_people_sub)));
                        if (btnSubscribe != null) {
                            btnSubscribe.setContentDescription(isFavorite ? "已订阅" : "订阅");
                        }
                    }

                    @Override
                    public void fail(String msg) {
                        isCollectLoading = false;
                        if (getOnSubscribeListener() != null) {
                            getOnSubscribeListener().onFailed();
                        }
                        CustomToast.showFailToast(msg);
                        collectLoading.clearAnimation();
                        collectLoading.setVisibility(View.GONE);
                    }

                });
        AutoTraceHelper.bindData(btnSubscribe, AutoTraceHelper.MODULE_DEFAULT, "");
    }

    public TextView getBtnLookAll() {
        return tvLookAll;
    }

    public void releaseRichWeb() {
        if (tvRichContent != null)
            tvRichContent.destroy();
    }

    public void resumeRichWeb() {
        if (tvRichContent != null)
            tvRichContent.onResume();
    }

    public void pauseRichWeb() {
        if (tvRichContent != null)
            tvRichContent.onPause();
    }

    public int getLocation() {
        if (!hasInit)
            return 0;
        int[] headerLocation = new int[2];
        if (tvTrackTitle != null) {
            tvTrackTitle.getLocationOnScreen(headerLocation);
        } else {
            ivAlbumCover.getLocationOnScreen(headerLocation);//这里渲染限制只在富文本，标题栏因为在首屏，强制渲染
        }
        return headerLocation[1] - BaseUtil.dp2px(function.getContext(), 20);
    }

    @Override
    public void onCollectChanged(boolean collect, long id) {
        if (canRender() && id == album.getId()) {
            int collectResId = (typeFrom == TYPE_PLAY_FRAGMENT) ? R.drawable
                    .main_play_btn_collect : R.drawable.main_play_btn_collect_album;
            collectResId = collect ? R.drawable.main_btn_collected : collectResId;
            if (function != null && function.getContext() != null && function.getContext()
                    .getResources() != null) {
                Drawable drawable = function.getContext().getResources().getDrawable(collectResId);
                btnSubscribe.setImageDrawable(drawable);
                btnSubscribe.setContentDescription(collect ? "已订阅" : "订阅");
            }
        }
    }

    public void setVideoPlayListPresenter(VideoPlayListPresenter presenter) {
        if (mVideoListView != null) {
            mVideoListView.setPresenter(presenter);
        }
    }

    private static class MyURLClickListener implements RichWebView.URLClickListener {
        WeakReference<TrackInfoView> mTrackInfoViewWeakReference;

        public MyURLClickListener(TrackInfoView trackInfoView) {
            mTrackInfoViewWeakReference = new WeakReference<>(trackInfoView);
        }

        @Override
        public boolean urlClick(String url) {
            TrackInfoView trackInfoView = mTrackInfoViewWeakReference.get();
            if (trackInfoView == null || trackInfoView.function == null) {
                return true;
            }
            ToolUtil.recognizeItingUrl(trackInfoView.function.getFragment(), url);
            return true;
        }
    }

    static class MyOnContentChangedListener implements RichWebView.IContentChangeListener {
        private WeakReference<TrackInfoView> wr;

        MyOnContentChangedListener(TrackInfoView view) {
            this.wr = new WeakReference<>(view);
        }

        @Override
        public void onContentChange() {
            if (wr == null || wr.get() == null) {
                return;
            }
            wr.get().tvRichContent.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (wr == null || wr.get() == null) {
                        return;
                    }
                    wr.get().adjustWebView();
                }
            }, 200);
        }
    }
}
