package com.ximalaya.ting.android.main.playpage;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.DocRecommendIndexModel;
import com.ximalaya.ting.android.host.model.DocRecommendModel;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.view.lrcview.LrcEntry;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Create by {jian.kang} on 2023/4/3
 *
 * <AUTHOR>
 */
public class DocRecommendDataManager {
    public long mCurTrackId;
    public long mCurAlbumId;

    public void loadDocRecommendData(long trackId, long albumId, List<LrcEntry> entryList, IDataCallBack<List<LrcEntry>> callBack) {
        clearRecommendData(entryList);
        // 回调没有，还往下走啥
        if (callBack == null) {
            return;
        }
        if (entryList == null || entryList.size() <= 0) {
            callBack.onSuccess(entryList);
            return;
        }

        HashMap<String, String> params = new HashMap<>();
        params.put("id", trackId + "");
        CommonRequestM.getDocRecommendData(trackId, params, new IDataCallBack<List<DocRecommendModel>>() {
            @Override
            public void onSuccess(@Nullable List<DocRecommendModel> data) {
                if (data == null || data.size() <= 0) {
                    callBack.onSuccess(entryList);
                } else {
                    afterDocRecommendLoaded(entryList, data, callBack);
                    mCurTrackId = trackId;
                    mCurAlbumId = albumId;
                }
            }

            @Override
            public void onError(int code, String message) {
                callBack.onSuccess(entryList);
            }
        });
    }

    public void afterDocRecommendLoaded(List<LrcEntry> entryList, List<DocRecommendModel> data, IDataCallBack<List<LrcEntry>> callBack) {
        if (entryList == null || entryList.size() <= 0 || data == null || data.size() <= 0 || callBack == null) {
            return;
        }

        new MyAsyncTask<Void, Void, List<LrcEntry>>() {
            @Override
            protected List<LrcEntry> doInBackground(Void... voids) {
                try {
                    setRecommendDataForEntryList(entryList, data);
                } catch (Throwable e) {
                    e.printStackTrace();
                }
                return entryList;
            }

            @Override
            protected void onPostExecute(List<LrcEntry> entryList) {
                super.onPostExecute(entryList);
                HandlerManager.postOnUIThread(() -> {
                    if (callBack != null) {
                        callBack.onSuccess(entryList);
                    }
                });
            }
        }.myexec();
    }

    public void setRecommendDataForEntryList(List<LrcEntry> entryList, List<DocRecommendModel> data) {
        if (entryList == null || entryList.size() <= 0 || data == null || data.size() <= 0) {
            return;
        }
        int textColor =  ColorUtil.covertColorToFixedSaturationAndLightness(PlayPageDataManager.getInstance().getBackgroundColor(),
                1.0f, 0.8f);

        for (DocRecommendModel model : data) {
            if (model == null || TextUtils.isEmpty(model.entity)) {
                continue;
            }
            setRecommendStyle(model, entryList, textColor);
        }
    }

    public void setRecommendStyle(DocRecommendModel recommendModel, List<LrcEntry> entryList, int textColor) {
        if (recommendModel == null || TextUtils.isEmpty(recommendModel.entity)) {
            return;
        }
        int curLrcEntryIndex = findCurEntryWithRecommendStartTime(entryList, recommendModel.recommendStartTime);
        if (curLrcEntryIndex < 0 || curLrcEntryIndex >= entryList.size()) {
            return;
        }
        LrcEntry lrcEntry = entryList.get(curLrcEntryIndex);
        if (lrcEntry == null || TextUtils.isEmpty(lrcEntry.getText())) {
            return;
        }

        if (lrcEntry.docRecommendList == null) {
            lrcEntry.docRecommendList = new ArrayList<>();
        }
        lrcEntry.docRecommendList.add(recommendModel);

        int startIndex = lrcEntry.getText().indexOf(recommendModel.entity);
        int endIndex = startIndex + recommendModel.entity.length();
        DocRecommendIndexModel indexModel = new DocRecommendIndexModel(startIndex, endIndex, textColor);
        if (lrcEntry.recommendPositionList == null) {
            lrcEntry.recommendPositionList = new ArrayList<>();
            indexModel.modelIndex = 0;
        } else {
            indexModel.modelIndex = lrcEntry.recommendPositionList.size();
        }
        lrcEntry.recommendPositionList.add(indexModel);
    }

    private int findCurEntryWithRecommendStartTime(List<LrcEntry> entryList, long startTime) {
        for (int i = 0; i < entryList.size(); i++) {
            if (i == entryList.size() - 1) {
                // 最后一段单独查找
                if (startTime >= entryList.get(i).getTime() && startTime <= entryList.get(i).getEntryListEndTime()) {
                    return i;
                } else {
                    return -1;
                }
            } else {
                if (startTime >= entryList.get(i).getTime() && startTime < entryList.get(i + 1).getTime()) {
                    return i;
                }
            }
        }
        return -1;
    }

    public boolean resetRecommendDataDocColor(List<LrcEntry> entryList, int textColor) {
        boolean changed = false;
        if (entryList == null || entryList.size() <= 0) {
            return changed;
        }

        for (LrcEntry entry : entryList) {
            if (entry == null || entry.recommendPositionList == null || entry.recommendPositionList.size() <= 0) {
                continue;
            }

            for (DocRecommendIndexModel indexModel : entry.recommendPositionList) {
                if (indexModel != null) {
                    if (textColor != indexModel.textColor) {
                        indexModel.textColor = textColor;
                        changed = true;
                    }
                }
            }
        }
        return changed;
    }

    // ai文稿存在复用机制，当含有推荐词的声音和tts或者歌词声音来回切换时，会存留有上次的数据
    private void clearRecommendData(List<LrcEntry> entryList) {
        if (entryList == null || entryList.size() <= 0) {
            return;
        }

        for (LrcEntry entry: entryList) {
            if (entry == null) {
                continue;
            }

            if (entry.recommendPositionList != null) {
                entry.recommendPositionList.clear();
            }

            if (entry.docRecommendList != null) {
                entry.docRecommendList.clear();
            }
        }
    }
}
