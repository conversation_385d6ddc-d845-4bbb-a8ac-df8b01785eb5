package com.ximalaya.ting.android.main.playpage.adapter

import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.ximalaya.ting.android.host.adapter.ChangeableTabAdapter
import com.ximalaya.ting.android.main.playpage.fragment.VideoPlayDetailFragment
import com.ximalaya.ting.android.main.playpage.fragment.VideoPlayTabFragmentNew

/**
 * Created by WolfXu on 2022/4/19.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class VideoPlayPageAdapter(private val mApi: VideoPlayTabFragmentNew.IApiForPlayDetailPage,
                           fm: FragmentManager, fraList: List<FragmentHolder>) :
    ChangeableTabAdapter(fm, fraList) {

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val `object` = super.instantiateItem(container, position)
        if (`object` is VideoPlayDetailFragment) {
            `object`.setVideoPageApi(mApi)
        }
        return `object`
    }
}