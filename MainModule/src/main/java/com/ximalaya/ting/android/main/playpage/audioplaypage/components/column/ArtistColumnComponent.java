package com.ximalaya.ting.android.main.playpage.audioplaypage.components.column;

import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.model.album.ArtistListInfo;
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.BaseColumnComponent;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager;
import com.ximalaya.ting.android.main.util.other.ArtistUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import androidx.annotation.Nullable;

/**
 * Created by ZhuPeipei on 2020-05-14 18:01.
 *
 * @Description: 音乐人栏目
 */
public class ArtistColumnComponent extends BaseColumnComponent {

    private FrameLayout mFlArtistAvatars;
    private TextView mTvName;
    private ArtistListInfo mArtistListInfo;
    private PlayPageMinorData mPlayPageMinorData;

    public boolean needShowThisComponent() {
        if (mPlayPageMinorData == null || mPlayPageMinorData.artistListInfo == null) {
            return false;
        }
        return true;
    }

    @Override
    public void onSoundInfoLoaded(PlayingSoundInfo soundInfo) {
        setCurSoundInfo(soundInfo);
        mPlayPageMinorData = null;
        if (soundInfo == null) {
            hide();
            return;
        }
        long trackId = getCurTrackId();
        PlayPageMinorDataManager.getInstance().getData(trackId, new IDataCallBack<PlayPageMinorData>() {
            @Override
            public void onSuccess(@Nullable PlayPageMinorData data) {
                mPlayPageMinorData = data;
                if (canUpdateUi()) {
                    if (!needShowThisComponent()
                            || (ChildProtectManager.isChildProtectOpen(mContext) && !canShowOnChildProtectMode())) {
                        hide();
                        return;
                    }
                    createOrShowView();
                    setDataForView(soundInfo);
                    if (mContentView != null) {
                        AutoTraceHelper.bindDataCallback(mContentView, mDataProvider);
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                if (canUpdateUi()) {
                    hide();
                }
            }
        });
    }

    @Override
    protected void setDataForView(PlayingSoundInfo soundInfo) {
        if (soundInfo == null || mPlayPageMinorData == null || mPlayPageMinorData.artistListInfo == null
                || ToolUtil.isEmptyCollects(mPlayPageMinorData.artistListInfo.getArtistResults())) {
            return;
        }
        mArtistListInfo = mPlayPageMinorData.artistListInfo;
        if (mContentView != null) {
            AutoTraceHelper.bindData(mContentView, "播放页", mPlayPageMinorData);
        }
        updateUI(mPlayPageMinorData.artistListInfo);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_play_column_artist;
    }

    @Override
    public void initUi() {
        mFlArtistAvatars = findViewById(R.id.main_fl_artist_avatars);
        mTvName = findViewById(R.id.main_tv_name);
        if (mContentView != null) {
            mContentView.setOnClickListener(v -> {
                if (mArtistListInfo == null) {
                    return;
                }
                ArtistUtil.gotoArtistPage(mArtistListInfo.getArtistResults(), getActivity(),
                        new IDataCallBack<ArtistListInfo.ArtistInfo>() {
                            @Override
                            public void onSuccess(@Nullable ArtistListInfo.ArtistInfo artistInfo) {
                                if (artistInfo != null) {
                                    new UserTracking()
                                            .setSrcPage("track")
                                            .setTrackId(getCurTrackId())
                                            .setSrcModule("musician")
                                            .setItem("musician")
                                            .setItemId(artistInfo.getId())
                                            .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK);
                                }
                            }

                            @Override
                            public void onError(int code, String message) {

                            }
                        });
            });
        }
    }

    private void updateUI(ArtistListInfo artistListInfo) {
        if (artistListInfo == null || ToolUtil.isEmptyCollects(artistListInfo.getArtistResults())) {
            return;
        }
        mFlArtistAvatars.removeAllViews();
        int avatarMargin = BaseUtil.dp2px(getActivity(), 35);
        for (int i = 0; i < artistListInfo.getArtistResults().size() && i < 2; i++) {
            ArtistListInfo.ArtistInfo artistInfo = artistListInfo.getArtistResults().get(i);
            if (artistInfo != null) {
                ImageView imageView = (ImageView) mLayoutInflater.inflate(
                        R.layout.main_view_artist_avatar_new, mFlArtistAvatars, false);
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) imageView.getLayoutParams();
                layoutParams.leftMargin = avatarMargin * i;
                imageView.setLayoutParams(layoutParams);
                mFlArtistAvatars.addView(imageView);
                ImageManager.from(mContext).displayImage(imageView, artistInfo.getSmallLogo(), R.drawable.main_default_musician_avatar);
            }
        }
        mTvName.setText(artistListInfo.getNameGroup());
    }
}
