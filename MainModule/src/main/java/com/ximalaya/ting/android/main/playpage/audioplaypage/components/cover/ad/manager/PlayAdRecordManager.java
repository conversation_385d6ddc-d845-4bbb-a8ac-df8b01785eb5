package com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.manager;

import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_FORWARD_VIDEO;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_PLAY_ICON;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_SOUND_PATCH;
import static com.ximalaya.ting.android.opensdk.model.PlayableModel.KIND_TRACK;
import static com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants.IRemoveAdHintBenefit.REMOVE_AD_HINT_BENEFIT_STRONG;

import android.app.Activity;
import android.text.TextUtils;

import androidx.core.util.Pair;

import com.google.common.base.Splitter;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd;
import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.adsdk.model.AdModel;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.ad.BackgroundListenerAdManager;
import com.ximalaya.ting.android.host.manager.ad.ForwardVideoManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.AdClientRequestFrequencyHelper;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.statistic.FreeAdTimeManager;
import com.ximalaya.ting.android.host.model.ad.AdCollectData;
import com.ximalaya.ting.android.host.model.ad.AdCollectDataShowTime;
import com.ximalaya.ting.android.host.model.ad.AdDownUpPositionModel;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.trace.SoundPatchAdTrace;
import com.ximalaya.ting.android.host.util.PlayPageStyleUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.adModule.manager.PlayAdStateRecordManager;
import com.ximalaya.ting.android.host.manager.ad.WebViewPreloadManager;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.model.PlayAdRecordParams;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.NewPlayPageUtil;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.Map;

/**
 * Created by le.xin on 2020/6/12.
 * 播放页广告上报
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class PlayAdRecordManager {
    private Pair<Advertis, Long> mLastRecordTime;

    private long mRequestTime;

    public void startRequestTime() {
        mRequestTime = System.currentTimeMillis();
    }

    public long getRequestTime() {
        return mRequestTime;
    }

    public void resetRecordTime() {
        mLastRecordTime = null;
    }

    private Advertis mCurAdvertis;

    // 上次展示时长
    public boolean recordAdShowTime(Advertis mSoundAd, String disappearType) {
        if (mSoundAd == null
                || mLastRecordTime == null
                || mLastRecordTime.first != mSoundAd
                || mLastRecordTime.second == null
                || TextUtils.isEmpty(disappearType)
                || mSoundAd.isRecordedShowTime()
                // 老版播放页,如果soundType为弹幕不上报这些
                || (IAdConstants.IAdPlayVersion.PLAY_VERSION_OLD.equals(AdManager.getAdPlayVersion()) && mSoundAd.getSoundType() == Advertis.TYPE_DANMU)) {
            return false;
        }

        if (mSoundAd.isPreviewAd()) {
            return false;
        }
        mSoundAd.setRecordedShowTime(true);
        AdCollectDataShowTime adCollectDataShowTime = new AdCollectDataShowTime();
        adCollectDataShowTime.setDisappearType(disappearType);
        adCollectDataShowTime.setAdPlayVersion(AdManager.getAdPlayVersion());
        adCollectDataShowTime.setShowTimeMs((int) (System.currentTimeMillis() - mLastRecordTime.second));
        adCollectDataShowTime.setAdItemId(mSoundAd.getAdid() + "");
        adCollectDataShowTime.setResponseId(mSoundAd.getResponseId() + "");
        adCollectDataShowTime.setCommonReportMap(mSoundAd.getCommonReportMap());
        adCollectDataShowTime.setUbtReportMap(mSoundAd.getUbtReportMap());
        adCollectDataShowTime.setClickPostMap(mSoundAd.getClickPostMap());
        adCollectDataShowTime.setClickRequestMethod(mSoundAd.getClickRequestMethod());
        adCollectDataShowTime.setLogType(AppConstants.AD_LOG_TYPE_SHOW_TIME);
        adCollectDataShowTime.setAdScene(mSoundAd.getAdScene());
        adCollectDataShowTime.setNewPlayPageVersion(NewPlayPageUtil.getNewPageParams());
        adCollectDataShowTime.setClickRewardType(mSoundAd.getClickRewardType());
        if (AdManager.isForwardVideo(mSoundAd)) {
            adCollectDataShowTime.setPositionName(AD_POSITION_NAME_FORWARD_VIDEO);
        } else if(mSoundAd.getShowstyle() == Advertis.IMG_SHOW_TYPE_PLAY_ICON_DANMU) {
            adCollectDataShowTime.setPositionName(AD_POSITION_NAME_PLAY_ICON);
        } else {
            adCollectDataShowTime.setPositionName(AdPositionIdManager.getPositionNameByPositionId(mSoundAd.getAdPositionId()));
        }
        adCollectDataShowTime.setPositionId(mSoundAd.getAdPositionId());
        CommonRequestM.statOnlineAd(adCollectDataShowTime);

        mLastRecordTime = null;
        return true;
    }

    public void recordShowSuccess(IAbstractAd abstractAd,
                                  PlayAdRecordParams playAdRecordParams) {
        if (abstractAd == null || abstractAd.getAdvertis() == null) {
            return;
        }

        Advertis ad = abstractAd.getAdvertis();
        mLastRecordTime = Pair.create(ad, System.currentTimeMillis());
        if (AdManager.isThirdAd(abstractAd)) {
            MMKVUtil.getInstance().saveLong(PreferenceConstantsInOpenSdk.KEY_AD_EXPOSURE_SOUND_PATCH_TIME, System.currentTimeMillis());
        }
        PlayAdStateRecordManager.getInstance().onShowSuccess(ad);
        AdClientRequestFrequencyHelper.INSTANCE.saveExposureCache();
        WebViewPreloadManager.getInstance().preloadWhenAdExposure(ad);

        boolean isVideoAd = AdManager.isVideoAd(ad) && !AdManager.isThirdAd(ad);

        // 表示声音贴片上报一次,展示图片上报一次
        if (ad.isXmul()
                // 暂停和二次曝光场景返回了有声贴片，但是不播放声音时，需要上报tingShow
                || (!TextUtils.isEmpty(ad.getSoundUrl()) && (ad.isPausedRequestAd() || ad.isDuringPlay()))
                // 没有声音贴片并且不是视频贴片
                || (TextUtils.isEmpty(ad.getSoundUrl()) && !isVideoAd)
                // 如果是视频广告并且展示的是静态图表示视频下载失败了
                || (isVideoAd && playAdRecordParams != null && playAdRecordParams.getShowType() == Advertis.SHOW_TYPE_STATIC)) {
            //
            if (ad.isPausedRequestAd() && ad.isShowedToRecorded()) {
                return;
            }

            if(AdManager.isForwardVideo(ad)) {
                int benefitType;
                if (FreeAdTimeManager.getLimitTime(MainApplication.getMyApplicationContext()) > 0) {
                    benefitType = 4; // 免广告期间， 权益快到期
                } else {
                    benefitType = 3; // 非免广告期间， 转换前
                }
                AdManager.adRecord(MainApplication.getMyApplicationContext(), ad,
                        AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SHOW_OB,
                                AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                .showType(playAdRecordParams != null ?
                                        playAdRecordParams.getShowType() : Advertis.SHOW_TYPE_STATIC)
                                .isDisplayedInScreen(1)
                                .setBenefitType(benefitType)
                                .newPlayPageVersion(NewPlayPageUtil.getNewPageParams())
                                .prompt("0").build());
                return;
            }

            ad.setShowedToRecorded(true);

            String positionName = AdPositionIdManager.getPositionNameByPositionId(ad.getAdPositionId());
            if(ad.getShowstyle() == Advertis.IMG_SHOW_TYPE_PLAY_ICON_DANMU || AdManager.isLiveDanmuAd(ad)) {
                positionName = AppConstants.AD_POSITION_NAME_PLAY_ICON;
            }

            if (TextUtils.equals(IAdConstants.IAdPositionId.PAOPAO_ACTIVITY_ENTRY, ad.getPositionId() + "")) {
                // 推啊 类型活动
                positionName = AppConstants.AD_POSITION_NAME_PAOPAO_ACTIVITY_ENTRY;
            }

            traceWhenAdClickOrExposure(ad, false);
            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                    ad, AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW, positionName)
                            .showType(playAdRecordParams != null ?
                                    playAdRecordParams.getShowType() : Advertis.SHOW_TYPE_STATIC)
                            .adPlayVersion(AdManager.getAdPlayVersion())
                            .playPageRevision(PlayPageStyleUtil.getNewPageStyle() + "")
                            .newPlayPageVersion(NewPlayPageUtil.getNewPageParams())
                            .setNewResponseId(ad.getNewResponseId())
                            .setIsAdxCacheMaterial(ad.isAdxCacheMaterial() ? 1 : 0)
                            .clickRewardType(ad.getClickRewardType())
                            .isDisplayedInScreen(1).build());
            SoundPatchAdTrace.reportRealShow(ad);
        }
    }

    // 声音互动广告独立播放器及浮层曝光上报
    public void recordInteractSoundAdShowSuccess(Advertis advertis, int durationShowSrc) {
        if (advertis == null) {
            return;
        }
        int gtDurationShowType;
        if (advertis.getDurationPicStyle() == Advertis.DURATION_PIC_STYLE_ANCHOR) {
            gtDurationShowType = 3;
        } else {
            gtDurationShowType = !TextUtils.isEmpty(advertis.getGtDurationPic()) ? 1 : 2;
        }
        if (durationShowSrc  == 1) {
            if (BackgroundListenerAdManager.getInstance().isLastSoundAdHasShowToRecord()) {
                return; // 独立播放器不重复曝光
            }
            BackgroundListenerAdManager.getInstance().setLastSoundAdHasShowToRecord(true);
        }
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                advertis, AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW, AD_POSITION_NAME_SOUND_PATCH)
                        .showType(Advertis.SHOW_TYPE_STATIC)
                        .adPlayVersion(AdManager.getAdPlayVersion())
                        .playPageRevision(PlayPageStyleUtil.getNewPageStyle() + "")
                        .newPlayPageVersion(NewPlayPageUtil.getNewPageParams())
                        .isDisplayedInScreen(1)
                        .durationShowSrc(durationShowSrc)
                        .gtDurationShowType(gtDurationShowType)
                        .build());
    }

    public void recordInteractSoundAdClose(Advertis advertis, int durationShowSrc) {
        if (advertis == null) {
            return;
        }
        int gtDurationShowType;
        if (advertis.getDurationPicStyle() == Advertis.DURATION_PIC_STYLE_ANCHOR) {
            gtDurationShowType = 3;
        } else {
            gtDurationShowType = !TextUtils.isEmpty(advertis.getGtDurationPic()) ? 1 : 2;
        }
        AdManager.postRecord(()-> {
            AdCollectData adCollectData = AdManager.thirdAdToAdCollect(MainApplication.getMyApplicationContext(),
                    advertis,
                    new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_SOUND_TINGCLOSE,
                            AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId()))
                            .playPageRevision(PlayPageStyleUtil.getNewPageStyle() + "")
                            .newPlayPageVersion(NewPlayPageUtil.getNewPageParams())
                            .isDisplayedInScreen(1)
                            .durationShowSrc(durationShowSrc)
                            .gtDurationShowType(gtDurationShowType)
                            .action("skip")
                            .adPlayVersion(AdManager.getAdPlayVersion()).build());
            CommonRequestM.statOnlineAd(adCollectData);
        });
    }

    public void interactSoundAdClick(IAbstractAd abstractAd, int durationShowSrc, String action, boolean isOnlyRecord) {
        if (abstractAd == null) {
            return;
        }
        Advertis advertis = abstractAd.getAdvertis();
        if(advertis == null) {
            return;
        }
        int gtDurationShowType;
        if (advertis.getDurationPicStyle() == Advertis.DURATION_PIC_STYLE_ANCHOR) {
            gtDurationShowType = 3;
        } else {
            gtDurationShowType = !TextUtils.isEmpty(advertis.getGtDurationPic()) ? 1 : 2;
        }
        AdReportModel.Builder builder = AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SOUND_CLICK, AD_POSITION_NAME_SOUND_PATCH)
                .showType(Advertis.SHOW_TYPE_STATIC)
                .adPlayVersion(AdManager.getAdPlayVersion())
                .isDisplayedInScreen(1)
                .action(action)
                .onlyClickRecord(isOnlyRecord)
                .durationShowSrc(durationShowSrc)
                .gtDurationShowType(gtDurationShowType);

        builder.playPageRevision(PlayPageStyleUtil.getNewPageStyle() + "");
        builder.newPlayPageVersion(NewPlayPageUtil.getNewPageParams());
        AdManager.hanlderSoundAdClick(MainApplication.getMyApplicationContext(), advertis,
                builder.build());
    }

    private void traceWhenAdClickOrExposure(Advertis advertis, boolean isClick) {
        if (advertis == null) {
            return;
        }
        String realLink = advertis.getRealLink();
        if (realLink == null) {
            return;
        }
        if (!realLink.contains("iting://open")) {
            return;
        }
        if (!(realLink.contains("track_id") || realLink.contains("album_id"))) {
            return;
        }
        int soundType = advertis.getSoundType();
        boolean isHorizontalAd = (soundType == Advertis.TYPE_HORIZONTAL_LARGE_AD) ||
                (soundType == Advertis.TYPE_HORIZONTAL_LARGE_DANMU_AD) ||
                (soundType == Advertis.TYPE_HORIZONTAL_LARGE_FLLOW_HAVE_SOUND) ||
                (soundType == Advertis.TYPE_HORIZONTAL_LARGE_FLLOW) ||
                (soundType == Advertis.TYPE_HORIZONTAL_LARGE_GIF_AD) ||
                (soundType == Advertis.TYPE_HORIZONTAL_LARGE_GIF_DANMU_AD);
        boolean isPaoPaoAd = (soundType == Advertis.TYPE_NEW_VERSION_PAOPAO) ||
                (soundType == Advertis.TYPE_DANMU) ||
                (advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_PLAY_ICON_DANMU);
        if (!(isHorizontalAd || isPaoPaoAd)) {
            return;
        }

        boolean isRecommendAlbum = realLink.contains("album_id");
        String recommendAnchorId = getRecommendAnchorId(realLink, isRecommendAlbum);

        XMTraceApi.Trace trace = new XMTraceApi.Trace().put("currPage","newPlay");
        if (isClick) {
            trace.click(isPaoPaoAd ? 29173 : 29171);
        } else {
            trace.setMetaId(isPaoPaoAd ? 29174 : 29172).setServiceId("slipPage");
        }
        if (isRecommendAlbum) {
            trace.put("albumId", recommendAnchorId);
        } else {
            trace.put("trackId", recommendAnchorId);
        }
        if (advertis.getXmRequestIds() != null) {
            trace.put("xmRequestId", advertis.getXmRequestIds());
            trace.put("contentId", advertis.getAdid() + "");
            trace.put("contentType", "soundPatch");
        }
        if (XmPlayerManager.getInstance(ToolUtil.getCtx()) != null) {
            PlayableModel track = XmPlayerManager.getInstance(ToolUtil.getCtx()).getCurrSound();
            if (track != null && track instanceof Track) {
                if (KIND_TRACK.equals(track.getKind())) {
                    trace.put("currTrackId", track.getDataId() + "");
                }
                SubordinatedAlbum album = ((Track) track).getAlbum();
                if (album != null) {
                    trace.put("currAlbumId", album.getAlbumId() + "");
                }
            }
        }
        trace.createTrace();
    }

    private String getRecommendAnchorId(String realLink, boolean isAlbum) {
        String params = realLink.substring(realLink.indexOf("?") + 1, realLink.length());
        Map<String, String> split = Splitter.on("&").withKeyValueSeparator("=").split(params);
        if (isAlbum) {
            return split.get("album_id");
        } else {
            return split.get("track_id");
        }
    }

    public void onAdClick(IAbstractAd abstractAd, PlayAdRecordParams playAdRecordParams,
                          PlayForwardViewHelper forwardViewHelper) {
        if (abstractAd == null) {
            return;
        }

        Advertis advertis = abstractAd.getAdvertis();

        // 前插视频跳转到激励视频
        if (AdManager.isForwardVideo(advertis)) {
            int benefitType;
            if (FreeAdTimeManager.getLimitTime(MainApplication.getMyApplicationContext()) > 0) {
                benefitType = 4; // 免广告期间， 权益快到期
            } else {
                benefitType = 3; // 非免广告期间， 转换前
            }

            AdReportModel.Builder builder = AdReportModel.newBuilder(
                    AppConstants.AD_LOG_TYPE_CLICK_OB,
                    AD_POSITION_NAME_FORWARD_VIDEO)
                    .prompt("0")
                    .adPlayVersion(AdManager.getAdPlayVersion())
                    .newPlayPageVersion(NewPlayPageUtil.getNewPageParams())
                    .ignoreTarget(true)
                    .setBenefitType(benefitType);

            if(playAdRecordParams != null) {
                updateDownloadPercentXY(builder, playAdRecordParams.getDownUpPositionModel());
            }

            AdManager.adRecord(MainApplication.getMyApplicationContext(), advertis,
                    builder.build());

            Activity activity = MainApplication.getTopActivity();
            if (!ToolUtil.activityIsValid(activity)) {
                activity = MainApplication.getMainActivity();
            }

            if(forwardViewHelper != null) {
                ForwardVideoManager.getInstance().lookVideo(activity, 1, REMOVE_AD_HINT_BENEFIT_STRONG,
                        forwardViewHelper.getRewardVideoCallBack(advertis, null), advertis);
            }
            return;
        }

        if (AdManager.isThirdAd(advertis) && abstractAd instanceof AbstractThirdAd) {
            AdManager.handlerGDTAd((AbstractThirdAd) abstractAd, advertis,
                    MainApplication.getTopActivity(), null,
                    AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SOUND_CLICK,
                            AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId()))
                            .adPlayVersion(AdManager.getAdPlayVersion())
                            .newPlayPageVersion(NewPlayPageUtil.getNewPageParams())
                            .clickRewardType(advertis.getClickRewardType())
                            .build());
            return;
        }

        if(advertis == null) {
            return;
        }

        boolean isInteractClick =
                playAdRecordParams != null && playAdRecordParams.isInteractClick();

        String logType = AppConstants.AD_LOG_TYPE_SOUND_CLICK;
        if (isInteractClick) {
            logType = AppConstants.AD_LOG_TYPE_SOUND_INTERACTI_CLICK;
        } else if (advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_PLAY_ICON_DANMU) {
            logType = AppConstants.AD_LOG_TYPE_SITE_CLICK;
        }

        int showType = playAdRecordParams != null ? playAdRecordParams.getShowType() :
                Advertis.SHOW_TYPE_STATIC;
        if (playAdRecordParams != null && playAdRecordParams.isDazzlingClick()) {
            showType = Advertis.SHOW_TYPE_STATIC;
        }

        boolean ignoreTarget = playAdRecordParams != null && playAdRecordParams.isIgnoreTagert();

        String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
        if(advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_PLAY_ICON_DANMU) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_ICON;
        }
        if (TextUtils.equals(IAdConstants.IAdPositionId.PAOPAO_ACTIVITY_ENTRY, advertis.getPositionId() + "")) {
            // 推啊 类型活动
            positionName = AppConstants.AD_POSITION_NAME_PAOPAO_ACTIVITY_ENTRY;
            logType = AppConstants.AD_LOG_TYPE_SITE_CLICK;
        }

        AdReportModel.Builder builder = AdReportModel.newBuilder(logType, positionName)
                .showType(showType)
                .adPlayVersion(AdManager.getAdPlayVersion())
                .ignoreTarget(ignoreTarget)
                .adDownUpPositionModel(playAdRecordParams != null ?
                        playAdRecordParams.getDownUpPositionModel() : null);
        if(playAdRecordParams != null) {
            updateDownloadPercentXY(builder, playAdRecordParams.getDownUpPositionModel());
            if (AdManager.isAdSupportDowloadInfoFloat(advertis)) {
                builder.downloadDirect(playAdRecordParams.isClickInButton());
            }
        }
        builder.playPageRevision(PlayPageStyleUtil.getNewPageStyle() + "");
        builder.newPlayPageVersion(NewPlayPageUtil.getNewPageParams());
        if (advertis.getFinishSeconds() != null) {
            builder.setFinishSeconds(advertis.getFinishSeconds());
        }
        if (advertis.isAdxCacheMaterial()) {
            builder.setIsAdxCacheMaterial(1);
        }
        if (advertis.getNewResponseId() != 0) {
            builder.setNewResponseId(advertis.getNewResponseId());
        }
        builder.clickRewardType(advertis.getClickRewardType());
        traceWhenAdClickOrExposure(advertis, true);
        if (advertis.getAnimationType() == Advertis.ANIMATION_TYPE_LARGE_COVER_AND_AUTO_OPEN_WEB
            || advertis.getAnimationType() == Advertis.ANIMATION_TYPE_AUTO_OPEN_WEB
            || advertis.getAnimationType() == Advertis.ANIMATION_TYPE_AUTO_OPEN_WEB_LARGE_AD ) {
            builder.autoPull(1);
        }
        AdManager.hanlderSoundAdClick(MainApplication.getMyApplicationContext(), advertis,
                builder.build());
    }

    private static void updateDownloadPercentXY(AdReportModel.Builder builder,
                                                AdDownUpPositionModel downUpPositionModel) {
        if(builder == null || downUpPositionModel == null
                || downUpPositionModel.getDownPercentX() == 0
                || downUpPositionModel.getDownPercentY() == 0) {
            return;
        }
        builder.xy(downUpPositionModel.getDownPercentX(), downUpPositionModel.getDownPercentY());
    }

    public void recordClose(Advertis advertis, boolean isAutoClose) {
        if (advertis == null) {
            return;
        }

        if (advertis == mCurAdvertis) {
            Logger.e("-------msg", " -----recordClose ------ -- - - -- ---  物料已经上报过了， 不再上报， 拦截 " + mCurAdvertis);
            return;
        }
        mCurAdvertis = advertis;

        if (advertis != null) {
            Logger.i("-------msg", " -----recordClose 视频总时长： " + advertis.getTotalSeconds()  + " ， 视频已播放时长： " + advertis.getFinishSeconds()
                    +  " ，是否自动关闭贴片 ： "+ isAutoClose + ", advertis = " + advertis);
        }

        AdManager.postRecord(()-> {
            AdCollectData adCollectData = AdManager.thirdAdToAdCollect(MainApplication.getMyApplicationContext(),
                    advertis,
                    new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_SOUND_TINGCLOSE,
                            AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId()))
                            .playPageRevision(PlayPageStyleUtil.getNewPageStyle() + "")
                            .newPlayPageVersion(NewPlayPageUtil.getNewPageParams())
                            .adPlayVersion(AdManager.getAdPlayVersion()).build());
            if (advertis != null && advertis.getTotalSeconds() != null) {
                adCollectData.setTotalSeconds(advertis.getTotalSeconds());
            }
            if (advertis != null && advertis.getFinishSeconds() != null) {
                adCollectData.setFinishSeconds(advertis.getFinishSeconds());
            }

            adCollectData.setAutoCloseSoundPatch(isAutoClose);

            if (AdManager.isForwardVideo(advertis)) {
                // 前插视频广告
                adCollectData.setPositionName(AD_POSITION_NAME_FORWARD_VIDEO);
            } else if(advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_PLAY_ICON_DANMU) {
                // 播放页icon样式弹幕
                adCollectData.setPositionName(AD_POSITION_NAME_PLAY_ICON);
            } else {
                adCollectData.setPositionName(AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId()));
            }
            adCollectData.setAdScene(advertis.getAdScene());
            adCollectData.setClickRewardType(advertis.getClickRewardType());
            CommonRequestM.statOnlineAd(adCollectData);
        });
    }
}
