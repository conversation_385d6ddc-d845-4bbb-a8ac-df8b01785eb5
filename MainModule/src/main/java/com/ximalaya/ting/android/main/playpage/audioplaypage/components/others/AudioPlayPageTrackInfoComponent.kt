package com.ximalaya.ting.android.main.playpage.audioplaypage.components.others

import android.app.Activity
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.Rect
import android.net.Uri
import android.os.SystemClock
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.AbsListView
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback
import com.ximalaya.ting.android.host.listener.IFollowDialogCallback
import com.ximalaya.ting.android.host.manager.account.AnchorCollectManage
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockVipTrackManager
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam
import com.ximalaya.ting.android.host.model.payment.UniversalPayment
import com.ximalaya.ting.android.host.model.play.AnchorRewardInfo
import com.ximalaya.ting.android.host.model.play.CommonGuidanceInfo
import com.ximalaya.ting.android.host.model.play.PlayPageMinorData
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.model.play.YellowZoneModel
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.VersionUtil
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.ShadowView
import com.ximalaya.ting.android.host.view.popupwindow.CustomPopWindow
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.newAnchorSpaceFragment
import com.ximalaya.ting.android.main.dialog.universal.RequestMaterial
import com.ximalaya.ting.android.main.dialog.universal.UniversalPaymentActionsDialog
import com.ximalaya.ting.android.main.fragment.find.other.rank.TrackListFragment
import com.ximalaya.ting.android.main.manager.playPage.PlayPageMarkPointManager
import com.ximalaya.ting.android.main.mine.extension.isRealVisible
import com.ximalaya.ting.android.main.mine.extension.visible
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageSubscribeFirstManager
import com.ximalaya.ting.android.main.playpage.audioplaypage.ColumnComponentsManager
import com.ximalaya.ting.android.main.playpage.audioplaypage.CoverComponentsManager
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.BaseComponent
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.remote.IScrollListenerCallBack
import com.ximalaya.ting.android.main.playpage.audioplaypage.isExtremelyLargeDevice
import com.ximalaya.ting.android.main.playpage.internalservice.IAudioPlayFragmentService
import com.ximalaya.ting.android.main.playpage.internalservice.IAudioPlayPageTrackInfoComponentService
import com.ximalaya.ting.android.main.playpage.internalservice.IBelowTitleOperationPositionComponentService
import com.ximalaya.ting.android.main.playpage.internalservice.IDocOnCoverComponentService
import com.ximalaya.ting.android.main.playpage.internalservice.IXPlayFragmentService
import com.ximalaya.ting.android.main.playpage.internalservice.OnFollowGuideVisibleChangedListener
import com.ximalaya.ting.android.main.playpage.manager.PlayGuideManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager
import com.ximalaya.ting.android.main.playpage.util.AudioPlayPageAlbumBuyManager
import com.ximalaya.ting.android.main.playpage.util.PlayCommentUtil
import com.ximalaya.ting.android.main.playpage.util.PlayTtsUtil
import com.ximalaya.ting.android.main.util.CommonUtil
import com.ximalaya.ting.android.main.view.FreeListenCoverView
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

/**
 * Created by WolfXu on 2022/6/24.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class AudioPlayPageTrackInfoComponent : BaseComponent(), OnFollowGuideVisibleChangedListener {

    companion object {
        fun newInstanceAndInit(fragment: BaseFragment2, vAncestorView: View): AudioPlayPageTrackInfoComponent {
            val component = AudioPlayPageTrackInfoComponent()
            component.onCreate(fragment)
            component.init(vAncestorView)
            return component
        }
    }

    private val mIvCover: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<ImageView>(R.id.main_iv_track_cover)
    }
    private val mTvTrackTitle: TextView? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<TextView>(R.id.main_tv_track_title)
    }
    private val mTvAlbumTitle: TextView? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<TextView>(R.id.main_tv_album_title)
    }
    private val mFreeListenCover: FreeListenCoverView? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<FreeListenCoverView?>(R.id.main_vv_free_listen_cover)
    }
    private val mTvSubscribeBtn: TextView? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<TextView>(R.id.main_tv_subscribe_btn)
    }
    private val mTvReadBookBtnOnAlbumView: View? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<View>(R.id.main_tv_read_book_with_album_title)
    }
    private val mGroupAlbum: Group? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<Group>(R.id.main_group_album)
    }
    /*private val mIvSwitchCoverBtnBg: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        findViewById<ImageView>(R.id.main_iv_switch_cover_btn_bg)
    }*/
    private var mVAuthorRootView: View? = null
    private var mTvAuthor: TextView? = null
    private var mTvFollowBtn: TextView? = null
    private var mTvRewardBtn: TextView? = null
    private var mTvOpenRewardBtn: TextView? = null
    private var mTvReadBookBtnOnAuthorView: TextView? = null
    private var mSpaceReadBookOnAuthorView: View? = null
    private var mCurBelowTitleView: View? = null
    private var mOnClickListener: View.OnClickListener? = null
    private var mPlayPageMinorData: PlayPageMinorData? = null
    private var mRewardIting: String? = null // 跳打赏弹窗
    private var mLastTrackIdOnPause: Long? = null
    private var mAuthorViewFirstShowAfterDataLoaded = true
    private var mAlbumViewFirstShowAfterDataLoaded = true
    private var mRewardFirstShowAfterDataLoaded = true
    private var mShowingContentChangeListener: ((Boolean) -> Unit)? = null
    private var mOnAudioPlayFragmentScrollStateChangeListener: IScrollListenerCallBack? = null
    private var mFreeListenPopup: CustomPopWindow? = null

    private val mShowSubscribeCategories by lazy(LazyThreadSafetyMode.NONE) {
        val config = ConfigureCenter.getInstance().getString(
            CConstants.Group_toc.GROUP_NAME,
            CConstants.Group_toc.ITEM_PLAYPAGE_SUBSCRIBE_SWITCH, ""
        )
        config?.split(",")?.filter { it.isNotEmpty() }
    }

    private val guideListener = object : PlayGuideManager.OnManuscriptShowListener {
        override fun onNext() {
            checkShowFreeListenGuide()
        }
    }

    fun init(vAncestorView: View) {
        mContentView = vAncestorView.findViewById(R.id.main_vg_top_track_info)
        adapterForExtremelyLargeDevice()
        initOnClickListener()
        AlbumEventManage.addListener(mAlbumSubscribeListener)
        AnchorFollowManage.getSingleton().addFollowListener(mFollowAnchorListener)
        registerService()
        mFreeListenCover?.setOnShowListener(object : FreeListenCoverView.OnShowListener {
            override fun show() {
                HandlerManager.postOnUIThreadDelay({
                    checkShowFreeListenGuide()
                }, 250)
            }

            override fun hide() {
                if (mFreeListenPopup?.popupWindow?.isShowing == true) {
                    mFreeListenPopup?.dismiss()
                    mFreeListenPopup = null
                }
            }
        })
        PlayGuideManager.register(guideListener)
    }

    private fun registerService() {
        PlayPageInternalServiceManager.getInstance().registerService(
            IAudioPlayPageTrackInfoComponentService::class.java,
            object : IAudioPlayPageTrackInfoComponentService {
                override fun isAnchorViewShow(): Boolean {
                    return mContentView?.visibility == View.VISIBLE && mCurBelowTitleView == mVAuthorRootView
                }

                override fun isContentViewShow(): Boolean {
                    return mContentView?.visibility == View.VISIBLE
                }

                override fun addShowingContentChangeListener(listener: (isAnchorViewShow: Boolean) -> Unit) {
                    mShowingContentChangeListener = listener
                }

                override fun getTrackCoverPositionInWindow(): Rect? {
                    mIvCover?.let {
                        if (it.width > 0 && it.height > 0) {
                            val location = IntArray(2)
                            it.getLocationInWindow(location)
                            val rect = Rect(location[0], location[1], location[0] + it.width,
                                location[1] + it.height)
                            return rect
                        }
                    }
                    return null
                }

                override fun getTrackCoverSize(): Int {
                    return mIvCover?.height ?: 0
                }

                override fun setFreeListenCoverData(trackId: Long, data: CommonGuidanceInfo?): Boolean {
                    mFreeListenCover?.setData(trackId, data, true)
                    return true
                }

                override fun setShowState(isOpen: Boolean) {
                    mFreeListenCover?.setShowState(isOpen)
                }
            })

        PlayPageInternalServiceManager.getInstance().getService(
            IBelowTitleOperationPositionComponentService::class.java
        )?.addFollowGuideVisibleChangedListener(this)
    }

    private fun adapterForExtremelyLargeDevice() {
        // 只在初始化时修改，后续不再需要修改的属性可以放在这里，否则要放在modifyUiProperties里
        if (isExtremelyLargeDevice()) {
//            mTvTrackTitle?.setTextSize(TypedValue.COMPLEX_UNIT_PX, getAudioPlayAdaptSizeBySp(15))
//            mTvAlbumTitle?.setTextSize(TypedValue.COMPLEX_UNIT_PX, getAudioPlayAdaptSizeBySp(12))
//            mTvSubscribeBtn?.setTextSize(TypedValue.COMPLEX_UNIT_PX, getAudioPlayAdaptSizeBySp(10))
//            mIvCover?.layoutParams?.let {
//                val size = getAudioPlayAdaptSizeByDp(60).toInt()
//                it.width = size
//                it.height = size
//            }
        }
    }

    override fun onSoundInfoLoaded(soundInfo: PlayingSoundInfo?) {
        super.onSoundInfoLoaded(soundInfo)
        if (mContentView?.visibility == View.VISIBLE) {
            mAuthorViewFirstShowAfterDataLoaded = true
            mAlbumViewFirstShowAfterDataLoaded = true
            bindData()
            loadMinorData(soundInfo)
            statTrackTitleViewed()
            mFreeListenCover?.setData(soundInfo?.trackInfo?.trackId ?: 0L, CommonGuidanceInfo.findTarget(soundInfo, CommonGuidanceInfo.TYPE_TIME_LIMIT_LISTEN_TIPS), true)
        }
    }

    override fun onResume() {
        super.onResume()
        updateUIBelowTitle()
        updateColor(backgroundColor)
        val soundChanged = mLastTrackIdOnPause != PlayPageDataManager.getInstance().curTrackId
        val soundInfoIsEmpty = curSoundInfo == null
        HandlerManager.postOnUIThreadDelay({
            // 如果声音改变了的话，接口请求完成会上报，这里就不上报了
            if (!soundChanged && !soundInfoIsEmpty) {
                traceAuthorViewViewed()
                statTrackTitleViewed()
            }
            eTraceFollowView()
            eTraceSubscribeView()
        }, 250)
        mFreeListenCover?.doOnResume()
        registerScrollListener()
    }

    override fun onPause() {
        super.onPause()
        mLastTrackIdOnPause = curTrackId
        unregisterScrollListener()
    }

    override fun onDestroy() {
        super.onDestroy()
        PlayGuideManager.unRegister()
        AnchorFollowManage.getSingleton().removeFollowListener(mFollowAnchorListener)
        AlbumEventManage.removeListener(mAlbumSubscribeListener)
        PlayPageInternalServiceManager.getInstance().unRegisterService(IAudioPlayPageTrackInfoComponentService::class.java)
        PlayPageInternalServiceManager.getInstance().unRegisterService(IBelowTitleOperationPositionComponentService::class.java)
    }

    private fun loadMinorData(soundInfo: PlayingSoundInfo?) {
        val soundInfoTrackId = soundInfo?.trackInfo?.trackId
        if (mPlayPageMinorData == null || soundInfoTrackId != mPlayPageMinorData?.trackId) {
            mPlayPageMinorData = null
            val trackId = soundInfoTrackId ?: 0
            if (trackId > 0) {
                PlayPageMinorDataManager.getInstance().getData(trackId, mMinorDataCallback, false)
            }
        }
    }

    private fun bindData() {
        val soundInfo = curSoundInfo ?: return
        val trackInfo = soundInfo.trackInfo ?: return
        mTvTrackTitle?.text = trackInfo.title
        var coverUrl = trackInfo.validCover
        if (coverUrl.isNullOrEmpty()) {
            coverUrl = soundInfo.albumInfo?.validCoverUrl
        }
        ImageManager.from(context).displayImage(mIvCover, coverUrl, com.ximalaya.ting.android.host.R.drawable.host_default_album)
        updateUIBelowTitle()
    }

    private fun updateUIBelowTitle() {
        if (curSoundInfo == null) {
            return
        }
        val newShowView = when {
            shouldShowAlbumView() -> mGroupAlbum
            else -> initAuthorViews()
        }
        setCurBelowTitleView(newShowView)
        hideIfNotCurBelowTitleView(mGroupAlbum)
        hideIfNotCurBelowTitleView(mVAuthorRootView)
        mCurBelowTitleView.visible(View.VISIBLE)
        var trackTitleBottomToTopId = mCurBelowTitleView?.id
        when (mCurBelowTitleView) {
            mGroupAlbum -> {
                updateAlbumViewUI()
                trackTitleBottomToTopId = R.id.main_tv_subscribe_btn
            }
            mVAuthorRootView -> {
                updateAuthorViewUI()
            }
        }
        val trackTitleLp = mTvTrackTitle?.layoutParams as? ConstraintLayout.LayoutParams
        trackTitleLp?.bottomToTop = trackTitleBottomToTopId
    }

    private fun setCurBelowTitleView(newShowView: View?) {
        if (newShowView != mCurBelowTitleView) {
            mCurBelowTitleView = newShowView
            mShowingContentChangeListener?.invoke(mCurBelowTitleView == mVAuthorRootView)
        }
    }

    private fun hideIfNotCurBelowTitleView(view: View?) {
        view ?: return
        if (mCurBelowTitleView != view) {
            view.visible(View.INVISIBLE)
        }
    }

    private fun updateColor(backgroundColor: Int) {
        when (mCurBelowTitleView) {
            mGroupAlbum -> updateAlbumViewColor(backgroundColor)
            mVAuthorRootView -> {
                updateAuthorViewColor()
                updateRewardBtnColor()
            }
        }
        // mIvSwitchCoverBtnBg?.colorFilter = PorterDuffColorFilter(backgroundColor, PorterDuff.Mode.SRC_IN)
    }

    private fun initAuthorViews(): View? {
        if (mVAuthorRootView != null) {
            return mVAuthorRootView
        }
        val vsAuthor: ViewStub? = findViewById(R.id.main_vs_author)
        if (vsAuthor?.parent == null) {
            return null
        }
        mVAuthorRootView = vsAuthor.inflate()
        mVAuthorRootView?.let {
            mTvAuthor = it.findViewById<TextView?>(R.id.main_tv_author_name)
            mTvFollowBtn = it.findViewById<TextView?>(R.id.main_tv_follow_btn)
            mTvRewardBtn = it.findViewById<TextView?>(R.id.main_tv_reward_btn)
            mTvOpenRewardBtn = it.findViewById<TextView?>(R.id.main_tv_open_reward_btn)
            mTvReadBookBtnOnAuthorView = it.findViewById(R.id.main_tv_read_book)
            mSpaceReadBookOnAuthorView = it.findViewById(R.id.main_space_read_book)
            mTvAuthor?.compoundDrawables?.get(2)?.mutate()?.alpha = 255
        }
        mTvFollowBtn?.setOnClickListener(mOnClickListener)
        mTvRewardBtn?.setOnClickListener(mOnClickListener)
        mTvOpenRewardBtn?.setOnClickListener(mOnClickListener)
        mTvAuthor?.setOnClickListener(mOnClickListener)
        mTvReadBookBtnOnAuthorView?.setOnClickListener(mOnClickListener)
        return mVAuthorRootView
    }

    private fun isNovel(): Boolean {
        return curSoundInfo?.albumInfo?.categoryId == ColumnComponentsManager.CATEGORY_BOOK.toLong() &&
                curSoundInfo?.ebookInfo?.landingUrl != null
    }

    private fun isTts(): Boolean {
        return curSoundInfo?.docInfo?.docType == PlayingSoundInfo.DocInfo.TYPE_TTS
    }

    private fun updateAuthorViewUI() {
        mTvAuthor?.text = curSoundInfo?.userInfo?.nickname
        updateAnchorFollowStatus()
        updateAuthorViewColor()
        val readBookBtnVisibility = if (isNovel() || isTts()) View.VISIBLE else View.GONE
        mTvReadBookBtnOnAuthorView.visible(readBookBtnVisibility)
        mSpaceReadBookOnAuthorView.visible(readBookBtnVisibility)
        if (mAuthorViewFirstShowAfterDataLoaded) {
            mAuthorViewFirstShowAfterDataLoaded = false
            traceAuthorViewViewed()
            eTraceFollowView()
        }
    }

    private fun updateAnchorFollowStatus() {
        if (ChildProtectManager.isChildProtectOpen(mContext)) {
            mTvFollowBtn?.visibility = View.GONE
            return
        }
        val isFollow = curSoundInfo?.otherInfo?.isFollowed
        // 关注引导中有关注按钮，关注引导显示时，这里就不显示了
        mTvFollowBtn.visible(if (isFollow == true || canShowGuideText() || isLoginUsersSound()) View.GONE else View.VISIBLE)
        checkShowReward(mPlayPageMinorData)
    }

    override fun onChange(show: Boolean) {
        if (show) {
            HandlerManager.postOnUIThreadDelay({ updateAnchorFollowStatus() }, 200)
        }
    }

    private fun canShowGuideText(): Boolean {
        return PlayPageInternalServiceManager.getInstance().getService(IBelowTitleOperationPositionComponentService::class.java)?.isShowFollowGuide() == true
    }

    private fun updateAuthorViewColor() {
        if (mVAuthorRootView?.visibility != View.VISIBLE) {
            return
        }
        mTvFollowBtn?.let {
            if (it.visibility == View.VISIBLE) {
                it.setTextColor(backgroundColor)
                it.compoundDrawables[0]?.mutate()?.colorFilter =
                    PorterDuffColorFilter(backgroundColor, PorterDuff.Mode.SRC_IN)
            }
        }
        mTvAuthor?.let {
            if (it.visibility == View.VISIBLE) {
                it.setTextColor(ColorUtil.covertColorToFixedSaturationAndLightness(
                    backgroundColor, 1f, 0.8f))
                it.compoundDrawables[2]?.mutate()?.colorFilter =
                    PorterDuffColorFilter(foregroundColor, PorterDuff.Mode.SRC_IN)
            }
        }
    }

    private fun updateRewardBtnColor() {
        mTvRewardBtn?.let {
            if (it.visibility == View.VISIBLE) {
                it.setTextColor(backgroundColor)
                it.compoundDrawables[0]?.mutate()?.colorFilter =
                    PorterDuffColorFilter(backgroundColor, PorterDuff.Mode.SRC_IN)
            }
        }
        mTvOpenRewardBtn?.let {
            if (it.visibility == View.VISIBLE) {
                it.setTextColor(backgroundColor)
                it.compoundDrawables[0]?.mutate()?.colorFilter =
                    PorterDuffColorFilter(backgroundColor, PorterDuff.Mode.SRC_IN)
            }
        }
    }

    /**
     * 判断是否显示打赏打call
     */
    private fun checkShowReward(minorData: PlayPageMinorData?) {
        if (minorData == null) {
            hideReward()
            return
        }
        if (minorData.anchorRewardInfo != null) {
            mRewardIting = minorData.anchorRewardInfo!!.url
        }
        if (mVAuthorRootView == null || mVAuthorRootView?.visibility != View.VISIBLE
            || mTvFollowBtn?.visibility == View.VISIBLE || canShowGuideText()) {
            hideReward()
            return
        }
        if (minorData.anchorRewardInfo != null
            && AnchorRewardInfo.REWARD_CLOSED != minorData.anchorRewardInfo!!.status) {
            when {
                AnchorRewardInfo.REWARD_OPEN == minorData.anchorRewardInfo!!.status -> {
                    if (mTvRewardBtn?.visibility != View.VISIBLE) {
                        mTvRewardBtn?.visibility = View.VISIBLE
                    }
                    if (mRewardFirstShowAfterDataLoaded) {
                        mRewardFirstShowAfterDataLoaded = false
                        trackViewShown(mTvRewardBtn) { trackRewardShown(true) }
                    }
                    updateRewardBtnColor()
                    mTvOpenRewardBtn?.visibility = View.GONE
                }
                isLoginUsersSound() -> {
                    // 登录用户的音频，未开通
                    mTvRewardBtn?.visibility = View.GONE
                    if (mTvOpenRewardBtn?.visibility != View.VISIBLE) {
                        mTvOpenRewardBtn?.visibility = View.VISIBLE
                    }
                    if (mRewardFirstShowAfterDataLoaded) {
                        mRewardFirstShowAfterDataLoaded = false
                        trackViewShown(mTvOpenRewardBtn) { trackRewardShown(false) }
                    }
                }
                else -> {
                    hideReward()
                }
            }
        } else {
            hideReward()
        }
    }

    private fun trackViewShown(view: TextView?, trackShown: () -> Unit) {
        val time = SystemClock.elapsedRealtime()
        if (view?.tag is Long) {
            val tagTime = view.tag as Long
            if (time - tagTime > 500) {
                view.tag = time
                trackShown()
            }
        } else {
            view?.tag = time
            trackShown()
        }
    }

    private fun checkShowFreeListenGuide() {
        // 封面引导优先展示
        if (!MMKVUtil.getInstance()
                .getBoolean(PreferenceConstantsInHost.KEY_MANUSCRIPT_GUIDE_SHOW, false)
        ) {
            return
        }
        if (mFragment == null || mFragment.activity == null || mFreeListenCover == null || mFragment?.activity?.window == null) {
            return
        }
        val context = mFragment.context ?: return
        if (!mFragment.isRealVisable || !mFragment.canUpdateUi() || !ViewStatusUtil.viewIsRealShowing(
                mFreeListenCover
            )
        ) {
            return
        }
        if (ChildProtectManager.isChildMode(mFragment.activity)) {
            return
        }
        if (!VersionUtil.hasUpgrade()) {
            return
        }
        if (MMKVUtil.getInstance()
                .getBoolean(PreferenceConstantsInHost.KEY_MANUSCRIPT_COVER_GUIDE_SHOW, false)) {
            return
        }
        if (ViewUtil.haveDialogIsShowing(mFragment.activity)) {
            return
        }
        val sv = ShadowView(context)
        val popView = View.inflate(mFragment.activity, R.layout.main_popup_free_listen_tip, null)
        val popWindow = CustomPopWindow.PopupWindowBuilder(mFragment.context)
            .enableOutsideTouchableDissmiss(true)
            .setView(popView)
            .setOnDissmissListener {
                ViewUtil.setHasDialogShow(
                    false
                )
                (mFragment?.activity?.window?.decorView as? ViewGroup)?.removeView(sv)
            }
            .create()
        sv.setMode(ShadowView.MODE_FLAT)
        val location = IntArray(2)
        mFreeListenCover!!.getLocationOnScreen(location)
        val focus: ShadowView.Focus = sv.Focus(
            ShadowView.Focus.SHAPE_ROUND_RECT,
            location[0] + mFreeListenCover!!.measuredWidth / 2f,
            location[1] + mFreeListenCover!!.measuredHeight / 2f,
            mFreeListenCover!!.measuredWidth,
            mFreeListenCover!!.measuredHeight
        )
        focus.radius = BaseUtil.dp2px(mContentView!!.context, mFreeListenCover!!.measuredWidth / 2f)
        sv.addFocus(focus)
        val view = mFragment?.activity?.window?.decorView ?: return
        (view as ViewGroup).addView(sv)
        popView.findViewById<View>(R.id.main_tv_next).setOnClickListener { v: View? ->
            popWindow.dismiss()
            ViewUtil.setHasDialogShow(false)
        }
        popWindow.showAsDropDown(
            mFreeListenCover,
            (focus.x - BaseUtil.dp2px(context, 43f)).toInt(),
            BaseUtil.dp2px(mFragment.context, 8f),
        )
        mFreeListenPopup = popWindow
        ViewUtil.setHasDialogShow(true)
        MMKVUtil.getInstance()
            .saveBoolean(PreferenceConstantsInHost.KEY_MANUSCRIPT_COVER_GUIDE_SHOW, true)
    }

    private fun hideReward() {
        mTvRewardBtn.visible(View.GONE)
        mTvOpenRewardBtn.visible(View.GONE)
    }

    private fun isLoginUsersSound(): Boolean {
        val loginUId = UserInfoMannage.getUid()
        return loginUId > 0 && loginUId == curSoundInfo?.userInfo?.uid
    }

    private fun shouldShowAlbumView(): Boolean {
        val soundInfo = curSoundInfo
        val showCategory =
            mShowSubscribeCategories?.contains(soundInfo?.trackInfo?.categoryId?.toString())
                ?: false
        val showSubscribeFirst = AudioPlayPageSubscribeFirstManager.needShowSubscribeFirst(soundInfo)
        return (showCategory || showSubscribeFirst) && soundInfo?.albumInfo?.isFavorite == false
                && soundInfo.albumInfo?.title != null
                && soundInfo.trackInfo?.decoupleStatus != Track.TYPE_SINGLE
    }

    private fun updateAlbumViewUI() {
        val soundInfo = curSoundInfo ?: return
        mTvAlbumTitle?.text = soundInfo.albumInfo?.title
        updateSubscribeStatus()
        updateAlbumViewColor(backgroundColor)
        if (PlayTtsUtil.isTtsAPlus()) {
            mTvReadBookBtnOnAlbumView.visible(View.GONE)
        } else {
            mTvReadBookBtnOnAlbumView.visible(if ((isNovel() || isTts())) View.VISIBLE else View.GONE)
        }
        if (mAlbumViewFirstShowAfterDataLoaded) {
            mAlbumViewFirstShowAfterDataLoaded = false
        }
    }

    private fun updateSubscribeStatus() {
        mTvSubscribeBtn.visible(if (curSoundInfo?.albumInfo?.isFavorite == true) View.INVISIBLE else View.VISIBLE)
    }

    private fun updateAlbumViewColor(backgroundColor: Int) {
        mTvAlbumTitle?.setTextColor(
            ColorUtil.covertColorToFixedSaturationAndLightness(
                backgroundColor, 1f, 0.8f
            )
        )
        mTvAlbumTitle?.compoundDrawables?.get(2)?.mutate()?.colorFilter =
            PorterDuffColorFilter(foregroundColor, PorterDuff.Mode.SRC_IN)
        mTvSubscribeBtn?.let {
            it.setTextColor(backgroundColor)
            it.compoundDrawables[0]?.mutate()?.colorFilter = PorterDuffColorFilter(
                backgroundColor,
                PorterDuff.Mode.SRC_IN
            )
        }
    }

    override fun onThemeColorChanged(foregroundColor: Int, backgroundColor: Int) {
        super.onThemeColorChanged(foregroundColor, backgroundColor)
        if (mContentView?.visibility == View.VISIBLE) {
            updateColor(backgroundColor)
        }
    }

    private val mAlbumSubscribeListener by lazy(LazyThreadSafetyMode.NONE) {
        AlbumEventManage.CollectListener { isSubscribed, id ->
            handleSubscribeEvent(isSubscribed, id)
        }
    }

    private fun handleSubscribeEvent(isSubscribed: Boolean, id: Long) {
        if (id == curAlbumId) {
            curSoundInfo?.albumInfo?.isFavorite = isSubscribed
            // 不可见的话，onResume时候会再处理
            if (mFragment?.isRealVisable == true && canUpdateUi()) {
                updateUIBelowTitle()
            }
        }
    }

    private fun initOnClickListener() {
        // 已经创建过，那就return
        if (mOnClickListener != null) {
            return
        }
        mOnClickListener = View.OnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@OnClickListener
            }
            when (v) {
                mTvSubscribeBtn -> {
                    doSubscribe()
                    statInteractionAction("订阅")
                }
                mTvRewardBtn, mTvOpenRewardBtn -> {
                    toRewardDialog()
                    statInteractionAction("打赏")
                }
                mTvFollowBtn -> {
                    followAnchor()
                    statInteractionAction("关注")
                }
                mTvAlbumTitle -> {
                    toAlbumPage()
                }
                mTvAuthor -> {
                    toAuthorPage(v)
                }
                mTvTrackTitle -> handleTrackTitleClick()
                mTvReadBookBtnOnAlbumView, mTvReadBookBtnOnAuthorView -> toBookPage()
                mIvCover -> {
//                    setOpenDoc(context, curSoundInfo, false)
//                    // 新声音播放页_封面-切换  点击事件
//                    XMTraceApi.Trace()
//                        .click(43141) // 用户点击时上报
//                        .put("contentType", PlayPageManuscriptViewUtil.getTraceContentTypeByDocType(curSoundInfo)) // 声音｜视频｜文稿｜广告......
//                        .put("contentId", "$curTrackId")
//                        .put("currTrackId", "$curTrackId")
//                        .put("currAlbumId", "$curAlbumId")
//                        .put("Item", "图")
//                        .put("currPage", "newPlay")
//                        .createTrace()
                    // do Nothing for now
                }
                mFreeListenCover -> {
                    toFreeListenSth()
                }
            }
        }
        mTvSubscribeBtn?.setOnClickListener(mOnClickListener)
        mTvAlbumTitle?.setOnClickListener(mOnClickListener)
        mTvTrackTitle?.setOnClickListener(mOnClickListener)
        mTvReadBookBtnOnAlbumView?.setOnClickListener(mOnClickListener)
        mIvCover?.setOnClickListener(mOnClickListener)
        mFreeListenCover?.setOnClickListener(mOnClickListener)
    }

    private fun toBookPage() {
        val docOnCoverComponentService = PlayPageInternalServiceManager.getInstance()
            .getService(IDocOnCoverComponentService::class.java)
        docOnCoverComponentService?.jumpNovelOrTTS()
    }

    private fun handleTrackTitleClick() {
        if (PlayCommentUtil.isDecoupleTrack(curSoundInfo)) {
            toTrackListPage()
        } else {
            toAlbumPage()
        }
        XMTraceApi.Trace()
            .click(45710)
            .put("trackId", curTrackId.toString())
            .put("currTrackId", curTrackId.toString())
            .put("currPage", "newPlay")
            .put("status", if (CoverComponentsManager.isCoverFullScreen()) "全屏" else "半屏")
            .put("content", "声音标题")
            .put("Item", mTvTrackTitle?.text?.toString() ?: "")
            .createTrace()
    }

    private fun statTrackTitleViewed() {
        if (mTvTrackTitle.isRealVisible() && curSoundInfo != null) {
            // 新声音播放页-顶部悬浮  控件曝光
            XMTraceApi.Trace()
                .setMetaId(45711)
                .setServiceId("slipPage")
                .put("trackId", curTrackId.toString())
                .put("currTrackId", curTrackId.toString())
                .put("currPage", "newPlay")
                .put("status", if (CoverComponentsManager.isCoverFullScreen()) "全屏" else "半屏")
                .put("content", "声音标题")
                .put(XmRequestIdManager.IS_DUPLICATE_VIEW, "2")
                .put("Item", mTvTrackTitle?.text?.toString() ?: "")
                .createTrace()
        }
    }

    private fun toTrackListPage() {
        if (mFragment != null) {
            mFragment.startFragment(
                TrackListFragment.newInstanceByAnchor(PlayCommentUtil.getCurAnchorId(curSoundInfo),
                    "全部声音"))
        }
    }

    private fun toAlbumPage() {
        val albumInfo = curSoundInfo?.albumInfo ?: return
        AlbumEventManage.startMatchAlbumFragment(
            albumInfo.albumId, AlbumEventManage.FROM_ALBUM_BELONG,
            ConstantsOpenSdk.PLAY_FROM_TAB_ALBUM, null, null, -1, activity
        )
        // 新声音播放页-引导订阅专辑box-引导语  点击事件
        XMTraceApi.Trace()
            .click(51716) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("currTrackId", "${curSoundInfo?.trackInfo?.trackId ?: 0}")
            .put("currAlbumId", "${curSoundInfo?.albumInfo?.albumId ?: 0}")
            .put("anchorId", "$curAnchorId")
            .put("categoryId", curSoundInfo?.albumInfo?.categoryId?.toString())
            .put("styleItem", "固定式")
            .createTrace()
    }

    private fun toAuthorPage(view: View, tipsText: CharSequence = "") {
        val userInfo = curSoundInfo?.userInfo ?: return
        mFragment.startFragment(
            newAnchorSpaceFragment(
                userInfo.uid, -1
            ), view
        )
        trackAnchorClick(tipsText.toString())
    }

    private fun trackAnchorClick(tipsText: String) {
        curSoundInfo?.trackInfo?.let {
            // 新声音播放页-主播  点击事件
            XMTraceApi.Trace()
                .click(45712)
                .put("currTrackId", it.trackId.toString())
                .put("anchorId", getAnchorId().toString())
                .put("currPage", "newPlay")
                .put("tipsText", tipsText)
                .put("status", if (CoverComponentsManager.isCoverFullScreen()) "全屏" else "半屏")
                .createTrace()
        }
    }

    private fun toFreeListenSth() {
        mFreeListenCover?.data?.let { commonGuidanceInfo ->
            val btn: YellowZoneModel.ButtonInfo? = CommonUtil.safelyGetItem(commonGuidanceInfo.buttonList, 0)
            if (null == btn) {
                PlayPageMarkPointManager.PlayPageV1.Companion.markPointOnClickFreeListenCountDown(curSoundInfo, "小图", "免费听")
                CustomToast.showSuccessToast("免费时长已达上限，明日再来")
                return
            }
            PlayPageMarkPointManager.PlayPageV1.Companion.markPointOnClickFreeListenCountDown(curSoundInfo, "小图", btn.text)
            if (!UserInfoMannage.hasLogined() && btn.needLoginCheck()) {
                UserInfoMannage.gotoLogin(BaseApplication.getMyApplicationContext())
                return
            }
            when (btn.actionId) {
                YellowZoneModel.ButtonInfo.ACTION_ID_PURCHASE -> {
                    AudioPlayPageAlbumBuyManager.Companion.create(mFragment).doOnPurchaseActionIdClicked(btn, curSoundInfo, curAlbumId)
                }
                YellowZoneModel.ButtonInfo.ACTION_ID_JUMP_URL -> {
                    ToolUtil.clickUrlAction(mFragment, btn.url, mFreeListenCover)
                }
                YellowZoneModel.ButtonInfo.ACTION_ID_UNIVERSAL_PAYMENT -> {
                    curSoundInfo?.let { playingSoundInfo ->
                        val requestMaterial: RequestMaterial = RequestMaterial(curAlbumId, UniversalPayment.SOURCE_PLAY_BEFORE_SAMPLE)
                            .setTrack(playingSoundInfo.trackInfo2TrackM())
                        requestMaterial.shutAutoPerform()
                        UniversalPaymentActionsDialog.requestAndShowDialog(mFragment, requestMaterial)
                    }
                }
                YellowZoneModel.ButtonInfo.ACTION_ID_GO_WATCH_AD -> {
                    curSoundInfo?.let { playingSoundInfo ->
                        if (AdMakeVipLocalManager.getInstance().needToGuideToLogin(true)) {
                            return
                        }
                        val track: Track = playingSoundInfo.trackInfo2TrackM()
                        val unlockMaterial: AdMakeVipLocalManager.UnlockMaterial = AdMakeVipLocalManager.UnlockMaterial()
                        unlockMaterial.vipFreeTestType = AdUnLockVipTrackManager.getNewestVipFreeType()
                        // 其实此个IsAutoPerform的含义是判断是否 非弹窗进入的广告
                        unlockMaterial.isAutoPerform = true
                        unlockMaterial.callDialogListener = com.ximalaya.ting.android.main.manager.freelisten.CommonUtil.buildDefaultAdUnlockCallDialogListener(track)
                        AdMakeVipLocalManager.getInstance().unlockTrack(track, PlayerConstants.PLAY_METHOD_MANUAL_PLAY, unlockMaterial)
                    }
                }
                else -> {}
            }
        }
    }

    private fun getAnchorId(): Long {
        return if (curSoundInfo != null && curSoundInfo!!.userInfo != null) {
            curSoundInfo!!.userInfo!!.uid
        } else 0
    }

    /**
     * 打开喜点打赏底部弹窗
     */
    private fun toRewardDialog() {
        if (!checkIsChildProtectModeForFunction()) {
            return
        }
        val activity: Activity? = mFragment?.activity
        if (activity != null && !activity.isDestroyed && !activity.isFinishing &&
            mRewardIting?.startsWith("iting://") == true) {
            try {
                val uri = Uri.parse(mRewardIting)
                if (uri.queryParameterNames != null && !uri.queryParameterNames.contains("targetId")) {
                    val newRewardIting: String = if (mRewardIting!!.contains("?")) {
                        "$mRewardIting&targetId=$curTrackId"
                    } else {
                        "$mRewardIting?targetId=$curTrackId"
                    }
                    Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)?.functionAction
                        ?.handleIting(mFragment.activity, Uri.parse(newRewardIting))
                } else {
                    Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)
                        ?.functionAction?.handleIting(mFragment.activity, uri)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        // 打赏点击统计
        val soundInfo = curSoundInfo
        if (soundInfo != null) {
            XMTraceApi.Trace()
                .click(38026)
                .put("trackId", "" + (soundInfo.trackInfo?.trackId ?: 0))
                .put("currPage", "newPlay")
                .put(
                    "isOpen",
                    (mTvOpenRewardBtn?.visibility != View.VISIBLE).toString()
                )
                .createTrace()
        }
    }

    private fun followAnchor() {
        val soundInfo = curSoundInfo ?: return
        val otherInfo = soundInfo.otherInfo ?: return
        val userInfo = soundInfo.userInfo ?: return
        val isCurrentFollowed = otherInfo.isFollowed
        AnchorFollowManage.followV3WithLoginCheckDeferredAction(
            activity,
            userInfo.uid,
            isCurrentFollowed,
            AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE,
            1,
            object : IDataCallBack<Boolean?> {
                override fun onSuccess(`object`: Boolean?) {
                    if (!canUpdateUi()) {
                        return
                    }
                    if (`object` == null || soundInfo.otherInfo == null) {
                        return
                    }
                    soundInfo.otherInfo?.isFollowed = `object`
                    if (`object`) {
                        CustomToast.showSuccessToast("关注成功")
//                        if (DyncAbTestUtil.savaDyncPageValue && mFragment != null && !PlayFollowGuideDialogFragment.isPlayFollowGuideDialogFragmentShown()) {
//                            val bundle = Bundle()
//                            bundle.putLong(
//                                PlayFollowGuideDialogFragment.BUNDLE_KEY_ALBUM_ID,
//                                PlayCommentUtil.getCurAlbumId(soundInfo)
//                            )
//                            bundle.putLong(
//                                PlayFollowGuideDialogFragment.BUNDLE_KEY_TRACK_ID,
//                                PlayCommentUtil.getCurTrackId(soundInfo)
//                            )
//                            bundle.putLong(
//                                PlayFollowGuideDialogFragment.BUNDLE_KEY_ANCHOR_ID,
//                                PlayCommentUtil.getCurAnchorId(soundInfo)
//                            )
//                            PlayFollowGuideDialogFragment.newInstance(bundle)
//                                .show(mFragment.childFragmentManager, "")
//                        }
                    }
                    updateUIBelowTitle()
                }

                override fun onError(code: Int, message: String) {}
            }, true, "newPlay"
        )
        val trackFromPlayer = XmPlayerManager.getInstance(context).currSound as? Track
        // 新声音播放页-关注  点击事件
        val traceApi = XMTraceApi.Trace()
            .click(39183)
            .put("currTrackId", PlayCommentUtil.getCurTrackId(curSoundInfo).toString())
            .put("currAlbumId", PlayCommentUtil.getCurAlbumId(curSoundInfo).toString())
            .put("anchorId", PlayCommentUtil.getCurAnchorId(curSoundInfo).toString())
            .put("currPage", "newPlay")
            .put("moduleName", "页面关注按钮")
            .put("albumCategoryId", curSoundInfo?.albumInfo?.categoryId?.toString() ?: "")
            .put("status", if (CoverComponentsManager.isCoverFullScreen()) "全屏" else "半屏")
            .put("style", "顶部悬浮")
            .put("bizType", "${AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE}")
            .put("subBizType", "1")
        PlayPageInternalServiceManager.getInstance().getService(
            IXPlayFragmentService::class.java
        )?.let {
            traceApi.put("trackForm", if (it.isVideoMode) "video" else "track")
        }
        if (trackFromPlayer != null) {
            if (!trackFromPlayer.recTrack.isNullOrEmpty()) {
                traceApi.put("rec_track", trackFromPlayer.recTrack)
            }
            if (!trackFromPlayer.recSrc.isNullOrEmpty()) {
                traceApi.put("rec_src", trackFromPlayer.recSrc)
            }
        }
        traceApi.createTrace()
    }

    fun doSubscribe() {

        val album = curSoundInfo?.toAlbumM()
        album?.isFavorite = curSoundInfo?.albumInfo?.isFavorite ?: false
        AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(
            AlbumCollectParam(
                "newPlay",
                AnchorCollectManage.SUBSCRIBE_BIZ_TYPE_AUDIO_PLAY_50003,
                AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE,
                8,
                album
            ),
            mFragment, object : ICollectWithFollowStatusCallback {

                override fun getFollowSubBizType(): Int {
                    return 8
                }

                override fun onCollectSuccess(code: Int, isCollected: Boolean) {
                    curSoundInfo?.toAlbumM()?.isFavorite = isCollected
                    curSoundInfo?.albumInfo?.isFavorite = isCollected
                }

                override fun onError() {
                }

                override fun followDialogAction(status: Int) {
                    when (status) {
                        ICollectWithFollowStatusCallback.STATUS_SHOW -> {
//                            // 新声音播放页_关注  弹框展示
//                            XMTraceApi.Trace()
//                                .setMetaId(43115)
//                                .setServiceId("dialogView")
//                                .put("trackId", curTrackId.toString())
//                                .put("albumId", curAlbumId.toString())
//                                .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
//                                .put(
//                                    "albumCategoryId",
//                                    curSoundInfo?.albumInfo?.categoryId?.toString()
//                                )
//                                .put("currPage", "newPlay")
//                                .createTrace()
                        }
                        ICollectWithFollowStatusCallback.STATUS_FOLLOW_SUCCESS -> {
                            curSoundInfo?.otherInfo?.isFollowed = true
                            updateAnchorFollowStatus()
                        }
                    }
                }

                override fun getTrackId(): Long {
                    return curTrackId
                }

                override fun followDialogBtnClick(clickBtn: Int, extra: Any?) {
                    when (clickBtn) {
                        IFollowDialogCallback.CLICK_CLOSE -> {
//                            // 新声音播放页_关注-关闭  弹框控件点击
//                            XMTraceApi.Trace()
//                                .setMetaId(43118)
//                                .setServiceId("dialogClick")
//                                .put("trackId", curTrackId.toString())
//                                .put("albumId", curAlbumId.toString())
//                                .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
//                                .put(
//                                    "albumCategoryId",
//                                    curSoundInfo?.albumInfo?.categoryId?.toString()
//                                )
//                                .put("currPage", "newPlay")
//                                .createTrace()
                        }
                        IFollowDialogCallback.CLICK_FOLLOW -> {
//                            // 新声音播放页_关注-关注  弹框控件点击
//                            XMTraceApi.Trace()
//                                .setMetaId(43116)
//                                .setServiceId("dialogClick")
//                                .put("trackId", curTrackId.toString())
//                                .put("albumId", curAlbumId.toString())
//                                .put(
//                                    "albumCategoryId",
//                                    curSoundInfo?.albumInfo?.categoryId?.toString()
//                                )
//                                .put("item", if (extra == true) "关注" else "取消关注")
//                                .put("currPage", "newPlay")
//                                .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
//                                .createTrace()
                        }
                        IFollowDialogCallback.CLICK_I_KNOW -> {
//                            // 新声音播放页_关注-我知道了  弹框控件点击
//                            XMTraceApi.Trace()
//                                .setMetaId(43117)
//                                .setServiceId("dialogClick")
//                                .put("trackId", curTrackId.toString())
//                                .put("albumId", curAlbumId.toString())
//                                .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
//                                .put(
//                                    "albumCategoryId",
//                                    curSoundInfo?.albumInfo?.categoryId?.toString()
//                                )
//                                .put("currPage", "newPlay")
//                                .createTrace()
                        }
                    }
                }
            }, "newPlay"
        )
        // 新声音播放页-订阅  点击事件
        XMTraceApi.Trace()
            .click(43103)
            .put("trackId", curTrackId.toString())
            .put("albumId", curAlbumId.toString())
            .put("anchorId", curSoundInfo?.userInfo?.uid?.toString())
            .put("albumCategoryId", curSoundInfo?.albumInfo?.categoryId?.toString())
            .put("currPage", "newPlay")
            .createTrace()
    }

    private fun statInteractionAction(action: String) {
        // 新声音播放页-互动状态  点击事件
        XMTraceApi.Trace()
            .click(45667) // 用户点击时上报
            .put("currPage", "互动播放页")
            .put("Item", action) // 关注/订阅/打赏
            .put("anchorId", curAnchorId.toString())
            .put("currAlbumId", curAlbumId.toString())
            .put("currTrackId", curTrackId.toString())
            .createTrace()
    }

    private fun traceAuthorViewViewed() {
        if (curSoundInfo == null) {
            return
        }
        if (mVAuthorRootView.isRealVisible()) {
            // 新声音播放页-主播  控件曝光
            XMTraceApi.Trace()
                .setMetaId(45713)
                .setServiceId("slipPage")
                .put("currTrackId", curTrackId.toString())
                .put("anchorId", curAnchorId.toString())
                .put("currPage", "newPlay")
                .put(
                    XmRequestIdManager.XM_REQUEST_ID,
                    XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
                )
                .put(XmRequestIdManager.CONT_ID, curTrackId.toString())
                .put(XmRequestIdManager.CONT_TYPE, "newPlayAuthor")
                .createTrace()
        }
    }

    /**
     * 打赏曝光埋点
     * @param isOpen 是否开通打赏
     */
    private fun trackRewardShown(isOpen: Boolean) {
        curSoundInfo?.trackInfo?.let {
            XMTraceApi.Trace()
                .setMetaId(38027)
                .setServiceId("slipPage")
                .put("trackId", "" + it.trackId)
                .put("currPage", "newPlay")
                .put("isOpen", isOpen.toString())
                .put(XmRequestIdManager.CONT_ID, "" + it.trackId)
                .put(XmRequestIdManager.CONT_TYPE, "newPlayReward")
                .put(
                    XmRequestIdManager.XM_REQUEST_ID, XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
                )
                .createTrace()
        }
    }

    fun setVerticalAdShowTag(isShow: Boolean) {
        if (isShow) {
            mContentView?.setTag(R.id.main_play_vertical_ad_show_status, 1)
        } else {
            mContentView?.setTag(R.id.main_play_vertical_ad_show_status, 0)
        }
    }

    fun show() {
        val verticalAdTag = mContentView?.getTag(R.id.main_play_vertical_ad_show_status)
        if (verticalAdTag != null && verticalAdTag == 1) {
            return
            // 竖版贴片展示过程中不能显示该组件
        }
        mContentView.visible(View.VISIBLE)
        updateUIBelowTitle()
        updateColor(backgroundColor)
        mFreeListenCover?.isAllowToShow = true
        mFreeListenCover?.tryToShow()
    }

    fun gone() {
        mContentView.visible(View.GONE)
        mFreeListenCover?.isAllowToShow = false
        mFreeListenCover?.hide()
        setCurBelowTitleView(null)
    }

    private fun registerScrollListener() {
        if (mOnAudioPlayFragmentScrollStateChangeListener == null) {
            mOnAudioPlayFragmentScrollStateChangeListener = IScrollListenerCallBack { view, scrollState ->
                if (scrollState == AbsListView.OnScrollListener.SCROLL_STATE_IDLE) {
                    statTrackTitleViewed()
                    traceAuthorViewViewed()
                    eTraceFollowView()
                    eTraceSubscribeView()
                    if (mTvRewardBtn?.getGlobalVisibleRect(Rect()) == true) {
                        trackRewardShown(true)
                    }
                    if (mTvOpenRewardBtn?.getGlobalVisibleRect(Rect()) == true) {
                        trackRewardShown(false)
                    }
                }
            }
        }
        val playFragmentService = PlayPageInternalServiceManager.getInstance()
            .getService(IAudioPlayFragmentService::class.java)
        playFragmentService?.registerScrollChangeListener(
            mOnAudioPlayFragmentScrollStateChangeListener
        )
    }

    private fun unregisterScrollListener() {
        mOnAudioPlayFragmentScrollStateChangeListener?.let {
            val playFragmentService = PlayPageInternalServiceManager.getInstance()
                .getService(IAudioPlayFragmentService::class.java)
            playFragmentService?.unregisterScrollChangeListener(it)
        }
    }

    private val mMinorDataCallback by lazy(LazyThreadSafetyMode.NONE) {
        object : IDataCallBack<PlayPageMinorData> {
            override fun onSuccess(data: PlayPageMinorData?) {
                mPlayPageMinorData = data
                if (canUpdateUi()) {
                    updateUIBelowTitle()
                }
            }

            override fun onError(code: Int, message: String?) {
            }
        }
    }

    private val mFollowAnchorListener by lazy(LazyThreadSafetyMode.NONE) {
        AnchorFollowManage.IFollowAnchorListener { uid: Long, follow: Boolean ->
            val soundInfo = curSoundInfo ?: return@IFollowAnchorListener
            val userInfo = soundInfo.userInfo ?: return@IFollowAnchorListener
            val otherInfo = soundInfo.otherInfo ?: return@IFollowAnchorListener
            if (userInfo.uid == uid) {
                otherInfo.isFollowed = follow
                if (canUpdateUi()) {
                    updateUIBelowTitle()
                }
            }
        }
    }

    private fun eTraceFollowView() {
        if (mTvFollowBtn.isRealVisible()) {
            // 新声音播放页-关注（曝光）  控件曝光
            XMTraceApi.Trace()
                .setMetaId(51721)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newPlay")
                .put("currAlbumId", "${curSoundInfo?.albumInfo?.albumId ?: 0}")
                .put("currTrackId", "${curSoundInfo?.trackInfo?.trackId ?: 0}")
                .put("anchorId", "$curAnchorId")
                .put("categoryId", curSoundInfo?.albumInfo?.categoryId?.toString())
                .put("bizType", "${AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE}")
                .put("subBizType", "1")
                .createTrace()
        }
    }

    private fun eTraceSubscribeView() {
        if (mTvSubscribeBtn.isRealVisible()) {
            // 新声音播放页-订阅按钮  控件曝光
            XMTraceApi.Trace()
                .setMetaId(51715)
                .setServiceId("slipPage")
                .put("currPage", "newPlay")
                .put("currAlbumId", "${curSoundInfo?.albumInfo?.albumId ?: 0}")
                .put("currTrackId", "${curSoundInfo?.trackInfo?.trackId ?: 0}")
                .put("anchorId", "$curAnchorId")
                .put("categoryId", curSoundInfo?.albumInfo?.categoryId?.toString())
                .put(XmRequestIdManager.CONT_ID, "${curSoundInfo?.trackInfo?.trackId ?: 0}")
                .put(XmRequestIdManager.CONT_TYPE, "newPlaySubscribe")
                .put(
                    XmRequestIdManager.XM_REQUEST_ID,
                    XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE)
                )
                .createTrace()
        }
    }
}