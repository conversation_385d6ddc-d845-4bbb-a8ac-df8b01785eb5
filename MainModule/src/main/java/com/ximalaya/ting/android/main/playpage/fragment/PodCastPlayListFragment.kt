package com.ximalaya.ting.android.main.playpage.fragment

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Bundle
import android.os.IBinder
import android.os.RemoteException
import android.text.TextUtils
import android.util.Pair
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.google.gson.Gson
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef
import com.ximalaya.ting.android.host.manager.NewShowNotesManager
import com.ximalaya.ting.android.host.manager.PodCastPlayListSortManager
import com.ximalaya.ting.android.host.manager.ToListenManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.util.BarUtils
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.XmRequestPage.getPageUniqueRequestId
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.host.util.common.smoothScrollToPositionWithOffset
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.XMSmartRefreshLayout
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.host.view.rvlayoutmanager.LinearLayoutManagerWrapper
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.album.item.PodCastPlayListAdapter
import com.ximalaya.ting.android.main.adapter.album.item.PodCastPlayListAdapter.Companion.checkSoundsBelongSameAlbum
import com.ximalaya.ting.android.main.adapter.album.item.PodCastPlayListAdapter.Companion.getAlbumIdAndTitle
import com.ximalaya.ting.android.main.adapter.album.item.PodCastPlayListAdapter.Companion.getSourceFromStr
import com.ximalaya.ting.android.main.adapter.album.item.PodCastPlayListAdapter.Companion.isFromSpecialList
import com.ximalaya.ting.android.main.adapter.album.item.PodCastPlayListAdapter.Companion.isFromTingList
import com.ximalaya.ting.android.main.albumModule.album.album2.ShowNotesAlbumTrackAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager.mFragment
import com.ximalaya.ting.android.main.model.PodCastPlayListModel
import com.ximalaya.ting.android.main.playpage.dialog.PlayModePopupWindow
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.playy.dialog.YPlayListAndHistoryFragment
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList
import com.ximalaya.ting.android.opensdk.model.track.SimpleTrackForToListen
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.service.IXmDataCallback
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask
import com.ximalaya.ting.android.opensdk.util.ToListenUtil
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import java.util.concurrent.CopyOnWriteArrayList


class PodCastPlayListFragment : BaseFragment2(), OnRefreshLoadMoreListener {

    val mToBeRecyclerView: RecyclerView by lazy(LazyThreadSafetyMode.NONE) {
        findViewById(R.id.main_to_play_recyclerview)
    }
    private val mSmartRefreshLayout: XMSmartRefreshLayout by lazy(LazyThreadSafetyMode.NONE) {
        findViewById(R.id.main_refresh_layout)
    }

    private var mToBeAdapter: PodCastPlayListAdapter? = null
    var mXmRequestId = ""
    private var mLinearLayoutManagerWrapper: LinearLayoutManagerWrapper? = null
    var mNormalOriginalParams: HashMap<String, String>? = null

    companion object {
        const val TO_BE_PLAY_OK: Int = 0
        const val CURRENT_PLAYING_HEADER = 1
        const val CURRENT_PLAYING = 2
        const val TO_BE_PLAY_HEADER: Int = 3
        const val NORMAL_HEADER = 4
        const val NORMAL_PLAY_LIST_OK = 5

        fun getPlayModeDrawableResId(playMode: Int): Int {
            val drawableResId: Int = when (XmPlayListControl.PlayMode.getIndex(playMode)) {
                    XmPlayListControl.PlayMode.PLAY_MODEL_LIST -> R.drawable.main_play_page_new_play_mode_default
                    XmPlayListControl.PlayMode.PLAY_MODEL_SINGLE_LOOP -> R.drawable.main_play_page_new_play_mode_single
                    XmPlayListControl.PlayMode.PLAY_MODEL_RANDOM -> R.drawable.main_play_page_new_play_mode_random
                    XmPlayListControl.PlayMode.PLAY_MODEL_LIST_LOOP -> R.drawable.main_play_page_new_play_mode_loop
                    else -> R.drawable.main_play_page_new_play_mode_default
                }
            return drawableResId
        }


        fun showPlayModePop(context: Context, mPlayModeBtn: TextView, ivPlayMode: ImageView,
                            adapter: PodCastPlayListAdapter, fragment: PodCastPlayListFragment) {
            PlayModePopupWindow(context, mPlayModeBtn) { playMode: XmPlayListControl.PlayMode ->
                setPlayMode(context, playMode, mPlayModeBtn, ivPlayMode, adapter, fragment)
                null
            }
        }

        private fun setPlayMode(context: Context, playMode: XmPlayListControl.PlayMode, playModeBtn: TextView, ivPlayMode: ImageView,
                                adapter: PodCastPlayListAdapter, fragment: PodCastPlayListFragment) {
            val strModeName: Int = when (playMode) {
                XmPlayListControl.PlayMode.PLAY_MODEL_LIST -> R.string.main_play_model_list
                XmPlayListControl.PlayMode.PLAY_MODEL_SINGLE_LOOP -> R.string.main_play_model_single_loop
                XmPlayListControl.PlayMode.PLAY_MODEL_RANDOM -> R.string.main_play_model_random
                XmPlayListControl.PlayMode.PLAY_MODEL_LIST_LOOP -> R.string.main_play_model_list_loop
                else -> R.string.main_play_model_list
            }
            XmPlayerManager.getInstance(context).playMode = playMode
            PlayTools.savePlayModeToMmkv(context, playMode.ordinal)
            playModeBtn.setText(strModeName)
            ivPlayMode.setImageResource(getPlayModeDrawableResId(playMode.ordinal))
            CustomToast.showToast(
                context.resources.getString(
                    R.string.main_str_play_mode_toast,
                    context.resources.getString(strModeName)
                )
            )
            adapter.notifyDataSetChanged()
            fragment.initStickyHeaderData()
            val info = PlayPageDataManager.getInstance().soundInfo
            if (info?.trackInfo2TrackM() == null || info.toAlbumM() == null) {
                return
            }
            val track = info.trackInfo2TrackM()
            val album = info.toAlbumM()
            PlayPageDataManager.getInstance().notifyPlayModeChange()
            XMTraceApi.Trace()
                .click(17626)
                .put("currPage", "newPlay")
                .put("currTrackId", track.dataId.toString())
                .put("currAlbumId", album.id.toString())
                .put("anchorId", track.uid.toString())
                .put("categoryId", track.categoryId.toString())
                .put("item", context.resources.getString(strModeName))
                .put(XmRequestIdManager.XM_REQUEST_ID, getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .createTrace()
        }
    }

    override fun getPageLogicName(): String {
        return "PodCastPlayListFragment"
    }

    override fun initUi(savedInstanceState: Bundle?) {
        initPlayingTrackView()
        ToListenManager.mIsShowingToListenPage = true
        mToBeAdapter = PodCastPlayListAdapter(this@PodCastPlayListFragment)
        initStickyHeader()
        mToBeAdapter!!.onCreate()
        mSmartRefreshLayout.setOnRefreshLoadMoreListener(this)
        mSmartRefreshLayout.setFooterHeight(70f)
        mSmartRefreshLayout.setReboundDuration(50)
//        mSmartRefreshLayout.setIOnRefreshStateChangedListener {
//            val normalHeaderIndex = mToBeAdapter?.findFirstIndexOfType(NORMAL_HEADER)?: -1
//            if (normalHeaderIndex >= 0) {
//                val view = mLinearLayoutManagerWrapper?.findViewByPosition(normalHeaderIndex)
//                mToBeAdapter?.setFuckVisibility(!it, view)
//            }
//        }
        mToBeRecyclerView?.apply {
            this.adapter = mToBeAdapter
            mLinearLayoutManagerWrapper = LinearLayoutManagerWrapper(mContext, LinearLayoutManager.VERTICAL)
            this.layoutManager = mLinearLayoutManagerWrapper
            if (this.itemAnimator is SimpleItemAnimator) { //解决切换item闪烁问题
                (this.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
            }
            mToBeAdapter?.getItemTouchHelper()?.attachToRecyclerView(this)
            this.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    updateOutHeaderAndListHeader()
                }
            })
        }
        onPageLoadingCompleted(LoadCompleteType.LOADING)
    }


    private var mNormalHeaderLayout: View? = null
    private var mStickyHeaderTitle: TextView? = null
    private var mPlayModeBtn: TextView? = null
    private var mStickyHeaderSubTitle: TextView? = null
    private var mIvPlayMode: ImageView? = null
    private var mStickyHeaderTitleArrow: ImageView? = null
    private fun initStickyHeader() {
        mNormalHeaderLayout = findViewById(R.id.main_normal_header_area)
        mStickyHeaderTitle = findViewById(R.id.main_tv_header_title)
        mStickyHeaderTitleArrow = findViewById(R.id.main_iv_header_title_arrow)
        mStickyHeaderSubTitle = findViewById(R.id.main_tv_header_sub_title)
        mPlayModeBtn = findViewById(R.id.main_play_mode)
        mIvPlayMode = findViewById(R.id.main_iv_play_mode)
        mPlayModeBtn!!.setOnClickListener {
            showPlayModePop(mContext, mPlayModeBtn!!, mIvPlayMode!!, mToBeAdapter!!, this)
        }
        mIvPlayMode!!.setOnClickListener {
            showPlayModePop(mContext, mPlayModeBtn!!, mIvPlayMode!!, mToBeAdapter!!, this)
        }
        mStickyHeaderSubTitle!!.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            changePodCastListPlaySortUpWithToListen()
        }
    }

    fun initStickyHeaderData() {
        val tracks = mToBeAdapter?.getDataList()?.filter {
            it.listType == PodCastPlayListFragment.NORMAL_PLAY_LIST_OK
        }?.mapNotNull {
            it.track
        }
        if (tracks.isNullOrEmpty()) {
            return
        }
        ViewStatusUtil.setVisible(if (NewShowNotesManager.getPodCastSortCloseState()) View.INVISIBLE else View.VISIBLE, mStickyHeaderSubTitle)
        if (checkSoundsBelongSameAlbum(tracks) && !isFromTingList(tracks) && TextUtils.isEmpty(isFromSpecialList(tracks))) {
            val pair = getAlbumIdAndTitle(tracks)
            if (pair != null) {
                SpanUtils.with(mStickyHeaderTitle!!)
                    .append("来自：")
                    .append(pair.second)
                    .setClickSpan(mContext.resources.getColor(R.color.host_color_444444_dcdcdc), false) {
                        if (pair.first > 0) {
                            AlbumEventManage.startMatchAlbumFragment(
                                pair.first, AlbumEventManage.FROM_OTHER,
                                -1, null, null,
                                -1, mFragment?.activity
                            )
                        }
                    }
                    .create()
                mStickyHeaderTitleArrow!!.setOnClickListener {
                    if (pair.first > 0) {
                        AlbumEventManage.startMatchAlbumFragment(
                            pair.first, AlbumEventManage.FROM_OTHER,
                            -1, null, null,
                            -1, mFragment?.activity
                        )
                    }
                }
                mStickyHeaderTitleArrow!!.visibility = View.VISIBLE
            } else {
                mStickyHeaderTitle!!.text = getSourceFromStr(tracks)
                mStickyHeaderTitleArrow!!.visibility = View.GONE
            }
        } else {
            mStickyHeaderTitle!!.text = getSourceFromStr(tracks)
            mStickyHeaderTitleArrow!!.visibility = View.GONE
        }

        val sortByUp = isPodCastListPlaySortUpWithToListen()
        if (sortByUp) {
            mStickyHeaderSubTitle!!.text = "向上播"
            modeItemExplore("向上播")
            mStickyHeaderSubTitle!!.setCompoundDrawablesWithIntrinsicBounds(R.drawable.host_ic_standard_order_down_to_top_20, 0, 0, 0)
        } else {
            mStickyHeaderSubTitle!!.text = "向下播"
            modeItemExplore("向下播")
            mStickyHeaderSubTitle!!.setCompoundDrawablesWithIntrinsicBounds(R.drawable.host_ic_standard_order_top_to_down_20, 0, 0, 0)
        }

        val playMode = PlayTools.getSavedPlayMode(context)
        val strModeName: Int = when (XmPlayListControl.PlayMode.getIndex(playMode)) {
            XmPlayListControl.PlayMode.PLAY_MODEL_LIST -> R.string.main_play_model_list
            XmPlayListControl.PlayMode.PLAY_MODEL_SINGLE_LOOP -> R.string.main_play_model_single_loop
            XmPlayListControl.PlayMode.PLAY_MODEL_RANDOM -> R.string.main_play_model_random
            XmPlayListControl.PlayMode.PLAY_MODEL_LIST_LOOP -> R.string.main_play_model_list_loop
            else -> R.string.main_play_model_list
        }
        mPlayModeBtn!!.setText(strModeName)
        modeItemExplore(mContext.resources.getString(strModeName))
        mIvPlayMode!!.setImageResource(getPlayModeDrawableResId(playMode))
    }

    private var mPlayingRootView: View? = null
    private var mPlayingCoverIv: ImageView? = null
    private var mPlayingIvPlaying: ImageView? = null
    private var mPlayingIvPlayPanel: ImageView? = null
    private var mPlayingTrackTitleTv: TextView? = null
    private var mPlayingTrackRemainTimeTv: TextView? = null
    private var mPlayingLottieIcon: XmLottieAnimationView? = null
    private fun initPlayingTrackView() {
        mPlayingRootView = findViewById(R.id.main_item_to_be_root)
        mPlayingCoverIv = findViewById(R.id.main_iv_cover)
        mPlayingTrackTitleTv = findViewById(R.id.main_tv_track_title)
        mPlayingTrackRemainTimeTv = findViewById(R.id.main_tv_remain_time)
        mPlayingIvPlaying = findViewById(R.id.main_iv_playing)
        mPlayingIvPlayPanel = findViewById(R.id.main_iv_play_panel)
        mPlayingLottieIcon = findViewById(R.id.main_iv_playing_icon)
        mPlayingTrackTitleTv!!.setTextColor(mContext.resources.getColor(R.color.main_color_ff4444))
    }

    fun initPlayingTrackData() {
        val model = PlayTools.getCurTrack(mContext)
        ViewStatusUtil.setVisible(if (model == null) View.GONE else View.VISIBLE, mPlayingRootView)
        model?: return
        val isPlayingNow = XmPlayerManager.getInstance(mContext).isPlaying || isVideoPlaying()
        if (isPlayingNow) {
            if (mPlayingLottieIcon!!.isAnimating) {
                mPlayingLottieIcon!!.resumeAnimation()
            } else {
                mPlayingLottieIcon!!.progress = 0f
                mPlayingLottieIcon!!.post { mPlayingLottieIcon?.playAnimation() }
            }
        } else {
            mPlayingLottieIcon!!.cancelAnimation()
        }
        val drawable = if (isPlayingNow) {
            R.drawable.host_btn_pause_btn_n_fill_n_12
        } else {
            R.drawable.host_btn_play_btn_inside_fill_n_12
        }
        mPlayingIvPlaying!!.setImageResource(drawable)
        mPlayingLottieIcon!!.visibility = View.VISIBLE
        SpanUtils.with(mPlayingTrackTitleTv)
            .appendSpace(18.dp)
            .append(model.trackTitle ?: "")
            .create()
        val spanUtils = SpanUtils.with(mPlayingTrackRemainTimeTv)
        val createTime = ShowNotesAlbumTrackAdapter.getFriendlyTimeStr(model.createdAt)
        if (!TextUtils.isEmpty(createTime)) {
            spanUtils.append(createTime)
        }
        val durationStr: String? = ShowNotesAlbumTrackAdapter.getPlayDurationStr(model.duration.toLong())
        if (!TextUtils.isEmpty(durationStr)) {
            if (!TextUtils.isEmpty(createTime)) {
                spanUtils.appendSpace(BaseUtil.dp2px(mContext, 3f))
                    .appendVerticalCenterImage(mToBeAdapter!!.mPointDrawable!!, true)
                    .appendSpace(BaseUtil.dp2px(mContext, 3f))
            }
            spanUtils.appendVerticalCenterImage(mToBeAdapter!!.mTimeDrawable!!, true)
                .appendSpace(BaseUtil.dp2px(mContext, 2f))
                .append(durationStr!!)
        }

        val lastPos = XmPlayerManager.getInstance(mContext).getHistoryPos(model.dataId)
        val ps: Pair<Int, String>? = ShowNotesAlbumTrackAdapter.getPlayScheduleForShowNotesNew(lastPos.toLong(), model.duration.toLong())
        if (ps != null && ps.first == 2) {
            if (!TextUtils.isEmpty(ps.second)) {
                spanUtils.appendSpace(BaseUtil.dp2px(mContext, 3f))
                    .appendVerticalCenterImage(mToBeAdapter!!.mPointDrawable!!, true)
                    .appendSpace(BaseUtil.dp2px(mContext, 3f))
                    .append(ps.second)
                    .setForegroundColor(Color.parseColor("#B3FF7C3A"))
            }
        }
        spanUtils.create()
        ImageManager.from(mContext).displayImageNotIncludeDownloadCacheSizeInDp(mPlayingCoverIv, model.validCover, R.drawable.host_default_album, 60, 60) { _, bitmap ->
            run {
                ColorUtil.getDefaultAlbumCoverDomainColor(bitmap, Color.BLACK) { color: Int -> mPlayingIvPlayPanel!!.imageTintList = ColorStateList.valueOf(color) }
            }
        }
    }

    override fun onMyResume() {
        super.onMyResume()
        onRealResume()
    }

    override fun onPause() {
        super.onPause()
        onRealPause()
    }

    private fun onRealPause() {
        mToBeAdapter?.let {
            ToListenManager.removeListChangeListener(it)
            XmPlayerManager.getInstance(mContext).removePlayerStatusListener(it.mPlayStatusListener)
        }
        XmPlayerManager.getInstance(activity).removePlayListChangeListener(mXmDataCallback)
    }

    private fun onRealResume() {
        doAfterAnimation {
            initPlayingTrackData()
            loadToBePlayData()
            mToBeAdapter?.let {
                ToListenManager.addListChangeListener(it)
                XmPlayerManager.getInstance(mContext).addPlayerStatusListener(it.mPlayStatusListener)
            }
        }
        XmPlayerManager.getInstance(activity).addPlayListChangeListener(mXmDataCallback)
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser && canUpdateUi()) {
            onRealResume()
        } else if (canUpdateUi()) {
            onRealPause()
        }
    }

    override fun loadData() {
    }

    // 加载待播列表
    fun loadToBePlayData() {
        if (!canUpdateUi() || mToBeAdapter == null) {
            return
        }
        mXmRequestId = XmRequestIdManager.getInstance(context).requestId
        onPageLoadingCompleted(LoadCompleteType.LOADING)
        ToListenManager.getTrackListBySync(object : IDataCallBack<CopyOnWriteArrayList<SimpleTrackForToListen>> {
            override fun onSuccess(data: CopyOnWriteArrayList<SimpleTrackForToListen>?) {
                if (!canUpdateUi() || mToBeAdapter == null) {
                    return
                }
                ToListenUtil.getRealPlayDataFromMmkv(object : IDataCallBack<List<Track?>?> {
                    override fun onSuccess(normalTrackList: List<Track?>?) {
                        val sortByUp = isPodCastListPlaySortUpWithToListen()
                        mToBeAdapter!!.clean()
                        mToBeAdapter?.mOriginalTrackId = ToListenUtil.getCommonMMKVUtil().getLong("ToListenOriginalPlayingId", -1)
                        var shouldScrollIndex = 0
                        var hasToListen = false
                        if (ToListenManager.isPlayingToListenTracks() && data != null && data.size > 0) {
                            if (data.size > 1) {
                                hasToListen = true
                                mToBeAdapter?.addData(PodCastPlayListModel(null, null, TO_BE_PLAY_HEADER, mXmRequestId))
                                data.forEachIndexed { index, simpleTrackForToListen ->
                                    if (index > 0) {
                                        mToBeAdapter?.addData(PodCastPlayListModel(null, simpleTrackForToListen, TO_BE_PLAY_OK, mXmRequestId))
                                    }
                                }
                            }
                            if (!normalTrackList.isNullOrEmpty()) {
                                mToBeAdapter?.addData(PodCastPlayListModel(null, null, NORMAL_HEADER, mXmRequestId))
                                val originalIndex = ToListenUtil.getCommonMMKVUtil().getInt("ToListenOriginalIndex", 0)
                                normalTrackList?.forEachIndexed { index, it ->
                                    if (sortByUp) {
                                        if (index <= originalIndex + 2) {
                                            mToBeAdapter?.addData(PodCastPlayListModel(it, null, NORMAL_PLAY_LIST_OK, mXmRequestId), true)
                                        }
                                    } else {
                                        if (index >= originalIndex - 2) {
                                            mToBeAdapter?.addData(PodCastPlayListModel(it, null, NORMAL_PLAY_LIST_OK, mXmRequestId), false)
                                        }
                                    }
                                }
                            }
                        } else {
                            val trackList: List<Track>? = XmPlayerManager.getInstance(mContext).playList
                            if (data != null && data.size > 0) {
                                hasToListen = true
                                mToBeAdapter?.addData(PodCastPlayListModel(null, null, TO_BE_PLAY_HEADER, mXmRequestId))
                                data.forEachIndexed { index, simpleTrackForToListen ->
                                    mToBeAdapter?.addData(PodCastPlayListModel(null, simpleTrackForToListen, TO_BE_PLAY_OK, mXmRequestId))
                                }
                            }
                            if (!trackList.isNullOrEmpty()) {
                                mToBeAdapter?.addData(PodCastPlayListModel(null, null, NORMAL_HEADER, mXmRequestId))
                                if (hasToListen) {
                                    val originalIndex = XmPlayerManager.getInstance(mContext).currentIndex
                                    trackList.forEachIndexed { index, track ->
                                        if (sortByUp) {
                                            if (index <= originalIndex + 2) {
                                                mToBeAdapter?.addData(PodCastPlayListModel(track, null, NORMAL_PLAY_LIST_OK, mXmRequestId), true)
                                            }
                                        } else {
                                            if (index >= originalIndex - 2) {
                                                mToBeAdapter?.addData(PodCastPlayListModel(track, null, NORMAL_PLAY_LIST_OK, mXmRequestId), false)
                                            }
                                        }
                                    }
                                } else {
                                    trackList.forEach { track ->
                                        mToBeAdapter?.addData(PodCastPlayListModel(track, null, NORMAL_PLAY_LIST_OK, mXmRequestId), sortByUp)
                                    }
                                    val scrollIndex = mToBeAdapter?.findNormalTrackItemIndex(XmPlayerManager.getInstance(mContext).currSound?.dataId ?: -1) ?: -1
                                    if (scrollIndex != -1 && scrollIndex > 2) {
                                        shouldScrollIndex = scrollIndex - 2
                                    }
                                }
                            }
                        }
                        changeListRefreshState(!hasToListen)
                        if (mToBeAdapter?.getDataList().isNullOrEmpty()) {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT)
                        } else {
                            onPageLoadingCompleted(LoadCompleteType.OK)
                        }
                        mToBeAdapter?.notifyDataSetChanged()
                        updateOutHeaderAndListHeader()
                        if (shouldScrollIndex >= 0) {
                            mToBeRecyclerView.scrollToPosition(shouldScrollIndex)
                            mToBeRecyclerView.smoothScrollToPositionWithOffset(shouldScrollIndex, mNormalHeaderLayout!!.measuredHeight)
                        }
                        mToBeRecyclerView.post {
                            updateOutHeaderAndListHeader()
                        }
                        getRealPlayParamsFromMmkv(object : IDataCallBack<Map<String, String>?> {
                            override fun onSuccess(data: Map<String, String>?) {
                                mNormalOriginalParams = data as? HashMap<String, String>?
                                mToBeAdapter?.setParams(data)
                                initStickyHeaderData()
                                val hasShow = MMKVUtil.getInstance().getBoolean("has_show_pod_cast_list_guide", false)
                                if (!hasShow) {
                                    postOnUiThreadDelayedAndRemovedOnPause(300) {
                                        showToListenGuide()
                                    }
                                }
                            }

                            override fun onError(code: Int, message: String?) {}
                        })
                    }

                    override fun onError(code: Int, message: String?) {
                    }
                })
            }

            override fun onError(code: Int, message: String?) {
                if (!canUpdateUi() || mToBeAdapter == null) {
                    return
                }
                onPageLoadingCompleted(LoadCompleteType.OK)
            }
        })
    }

    fun scroll2PlayIndex() {
        var shouldScrollIndex = -1
        val scrollIndex = mToBeAdapter?.findNormalTrackItemIndex(XmPlayerManager.getInstance(mContext).currSound?.dataId ?: -1) ?: -1
        if (scrollIndex != -1) {
            shouldScrollIndex = if (scrollIndex > 2) {
                scrollIndex - 2
            } else {
                scrollIndex
            }
        }
        if (shouldScrollIndex > 0 && !ToListenManager.isPlayingToListenTracks()) {
            mToBeRecyclerView.scrollToPosition(shouldScrollIndex)
            mToBeRecyclerView.smoothScrollToPositionWithOffset(shouldScrollIndex, mNormalHeaderLayout!!.measuredHeight)
        }
    }

    fun updateOutHeaderAndListHeader() {
        val currentDataPos = mToBeAdapter?.findFirstIndexOfType(NORMAL_HEADER) ?: -1
        val firstVisiblePosition = mLinearLayoutManagerWrapper?.findFirstVisibleItemPosition() ?: -1
        if (currentDataPos >= 0 && firstVisiblePosition >= 0 && firstVisiblePosition >= currentDataPos) {
            ViewStatusUtil.setVisible(View.VISIBLE, mNormalHeaderLayout)
            mToBeAdapter?.updateNormalHeaderVisible(true, currentDataPos)
        } else {
            ViewStatusUtil.setVisible(View.GONE, mNormalHeaderLayout)
            if (currentDataPos != -1) {
                mToBeAdapter?.updateNormalHeaderVisible(false, currentDataPos)
            }
        }
    }

    private fun getRealPlayParamsFromMmkv(callBack: IDataCallBack<Map<String, String>?>) {
        if (ToListenManager.isPlayingToListenTracks()) {
            object : MyAsyncTask<Void?, Void?, Map<String, String>?>() {
                override fun doInBackground(vararg params: Void?): Map<String, String>? {
                    return ToListenUtil.getToListenOriginalParams()
                }

                override fun onPostExecute(params: Map<String, String>?) {
                    callBack?.onSuccess(params)
                }
            }.myexec()
        } else {
            callBack.onSuccess(XmPlayerManager.getInstance(mContext).playListParams)
        }
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_pod_cast_play_list_layout
    }

    override fun onDestroy() {
        super.onDestroy()
        ToListenManager.mIsShowingToListenPage = false
    }

    private fun loadMoreOrRefreshWhilePlayToListen(loadNextPage: Boolean) {
        if (mToBeAdapter != null && !mToBeAdapter!!.getDataList().isNullOrEmpty() && mNormalOriginalParams != null
            && (mNormalOriginalParams!!["isAsc"] != null || mNormalOriginalParams!!["local_is_asc"] != null)) {
            val originalUrl = mNormalOriginalParams!![DTransferConstants.TRACK_BASE_URL]
            if (UrlConstants.getInstanse().albumPlayList != originalUrl && UrlConstants.getInstanse().albumData != originalUrl &&
                UrlConstants.getInstanse().playHistory != originalUrl) {
                // 非专辑
                mSmartRefreshLayout.finishRefresh()
                mSmartRefreshLayout.finishLoadMore()
                return
            }
            val fromRefreshing = mSmartRefreshLayout.isRefreshing // true是下拉刷新，false是预加载上一页
            val sortByUp = isPodCastListPlaySortUpWithToListen()
            val isNextPage = if (fromRefreshing) {
                sortByUp
            } else {
                !sortByUp
            }
            val lastData = mToBeAdapter!!.getDataList().getOrNull(if (fromRefreshing) mToBeAdapter!!.findFirstIndexOfType(NORMAL_PLAY_LIST_OK) else mToBeAdapter!!.getDataList().size - 1)
            if (lastData != null && lastData.listType == NORMAL_PLAY_LIST_OK && lastData.track != null) {
                val ascTemp = if (mNormalOriginalParams!!["isAsc"] != null) mNormalOriginalParams!!["isAsc"].toBoolean() else mNormalOriginalParams!!["local_is_asc"].toBoolean()
                var isAscFinal = if (loadNextPage) ascTemp else !ascTemp
                PodCastPlayListSortManager.changeSortType(lastData.getDataId(),
                    lastData.track!!.album?.albumId ?: -1, isAscFinal,
                    mNormalOriginalParams!![DTransferConstants.TRACK_BASE_URL], object : IDataCallBack<CommonTrackList<*>> {
                        override fun onSuccess(data: CommonTrackList<*>?) {
                            if (data != null) {
                                val list = data.tracks as? MutableList<Track>?
                                onMyDataReady(if (isNextPage) list else list?.reversed(), false, isNextPage)
                            } else {
                                mSmartRefreshLayout.finishRefresh()
                                mSmartRefreshLayout.finishLoadMore()
                            }
                        }

                        override fun onError(code: Int, message: String?) {
                            mSmartRefreshLayout.finishRefresh()
                            mSmartRefreshLayout.finishLoadMore()
                        }
                    })
            } else {
                mSmartRefreshLayout.finishRefresh()
                mSmartRefreshLayout.finishLoadMore()
            }
        } else {
            mSmartRefreshLayout.finishRefresh()
            mSmartRefreshLayout.finishLoadMore()
        }
    }

    private fun onMyDataReady(list: List<Track>?, fromPlayServer: Boolean, isNextPage: Boolean) {
        HandlerManager.postOnUIThread {
            if (!canUpdateUi() || mToBeRecyclerView == null) {
                return@postOnUIThread
            }
            if (ToListenManager.isPlayingToListenTracks()) {
                mSmartRefreshLayout.finishRefresh()
                mSmartRefreshLayout.finishLoadMore()
                return@postOnUIThread
            }
            mToBeRecyclerView.post(Runnable {
                if (!canUpdateUi() || mToBeAdapter == null) {
                    return@Runnable
                }
                mSmartRefreshLayout.finishRefresh()
                mSmartRefreshLayout.finishLoadMore()
                if (list.isNullOrEmpty()) {
                    return@Runnable
                }
                val requestId = XmRequestIdManager.getInstance(BaseApplication.getMyApplicationContext()).requestId
                handleRequestId(list, requestId)
                val tempList = mutableListOf<PodCastPlayListModel>()
                val sortByUp = isPodCastListPlaySortUpWithToListen()
                var addToHead = if (sortByUp) {
                    isNextPage
                } else {
                    !isNextPage
                }
                var sameItemCount = 0
                for (track in list) {
                    val model = PodCastPlayListModel(track, null, NORMAL_PLAY_LIST_OK, requestId, fromPlayServer)
                    val trackIndex = mToBeAdapter?.findNormalTrackItemIndex(track.dataId) ?: -1
                    if (trackIndex == -1) {
                        if (sortByUp) {
                            tempList.add(0, model)
                        } else {
                            tempList.add(model)
                        }
                    } else {
                        sameItemCount++
                    }
                }
                if (sameItemCount <= 2 && tempList.size > 0) {
                    if (addToHead) {
                        mToBeAdapter?.addDataList(mToBeAdapter!!.findFirstIndexOfType(NORMAL_HEADER) + 1, tempList)
                    } else {
                        mToBeAdapter?.addDataList(-1, tempList)
                    }
                    mToBeAdapter!!.notifyDataSetChanged()
                    if (addToHead) {
                        mToBeRecyclerView.smoothScrollToPosition(0)
                    }
                }
            })
        }
    }

    fun changeListRefreshState(canRefresh: Boolean) {
        mSmartRefreshLayout.setEnableRefresh(canRefresh)
        mSmartRefreshLayout.setEnableLoadMore(!(ToListenManager.isPlayingToListenTracks() && mToBeAdapter?.hasToLoListenDataInList() == true))
    }


    private val mXmDataCallback: IXmDataCallback = object : IXmDataCallback {
        override fun asBinder(): IBinder? {
            return null
        }

        @Throws(RemoteException::class)
        override fun onListChange() {
            HandlerManager.postOnUIThreadDelay({
                if (ToListenManager.isPlayingToListenTracks()) {
                    mSmartRefreshLayout.finishRefresh()
                    mSmartRefreshLayout.finishLoadMore()
                    return@postOnUIThreadDelay
                }
                loadToBePlayData()
            }, 100)
        }

        @Throws(RemoteException::class)
        override fun onDataReady(list: List<Track>?, hasMorePage: Boolean, isNextPage: Boolean) {
            onMyDataReady(list, true, isNextPage)
        }

        override fun onError(code: Int, message: String, isNextPage: Boolean) {
            HandlerManager.postOnUIThread {
                mSmartRefreshLayout.finishRefresh()
                mSmartRefreshLayout.finishLoadMore()
            }
        }
    }

    private fun handleRequestId(list: List<Track>?, requestId: String?) {
        if (!list.isNullOrEmpty()) {
            for (track in list) {
                track.xmRequestId = requestId
            }
        }
    }

    private fun isVideoPlaying(): Boolean {
        val playPageRef = XPlayPageRef.get()
        return playPageRef != null && playPageRef.isVideoPlaying()
    }

    fun showToListenGuide() {
        val hasShow = MMKVUtil.getInstance().getBoolean("has_show_pod_cast_list_guide", false)
        if (hasShow) {
            return
        }
        if (!((parentFragment as? PlayListParentFragment)?.parentFragment is YPlayListAndHistoryFragment)) {
            return
        }
        mLinearLayoutManagerWrapper?: return
        mToBeAdapter?: return
        val headerPosition = mToBeAdapter?.findFirstIndexOfType(TO_BE_PLAY_HEADER) ?: -1
        val toBePosition = mToBeAdapter?.findFirstIndexOfType(TO_BE_PLAY_OK) ?: -1
        val firstPosition = mLinearLayoutManagerWrapper!!.findFirstVisibleItemPosition()
        val lastPosition = mLinearLayoutManagerWrapper!!.findLastVisibleItemPosition()
        if (headerPosition != -1 && toBePosition != -1 && headerPosition >= firstPosition && toBePosition <= lastPosition) {
            val headerView = mLinearLayoutManagerWrapper!!.findViewByPosition(headerPosition)
            val toBeView = mLinearLayoutManagerWrapper!!.findViewByPosition(toBePosition)
            if (headerView != null && toBeView != null) {
                val topMargin = ViewStatusUtil.getViewTopOrBottom(headerView, true)
                val height = ViewStatusUtil.getViewTopOrBottom(toBeView, false) - ViewStatusUtil.getViewTopOrBottom(headerView, true)
                var parent = parentFragment
                if (parent != null && parent.parentFragment is YPlayListAndHistoryFragment) {
                    (parent.parentFragment as YPlayListAndHistoryFragment).showPodCastGuide(topMargin, height)
                    MMKVUtil.getInstance().saveBoolean("has_show_pod_cast_list_guide", true)
                    val info = PlayPageDataManager.getInstance().soundInfo
                    if (info.trackInfo2TrackM() != null && info.toAlbumM() != null) {
                        val track = info.trackInfo2TrackM()
                        val album = info.toAlbumM()
                        PlayPageDataManager.getInstance().notifyPlayModeChange()
                        // 新声音播放页-待播列表-新手引导  控件曝光
                        XMTraceApi.Trace()
                            .setMetaId(65098)
                            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                            .put("currPage", "newPlay")
                            .put("xmRequestId", mXmRequestId)
                            .put("contentId", track.dataId.toString())
                            .put("contentType", "track")
                            .put("currTrackId", track.dataId.toString())
                            .put("currAlbumId", album.id.toString())
                            .createTrace()
                    }
                }
            }
        }
    }

    private fun getNavHeight(): Int {
        return BarUtils.getNavBarHeight()
    }

    override fun filtStatusBarSet(): Boolean {
        return true
    }

    fun changePodCastListPlaySortUpWithToListen() {
        val currentUpMode = isPodCastListPlaySortUpWithToListen()
        val info = PlayPageDataManager.getInstance().soundInfo
        if (info?.trackInfo2TrackM() != null && info.toAlbumM() != null) {
            val track = info.trackInfo2TrackM()
            val album = info.toAlbumM()
            PlayPageDataManager.getInstance().notifyPlayModeChange()
            XMTraceApi.Trace()
                .click(17626)
                .put("currPage", "newPlay")
                .put("currTrackId", track.dataId.toString())
                .put("currAlbumId", album.id.toString())
                .put("anchorId", track.uid.toString())
                .put("categoryId", track.categoryId.toString())
                .put("item", if (currentUpMode) "向下播" else "向上播")
                .put(XmRequestIdManager.XM_REQUEST_ID, getPageUniqueRequestId(XmRequestPage.PAGE_PLAY_PAGE))
                .createTrace()
        }
        if (ToListenManager.isPlayingToListenTracks()) {
            getRealPlayParamsFromMmkv(object : IDataCallBack<Map<String, String>?> {
                override fun onSuccess(data: Map<String, String>?) {
                    mNormalOriginalParams = data as? HashMap<String, String>?
                    if (mNormalOriginalParams != null) {
                        if (currentUpMode) {
                            mNormalOriginalParams!!.remove(DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT)
                        } else {
                            mNormalOriginalParams!![DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT] = DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT_UP
                        }
                        //从本地存储的播放列表顺序
                        if (mNormalOriginalParams!![DTransferConstants.LOCAL_IS_ASC] != null) {
                            val isAsc = mNormalOriginalParams!![DTransferConstants.LOCAL_IS_ASC]
                            mNormalOriginalParams!![DTransferConstants.LOCAL_IS_ASC] = isAsc.toBoolean().not().toString()
                        }
                        if (mNormalOriginalParams!!["isAsc"] != null) {
                            val isAsc = mNormalOriginalParams!!["isAsc"]
                            mNormalOriginalParams!!["isAsc"] = isAsc.toBoolean().not().toString()
                        }
                        if (mNormalOriginalParams!!["asc"] != null) {
                            val isAsc = mNormalOriginalParams!!["asc"]
                            mNormalOriginalParams!!["asc"] = isAsc.toBoolean().not().toString()
                        }

                        object : MyAsyncTask<Void?, Void?, String?>() {
                            override fun doInBackground(vararg params: Void?): String? {
                                val params = Gson().toJson(mNormalOriginalParams)
                                ToListenUtil.getCommonMMKVUtil().saveString("ToListenOriginalParams", params)
                                return params
                            }

                            override fun onPostExecute(params: String?) {
                                initStickyHeaderData()
                                mToBeAdapter?.notifyDataSetChanged()
                            }
                        }.myexec()
                    }
                }

                override fun onError(code: Int, message: String?) {}
            })
        } else {
            XmPlayerManager.getInstance(mContext).updatePodCastListPlaySortUpCurrent(!currentUpMode)
            initStickyHeaderData()
            mToBeAdapter?.notifyDataSetChanged()
        }
    }

    fun modeItemExplore(item: String?) {
        val info = PlayPageDataManager.getInstance().soundInfo
        if (info?.trackInfo2TrackM() == null || info.toAlbumM() == null) {
            return
        }
        val track = info.trackInfo2TrackM()
        val album = info.toAlbumM()
        // 新声音播放页-播放列表弹窗-播放模式  控件曝光
        XMTraceApi.Trace()
            .setMetaId(65096)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newPlay")
            .put("xmRequestId", mXmRequestId)
            .put("contentId", item)
            .put("contentType", "item")
            .put("Item", item) // "向上播 | 向下播  | 顺序播放 | 单曲循环 | 列表循环") // 根据实际文案传值
            .put("currTrackId", track.dataId.toString())
            .put("currAlbumId", album.id.toString())
            .createTrace()
    }

    fun isPodCastListPlaySortUpWithToListen(): Boolean {
        if (ToListenManager.isPlayingToListenTracks()) {
            val tempParamString = ToListenUtil.getCommonMMKVUtil().getString("ToListenOriginalParams")
            return !TextUtils.isEmpty(tempParamString) && tempParamString.contains(
                DTransferConstants.PARAM_POD_CAST_LIST_PLAY_SORT_UP
            )
        }
        return XmPlayerManager.getInstance(mContext).isPodCastListPlaySortUpCurrent
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        val sortByUp = isPodCastListPlaySortUpWithToListen()
        if (ToListenManager.isPlayingToListenTracks()) {
            loadMoreOrRefreshWhilePlayToListen(sortByUp)
            return
        }
        if (sortByUp) {
            XmPlayerManager.getInstance(activity).getNextPlayList()
        } else {
            XmPlayerManager.getInstance(activity).getPrePlayList()
        }
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        val sortByUp = isPodCastListPlaySortUpWithToListen()
        if (ToListenManager.isPlayingToListenTracks()) {
            loadMoreOrRefreshWhilePlayToListen(!sortByUp)
            return
        }
        if (sortByUp) {
            XmPlayerManager.getInstance(activity).getPrePlayList()
        } else {
            XmPlayerManager.getInstance(activity).getNextPlayList()
        }
    }
}
