package com.ximalaya.ting.android.main.playpage.fragment

import android.animation.ValueAnimator
import android.animation.ValueAnimator.RESTART
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.handmark.pulltorefresh.library.PullToRefreshBase
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.manager.ToListenManager
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.view.ToBePlayLoadMoreRecyclerView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.album.item.ToBePlayedWithPlayerAdapter
import com.ximalaya.ting.android.main.mine.fragment.EditListenListFragment
import com.ximalaya.ting.android.main.playpage.dialog.PlayListAndHistoryDialogFragment
import com.ximalaya.ting.android.main.playpage.manager.ToBePlayMarkPointManager
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.track.SimpleTrackForToListen
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.util.ToListenUtil
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import java.util.concurrent.CopyOnWriteArrayList

/**
 * Create by {jian.kang} on 7/1/22
 * <AUTHOR>
 */
class ToBePlayedFragment : BaseFragment2(),
        View.OnClickListener {

    private lateinit var mAdapter: ToBePlayedWithPlayerAdapter
    private lateinit var mRecyclerView: ToBePlayLoadMoreRecyclerView
    private lateinit var mOrderPlayV: View
    private lateinit var mOrderPlayTv: TextView
    private lateinit var mEditV: View
    private var isPositionOrder = false
    private var mIsPause = false
    private var mScrollY: Int = 0

    override fun getPageLogicName(): String {
        return "ToBePlayedFragment"
    }

    override fun initUi(savedInstanceState: Bundle?) {
        mOrderPlayV = findViewById(R.id.main_v_adjust_play_order)
        mOrderPlayV.setOnClickListener(this)
        mOrderPlayTv = findViewById(R.id.main_tv_adjust_play_order)
        mEditV = findViewById(R.id.main_v_edit)
        mEditV.setOnClickListener(this)
        mAdapter = ToBePlayedWithPlayerAdapter(this)
        mRecyclerView = findViewById<ToBePlayLoadMoreRecyclerView>(R.id.main_play_recyclerview).apply {
            this.mode = PullToRefreshBase.Mode.DISABLED
            this.setAdapter(mAdapter)
            this.refreshableView.layoutManager = RemoveLinearLayoutManager(context)
            this.refreshableView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState == RecyclerView.SCROLL_STATE_IDLE && Math.abs(mScrollY) > 0) {
                        traceOnItemShow()
                        mScrollY = 0
                    }
                }

                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    mScrollY = dy
                }
            })
        }
        isPositionOrder = MMKVUtil.getInstance().getBoolean(ToListenUtil.PLAY_ORDER_BY_LISTEN_LIST, true).also {
            mOrderPlayTv.text = if (it) "顺序播放" else "倒序播放"
        }
    }

    private class RemoveLinearLayoutManager(context: Context) : LinearLayoutManager(context) {
        override fun supportsPredictiveItemAnimations(): Boolean {
            return true
        }
    }

    override fun loadData() {
        onPageLoadingCompleted(LoadCompleteType.LOADING)
        ToListenManager.getTrackListBySync(object : IDataCallBack<CopyOnWriteArrayList<SimpleTrackForToListen>> {
            override fun onSuccess(data: CopyOnWriteArrayList<SimpleTrackForToListen>?) {
                if (canUpdateUi()) {
                    if (data == null || data.size == 0) {
                        onPageLoadingCompleted(LoadCompleteType.NOCONTENT)
                    } else {
                        mAdapter.addDataList(if (isPositionOrder) data else data.reversed())
                        mAdapter.notifyDataSetChanged()
                        onPageLoadingCompleted(LoadCompleteType.OK)
                        postOnUiThreadDelayed({ traceOnItemShow() }, 300)
                    }
                }
            }

            override fun onError(code: Int, message: String?) {
            }
        })
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_to_be_played_list_layout
    }

    override fun onClick(v: View?) {
        if (v == null || !OneClickHelper.getInstance().onClick(v)) {
            return
        }

        when (v.id) {
            R.id.main_v_edit -> {
                gotoEditListenListFragment()
            }
            R.id.main_v_adjust_play_order -> {
                adjustTrackPlayOrder()
            }
        }
    }

    private fun gotoEditListenListFragment() {
        dismissDialog()
        EditListenListFragment.getInstance().let {
                startFragment(it)
        }
    }

    fun dismissDialog() {
        if (parentFragment is BaseDialogFragment<*>) {
            (parentFragment as BaseDialogFragment<*>).dismiss()
        }
    }

    private fun gotoDailyNewsFragment(v: View) {
        dismissDialog()
        MainApplication.getMainActivity().let {
            if (it is MainActivity) {
                ToolUtil.clickUrlAction(it, AppConstants.ONE_KEY_LISTEN_PAGE_URL, v)
            }
        }
        ToBePlayMarkPointManager.Player.markPointOnToFindClick()
    }

    private fun adjustTrackPlayOrder() {
        // UI更新
        setOrderTv()
        mAdapter.addDataList(mAdapter.getDataList().reversed())
        mAdapter.notifyDataSetChanged()

        // 播放器更新
        if (ToListenManager.isPlayingToListenTracks()) {
            val playList = XmPlayerManager.getInstance(mContext).playList
            val playing = XmPlayerManager.getInstance(mContext).isPlaying
            val currentIndex = XmPlayerManager.getInstance(mContext).currentIndex
            val reversed = playList?.reversed()
            var playIndex = reversed?.size!! - currentIndex - 1

            if (playing) {
                XmPlayerManager.getInstance(mContext).playList(reversed, playIndex)
            } else {
                XmPlayerManager.getInstance(mContext).setPlayList(reversed, playIndex)
            }
        }
    }

    private fun setOrderTv() {
        if (canUpdateUi()) {
            isPositionOrder = !isPositionOrder
            mOrderPlayTv.text = if (isPositionOrder) "顺序播放" else "倒序播放"
            MMKVUtil.getInstance().saveBoolean(ToListenUtil.PLAY_ORDER_BY_LISTEN_LIST, isPositionOrder)
        }
    }

    override fun onPageLoadingCompleted(loadCompleteType: LoadCompleteType?) {
        super.onPageLoadingCompleted(loadCompleteType)
        when (loadCompleteType) {
            LoadCompleteType.NOCONTENT,
            LoadCompleteType.NETWOEKERROR -> {
                showEmpty(true)
            }
            LoadCompleteType.OK -> {
                showEmpty(false)
            }
            LoadCompleteType.LOADING -> {
            }
            else -> {}
        }
    }

    private fun showEmpty(empty: Boolean) {
        if (empty) {
            mOrderPlayV?.visibility = View.GONE
            mEditV?.visibility = View.GONE
            mRecyclerView.onRefreshComplete(true)
        } else {
            mOrderPlayV?.visibility = View.VISIBLE
            mEditV?.visibility = View.VISIBLE
            mRecyclerView.onRefreshComplete(false)
            mRecyclerView.setFootText("播完待播的声音后将继续为您播放默认播放列")
        }
    }

    override fun getNoContentView(): View? {
        var emptyView: View? = null
        try {
            val layoutResId = com.ximalaya.ting.android.host.R.layout.host_listen_list_empty_layout
            emptyView = View.inflate(mActivity, layoutResId, null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        if (emptyView != null) {
            val toFindTv = emptyView.findViewById<View>(com.ximalaya.ting.android.host.R.id.host_to_find) as TextView
            val descTv = emptyView.findViewById<View>(com.ximalaya.ting.android.host.R.id.host_tv_desc) as TextView
            val countDownTv = emptyView.findViewById<View>(com.ximalaya.ting.android.host.R.id.host_count_down) as TextView

            if (toFindTv != null && descTv != null && countDownTv != null) {
                if (!mAdapter.mIsToBeTrack) {
                    toFindTv.visibility = View.VISIBLE
                    descTv.visibility = View.VISIBLE
                    countDownTv.visibility = View.GONE
                    toFindTv.setOnClickListener {
                        gotoDailyNewsFragment(toFindTv)
                    }
                    ToBePlayMarkPointManager.Player.markPointOnToFindShow()
                } else {
                    toFindTv.visibility = View.GONE
                    descTv.visibility = View.GONE
                    countDownTv.visibility = View.VISIBLE

                    ValueAnimator.ofInt(2, 0).apply {
                        this.repeatMode = RESTART
                        this.duration = 3000
                        this.interpolator = object : LinearInterpolator() {}
                    }.also {
                        it?.addUpdateListener { valueAnimator ->
                            if (canUpdateUi()) {
                                if (valueAnimator.animatedValue == 0) {
                                    toFindTv.visibility = View.VISIBLE
                                    descTv.visibility = View.VISIBLE
                                    countDownTv.visibility = View.GONE
                                    toFindTv.setOnClickListener {
                                        gotoDailyNewsFragment(toFindTv)
                                    }
                                    togglePlayList()
                                    ToBePlayMarkPointManager.Player.markPointOnToFindShow()
                                } else {
                                    countDownTv.text = "待播列表已播完，\n接下来为你继续播放默认播放列表 ${it.animatedValue}s"
                                }
                            }
                        }
                    }.start()
                    mAdapter?.mIsToBeTrack = false
                }
            }
        }
        return emptyView
    }

    private fun togglePlayList() {
        if (parentFragment is PlayListAndHistoryDialogFragment) {
            (parentFragment as PlayListAndHistoryDialogFragment).toggleViewPage(0)
        }
    }

    override fun addLoadStateView(
            parent: ViewGroup?,
            addView: View?,
            lp: ViewGroup.LayoutParams?,
            type: LoadCompleteType?
    ) {
        if (parent != null && addView != null) {
            val params = ViewGroup.LayoutParams(-1, -2)
            parent.addView(addView, params)
        }
    }

    override fun onMyResume() {
        super.onMyResume()
        if (mIsPause) {
            mIsPause = false
            postOnUiThreadDelayed({ traceOnItemShow() }, 300)
            ToBePlayMarkPointManager.Player.markPointOnFragmentShow()
        }
    }

    override fun onPause() {
        super.onPause()
        mIsPause = true
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser) {
            MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInOpenSdk.KEY_TO_BE_PLAY_RED_DOT, false)

            ToBePlayMarkPointManager.Player.markPointOnFragmentShow()
            postOnUiThreadDelayed({ traceOnItemShow() }, 300)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mAdapter?.onDestroy()
    }

    private fun traceOnItemShow() {
        if (mRecyclerView == null || mRecyclerView.refreshableView == null
                || mRecyclerView.refreshableView.layoutManager !is LinearLayoutManager
                || mAdapter == null || mAdapter.getDataList() == null || mAdapter.getDataList().size <= 0) {
            return
        }

        var linearLayoutManager: LinearLayoutManager = mRecyclerView.refreshableView.layoutManager as LinearLayoutManager
        var first = linearLayoutManager.findFirstVisibleItemPosition()
        var last = linearLayoutManager.findLastVisibleItemPosition()

        if (first < 0) {
            first = 0
        }

        if (last >= mAdapter.getDataList().size) {
            last = mAdapter.getDataList().size - 1
        }

        mAdapter.getDataList().forEachIndexed { index, simpleTrackForToListen ->
            if (index in first..last) {
                val track = mAdapter.getDataList()[index]
                if (track != null) {
                    ToBePlayMarkPointManager.Player.markPointOnTrackShow(track.dataId, track.albumId, index + 1)
                }
            }
        }
    }
}