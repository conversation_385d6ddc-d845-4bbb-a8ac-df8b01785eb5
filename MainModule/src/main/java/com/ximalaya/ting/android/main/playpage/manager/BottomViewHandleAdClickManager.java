package com.ximalaya.ting.android.main.playpage.manager;

import android.app.Activity;

import androidx.fragment.app.FragmentActivity;

import com.ximalaya.ting.android.adsdk.InnerHelper;
import com.ximalaya.ting.android.adsdk.adapter.XmNativeAd;
import com.ximalaya.ting.android.adsdk.bridge.inner.IInnerProvider;
import com.ximalaya.ting.android.adsdk.bridge.inner.download.IDownloadTaskListener;
import com.ximalaya.ting.android.adsdk.bridge.inner.download.IDownloadTaskListenerAdapter;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.AdSDKAdapterModel;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.SDKAdReportModel;
import com.ximalaya.ting.android.adsdk.download.utils.XmDownloadUtils;
import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.adsdk.external.bean.XmDownloadInfo;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.other.WebViewDialogFragment;
import com.ximalaya.ting.android.host.manager.ad.AdApkInstallManager;
import com.ximalaya.ting.android.host.manager.ad.AdConversionUtil;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.ad.playweb.view.AdPlayWebDialogFragment;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.listener.IAdActionProvider;
import com.ximalaya.ting.android.main.playpage.dialog.AdOpenOtherAppDialog;
import com.ximalaya.ting.android.main.playpage.dialog.RecentlyAdListDialogFragment;
import com.ximalaya.ting.android.main.playpage.listener.IBottomViewCallback;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Time 2022/8/26
 * @Description
 */
public class BottomViewHandleAdClickManager {

    private Advertis currentAdvertis;

    private boolean isFragmentPause = false;

    private WeakReference <RecentlyAdListDialogFragment> mCurrentAdDialog;
    private WeakReference <AdPlayWebDialogFragment> mCurrentAdWebDialog;

    public static class INSTANCE {
        private static BottomViewHandleAdClickManager manager = new BottomViewHandleAdClickManager();
    }

    public static BottomViewHandleAdClickManager getInstance() {
        return INSTANCE.manager;
    }

    private BottomViewHandleAdClickManager() {
    }

    private final static String KEY_AD_AUTO_CLICK_LAST_TIME = "key_ad_auto_click_last_time";

    public void handleAdClick(IBottomViewCallback mBottomViewCallback, List<INativeAd> nativeAdList,
                                     boolean isAutoClick, IAdActionProvider iRecordListener) {

        if (nativeAdList != null) {
            INativeAd iNativeAd = nativeAdList.get(0);
            if (iNativeAd != null && iNativeAd instanceof XmNativeAd) {

                if (isNewAutoScrollClick(nativeAdList)) {
                    handleAutoScrollClick(nativeAdList, isAutoClick);
                    return;
                }
                Advertis advertis = AdConversionUtil.translateSDKAdModelToAdvertis(iNativeAd.getAdSDKAdapterModel());
                // 判断是否是跳列表
                if (advertis != null && advertis.getBusinessExtraInfo() != null && "1".equals(advertis.getBusinessExtraInfo().getSoundAggType())) {
                    if (AdManager.isDownloadAd(advertis) || AdManager.isDpLinkAd(advertis)) {
                        Logger.v("-------msg", " ------ 搜索彩蛋  ----- download 或者 dp link");
                        InnerHelper.getInstance().clickAd(AdConversionUtil.conversionModel(advertis, advertis.getPositionName()), null,
                                new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_SITE_CLICK, advertis.getPositionName())
                                        .build(), IInnerProvider.IClickViewType.ONLY_NORMAL_CLICK_VIEW_TYPE);
                        return;
                    } else {
                        showAdWebViewDialog(advertis);
                        Logger.v("-------msg", " ------ 搜索彩蛋  ----- 半屏 webview");
                        return;
                    }
                }
            }
            showAdDialog(mBottomViewCallback, nativeAdList, iRecordListener);
        }
    }

    private void showAdWebViewDialog(Advertis advertis) {
        if (mCurrentAdWebDialog != null && mCurrentAdWebDialog.get() != null) {
            mCurrentAdWebDialog.get().dismiss();
        }
        AdPlayWebDialogFragment adPlayWebDialogFragment = AdPlayWebDialogFragment.newInstance(advertis);
        Activity mainActivity = MainApplication.getMainActivity();
        if (mainActivity instanceof MainActivity) {
            adPlayWebDialogFragment.show(((MainActivity) mainActivity).getSupportFragmentManager(), "ad_play_webview");
        }
        mCurrentAdWebDialog = new WeakReference<>(adPlayWebDialogFragment);
        try {
            ArrayList<AdSDKAdapterModel> models = new ArrayList<>();
            models.add(AdConversionUtil.conversionModel(advertis, advertis.getPositionName()));
            InnerHelper.getInstance().batchAdRecord(mainActivity, models,
                    SDKAdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW, advertis.getPositionName()).build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleAutoScrollClick(List<INativeAd> nativeAdList, boolean isAutoClick) {
        if (nativeAdList!=null && nativeAdList.get(0) !=null && nativeAdList.get(0).isXmNativeAd()){
            AdSDKAdapterModel adSDKAdapterModel = nativeAdList.get(0).getAdSDKAdapterModel();
            if (adSDKAdapterModel!=null && adSDKAdapterModel.getBusinessExtraInfo()!=null){
                Logger.e("-----------msg", " -------------- 新版自动拉起 --- 时长 ---- "
                        + adSDKAdapterModel.getBusinessExtraInfo().getDpAutoJumpSecond());
            }
        }

        if (nativeAdList == null || nativeAdList.get(0) == null || nativeAdList.get(0).getAdSDKAdapterModel() == null) {
            return;
        }
        if (nativeAdList.get(0) instanceof XmNativeAd) {
            Advertis advertis = AdConversionUtil.translateSDKAdModelToAdvertis(nativeAdList.get(0).getAdSDKAdapterModel());
            if (advertis != null) {
                if (AdManager.isDpLinkAd(advertis) || AdManager.isWxAppletsAd(advertis)) {
                    handleDeepLinkOrWxAppletsClick(advertis, isAutoClick);
                } else if (AdManager.isDownloadAd(advertis)) {
                    handleDownloadClick(advertis, isAutoClick, false);
                } else {
                    handleWebOrITingClick(advertis, isAutoClick);
                }
            }
        }
    }

    private void handleWebOrITingClick(Advertis advertis, boolean isAutoClick) {

        if (advertis == null) {
            return;
        }
        if (!TextUtils.isEmpty(advertis.getRealLink()) && advertis.getRealLink().contains("iting://")) {
            Logger.v("-------msg", " ------ handleWebOrITingClick  ----- iting " + advertis.getRealLink());
            InnerHelper.getInstance().clickAd(AdConversionUtil.conversionModel(advertis, advertis.getPositionName()), null,
                    new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_SITE_CLICK, advertis.getPositionName())
                            .build(), IInnerProvider.IClickViewType.ONLY_NORMAL_CLICK_VIEW_TYPE);
        } else {
            if (mCurrentAdWebDialog != null && mCurrentAdWebDialog.get() != null) {
                mCurrentAdWebDialog.get().dismiss();
            }
            Logger.v("-------msg", " ------ handleWebOrITingClick  ----- 半屏 webview --- " + advertis.getRealLink());
            AdPlayWebDialogFragment adPlayWebDialogFragment = AdPlayWebDialogFragment.newInstance(advertis);
            Activity mainActivity = MainApplication.getMainActivity();
            if (mainActivity instanceof MainActivity) {
                adPlayWebDialogFragment.show(((MainActivity) mainActivity).getSupportFragmentManager(), "ad_auto_play_webview");
            }
            mCurrentAdWebDialog = new WeakReference<>(adPlayWebDialogFragment);

            adPlayWebDialogFragment.setOnTouchListener(new WebViewDialogFragment.IWebViewTouchListener() {

                private boolean isWebViewTouched = false;

                @Override
                public void onTouch() {
                    if (!isWebViewTouched) {
                        isWebViewTouched = true;
                        AdReportModel adReportModel = (AdReportModel) AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                        TextUtils.isEmpty(advertis.getPositionName()) ? AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId())
                                                : advertis.getPositionName())
                                .ignoreTarget(true)
                                .onlyClickRecord(true)
                                .autoPull(3)
                                .build();
                        AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), advertis, adReportModel);
                    }
                    isWebViewTouched = true;
                }
            });
            if (isAutoClick && advertis.getBusinessExtraInfo() != null && advertis.getBusinessExtraInfo().isScrollClickReport()) {
                AdReportModel adReportModel = (AdReportModel) AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                TextUtils.isEmpty(advertis.getPositionName()) ? AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId())
                                        : advertis.getPositionName())
                        .ignoreTarget(true)
                        .onlyClickRecord(true)
                        .autoPull(2)
                        .build();
                AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), advertis, adReportModel);
//                recordClick(advertis);
            }
        }
        try {
            ArrayList<AdSDKAdapterModel> models = new ArrayList<>();
            models.add(AdConversionUtil.conversionModel(advertis, advertis.getPositionName()));
            InnerHelper.getInstance().batchAdRecord(MainApplication.getMyApplicationContext(), models,
                    SDKAdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW, advertis.getPositionName()).build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleDeepLinkOrWxAppletsClick(Advertis advertis, boolean isAutoClick) {
        if (advertis == null) {
            return;
        }
        if (isAutoClick) {
            Logger.v("-------msg", " ------ handleDeepLinkOrWxAppletsClick  ----- 11111 needShowDpDialog = " + isAutoClick);
            AdOpenOtherAppDialog adOpenOtherAppDialog = new AdOpenOtherAppDialog(advertis);
            Activity mainActivity = MainApplication.getMainActivity();
            if (mainActivity instanceof MainActivity) {
                adOpenOtherAppDialog.showDialogView((FragmentActivity) mainActivity);
            }
            try {
                ArrayList<AdSDKAdapterModel> models = new ArrayList<>();
                models.add(AdConversionUtil.conversionModel(advertis, advertis.getPositionName()));
                InnerHelper.getInstance().batchAdRecord(mainActivity, models,
                        SDKAdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW, advertis.getPositionName()).build());
            } catch (Exception e) {
                e.printStackTrace();
            }
//
//            if (isAutoClick && advertis.getBusinessExtraInfo() != null && advertis.getBusinessExtraInfo().isScrollClickReport()) {
//                AdReportModel adReportModel = (AdReportModel) AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
//                                TextUtils.isEmpty(advertis.getPositionName()) ? AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId())
//                                        : advertis.getPositionName())
//                        .ignoreTarget(true)
//                        .onlyClickRecord(true)
//                        .autoPull(2)
//                        .build();
//                AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), advertis, adReportModel);
////                recordClick(advertis);
//            }
        } else {
            Logger.v("-------msg", " ------ handleDeepLinkOrWxAppletsClick  ----- 2222 needShowDpDialog = " + isAutoClick);
            Logger.v("-------msg", " ------ handleDeepLinkOrWxAppletsClick  -----  dp跳转 或 微信小程序 " + advertis.getDpRealLink());
            InnerHelper.getInstance().clickAd(AdConversionUtil.conversionModel(advertis, advertis.getPositionName()), null,
                    new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_SITE_CLICK, advertis.getPositionName())
                            .build(), IInnerProvider.IClickViewType.ONLY_NORMAL_CLICK_VIEW_TYPE);
        }
    }

    public void handleDownloadClick(Advertis mAdvertis, boolean isAutoClick, boolean isDialogClick) {

        if (isAutoClick) {
            Logger.v("-------msg", " ------ handleDownloadClick  ----- 下载  ---- 自动拉起的流程");
            String downloadKey = XmDownloadUtils.getDownloadKey(mAdvertis.getRealLink());
            XmDownloadInfo downloadInfo = InnerHelper.getInstance().getDownloadTaskManager().getDownloadInfoByOnlyKey(downloadKey);

            if (downloadInfo != null
                    && (downloadInfo.status == XmDownloadInfo.Status.FINISHED
                    || downloadInfo.status == XmDownloadInfo.Status.INSTALLED
                    || downloadInfo.status == XmDownloadInfo.Status.DOWNLOADING
                    || AdApkInstallManager.getInstance().isInstallApkByAdvertis(mAdvertis))) {
                Logger.e("-----------msg", " ---------- showDownloadDialog ---- 该app 已被安装-下载完成 --------- return 下一步");
                if (downloadInfo.status != XmDownloadInfo.Status.DOWNLOADING) {
                    InnerHelper.getInstance().getDownloadTaskManager().showDownloadDialogByDownloadInfo(MainApplication.getTopActivity(),
                            AdConversionUtil.conversionModel(mAdvertis, mAdvertis.getPositionName()), downloadInfo);
                    currentAdvertis = mAdvertis;
                    InnerHelper.getInstance().getDownloadTaskManager().addTaskListener(getDownloadTaskListener());
                    if (!isDialogClick) {
                        try {
                            ArrayList<AdSDKAdapterModel> models = new ArrayList<>();
                            models.add(AdConversionUtil.conversionModel(mAdvertis, mAdvertis.getPositionName()));
                            InnerHelper.getInstance().batchAdRecord(MainApplication.getTopActivity(), models,
                                    SDKAdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW, mAdvertis.getPositionName()).build());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                return;
            }
//            if (isAutoClick && mAdvertis.getBusinessExtraInfo() != null && mAdvertis.getBusinessExtraInfo().isScrollClickReport()) {
//                if (isDialogClick) {
//                    recordClick(mAdvertis);
//                } else {
//                    AdReportModel adReportModel = (AdReportModel) AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
//                                    TextUtils.isEmpty(mAdvertis.getPositionName()) ? AdPositionIdManager.getPositionNameByPositionId(mAdvertis.getAdPositionId())
//                                            : mAdvertis.getPositionName())
//                            .ignoreTarget(true)
//                            .onlyClickRecord(true)
//                            .autoPull(2)
//                            .build();
//                    AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), mAdvertis, adReportModel);
//                }
//            }
            if (!isDialogClick) {
                try {
                    ArrayList<AdSDKAdapterModel> models = new ArrayList<>();
                    models.add(AdConversionUtil.conversionModel(mAdvertis, mAdvertis.getPositionName()));
                    InnerHelper.getInstance().batchAdRecord(MainApplication.getTopActivity(), models,
                            SDKAdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW, mAdvertis.getPositionName()).build());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            Logger.e("-------------------msg", " ------------ onFragmentPause  " + isFragmentPause);
            if (isFragmentPause) {
                Logger.e("-------msg", " ------ handleDownloadClick 页面不可见---return ");
                return;
            }
            currentAdvertis = mAdvertis;
            InnerHelper.getInstance().getDownloadTaskManager().handleDownloadActionByAdModel(MainApplication.getTopActivity(),
                    AdConversionUtil.conversionModel(currentAdvertis, TextUtils.isEmpty(currentAdvertis.getPositionName()) ?
                            AdPositionIdManager.getPositionNameByPositionId(currentAdvertis.getAdPositionId())
                            : currentAdvertis.getPositionName()), true);
            InnerHelper.getInstance().getDownloadTaskManager().addTaskListener(getDownloadTaskListener());
        } else {
            Logger.v("-------msg", " ------ handleDownloadClick  ----- 下载  ---- 点击流程");
            InnerHelper.getInstance().clickAd(AdConversionUtil.conversionModel(mAdvertis, mAdvertis.getPositionName()), null,
                    new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_SITE_CLICK, mAdvertis.getPositionName())
                            .build(), IInnerProvider.IClickViewType.ONLY_NORMAL_CLICK_VIEW_TYPE);
        }
    }

    private IDownloadTaskListener iDownloadTaskListener;

    private IDownloadTaskListener getDownloadTaskListener() {

        if (iDownloadTaskListener == null) {
            iDownloadTaskListener = new IDownloadTaskListenerAdapter() {
//                volatile boolean isDownloadRecorded = false;

                @Override
                public void onStart(XmDownloadInfo info, boolean isRestart) {
//                    if (isDownloadRecorded) {
//                        return;
//                    }
                    if (info.adId == currentAdvertis.getAdid()) {
                        recordDownload();
                    }
                }

                @Override
                public void onSuccess(XmDownloadInfo info) {
//                    if (isDownloadRecorded) {
//                        return;
//                    }
                    if (info.adId == currentAdvertis.getAdid()) {
                        recordDownload();
                    }
                }

                @Override
                public void onInstallBegin(XmDownloadInfo info) {
//                    if (isDownloadRecorded) {
//                        return;
//                    }
                    if (info.adId == currentAdvertis.getAdid()) {
                        recordDownload();
                    }
                }

                @Override
                public void onInstallSuccess(XmDownloadInfo info) {
//                    if (isDownloadRecorded) {
//                        return;
//                    }
                    if (info.adId == currentAdvertis.getAdid()) {
                        recordDownload();
                    }
                }

                @Override
                public void onOpenApk(XmDownloadInfo info, boolean isSuccess) {
//                    if (isDownloadRecorded) {
//                        return;
//                    }
                    if (info.adId == currentAdvertis.getAdid()) {
                        recordDownload();
                    }
                }

                private synchronized void recordDownload() {

//                    if (isDownloadRecorded || mAdvertis == null) {
//                        return;
//                    }
//                    Logger.w("--------msg", " --------- -- - --- recordDownload  ----- 下载事件上报 ------- " + isDownloadRecorded);
//                    isDownloadRecorded = true;
                    if (!currentAdvertis.isClickToRecorded()) {
                        AdReportModel adReportModel = (AdReportModel) AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                        TextUtils.isEmpty(currentAdvertis.getPositionName()) ? AdPositionIdManager.getPositionNameByPositionId(currentAdvertis.getAdPositionId())
                                                : currentAdvertis.getPositionName())
                                .ignoreTarget(true)
                                .onlyClickRecord(true)
                                .autoPull(3)
                                .build();
                        AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), currentAdvertis, adReportModel);
                        currentAdvertis.setClickToRecorded(true);
                    }
                }
            };
        }
       return iDownloadTaskListener;
    }


    private void showAdDialog(IBottomViewCallback mBottomViewCallback, List<INativeAd> nativeAdList, IAdActionProvider iRecordListener) {
        if (mCurrentAdDialog != null && mCurrentAdDialog.get() != null) {
            mCurrentAdDialog.get().dismiss();
        }
        RecentlyAdListDialogFragment recentlyAdListDialogFragment = new RecentlyAdListDialogFragment();
        recentlyAdListDialogFragment.setData(nativeAdList);
        if (mBottomViewCallback != null) {
            recentlyAdListDialogFragment.show(mBottomViewCallback.getFragmentManager(), "RecentlyAdListDialog");
            if (iRecordListener != null) {
                iRecordListener.needDoAction();
            }
        }
        mCurrentAdDialog = new WeakReference<>(recentlyAdListDialogFragment);
    }

    public void autoClickAdFromConfig(List<INativeAd> mLastNativeAds, IAdNeedAutoClick iAdActionProvider) {

        if (isNewAutoScrollClick(mLastNativeAds)) {
            if (iAdActionProvider != null) {
                iAdActionProvider.needAutoClick(true);
            }
            return;
        }

        boolean isAutoClick = ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_PLAY_PAGE_CART_AUTO_CLICK, false);
        long clickIntervalTime = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_PLAY_PAGE_CART_AUTO_CLICK_INTERVAL, 3600);
        int clickPerThousand = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_PLAY_PAGE_CART_AUTO_CLICK_PER_THOUSAND, -1);

        if (isAutoClick && isOverIntervalTime(clickIntervalTime) && isDeviceIdInPerThousand(clickPerThousand)) {
            if (iAdActionProvider != null) {
                iAdActionProvider.needAutoClick(true);
            }
            long currentTime = System.currentTimeMillis();
            MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveLong(KEY_AD_AUTO_CLICK_LAST_TIME, currentTime);
        } else {
            if (iAdActionProvider != null) {
                iAdActionProvider.needAutoClick(false);
            }
        }
    }

    public boolean isNeedAutoClick(List<INativeAd> mLastNativeAds) {
        if (isNewAutoScrollClick(mLastNativeAds)) {
            return true;
        }

        boolean isAutoClick = ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_PLAY_PAGE_CART_AUTO_CLICK, false);
        long clickIntervalTime = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_PLAY_PAGE_CART_AUTO_CLICK_INTERVAL, 3600);
        int clickPerThousand = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_PLAY_PAGE_CART_AUTO_CLICK_PER_THOUSAND, -1);

        if (isAutoClick && isOverIntervalTime(clickIntervalTime) && isDeviceIdInPerThousand(clickPerThousand)) {
            return true;
        } else {
            return false;
        }
    }

    private boolean isDeviceIdInPerThousand(int clickPerThousand) {
        if (clickPerThousand == 0) {
            return false;
        }
        try {
            String deviceToken = DeviceUtil.getDeviceToken(MainApplication.getMyApplicationContext());
            int deviceHash = Math.abs(deviceToken.hashCode());
            int devicePerThousand = deviceHash % 1000;
            if (devicePerThousand <= clickPerThousand) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private boolean isOverIntervalTime(long clickIntervalTime) {
        long lastShowTime = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getLongCompat(KEY_AD_AUTO_CLICK_LAST_TIME);
        long currentTime = System.currentTimeMillis();
        if (lastShowTime + clickIntervalTime * 1000 < currentTime) {
            return true;
        }
        return false;
    }

    private boolean isNewAutoScrollClick(List<INativeAd> mLastNativeAds) {
        try {
            if (mLastNativeAds != null && mLastNativeAds.get(0) != null && mLastNativeAds.get(0).isXmNativeAd() && mLastNativeAds.get(0).getAdSDKAdapterModel()!=null){
                AdSDKAdapterModel adSDKAdapterModel = mLastNativeAds.get(0).getAdSDKAdapterModel();
                if (adSDKAdapterModel!=null && adSDKAdapterModel.getBusinessExtraInfo()!=null && adSDKAdapterModel.getBusinessExtraInfo().isAutoScroll()){
                    return true;
                }
            }
//
//            if (mLastNativeAds != null && mLastNativeAds.get(0) != null && mLastNativeAds.get(0).getAdModel() != null
//                    && (mLastNativeAds.get(0) instanceof XmNativeAd) && ((AdModel) mLastNativeAds.get(0).getAdModel()).getBusinessExtraInfo() != null
//                    && ((AdModel) mLastNativeAds.get(0).getAdModel()).getBusinessExtraInfo().isAutoScroll()) {
//                return true;
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Logger.e("-----------msg", " -------------- 旧版的 自动点击。。。。");
        return false;
    }

    public void recordClick(Advertis mAdvertis) {
        if (mAdvertis != null && !mAdvertis.isClickToRecorded()) {
            AdReportModel adReportModelRecord = (AdReportModel) AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                            TextUtils.isEmpty(mAdvertis.getPositionName()) ? AdPositionIdManager.getPositionNameByPositionId(mAdvertis.getAdPositionId())
                                    : mAdvertis.getPositionName())
                    .ignoreTarget(true)
                    .onlyClickRecord(true)
                    .autoPull(3)
                    .build();
            AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), mAdvertis, adReportModelRecord);
            mAdvertis.setClickToRecorded(true);
        }
    }

    public void onFragmentPause() {
        isFragmentPause = true;
//        InnerHelper.getInstance().getDownloadTaskManager().cancelDownloadDialog();
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                InnerHelper.getInstance().getDownloadTaskManager().cancelDownloadDialog();
            }
        }, 150);
    }

    public void onFragmentResume() {
        isFragmentPause = false;
    }

    public interface IAdNeedAutoClick {
        void needAutoClick(boolean isNeedClick);
    }
}
