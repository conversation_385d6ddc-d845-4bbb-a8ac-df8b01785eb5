package com.ximalaya.ting.android.main.playpage.playy.component.commercial;

import android.content.Context;
import android.util.Pair;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenLogManager;
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager;
import com.ximalaya.ting.android.host.manager.play.PlayAutoBuyTrackActionManager;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.service.TingLocalMediaService;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.dialog.universal.UniversalPaymentActionsDialog;
import com.ximalaya.ting.android.main.playpage.FragmentFinishCallbackForPlayPageTab;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.manager.ITrackOverAuditionChildView;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.manager.ITrackOverAuditionDataProvider;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.manager.ITrackOverAuditionUiProvider;
import com.ximalaya.ting.android.main.playpage.fragment.BasePlayPageTabFragment;
import com.ximalaya.ting.android.main.playpage.internalservice.ITrackOverAuditionComponentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.playy.ScreenChangeListener;
import com.ximalaya.ting.android.main.playpage.playy.XPlayPageStatus;
import com.ximalaya.ting.android.main.playpage.playy.component.base.XBaseCoverComponentWithPlayStatusListener;
import com.ximalaya.ting.android.main.playpage.playy.listener.IPlayPageControlChangeListener;
import com.ximalaya.ting.android.main.playpage.playy.manager.PlayPageControlManager;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerConfig;
import com.ximalaya.ting.android.opensdk.util.FreeListenV2Util;
import com.ximalaya.ting.android.xmutil.Logger;


public class XTrackOverAuditionComponentX extends XBaseCoverComponentWithPlayStatusListener
        implements IPlayPageControlChangeListener, ITrackOverAuditionDataProvider, ITrackOverAuditionUiProvider, ViewManagerCore.ICallbackForCore, ScreenChangeListener {

    private static final int DP40 = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 40f);
    private static final int DP80 = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 80f);
    private static final int DP105 = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 105f);
    private static final int DP24 = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 24f);

    private int viewType;
    private boolean isActivate = true;

    private com.ximalaya.ting.android.main.playpage.playy.component.commercial.ViewManagerCore managerCore;
    private boolean addToServiceManager = true;

    private FragmentFinishCallbackForPlayPageTab fragmentFinishCallback;
    private long mNeedShowTrackId;
    private Pair<Long,Boolean> auditionOverCallPair = new Pair(0L,false);

    public XTrackOverAuditionComponentX() {
        updateViewType(true);
        initChildViewManager();
    }

    private void updateViewType(boolean isFullScreen) {
        this.viewType = ITrackOverAuditionChildView.LOCATION_X_PLAY_PAGE_FULL_SCREEN;
    }

    private void initChildViewManager() {
        managerCore = new ViewManagerCore(this);
        managerCore.initChildViewManager(viewType);
    }

    @Override
    public void onCreate(BaseFragment2 fragment) {
        super.onCreate(fragment);
        if (addToServiceManager) {
            PlayPageInternalServiceManager.getInstance().registerService(ITrackOverAuditionComponentService.class,
                    () -> {
                        if (getCoverComponentsManager() != null) {
                            getCoverComponentsManager().updateComponents();
                        }
                    });
        }
        PlayPageControlManager.INSTANCE.addPlayControlChangeListener(this);
    }

    @Override
    public void initUi() {
        if (null != managerCore) {
            managerCore.initUi(mContentView);
            if(getCoverComponentsManager() != null && getCoverComponentsManager().getPlayContainer() != null){
                onFullScreenChanged(true);
                getCoverComponentsManager().getPlayContainer().addScreenChangeListener(this);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();

        if (getCoverComponentsManager() != null) {
            getCoverComponentsManager().updateComponents();
        }

        if (null != managerCore) {
            managerCore.doOnResume();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        unregisterBuyXiMiVipBroadcast();
        PlayPageInternalServiceManager.getInstance().unRegisterService(ITrackOverAuditionComponentService.class);
        PlayPageControlManager.INSTANCE.removePlayControlChangeListener(this);
    }

    @Override
    public boolean needShowThisComponent(PlayingSoundInfo soundInfo) {
        if (soundInfo != null) {
            TrackM trackM = soundInfo.trackInfo2TrackM();
            return needShowThisComponent(trackM, soundInfo);
        }
        return false;
    }

    private boolean needShowThisComponent(TrackM trackM, @NonNull PlayingSoundInfo soundInfo) {
        if (trackM != null) {
            if (PlayAutoBuyTrackActionManager.getInstance().isAutoBuyOpen(trackM)) {
                return false;
            }
            int historyPos = XmPlayerManager.getInstance(mContext).getHistoryPos(trackM.getDataId());
            if (FreeListenConfigManager.isFreeListenV2Open()) {
                //畅听不出购买
                boolean supportFreeListen = trackM != null && trackM.getAlbum() != null && trackM.getAlbum().supportFreeListen();
                TrackM info2TrackM = soundInfo.trackInfo2TrackM();
                boolean supportFreeListenBySoundInfo = info2TrackM != null && info2TrackM.getAlbum() != null && info2TrackM.getAlbum().supportFreeListen();
                Logger.w("z_freelisten", "XTrackOverAuditionComponentX needShowThisComponent, supportFreeListen: " + supportFreeListen + ", supportFreeListenBySoundInfo: " + supportFreeListenBySoundInfo);
                if (supportFreeListenBySoundInfo) {
                    return false;
                }
            }

            if (!trackM.isAuthorized()
                    && (trackM.isPayTrack()
                    || ViewManagerCore.isXiMiFirstAlbum(soundInfo)
                    || ViewManagerCore.isVipPriorSound(soundInfo))) {
                if (trackM.isAudition()) {
                    boolean isPlayCompleted;
                    if (trackM.getDataId() == auditionOverCallPair.first) {
                        if (XmPlayerConfig.getInstance(mContext).isBreakpointResume()) {
                            isPlayCompleted = historyPos == PlayerConstants.PLAY_COMPLETE;
                        } else {
                            isPlayCompleted = auditionOverCallPair.second;
                        }
                    } else {
                        isPlayCompleted = historyPos == PlayerConstants.PLAY_COMPLETE;
                        auditionOverCallPair = Pair.create(0L, false);
                    }
                    if (isPlayCompleted
                            && !XmPlayerManager.getInstance(mContext).isLoading()
                            && !XmPlayerManager.getInstance(mContext).isTrackPlaying()) {
                        mNeedShowTrackId = 0;
                        return true;
                    } else {
                        mNeedShowTrackId = trackM.getDataId();
                        return false;
                    }
                } else if (trackM.getType() == Track.TYPE_TRAINING_CAMP) {
                    return true;
                } else {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void onSoundInfoLoaded(PlayingSoundInfo soundInfo) {
        super.onSoundInfoLoaded(soundInfo);

        if (stopByFreeListen()) {
            return;
        }

        if (null != managerCore) {
            managerCore.doOnSoundInfoLoaded(soundInfo);
        }
    }

    private boolean stopByFreeListen() {

        Track currentTrack = getCurrentTrack();

        if (currentTrack == null) {
            currentTrack = PlayTools.getCurTrack(BaseApplication.getMyApplicationContext());
        }

        if (FreeListenConfigManager.isFreeListenV2Open()) {
            //畅听不出购买
            boolean supportFreeListen = currentTrack != null && currentTrack.getAlbum() != null && currentTrack.getAlbum().supportFreeListen() && !UserInfoMannage.isVipUser();
            Logger.w("z_freelisten", "XTrackOverAuditionComponentX onRequestPlayUrlError, supportFreeListen: " + supportFreeListen);
            if (supportFreeListen) {

                if (!FreeListenV2Util.getInstance().checkTrackPermission(currentTrack)) {
                    FreeListenLogManager.writeLog("XTrackOverAuditionComponentX  onRequestPlayUrlError >>> checkTrackPermission return false, stop play");

                    XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).stop();
                    if (AdMakeVipLocalManager.getInstance().checkAdAudioCode()) {
                        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).playFreeListenTimeRunOutSoundPatch(false);
                    }

                    TingLocalMediaService.getInstance().notifyStopPlayDueToFreeListen();
                }
                return true;
            }
        }
        return false;
    }

    @Override
    public void onRequestPlayUrlError(int code, String message) {
        super.onRequestPlayUrlError(code, message);

        stopByFreeListen();
    }

    @Override
    public boolean needListenPlayStatusEvenHidden() {
        return true;
    }


    @Override
    public void onPlayStart() {
        super.onPlayStart();
        if (canUpdateUi() && isVisible() && getCoverComponentsManager() != null) {
            getCoverComponentsManager().updateComponents();
        }
    }

    @Override
    public void onAudioAuditionOver(Track track) {
        super.onAudioAuditionOver(track);
        if (PlayTools.getCurTrackId(getContext()) == mNeedShowTrackId) {
            auditionOverCallPair = Pair.create(mNeedShowTrackId, true);
            if (getCoverComponentsManager() != null) {
                getCoverComponentsManager().updateComponents();
            }
        }
    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        super.onSoundSwitch(lastModel, curModel);
        if (canUpdateUi()) {
            // dismissOverAuditionDialog();
        }
        unregisterBuyXiMiVipBroadcast();
    }


    @Override
    protected int getViewStubId() {
        return R.id.main_track_over_audition_stub;
    }


    @Override
    public long getCurrentAlbumId() {
        return super.getCurAlbumId();
    }

    @Override
    public long getCurrentTrackId() {
        return super.getCurTrackId();
    }

    @Override
    public PlayingSoundInfo getPlayingSoundInfo() {
        return super.getCurSoundInfo();
    }

    @Override
    public Track getCurrentTrack() {
        PlayingSoundInfo soundInfo = super.getCurSoundInfo();
        if (soundInfo != null) {
            return soundInfo.trackInfo2TrackM();
        }
        return null;
    }

    @Override
    public void dismissOverAuditionDialog() {
        UniversalPaymentActionsDialog.dismissDialog();
    }

    @Override
    public void startFragment(Fragment fragment) {
        super.startFragment(fragment);
    }

    @Override
    public FragmentManager getFragmentManager() {
        return super.getFragmentManager();
    }

    @Override
    public BaseFragment2 getFragment() {
        return mFragment;
    }

    @Override
    public boolean canUpdateUi() {
        return super.canUpdateUi();
    }

    @Override
    public void registerBuyXiMiVipBroadcast() {
        // AudioPlayXiMiVipPayBroadcastManager.register(getFragment(), getCurrentTrackId());
    }

    private void unregisterBuyXiMiVipBroadcast() {
        // AudioPlayXiMiVipPayBroadcastManager.unregister(getContext());
    }

    @Override
    public FragmentFinishCallbackForPlayPageTab getFragmentFinishCallback(long albumId) {
        if (fragmentFinishCallback == null && mFragment instanceof BasePlayPageTabFragment) {
            fragmentFinishCallback = FragmentFinishCallbackForPlayPageTab.newInstance(albumId, (BasePlayPageTabFragment) mFragment, this);
        }
        if (fragmentFinishCallback != null) {
            fragmentFinishCallback.setAlbumId(albumId);
        }
        return fragmentFinishCallback;
    }

    @Override
    public Context getComponentContext() {
        return this.getContext();
    }

    @Override
    public void onPlayPageControlActivate(boolean isActivate) {
        this.isActivate = isActivate;
        updateContentViewMargin(isActivate);
    }

    @Override
    public void onPlayPageControlRealFullScreen(boolean isFull) {

    }

    @Override
    public void onPlayPageControlFullScreen(boolean isFull) {
//        updateViewType(isFull);
//        this.isActivate = true;
//        updateContentViewMargin(this.isActivate);
//        if (null != managerCore) {
//            managerCore.updateViewType(viewType);
//            managerCore.doOnPlayPageControlFullScreenStatusChange(getPlayingSoundInfo());
//        }
    }

    @Override
    public void onStageAreaSizeChanged(int targetHeight, boolean isAdExpand) {
        // do Nothing for Now
    }

    @Override
    protected void inflateAndInitUI() {
        super.inflateAndInitUI();
        updateContentViewMargin(this.isActivate);
        /*if (null != mContentView && ConstantsOpenSdk.isDebug) {
            mContentView.setBackgroundColor(Color.parseColor("#2800ff00"));
        }*/
    }

    private void updateContentViewMargin(boolean isActivate) {
        if (null == mContentView) {
            return;
        }
        ViewGroup.LayoutParams layoutParams = mContentView.getLayoutParams();
        ViewGroup.MarginLayoutParams marginLayoutParams = null;
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            marginLayoutParams = (ViewGroup.MarginLayoutParams) layoutParams;
        }
        if (null == marginLayoutParams) {
            return;
        }
        marginLayoutParams.topMargin = DP24;
        if (ITrackOverAuditionChildView.LOCATION_X_PLAY_PAGE_FULL_SCREEN == viewType) {
            // 全屏
            marginLayoutParams.bottomMargin = DP105;
        } else {
            if (isActivate) {
                marginLayoutParams.bottomMargin = DP80;
            } else {
                marginLayoutParams.bottomMargin = DP40;
            }
        }
        mContentView.setLayoutParams(marginLayoutParams);
    }

    @Override
    public void beforeFullScreenChanged(boolean fullScreen) {

    }

    @Override
    public void onFullScreenChanged(boolean fullScreen) {
        Track track = getCurrentTrack();
        if (FreeListenConfigManager.isFreeListenV2Open() ) {
            //畅听不出购买
            boolean supportFreeListen = track != null && track.getAlbum() != null && track.getAlbum().supportFreeListen();
            Logger.w("z_freelisten", "XTrackOverAuditionComponentX needShowThisComponent, supportFreeListen: " + supportFreeListen);
            if (supportFreeListen) {
                return;
            }
        }

        updateViewType(fullScreen);
        this.isActivate = true;
        updateContentViewMargin(this.isActivate);
        if (null != managerCore) {
            managerCore.updateViewType(viewType);
            managerCore.doOnPlayPageControlFullScreenStatusChange(getPlayingSoundInfo());
        }
    }

    @Override
    public void afterFullScreenChanged(boolean fullScreen) {

    }

    @Override
    public void onScreenStatusChanged(@NonNull XPlayPageStatus xPlayPageStatus) {

    }
}
