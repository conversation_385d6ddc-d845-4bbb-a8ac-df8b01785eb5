package com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.view;

import android.content.Context;
import android.os.Build;
import android.transition.ChangeBounds;
import android.transition.Transition;
import android.transition.TransitionManager;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageAdaptationUtilKt;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.AudioPlayAdUtil;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.manager.AdSoundPatchWebManager;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.IXAdComponentProvider;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * Created by le.xin on 2020/6/14.
 * 横版视频广告布局
 * <AUTHOR>
 * @email <EMAIL>
 */
public class XHorizontalVideoSmallAdView extends XBaseVideoAdView {
    public XHorizontalVideoSmallAdView(Context context, IXAdComponentProvider adComponentProvider) {
        super(context, adComponentProvider);
        changeCornerForSmallDevice();
    }

    public XHorizontalVideoSmallAdView(Context context, AttributeSet attrs) {
        super(context, attrs);
        changeCornerForSmallDevice();
    }

    public XHorizontalVideoSmallAdView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        changeCornerForSmallDevice();
    }

    private void changeCornerForSmallDevice() {
        if (mAdComponentProvider == null) {
            return;
        }
        if (mAdComponentProvider.getAdEngineProvider() != null && mAdComponentProvider.getAdEngineProvider().getAdContentLayout() != null) {
            if (mAdComponentProvider.getAdEngineProvider().getAdContentLayout().getPaddingLeft() != 0) {
                if (mLayoutView != null) {
                    mLayoutView.setCornerRadius(BaseUtil.dp2px(getContext(), 8));
                }
                if (mBottomAdView != null) {
                    mBottomAdView.setRatioCorner();
                }
            }
        }
    }

    protected View getView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.main_play_video_view_y_small, this, true);
        return view;
    }

    @Override
    public void bindViewAfter() {
        if (mAdTag == null || mAdFeedBack == null || getCurAbstractAd() == null || mNativeAdContainer == null) {
            return;
        }
        try {
            //放在对应的广告类型里面处理好点
            if (!AdManager.isThirdAd(getCurAbstractAd())) {
                mAdFeedBack.setVisibility(View.GONE);
            } else {
                if (AdManager.isCSJAd(getCurAbstractAd().getAdvertis())) {
                    mAdTag.setImageResource(R.drawable.host_csj_ad_tag_gray);
                } else if (AdManager.isGdtAd(getCurAbstractAd().getAdvertis())) {
                    mAdTag.setImageResource(R.drawable.host_gdt_ad_tag_gray);
                    mAdTag.setVisibility(View.VISIBLE);
                    ViewGroup.LayoutParams lp = mAdTag.getLayoutParams();
                    if (AdManager.isGdtAd(getCurAbstractAd().getAdvertis())) {
                        lp.width = BaseUtil.dp2px(getContext(), 12);
                    }
                    mAdTag.setLayoutParams(lp);
                    AdManager.removeGdtAdMask(mNativeAdContainer);
                } else {
                    mAdTag.setImageResource(R.drawable.host_jad_ad_tag_gray);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void initNormalCoverView() {
        if (mLayoutView != null && mAdCover != null && mLayoutView.indexOfChild(mAdCover) < 0) {
            try {
                if (mAdCover.getParent() != null) {
                    ((ViewGroup)mAdCover.getParent()).removeView(mAdCover);
                }
                mLayoutView.addView(mAdCover);
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    protected void initLargeCoverView() {
        Logger.e("---------msg", " --------- initLargeCoverView  11111111------- ");
        if (mLayoutView != null && mAdCover != null && mLayoutView.indexOfChild(mAdCover) >= 0) {
            try {
                if (mAdCover.getParent() != null) {
                    ((ViewGroup)mAdCover.getParent()).removeView(mAdCover);
                }
                Logger.e("---------msg", " --------- initLargeCoverView  222222222------- ");
                FrameLayout frameLayout = new FrameLayout(getContext());
                LayoutParams layoutParams = new LayoutParams(LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT);
                layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
                frameLayout.setLayoutParams(layoutParams);
                frameLayout.addView(mAdCover);
                mLayoutView.addView(frameLayout);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    protected boolean isVerticalViewAd() {
        return false;
    }

    @Override
    protected boolean isBottomInCover() {
        return false;
    }

    @Override
    protected boolean isButtonShowInBottomCenter() {
        return false;
    }

    @Override
    protected boolean isNeedRender() {
        return false;
    }

    @Override
    protected boolean isNeedHideBottomTitleWhenEnd() {
        return false;
    }

    @Override
    protected boolean isNeedHideBottomButtonWhenEnd() {
        return true;
    }

    public void initLargeCoverAdSize() {
        setVisibility(GONE);
        int targetHeight = AudioPlayPageAdaptationUtilKt.isLargeDevice() ? BaseUtil.dp2px(getContext(), 500) :
                BaseUtil.dp2px(getContext(), 404);
        mLargeCoverHeight = targetHeight;
        if (mAdComponentProvider != null && mAdComponentProvider.getCoverComponentsManager() != null) {
            mAdComponentProvider.getCoverComponentsManager().extendCoverHeight(targetHeight);
        }
        setLargeAdVideoLayoutSize(targetHeight);
        AdSoundPatchWebManager.getInstance().changeWebViewTopMarge((int) (targetHeight), false);
    }

    public void initNormalAdSize() {
        super.initNormalAdSize();
        isLargeCover = false;
        mBottomAdView.setVisibility(canShowBottomLay ? View.VISIBLE : View.GONE);
        LayoutParams adVideoLayoutParams2222 = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        adVideoLayoutParams2222.addRule(RelativeLayout.CENTER_HORIZONTAL);
        mNativeAdContainer.setLayoutParams(adVideoLayoutParams2222);

//        FrameLayout.LayoutParams adVideoLayoutParams = (FrameLayout.LayoutParams) mRootLayoutView.getLayoutParams();
//        adVideoLayoutParams.gravity = Gravity.CENTER;
//        mRootLayoutView.setLayoutParams(adVideoLayoutParams);
    }

    public void resetCoverSizeToNormal(boolean isNeedShowBottomView) {
        super.resetCoverSizeToNormal(isNeedShowBottomView);
        int transitionDuration = 450;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            Transition autoTransition = new ChangeBounds();
            autoTransition.setDuration(transitionDuration);
            RelativeLayout coverRootLayout = null;
            if (mFragment != null) {
                coverRootLayout = mFragment.findViewById(R.id.main_vg_cover_container_real_layout);
            }
            if (coverRootLayout != null) {
                if (coverRootLayout.getParent() != null) {
                    TransitionManager.beginDelayedTransition((ViewGroup) coverRootLayout.getParent(), autoTransition);
                } else {
                    TransitionManager.beginDelayedTransition(coverRootLayout, autoTransition);
                }
            } else {
                if (mAdComponentProvider != null) {
                    TransitionManager.beginDelayedTransition(mNativeAdContainer, autoTransition);
                }
            }
            if (mBottomAdView != null) {
//                TransitionManager.beginDelayedTransition(mBottomAdView, autoTransition);
            }
        }
        if (mBottomAdView != null) {
            if (canShowBottomLay) {
                mBottomAdView.setShowLargeCover(false);
                mBottomAdView.setContainerBackgroundResource(R.drawable.main_ad_bottom_bg);
                ConstraintSet bottomViewSet = new ConstraintSet();
                bottomViewSet.clone(mRootLayoutView);
                bottomViewSet.clear(R.id.main_video_bottom_ad, ConstraintSet.TOP);
                bottomViewSet.clear(R.id.main_video_bottom_ad, ConstraintSet.BOTTOM);
                bottomViewSet.connect(R.id.main_video_bottom_ad, ConstraintSet.TOP,  R.id.main_play_video_lay, ConstraintSet.BOTTOM);
                bottomViewSet.connect(R.id.main_video_bottom_ad, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);
                bottomViewSet.applyTo(mRootLayoutView);

            } else {
                mBottomAdView.setVisibility(View.GONE);
            }
        }

        double screenWidth = BaseUtil.getScreenWidth(getContext()) * 1f;
        double targetWidth = screenWidth;
        if (mAdComponentProvider.getAdEngineProvider() != null && mAdComponentProvider.getAdEngineProvider().getAdContentLayout() != null) {
            targetWidth = screenWidth - mAdComponentProvider.getAdEngineProvider().getAdContentLayout().getPaddingLeft() * 2;
        }
        double targetHeight = targetWidth / 16 * 9;
        setLargeAdVideoSize((int) targetWidth, (int) targetHeight);


        int coverContainerNormalHeight = -1;
        coverContainerNormalHeight =  (int) (targetHeight + AudioPlayAdUtil.getAdBottomTitleHeight());
        if (mNativeAdContainer!= null) {
            LayoutParams adVideoLayoutParams2222 = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
            adVideoLayoutParams2222.addRule(RelativeLayout.CENTER_IN_PARENT);
            mNativeAdContainer.setLayoutParams(adVideoLayoutParams2222);
        }
//        if (mRootLayoutView != null) {
//            FrameLayout.LayoutParams adVideoLayoutParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT);
//            if (AdManager.isDownloadAd(mAdvertis)
//                    || AdSoundPatchWebManager.getInstance().isInterceptAutoShow) {
//                adVideoLayoutParams.gravity = Gravity.CENTER;
//            } else {
//                adVideoLayoutParams.gravity = Gravity.TOP;
//            }
//            mRootLayoutView.setLayoutParams(adVideoLayoutParams);
//        }

        mLayoutView.setRatio(16f/9);
        if (mLayoutView != null) {
            ConstraintLayout.LayoutParams videoLayout = (ConstraintLayout.LayoutParams)mLayoutView.getLayoutParams();
            videoLayout.width = (int) targetWidth;
            videoLayout.height = (int) targetHeight;
            mLayoutView.setLayoutParams(videoLayout);
            mLayoutView.setNotMeasuredView(true);
            mLayoutView.postDelayed(() -> {
                // 缩放动画结束，允许View刷新，给View设置圆角
                mLayoutView.setNotMeasuredView(false);
                mLayoutView.requestLayout();
            }, transitionDuration);
        }
        if (mAdCover != null) {
            FrameLayout.LayoutParams adCoverLayoutParams = (FrameLayout.LayoutParams) mAdCover.getLayoutParams();
            adCoverLayoutParams.height = (int) targetHeight;
            adCoverLayoutParams.width = (int) targetWidth;
            adCoverLayoutParams.gravity = Gravity.CENTER;
            mAdCover.setLayoutParams(adCoverLayoutParams);
        }
//        closeCoverPage();
        isLargeCover = false;

        if (isNeedShowBottomView) {
            if (AdManager.isDownloadAd(mAdvertis)) {
                Logger.e("---------msg", " -------- 下载类的广告， 走通用点击逻辑");
//            AdManager.handlerAdClick(getContext(), mAdvertis, AppConstants.AD_POSITION_NAME_SOUND_PATCH);
                showDownloadDialog();
            } else {
                showBottomWebCoverPage(coverContainerNormalHeight);
            }
        }
    }

    private void setLargeAdVideoLayoutSize(int targetHeight) {

        setLargeAdVideoSize((int) (targetHeight * 16 * 1d / 9), targetHeight);

        if (canShowBottomLay) {
            mBottomAdView.setVisibility(View.VISIBLE);
            mBottomAdView.setShowLargeCover(true);
            mBottomAdView.setContainerBackgroundResource(R.drawable.main_ad_bottom_bg_large_cover);
            ConstraintSet bottomViewSet = new ConstraintSet();
            bottomViewSet.clone(mRootLayoutView);
            bottomViewSet.clear(R.id.main_video_bottom_ad, ConstraintSet.TOP);
            bottomViewSet.clear(R.id.main_video_bottom_ad, ConstraintSet.BOTTOM);
            bottomViewSet.connect(R.id.main_video_bottom_ad, ConstraintSet.BOTTOM,  R.id.main_play_video_lay, ConstraintSet.BOTTOM);
            bottomViewSet.applyTo(mRootLayoutView);

        } else {
            mBottomAdView.setVisibility(View.GONE);
        }

        int screenWidth = BaseUtil.getScreenWidth(getContext());
        double targetWidth = screenWidth;
        if (mAdComponentProvider.getAdEngineProvider() != null && mAdComponentProvider.getAdEngineProvider().getAdContentLayout() != null) {
            targetWidth = screenWidth - mAdComponentProvider.getAdEngineProvider().getAdContentLayout().getPaddingLeft() * 2;
        }
        LayoutParams adVideoLayoutParams2222 = new LayoutParams((int)targetWidth, targetHeight);
        adVideoLayoutParams2222.addRule(RelativeLayout.CENTER_IN_PARENT);
        mNativeAdContainer.setLayoutParams(adVideoLayoutParams2222);

//        FrameLayout.LayoutParams adVideoLayoutParams = new FrameLayout.LayoutParams((int) targetWidth, targetHeight);
//        adVideoLayoutParams.gravity = Gravity.CENTER;
//        mRootLayoutView.setLayoutParams(adVideoLayoutParams);

        mLayoutView.setRatio(0);
        ConstraintLayout.LayoutParams videoLayout = (ConstraintLayout.LayoutParams) mLayoutView.getLayoutParams();
        videoLayout.width = (int) targetWidth;
        videoLayout.height = (int) targetHeight;
        mLayoutView.setLayoutParams(videoLayout);

        FrameLayout.LayoutParams adCoverLayoutParams = (FrameLayout.LayoutParams) mAdCover.getLayoutParams();
        adCoverLayoutParams.height = (int) targetHeight;
        adCoverLayoutParams.width = (int) (targetHeight * 16 * 1d / 9);
        adCoverLayoutParams.gravity = Gravity.CENTER;
        mAdCover.setLayoutParams(adCoverLayoutParams);
        isLargeCover = true;

        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                hideTrackTitle();
                setVisibility(VISIBLE);
            }
        }, 36);
    }

}
