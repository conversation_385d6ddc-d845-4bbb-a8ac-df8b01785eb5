package com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.view;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.Nullable;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

/**
 * Created by zhao.peng.
 * describe
 * Date: 2023/6/19
 */
public class XSkinAdHotZoneClickView extends View {

    public XSkinAdHotZoneClickView(Context context) {
        this(context, null);
    }

    public XSkinAdHotZoneClickView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public XSkinAdHotZoneClickView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void reSize(boolean fullScreen) {
        resizeClickHotZoneDefaultConfig(fullScreen);
    }


    private void resizeClickHotZoneDefaultConfig(boolean fullScreen) {
//        if (ConstantsOpenSdk.isDebug) {
//            setBackgroundColor(Color.parseColor("#40fd5353"));
//        }
        try {
            //默认全部可点击
            ViewGroup.LayoutParams layoutParams = getLayoutParams();
            if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                ((ViewGroup.MarginLayoutParams) layoutParams).topMargin = BaseUtil.dp2px(getContext(), fullScreen ? 95 : 85);
                ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin = BaseUtil.dp2px(getContext(), 16);
                ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin = BaseUtil.dp2px(getContext(), 16);
                ((ViewGroup.MarginLayoutParams) layoutParams).height = (int) (BaseUtil.getScreenWidth(getContext()) * (fullScreen ? 0.95 : 0.386));
            }
            setLayoutParams(layoutParams);
            setClickable(true);
        } catch (Exception exception) {
            exception.printStackTrace();
        }

    }
}
