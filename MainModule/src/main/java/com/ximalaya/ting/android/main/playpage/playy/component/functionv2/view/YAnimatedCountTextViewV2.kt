package com.ximalaya.ting.android.main.playpage.playy.component.functionv2.view

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.widget.TextView
import com.ximalaya.ting.android.host.util.common.StringUtil

@SuppressLint("AppCompatCustomView")
class YAnimatedCountTextViewV2 @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : TextView(context, attrs) {
    private var lastAnimation: Animator? = null

    private var count = 0L

    fun setCount(count: Long) {
        this.count = count
        if (lastAnimation?.isRunning != true) {
            updateText()
        }
    }

    private fun updateText() {
        text = "${StringUtil.getFriendlyNumStr(count)}"
    }

    fun animateChange(count: Long) {
        this.count = count

        val willDisAppear = ValueAnimator.ofFloat(0.55f, 0f).apply {
            this.addUpdateListener {
                val value = it.animatedValue as Float
                alpha = value
            }
            this.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator?) {
                }

                override fun onAnimationEnd(animation: Animator?) {
                    updateText()
                }

                override fun onAnimationCancel(animation: Animator?) {
                    updateText()
                }

                override fun onAnimationRepeat(animation: Animator?) {
                }

            })
            this.duration = 160
        }

        val willShow = ValueAnimator.ofFloat(0f, 0.55f).apply {
            this.addUpdateListener {
                val value = it.animatedValue as Float
                alpha = value
            }
            startDelay = 600
            this.duration = 160
        }

        AnimatorSet().apply {
            playSequentially(willDisAppear, willShow)
            lastAnimation = this
        }.start()
    }
}