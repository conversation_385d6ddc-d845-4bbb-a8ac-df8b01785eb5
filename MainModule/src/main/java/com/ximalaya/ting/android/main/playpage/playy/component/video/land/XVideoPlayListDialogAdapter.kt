package com.ximalaya.ting.android.main.playpage.playy.component.video.land

import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.host.model.album.AlbumVideoInfoModel
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.opensdk.constants.SharedModelConstants
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter

/**
 * Created by WolfXu on 2022/4/18.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class XVideoPlayListDialogAdapter(
        private val mOnItemClickListener: (position: Int, download: Boolean) -> Unit
): AbRecyclerViewAdapter<XVideoPlayListDialogAdapter.VideoItemViewHolder>() {
    var videoList: List<Track>? = null
    private var mCurVideoId: Long = 0

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VideoItemViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.main_item_xvideo_list_item_land, parent, false)
        return VideoItemViewHolder(view)
    }

    override fun onBindViewHolder(holder: VideoItemViewHolder, position: Int) {
        val videoInfo = videoList?.getOrNull(position) ?: return
        val context = holder.itemView.context ?: return
        var titleColor = Color.BLACK
        var detailInfoColor = Color.parseColor("#b3ffffff")

        if (videoInfo.dataId == mCurVideoId) {
            titleColor = 0xffff4444.toInt()
            detailInfoColor = 0xffff4444.toInt()
            holder.lottiePlaying?.let {
                it.visibility = View.VISIBLE
                it.playAnimation()
            }
        } else {
            holder.lottiePlaying?.let {
                it.cancelAnimation()
                it.visibility = View.GONE
            }
            if (XmPlayerManager.getInstance(context).getHistoryPos(videoInfo.dataId) == SharedModelConstants.PLAY_NO_HISTORY) {
                titleColor = Color.parseColor("#ffffff")
            } else {
                // 已播放的标题色值不一样
                titleColor = Color.parseColor("#66ffffff")
            }
        }
        holder.ivDurationIcon?.colorFilter = PorterDuffColorFilter(detailInfoColor, PorterDuff.Mode.SRC_IN)
        holder.ivPlayCountIcon?.colorFilter = PorterDuffColorFilter(detailInfoColor, PorterDuff.Mode.SRC_IN)
        holder.tvVideoTitle?.let {
            it.text =  videoInfo.trackTitle//if (videoInfo.dataId == mCurVideoId) videoInfo.trackTitle else videoInfo.trackTitle
            it.setTextColor(titleColor)
        }
        holder.tvPlayCount?.let {
            it.text = StringUtil.getFriendlyNumStr(videoInfo.playCount)
            it.setTextColor(detailInfoColor)
        }
        holder.tvDuration?.let {
            it.text = TimeHelper.toTime(videoInfo.duration.toDouble())
            it.setTextColor(detailInfoColor)
        }
    }


    override fun getItemCount(): Int {
        return videoList?.size ?: 0
    }

    override fun getItem(position: Int): Any? {
        return videoList?.getOrNull(position)
    }

    private fun getVideoCover(videoInfo: AlbumVideoInfoModel.AlbumVideoInfo): String? {
        var cover = videoInfo.videoCover
        if (cover.isNullOrEmpty()) {
            cover = videoInfo.coverLarge
            if (cover.isNullOrEmpty()) {
                cover = videoInfo.coverMiddle
                if (cover.isNullOrEmpty()) {
                    cover = videoInfo.coverSmall
                }
            }
        }
        return cover
    }

    fun setCurVideoId(curVideoId: Long) {
        mCurVideoId = curVideoId
    }

    inner class VideoItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvVideoTitle: TextView? = itemView.findViewById(R.id.main_tv_video_title)
        val tvPlayCount: TextView? = itemView.findViewById(R.id.main_tv_play_count)
        val tvDuration: TextView? = itemView.findViewById(R.id.main_tv_duration)
        val ivPlayCountIcon: ImageView? = itemView.findViewById(R.id.main_iv_play_count)
        val ivDurationIcon: ImageView? = itemView.findViewById(R.id.main_iv_duration)
        val lottiePlaying: XmLottieAnimationView? = itemView.findViewById(R.id.main_lottie_playing)


        init {
            itemView.setOnClickListener { mOnItemClickListener(adapterPosition, false) }
        }
    }

    private fun notifyItem(downloadTask: BaseDownloadTask?) {
        val downloadTrackId = downloadTask?.track?.dataId?: return
        videoList?.forEachIndexed { index, albumVideoInfo ->
            if (albumVideoInfo.dataId == downloadTrackId) {
                notifyItemChanged(index)
                return
            }
        }
    }
}