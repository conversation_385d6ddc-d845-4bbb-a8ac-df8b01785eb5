package com.ximalaya.ting.android.main.playpage.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Region
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.main.R


/**
 * Created by <PERSON><PERSON>Zhang on 2022/10/20.
 */
class AudioCoverView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
    private val rectF = RectF()
    private val paint = Paint().apply {
        isAntiAlias = true
        isDither = true
        color = 0x19000000
    }

    private val punchingRadius = 27.dpFloat / 2
    private val punchingPath = Path()

    private val clipPath = Path()
    var radius = 4.dpFloat
        set(value) {
            field = value
            invalidate()
        }

    // play count res
    private val textPaint = Paint().apply {
        isAntiAlias = true
        isDither = true
        color = Color.WHITE
        textSize = 10.dpFloat
    }

    private val iconPaint = Paint().apply {
        isAntiAlias = true
        isDither = true
    }

    private val playCountIcon = BitmapFactory.decodeResource(context.resources, R.drawable.main_play_count)
    private val playCoverMask = BitmapFactory.decodeResource(context.resources, R.drawable.main_play_track_cover_mask)
    private val viewRect = Rect()

    private var textWidth = 0f
    private var textContainerWidth = 0f
    var playCountStr: String = ""
        set(value) {
            field = value
            updateTextInfo(field)
            invalidate()
        }

    private fun updateTextInfo(text: String) {
        if (text.isBlank()) {
            textWidth = 0f
            return
        }
        textWidth = textPaint.measureText(text)
        textContainerWidth = 4.dpFloat + playCountIcon.width + 2.dpFloat+ textWidth + 5.dpFloat
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas?) {
        clipPath.reset()
        rectF.set(0f, 0f, width.toFloat(), height.toFloat())
        clipPath.addRoundRect(rectF, radius, radius, Path.Direction.CW)
        canvas?.clipPath(clipPath)

        //draw punchingPath
        punchingPath.reset()
        punchingPath.addCircle(width.toFloat() + 3.dpFloat /2, height.toFloat() / 2, punchingRadius, Path.Direction.CW)
        canvas?.clipPath(punchingPath, Region.Op.DIFFERENCE)
        super.onDraw(canvas)
        viewRect.set(0, 0, width, height)
        canvas?.drawBitmap(playCoverMask, null, viewRect, iconPaint)

        if (playCountStr.isNotEmpty() && textWidth > 0f) {
            var x = width.toFloat() - textContainerWidth
            rectF.set(x,  height.toFloat() - 18.dpFloat, width.toFloat(), height.toFloat())
            canvas?.drawRoundRect(rectF, radius, radius, paint)
            x += 4.dpFloat
            canvas?.drawBitmap(playCountIcon, x, height.toFloat() - 9.dpFloat - playCountIcon.height/2, iconPaint)
            x += playCountIcon.width + 2.dpFloat
            val textHeight = textPaint.descent() - textPaint.ascent()
            val textY = height - (18.dpFloat - textHeight)/2 - textPaint.descent()// - textPaint.ascent()
            canvas?.drawText(playCountStr, x , textY, textPaint)
        }
    }
}