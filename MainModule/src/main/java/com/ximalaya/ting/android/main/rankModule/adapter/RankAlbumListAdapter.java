package com.ximalaya.ting.android.main.rankModule.adapter;

import android.app.Activity;
import android.content.Context;
import android.graphics.ColorFilter;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Typeface;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.DrawableRes;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.ui.AlbumTagUtilNew;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.rankModule.RankTraceUtil;
import com.ximalaya.ting.android.main.rankModule.fragment.RankDetailFragment;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

import java.util.List;

/**
 * Created by WolfXu on 2019/9/10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class RankAlbumListAdapter extends BaseRankItemListAdapter<AlbumM> {

    private Activity mActivity;
    private RankDetailFragment.IOnItemClickListener mOnItemClickListener;

    public RankAlbumListAdapter(Context context, List<AlbumM> listData, RankDetailFragment.IOnItemClickListener onItemClickListener) {
        super(context, listData);
        mOnItemClickListener = onItemClickListener;
        if (context instanceof Activity) {
            mActivity = (Activity) context;
        } else {
            mActivity = BaseApplication.getOptActivity();
        }
    }

    @Override
    public void onClick(View view, AlbumM albumM, int position, BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_rank_album_list;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new AlbumViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder h, final AlbumM albumM, final int position) {
        if (albumM != null && h instanceof AlbumViewHolder) {
            final AlbumViewHolder holder = (AlbumViewHolder) h;
            holder.itemView.setTag(R.id.main_rank_list_is_content, true);
            holder.itemView.setTag(R.id.main_rank_list_item_data, albumM);
            if (position >= 3) {
                holder.tvRanking.setText(String.valueOf(position + 1));
                holder.tvRanking.setVisibility(View.VISIBLE);
                holder.ivTopRanking.setVisibility(View.GONE);
            } else {
                @DrawableRes int topRankingResId = R.drawable.main_ic_ranking_top1;
                switch (position) {
                case 1:
                    topRankingResId = R.drawable.main_ic_ranking_top2;
                    break;
                case 2:
                    topRankingResId = R.drawable.main_ic_ranking_top3;
                    break;
                default:
                    break;
                }
                holder.ivTopRanking.setImageResource(topRankingResId);
                holder.ivTopRanking.setVisibility(View.VISIBLE);
                holder.tvRanking.setVisibility(View.GONE);
            }
            @DrawableRes int shiftResId = R.drawable.main_search_host_list_red_up_new;
            ColorFilter colorFilter = null;
            switch (albumM.getPositionChange()) {
            case 0:
                shiftResId = R.drawable.main_search_host_list_white;
                colorFilter = new PorterDuffColorFilter(0xffd9d9d9, PorterDuff.Mode.SRC_IN);
                break;
            case 2:
                shiftResId = R.drawable.main_search_host_list_green_down_new;
                break;
            default:
                break;
            }
            holder.ivRankingShift.setImageResource(shiftResId);
            holder.ivRankingShift.setColorFilter(colorFilter);
            ImageManager.from(context).displayImage(holder.ivCover, albumM.getValidCover(),
                    com.ximalaya.ting.android.host.R.drawable.host_default_album);
            holder.tvTitle.setText(albumM.getAlbumTitle());
            if (!TextUtils.isEmpty(albumM.getIntro()) && !"null".equals(albumM.getIntro())) {
                ViewStatusUtil.setVisible(View.VISIBLE, holder.tvSubTitle);
                holder.tvSubTitle.setText(albumM.getIntro());
            } else {
                ViewStatusUtil.setVisible(View.GONE, holder.tvSubTitle);
                holder.tvSubTitle.setText("");
            }
            AlbumTagUtilNew.getInstance().loadImage(holder.ivAlbumCoverTag, albumM.getAlbumSubscriptValue());
            bindTitle(holder, albumM);

            if (TextUtils.isEmpty(albumM.getActivityTag())) {
                holder.ivActivityTag.setVisibility(View.GONE);
            } else {
                holder.ivActivityTag.setImageDrawable(null);
                holder.ivActivityTag.setVisibility(View.VISIBLE);
                ImageManager.from(mActivity).displayImage(holder.ivActivityTag, albumM.getActivityTag(), -1);
            }

            // 有专辑评分，表明是口碑榜
            if (albumM.getScore() > 0) {
                holder.llScore.setVisibility(View.VISIBLE);
                holder.tvScore.setText(String.format("%s", albumM.getScore()));
                holder.tvSubTitle.setMaxLines(2);
            } else {
                holder.llScore.setVisibility(View.GONE);
                holder.tvSubTitle.setMaxLines(1);
            }

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    long albumId = null == albumM ? 0 : albumM.getId();
                    AlbumEventManage.startMatchAlbumFragment(albumId, AlbumEventManage.FROM_OTHER,
                            ConstantsOpenSdk.PLAY_FROM_RANK, albumM.getRecommentSrc(), albumM.getRecTrack(), -1,
                            mActivity);
                    // 因为页面多元化后，需要走paid/info接口判断要进入哪个页面，所以更改使用的方法
                    /*AlbumEventManage.startMatchAlbumFragment(albumM, AlbumEventManage.FROM_OTHER,
                            ConstantsOpenSdk.PLAY_FROM_RANK, albumM.getRecommentSrc(), albumM.getRecTrack(), -1,
                            mActivity);*/
                    statItemClick(albumM, position);
                    if (mOnItemClickListener != null) {
                        mOnItemClickListener.onItemClick(RankTraceUtil.ITEM_TYPE_ALBUM, albumM.getId(), 0, 0, position);
                    }
                }
            });
        }
    }

    private void statItemClick(AlbumM albumM, int position) {
        UserTracking ut = new UserTracking();
        ut.setSrcPage("rankCluster").setSrcModule("albumList").setItem("album").setItemId(albumM.getId()).setRankListId(getRankListId())
                .setCategoryId(getCategoryId()).setSrcPageId(getRankListId()).setSrcPosition(position).statIting("event", "pageClick");
    }

    private void bindTitle(AlbumViewHolder holder, AlbumM albumM) {
        Spanned titleWithTag = null;
        int textSize = (int) holder.tvTitle.getTextSize();
        if (albumM.getType() == 3) { // 训练营
            titleWithTag = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(context
                    , " " + albumM.getAlbumTitle(), com.ximalaya.ting.android.host.R.drawable.host_tag_training_camp, textSize);
        } else if (albumM.getIsFinished() == 2) {
            titleWithTag = ToolUtil.getTitleWithPicAheadCenterAlignAndFitHeight(context
                    , " " + albumM.getAlbumTitle(), com.ximalaya.ting.android.host.R.drawable.host_tag_complete, textSize);
        }
        if (titleWithTag != null) {
            holder.tvTitle.setText(titleWithTag);
        } else {
            holder.tvTitle.setText(albumM.getAlbumTitle());
        }
    }

    static class AlbumViewHolder extends BaseViewHolder {
        private View itemView;
        private TextView tvRanking;
        private ImageView ivCover;
        private TextView tvTitle;
        private TextView tvSubTitle;
        private ImageView ivAlbumCoverTag;
        private ImageView ivTopRanking;
        private ImageView ivRankingShift;
        private View vDivider;
        private ImageView ivActivityTag;
        private TextView tvScore;
        private LinearLayout llScore;
        private TextView tvHot;

        AlbumViewHolder(View itemView) {
            this.itemView = itemView;
            tvRanking = itemView.findViewById(R.id.main_tv_ranking);
            Typeface typeface = Typeface.createFromAsset(itemView.getContext().getResources().getAssets(), "fonts" +
                    "/LiveNumber-Bold.ttf");
            tvRanking.setTypeface(typeface);
            ivCover = itemView.findViewById(R.id.main_iv_cover);
            tvTitle = itemView.findViewById(R.id.main_tv_title);
            tvSubTitle = itemView.findViewById(R.id.main_tv_sub_title);
            setMarginTop(tvTitle, BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 2));
            setMarginTop(tvSubTitle, BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 7));
            ivAlbumCoverTag = itemView.findViewById(R.id.main_iv_album_cover_tag);
            ivTopRanking = itemView.findViewById(R.id.main_iv_top_ranking);
            ivRankingShift = itemView.findViewById(R.id.main_iv_ranking_shift);
            vDivider = itemView.findViewById(R.id.main_v_divider);
            tvHot = itemView.findViewById(R.id.main_tv_hot);
            tvHot.setVisibility(View.GONE);
            ivActivityTag = itemView.findViewById(R.id.main_iv_activity_tag);
            ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) vDivider.getLayoutParams();
            marginLayoutParams.rightMargin = BaseUtil.dp2px(itemView.getContext(), 15);

            tvScore = itemView.findViewById(R.id.main_tv_score);
            llScore = itemView.findViewById(R.id.main_ll_score);
            tvScore.setTypeface(Typeface.createFromAsset(itemView.getContext().getResources().getAssets(), "fonts/XmlyNumberV1.0-Regular.otf"));
        }

        private void setMarginTop(View view, int top) {
            if (view == null) {
                return;
            }
            if (view.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
                layoutParams.topMargin = top;
                view.setLayoutParams(layoutParams);
            }
        }
    }
}
