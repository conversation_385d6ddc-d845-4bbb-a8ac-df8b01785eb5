package com.ximalaya.ting.android.main.share.dialog

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.share.model.EmotionalValueConfigModel
import com.ximalaya.ting.android.host.share.util.ShareDialogNewUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.util.other.ShareUtilsInMain
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.shareservice.base.IShareDstType
import com.ximalaya.ting.android.xmtrace.XMTraceApi

class EmotionalValueShareDialog(val mEmotionalValueConfigModel: EmotionalValueConfigModel, val mAlbumM: AlbumM?, val currPage: String?) : BaseDialogFragment<EmotionalValueShareDialog>() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.main_dialog_emotiona_value_share, container, false)
    }

    private var closeIv: ImageView ? = null
    private var mBlurIv: ImageView ? = null
    private var topImageView: XmLottieAnimationView? = null
    private var titleTv: TextView ? = null
    private var contentTv: TextView ? = null
    private var mBtnName: TextView ? = null
    private var btnLayout: View ? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        topImageView = view.findViewById(R.id.main_cover_iv)
        titleTv = view.findViewById(R.id.main_tv_title)
        contentTv = view.findViewById(R.id.main_tv_content)
        btnLayout = view.findViewById(R.id.main_btn_layout)
        closeIv = view.findViewById(R.id.main_iv_close)
        mBlurIv = view.findViewById(R.id.main_bottom_blur_iv)
        mBtnName = view.findViewById(R.id.host_tv_btn1)
        ImageManager.from(BaseApplication.getMyApplicationContext())
            .displayImage(mBlurIv, "https://imagev2.xmcdn.com/storages/843d-audiofreehighqps/C7/B1/GAqhqKwMMBSTAAEKSAPQggSS.webp", -1)

        val coverUrl = mEmotionalValueConfigModel.lottie
        if (!coverUrl.isNullOrEmpty()) {
            topImageView?.apply {
                setAnimationFromUrl(coverUrl)
                playAnimation()
            }
        }

        closeIv!!.setOnClickListener {
            traceClick("关闭")
            dismiss()
        }
        if (BaseFragmentActivity.sIsDarkMode) {
            mBlurIv?.alpha = 0.3f
        }
        mBtnName!!.text = mEmotionalValueConfigModel.btnName
        titleTv!!.text = mEmotionalValueConfigModel.title
        contentTv!!.text = mEmotionalValueConfigModel.subTitle
        btnLayout!!.setOnClickListener {
            traceClick(mBtnName!!.text?.toString())
            BaseApplication.getMainActivity()?.let {
                ShareUtilsInMain.shareAlbum(it, mAlbumM, IShareDstType
                    .SHARE_TYPE_WX_FRIEND, ICustomShareContentType.SHARE_TYPE_ALBUM)
            }
            dismiss()
        }

        // 新声音播放页-情绪价值半弹层  控件曝光
        XMTraceApi.Trace()
            .setMetaId(69126)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "album")
            .put("xmRequestId", mAlbumM?.requestId)
            .put("contentId", mAlbumM?.id?.toString()) // 去重使用
            .put("contentType", "album") // 去重使用
            .put("currTrackId", XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId?.toString())
            .put("currAlbumId", mAlbumM?.id?.toString())
            .createTrace()
    }

    private fun traceClick(item: String?) {
        // 新声音播放页-情绪价值半弹层  点击事件
        XMTraceApi.Trace()
            .click(69125) // 用户点击时上报
            .put("currPage", "album")
            .put("xmRequestId", mAlbumM?.requestId)
            .put("Item", item)
            .put("contentId", mAlbumM?.id?.toString()) // 去重使用
            .put("contentType", "album") // 去重使用
            .put("currTrackId", XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound?.dataId?.toString())
            .put("currAlbumId", mAlbumM?.id?.toString())
            .createTrace()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCanceledOnTouchOutside(true)
        isCancelable = true
        dialog.window?.let { window ->
            window.decorView.setPadding(0, 0, 0, 0)
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            val lp = window.attributes
            lp.gravity = Gravity.BOTTOM
            window.attributes = lp
            if (BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext()) > 600.dp) {
                window.setLayout(375.dp, ViewGroup.LayoutParams.WRAP_CONTENT)
            } else {
                window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            }
        }
        ShareDialogNewUtil.fitStatusBar(dialog.window)
        return dialog
    }

    companion object {
        fun newInstance(emotionalValueConfigModel: EmotionalValueConfigModel, album: AlbumM, currPage: String?): EmotionalValueShareDialog {
            return EmotionalValueShareDialog(emotionalValueConfigModel, album, currPage)
        }
    }
}