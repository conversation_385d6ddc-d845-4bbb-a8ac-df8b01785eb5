package com.ximalaya.ting.android.main.share.video.model;

import android.webkit.URLUtil;

import androidx.annotation.Keep;

import com.ximalaya.ting.android.host.model.community.FindCommunityModel;
import com.ximalaya.ting.android.host.model.kacha.ShortContentSubtitleModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> feiwen date   : 2019-09-09 desc   :
 */
@Keep
public class ShortContentProductModel implements Serializable {

    public static final int SYNTHESIS_STAGE_TYPE_CREATE_CLIP_SOUND = 0;
    public static final int SYNTHESIS_STAGE_TYPE_CREATE_CLIP_VIDEO = 1;
    public static final int SYNTHESIS_STAGE_TYPE_CREATE_NO_WATERMARK_AUDIO_VIDEO = 2;
//    public static final int SYNTHESIS_STAGE_TYPE_CREATE_VIDEO_WITH_LRC = 3;
//    public static final int SYNTHESIS_STAGE_TYPE_CREATE_FINAL_CHAPTER = 3;
    public static final int SYNTHESIS_STAGE_TYPE_SAVE_FINAL_VIDEO = 3;

    public static final int SOURCE_TYPE_VALID = 0;
    public static final int SOURCE_TYPE_PIC = 1;
    public static final int SOURCE_TYPE_REMOTE_VIDEO_MODEL = 2;
    public static final int SOURCE_TYPE_REMOTE_PIC_MODEL = 3;
    public static final int SOURCE_TYPE_CUSTOM_VIDEO = 4;
    public static final int SOURCE_TYPE_DEFAULT_VIDEO = 5; // 默认动画模板
    public static final int SOURCE_TYPE_CAMERA_VIDEO = 6; // 拍摄模式
    private static final long serialVersionUID = 2504525583353005972L;
    /**
     * 最终视频生成路径
     * */
    public String finalVideoStoragePath;

    /**
     * 合成无水印无字幕视频路径
     */
    public String finalNoWatermarkNoLrcVideoStoragePath;
    /**
     * 合成无水印视频路径
     */
    public String finalNoWatermarkVideoStoragePath;
    /**
     * 合成加水印视频路径
     */
    public String finalWatermarkVideoStoragePath;
    /**
     * (选择合成视频) 视频文件路径 模板视频或是本地视频
     */
    public String videoStoragePath;
    /**
     * 裁剪后的无水印视频文件路径
     */
    public String clipVideoNoWatermarkStoragePath;
    /**
     * 截取声音文件路径
     */
    public String audioStoragePath;
    /**
     * 总的声音文件路径
     */
    public String originAudioPath;
    /**
     * 用于声音识别的文件路径
     */
    public String audioStoragePath4Asr;
    /**
     * 片尾水印转原始视频专辑封面文件路径
     */
    public String tailOriginVideoAlbumCoverPath;
    /**
     * 片尾水印文件路径
     */
    public String tailPicStoragePath;
    /**
     * 片尾二维码文件路径
     */
    public String tailQrPicStoragePath;
    /**
     * (选择合成图片) 原始图片文件路径
     */
    public String originCropPicStoragePath;
    /**
     * (选择合成图片) 转码图片文件路径
     */
    public String convertCropPicStoragePath;
    /**
     * 封面图片文件路径
     */
    public String coverPicStoragePath;
    /**
     * 声音截取开始
     */
    public long soundStartSecond;
    /**
     * 声音截取开始
     */
    public long soundStartMs;
    /**
     * 声音截取结束
     */
    public long soundEndSecond;
    /**
     * 声音截取结束
     */
    public long soundEndMs;
    /**
     * 声音时长
     */
    public long soundDurationMs;
    /**
     * 视频截取开始
     */
    public long videoStartMs;
    /**
     * 视频截取结束
     */
    public long videoEndMs;
    /**
     * 视频时长
     */
    public long videoDurationMs;
    /**
     * 视频时长
     */
    public int soundDurationS;
    /**
     * 选择合成类型：图片/本地视频/视频模板
     */
    public int sourceType;

    public int outVideoWidth;

    public int outVideoHeight;

    public int rotate;

    public String title;

    public long sourceTrackId;

    public long categoryId;

    public long videoChooseCoverTimeMs;

    public long albumId;

    public String albumName;

    public String trackName;

    /**
     * 模板id
     */
    public long templeId;

    /**
     * 模板封面url
     */
    public String templateCoverUrl;

    public int synthesisStageType;

    public String albumCoverUrl;

    /**
     * 短内容 id
     */
    public long shortContentId;
    /**
     * 短内容对应的 trackId
     */
    public long shortContentTrackId;

    /**
     * 字幕文件地址
     */
    public String srtFilePath;
    public List<String> thumbnailBmpPaths;
    public List<ShortContentSubtitleModel> subtitleList;
    public List<ShortContentSubtitleModel> subtitleList4Camera;
    public List<ShortContentSubtitleModel> copySubtitleList;
    public FindCommunityModel.Lines lines;

    /**
     * 上传后的　url
     */
    public String uploadCoverUrl;
    public String uploadVideoUrl;
    public long uploadVideoId;

    public boolean isVideoHasAudio;
    public float videoVolume;
    public float audioVolume = 1.0f;
    public boolean isEffectSubtitle;
    public boolean willRefreshWorkPlatform;

    public String getFinalValidCoverUrl() {
        if (URLUtil.isValidUrl(uploadCoverUrl)) {
            return uploadCoverUrl;
        }
        if (URLUtil.isValidUrl(templateCoverUrl)) {
            return templateCoverUrl;
        }
        if (URLUtil.isValidUrl(albumCoverUrl)) {
            return templateCoverUrl;
        }
        return null;
    }

    public boolean hasSameSubtitle() {
        if (ToolUtil.isEmptyCollects(subtitleList)) {
            return ToolUtil.isEmptyCollects(copySubtitleList);
        }
        if (ToolUtil.isEmptyCollects(copySubtitleList)) {
            return false;
        }
        if (subtitleList.size() != copySubtitleList.size()) {
            return false;
        }
        for (int i = 0, size = subtitleList.size(); i < size; i++) {
            if (subtitleList.get(i).equals(copySubtitleList.get(i))) {
                continue;
            }
            return false;
        }
        return true;
    }
}
