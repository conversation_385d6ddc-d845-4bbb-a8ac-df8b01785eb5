package com.ximalaya.ting.android.main.tagModule.adapter

import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.text.SpannableString
import android.text.style.LeadingMarginSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.constraintlayout.widget.Group
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.TempDataManager
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage
import com.ximalaya.ting.android.host.model.play.CommentModel
import com.ximalaya.ting.android.host.model.play.PlayPageTab
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.extension.appendRequestIdAttrsForSpecial
import com.ximalaya.ting.android.host.util.extension.appendRequestIdAttrsForTrack
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.view.EmotionUtil
import com.ximalaya.ting.android.host.util.view.LocalImageUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.newAnchorSpaceFragment
import com.ximalaya.ting.android.main.fragment.find.child.trace.ConstantTracePageIdsForPodCast
import com.ximalaya.ting.android.main.manager.comment.CommentOperationUtil
import com.ximalaya.ting.android.main.mine.extension.visible
import com.ximalaya.ting.android.main.model.topic.Comment
import com.ximalaya.ting.android.main.model.topic.TopicDetailTrack
import com.ximalaya.ting.android.main.model.topic.TopicListenTrackInfo
import com.ximalaya.ting.android.main.model.topic.TopicModuleTitle
import com.ximalaya.ting.android.main.model.topic.TopicTimeLineInfo
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.util.TimeUtil
import com.ximalaya.ting.android.main.util.setOnOneClickListener
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * Created by WolfXu on 2023/3/15.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class TopicDetailAdapter(
    private val dataList: MutableList<Any>,
    private val mTrackEventListener: IOnTrackEventListener,
    private val mFragment: BaseFragment2
) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    var mTopicId: Long = 0

    private val VT_TITLE = 1
    private val VT_TRACK = 2
    private val VT_TIME_LINE = 3
    private val VT_LISTEN_TRACK = 4

    var moduleTitleClick: ((TopicModuleTitle, String?, Boolean) -> Unit)? = null
    var listenItemPlayClick: ((TopicListenTrackInfo) -> Unit)? = null
    var listenItemClick: ((TopicListenTrackInfo) -> Unit)? = null
    var timeLinePlayClick: ((View, TopicTimeLineInfo) -> Unit)? = null

    @ColorInt
    private var mPageThemeColor: Int? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VT_TITLE -> {
                createTitleViewHolder(parent, viewType)
            }

            VT_TRACK -> {
                onCreateTrackViewHolder(parent, viewType)
            }

            VT_TIME_LINE -> {
                createTimeLineViewHolder(parent, viewType)
            }

            VT_LISTEN_TRACK -> {
                createListenTrackViewHolder(parent, viewType)
            }

            else -> {
                onCreateTrackViewHolder(parent, viewType)
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (dataList[position]) {
            is TopicDetailTrack -> VT_TRACK
            is TopicListenTrackInfo -> VT_LISTEN_TRACK
            is TopicModuleTitle -> VT_TITLE
            else -> VT_TIME_LINE
        }
    }

    private fun onCreateTrackViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): TopicDetailTrackViewHolder {
        return TopicDetailTrackViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.main_layout_topic_detail_track_item, parent, false)
        )
    }

    private fun createTitleViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): TopicDetailTitleViewHolder {
        return TopicDetailTitleViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.main_layout_topic_module_title, parent, false)
        )
    }

    private fun createListenTrackViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): TopicListenTrackViewHolder {
        return TopicListenTrackViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.main_layout_topic_listen_track_item, parent, false)
        )
    }

    private fun createTimeLineViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): TopicDetailTimeLineViewHolder {
        return TopicDetailTimeLineViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.main_layout_topic_time_line_item, parent, false)
        )
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is TopicDetailTrackViewHolder -> onBindTrackViewHolder(holder, position)
            is TopicListenTrackViewHolder -> bindListenTrackViewHolder(holder, position)
            is TopicDetailTitleViewHolder -> bindTitleViewHolder(holder, position)
            is TopicDetailTimeLineViewHolder -> bindTimeLineViewHolder(holder, position)
            else -> {}
        }
    }

    private fun bindListenTrackViewHolder(holder: TopicListenTrackViewHolder, position: Int) {
        val track = dataList.getOrNull(position) as? TopicListenTrackInfo ?: return
        val context = ToolUtil.getCtx()
        holder.run {
            mainTvTrackName.text = track.title ?: ""
            mainIvPlayBg.isSelected =
                PlayTools.isCurrentTrack(context, track.getConvertTrack())
                        && (XmPlayerManager.getInstance(context).isPlaying
                        || XmPlayerManager.getInstance(context).isBuffering
                        || XmPlayerManager.getInstance(context).isAdPlaying)
            mainTvPlayCount.text = StringUtil.getFriendlyNumStr(track.playCount)
            mainTvDuration.text = StringUtil.toTime(track.duration.toInt())

            track.cachedThemeColor?.let {
                setPlayBtnBgColor(it, mainIvPlayBg)
            }

            ImageManager.from(ToolUtil.getCtx()).displayImage(
                mainIvCover,
                track.coverPath,
                com.ximalaya.ting.android.host.R.drawable.host_default_album
            ) { _, bitmap ->
                if (holder.cacheTrack == track) {
                    LocalImageUtil.getDomainColorForRecommend(bitmap, Color.BLACK) { color: Int ->
                        setPlayBtnBgColor(color, mainIvPlayBg)
                        track.cachedThemeColor = color
                    }
                }
            }

            mainIvPlayBg.setOnOneClickListener {
                listenItemPlayClick?.invoke(track)
            }

            itemView.setOnOneClickListener {
                listenItemClick?.invoke(track)
            }

            cacheTrack = track
        }
    }

    private fun bindTitleViewHolder(holder: TopicDetailTitleViewHolder, position: Int) {
        val title = dataList.getOrNull(position) as? TopicModuleTitle ?: return
        holder.run {
            title.paddingTopDp?.let {
                itemView.setPadding(itemView.paddingLeft, it.dp, itemView.paddingRight, itemView.paddingBottom)
            }
            mainTvTitle.text = title.title ?: ""
            mainTvNext.text =
                if (title.link.isNullOrEmpty() || title.linkTxt.isNullOrEmpty()) "" else title.linkTxt

            mainTvNext.visibility = if (title.link.isNullOrEmpty()) View.INVISIBLE else View.VISIBLE

            if (title.link.isNullOrEmpty().not()) {
                mainTvNext.setOnOneClickListener {
                    moduleTitleClick?.invoke(title, title.link, true)
                }

                holder.itemView.setOnOneClickListener {
                    moduleTitleClick?.invoke(title, title.link, false)
                }
            }
        }
    }

    private fun bindTimeLineViewHolder(holder: TopicDetailTimeLineViewHolder, position: Int) {
        val info = dataList.getOrNull(position) as? TopicTimeLineInfo ?: return
        holder.run {
            mainTvTrackName.text = info.title ?: ""

            if (info.businessId != 0L) {
                mainIvPlayBg.setOnOneClickListener {
                    timeLinePlayClick?.invoke(mainIvPlayBg, info)
                }
            }
            mainIvPlayBg.visibility = if (info.businessId == 0L) View.INVISIBLE else View.VISIBLE
            mainIvPlay.visibility = if (info.businessId == 0L) View.INVISIBLE else View.VISIBLE
            mainIvPlay.setImageResource(
                if (PlayTools.isPlayCurrTrackById(ToolUtil.getCtx(), info.businessId)
                    && (XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying
                            || XmPlayerManager.getInstance(ToolUtil.getCtx()).isBuffering
                            || XmPlayerManager.getInstance(ToolUtil.getCtx()).isAdPlaying)
                )
                    R.drawable.main_podcast_pause
                else
                    R.drawable.main_podcast_play
            )

            mPageThemeColor?.let { color ->
                mainIvPlayBg.setImageDrawable(ColorDrawable(color))
                mainIvPlay.imageTintList = ColorStateList.valueOf(color)
            }

            mainTvDate.let {
                it.text = if (info.time <= 0) {
                    ""
                } else TimeUtil.getFriendlyTimeStr2022(info.time)
            }

            if (info.moreItems.isNullOrEmpty()) {
                mainGMore.visibility = View.GONE
            } else {
                mainGMore.visibility = View.VISIBLE
                mainTvMore.setOnClickListener {
                    mainGMore.visibility = View.GONE
                    expandMoreTimeLineItems(info)
                }
            }
            cachedItem = info
        }
    }

    private fun TopicDetailTimeLineViewHolder.expandMoreTimeLineItems(
        info: TopicTimeLineInfo
    ): Boolean {
        val moreItems = info.moreItems ?: return true
        var index = 0
        cachedItem?.let {
            index = dataList.indexOf(it)
            it
        }?.takeIf {
            index > -1
        }?.let {
            if (index == dataList.size - 1) {
                moreItems.forEach { item ->
                    dataList.add(item)
                    traceListenItemShown(item.bizIndex, item.businessId.toString(), info.title, "最新进展")
                }
            } else {
                var offset = 1
                moreItems.forEach { item ->
                    dataList.add(index + offset, item)
                    offset++
                    traceListenItemShown(item.bizIndex, item.businessId.toString(), info.title, "最新进展")
                }
            }
            notifyItemRangeInserted(index + 1, moreItems.size)
            cachedItem?.moreItems = null
        }
        return false
    }

    fun onThemeColorChanged(color: Int) {
        mPageThemeColor = color
    }

    private fun onBindTrackViewHolder(holder: TopicDetailTrackViewHolder, position: Int) {
        val track = dataList.getOrNull(position) as? TopicDetailTrack ?: return
        holder.track = track
        val context = holder.itemView.context
        ImageManager.from(context).displayImage(
            holder.ivAuthorAvatar,
            track.logoPic,
            com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default
        )
        holder.tvAuthorName?.text = track.nickname
        ("${StringUtil.getFriendlyNumStr(track.fansCount)}粉丝").also { holder.tvFans?.text = it }
        holder.tvPTitle?.text = track.pTitle
        holder.vAuthorInfoDot.visible(if (track.pTitle.isNullOrEmpty()) View.INVISIBLE else View.VISIBLE)
        val hasThemeColor = track.cachedThemeColor != null
        if (hasThemeColor) {
            setPlayBtnBgColor(track.cachedThemeColor, holder)
        }
        ImageManager.from(context).displayImage(
            holder.ivTrackCover, track.coverPath,
            com.ximalaya.ting.android.host.R.drawable.host_default_album
        ) { _, bitmap ->
            if (holder.track == track) {
                LocalImageUtil.getDomainColorForRecommend(bitmap, Color.BLACK) { color: Int ->
                    setPlayBtnBgColor(color, holder)
                    track.cachedThemeColor = color
                }
            }
        }
        holder.tvTrackTitle?.text = track.title
        holder.ivTrackPlayBtn?.isSelected =
            PlayTools.isCurrentTrack(context, track.getConvertTrack())
                    && (XmPlayerManager.getInstance(context).isPlaying
                    || XmPlayerManager.getInstance(context).isBuffering
                    || XmPlayerManager.getInstance(context).isAdPlaying)
        holder.tvPlayCount?.text = StringUtil.getFriendlyNumStr(track.playCount)
        holder.tvDuration?.text = StringUtil.toTime(track.duration)
        holder.tvCommentCount?.text = if (track.commentsCount > 999) {
            "999+"
        } else if (track.commentsCount > 0) {
            track.commentsCount.toString()
        } else {
            "评论"
        }
        holder.tvLikeCount?.text = getLikeCountText(track)
        holder.tvFollow?.let {
            val gradientDrawable = GradientDrawable()
            gradientDrawable.shape = GradientDrawable.RECTANGLE
            gradientDrawable.cornerRadius = RpAdaptUtil.getRpAdaptSize(30.dp).toFloat()
            gradientDrawable.setStroke(0.6F.dp, mPageThemeColor ?: 0xffcb6a74.toInt())
            holder.vFollowBg?.background = gradientDrawable

            // 一开始请求到的数据就已经关注了，就不显示
            it.visibility = if (track.isFollow) View.INVISIBLE else View.VISIBLE
            holder.vFollowBg?.visibility = if (track.isFollow) View.INVISIBLE else View.VISIBLE
            if (track.isFollowNow()) {
                it.setTextColor(
                    context.resources?.getColor(R.color.main_color_bcbcbc_2a2a2a)
                        ?: 0xffbcbcbc.toInt()
                )
                it.text = "已关注"
            } else {
                it.setTextColor(mPageThemeColor ?: 0xffcb6a74.toInt())
                it.text = "关注"
            }
        }
        holder.lottieLike.visible(View.INVISIBLE)
        holder.ivLikeIcon.visible(View.VISIBLE)
        holder.ivLikeIcon?.isSelected = track.isLike
        bindGreatComment(holder, track)
        holder.vBottomDivider.visible(if (position == dataList.size - 1) View.INVISIBLE else View.VISIBLE)
        setOnClickListener(holder, track, position)
    }

    private fun setPlayBtnBgColor(color: Int?, holder: TopicDetailTrackViewHolder) {
        color ?: return
        val gradientDrawable = GradientDrawable()
        gradientDrawable.shape = GradientDrawable.RECTANGLE
        gradientDrawable.cornerRadius = RpAdaptUtil.getRpAdaptSize(20.dp).toFloat()
        gradientDrawable.setColor(color)
        holder.ivTrackPlayBtn?.background = gradientDrawable
    }

    private fun setPlayBtnBgColor(color: Int?, view: View?) {
        color ?: return
        val gradientDrawable = GradientDrawable()
        gradientDrawable.shape = GradientDrawable.RECTANGLE
        gradientDrawable.cornerRadius = RpAdaptUtil.getRpAdaptSize(20.dp).toFloat()
        gradientDrawable.setColor(color)
        view?.background = gradientDrawable
    }

    private fun getLikeCountText(track: TopicDetailTrack): String {
        return getLikeCountText(track.likesCount, 1)
    }

    private fun getLikeCountText(likeCount: Long, minCountToShow: Int): String {
        return if (likeCount > 999) {
            "999+"
        } else if (likeCount >= minCountToShow) {
            likeCount.toString()
        } else {
            "赞"
        }
    }

    private fun bindGreatComment(holder: TopicDetailTrackViewHolder, track: TopicDetailTrack) {
        val greatComment = track.greatComment
        if (greatComment?.isValid() != true) {
            holder.groupGreatComment.visible(View.GONE)
            holder.lottieCommentLike.visible(View.GONE)
            holder.ivCommentLikeIcon.visible(View.GONE)
            holder.tvCommentLikeCount.visible(View.GONE)
            return
        }
        holder.groupGreatComment.visible(View.VISIBLE)
        holder.tvCommentLikeCount?.text = getLikeCountText(greatComment.likesCount.toLong(), 1)
        holder.lottieCommentLike.visible(View.INVISIBLE)
        holder.ivCommentLikeIcon.visible(View.VISIBLE)
        holder.ivCommentLikeIcon?.isSelected = greatComment.isLike
        holder.tvCommentAuthorName?.let {
            it.text = "${greatComment.username}："
            it.post {
                val spannableString =
                    EmotionUtil.getInstance().convertEmotionText2Span(greatComment.comment)
                spannableString.setSpan(
                    LeadingMarginSpan.Standard(it.width, 0), 0, spannableString.length,
                    SpannableString.SPAN_INCLUSIVE_INCLUSIVE
                )
                holder.tvCommentContent?.text = spannableString
            }
        }
        val commentLikeListener = View.OnClickListener {
            checkOneClick(it) {
                doCommentLikeOrUnLike(track, greatComment, holder)
            }
        }
        holder.tvCommentLikeCount?.setOnClickListener(commentLikeListener)
        holder.ivCommentLikeIcon?.setOnClickListener(commentLikeListener)
        holder.lottieCommentLike?.setOnClickListener(commentLikeListener)
        holder.vCommentBg?.setOnClickListener {
            checkOneClick(it) {
                PlayPageDataManager.getInstance().isCommentNeededToAnchor = true
                PlayPageDataManager.getInstance().anchorComment =
                    greatComment.convertToCommentModel(track.trackId)
                TempDataManager.getInstance().saveInt(
                    BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SECTION, PlayPageTab.TYPE_COMMENT
                )
                TempDataManager.getInstance().saveLong(
                    BundleKeyConstants.KEY_XPLAY_FRAGMENT_OPEN_COMMENT_TRACK_ID, track.trackId
                )
                mTrackEventListener.playTrackAndStartPlayPage(track)
                traceGreatCommentClick(track)
            }
        }
    }

    private fun setOnClickListener(
        holder: TopicDetailTrackViewHolder, track: TopicDetailTrack,
        position: Int
    ) {
        holder.ivTrackPlayBtn?.setOnClickListener {
            checkOneClick(it) {
                mTrackEventListener.onTrackPlayBtnClick(track)
                traceItemClick(position, track, false)
            }
        }
        val trackItemClickListener = View.OnClickListener {
            checkOneClick(it) {
                mTrackEventListener.playTrackAndStartPlayPage(track)
                traceItemClick(position, track, true)
            }
        }
        holder.ivTrackCover?.setOnClickListener(trackItemClickListener)
        holder.tvTrackTitle?.setOnClickListener(trackItemClickListener)
        val anchorClickListenr = View.OnClickListener {
            checkOneClick(it) {
                mFragment.startFragment(newAnchorSpaceFragment(track.uid, -1))
                traceAnchorClick(position, track, true)
            }
        }
        holder.ivAuthorAvatar?.setOnClickListener(anchorClickListenr)
        holder.tvAuthorName?.setOnClickListener(anchorClickListenr)
        holder.tvFollow?.setOnClickListener {
            checkOneClick(it) {
                follow(track, holder, position)
                traceAnchorClick(position, track, false)
            }
        }
        val likeClickListener = View.OnClickListener {
            checkOneClick(it) {
                doLikeOrUnlike(track, holder)
                traceLikeClick(track, !track.isLike)
            }
        }
        holder.tvLikeCount?.setOnClickListener(likeClickListener)
        holder.ivLikeIcon?.setOnClickListener(likeClickListener)
        holder.lottieLike?.setOnClickListener(likeClickListener)
        // 评论按钮点击
        val commentBtnClickListener = View.OnClickListener {
            checkOneClick(it) {
                TempDataManager.getInstance().saveInt(
                    BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SECTION, PlayPageTab.TYPE_COMMENT
                )
                TempDataManager.getInstance().saveLong(
                    BundleKeyConstants.KEY_XPLAY_FRAGMENT_OPEN_COMMENT_TRACK_ID, track.trackId
                )
                mTrackEventListener.playTrackAndStartPlayPage(track)
                traceCommentBtnClick(track)
            }
        }
        holder.tvCommentCount?.setOnClickListener(commentBtnClickListener)
        holder.ivCommentIcon?.setOnClickListener(commentBtnClickListener)
    }

    private fun doLikeOrUnlike(track: TopicDetailTrack, holder: TopicDetailTrackViewHolder) {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mFragment.context)
            return
        }
        val targetIsLike = !track.isLike
        LikeTrackManage.toLikeOrUnLike(
            track.trackId,
            targetIsLike,
            object : IDataCallBack<Boolean> {
                override fun onSuccess(result: Boolean?) {
                    track.isLike = targetIsLike
                    track.likesCount += if (targetIsLike) 1 else -1
                    if (result == true && mFragment.canUpdateUi() && track == holder.track) {
                        holder.tvLikeCount?.text = getLikeCountText(track)
                        holder.ivLikeIcon?.isSelected = targetIsLike
                        if (targetIsLike) {
                            holder.lottieLike?.let {
                                it.visibility = View.VISIBLE
                                it.playAnimation()
                            }
                            holder.ivLikeIcon.visible(View.INVISIBLE)
                        } else {
                            holder.lottieLike?.let {
                                it.cancelAnimation()
                                it.visibility = View.INVISIBLE
                            }
                            holder.ivLikeIcon.visible(View.VISIBLE)
                        }
                    }
                }

                override fun onError(code: Int, message: String?) {
                }
            })
    }

    private fun doCommentLikeOrUnLike(
        track: TopicDetailTrack,
        comment: Comment,
        holder: TopicDetailTrackViewHolder
    ) {
        val targetIsLike = !comment.isLike
        CommentOperationUtil.doLike(
            mFragment.context,
            comment.convertToCommentModel(track.trackId),
            object : IDataCallBack<CommentModel?> {
                override fun onSuccess(model: CommentModel?) {
                    comment.isLike = targetIsLike
                    comment.likesCount += if (targetIsLike) 1 else -1
                    if (mFragment.canUpdateUi() && track == holder.track) {
                        holder.tvCommentLikeCount?.text =
                            getLikeCountText(comment.likesCount.toLong(), 1)
                        holder.ivCommentLikeIcon?.isSelected = targetIsLike
                        if (targetIsLike) {
                            holder.lottieCommentLike?.let {
                                it.visibility = View.VISIBLE
                                it.playAnimation()
                            }
                            holder.ivCommentLikeIcon.visible(View.INVISIBLE)
                        } else {
                            holder.lottieCommentLike?.let {
                                it.cancelAnimation()
                                it.visibility = View.INVISIBLE
                            }
                            holder.ivCommentLikeIcon.visible(View.VISIBLE)
                        }
                    }
                }

                override fun onError(code: Int, message: String) {}
            })
    }

    private fun follow(track: TopicDetailTrack, holder: TopicDetailTrackViewHolder, position: Int) {
        val isCurrentFollowed = track.isFollowNow()
        AnchorFollowManage.followV3(
            mFragment.activity,
            track.uid,
            isCurrentFollowed,
            AnchorFollowManage.FOLLOW_BIZ_TYPE_TOPIC_DETAIL,
            object : IDataCallBack<Boolean?> {
                override fun onSuccess(followed: Boolean?) {
                    if (!mFragment.canUpdateUi()) {
                        return
                    }

                    if (followed != null) {
                        track.setIsFollowNow(followed)
                        if (followed) {
                            CustomToast.showSuccessToast("关注成功")
                        }
                        notifyItemChanged(position)
                    }
                }

                override fun onError(code: Int, message: String) {}
            }, true
        )
    }

    private fun checkOneClick(view: View, action: () -> Unit) {
        if (OneClickHelper.getInstance().onClick(view)) {
            action()
        }
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    fun traceItemShown(position: Int) {
        val item = dataList.getOrNull(position)
        when (item) {
            is TopicListenTrackInfo -> {
                traceListenItemShown(item.bizIndex, item.trackId.toString(), item.title, "听单栏目")
            }

            is TopicModuleTitle -> {
                if (item.link.isNullOrEmpty().not()) {
                    traceListenTitleShown(item.tag?.toString(), item.title)
                }
            }

            is TopicTimeLineInfo -> {
                traceListenItemShown(item.bizIndex, item.businessId.toString(), item.title, "最新进展")
            }

            else -> {
                val track = dataList.getOrNull(position) as? TopicDetailTrack ?: return
                // 创作者话题详情页-声音条  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(47117)
                    .setServiceId("slipPage")
                    .put("currPage", "creatorTopicDetail")
                    .put("currTopicId", mTopicId.toString())
                    .put("pageType", "消费端")
                    .put(
                        "positionNew",
                        (position + 1).toString()
                    ) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                    .put("anchorId", track.uid.toString())
                    .put("anchorName", track.nickname)
                    .put("trackId", track.trackId.toString())
                    .put("albumId", track.albumId.toString())
                    .put("trackName", track.title)
                    .put("moduleName", "热议节目")
                    .appendRequestIdAttrsForTrack(track.trackId.toString(), ConstantTracePageIdsForPodCast.TopicDetailPageC)
                    .createTrace()
                traceGreatCommentShown(track)
            }
        }
    }

    private fun traceListenItemShown(position: Int?,
                                     trackId: String?,
                                     trackName: String?,
                                     moduleName: String?) {
        // 创作者话题详情页-声音条  控件曝光
        XMTraceApi.Trace()
            .setMetaId(47117)
            .setServiceId("slipPage")
            .put("currPage", "creatorTopicDetail")
            .put("currTopicId", mTopicId.toString())
            .put("pageType", "消费端")
            .put("tabName", null)
            .put("positionNew", position.toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put("anchorId", null)
            .put("anchorName", null)
            .put("trackId", trackId)
            .put("albumId", null)
            .put("trackName", trackName)
            .put("moduleName", moduleName)
            .appendRequestIdAttrsForTrack(trackId, ConstantTracePageIdsForPodCast.TopicDetailPageC)
            .createTrace()
    }

    private fun traceListenTitleShown(specialId: String?, listenName: String?) {
        // 创作者话题详情页-听单标题  控件曝光
        XMTraceApi.Trace()
            .setMetaId(57140)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("listenName", listenName)
            .put("specialId", specialId)
            .put("currPage", "creatorTopicDetail")
            .appendRequestIdAttrsForSpecial(specialId, ConstantTracePageIdsForPodCast.TopicDetailPageC)
            .createTrace()
    }

    private fun traceGreatCommentShown(track: TopicDetailTrack) {
        val commentId = track.greatComment?.commentId ?: return
        // 创作者话题详情页- 声音条-精彩评论  控件曝光
        XMTraceApi.Trace()
            .setMetaId(52797)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "creatorTopicDetail")
            .put("currTopicId", mTopicId.toString())
            .put("pageType", "消费端")
            .put("trackId", track.trackId.toString())
            .put("commentId", commentId.toString())
            .appendRequestIdAttrsForTrack(track.trackId.toString(), ConstantTracePageIdsForPodCast.TopicDetailPageC)
            .createTrace()
    }

    private fun traceItemClick(position: Int, track: TopicDetailTrack, openPlayPage: Boolean) {
        // 创作者话题详情页-声音条  点击事件
        XMTraceApi.Trace()
            .click(47116) // 用户点击时上报
            .put("currPage", "creatorTopicDetail")
            .put("currPage", "creatorTopicDetail")
            .put("currTopicId", mTopicId.toString())
            .put("pageType", "消费端")
            .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put("anchorId", track.uid.toString())
            .put("anchorName", track.nickname)
            .put("trackId", track.trackId.toString())
            .put("albumId", track.albumId.toString())
            .put("trackName", track.title)
            .appendRequestIdAttrsForTrack(track.trackId.toString(), ConstantTracePageIdsForPodCast.TopicDetailPageC)
            .put(
                "action",
                if (openPlayPage) "openUrl" else "play"
            ) // play 表示当前页触发播放，openUrl 表示发生页面跳转
            .put("moduleName", "热议节目")
            .createTrace()
    }

    private fun traceAnchorClick(position: Int, track: TopicDetailTrack, openPage: Boolean) {
        // 创作者话题详情页-声音条-主播信息条  点击事件
        XMTraceApi.Trace()
            .click(47118) // 用户点击时上报
            .put("currPage", "creatorTopicDetail")
            .put("currTopicId", mTopicId.toString())
            .put("pageType", "消费端")
            .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put("anchorId", track.uid.toString())
            .put("anchorName", track.nickname)
            .put("trackId", track.trackId.toString())
            .put("albumId", track.albumId.toString())
            .put("trackName", track.title)
            .appendRequestIdAttrsForTrack(track.trackId.toString(), ConstantTracePageIdsForPodCast.TopicDetailPageC)
            .put("action", if (openPage) "openUrl" else "play") // follow 表示关注，openUrl 表示发生页面跳转
            .createTrace()
    }

    private fun traceLikeClick(track: TopicDetailTrack, doLike: Boolean) {
        // 创作者话题详情页-声音条-点赞  点击事件
        XMTraceApi.Trace()
            .click(52794) // 用户点击时上报
            .put("currPage", "creatorTopicDetail")
            .put("currTopicId", mTopicId.toString())
            .put("pageType", "消费端")
            .put("Item", if (doLike) "点赞" else "取消点赞")
            .put("trackId", track.trackId.toString())
            .appendRequestIdAttrsForTrack(track.trackId.toString(), ConstantTracePageIdsForPodCast.TopicDetailPageC)
            .createTrace()
    }

    private fun traceCommentBtnClick(track: TopicDetailTrack) {
        // 创作者话题详情页-声音条-评论  点击事件
        XMTraceApi.Trace()
            .click(52795) // 用户点击时上报
            .put("currPage", "creatorTopicDetail")
            .put("currTopicId", mTopicId.toString())
            .put("pageType", "消费端")
            .put("trackId", track.trackId.toString())
            .appendRequestIdAttrsForTrack(track.trackId.toString(), ConstantTracePageIdsForPodCast.TopicDetailPageC)
            .createTrace()
    }

    private fun traceGreatCommentClick(track: TopicDetailTrack) {
        val commentId = track.greatComment?.commentId ?: return
        // 创作者话题详情页- 声音条-精彩评论  点击事件
        XMTraceApi.Trace()
            .click(52796) // 用户点击时上报
            .put("currPage", "creatorTopicDetail")
            .put("currTopicId", mTopicId.toString())
            .put("pageType", "消费端")
            .put("trackId", track.trackId.toString())
            .put("commentId", commentId.toString())
            .appendRequestIdAttrsForTrack(track.trackId.toString(), ConstantTracePageIdsForPodCast.TopicDetailPageC)
            .createTrace()
    }

    class TopicDetailTrackViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivAuthorAvatar: ImageView? = itemView.findViewById(R.id.main_iv_author_avatar)
        val tvAuthorName: TextView? = itemView.findViewById(R.id.main_tv_author_name)
        val tvFans: TextView? = itemView.findViewById(R.id.main_tv_fans)
        val tvPTitle: TextView? = itemView.findViewById(R.id.main_tv_ptitle)
        val vAuthorInfoDot: View = itemView.findViewById(R.id.main_v_author_info_dot)
        val tvFollow: TextView? = itemView.findViewById(R.id.main_tv_follow)
        val vFollowBg: View? = itemView.findViewById(R.id.main_v_follow_bg)
        val ivTrackCover: ImageView? = itemView.findViewById(R.id.main_iv_track_cover)
        val ivTrackPlayBtn: ImageView? = itemView.findViewById(R.id.main_iv_track_play_btn)
        val tvTrackTitle: TextView? = itemView.findViewById(R.id.main_tv_track_title)
        val tvPlayCount: TextView? = itemView.findViewById(R.id.main_tv_play_count)
        val tvDuration: TextView? = itemView.findViewById(R.id.main_tv_duration)
        val tvCommentCount: TextView? = itemView.findViewById(R.id.main_tv_comment_count)
        val ivCommentIcon: ImageView? = itemView.findViewById(R.id.main_iv_comment_icon)
        val tvLikeCount: TextView? = itemView.findViewById(R.id.main_tv_like_count)
        val ivLikeIcon: ImageView? = itemView.findViewById(R.id.main_iv_like_icon)
        val lottieLike: LottieAnimationView? = itemView.findViewById(R.id.main_iv_lottie_like)
        val groupGreatComment: Group? = itemView.findViewById(R.id.main_group_great_comment)
        val tvCommentAuthorName: TextView? = itemView.findViewById(R.id.main_tv_comment_author_name)
        val tvCommentContent: TextView? = itemView.findViewById(R.id.main_tv_great_comment_content)
        val tvCommentLikeCount: TextView? = itemView.findViewById(R.id.main_tv_comment_like_count)
        val ivCommentLikeIcon: ImageView? = itemView.findViewById(R.id.main_iv_comment_like_icon)
        val lottieCommentLike: LottieAnimationView? =
            itemView.findViewById(R.id.main_iv_comment_lottie_like)
        val vCommentBg: View? = itemView.findViewById(R.id.main_v_comment_bg)
        val vBottomDivider: View? = itemView.findViewById(R.id.main_v_bottom_divider)
        var track: TopicDetailTrack? = null
    }

    class TopicListenTrackViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val mainIvCover: ImageFilterView by lazy { itemView.findViewById<ImageFilterView>(R.id.main_iv_cover) }
        val mainIvPlayBg: ImageView by lazy { itemView.findViewById<ImageView>(R.id.main_iv_play_bg) }
        val mainTvTrackName: TextView by lazy { itemView.findViewById<TextView>(R.id.main_tv_track_name) }
        val mainTvPlayCount: TextView by lazy { itemView.findViewById<TextView>(R.id.main_tv_play_count) }
        val mainTvDuration: TextView by lazy { itemView.findViewById<TextView>(R.id.main_tv_duration) }
        var cacheTrack: TopicListenTrackInfo? = null
    }

    class TopicDetailTitleViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val mainTvTitle: TextView by lazy { itemView.findViewById<TextView>(R.id.main_tv_title) }
        val mainTvNext: TextView by lazy { itemView.findViewById<TextView>(R.id.main_tv_next) }
    }

    class TopicDetailTimeLineViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val mainTvDate: TextView by lazy { itemView.findViewById<TextView>(R.id.main_tv_date) }
        val mainIvPlayBg: ImageFilterView by lazy { itemView.findViewById<ImageFilterView>(R.id.main_iv_play_bg) }
        val mainIvPlay: ImageView by lazy { itemView.findViewById<ImageView>(R.id.main_iv_play) }
        val mainTvTrackName: TextView by lazy { itemView.findViewById<TextView>(R.id.main_tv_track_name) }
        val mainTvMore: TextView by lazy { itemView.findViewById<TextView>(R.id.main_tv_more) }
        val mainGMore: Group by lazy { itemView.findViewById<Group>(R.id.main_g_more) }
        var cachedItem: TopicTimeLineInfo? = null
    }

    interface IOnTrackEventListener {
        fun onTrackPlayBtnClick(track: TopicDetailTrack)
        fun playTrackAndStartPlayPage(track: TopicDetailTrack)
    }
}