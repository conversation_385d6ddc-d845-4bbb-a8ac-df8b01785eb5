package com.ximalaya.ting.android.main.util

import com.google.gson.Gson
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.model.customize.InterestCardModel
import com.ximalaya.ting.android.host.util.common.JsonUtil
import com.ximalaya.ting.android.main.fragment.find.other.BaseChooseLikeFragment
import com.ximalaya.ting.android.main.fragment.find.other.ChooseLikeFragmentV5
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil

/**
 * 新老兴趣选择界面处理工具类
 */
object ChooseLikeUtil {

    private const val CHOOSE_LIKE_V5_DATA_KEY = "choose_like_v5_data_key"

    @JvmStatic
    fun newInstance(
        isShowRecommendAlbumCardFra: Boolean,
        isShowHalfScreenGenderAge: <PERSON>olean,
        skipShowPlayCard: Boolean,
        from: String?
    ): BaseChooseLikeFragment {
        return newInstance(
            isShowRecommendAlbumCardFra,
            isShowHalfScreenGenderAge,
            skipShowPlayCard,
            "",
            InterestCardModel.GENDER_UNKNOWN, from
        )
    }

    @JvmStatic
    fun newInstance(
        isShowRecommendAlbumCardFra: Boolean, isShowHalfScreenGenderAge: Boolean,
        skipShowPlayCard: Boolean, ageRange: String, gender: Int,
        from: String?
    ): BaseChooseLikeFragment {
        return newInstance(
            isShowRecommendAlbumCardFra,
            isShowHalfScreenGenderAge,
            skipShowPlayCard,
            ageRange,
            gender,
            false, from
        )
    }

    @JvmStatic
    fun newInstance(
        isShowRecommendAlbumCardFra: Boolean, isShowHalfScreenGenderAge: Boolean,
        skipShowPlayCard: Boolean, ageRange: String, gender: Int, showBackBtn: Boolean,
        from: String?
    ): BaseChooseLikeFragment {
        val chooseLikeFragment = ChooseLikeFragmentV5.newInstance(
            isShowRecommendAlbumCardFra,
            isShowHalfScreenGenderAge,
            skipShowPlayCard,
            ageRange,
            gender,
            showBackBtn
        )
        chooseLikeFragment.from = from ?: ""

        return chooseLikeFragment
    }

    @JvmStatic
    fun saveChooseLikeData(selectedId: HashSet<String>, selectedCode: HashSet<String>) {
        val context = BaseApplication.getMyApplicationContext()

        val chooseLikeData = ChooseLikeData(selectedId, selectedCode, System.currentTimeMillis())

        JsonUtil.toJson(chooseLikeData) {
            MmkvCommonUtil.getInstance(context).saveString(CHOOSE_LIKE_V5_DATA_KEY, it)
        }
    }

    @JvmStatic
    fun getChooseLikeData(): ChooseLikeData? {
        val context = BaseApplication.getMyApplicationContext()
        val data = MmkvCommonUtil.getInstance(context).getString(CHOOSE_LIKE_V5_DATA_KEY)

        if (data.isNullOrEmpty()) {
            return null
        }

        return Gson().fromJson(data, ChooseLikeData::class.java)
    }

    data class ChooseLikeData(
        val selectedId: HashSet<String>,
        val selectedCode: HashSet<String>,
        val selectTime: Long
    )
}