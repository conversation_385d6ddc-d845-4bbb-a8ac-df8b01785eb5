package com.ximalaya.ting.android.main.util;

import android.app.Activity;
import android.net.Uri;
import android.os.Bundle;
import android.text.StaticLayout;
import android.view.View;
import android.view.ViewStub;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.FragmentUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.push.PushModel;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.main.IStableProvider;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.StableProvider;
import com.ximalaya.ting.android.main.accessibilitymode.fragment.AccessibilityModeSubscribeListFragment;
import com.ximalaya.ting.android.main.adModule.fragment.AdSettingFragment;
import com.ximalaya.ting.android.main.adapter.CalabashLineAdapter;
import com.ximalaya.ting.android.main.adapter.find.recommend.CalabashAdapterProvider;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.albumModule.album.AlbumRateListFragmentV2;
import com.ximalaya.ting.android.main.albumModule.album.album2.AlbumFragmentNew2;
import com.ximalaya.ting.android.main.albumModule.album.singleAlbum.AlbumLoadErrorManager;
import com.ximalaya.ting.android.main.albumModule.album.singleAlbum.AlbumTraceManagerKt;
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.fragment.AnchorPrivacySettingFragment;
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.AnchorSpaceUtil;
import com.ximalaya.ting.android.main.dubbingModule.fragment.DubbingUserInfoFragment;
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentNew;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.PushSettingFragment;
import com.ximalaya.ting.android.main.listener.MonthlyVoteResultCallback;
import com.ximalaya.ting.android.main.manager.ITingHandler;
import com.ximalaya.ting.android.main.manager.TempoManager;
import com.ximalaya.ting.android.main.manager.mylisten.CategoryViewManager;
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew;
import com.ximalaya.ting.android.main.model.rec.RecommendModelNew;
import com.ximalaya.ting.android.main.model.recommend.RecommendDiscoveryM;
import com.ximalaya.ting.android.main.playlet.fragment.PlayletCommentFragment;
import com.ximalaya.ting.android.main.playlet.fragment.PlayletPlayInfoFragment;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageAdaptationUtilKt;
import com.ximalaya.ting.android.main.playpage.fragment.AudioPlayFragment;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.vote.dialog.VoteDialogFragment;
import com.ximalaya.ting.android.main.roleModule.fragment.RoleCommentsFragment;
import com.ximalaya.ting.android.main.util.other.ShareUtilsInMain;
import com.ximalaya.ting.android.main.view.text.StaticLayoutManager;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import org.json.JSONObject;

import java.util.List;

/**
 * Created by nali on 2024/10/16.
 *
 * <AUTHOR>
 */

public class StableUtil {

    public static void init() {
        StableProvider.init(new IStableProvider() {
            @Override
            public void handleCalabashAdapterProvider(IMulitViewTypeViewAndData mulitViewTypeAdapter, int mFromForCalabashLineAdapter, List<ItemModel> listData, int position) {
                if (mulitViewTypeAdapter instanceof CalabashAdapterProvider) {
                    CalabashAdapterProvider twoLineCalabashAdapterProvider =
                            (CalabashAdapterProvider) mulitViewTypeAdapter;
                    try {
                        twoLineCalabashAdapterProvider.setIsSingleLineStyle((List<RecommendDiscoveryM>) listData.get(position).object);
                        if (mFromForCalabashLineAdapter != -1) {
                            twoLineCalabashAdapterProvider.setFrom(mFromForCalabashLineAdapter);
                        } else {
                            twoLineCalabashAdapterProvider.setFrom(CalabashLineAdapter.FROM_RECOMMEND);
                        }
                    } catch (ClassCastException e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void shareTrack(Activity activity, TrackM trackM, int shareTypeTrack) {
                ShareUtilsInMain.shareTrack(activity, trackM, shareTypeTrack);
            }

            @Override
            public String getDISCOVER_TYPE_ITING() {
                return CalabashLineAdapter.DISCOVER_TYPE_ITING;
            }

            @Override
            public String getDISCOVER_TYPE_H5() {
                return CalabashLineAdapter.DISCOVER_TYPE_H5;
            }

            @Override
            public BaseFragment2 getDubbingUserInfoFragment(long uid) {
                return DubbingUserInfoFragment.newInstance(uid);
            }

            @Override
            public void shareTrackWithoutXdcs(Activity activity, Track track, String dstType, int shareType) {
                 ShareUtilsInMain.shareTrackWithoutXdcs(activity, track, dstType, shareType);
            }

            @Override
            public void shareDubToDst(Activity activity, Track track, String dstName) {
                ShareUtilsInMain.shareDubToDst(activity, track, dstName);
            }

            @Override
            public void loadRecommendNetManagerLoadDataForNet(boolean isLoadMore, IDataCallBack<JSONObject> callBack) {
                RecommendFragmentNetManager.Companion.getInstance().loadDataFromNet(isLoadMore, true, false, new IDataCallBack<RecommendModelNew>() {
                    @Override
                    public void onSuccess(@Nullable RecommendModelNew data) {
                        if (data != null && data.getBody() != null) {
                            for (RecommendItemNew recommendItemNew : data.getBody()) {
                                try {
                                    if ("ListenList/Album_BigCard".equals(recommendItemNew.getItemType())) {
                                        JSONObject jsonObject = recommendItemNew.getJsonObject();
                                        callBack.onSuccess(jsonObject);
                                        return;

                                    }
                                } catch (Exception e) {
                                    callBack.onError(604, e.getMessage());
                                    return;
                                }
                            }

                            callBack.onSuccess(null);

                        }
                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });
            }

            @Override
            public int getPlayPageManagerBackgroudColor() {
                return PlayPageDataManager.getInstance().getBackgroundColor();
            }

            @Override
            public BaseFragment2 newAnchorSpaceFragment(long uid) {
                return AnchorSpaceUtil.newAnchorSpaceFragment(uid);
            }

            @Override
            public void setPushSettingOpenIncludeLogoutState(String key, boolean isPush) {
                PushSettingFragment.setPushSettingOpenIncludeLogoutState(key, isPush);
            }

            @Override
            public boolean isFromPaidAlbum(PlayableModel playableModel) {
                return AlbumTypeUtil.isFromPaidAlbum(playableModel);
            }

            @Override
            public boolean isFromVipAlbum(PlayableModel playableModel) {
                return AlbumTypeUtil.isFromVipAlbum(playableModel);
            }

            @Override
            public boolean isFreeTrack(PlayableModel playableModel) {
                return AlbumTypeUtil.isFreeTrack(playableModel);
            }

            @Override
            public void showNewUserScrollMissionTip() {
                Activity activity = BaseApplication.getMainActivity();
                if (null != activity && activity instanceof FragmentActivity) {
                    Fragment fragment = FragmentUtil.getShowingFragmentByClass((FragmentActivity) activity, RecommendFragmentNew.class);
//                    if (null != fragment && fragment instanceof RecommendFragmentNew) {
//                        ((RecommendFragmentNew) fragment).showNewUserScrollMissionTip();
//                    }
                }
            }

            @Override
            public BaseFragment2 newAnchorPrivacySettingFragment() {
                return AnchorPrivacySettingFragment.Companion.newInstance(null);
            }

            @Override
            public BaseFragment2 newAdSettingFragment() {
                return new AdSettingFragment();
            }

            @Override
            public List<String> searchUtilsParseList(String saleList) {
                return SearchUtils.parseList(saleList, new SearchUtils.IParse<String>() {
                    @Override
                    public String parse(String string) {
                        return string;
                    }
                });
            }

            @Override
            public Class<? extends Fragment> getAlbumRateListFragmentV2() {
                return AlbumRateListFragmentV2.class;
            }

            @Override
            public BaseFragment2 getAlbumFragmentNew2(String title, long albumId, int from, int playSource, int unreadNum) {
                return AlbumFragmentNew2.newInstance(title, albumId, from, playSource, unreadNum);
            }

            @Override
            public void handleIting(Activity activity, Uri uri) {
                ITingHandler iTingHandler = new ITingHandler();
                iTingHandler.handleITing(activity, uri);
            }

            @Override
            public boolean handleITing(Activity activity, PushModel pm) {
                ITingHandler iTingHandler = new ITingHandler();
                return iTingHandler.handleITing(activity, pm);
            }

            @Override
            public void pageTraceFail(BaseFragment2 fragment2) {
                AlbumTraceManagerKt.pageTraceFail(fragment2);
            }

            @Override
            public void pageTraceSuccess(BaseFragment2 fragment2) {
                AlbumTraceManagerKt.pageTraceSuccess(fragment2);
            }

            @Override
            public void pageMonitorReport(BaseFragment2 fragment, int result, String type, String message) {
                AlbumLoadErrorManager.pageMonitorReport(fragment, result, type, message);
            }

            @Override
            public StaticLayout getNormalLayout(String content, int width) {
                return StaticLayoutManager.getInstance().getNormalLayout(content, width);
            }

            @Override
            public StaticLayout getLimitLayout(String content, int width, IHandleOk onLookAllClick) {
                return StaticLayoutManager.getInstance().getLimitLayout(content, width, onLookAllClick);
            }

            @Override
            public void shareAlbum(Activity activity, AlbumM album, String currPage, ShareManager.Callback callback) {
                ShareUtilsInMain.shareAlbum(activity, album, currPage, callback);
            }

            @Override
            public View getHintArea(BaseFragment2 fragment2) {
                if (fragment2 instanceof AudioPlayFragment) {
                    int viewStubId = AudioPlayPageAdaptationUtilKt.isExtremelySmallDevice() ? R.id.main_vs_training_camp_hint_in_list :
                            R.id.main_vs_training_camp_hint;
                    ViewStub stub = fragment2.findViewById(viewStubId);
                    if (null != stub) {
                        return stub.inflate();
                    }
                }
                return null;
            }

            @Override
            public Fragment newInstancePlayletPlayInfoFragment(Track track, boolean isAutoShowKeyboard, PlayletPlayInfoFragment.ICommentClick iCommentFragment) {
                return PlayletCommentFragment.Companion.newInstance(track, false, null);
            }

            @Override
            public BaseFragment2 newInstanceAlbumFragmentNew2(String title, String recSrc, String recTrack, long albumId, int from, int playSource, int unreadNum) {
                return AlbumFragmentNew2.newInstance(title, recSrc, recTrack, albumId, from, playSource, unreadNum);
            }

            @Override
            public void addCategoryViewManager(List<TabCommonAdapter.FragmentHolder> fragmentHolderList) {
                CategoryViewManager.CalDimension[] values = CategoryViewManager.CalDimension.values();
                for (CategoryViewManager.CalDimension item : values) {
                    Bundle arguments = new Bundle();
                    arguments.putString(AccessibilityModeSubscribeListFragment.ARGUMENT_KEY_DIMENSION, item.getKey());
                    TabCommonAdapter.FragmentHolder fragmentHolder = new TabCommonAdapter.FragmentHolder(AccessibilityModeSubscribeListFragment.class, item.getTitle(), arguments);
                    fragmentHolderList.add(fragmentHolder);
                }
            }

            @Override
            public void showVoteDialogFragment(FragmentManager childFragmentManager, long albumId, long anchorUid, IHandleOk iHandleOk) {
                VoteDialogFragment voteDialogFragment = VoteDialogFragment.show(childFragmentManager, "", 0L, albumId, anchorUid, VoteDialogFragment.TAB_INDEX_SEND_TICKET);
                voteDialogFragment.setOnMonthlyVoteResultCallback(new MonthlyVoteResultCallback() {
                    @Override
                    public void success(int voteCount) {
                        if (iHandleOk != null) {
                            iHandleOk.onReady();
                        }
                    }
                });
            }

            @Nullable
            @Override
            public BaseFragment2 newRoleCommentsFragment() {
                return new RoleCommentsFragment();
            }

            @Override
            public void resetTempoAlbum(long albumId) {
                if (TempoManager.getInstance().getCurrentAlbumId() == albumId) {
                    TempoManager.getInstance().readCurrentAlbumTempo(albumId, true);
                }
            }
        });
    }
}
