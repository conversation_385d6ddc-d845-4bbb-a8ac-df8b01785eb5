@file:JvmMultifileClass
@file:JvmName("RecommendPageModuleUtil")

package com.ximalaya.ting.android.main.util.ui

import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.util.extension.dp

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/1/25.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */

val MODULE_ITEM_RIGHT_SPACING_FIRST_PAGE = 21.dp
val MODULE_ITEM_MIN_WIDTH = 85.dp
val MODULE_ITEM_MAX_WIDTH = 150.dp

@JvmOverloads fun calcItemWidth(firstPageItemCount: Int, leftMargin: Int, spacing: Int,
                  rightSpacingFirstPage: Int = MODULE_ITEM_RIGHT_SPACING_FIRST_PAGE,
                  minItemWidth: Int = MODULE_ITEM_MIN_WIDTH, maxItemWidth: Int = MODULE_ITEM_MAX_WIDTH) : Int {
    val screenWidth = BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext())
    val itemWidth = (screenWidth - leftMargin - spacing * (firstPageItemCount - 1) - rightSpacingFirstPage) / firstPageItemCount;
    val min = if (minItemWidth <= 0) 1 else minItemWidth
    val max = if (maxItemWidth <= 0) Int.MAX_VALUE else maxItemWidth
    return itemWidth.coerceIn(min, max)
}