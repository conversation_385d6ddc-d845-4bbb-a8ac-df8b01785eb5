package com.ximalaya.ting.android.main.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;

public class NestedScrollViewWithListen extends NestedScrollView {
    private IScrollStateChangeListener scrollStateChangeListener;
    private int scrollState = 0; // 0: 滑动中, 1: 滑动停止
    private Runnable scrollStopRunnable = new Runnable() {
        @Override
        public void run() {
            scrollState = 1; // 标记为停止
            onStopScrolling(); // 调用停止滑动的方法
        }
    };

    public NestedScrollViewWithListen(@NonNull Context context) {
        super(context);
    }

    public NestedScrollViewWithListen(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public NestedScrollViewWithListen(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        switch (ev.getActionMasked()) {
            case MotionEvent.ACTION_MOVE:
                scrollState = 0; // 正在滑动中
                onStartScrolling();
                removeCallbacks(scrollStopRunnable); // 移除之前的停止检测
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                postDelayed(scrollStopRunnable, 300); // 300ms后检测停止
                break;
            default:
                break;
        }
        return super.onTouchEvent(ev);
    }

    public void addScrollStateChangeListener(IScrollStateChangeListener scrollStateChangeListener) {
        this.scrollStateChangeListener = scrollStateChangeListener;
    }

    private void onStartScrolling() {
        if (scrollStateChangeListener != null) {
            scrollStateChangeListener.onScrollStart();
        }
    }

    private void onStopScrolling() {
        if (scrollStateChangeListener != null) {
            scrollStateChangeListener.onScrollStop();
        }
    }

    public interface IScrollStateChangeListener {
        void onScrollStart();

        void onScrollStop();
    }
}
