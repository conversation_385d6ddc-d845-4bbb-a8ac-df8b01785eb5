package com.ximalaya.ting.android.main.view.comment

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.model.play.QuickCommentTemplateModel
import com.ximalaya.ting.android.host.util.view.EmotionUtil
import com.ximalaya.ting.android.main.R

/**
 * Created by ZhuPeipei on 2020/10/21 14:12.
 */
class QuickBulletAdapter(private val mCategoryManager: QuickBulletViewManager) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val mData = ArrayList<QuickCommentTemplateModel>()

    fun setData(categoryModels: List<QuickCommentTemplateModel>?) {
        mData.clear()
        if (categoryModels != null) {
            mData.addAll(categoryModels)
        }
        notifyDataSetChanged()
    }

    fun clearData() {
        mData.clear()
        notifyDataSetChanged()
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val model = getItem(position) ?: return
        if (holder is MyViewHolder) {
            holder.categoryTv.text = EmotionUtil.getInstance().convertEmotionText(model.content)
        }
        holder.itemView.setOnClickListener(View.OnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@OnClickListener
            }
            mCategoryManager.onClickItem(model, position)
        })
    }

    private fun getItem(position: Int): QuickCommentTemplateModel? {
        if (position >= 0 && position < mData.size) {
            return mData[position]
        }
        return null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(
            R.layout.main_layout_quick_bullet_item, parent, false
        )
        return MyViewHolder(view)
    }

    override fun getItemCount(): Int {
        return mData.size
    }

    class MyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val categoryTv: TextView by lazy { this.itemView.findViewById<TextView>(R.id.main_tv_content) }
    }
}