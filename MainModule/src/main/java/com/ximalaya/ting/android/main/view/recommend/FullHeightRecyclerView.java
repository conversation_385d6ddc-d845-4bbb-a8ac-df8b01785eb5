package com.ximalaya.ting.android.main.view.recommend;

import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.ximalaya.ting.android.host.manager.bundleframework.classloader.ShareReflectUtil;
import com.ximalaya.ting.android.main.R;

import java.lang.reflect.Field;

public class FullHeightRecyclerView extends RecyclerView {

    public FullHeightRecyclerView(Context context) {
        super(context);
        init();
    }

    public FullHeightRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public FullHeightRecyclerView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init() {
        // 禁用嵌套滚动避免冲突
        setNestedScrollingEnabled(false);
    }

    @Override
    protected void onMeasure(int widthSpec, int heightSpec) {
        // 重写测量规则，允许RecyclerView扩展到实际内容高度
        int expandSpec = MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE >> 2, MeasureSpec.AT_MOST);
        super.onMeasure(widthSpec, expandSpec);
    }

    // 当Item高度变化时调用此方法
    public void notifyItemHeightChanged() {
        // 通知布局管理器重新计算
        if (getLayoutManager() != null) {
            getLayoutManager().requestLayout();
        }
        requestLayout();
    }
}

