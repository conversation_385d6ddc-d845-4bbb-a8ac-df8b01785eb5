package com.ximalaya.ting.android.main.view.recommend

import android.content.Context
import android.util.AttributeSet
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.xmutil.Logger

/**
 * 新的自适应高度ViewPager
 * 支持RecyclerView渲染完成后自动调整高度
 * 专门用于解决嵌套自适应高度容器的问题
 */
class WrapContentViewPager @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : NonSwipeableViewPager(context, attrs) {

    companion object {
        const val TAG = "WrapContentViewPager"
    }

    var logEnable = false

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)

        // 如果父容器指定了确切高度，直接使用
        if (heightMode == MeasureSpec.EXACTLY) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
            return
        }

        // 正确测量所有子view
        var maxHeight = 0
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            child.measure(
                widthMeasureSpec,
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
            )

            // 获取子view的测量高度
            val childHeight = child.measuredHeight
            maxHeight = maxHeight.coerceAtLeast(childHeight)

            if (logEnable) {
                Logger.d(TAG, "i:${i}  child.measuredHeight:${childHeight}")
            }
        }

        // 设置合理的高度范围
        val finalHeight = when {
            maxHeight <= 0 -> 228.dp  // 默认高度
            maxHeight > 5000 -> 5000  // 最大高度限制
            else -> maxHeight
        }

        if (logEnable) {
            Logger.d(TAG, "viewPageHeight:$finalHeight")
        }

        super.onMeasure(
            widthMeasureSpec,
            MeasureSpec.makeMeasureSpec(finalHeight, MeasureSpec.EXACTLY)
        )
    }

    // 提供手动触发重测量的方法
    fun requestHeightRefresh() {
        requestLayout()
    }

}