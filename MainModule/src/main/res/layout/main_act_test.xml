<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="20dp">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/framework_bg_color"
                android:visibility="gone" />

            <RadioGroup
                android:id="@+id/main_choose_environment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <RadioButton
                    android:id="@+id/main_release"
                    style="@style/main_setting_textview_bg_white"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableRight="@null"
                    android:text="正式" />

                <RadioButton
                    android:id="@+id/main_debug"
                    style="@style/main_setting_textview_bg_white"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableRight="@null"
                    android:text="测试" />

                <RadioButton
                    android:id="@+id/main_uat"
                    style="@style/main_setting_textview_bg_white"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableRight="@null"
                    android:text="uat" />

                <TextView
                    android:id="@+id/main_tv_debug_power_test"
                    style="@style/main_setting_textview_bg_white"
                    android:text="电量调试页面" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="AutoSize全局开关(已创建页面重启生效)" />

                    <CheckBox
                        android:id="@+id/main_auto_size_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>


                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="thread监控" />

                    <CheckBox
                        android:id="@+id/main_lock_monitor_cb"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="h5悬浮日志开关" />

                    <CheckBox
                        android:id="@+id/main_main_setting_h5_share_log_cb"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>


                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="布局定位（长按显示布局代码相关信息）" />

                    <CheckBox
                        android:id="@+id/main_view_code_locate_cb"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" >

                    <Button
                        android:id="@+id/main_open_quick_rn_page"
                        android:text="快听RN单流"
                        android:layout_weight="1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />
                    <Button
                        android:id="@+id/main_open_quick_native_tab"
                        android:text="快听原生tab"
                        android:layout_weight="1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />
                    <Button
                        android:id="@+id/main_open_quick_rn_tab"
                        android:text="快听RNTab"
                        android:layout_weight="1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <TextView
                    android:id="@+id/main_btn_goto_tecent_video"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="打开腾讯视频SDK" />

                <TextView
                    android:id="@+id/main_btn_goto_game_center"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="打开游戏入口" />

                <TextView
                    android:id="@+id/main_btn_kill_main_process"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="强杀主进程" />

                <TextView
                    android:id="@+id/main_btn_kill_other_process"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="强杀其他进程" />

                <TextView
                        android:id="@+id/main_btn_bugly_upload"
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="bugly上报" />

                <!--                <TextView-->
                <!--                    android:id="@+id/main_tv_one_key_new_user"-->
                <!--                    style="@style/main_setting_textview_bg_white"-->
                <!--                    android:clickable="false"-->
                <!--                    android:drawableRight="@null"-->
                <!--                    android:text="一键新用户" />-->

                <TextView
                    android:id="@+id/main_tv_debug_rn"
                    style="@style/main_setting_textview_bg_white"
                    android:text="RN调试页面" />

                <TextView
                    android:id="@+id/main_tv_debug_abtest"
                    style="@style/main_setting_textview_bg_white"
                    android:text="ABTest调试页面" />

                <TextView
                    android:id="@+id/main_tv_debug_notification_test"
                    style="@style/main_setting_textview_bg_white"
                    android:text="通知调试页面" />

                <TextView
                    android:id="@+id/main_tv_debug_qiji_test"
                    style="@style/main_setting_textview_bg_white"
                    android:text="奇迹测试页面" />

                <TextView
                    android:id="@+id/main_tv_debug_read_test"
                    style="@style/main_setting_textview_bg_white"
                    android:text="阅读测试页面" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        android:id="@+id/main_tv_offline_res_check_label"
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="离线资源图片标识(首次打开需清理缓存)" />

                    <CheckBox
                        android:id="@+id/main_switch_offline_res_check"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/main_tv_isolation"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="隔离组（仅测试环境）" />

                <TextView
                    android:id="@+id/main_tv_ksh_trace_debug"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="UBT小工具" />

                <TextView
                    android:id="@+id/main_tv_listen_time_new"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="收听时长" />


                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="使用新版播放页" />

                    <CheckBox
                        android:id="@+id/main_switch_use_new_playfragment"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>


                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="是否打开卡顿监测" />

                    <CheckBox
                        android:id="@+id/main_switch_block_fps"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>


                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="是否使用新版本动态页" />

                    <CheckBox
                        android:id="@+id/main_switch_use_new_dynamic_checkbox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        android:id="@+id/main_tv_new_expose_delay_label"
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="新版曝光滚动延时3秒触发" />

                    <CheckBox
                        android:id="@+id/main_switch_new_expose_delay_check"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        android:id="@+id/main_tv_ksh_check_label"
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="可视化回归自动校验" />

                    <CheckBox
                        android:id="@+id/main_switch_ksh_auto_check"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        android:id="@+id/main_tv_ksh_notify_label"
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="可视化埋点debug通知栏" />

                    <CheckBox
                        android:id="@+id/main_switch_ksh_notify"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="弹屏debug" />

                    <CheckBox
                        android:id="@+id/main_switch_firework_debug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/main_tv_clear_firework"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="清除弹屏缓存(重启生效)" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="是否打开弹幕帧率显示" />

                    <com.ximalaya.ting.android.host.view.bar.SwitchButton
                        android:id="@+id/main_sb_show_danmu_fps"
                        android:layout_width="60dp"
                        android:layout_height="21dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:clickable="true" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="后台绘制问题是否弹窗提示" />

                    <com.ximalaya.ting.android.host.view.bar.SwitchButton
                        android:id="@+id/main_sb_show_background_ui_loop_dialog"
                        android:layout_width="60dp"
                        android:layout_height="21dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:clickable="true" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="xlog debug(单条日志)" />

                    <CheckBox
                        android:id="@+id/main_switch_xlog_debug_single"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="xlog debug(上传log返回上传的数据)" />

                    <CheckBox
                        android:id="@+id/main_switch_xlog_debug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="xlogDecode(dir:files/xlog_decoded_file)" />

                    <CheckBox
                        android:id="@+id/main_switch_xlog_decode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/main_setting_xlog_flush"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="xlog flush（会触发上传日志）" />


                <TextView
                    android:id="@+id/main_setting_xlog_test"
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="xlog Test" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="apm使用debug配置" />

                    <CheckBox
                        android:id="@+id/main_switch_apm_use_debug_config"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        android:id="@+id/main_tv_use_test_plugin"
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="使用测试环境插件" />

                    <CheckBox
                        android:id="@+id/main_switch_use_test_plugin"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        android:id="@+id/main_tv_plugin_release_ev"
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="debug关闭远程插件下载" />

                    <CheckBox
                        android:id="@+id/main_switch_plugin_release_ev"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>


                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="Mock数据开关" />

                    <CheckBox
                        android:id="@+id/main_switch_mock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="首页广告是否使用广告sdk" />

                    <CheckBox
                        android:id="@+id/main_switch_recommend_ad_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="播放报错弹窗" />

                    <CheckBox
                        android:id="@+id/main_switch_play_error_dialog"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="开屏广告是否使用广告sdk" />

                    <CheckBox
                        android:id="@+id/main_switch_splash_ad_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="是否使用混排首页" />

                    <CheckBox
                        android:id="@+id/main_switch_feed_fragment_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="广播页贴片频控" />

                    <CheckBox
                        android:id="@+id/main_switch_radio_ad_frequence_control"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="播放页贴片广告频控" />

                    <CheckBox
                        android:id="@+id/main_switch_play_cover_ad_frequence_control"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="贴片场景差异化" />

                    <CheckBox
                        android:id="@+id/main_switch_new_sound_patch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="httpdns mock开关" />

                    <CheckBox
                        android:id="@+id/main_switch_httpdns_mock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false">

                    <TextView
                        style="@style/main_setting_textview_bg_white"
                        android:clickable="false"
                        android:drawableRight="@null"
                        android:text="是否使用新的RTBToken获取策略" />

                    <CheckBox
                        android:id="@+id/main_load_rtb_token_new_stategy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:background="@drawable/host_switch_selector"
                        android:button="@null" />
                </RelativeLayout>

            </RadioGroup>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="图片内存监控 (切换重启生效)" />

                <CheckBox
                    android:id="@+id/main_bitmap_monitor_cb"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/host_switch_selector"
                    android:button="@null" />
            </RelativeLayout>

            <Button
                android:id="@+id/main_btn_create_uuid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="生成uuid" />

            <Button
                    android:id="@+id/main_btn_remove_agree"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="onClick"
                    android:text="清除隐私协议同意" />

            <Button
                android:id="@+id/main_btn_remove_mock_proxy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="删除mock代理" />

            <Button
                android:id="@+id/main_btn_upload_tracer_pid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="上报tracerPid" />

            <Button
                android:id="@+id/main_btn_tts_enter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="TTS入口" />


            <Button
                android:id="@+id/main_btn_sea_enter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="声音交友入口" />

            <Button
                android:id="@+id/main_btn_apply_skin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="更新皮肤" />

            <Button
                android:id="@+id/main_btn_restore_skin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="重置皮肤" />

            <Button
                android:id="@+id/main_btn_ali_auth"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="阿里实名认证" />

            <Button
                android:id="@+id/main_btn_refresh_update_bundle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="更新Bundle" />

            <Button
                android:id="@+id/main_btn_debug_rn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="RN前端调试页面" />

            <Button
                android:id="@+id/main_btn_remove_sp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="删除sp" />

            <Button
                android:id="@+id/main_btn_woting_position"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="vip,我听,我页a,b,c方案切换"
                android:textAllCaps="false"
                android:textSize="15sp" />

            <Button
                android:id="@+id/main_btn_remove_ad_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="删除免广告时间(点击一次少5分钟)"
                android:textAllCaps="false"
                android:textSize="15sp" />

            <Button
                android:id="@+id/main_mock_request_all"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="mock xdcs并发请求"
                android:textAllCaps="false"
                android:textSize="15sp" />

            <Button
                android:id="@+id/main_btn_mock_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="模拟登录" />

            <Button
                android:id="@+id/main_btn_mock_unlock"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="模拟解锁" />

            <Button
                android:id="@+id/main_btn_unicom_single"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="跳转到联通免流单独页面" />

            <Button
                android:id="@+id/main_btn_unicom_has_vip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="跳转到联通免流含vip页面" />

            <Button
                android:id="@+id/main_btn_free_flow"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="跳转到免流页面" />

            <Button
                android:id="@+id/main_btn_request_ad"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="模拟请求" />

            <Button
                android:id="@+id/main_btn_reduce_unlock_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="清除畅听时长(操作后重启)" />

            <Button
                android:id="@+id/main_btn_refresh_config_center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:text="刷新配置中心" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="Debug WebView" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_sb_debug_web_view"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="强制更新build in bundle(重启生效)" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_sb_force_update_build_in_bundle"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:clickable="true" />
            </RelativeLayout>


            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="内存泄漏检测LeakCananry" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_test_sb_leak"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:clickable="true" />
            </RelativeLayout>


            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="卡顿检测BlockCananry" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_test_sb_block"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="检查MAsyncTask任务" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_test_sb_check_pool"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="不检查广告内容" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_test_check_ad_content"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="广告链接是否添加ts" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:clickable="true" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:text="block canary 阈值：" />

                <EditText
                    android:id="@+id/main_et_block_canary_threshold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="是否检测正式环境中url是否包含test" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_check_weburl"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="是否开启硬件解码" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_sb_use_hardware_decode"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="是否开启视频磁盘缓存" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_sb_video_disk_cache_enable"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="true"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="切声音时显示鼓励弹窗" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_sb_show_raise_dialog_when_sound_switch"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="true"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="bannerView 自动切换" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_banner_auto_switch"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="true"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="播放页在封面上显示文稿" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_play_page_show_doc_on_cover"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="false"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="贴片关闭之后展示主播气泡" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_show_pop_ad_after_sound_patch"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="false"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="是否开启调试Log开关" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_user_access_log"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="false"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="RN页面是否显示初始化参数" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_rn_show_init"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="false"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="广点通模板渲染可以展示广告" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_gdt_ad_can_show_ad"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="false"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="今日热点进入白名单" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_dailynews_add_white_list_sb"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="false"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="是否关闭截屏自动提交缺陷功能" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_close_bug_commit_by_screenshot"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="false"
                    android:clickable="true" />
            </RelativeLayout>


            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="mock免流剩余流量为0" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_mock_free_flow_zone"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="false"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="打开测试开屏SDK广告开关" />

                <com.ximalaya.ting.android.host.view.bar.SwitchButton
                    android:id="@+id/main_test_splash_ad"
                    android:layout_width="60dp"
                    android:layout_height="21dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:checked="false"
                    android:clickable="true" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="全局置灰" />

                <CheckBox
                    android:id="@+id/main_global_gray"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/host_switch_selector"
                    android:button="@null" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="驾驶模式测试" />

                <CheckBox
                    android:id="@+id/main_global_drive_mode_test"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/host_switch_selector"
                    android:button="@null" />
            </RelativeLayout>

            <TextView
                android:id="@+id/main_tv_share_panel"
                style="@style/main_setting_textview_bg_white"
                android:text="新版分享组件" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="是否开启压缩请求头" />

                <CheckBox
                    android:id="@+id/main_open_compress_header_test"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/host_switch_selector"
                    android:button="@null" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="是否开启启动IO监控" />

                <CheckBox
                    android:id="@+id/main_open_startup_io_monitor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/host_switch_selector"
                    android:button="@null" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="是否开启webview加速" />

                <CheckBox
                    android:id="@+id/main_open_web_view_fast"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/host_switch_selector"
                    android:button="@null" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="打开听看一体开关(打开状态忽略ab)" />

                <CheckBox
                    android:id="@+id/main_open_web_aitext_reader"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/host_switch_selector"
                    android:button="@null" />

            </RelativeLayout>


            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="打开听看一体广告(打开状态忽略ab)" />

                <CheckBox
                    android:id="@+id/main_open_web_aitext_reader_show_ad"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/host_switch_selector"
                    android:button="@null" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="false">

                <TextView
                    style="@style/main_setting_textview_bg_white"
                    android:clickable="false"
                    android:drawableRight="@null"
                    android:text="打开听看一体免广告(打开状态忽略ab)" />

                <CheckBox
                    android:id="@+id/main_open_ting_read_free_ad"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/host_switch_selector"
                    android:button="@null" />

            </RelativeLayout>

            <TextView
                android:id="@+id/main_tv_expression"
                style="@style/main_setting_textview_bg_white"
                android:text="通用点赞点踩组件1" />


            <TextView
                android:id="@+id/main_new_category"
                style="@style/main_setting_textview_bg_white"
                android:text="新分类页" />

            <RadioGroup
                style="@style/main_setting_textview_bg_white"
                android:id="@+id/main_rg_mine_slide"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12dp"
                    android:text="侧边栏:" />

                <RadioButton
                    android:id="@+id/main_rb_slide_0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12dp"
                    android:text="对照" />

                <RadioButton
                    android:id="@+id/main_rb_slide_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12dp"
                    android:text="一" />

                <RadioButton
                    android:textSize="12dp"
                    android:id="@+id/main_rb_slide_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="二" />

                <RadioButton
                    android:textSize="12dp"
                    android:id="@+id/main_rb_slide_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="三" />
            </RadioGroup>


        </LinearLayout>
    </ScrollView>


    <FrameLayout
        android:id="@+id/flutter_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</FrameLayout>