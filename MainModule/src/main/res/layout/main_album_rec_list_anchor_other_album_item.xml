<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/main_cover_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp">
        <!--专辑图-->
        <ImageView
            style="@style/main_album_item_cover"
            android:layout_marginLeft="0dp"
            android:layout_marginBottom="0dp"
            android:contentDescription="@string/main_content_description_album_default" />
        <!--付费角标-->
        <ImageView
            style="@style/main_album_item_pay_cover_tag_bar_style"
            android:visibility="invisible"
            tools:visibility="visible" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/main_tv_album_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/main_cover_group"
        android:layout_marginLeft="12dp"
        android:layout_toRightOf="@id/main_cover_group"
        android:singleLine="true"
        android:textColor="@color/main_normal_album_title_color"
        android:textSize="14sp"
        tools:text="后继者先锋" />

    <TextView
        android:id="@+id/main_tv_album_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_tv_album_title"
        android:layout_alignLeft="@id/main_tv_album_title"
        android:layout_marginTop="7dp"
        android:singleLine="true"
        android:textColor="@color/main_color_999999_888888"
        android:textSize="12sp"
        tools:text="午后原味音乐跳动感" />

    <TextView
        android:id="@+id/main_tv_play_times"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_tv_album_subtitle"
        android:layout_alignLeft="@id/main_tv_album_subtitle"
        android:layout_marginTop="7dp"
        android:drawableLeft="@drawable/main_ic_item_play_count"
        android:textColor="@color/main_color_999999_888888"
        android:textSize="12sp"
        tools:text="1351.8万" />

    <TextView
        android:id="@+id/main_tv_track_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/main_tv_play_times"
        android:layout_marginLeft="15dp"
        android:layout_toRightOf="@id/main_tv_play_times"
        android:drawableLeft="@drawable/host_ic_track_count"
        android:textColor="@color/main_color_999999_888888"
        android:textSize="12sp"
        tools:text="48" />

</RelativeLayout>