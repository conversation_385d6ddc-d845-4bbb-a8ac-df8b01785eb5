<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:tools="http://schemas.android.com/tools"
              android:id="@+id/main_layout_ad"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:background="@drawable/main_soft_ad_dialog_bg"
              android:orientation="vertical">

    <ImageView
        android:id="@+id/main_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:padding="6dp"
        android:src="@drawable/main_woting_cancel_subscribe"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="-5dp">

        <ImageView
            android:id="@+id/main_ad_icon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:scaleType="fitXY"/>

        <TextView
            android:id="@+id/main_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_toRightOf="@id/main_ad_icon"
            android:ellipsize="end"
            android:maxLines="1"
            android:textStyle="bold"
            android:textColor="@color/main_color_333333_cfcfcf"
            android:textSize="14sp"
            tools:text="恭喜您！获得100元月饼购物券！"/>
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/main_ad_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginTop="15dp"
            android:scaleType="fitXY"
            tools:src="@drawable/main_player_img_ad_coupon"/>

        <RelativeLayout
            android:visibility="gone"
            tools:visibility="visible"
            android:id="@+id/main_ad_favorable_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/main_ad_img"
            android:layout_alignLeft="@id/main_ad_img"
            android:layout_alignRight="@id/main_ad_img"
            android:layout_alignTop="@id/main_ad_img">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:layout_centerVertical="true">

                <TextView
                    android:id="@+id/main_ad_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:textColor="@color/main_white"
                    android:textSize="35sp"
                    android:textStyle="bold"
                    tools:text="100"/>

                <TextView
                    android:id="@+id/main_ad_price_suffix"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@id/main_ad_price"
                    android:layout_marginLeft="13dp"
                    android:layout_toRightOf="@id/main_ad_price"
                    android:text="元"
                    android:textColor="@color/main_white"
                    android:textSize="13sp"/>

                <TextView
                    android:id="@+id/main_ad_favorable_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/main_ad_price"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/main_white"
                    tools:text="100元全场直减"/>

                <View
                    android:id="@+id/main_ad_mark_view"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="80dp"
                    android:layout_width="1px"
                    android:layout_height="10dp"/>

                <View
                    android:id="@+id/main_ad_favorable_line"
                    android:layout_width="wrap_content"
                    android:layout_height="1dp"
                    android:layout_alignRight="@id/main_ad_mark_view"
                    android:layout_marginRight="24dp"
                    android:layout_below="@id/main_ad_favorable_content"
                    android:layout_marginBottom="6dp"
                    android:layout_marginTop="6dp"
                    android:background="#4cffffff"/>

                <TextView
                    android:id="@+id/main_ad_favorable_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/main_ad_favorable_line"
                    android:textColor="@color/main_white"
                    android:textSize="11sp"/>

                <TextView
                    android:id="@+id/main_ad_do_something"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignLeft="@id/main_ad_mark_view"
                    android:layout_alignParentRight="true"
                    android:gravity="center"
                    android:layout_centerVertical="true"
                    android:textColor="@color/main_white"
                    android:textSize="13sp"
                    tools:text="去使用"/>
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/main_lotty_lay"
                android:background="@color/main_color_f5f5f5_2a2a2a"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:gravity="center"
                tools:visibility="visible"
                android:visibility="gone"
                android:layout_height="match_parent">

                <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                    android:id="@+id/main_lottie_view"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"/>

                <ImageView
                    android:id="@+id/main_lotty_error"
                    android:layout_width="wrap_content"
                    android:visibility="gone"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/main_lottie_title"
                    android:layout_width="wrap_content"
                    android:textSize="14sp"
                    android:layout_marginTop="10dp"
                    android:textColor="@color/main_color_666666_888888"
                    tools:text="兑换码：REWJHK123"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:id="@+id/main_lottie_subtitle"
                    android:layout_width="wrap_content"
                    android:textSize="14sp"
                    android:layout_marginTop="8dp"
                    tools:text="已复制成功，快去使用吧！"
                    android:textColor="@color/main_color_666666_888888"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>

    <TextView
        android:id="@+id/main_ad_bottom_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:maxLines="2"
        tools:text="本优惠券由「阿里巴巴旗下返利app_一淘」提供"
        android:textColor="@color/main_color_666666_888888"
        android:textSize="12sp"/>

</LinearLayout>