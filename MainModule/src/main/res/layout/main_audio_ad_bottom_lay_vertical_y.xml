<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_audio_bottom_ad_lay"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"
    tools:parentTag="com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.adnew.view.AudioAdBottomContentView">

    <com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout
        android:id="@+id/main_audio_bottom_ad_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:corner="left_bottom|right_bottom"
        app:corner_radius="8dp">

        <TextView
            android:id="@+id/main_audio_bottom_ad_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:ellipsize="end"
            android:lineSpacingMultiplier="1.2"
            android:gravity="center"
            android:maxLines="2"
            android:text="标题最多字数十八个字占位占位位…标题最多字数十八个字占位占位位问问"
            android:textColor="@color/main_color_ffffff"
            android:textSize="14sp" />

        <com.ximalaya.ting.android.host.view.ad.XAdActionBtnView
            android:layout_marginTop="12dp"
            android:id="@+id/main_audio_bottom_ad_click"
            android:layout_width="match_parent"
            android:maxWidth="168dp"
            android:layout_height="36dp"
            android:layout_below="@id/main_audio_bottom_ad_title"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:background="#33FFFFFF"
            android:textColor="@color/main_white"
            android:textSize="13sp"
            android:visibility="gone"
            tools:text="立即查看"
            tools:visibility="visible" />

        <com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.view.YClickAdRewardHintView
            android:layout_marginTop="12dp"
            android:layout_below="@+id/main_audio_bottom_ad_title"
            android:id="@+id/main_play_ad_click_ad_reward_hint"
            android:layout_centerHorizontal="true"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/main_cover_ad_shake_layout_bottom_btn"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:gravity="center"
            android:layout_below="@+id/main_audio_bottom_ad_title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="12dp"
            android:visibility="visible"
            tools:visibility="visible" />

    </com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout>
</RelativeLayout>
