<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content_ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_rect_ffffff_1e1e1e_radius_10">

    <TextView
        android:id="@+id/main_tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:maxLines="2"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:text="自定义金额"
        android:textColor="@color/framework_color_333333_cfcfcf"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/main_et_dian_value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/main_bg_f3f4f5_353535_corner_4dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:hint="请输入2-4999间的正整数"
        android:includeFontPadding="false"
        android:inputType="number"
        android:paddingStart="28dp"
        android:paddingTop="9dp"
        android:paddingBottom="9dp"
        android:singleLine="true"
        android:textColor="@color/host_color_111111_bbbbbb"
        android:textColorHint="@color/host_color_bbbbbb_888888"
        android:textSize="13sp"
        app:layout_constraintTop_toBottomOf="@id/main_tv_title" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:text="¥"
        android:textSize="13sp"
        app:layout_constraintBottom_toBottomOf="@id/main_et_dian_value"
        app:layout_constraintStart_toStartOf="@id/main_et_dian_value"
        app:layout_constraintTop_toTopOf="@id/main_et_dian_value" />

    <TextView
        android:id="@+id/main_tv_use_dian"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="共消耗喜点 : 0"
        android:textColor="@color/host_color_666666_888888"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_et_dian_value" />

    <TextView
        android:id="@+id/main_tv_dian_remaining"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="喜点余额 : 0"
        android:textColor="@color/host_color_666666_888888"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_tv_use_dian" />

    <View
        android:id="@+id/main_btn_border"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="20dp"
        android:background="@color/framework_color_e8e8e8_2a2a2a"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_tv_use_dian" />

    <Button
        android:id="@+id/main_btn_cancel"
        style="@style/round_bottom_btn_left_bg"
        android:layout_width="0dp"
        android:layout_height="45dp"
        android:singleLine="true"
        android:text="取消"
        android:textColor="@color/framework_color_666666_888888"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/main_btn_separator_border"
        app:layout_constraintTop_toBottomOf="@id/main_btn_border" />

    <View
        android:id="@+id/main_btn_separator_border"
        android:layout_width="1px"
        android:layout_height="45dp"
        android:background="@color/framework_color_e8e8e8_2a2a2a"
        app:layout_constraintLeft_toRightOf="@id/main_btn_cancel"
        app:layout_constraintRight_toLeftOf="@id/main_btn_give"
        app:layout_constraintTop_toBottomOf="@id/main_btn_border" />


    <Button
        android:id="@+id/main_btn_give"
        style="@style/round_bottom_btn_right_bg"
        android:layout_width="0dp"
        android:layout_height="45dp"
        android:singleLine="true"
        android:text="打赏"
        android:textColor="@color/framework_white_ffffff"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/main_btn_separator_border"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_btn_border" />

    <Button
        android:id="@+id/main_btn_recharge"
        style="@style/round_bottom_btn_right_bg"
        android:layout_width="0dp"
        android:layout_height="45dp"
        android:singleLine="true"
        android:text="余额不足请充值"
        android:textColor="@color/framework_white_ffffff"
        android:textSize="16sp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/main_btn_separator_border"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_btn_border" />
</androidx.constraintlayout.widget.ConstraintLayout>
