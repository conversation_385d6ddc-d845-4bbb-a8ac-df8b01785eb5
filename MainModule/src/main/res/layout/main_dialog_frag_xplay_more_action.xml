<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_frag_play_more_lay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/main_rect_radius_10_color_f5f5f5"
    android:orientation="vertical">

    <View
        android:layout_width="30dp"
        android:layout_height="@dimen/host_y4"
        android:layout_marginTop="@dimen/host_y8"
        android:layout_marginBottom="@dimen/host_y10"
        android:background="@drawable/main_bg_rect_80aaaaaa_4d8d8d91_corner_100"
        android:layout_gravity="center_horizontal"
        />

    <ViewStub
        android:id="@+id/main_operate_float_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/host_y4"
        android:layout="@layout/host_share_or_more_ad_layout_new" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/main_play_more_action_header_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/host_y6"
            android:fadingEdge="none"
            android:overScrollMode="never"
            android:scrollbars="none"
            android:clipChildren="false"
            android:clipToPadding="false"
            tools:layout_height="75dp" />

        <!--此处因为是倒计时的ui所占宽度超过了各个item均分的范围，因此没法放在itemview中-->
        <com.ximalaya.ting.android.main.playModule.quality.PlayQualityCountDownTextView
            android:id="@+id/main_play_quality_count_down"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:background="@drawable/main_bg_rect_fadfc6_dcac7c_corner_8"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:textColor="@color/host_color_ffffff"
            android:textSize="10sp"
            android:visibility="gone"
            tools:text="2天17时26分"
            tools:visibility="visible" />

    </RelativeLayout>

    <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
        android:id="@+id/main_play_more_action_lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="20dp"
        android:layout_weight="1"
        android:background="@color/main_transparent"
        android:clipToPadding="true"
        android:divider="@null"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:listSelector="@color/main_transparent"
        android:overScrollMode="never"
        android:scrollbars="none" />

</LinearLayout>