<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="16dp"
    tools:background="@color/host_color_000000_24"
    tools:ignore="MissingDefaultResource">

    <View
        android:id="@+id/main_view_space"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/main_dialog_real_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/main_iv_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="12dp"
        android:src="@drawable/main_mot_dialog_close_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/main_dialog_real_container" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_dialog_real_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Space
            android:id="@+id/main_iv_top_card_space"
            android:layout_width="match_parent"
            android:layout_height="1px"
            app:layout_constraintVertical_bias="0.4465"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main_cl_platinum_coupon"
            app:layout_constraintBottom_toBottomOf="@+id/main_cl_platinum_coupon"
            app:layout_constraintStart_toStartOf="parent" />

        <Space
            android:id="@+id/main_iv_top_card_space1"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginBottom="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/main_iv_top_card_space"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/main_bottom_8corner_ffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main_iv_top_card_space1" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_iv_top_bg_img"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            tools:background="@drawable/main_8corner_ffffff"
            app:layout_constraintDimensionRatio="343:210"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/main_iv_top_card_space"
            app:round="8dp" />

        <TextView
            android:id="@+id/main_tv_title_part_1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="16dp"
            app:layout_constrainedWidth="true"
            android:layout_marginEnd="62dp"
            android:textColor="#ff185079"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:ellipsize="end"
            android:textSize="22dp"
            android:textFontWeight="700"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="白金会员升级礼" />

        <TextView
            android:id="@+id/main_tv_title_part_2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="#ff4C739E"
            android:textSize="14dp"
            android:maxLines="2"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:lineSpacingExtra="9.2dp"
            android:layout_marginTop="10dp"
            android:textFontWeight="400"
            android:layout_marginEnd="50dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/main_tv_title_part_1"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_title_part_1"
            tools:text="感谢你161天的陪伴，在这特殊的日子为你精心准备了一份“升级礼”：" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_cl_platinum_coupon"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/main_ll_expire_time_container">

            <ImageView
                android:id="@+id/main_sku_bg_img"
                android:scaleType="fitXY"
                app:layout_constraintDimensionRatio="303:159"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="0dp"
                android:layout_height="0dp" />

            <Space
                android:id="@+id/main_origin_price_space"
                android:layout_width="1px"
                android:layout_height="match_parent"
                app:layout_constraintHorizontal_bias="0.1155"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/main_tv_platinum_vip_origin_price_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.5"
                android:gravity="center"
                android:text="原价"
                android:textColor="#ff5E6076"
                android:textSize="13dp"
                app:layout_constraintEnd_toEndOf="@+id/main_origin_price_space"
                app:layout_constraintStart_toStartOf="@+id/main_origin_price_space"
                app:layout_constraintBottom_toBottomOf="@+id/main_right_tag_space"
                app:layout_constraintTop_toTopOf="@+id/main_right_tag_space" />

            <TextView
                android:id="@+id/main_tv_platinum_vip_origin_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="@+id/main_new_price_space"
                android:alpha="0.8"
                android:includeFontPadding="false"
                android:textColor="#ff5E6076"
                android:textSize="16dp"
                tools:text="¥15/月" />

            <Space
                android:id="@+id/main_new_price_space"
                android:layout_width="1px"
                android:layout_height="0dp"
                app:layout_constraintHorizontal_bias="0.397"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Space
                android:id="@+id/main_right_tag_space"
                android:layout_width="1px"
                android:layout_height="1px"
                app:layout_constraintVertical_bias="0.214"
                app:layout_constraintHorizontal_bias="0.858"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/main_tv_right_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="italic"
                app:layout_constraintBottom_toBottomOf="@+id/main_right_tag_space"
                app:layout_constraintTop_toTopOf="@+id/main_right_tag_space"
                app:layout_constraintStart_toStartOf="@+id/main_right_tag_space"
                app:layout_constraintEnd_toEndOf="@+id/main_right_tag_space"
                android:includeFontPadding="false"
                android:textColor="#ffffff"
                android:textSize="13dp"
                tools:text="818宠粉价" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main_new_price_layout"
                app:layout_constraintStart_toStartOf="@+id/main_new_price_space"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/main_tv_platinum_vip_dicount_price_symbol"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="¥"
                    android:textColor="#ff461717"
                    android:textFontWeight="500"
                    android:textSize="30dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/main_tv_platinum_vip_dicount_price"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/main_tv_platinum_vip_dicount_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:textColor="#ff461717"
                    android:textFontWeight="500"
                    android:textSize="42dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/main_tv_platinum_vip_dicount_price_symbol"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="20" />

                <TextView
                    android:id="@+id/main_tv_platinum_vip_dicount_price_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:includeFontPadding="false"
                    android:textColor="#ff461717"
                    android:textSize="16dp"
                    app:layout_constraintBaseline_toBaselineOf="@+id/main_tv_platinum_vip_dicount_price"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/main_tv_platinum_vip_dicount_price"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="/首2月" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/main_tv_platinum_vip_dicount_price_tip"
                app:layout_constraintStart_toStartOf="@+id/main_new_price_layout"
                app:layout_constraintEnd_toEndOf="@+id/main_new_price_layout"
                app:layout_constraintTop_toBottomOf="@+id/main_new_price_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="5dp"
                android:alpha="0.4"
                android:includeFontPadding="false"
                android:text="每个设备仅可参与1次"
                android:textColor="#ff444444"
                android:textFontWeight="500"
                android:textSize="10dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/main_ll_expire_time_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible"
            android:paddingHorizontal="30dp"
            android:paddingBottom="12dp"
            android:paddingTop="5dp"
            app:layout_constraintBottom_toTopOf="@+id/main_iv_action_bg"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_action_bg"
            app:layout_constraintStart_toStartOf="@+id/main_iv_action_bg">

            <com.ximalaya.ting.android.main.view.CountDownTimerView2
                android:id="@+id/main_view_expire_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible" />

            <TextView
                android:id="@+id/main_tv_expire_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="后失效"
                android:textColor="#ff2C2C3C"
                android:textSize="14sp"
                android:visibility="visible" />
        </LinearLayout>


        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/main_iv_action_bg"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:scaleType="fitXY"
            android:layout_marginLeft="32dp"
            android:layout_marginTop="12dp"
            android:layout_marginRight="32dp"
            android:layout_marginBottom="18dp"
            app:round="100dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/main_tv_action"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            android:text="立即锁定"
            android:textColor="@android:color/white"
            android:textFontWeight="700"
            android:textSize="17sp"
            app:layout_constraintBottom_toBottomOf="@+id/main_iv_action_bg"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_action_bg"
            app:layout_constraintStart_toStartOf="@+id/main_iv_action_bg"
            app:layout_constraintTop_toTopOf="@+id/main_iv_action_bg" />


        <FrameLayout
            android:id="@+id/main_fl_dialog_loading_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="invisible">

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_loading_view_progress_xmlottieview"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                app:lottie_fileName="lottie/host_loading/loading.json"
                app:lottie_imageAssetsFolder="lottie/host_loading"
                app:lottie_repeatCount="-1"
                app:lottie_repeatMode="restart" />
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>