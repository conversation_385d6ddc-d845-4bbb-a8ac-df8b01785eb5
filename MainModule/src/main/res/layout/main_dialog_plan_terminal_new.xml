<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                   xmlns:app="http://schemas.android.com/apk/res-auto"
                                                   xmlns:tools="http://schemas.android.com/tools"
                                                   android:background="@drawable/main_top10corner_f8f8f8"
                                                   android:paddingBottom="60dp"
                                                   android:layout_width="match_parent"
                                                   android:layout_height="wrap_content">

    <TextView
        android:id="@+id/main_tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:gravity="start|center_vertical"
        android:paddingStart="16dp"
        android:text="定时"
        android:textStyle="bold"
        android:fontFamily="sans-serif-light"
        android:textColor="@color/host_color_111111_ffffff"
        android:textSize="17sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/main_iv_close"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginEnd="11dp"
        android:padding="5dp"
        android:scaleType="fitXY"
        android:src="@drawable/main_ic_play_pod_close"
        android:tint="@color/host_color_8d8d91"
        app:layout_constraintBottom_toBottomOf="@id/main_tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_tv_title" />

    <View
        android:id="@+id/main_terminal_background"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/main_bg_rect_ffffff_1b1b1b_radius_6"
        app:layout_constraintTop_toTopOf="@+id/main_time_countdown_layout"
        app:layout_constraintBottom_toBottomOf="@+id/main_series_select_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_time_countdown_layout"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/main_tv_terminal_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingStart="16dp"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            android:maxLines="1"
            android:text="定时关闭"
            android:textColor="@color/host_color_111111_ffffff"
            android:textSize="17sp"
            android:includeFontPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <CheckBox
            android:id="@+id/main_terminal_box"
            android:layout_width="42dp"
            android:layout_height="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="16dp"
            android:background="@drawable/host_switch_selector_plan_terminal"
            android:button="@null"
            android:checked="false"
            android:clickable="true" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/main_terminal_divider"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@color/host_color_f0f0f0_282828"
        app:layout_constraintTop_toBottomOf="@+id/main_time_countdown_layout"
        android:layout_width="0dp"
        android:layout_height="1px"
    />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_time_select_layout"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:paddingTop="16dp"
        android:paddingBottom="20dp"
        app:layout_goneMarginTop="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_time_countdown_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/main_tv_select_time_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingStart="16dp"
            android:layout_marginEnd="10dp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="按时间"
            android:includeFontPadding="false"
            android:textColor="@color/host_color_111111_ffffff"
            android:textSize="13sp"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_select_time_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/main_tv_select_time_subtitle"
            android:layout_width="wrap_content"
            android:textColor="@color/host_color_acacaf_66666b"
            android:drawablePadding="4dp"
            android:includeFontPadding="false"
            android:drawableLeft="@drawable/main_terminal_picker_un_select"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="@+id/main_tv_select_time_title"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_select_time_title"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="16dp"
            android:text="播完整集声音再停止" />

        <TextView
            android:id="@+id/main_tv_select_time15"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_marginTop="16dp"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            android:layout_marginStart="16dp"
            android:textSize="12dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:background="@drawable/main_terminal_time_selector"
            android:gravity="center"
            android:textColor="@color/host_color_000000_ffffff"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_select_time30"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_select_time_title"
            app:layout_constraintStart_toStartOf="parent"
            android:text="15分" />

        <TextView
            android:id="@+id/main_tv_select_time30"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:textSize="12dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:background="@drawable/main_terminal_time_selector"
            android:gravity="center"
            android:textColor="@color/host_color_000000_ffffff"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_select_time60"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_select_time15"
            app:layout_constraintTop_toTopOf="@+id/main_tv_select_time15"
            app:layout_constraintStart_toEndOf="@+id/main_tv_select_time15"
            android:text="30分" />

        <TextView
            android:id="@+id/main_tv_select_time60"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:textSize="12dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:background="@drawable/main_terminal_time_selector"
            android:gravity="center"
            android:textColor="@color/host_color_000000_ffffff"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_select_time90"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_select_time15"
            app:layout_constraintTop_toTopOf="@+id/main_tv_select_time15"
            app:layout_constraintStart_toEndOf="@+id/main_tv_select_time30"
            android:text="60分" />

        <TextView
            android:id="@+id/main_tv_select_time90"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:textSize="12dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:background="@drawable/main_terminal_time_selector"
            android:gravity="center"
            android:textColor="@color/host_color_000000_ffffff"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_select_time_custom"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_select_time15"
            app:layout_constraintTop_toTopOf="@+id/main_tv_select_time15"
            app:layout_constraintStart_toEndOf="@+id/main_tv_select_time60"
            android:text="90分" />

        <TextView
            android:id="@+id/main_tv_select_time_custom"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_marginEnd="16dp"
            android:textSize="12dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:background="@drawable/main_terminal_time_selector"
            android:gravity="center"
            android:textColor="@color/host_color_000000_ffffff"
            app:layout_constraintStart_toEndOf="@+id/main_tv_select_time90"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_select_time15"
            app:layout_constraintTop_toTopOf="@+id/main_tv_select_time15"
            app:layout_constraintEnd_toEndOf="parent"
            android:text="自定义" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_series_select_layout"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:paddingBottom="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_time_select_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/main_tv_select_series_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingStart="16dp"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            android:maxLines="1"
            android:text="按集数"
            android:includeFontPadding="false"
            android:textColor="@color/host_color_111111_ffffff"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/main_tv_select_series1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constrainedWidth="true"
            android:padding="12dp"
            android:layout_marginTop="16dp"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            android:layout_marginStart="16dp"
            android:textSize="12dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:background="@drawable/main_terminal_series_selector"
            android:gravity="center"
            android:textColor="@color/host_color_000000_ffffff"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_select_series2"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_select_series_title"
            app:layout_constraintStart_toStartOf="parent"
            android:text="播完本集" />

        <TextView
            android:id="@+id/main_tv_select_series2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            android:padding="12dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:background="@drawable/main_terminal_series_selector"
            android:gravity="center"
            android:textColor="@color/host_color_000000_ffffff"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_select_series3"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_select_series1"
            app:layout_constraintTop_toTopOf="@+id/main_tv_select_series1"
            app:layout_constraintStart_toEndOf="@+id/main_tv_select_series1"
            android:text="播完2集" />

        <TextView
            android:id="@+id/main_tv_select_series3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12dp"
            android:padding="12dp"
            app:layout_constrainedWidth="true"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:background="@drawable/main_terminal_series_selector"
            android:gravity="center"
            android:textColor="@color/host_color_000000_ffffff"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_select_series5"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_select_series1"
            app:layout_constraintTop_toTopOf="@+id/main_tv_select_series1"
            app:layout_constraintStart_toEndOf="@+id/main_tv_select_series2"
            android:text="播完3集" />

        <TextView
            android:id="@+id/main_tv_select_series5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="12dp"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            android:layout_marginEnd="16dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:background="@drawable/main_terminal_series_selector"
            android:gravity="center"
            android:textColor="@color/host_color_000000_ffffff"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_select_series1"
            app:layout_constraintTop_toTopOf="@+id/main_tv_select_series1"
            app:layout_constraintStart_toEndOf="@+id/main_tv_select_series3"
            android:text="播完5集" />

    </androidx.constraintlayout.widget.ConstraintLayout>

<!--    <TextView-->
<!--        android:id="@+id/main_tv_open"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:textSize="12sp"-->
<!--        android:layout_marginBottom="32dp"-->
<!--        android:maxLines="1"-->
<!--        android:ellipsize="end"-->
<!--        android:visibility="invisible"-->
<!--        android:layout_marginTop="40dp"-->
<!--        android:includeFontPadding="false"-->
<!--        android:gravity="center"-->
<!--        android:textColor="@color/host_color_5a7ea7"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/main_series_select_layout"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        android:text="设置定时开启" />-->


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_open_layout"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/main_bg_rect_ffffff_1b1b1b_radius_6"
        app:layout_constraintTop_toBottomOf="@+id/main_series_select_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/main_tv_open"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingStart="16dp"
            android:ellipsize="end"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            android:maxLines="1"
            android:text="设置定时启播设置定时"
            android:textColor="@color/host_color_111111_ffffff"
            android:textSize="15sp"
            android:drawablePadding="4dp"
            android:drawableEnd="@drawable/main_ic_alarm_guide_new"
            android:includeFontPadding="false"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/main_terminal_arrow"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/main_tv_alarm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="2dp"
            android:maxLines="1"
            tools:text="每天每天每天每天每天每天每天每天每天每天每天每天每天每天每天每天 22:00"
            android:textColor="@color/host_color_acacaf_66666b"
            android:textSize="12sp"
            android:layout_marginStart="10dp"
            app:layout_constraintHorizontal_bias="1"
            android:includeFontPadding="false"
            app:layout_constrainedWidth="true"
            app:layout_constraintStart_toEndOf="@+id/main_tv_open"
            app:layout_constraintEnd_toStartOf="@+id/main_terminal_arrow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/main_terminal_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="16dp"
            app:tint="@color/main_color_acacaf_66666b"
            android:src="@drawable/main_ic_terminal_arrow" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>