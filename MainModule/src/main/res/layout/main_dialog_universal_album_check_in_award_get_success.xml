<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/main_corner5_solid_white">

    <ImageView
        android:id="@+id/main_dialog_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/host_dialog_close"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/main_success_image"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_marginTop="60dp"
        android:src="@drawable/main_ic_pay_success"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_success_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:text="领取成功"
        android:textColor="#ff000000"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_success_image" />

    <TextView
        android:id="@+id/main_success_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="30dp"
        android:gravity="center_horizontal"
        android:textColor="#ff333333"
        android:textSize="15sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_success_title"
        tools:text="可在账号-我的钱包-余额中查看" />

    <Button
        android:id="@+id/main_share_btn"
        android:layout_width="140dp"
        android:layout_height="40dp"
        android:layout_marginTop="50dp"
        android:layout_marginBottom="60dp"
        android:background="@drawable/main_bg_ff4444_20"
        android:text="炫耀下"
        android:textColor="#ffffffff"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_success_subtitle" />



</androidx.constraintlayout.widget.ConstraintLayout>