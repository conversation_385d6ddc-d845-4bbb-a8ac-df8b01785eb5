<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!--<include-->
    <!--android:id="@+id/main_top_bar"-->
    <!--layout="@layhost_titlebar_top_top" />-->

    <RelativeLayout
        android:id="@+id/main_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height" />

    <com.ximalaya.ting.android.framework.view.MultiDirectionSlidingDrawer
        android:id="@+id/main_pulldown_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/main_title_bar">

        <RelativeLayout
            android:id="@id/framework_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.ximalaya.ting.android.host.view.SegmentedGroup xmlns:segmentedgroup="http://schemas.android.com/apk/res-auto"
                android:id="@+id/main_segmentgroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/main_color_ffffff_121212"
                android:orientation="horizontal"
                android:padding="10dp"
                android:visibility="gone"
                segmentedgroup:sc_border_width="1dp"
                segmentedgroup:sc_corner_radius="5dp"
                segmentedgroup:sc_tint_color="@color/main_playerprogress_1">

                <RadioButton
                    android:id="@+id/main_radio_01"
                    style="@style/main_radio_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="最火" />

                <RadioButton
                    android:id="@+id/main_radio_02"
                    style="@style/main_radio_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="最新" />

            </com.ximalaya.ting.android.host.view.SegmentedGroup>

            <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
                android:id="@+id/main_listview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@id/main_segmentgroup"
                android:divider="@null"
                android:listSelector="@color/main_transparent"
                android:scrollbarStyle="outsideOverlay"
                android:focusableInTouchMode="false"
                android:focusable="false" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@id/framework_pulldown_widget"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <GridView
                android:id="@+id/main_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/main_horizontal_divider"
                android:background="@color/main_color_ffffff_121212"
                android:numColumns="4"
                android:paddingBottom="15dp"
                android:paddingTop="15dp" />

            <View
                android:id="@+id/main_cancelLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/main_panel"
                android:background="@color/main_color_7fffffff_7f000000"
                android:clickable="true" />
        </RelativeLayout>
    </com.ximalaya.ting.android.framework.view.MultiDirectionSlidingDrawer>

</merge>