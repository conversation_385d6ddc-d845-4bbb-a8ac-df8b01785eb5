<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/main_root_vip"
    >

    <LinearLayout
        android:background="@drawable/main_bg_rect_ffffff_282828_top_radius_8"
        android:layout_alignParentBottom="true"
        android:paddingHorizontal="12dp"
        android:paddingBottom="30dp"
        android:id="@+id/main_ll_vip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:layout_alignTop="@id/main_ll_vip"
            android:layout_gravity="right"
            android:layout_marginTop="12dp"
            android:id="@+id/main_iv_close_vip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/host_ic_x_close_n_line_regular_20" />

        <ImageView
            android:layout_gravity="center_horizontal"
            android:id="@+id/main_iv_vip"
            android:layout_marginTop="3dp"
            android:layout_width="200dp"
            android:layout_height="132dp"/>

        <LinearLayout
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:layout_marginTop="20dp"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:text="含"
                android:textSize="16sp"
                android:textColor="@color/host_color_131313_ffffff"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:text="12"
                android:textSize="16sp"
                android:textColor="@color/host_color_ff4444"
                android:id="@+id/main_tv_vip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:text="集付费声音，开通会员可全部下载"
                android:textSize="16sp"
                android:textColor="@color/host_color_131313_ffffff"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>


        <TextView
            android:id="@+id/main_tv_confirm_vip"
            android:layout_width="220dp"
            android:layout_height="44dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="24dp"
            android:background="@drawable/main_bg_rect_fff09068_raduis_22"
            android:gravity="center"
            android:text="开通会员"
            android:textColor="#ffffff"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:text="仅下载8集免费声音"
            android:textSize="16sp"
            android:textColor="@color/host_color_666666_8d8d91"
            android:id="@+id/main_tv_dsp_vip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </LinearLayout>


</RelativeLayout>