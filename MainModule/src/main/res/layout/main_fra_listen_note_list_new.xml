<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:rll="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_ll_fra_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="false"
    android:background="@color/host_color_ffffff_131313"
    android:orientation="vertical">
    <!--注意main_ll_fra_content父view在MySubscribeListFragment设置了padding
        如果要设置padding，注意代码中的逻辑
        -->
    <FrameLayout
        android:id="@+id/main_listenNoteList_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/main_gray_underline_white_bg"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/main_ll_subscribe_container"
        android:layout_width="match_parent"
        android:focusable="false"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.ximalaya.ting.android.host.view.PullToRefreshStickyLayout
            android:id="@+id/main_snl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:id="@+id/host_id_stickynavlayout_topview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/main_fl_follow_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:layout_height="100dp"
                    tools:background="@color/host_red"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:orientation="vertical" />

                <LinearLayout
                        android:id="@+id/main_game_center_and_ebook_container"
                        android:layout_width="match_parent"
                        android:layout_height="80dp"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:orientation="horizontal">

<!--                    <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                            android:id="@+id/main_game_center_cl"-->
<!--                            android:layout_width="0dp"-->
<!--                            android:layout_weight="1"-->
<!--                            android:background="@drawable/main_radius_8_fff9e7_fff9e7_20"-->
<!--                            android:layout_marginLeft="@dimen/host_y16"-->
<!--                            android:layout_marginTop="16dp"-->
<!--                            android:layout_height="wrap_content">-->
<!--                        &lt;!&ndash; 游戏大厅 &ndash;&gt;-->
<!--                        <TextView-->
<!--                                android:id="@+id/main_game_tv"-->
<!--                                android:layout_width="wrap_content"-->
<!--                                android:layout_height="wrap_content"-->
<!--                                android:textColor="@color/host_color_6f5922_f7eed7"-->
<!--                                android:text="游戏大厅"-->
<!--                                android:lines="1"-->
<!--                                android:ellipsize="end"-->
<!--                                app:layout_constraintVertical_chainStyle="packed"-->
<!--                                app:layout_constraintBottom_toTopOf="@+id/main_game_desc_tv"-->
<!--                                app:layout_constraintTop_toTopOf="parent"-->
<!--                                app:layout_constraintLeft_toLeftOf="parent"-->
<!--                                android:layout_marginLeft="12dp"-->
<!--                                android:textSize="15sp" />-->

<!--                        <TextView-->
<!--                                android:id="@+id/main_game_desc_tv"-->
<!--                                android:layout_width="0dp"-->
<!--                                android:layout_height="wrap_content"-->
<!--                                android:textColor="@color/host_color_6f5922_f7eed7"-->
<!--                                android:text="边听边玩更快乐"-->
<!--                                android:lines="1"-->
<!--                                android:ellipsize="end"-->
<!--                                android:layout_marginTop="2dp"-->
<!--                                app:layout_constraintVertical_chainStyle="packed"-->
<!--                                app:layout_constraintTop_toBottomOf="@+id/main_game_tv"-->
<!--                                app:layout_constraintLeft_toLeftOf="parent"-->
<!--                                app:layout_constraintBottom_toBottomOf="parent"-->
<!--                                app:layout_constraintRight_toLeftOf="@+id/main_game_center_iv"-->
<!--                                android:layout_marginLeft="12dp"-->
<!--                                android:textSize="12sp" />-->

<!--                        <ImageView-->
<!--                                android:id="@+id/main_game_center_iv"-->
<!--                                android:layout_width="48dp"-->
<!--                                android:layout_height="42dp"-->
<!--                                android:layout_marginTop="11dp"-->
<!--                                android:layout_marginRight="10dp"-->
<!--                                android:layout_marginBottom="11dp"-->
<!--                                app:layout_constraintRight_toRightOf="parent"-->
<!--                                app:layout_constraintTop_toTopOf="parent"-->
<!--                                android:src="@drawable/main_game_center_iv"-->
<!--                                app:layout_constraintBottom_toBottomOf="parent" />-->

<!--                    </androidx.constraintlayout.widget.ConstraintLayout>-->

                    <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/main_ebook_cl"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_marginLeft="11dp"
                            android:layout_marginRight="@dimen/host_y16"
                            android:layout_marginTop="16dp"
                            android:background="@drawable/main_radius_8_eff8ff_eff8ff_20"
                            android:layout_height="wrap_content">
                        <!-- 电子书 -->
                        <TextView
                                android:id="@+id/main_ebook_tv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/host_color_6f5922_f7eed7"
                                android:text="小说专区"
                                android:lines="1"
                                android:ellipsize="end"
                                app:layout_constraintVertical_chainStyle="packed"
                                app:layout_constraintBottom_toTopOf="@+id/main_ebook_desc_tv"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintLeft_toLeftOf="parent"
                                android:layout_marginLeft="12dp"
                                android:textSize="15sp" />

                        <TextView
                                android:id="@+id/main_ebook_desc_tv"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:textColor="@color/host_color_6f5922_f7eed7"
                                android:text="海量小说免费看"
                                android:lines="1"
                                android:ellipsize="end"
                                app:layout_constraintVertical_chainStyle="packed"
                                app:layout_constraintBottom_toBottomOf="parent"
                                android:layout_marginTop="2dp"
                                app:layout_constraintTop_toBottomOf="@+id/main_ebook_tv"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintRight_toLeftOf="@+id/main_ebook_iv"
                                android:layout_marginLeft="12dp"
                                android:textSize="12sp" />

                        <ImageView
                                android:id="@+id/main_ebook_iv"
                                android:layout_width="48dp"
                                android:layout_height="42dp"
                                android:layout_marginTop="11dp"
                                android:layout_marginRight="10dp"
                                android:layout_marginBottom="11dp"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                android:src="@drawable/main_ebook_iv"
                                app:layout_constraintBottom_toBottomOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/main_cl_member"
                    android:layout_below="@+id/main_game_center_and_ebook_container"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_marginLeft="@dimen/host_y16"
                    android:layout_marginTop="@dimen/host_y14"
                    android:layout_marginRight="@dimen/host_y16"
                    android:layout_marginBottom="@dimen/host_y16"
                    android:background="@drawable/host_bg_rect_fff7f2_dark_fill2_radius_8"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <!-- 订阅量马上要到600上限了 -->
                    <TextView
                        android:id="@+id/main_tv_member_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginTop="9dp"
                        android:layout_marginRight="16dp"
                        android:ellipsize="end"
                        android:includeFontPadding="false"
                        android:maxLines="1"
                        android:text="订阅量马上要到600上限了"
                        android:textColor="#FF732F06"
                        android:textSize="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- 开通VIp 享受订阅量2000张特权 -->
                    <TextView
                        android:id="@+id/main_tv_member_subtitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginTop="6dp"
                        android:includeFontPadding="false"
                        android:text="开通VIp 享受订阅量2000张特权"
                        android:textColor="#99732F06"
                        android:textSize="11dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/main_tv_member_title" />

                    <TextView
                        android:id="@+id/main_tv_guide_vip_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:layout_marginRight="24dp"
                        android:background="@drawable/host_bg_ff4444_radius_22"
                        android:gravity="center"
                        android:paddingLeft="8dp"
                        android:paddingRight="8dp"
                        android:text="立即开通"
                        android:textColor="@color/main_color_ffffff"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/main_iv_member_close"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginStart="12dp"
                        android:padding="4dp"
                        android:src="@drawable/host_ic_close"
                        android:tint="@color/host_color_732f06_666666"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="UseAppTint" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

            <FrameLayout
                android:id="@+id/host_id_stickynavlayout_indicator"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/main_mysubscribe_has_subscribe_item_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/host_y4"
                    android:paddingBottom="@dimen/host_y6"
                    android:visibility="gone"
                    tools:visibility="visible">


                    <LinearLayout
                        android:id="@+id/main_subscribe_filter_head_ll"
                        android:layout_width="match_parent"
                        android:layout_height="24dp"
                        android:layout_marginTop="@dimen/host_y12"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingStart="@dimen/host_default_side_margin"
                        android:paddingEnd="@dimen/host_default_side_margin">

                        <TextView
                            android:id="@+id/main_subscribe_rb_default"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/main_bg_f6f7f8_1e1e1e_radius_15"
                            android:button="@null"
                            android:checked="true"
                            android:contentDescription="默认"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:minHeight="24dp"
                            android:text="默认"
                            android:textColor="@color/main_color_ff4c2e_666666_selector"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/main_subscribe_rb_recent_update"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/main_bg_f6f7f8_1e1e1e_radius_15"
                            android:button="@null"
                            android:checked="true"
                            android:contentDescription="最新更新"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:minHeight="24dp"
                            android:text="最新更新"
                            android:textColor="@color/main_color_ff4c2e_666666_selector"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/main_subscribe_rb_recent_subscribe"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/main_bg_f6f7f8_1e1e1e_radius_15"
                            android:button="@null"
                            android:checked="true"
                            android:contentDescription="最新订阅"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:minHeight="24dp"
                            android:text="最新订阅"
                            android:textColor="@color/main_color_ff4c2e_666666_selector"
                            android:textSize="13sp" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <ImageView
                            android:id="@+id/main_subscribe_iv_filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:contentDescription="筛选"
                            android:src="@drawable/host_filter_default_20"
                            android:tint="@color/host_subscribe_filter_color"
                            tools:ignore="UseAppTint" />

                        <ImageView
                            android:id="@+id/main_subscribe_iv_more"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="end"
                            android:contentDescription="更多"
                            android:layout_marginStart="16dp"
                            android:src="@drawable/host_ic_more_album2_new"
                            android:tint="@color/host_color_666666_8d8d91" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="24dp"
                        android:layout_marginTop="12dp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="16dp"
                            android:text="节目"
                            android:textColor="@color/host_color_666666_888888"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/main_mysubscribe_subscribe_count_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="4dp"
                            android:fontFamily="sans-serif-light"
                            android:textColor="@color/host_color_333333_dcdcdc"
                            android:textSize="13sp"
                            android:textStyle="bold"
                            tools:text="12" />

                        <Space
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                        <ImageView
                            android:id="@+id/main_mysubscribe_subscribe_switch_grid_iv"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="12dp"
                            android:contentDescription="订阅样式切换"
                            android:scaleType="center"
                            android:src="@drawable/main_subscribe_switch_list_new"
                            android:visibility="gone"
                            app:tint="@color/host_color_666666_888888"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/main_mysubscribe_subscribe_sort_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="12dp"
                            android:drawableLeft="@drawable/main_ic_subscribe_category_filter_new"
                            android:drawableTint="@color/host_color_666666_888888"
                            android:gravity="center_vertical"
                            android:text="筛选"
                            android:textColor="@color/host_color_666666_888888"
                            android:textSize="12sp"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <FrameLayout
                        android:id="@+id/main_mysubscribe_sort_container_fl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </FrameLayout>

            <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
                android:id="@+id/host_id_stickynavlayout_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:divider="@null"
                android:dividerHeight="0dp"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:listSelector="@color/host_transparent"
                android:overScrollMode="always"
                android:paddingBottom="150dp"
                android:scrollbars="none"
                rll:ptrDrawable="@drawable/host_ic_loading_circle"
                rll:ptrHeaderTextColor="@color/main_text_medium"
                rll:ptrShowIndicator="false" />

        </com.ximalaya.ting.android.host.view.PullToRefreshStickyLayout>

    </LinearLayout>

    <include
        android:id="@+id/main_chase_popup_view"
        layout="@layout/main_layout_chase_subscribe_popup"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/main_fl_subscribe_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        android:layout_marginBottom="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>