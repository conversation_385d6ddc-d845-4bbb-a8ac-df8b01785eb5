<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:psts="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@color/main_color_ffffff_121212">

    <FrameLayout
        android:id="@+id/main_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/main_gray_underline_white_bg" />

    <com.astuetz.PagerSlidingTabStrip
        android:id="@+id/main_tab"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        style="@style/host_my_pager_sliding_tab_strip_style"
        android:background="@color/main_color_ffffff_121212"
        psts:pstsActivateTextColor="@color/main_color_black"
        psts:pstsDeactivateTextColor="@color/host_color_999999_888888"
        psts:pstsTabPaddingLeftRight="10dp"
        psts:pstsShouldExpand="true" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/main_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>