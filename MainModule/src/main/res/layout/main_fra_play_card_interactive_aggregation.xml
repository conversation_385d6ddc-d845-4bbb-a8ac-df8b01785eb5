<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black">


    <com.ximalaya.ting.android.main.playpage.view.InteractiveBackgroundView
        android:id="@+id/main_v_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <View
        android:id="@+id/main_view_v"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_marginLeft="32dp"
        android:background="@color/main_white_alpha_10"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_title_bar"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/main_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:overScrollMode="never"
        android:paddingBottom="@dimen/host_bottom_bar_height"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_title_bar"
        tools:listitem="@layout/main_item_interactive_card_aggregation" />

    <ImageView
        android:id="@+id/main_iv_publish"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="24dp"
        android:layout_marginBottom="87dp"
        android:src="@drawable/main_play_page_icon_card_publish_door"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <FrameLayout
        android:id="@+id/main_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>