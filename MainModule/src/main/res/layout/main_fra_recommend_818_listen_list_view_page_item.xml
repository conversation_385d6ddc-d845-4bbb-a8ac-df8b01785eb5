<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <!--clipChildren="false"  需要  不然右边更多的图标显示被切-->
    <FrameLayout
        android:id="@+id/main_fl_module_title_flag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_module_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/main_tv_module_title">

        <ImageView
            android:id="@+id/main_iv_module_title_flag"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            tools:layout_height="40dp"
            tools:layout_width="20dp" />

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/main_lottie_module_title_flag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-1dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true" />

    </FrameLayout>

    <TextView
        android:id="@+id/main_tv_module_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="20dp"
        android:ellipsize="end"
        android:fontFamily="sans-serif-light"
        android:gravity="center_vertical"
        android:importantForAccessibility="yes"
        android:maxLines="1"
        android:textColor="@color/main_color_module_title"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@+id/main_fl_module_title_flag"
        app:layout_constraintRight_toLeftOf="@+id/main_rl_more"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_max="wrap"
        tools:text="人生松绑建议人生松绑建议人生松绑建议人生松绑建议" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_rl_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_module_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/main_tv_module_title">

        <ImageView
            android:id="@+id/main_iv_more_bg"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            android:src="@drawable/main_bg_recommend_818_listen_list"
            app:layout_constraintBottom_toBottomOf="@+id/main_view_holder"
            app:layout_constraintLeft_toLeftOf="@+id/main_view_holder"
            app:layout_constraintRight_toRightOf="@+id/main_tv_more" />

        <View
            android:id="@+id/main_view_holder"
            android:layout_width="4dp"
            android:layout_height="4dp"
            app:layout_constraintRight_toLeftOf="@+id/main_tv_more"
            app:layout_constraintTop_toBottomOf="parent" />

        <TextView
            android:id="@+id/main_tv_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:drawableEnd="@drawable/main_recommend_header_right_more_818"
            android:drawableTint="#2D92DE"
            android:gravity="center_vertical"
            android:importantForAccessibility="yes"
            android:text="查看专属赠礼"
            android:textColor="#2D92DE"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:clipChildren="false"
        android:importantForAccessibility="no"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_tv_module_title">

        <View
            android:id="@+id/main_view_bg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/main_bg_recommend_818_listen_list_shape"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.ximalaya.ting.android.host.view.CornerRelativeLayout
            android:id="@+id/main_crl_lottie_parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:corner="left_top|right_top"
            app:corner_radius="8dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_xm_lottie_fg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="-1dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:lottie_autoPlay="true"
                app:lottie_loop="true" />

        </com.ximalaya.ting.android.host.view.CornerRelativeLayout>


        <com.ximalaya.ting.android.main.adapter.find.view.RecommendGradientTextView
            android:id="@+id/main_tv_module_sub_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="30dp"
            android:ellipsize="end"
            android:fontFamily="sans-serif-light"
            android:gravity="center_vertical"
            android:importantForAccessibility="yes"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:textColor="#1870B2"
            android:textSize="17sp"
            android:textStyle="bold"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/main_iv_sub_more"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="以三千世界为炉，炼故事为药；\n每段过往，都是良方，听宇宙回响" />

        <ImageView
            android:id="@+id/main_iv_sub_more"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="16dp"
            android:contentDescription="更多"
            android:importantForAccessibility="yes"
            android:src="@drawable/main_ic_home_item_more"
            app:layout_constraintLeft_toRightOf="@+id/main_tv_module_sub_title"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main_tv_module_sub_title" />

        <com.ximalaya.ting.android.main.view.recommend.FullHeightRecyclerView
            android:id="@+id/main_rcv_album_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="8dp"
            android:clipChildren="false"
            android:overScrollMode="never"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_module_sub_title" />

        <TextView
            android:id="@+id/main_tv_hint"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:text="1"
            android:textColor="#44f00000"
            android:textSize="30dp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/main_rcv_album_list"
            app:layout_constraintLeft_toLeftOf="@+id/main_rcv_album_list"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main_rcv_album_list" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>