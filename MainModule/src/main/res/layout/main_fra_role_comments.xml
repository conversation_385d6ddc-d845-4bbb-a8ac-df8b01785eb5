<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ximalaya.ting.android.main.roleModule.view.RoleCommentBackgroundView
        android:id="@+id/main_v_comment_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="12dp" />

    <com.ximalaya.ting.android.main.roleModule.view.NoInterceptCoordinatorLayout
        android:id="@+id/main_coordinator_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="12dp">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/main_app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/main_transparent"
            app:elevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_scrollFlags="scroll">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/main_layout_album"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/main_bg_rect_ffffff_1b1b1b_radius_8">

                    <TextView
                        android:id="@+id/main_tv_current_album"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginTop="16dp"
                        android:fontFamily="sans-serif-light"
                        android:textStyle="bold"
                        android:textSize="15sp"
                        android:textColor="@color/host_color_titleColor"
                        android:text="当前专辑"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/main_tv_album_rank"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:paddingHorizontal="4dp"
                        android:paddingVertical="1dp"
                        android:includeFontPadding="false"
                        android:textSize="10sp"
                        android:textColor="@color/host_color_yello1"
                        tools:text="范闲 相关专辑排名NO.2"
                        android:drawableRight="@drawable/host_ic_jump_n_n_line_regular_12"
                        android:drawableTint="@color/host_color_yello1"
                        android:background="@drawable/main_bg_ff7c3a_alpha_50_corner_2_stroke"
                        app:layout_constraintLeft_toRightOf="@id/main_tv_current_album"
                        app:layout_constraintTop_toTopOf="@id/main_tv_current_album"
                        app:layout_constraintBottom_toBottomOf="@id/main_tv_current_album"
                        android:visibility="gone" />

                    <com.ximalaya.ting.android.framework.view.image.RoundImageView
                        android:id="@+id/main_iv_album_cover"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="20dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/host_default_album"
                        app:corner_radius="4dp"
                        app:layout_constraintLeft_toLeftOf="@id/main_tv_current_album"
                        app:layout_constraintTop_toBottomOf="@id/main_tv_current_album"
                        app:layout_constraintBottom_toBottomOf="parent" />

                    <TextView
                        android:id="@+id/main_tv_album_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="12dp"
                        android:layout_marginRight="4dp"
                        android:includeFontPadding="false"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:fontFamily="sans-serif-light"
                        android:textStyle="bold"
                        android:textColor="@color/host_color_listTitleColor"
                        android:textSize="16sp"
                        app:layout_constraintLeft_toRightOf="@id/main_iv_album_cover"
                        app:layout_constraintRight_toLeftOf="@+id/main_layout_subscribe"
                        app:layout_constraintTop_toTopOf="@id/main_iv_album_cover" />

                    <TextView
                        android:id="@+id/main_tv_play_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textSize="11sp"
                        android:textColor="@color/host_color_lightTextColor"
                        android:text="0"
                        tools:text="34.7万"
                        android:drawableTint="@color/host_color_lightTextColor"
                        android:drawableLeft="@drawable/host_ic_play_amount_n_line_regular_12"
                        android:drawablePadding="0.5dp"
                        app:layout_constraintLeft_toLeftOf="@id/main_tv_album_title"
                        app:layout_constraintTop_toBottomOf="@id/main_tv_album_title" />

                    <com.ximalaya.ting.android.framework.view.image.RoundImageView
                        android:id="@+id/main_iv_role_avatar"
                        android:layout_width="17dp"
                        android:layout_height="17dp"
                        android:layout_marginTop="8dp"
                        android:scaleType="centerCrop"
                        app:corner_radius="10dp"
                        app:layout_constraintLeft_toLeftOf="@id/main_tv_album_title"
                        app:layout_constraintBottom_toBottomOf="@id/main_iv_album_cover" />

                    <TextView
                        android:id="@+id/main_tv_role_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="4dp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textSize="11sp"
                        android:textColor="@color/main_color_999999_66666b"
                        tools:text="范闲"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintWidth_default="wrap"
                        app:layout_constraintLeft_toRightOf="@id/main_iv_role_avatar"
                        app:layout_constraintRight_toLeftOf="@+id/main_tv_love_point"
                        app:layout_constraintTop_toTopOf="@id/main_iv_role_avatar"
                        app:layout_constraintBottom_toBottomOf="@id/main_iv_role_avatar" />

                    <TextView
                        android:id="@+id/main_tv_love_point"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="12dp"
                        android:layout_marginRight="4dp"
                        android:textSize="11sp"
                        android:textColor="@color/main_color_999999_66666b"
                        tools:text="喜爱值200"
                        app:layout_constraintLeft_toRightOf="@id/main_tv_role_name"
                        app:layout_constraintRight_toLeftOf="@+id/main_layout_subscribe"
                        app:layout_constraintTop_toTopOf="@id/main_iv_role_avatar"
                        app:layout_constraintBottom_toBottomOf="@id/main_iv_role_avatar" />

                    <LinearLayout
                        android:id="@+id/main_layout_subscribe"
                        android:layout_width="58dp"
                        android:layout_height="24dp"
                        android:layout_marginRight="16dp"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/host_bg_ff4444_radius_22_stroke"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@id/main_iv_album_cover"
                        app:layout_constraintBottom_toBottomOf="@id/main_iv_album_cover">

                        <ImageView
                            android:id="@+id/main_iv_subscribe"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/host_ic_create_btn_n_line_regular_12" />

                        <TextView
                            android:id="@+id/main_tv_subscribe"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textSize="12sp"
                            android:textColor="@color/host_color_xmRed"
                            android:text="订阅" />

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/main_layout_tasks"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:paddingHorizontal="16dp"
                    android:background="@drawable/main_bg_rect_ffffff_1b1b1b_radius_8">

                    <TextView
                        android:id="@+id/main_tv_task_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="sans-serif-light"
                        android:textStyle="bold"
                        android:textSize="15sp"
                        android:textColor="@color/host_color_titleColor"
                        android:text="做任务领喜爱值"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/main_iv_qa"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="4dp"
                        android:src="@drawable/host_ic_rule_n_n_line_regular_16"
                        android:tint="@color/main_color_aaaaaa_66666b"
                        app:layout_constraintLeft_toRightOf="@id/main_tv_task_title"
                        app:layout_constraintTop_toTopOf="@id/main_tv_task_title"
                        app:layout_constraintBottom_toBottomOf="@id/main_tv_task_title" />

                    <TextView
                        android:id="@+id/main_tv_rest_love_point"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="@color/host_color_yello1"
                        tools:text="本专辑今日可投喜爱值 ×1"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@id/main_tv_task_title"
                        app:layout_constraintBottom_toBottomOf="@id/main_tv_task_title" />

                    <include
                        android:id="@+id/main_layout_task_1"
                        layout="@layout/main_item_love_point_task"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        app:layout_constraintTop_toBottomOf="@id/main_tv_task_title"/>

                    <include
                        android:id="@+id/main_layout_task_2"
                        layout="@layout/main_item_love_point_task"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="28dp"
                        app:layout_constraintTop_toBottomOf="@id/main_layout_task_1"/>

                    <TextView
                        android:id="@+id/main_tv_more_tasks"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:paddingVertical="16dp"
                        android:includeFontPadding="false"
                        android:textSize="13sp"
                        android:textColor="@color/host_color_mediumTextColor"
                        android:text="更多任务"
                        android:drawablePadding="1dp"
                        android:drawableRight="@drawable/host_ic_jump_n_n_line_regular_12"
                        android:drawableTint="@color/host_color_mediumTextColor"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/main_layout_task_2"
                        app:layout_constraintBottom_toBottomOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main_layout_comment_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/main_tv_title_comment_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="16dp"
                    android:fontFamily="sans-serif-light"
                    android:textStyle="bold"
                    android:textColor="@color/main_color_111111_ffffff"
                    android:textSize="15sp"
                    android:text="评论区"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/main_tv_quora_input"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="14dp"
                    android:layout_marginBottom="12dp"
                    android:paddingHorizontal="12dp"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="@color/host_color_textLinkColor"
                    android:textSize="12sp"
                    android:text="说说TA的故事吧"
                    android:drawableLeft="@drawable/host_ic_write_n_n_line_regular_16"
                    android:drawablePadding="4dp"
                    android:drawableTint="@color/host_color_textColor"
                    android:background="@drawable/main_bg_rect_f8f8fa_282828_radius_18"
                    android:contentDescription="点击评论"
                    app:layout_constraintTop_toBottomOf="@id/main_tv_title_comment_count"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <com.ximalaya.ting.android.main.view.comment.CommentRecyclerView
            android:id="@+id/main_comment_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="24dp"
            android:clipToPadding="false"
            app:layout_constraintTop_toBottomOf="@id/main_tv_quora_input"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    </com.ximalaya.ting.android.main.roleModule.view.NoInterceptCoordinatorLayout>

</FrameLayout>