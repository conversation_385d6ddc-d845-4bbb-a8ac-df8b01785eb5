<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="20dp"
    tools:background="@color/main_gray">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp">

        <android.widget.TextView
            android:id="@+id/main_TextViewTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:lines="1"
            android:textColor="@color/main_color_ffffff_alpha_70"
            android:textSize="13sp"
            tools:text="订阅此专辑的人还订阅了" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/main_ImageViewClose"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="6dp"
            android:src="@drawable/main_recommend_close" />

    </RelativeLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp">

        <com.ximalaya.ting.android.main.view.RecyclerViewCanDisallowIntercept
            android:id="@+id/main_RecyclerViewSubscribeRecommend"
            android:layout_width="match_parent"
            android:layout_height="180dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:scrollbars="none"
            android:visibility="invisible" />

        <android.widget.TextView
            android:id="@+id/main_TextViewNotification"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/host_loading"
            android:textColor="@color/main_white" />

    </FrameLayout>

</LinearLayout>