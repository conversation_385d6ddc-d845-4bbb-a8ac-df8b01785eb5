<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/main_iv_top_bg_img"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/main_bg_vip_payment_top" />

    <LinearLayout
        android:id="@+id/main_ll_title_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/host_default_side_margin"
        android:paddingTop="16dp"
        android:paddingBottom="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/main_vip_sub_title_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:alpha="0.4"
            android:gravity="left|center_vertical"
            android:text="享10w+专辑免费听、免音贴广告等特权"
            android:textColor="@color/host_color_333333_dcdcdc"
            android:textSize="12dp" />

        <TextView
            android:id="@+id/main_vip_more_menu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0.4"
            android:drawableRight="@drawable/main_ic_more_arrow_right"
            android:drawablePadding="4dp"
            android:drawableTint="@color/host_color_333333_dcdcdc"
            android:gravity="center"
            android:textColor="@color/host_color_333333_dcdcdc"
            android:textSize="12dp"
            android:visibility="gone"
            tools:text="更多套餐"
            tools:visibility="visible" />
    </LinearLayout>

    <com.ximalaya.ting.android.host.view.OnEdgeListenerNestedScrollView
        android:id="@+id/main_play_page_dialog_scroll_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:focusable="true"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_ll_title_container">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.ximalaya.ting.android.host.manager.share.IdleHorizontalScrollView
                android:id="@+id/main_hs_sku_scroll_container"
                android:layout_width="match_parent"
                android:layout_gravity="center"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:minHeight="100dp"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:scrollbars="none">

                <LinearLayout
                    android:id="@+id/main_ll_sku_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="bottom"
                    android:orientation="horizontal" />
            </com.ximalaya.ting.android.host.manager.share.IdleHorizontalScrollView>

            <androidx.constraintlayout.utils.widget.ImageFilterView
                android:id="@+id/main_iv_product_page_banner"
                 android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:scaleType="fitXY"
                android:layout_marginTop="12dp"
                app:round="4dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/main_tv_sku_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left|center_vertical"
                android:drawableRight="@drawable/main_ic_rule_info_n_n_line_regular_16"
                android:drawablePadding="3dp"
                android:drawableTint="@color/host_color_aaaaaa_66666b"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/host_default_side_margin"
                android:paddingTop="12dp"
                android:textColor="@color/host_color_aaaaaa_66666b"
                android:textSize="12dp"
                tools:text="sdgsdagasdgsdgsdgsdgdsgsdgsdgsdgsdgsd" />

            <LinearLayout
                android:id="@+id/main_ll_extra_behavior"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="4dp"
                android:layout_marginHorizontal="@dimen/host_x12"
                android:orientation="vertical" />
        </LinearLayout>

    </com.ximalaya.ting.android.host.view.OnEdgeListenerNestedScrollView>

    <FrameLayout
        android:id="@+id/main_fl_bottom_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>

