<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/main_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/main_gray_underline_white_bg" />

    <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView xmlns:ptr="http://schemas.android.com/apk/res-auto"
        android:id="@+id/main_listView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:divider="@null"
        android:dividerHeight="0dp"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:overScrollMode="always"
        android:scrollbars="none"
        android:background="@color/main_color_ffffff_121212"
        ptr:ptrDrawable="@drawable/host_ic_loading_circle"
        ptr:ptrHeaderTextColor="@color/main_text_medium"
        ptr:ptrShowIndicator="false" />

</LinearLayout>