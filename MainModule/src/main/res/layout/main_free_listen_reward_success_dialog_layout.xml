<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_dialog_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#b3000000">

    <View
        android:id="@+id/main_top_close_area"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/main_bottom_layout" />

    <com.ximalaya.ting.android.host.view.CornerRelativeLayout
        android:id="@+id/main_bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        app:corner="left_top|right_top"
        app:corner_radius="12dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/main_color_ffffff_282828"
            android:paddingBottom="30dp">

            <View
                android:id="@+id/main_top_blur_bg"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/main_gradient_339cffdc_00eafff8" />

            <ImageView
                android:id="@+id/main_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginTop="12dp"
                android:layout_marginRight="12dp"
                android:scaleType="centerInside"
                android:src="@drawable/main_dialog_close_btn" />

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_flower_lottie"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="64dp"
                app:lottie_loop="false"
                android:layout_width="210dp"
                android:layout_height="52dp"
                app:lottie_autoPlay="false"
                app:lottie_fileName="lottie/main_reward_flower_lottie.json" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:layout_marginTop="35dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="恭喜你获得"
                    android:textColor="@color/host_color_131313_ffffff"
                    android:textSize="20sp"
                    android:textFontWeight="800" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/main_reward_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="60"
                        android:textSize="34sp"
                        android:textFontWeight="1000"
                        android:textColor="#ff0DD09F" />

                    <TextView
                        android:id="@+id/main_reward_time_unit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:layout_gravity="center_vertical"
                        android:text="分钟"
                        android:textSize="30sp"
                        android:textFontWeight="1000"
                        android:textColor="@color/host_color_131313_ffffff"/>
                </LinearLayout>

                <TextView
                    android:id="@+id/main_close_bottom_button"
                    android:layout_width="199dp"
                    android:layout_height="44dp"
                    android:layout_marginTop="24dp"
                    android:background="@drawable/main_22_corner_ff0dd09f"
                    android:gravity="center"
                    android:text="畅享免费听"
                    android:textColor="#ffFFFFFF"
                    android:textFontWeight="600"
                    android:textSize="16dp" />

                <LinearLayout
                    android:id="@+id/main_other_method"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/main_watch_video_button"
                    android:layout_centerHorizontal="true"
                    android:orientation="vertical"
                    android:gravity="center_horizontal">
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>
    </com.ximalaya.ting.android.host.view.CornerRelativeLayout>
</RelativeLayout>