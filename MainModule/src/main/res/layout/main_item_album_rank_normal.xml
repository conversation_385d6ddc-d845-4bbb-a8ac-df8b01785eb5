<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_list_selector"
    android:descendantFocusability="blocksDescendants"
    android:paddingTop="15dp">

    <!--排行数字-->
    <TextView style="@style/main_album_item_rank_num" />

    <androidx.cardview.widget.CardView
        android:id="@+id/main_cover_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:layout_marginLeft="15dp"
        android:layout_toRightOf="@+id/main_tv_album_rank_num"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp">

        <!--专辑图-->
        <ImageView
            style="@style/main_album_item_cover"
            android:layout_marginBottom="0dp"
            android:layout_marginLeft="0dp"
            android:contentDescription="@string/main_content_description_album_default"
            />

        <!--付费角标-->
        <ImageView style="@style/main_album_item_pay_cover_tag_grid_style"
            />

    </androidx.cardview.widget.CardView>


    <!--专辑标题-->
    <TextView
        style="@style/main_album_item_title"
        android:layout_alignTop="@+id/main_cover_group"
        android:layout_toRightOf="@+id/main_cover_group" />

    <!--副标题-->
    <TextView style="@style/main_album_item_subtitle" />

    <LinearLayout
        android:id="@+id/main_ll_info_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/main_tv_album_title"
        android:layout_below="@id/main_tv_album_subtitle"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="6dp">

        <TextView
            android:id="@+id/main_tv_category_tag"
            style="@style/main_album_item_category_tag"
            android:layout_marginRight="9dp"
            android:text="历史"
            android:visibility="visible" />

        <TextView
            android:id="@+id/main_tv_play_count"
            style="@style/main_album_info"
            android:drawableLeft="@drawable/host_ic_play_count"
            android:text="1.4万" />

        <TextView
            android:id="@+id/main_tv_track_count"
            style="@style/main_album_info"
            android:drawableLeft="@drawable/host_ic_track_count"
            android:text="123集" />

    </LinearLayout>

    <!--专辑条分割线-->
    <View
        style="@style/main_album_item_border"
        android:layout_below="@+id/main_ll_info_container"
        android:layout_toRightOf="@+id/main_cover_group" />


</RelativeLayout>