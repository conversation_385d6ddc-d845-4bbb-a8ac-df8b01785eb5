<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:makeramen="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/host_bg_list_selector"
    android:descendantFocusability="blocksDescendants"
    android:gravity="left"
    android:orientation="horizontal">

    <!--排行数字-->
    <TextView
        android:id="@+id/main_rank_num"
        android:layout_width="40dp"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:contentDescription="@string/main_album_rank_num"
        android:gravity="center"
        tools:text="1"
        android:textColor="@color/main_orange"
        android:textSize="18sp" />

    <com.ximalaya.ting.android.host.view.RoundBottomRightCornerView
        android:id="@+id/main_station_image"
        android:layout_width="43dp"
        android:layout_height="43dp"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:layout_marginTop="15dp"
        android:layout_toRightOf="@+id/main_rank_num"
        android:scaleType="centerCrop"
        android:src="@drawable/host_default_avatar_88"
        makeramen:border_color="@color/main_black"
        tools:visibility="visible"
        makeramen:border_width="0px"
        makeramen:corner_radius="80dp"
        makeramen:pressdown_shade="false"
        makeramen:round_background="true" />

    <TextView
        android:id="@+id/main_station_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/main_station_image"
        android:layout_marginBottom="4dp"
        android:layout_marginRight="6dp"
        android:layout_toRightOf="@+id/main_station_image"
        android:ellipsize="end"
        android:gravity="center_vertical|left"
        android:maxLines="1"
        tools:visibility="visible"
        android:textColor="@color/main_color_4a4a4a_cfcfcf"
        tools:text="主播名称"
        android:textSize="15sp" />

    <ImageView
        android:id="@+id/main_anchor_grade"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/main_station_name"
        android:layout_alignBottom="@+id/main_station_name"
        android:layout_toRightOf="@+id/main_station_name"
        android:layout_marginRight="6dp"
        tools:visibility="visible"
        android:contentDescription="@string/main_content_description_anchor_grade"
        android:transformPivotX="@dimen/default_line_indicator_gap_width" />


    <ImageView
        android:id="@+id/main_vip_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/main_anchor_grade"
        android:layout_toRightOf="@+id/main_anchor_grade"
        android:layout_marginRight="6dp"
        android:scaleType="fitCenter"
        tools:visibility="visible"
        android:src="@drawable/main_vip_fra_vip_colorful"
        android:contentDescription="@string/main_content_description_vip_tag"
        android:visibility="gone" />

    <TextView
        android:id="@+id/main_personDescribe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/main_station_name"
        android:layout_below="@+id/main_station_name"
        android:layout_marginBottom="4dp"
        tools:text="主播描述"
        android:layout_marginRight="10dp"
        android:layout_toLeftOf="@+id/main_concern_btn"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/main_color_999999_888888"
        tools:visibility="visible"
        android:textSize="12sp" />

    <LinearLayout
        android:id="@+id/main_status_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/main_station_name"
        android:layout_below="@+id/main_personDescribe"
        android:layout_marginBottom="15dp"
        android:layout_toLeftOf="@+id/main_concern_btn"
        android:gravity="left|center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/main_sounds_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:drawableLeft="@drawable/host_ic_item_sounds_count"
            android:drawablePadding="3dp"
            tools:text="2012"
            android:textColor="@color/main_text_light"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/main_fans_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/main_icon_by_fans"
            android:drawablePadding="3dp"
            tools:text="20万"
            android:textColor="@color/main_text_light"
            android:textSize="12sp" />

        <ImageView
            android:id="@+id/main_live_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:visibility="visible"
            android:layout_marginLeft="10dp"
            android:contentDescription="@string/main_content_description_live_status"
            android:visibility="invisible" />
    </LinearLayout>


    <ToggleButton
        android:id="@+id/main_concern_btn"
        android:layout_width="23dp"
        android:layout_height="36dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="25dp"
        android:background="@drawable/main_btn_follow_selector"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:textOff="@null"
        android:textOn="@null"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/main_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        tools:visibility="visible"
        android:layout_marginRight="25dp"
        android:contentDescription="@string/main_content_description_more"
        android:background="@drawable/main_btn_more_selector"
        android:focusable="false"
        android:visibility="gone" />

    <View
        android:id="@+id/main_anchor_item_divider"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignLeft="@+id/main_station_name"
        android:layout_below="@+id/main_status_layout"
        android:background="@color/main_border_gray" />

    <View
        android:id="@+id/main_divider1"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_below="@+id/main_status_layout"
        android:background="@color/framework_bg_color"
        android:visibility="gone" />
</RelativeLayout>