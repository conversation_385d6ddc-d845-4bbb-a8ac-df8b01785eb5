<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:skin="http://schemas.android.com/android/skin"
    android:layout_width="58dp"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_iv_cover"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="4dp"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:src="@drawable/host_default_hulu_for_new_ui" />

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_iv_cover_enhance"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="4dp"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:src="@drawable/host_default_hulu_for_new_ui"
        android:visibility="gone"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/main_iv_cover_play"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/main_icon_zm_play"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="19dp"
        android:visibility="gone"
        tools:visibility="visible"
        />

    <ImageView
        android:id="@+id/main_new_flag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/main_iv_cover"
        android:layout_alignRight="@id/main_iv_cover"
        android:layout_marginTop="4dp"
        android:layout_marginRight="4dp"
        android:src="@drawable/main_ic_discovery_dot_new"
        android:visibility="gone"
        skin:enable="true"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/main_tv_flag_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/main_iv_cover"
        android:layout_marginLeft="-30dp"
        android:layout_marginTop="-1dp"
        android:layout_toRightOf="@id/main_iv_cover">

        <TextView
            android:id="@+id/main_flag_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="12dp"
            android:background="@drawable/main_bg_ff4444_corner_5"
            android:gravity="center"
            android:maxLines="1"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:textColor="@color/main_white"
            android:textSize="9sp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="喜马拉雅"
            skin:enable="true" />

        <com.ximalaya.ting.android.host.view.text.MarqueeTextView2
            android:id="@+id/main_tv_enhance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/main_bg_ff678a_corner_5_100_100_0"
            android:paddingLeft="3dp"
            android:textColor="@color/main_white"
            android:paddingRight="3dp"
            android:singleLine="true"
            android:focusable="true"
            android:ellipsize="none"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="喜马拉雅"
            android:focusableInTouchMode="true"
            android:textSize="9sp"/>

    </LinearLayout>


    <TextView
        android:id="@+id/main_item_finding_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_iv_cover"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/main_color_111111_888888"
        android:textSize="12sp"
        android:text=" "
        android:importantForAccessibility="no"
        skin:enable="true" />

</RelativeLayout>