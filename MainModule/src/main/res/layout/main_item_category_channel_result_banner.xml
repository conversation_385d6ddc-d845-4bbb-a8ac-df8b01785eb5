<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="265:56"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:id="@+id/main_iv_cover"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="12dp"
        app:corner_radius="4dp"
        android:layout_marginEnd="16dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>