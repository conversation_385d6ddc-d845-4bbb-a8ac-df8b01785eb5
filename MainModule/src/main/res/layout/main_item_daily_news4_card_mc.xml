<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:layout_marginStart="12dp"
    android:layout_marginEnd="12dp"
    android:id="@+id/main_feed_play_root"
    android:background="@drawable/main_feed_play_normal_card_bg">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_daily_mc_card_iv"
        app:corner_radius="4dp"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintDimensionRatio="319:124"
        tools:src="@drawable/main_dailynews3_topic_default_icon"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <TextView
        android:id="@+id/main_play_track_title"
        android:fontFamily="sans-serif-light"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="2"
        android:includeFontPadding="false"
        android:layout_marginStart="16dp"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="16dp"
        android:textColor="@color/main_color_333333_cfcfcf"
        android:textSize="17sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@+id/main_daily_mc_card_iv"
        tools:text="                三体第一季预告第一集科学边界广播剧预告来袭" />


    <LinearLayout
        app:layout_constraintTop_toTopOf="@+id/main_play_track_title"
        app:layout_constraintStart_toStartOf="@+id/main_play_track_title"
        android:orientation="horizontal"
        android:layout_marginTop="3dp"
        android:background="@drawable/main_4corner_29ff9935"
        android:layout_width="wrap_content"
        android:layout_height="19dp">

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/main_play_lottie"
            app:lottie_autoPlay="false"
            android:layout_marginEnd="1dp"
            android:layout_marginStart="3dp"
            app:lottie_repeatCount="-1"
            android:layout_gravity="center"
            android:layout_width="12dp"
            android:layout_height="12dp" />

        <TextView
            android:id="@+id/main_live_status_tv"
            android:fontFamily="sans-serif-light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:layout_marginEnd="4dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="#FF9935"
            android:textSize="11sp"
            android:text="热聊中" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_group_avatar"
        android:layout_width="0dp"
        tools:visibility="visible"
        android:layout_height="wrap_content"
        android:layout_marginTop="9dp"
        app:layout_constraintStart_toStartOf="@+id/main_play_track_title"
        app:layout_constraintEnd_toEndOf="@+id/main_play_track_title"
        app:layout_constraintTop_toBottomOf="@+id/main_play_track_title">

        <com.ximalaya.ting.android.main.playModule.dailyNews4.view.MyClubAvatarViewForDaily
            android:id="@+id/main_mcv_author1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            tools:visibility="visible"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/main_mcv_author2"
            app:layout_constraintHorizontal_chainStyle="spread_inside" />

        <com.ximalaya.ting.android.main.playModule.dailyNews4.view.MyClubAvatarViewForDaily
            android:id="@+id/main_mcv_author2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_mcv_author1"
            app:layout_constraintEnd_toStartOf="@+id/main_mcv_author3" />

        <com.ximalaya.ting.android.main.playModule.dailyNews4.view.MyClubAvatarViewForDaily
            android:id="@+id/main_mcv_author3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_mcv_author2"
            app:layout_constraintEnd_toStartOf="@+id/main_mcv_author4" />

        <com.ximalaya.ting.android.main.playModule.dailyNews4.view.MyClubAvatarViewForDaily
            android:id="@+id/main_mcv_author4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_mcv_author3"
            app:layout_constraintEnd_toStartOf="@+id/main_mcv_author5" />

        <com.ximalaya.ting.android.main.playModule.dailyNews4.view.MyClubAvatarViewForDaily
            android:id="@+id/main_mcv_author5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_mcv_author4"
            app:layout_constraintEnd_toEndOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/main_daily_mc_people_count"
        android:layout_marginTop="8dp"
        tools:visibility="visible"
        android:fontFamily="sans-serif-light"
        android:layout_marginStart="16dp"
        android:paddingBottom="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_group_avatar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:layout_gravity="center"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="@color/main_color_666666_888888"
        android:textSize="11sp"
        tools:text="405人参与 3人发言" />

    <LinearLayout
        android:id="@+id/main_live_stop_layout"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_daily_mc_people_count"
        android:gravity="center"
        android:paddingBottom="10dp"
        android:paddingTop="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/main_daily_news_live_stop_mc" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:textColor="@color/main_color_999999"
            android:textSize="12sp"
            android:text="该直播已经结束" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_topic_layout"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_live_stop_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="0dp"
        tools:visibility="visible"
        android:visibility="gone"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/main_bg_corner_4_f6f7f8"
        android:layout_height="36dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_topic_iv"
            tools:src="@drawable/host_default_album"
            android:scaleType="centerCrop"
            app:corner_radius="4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="36dp"
            android:layout_height="36dp" />

        <TextView
            android:id="@+id/main_topic_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:paddingEnd="16dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/main_color_666666_888888"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@+id/main_topic_iv"
            app:layout_constraintEnd_toStartOf="@+id/main_album_sub_name"
            app:layout_constraintBottom_toBottomOf="@+id/main_topic_iv"
            app:layout_constraintTop_toTopOf="@+id/main_topic_iv"
            tools:text="专辑 三体第一季预告来袭" />


        <TextView
            android:id="@+id/main_album_sub_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/main_color_999999_888888"
            android:textSize="11sp"
            app:layout_constraintEnd_toStartOf="@+id/main_arrow_iv"
            app:layout_constraintTop_toTopOf="@+id/main_topic_iv"
            app:layout_constraintBottom_toBottomOf="@+id/main_topic_iv"
            tools:text="虎嗅财经" />

        <ImageView
            android:id="@+id/main_arrow_iv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="8dp"
            android:src="@drawable/host_ic_standard_back_arrow_down_regular"
            android:scaleType="fitXY"
            android:tint="@color/main_color_999999_888888"
            android:rotation="270"
            android:padding="2dp"
            android:layout_width="16dp"
            android:layout_height="16dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
