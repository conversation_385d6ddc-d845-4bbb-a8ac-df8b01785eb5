<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:descendantFocusability="blocksDescendants"
    xmlns:makeramen="http://schemas.android.com/apk/res-auto">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="centerCrop"
        android:id="@+id/main_icon"
        android:src="@drawable/host_anchor_default_img"
        makeramen:corner_radius="5dp"
        makeramen:border_width="1px"
        android:clickable="false"
        android:background="@color/host_white"
        makeramen:border_bg_color="#EDEDED"
        makeramen:border_color="#EDEDED"
        />

    <ImageView
        android:id="@+id/main_head_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/main_edit_head"
        android:contentDescription="@string/main_content_description_head_logo"
        android:layout_alignBottom="@+id/main_icon"
        android:layout_alignRight="@+id/main_icon"
        android:layout_marginBottom="-2dp"
        android:layout_marginRight="-2dp"
        />

</RelativeLayout>
