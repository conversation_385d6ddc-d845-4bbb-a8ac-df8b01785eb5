<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_iv_cover"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_toRightOf="@id/main_tv_number"
        android:layout_marginLeft="10dp"
        android:layout_marginBottom="8dp"
        android:layout_marginRight="14dp"
        android:layout_marginTop="8dp"
        app:corner_radius="4dp"
        android:scaleType="centerCrop"
        tools:src="@drawable/ic_launcher" />

    <TextView
        android:id="@+id/main_tv_number"
        android:layout_width="24dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="right"
        android:textSize="20sp"
        tools:text="12" />

    <com.ximalaya.ting.android.framework.view.image.FlexibleRoundImageView
        android:id="@+id/main_iv_tag"
        style="@style/main_album_cover_tag_size_small"
        android:layout_alignLeft="@id/main_iv_cover"
        android:layout_alignTop="@id/main_iv_cover"
        tools:src="@drawable/host_album_tag_pay"
        app:flexible_round_corner="left_top"
        app:flexible_round_corner_radius="4dp"/>

    <TextView
        android:id="@id/main_tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/main_iv_cover"
        android:layout_marginRight="8dp"
        android:layout_toLeftOf="@+id/main_tv_subscribe"
        android:layout_toRightOf="@id/main_iv_cover"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/main_color_000000_cfcfcf"
        android:textSize="14sp"
        tools:text="专辑名称" />

    <TextView
        android:id="@+id/main_tv_intro"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_tv_subscribe"
        android:layout_marginRight="16dp"
        android:layout_marginTop="4dp"
        android:layout_toRightOf="@id/main_iv_cover"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/main_color_999999_888888"
        android:textSize="12sp"
        tools:text="专辑卖点专辑卖点专辑卖点专辑卖点专辑卖点专辑卖点专辑卖点专辑卖点专辑卖点" />

    <TextView
        android:id="@+id/main_tv_author"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_tv_intro"
        android:layout_marginRight="8dp"
        android:layout_toRightOf="@id/main_iv_cover"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:lines="1"
        android:maxWidth="128dp"
        android:textColor="@color/main_color_999999_888888"
        android:textSize="11sp"
        tools:text="主播名" />

    <TextView
        android:id="@+id/main_tv_also_listen"
        android:layout_below="@id/main_tv_intro"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="8dp"
        android:layout_marginTop="4dp"
        android:layout_toRightOf="@id/main_tv_author"
        android:ellipsize="end"
        android:lines="1"
        tools:text="50万人也在听"
        android:textColor="@color/main_color_999999_888888"
        android:textSize="11sp" />

    <TextView
        android:id="@id/main_tv_subscribe"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_alignParentRight="true"
        android:layout_alignTop="@id/main_iv_cover"
        android:background="@drawable/main_subscribe_dialog_btn_selector1"
        android:drawableLeft="@drawable/host_rec_subscribe_plus"
        android:drawablePadding="4dp"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="@string/main_subscribe"
        android:textColor="@color/main_subscribe_dialog_btn_text_selector"
        android:textSize="13sp"
        android:textStyle="bold" />

</RelativeLayout>