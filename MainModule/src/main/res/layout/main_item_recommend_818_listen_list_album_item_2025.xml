<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_csl_item_root_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="16dp"
    android:paddingTop="8dp"
    android:paddingRight="16dp"
    android:paddingBottom="8dp">

    <com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
        android:id="@+id/main_album_cover_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription=""
        android:importantForAccessibility="yes"
        app:albumCoverSize="70dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_layout_right_txt_area"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginEnd="2dp"
        app:layout_constraintBottom_toBottomOf="@+id/main_album_cover_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main_album_cover_layout"
        app:layout_constraintTop_toTopOf="@+id/main_album_cover_layout">

        <TextView
            android:id="@+id/main_tv_album_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="sans-serif-light"
            android:maxLines="2"
            android:text="这么多年 精品有声剧精品123456"
            android:textColor="@color/main_color_home_title_new"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/main_tv_sub_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="这么多年 精品有声剧"
            android:textColor="@color/main_color_662c2c3c_8d8d91"
            android:textSize="12sp"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_album_title" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_layout_show_tag_parent_album"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:importantForAccessibility="no"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_sub_title">

            <ImageView
                android:id="@+id/main_iv_ad_tag"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/main_recommend_ad_small_icon_new"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/main_layout_show_tag"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/main_layout_show_tag"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main_iv_ad_tag"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>