<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:importantForAccessibility="no">

    <com.ximalaya.ting.android.main.view.recommend.WrapContentViewPager
        android:id="@+id/main_view_pager_album_list"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="200dp" />

    <TextView
        android:id="@+id/main_tv_refresh"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/main_bg_818_listen_list_refresh_shape"
        android:fontFamily="sans-serif-light"
        android:gravity="center"
        android:minHeight="40dp"
        android:text="换一换"
        android:textColor="@drawable/main_text_color_recommend_818_listen_selector"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_view_pager_album_list" />

</androidx.constraintlayout.widget.ConstraintLayout>