<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_csl_item_root_view_track"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/main_bg_item_guide_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginVertical="1dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="50dp"
        android:background="@drawable/main_bg_new_home_item_guide_shape"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <com.ximalaya.ting.android.host.view.CornerRelativeLayout
        android:id="@+id/main_crl_item_cover"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_marginVertical="8dp"
        android:layout_marginLeft="16dp"
        android:contentDescription=""
        android:importantForAccessibility="yes"
        app:corner_radius="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/main_iv_item_cover_track"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/host_color_ffffff"
            android:scaleType="centerCrop"
            android:src="@drawable/host_default_album" />

    </com.ximalaya.ting.android.host.view.CornerRelativeLayout>

    <View
        android:id="@+id/main_v_bg_play"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="4dp"
        android:layout_marginBottom="4dp"
        android:background="@drawable/main_bg_rect_33ffffff_corner_19"
        app:layout_constraintBottom_toBottomOf="@+id/main_crl_item_cover"
        app:layout_constraintEnd_toEndOf="@+id/main_crl_item_cover" />

    <ImageView
        android:id="@+id/main_iv_play_btn"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:scaleType="centerCrop"
        android:src="@drawable/host_play_btn_inside_fill_n_28"
        android:tint="@color/host_color_ffffff"
        app:layout_constraintBottom_toBottomOf="@+id/main_v_bg_play"
        app:layout_constraintEnd_toEndOf="@+id/main_v_bg_play"
        app:layout_constraintStart_toStartOf="@+id/main_v_bg_play"
        app:layout_constraintTop_toTopOf="@+id/main_v_bg_play" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_layout_right_txt_area"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/main_crl_item_cover"
        app:layout_constraintEnd_toStartOf="@+id/main_show_notes_play_layout_wrap"
        app:layout_constraintStart_toEndOf="@+id/main_crl_item_cover"
        app:layout_constraintTop_toTopOf="@+id/main_crl_item_cover"
        app:layout_goneMarginEnd="16dp">

        <TextView
            android:id="@+id/main_tv_track_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="sans-serif-light"
            android:maxLines="2"
            android:text="这么多年 精品有声剧精品有声么多年精品有声么多年精品有声么多年"
            android:textColor="@color/main_color_home_title_new"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/main_tv_track_sub_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="这么多年 精品有声剧精品有声剧精品有声剧精品有声剧精品有声剧"
            android:textColor="@color/main_color_662c2c3c_8d8d91"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_track_title" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_layout_show_tag_parent_track"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:importantForAccessibility="no"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_track_sub_title">

            <ImageView
                android:id="@+id/main_iv_ad_tag_track"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/main_recommend_ad_small_icon_new"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/main_tv_track_show_tag_layout"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/main_tv_track_show_tag_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/main_iv_ad_tag_track"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/main_show_notes_play_layout_wrap"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_marginEnd="5dp"
        android:contentDescription="播放按钮"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/main_crl_item_cover"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/main_crl_item_cover"
        tools:visibility="visible">

        <FrameLayout
            android:id="@+id/main_show_notes_play_layout"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:background="@drawable/host_round_ffffff_1a8d8d91">

            <ImageView
                android:id="@+id/main_iv_show_notes_play_btn"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="center"
                android:scaleType="centerCrop"
                android:src="@drawable/host_btn_play_btn_inside_fill_n_24" />
        </FrameLayout>

    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>