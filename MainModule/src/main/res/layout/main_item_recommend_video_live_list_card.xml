<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="16dp"
    android:importantForAccessibility="no">

    <TextView
        android:id="@+id/main_tv_live_list_tittle_shadow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginEnd="28dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:importantForAccessibility="yes"
        android:lineSpacingExtra="3dp"
        android:maxLines="1"
        android:text="818重磅大咖直播"
        android:textSize="16sp"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_more"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_tv_live_list_tittle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginEnd="28dp"
        android:ellipsize="end"
        android:fontFamily="sans-serif-light"
        android:gravity="center_vertical"
        android:importantForAccessibility="yes"
        android:lineSpacingExtra="3dp"
        android:maxLines="1"
        android:textColor="@color/main_color_module_title"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_more"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="818重磅大咖直播" />

    <TextView
        android:id="@+id/main_tv_live_list_sub_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="28dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:importantForAccessibility="yes"
        android:maxLines="1"
        android:textColor="@color/main_color_662c2c3c_8d8d91"
        android:textSize="12sp"
        android:visibility="visible"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_more"
        app:layout_constraintStart_toStartOf="@+id/main_tv_live_list_tittle"
        app:layout_constraintTop_toBottomOf="@+id/main_tv_live_list_tittle"
        tools:text="不要焦虑不要焦虑不要焦虑不要焦虑不要焦虑不要焦虑不要焦虑" />

    <ImageView
        android:id="@+id/main_tv_more"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="更多"
        android:importantForAccessibility="yes"
        android:src="@drawable/main_ic_home_item_more"
        app:layout_constraintBottom_toBottomOf="@id/main_tv_live_list_tittle_shadow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_tv_live_list_tittle_shadow" />

    <LinearLayout
        android:id="@+id/main_ll_satisfy_root_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="6dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_tv_live_list_sub_title" />

    <com.ximalaya.ting.android.main.view.recommend.CustomRnRecyclerView
        android:id="@+id/main_rcv_live_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:clipToPadding="false"
        android:orientation="horizontal"
        android:overScrollMode="never"
        android:paddingStart="16dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_ll_satisfy_root_view"
        tools:listitem="@layout/main_item_recommend_818_live_list_item" />
</androidx.constraintlayout.widget.ConstraintLayout>