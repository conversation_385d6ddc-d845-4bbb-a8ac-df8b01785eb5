<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_list_selector"
    android:descendantFocusability="blocksDescendants">

    <ImageView
        android:id="@+id/main_iv_select"
        android:layout_width="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:src="@drawable/host_sync_dynamic_check_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--封面-->
    <androidx.cardview.widget.CardView
        android:id="@+id/main_cover_group"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginStart="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main_iv_select"
        app:layout_constraintTop_toTopOf="parent">
        <!--专辑图-->
        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_iv_album_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/main_album_cover"
            android:scaleType="centerCrop"
            android:src="@drawable/host_default_album"
            app:border_color="@color/main_color_e8e8e8_1e1e1e"
            app:border_width="1px"
            app:corner_radius="4dp"
            app:pressdown_shade="false"
            tools:background="@drawable/main_bg_album_cover" />

        <!--付费角标-->
        <ImageView
            android:id="@+id/main_iv_album_pay_cover_tag"
            style="@style/main_album_cover_tag_size_small"
            android:layout_height="10dp"
            android:layout_alignStart="@+id/main_iv_album_cover"
            android:layout_alignLeft="@+id/main_iv_album_cover"
            android:layout_alignTop="@+id/main_iv_album_cover"
            android:contentDescription="@string/main_paid_album_tag"
            android:visibility="invisible"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/main_iv_activity_tag"
            android:layout_width="match_parent"
            android:layout_height="19dp"
            android:layout_gravity="bottom"
            android:scaleType="fitXY" />

    </androidx.cardview.widget.CardView>

    <Space
        android:id="@+id/main_item_subscribe_space"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="4dp"
        android:layout_marginBottom="4dp"
        app:layout_constraintBottom_toTopOf="@+id/main_cover_group"
        app:layout_constraintLeft_toRightOf="@+id/main_cover_group" />

    <TextView
        android:id="@+id/main_item_tv_update_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/main_item_subscribe_f86442_ffffff_radius_7"
        android:contentDescription="@string/main_album_update_num"
        android:fontFamily="sans-serif-light"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:paddingLeft="4.5dp"
        android:paddingTop="1.5dp"
        android:paddingRight="4.5dp"
        android:paddingBottom="1.5dp"
        android:textColor="@color/main_color_ffffff"
        android:textSize="10sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintRight_toLeftOf="@+id/main_item_subscribe_space"
        app:layout_constraintTop_toTopOf="@+id/main_item_subscribe_space"
        tools:text="20"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_woting_album_item"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        app:layout_constraintBottom_toBottomOf="@id/main_cover_group"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/main_cover_group"
        app:layout_constraintTop_toTopOf="@id/main_cover_group">

        <!--主标题-->
        <TextView
            android:id="@+id/main_tv_album_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/main_album_item_title"
            android:ellipsize="end"
            android:fontFamily="sans-serif-light"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:text="@string/main_app_name"
            android:textColor="@color/main_color_333333_cfcfcf"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="@id/main_woting_album_item"
            app:layout_constraintTop_toTopOf="@id/main_woting_album_item"
            app:layout_constraintVertical_chainStyle="packed" />

        <!--副标题-->
        <TextView
            android:id="@+id/main_tv_album_subtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:contentDescription="@string/main_album_item_subtitle"
            android:ellipsize="end"
            android:lines="1"
            android:text="@string/main_app_name"
            android:textColor="@color/main_color_999999_888888"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="@id/main_woting_album_item"
            app:layout_constraintLeft_toLeftOf="@id/main_woting_album_item"
            app:layout_constraintStart_toStartOf="@id/main_woting_album_item"
            app:layout_constraintTop_toBottomOf="@id/main_tv_album_title"
            tools:text="ffsfsfsfsfsfsfsfsfsfsfsfsfsfsffsfsfsfsfsfsfsffsfsfsfsfsfsfsffsfsfsfsfsfsfsffsfsfsfsfsfsfsf"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/main_update_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/host_mylisten_album_status_bg"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:paddingLeft="4dp"
            android:paddingTop="1dp"
            android:paddingRight="4dp"
            android:paddingBottom="1dp"
            android:singleLine="true"
            android:textColor="#E5858D95"
            android:textSize="9sp"
            app:layout_constraintLeft_toLeftOf="@id/main_tv_album_title"
            app:layout_constraintTop_toBottomOf="@id/main_tv_album_subtitle"
            tools:text="完结"
            tools:visibility="visible" />

        <com.ximalaya.ting.android.main.view.text.CountDownTextView
            android:id="@+id/main_time_limit_free_listen_count_down"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="5dp"
            android:paddingTop="1dp"
            android:singleLine="true"
            android:textColor="#fff5a623"
            android:textSize="9sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/main_woting_album_item"
            app:layout_constraintStart_toEndOf="@id/main_update_status_text"
            app:layout_constraintTop_toBottomOf="@id/main_tv_album_subtitle"
            app:layout_goneMarginLeft="0dp"
            tools:text="限时免费听  3天12时57分"
            tools:visibility="visible" />


    </androidx.constraintlayout.widget.ConstraintLayout>


    <!--已下架-->
    <ImageView
        android:id="@+id/main_iv_off_sale"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:layout_marginRight="20dp"
        android:contentDescription="@string/main_item_woting_subscribe_iv_cd_off_sale"
        android:src="@drawable/main_icon_off_sale"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/main_cover_group"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_cover_group"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>