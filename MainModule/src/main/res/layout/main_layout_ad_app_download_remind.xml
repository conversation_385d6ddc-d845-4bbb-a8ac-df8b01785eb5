<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="30dp"
    android:layout_marginRight="30dp"
    android:background="@drawable/host_round_bg_white_radius_8">

    <ImageView
        android:id="@+id/main_download_hint_icon"
        android:src="@drawable/main_download_hint_icon"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="30dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/main_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginTop="16dp"
        android:layout_below="@id/main_download_hint_icon"
        android:text="提示"
        android:textColor="@color/host_color_333333_ffffff"
        android:textSize="15sp" />

    <View
        android:layout_width="match_parent"
        android:layout_below="@id/main_sub_title"
        android:layout_marginTop="25dp"
        android:background="@color/main_color_e8e8e8"
        android:layout_height="1px" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_sub_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="25dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/main_cancel"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="45dp"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/host_color_666666_888888"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/main_ok"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="45dp"
            android:gravity="center"
            android:textSize="16sp"
            android:background="@drawable/main_download_hint_bg"
            android:text="立即下载"
            android:textColor="@color/main_white" />
    </LinearLayout>

</RelativeLayout>