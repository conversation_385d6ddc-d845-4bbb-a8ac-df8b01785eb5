<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_download_style_2_dialog_view"
    android:layout_width="299dp"
    android:layout_height="wrap_content"
    android:background="@drawable/host_round_bg_white_radius_8"
    android:orientation="vertical">


    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_download_style_2_icon"
        android:layout_width="63dp"
        android:layout_height="63dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="28dp"
        android:scaleType="fitXY"
        app:corner_radius="6dp" />

    <TextView
        android:id="@+id/main_download_style_2_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="20dp"
        android:textColor="@color/host_color_333333_ffffff"
        android:textSize="18sp"
        android:textStyle="bold"
        tools:text="app name" />

    <TextView
        android:id="@+id/main_download_style_2_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="20dp"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="2"
        android:visibility="gone"
        android:textColor="@color/host_color_333333_ffffff"
        android:textSize="13sp"
        tools:text="介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/main_download_style_2_star"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="2dp"
            android:src="@drawable/main_icon_download_app_star_red" />

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="2dp"
            android:src="@drawable/main_icon_download_app_star_red" />

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="2dp"
            android:src="@drawable/main_icon_download_app_star_red" />

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="2dp"
            android:src="@drawable/main_icon_download_app_star_red" />

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="2dp"
            android:src="@drawable/main_icon_download_app_star_grey" />
    </LinearLayout>

    <TextView
        android:id="@+id/main_download_style_2_btn_ok"
        android:layout_width="match_parent"
        android:layout_height="41dp"
        android:layout_marginLeft="28dp"
        android:layout_marginTop="28dp"
        android:layout_marginRight="28dp"
        android:background="@drawable/host_bg_ff4444_radius_50"
        android:gravity="center"
        android:text="立即下载"
        android:textColor="@color/main_white"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/main_download_style_2_btn_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="19dp"
        android:gravity="center"
        android:text="放弃下载"
        android:textColor="#FF999990"
        android:textSize="13sp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="21dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="11dp"
        android:gravity="center"
        android:text="经过喜马官方检测，安全无毒，请放心安装"
        android:textColor="#FFCACACA"
        android:textSize="12sp" />
</LinearLayout>