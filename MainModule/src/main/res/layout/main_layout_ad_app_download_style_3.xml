<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_download_style_3_dialog_layout"
    android:layout_width="278dp"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:background="@drawable/host_round_bg_white_radius_8"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="278dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:orientation="vertical">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_download_style_3_icon"
            android:layout_width="63dp"
            android:layout_height="63dp"
            android:layout_gravity="center_horizontal"
            android:scaleType="fitXY"
            app:corner_radius="6dp"
            tools:src="@drawable/ting" />

        <TextView
            android:id="@+id/main_download_style_3_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:textColor="@color/host_color_333333_ffffff"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="app name" />

        <TextView
            android:id="@+id/main_download_style_3_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="2"
            android:textColor="@color/host_color_333333_ffffff"
            android:textSize="13sp"
            android:visibility="gone"
            tools:text="介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/main_download_style_3_star"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="13dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="3dp"
                android:src="@drawable/main_icon_download_app_star_red" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="3dp"
                android:src="@drawable/main_icon_download_app_star_red" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="3dp"
                android:src="@drawable/main_icon_download_app_star_red" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="3dp"
                android:src="@drawable/main_icon_download_app_star_red" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="3dp"
                android:src="@drawable/main_icon_download_app_star_pink" />
        </LinearLayout>

        <com.ximalaya.ting.android.host.manager.ad.webad.view.TextProgressBar
            android:id="@+id/main_download_style_3_btn_ok"
            style="@android:style/Widget.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="41dp"
            android:layout_marginLeft="28dp"
            android:layout_marginTop="25dp"
            android:layout_marginRight="28dp"
            android:layout_marginBottom="10dp"
            app:progressBarTextSize="15sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="11dp"
            android:drawableLeft="@drawable/host_icon_download_app_safety"
            android:drawablePadding="4dp"
            android:gravity="center"
            android:text="经过喜马官方检测，安全无毒，请放心安装"
            android:textColor="#B8B8B8"
            android:textSize="12sp" />
    </LinearLayout>

    <ImageView
        android:id="@+id/main_download_style_3_close"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_alignParentRight="true"
        android:layout_gravity="right"
        android:paddingTop="12dp"
        android:paddingRight="12dp"
        android:src="@drawable/host_icon_close_black" />
</RelativeLayout>
