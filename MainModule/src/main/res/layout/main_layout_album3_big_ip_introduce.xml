<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/host_x16"
    android:paddingEnd="@dimen/host_x16"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="@color/host_gray"
    tools:visibility="visible">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_album3_intro_padding_container"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingBottom="@dimen/host_y16"
        android:background="@drawable/main_bg_0affffff_radius_6"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.ximalaya.ting.android.main.albumModule.album.album3.view.AlbumTagsView3
            android:id="@+id/main_album3_big_ip_tags"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:requiresFadingEdge="horizontal"
            android:fadingEdge="horizontal"
            android:fadingEdgeLength="23dp"
            android:layout_marginTop="@dimen/host_y16"
            tools:layout_height="30dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/main_album3_big_ip_more_title_tv"
            android:textSize="14sp"
            android:textColor="@color/host_color_white"
            android:maxLines="2"
            android:ellipsize="end"
            android:lineSpacingExtra="4.4sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            android:includeFontPadding="false"
            app:layout_constraintTop_toBottomOf="@+id/main_album3_big_ip_tags"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/host_y14"
            app:layout_goneMarginTop="@dimen/host_y16"
            android:layout_marginStart="@dimen/host_x16"
            android:layout_marginEnd="@dimen/host_x16"
            android:layout_height="wrap_content" />

        <com.ximalaya.ting.android.main.albumModule.album.album3.view.AlbumMoreTextView3
            android:id="@+id/main_album3_big_ip_more_view"
            app:layout_constraintTop_toBottomOf="@+id/main_album3_big_ip_more_title_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/host_y8"
            app:layout_goneMarginTop="@dimen/host_y16"
            android:layout_marginStart="@dimen/host_x16"
            android:layout_marginEnd="@dimen/host_x16"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:id="@+id/main_ll_commercial_bar_in_intro"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_album3_big_ip_more_view"
            tools:visibility="visible" />

        <com.ximalaya.ting.android.main.albumModule.album.album3.view.AlbumLiveAndMcView3
            android:id="@+id/main_album3_big_ip_live_view"
            app:layout_constraintTop_toBottomOf="@+id/main_ll_commercial_bar_in_intro"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/host_y12"
            android:layout_width="match_parent"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>