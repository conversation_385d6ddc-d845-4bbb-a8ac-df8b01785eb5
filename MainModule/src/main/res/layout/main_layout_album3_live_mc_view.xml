<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:id="@+id/main_album3_live_divider_view"
        android:background="@color/host_color_14000000_14ffffff"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="1px" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_padding_view"
        android:paddingTop="@dimen/host_y12"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_iv_live_avatar"
            android:src="@drawable/host_default_avatar_210"
            android:layout_marginStart="16dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginTop="9dp"
            app:corner_radius="1000dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="40dp"
            android:layout_height="40dp" />

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/main_album3_live_avatar_lottie"
            app:layout_constraintTop_toTopOf="@+id/main_iv_live_avatar"
            app:layout_constraintBottom_toBottomOf="@+id/main_iv_live_avatar"
            app:layout_constraintStart_toStartOf="@+id/main_iv_live_avatar"
            app:layout_constraintEnd_toEndOf="@+id/main_iv_live_avatar"
            android:visibility="gone"
            android:layout_marginBottom="1px"
            android:layout_marginEnd="1px"
            app:lottie_autoPlay="false"
            tools:visibility="visible"
            app:lottie_repeatCount="-1"
            android:layout_width="58dp"
            android:layout_height="58dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            app:layout_constraintBottom_toBottomOf="@+id/main_album3_live_click_btn"
            app:layout_constraintTop_toTopOf="@+id/main_album3_live_click_btn"
            app:layout_constraintStart_toEndOf="@+id/main_iv_live_avatar"
            android:layout_marginStart="8dp"
            app:layout_goneMarginStart="0dp"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintEnd_toStartOf="@+id/main_album3_live_click_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_album3_live_lottie"
                app:layout_constraintTop_toTopOf="@+id/main_album3_live_status"
                app:layout_constraintBottom_toBottomOf="@+id/main_album3_live_status"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginStart="@dimen/host_x16"
                android:layout_marginBottom="2dp"
                tools:visibility="gone"
                android:visibility="visible"
                app:lottie_autoPlay="false"
                app:lottie_repeatCount="-1"
                android:layout_width="16dp"
                android:layout_height="16dp" />

            <TextView
                android:id="@+id/main_album3_live_status"
                android:text="直播"
                android:textSize="13sp"
                tools:visibility="gone"
                android:textColor="@color/main_color_ff4477_ff85a6"
                android:textStyle="bold"
                android:includeFontPadding="false"
                android:gravity="center"
                android:fontFamily="sans-serif-light"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main_album3_live_lottie"
                android:layout_marginStart="@dimen/host_x5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/main_album3_live_title"
                tools:text="聊一聊作家的脑洞都是怎样思考行程"
                android:maxLines="1"
                android:ellipsize="end"
                android:textStyle="bold"
                android:fontFamily="sans-serif-light"
                android:textSize="13sp"
                android:layout_marginEnd="@dimen/host_x8"
                android:layout_marginBottom="2dp"
                app:layout_goneMarginStart="0dp"
                app:layout_constraintTop_toTopOf="@+id/main_album3_live_status"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main_album3_live_status"
                app:layout_constraintBottom_toBottomOf="@+id/main_album3_live_status"
                android:layout_marginStart="@dimen/host_x8"
                android:textColor="@color/main_color_b3131313_b3ffffff"
                android:layout_width="0dp"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/main_album3_live_sub_title"
                tools:text="聊一聊作家的脑洞都是怎样思考行程"
                android:maxLines="1"
                android:ellipsize="end"
                android:visibility="gone"
                tools:visibility="visible"
                android:textSize="13sp"
                android:textFontWeight="400"
                android:layout_marginTop="@dimen/host_y4"
                app:layout_constraintTop_toBottomOf="@+id/main_album3_live_title"
                app:layout_constraintEnd_toEndOf="@+id/main_album3_live_title"
                app:layout_constraintStart_toStartOf="@+id/main_album3_live_status"
                android:textColor="@color/main_color_4d131313_4dffffff"
                android:layout_width="0dp"
                android:layout_height="wrap_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/main_album3_live_click_btn"
            tools:text="去围观"
            android:maxLines="1"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            android:ellipsize="end"
            android:background="@drawable/main_100corner_148d8d91"
            android:textSize="12sp"
            android:paddingStart="12dp"
            android:gravity="center"
            android:paddingEnd="12dp"
            android:minWidth="68dp"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="@dimen/host_x16"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>