<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/host_x16"
    android:paddingEnd="@dimen/host_x16"
    tools:background="@color/host_gray"
    tools:visibility="visible">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_album3_intro_padding_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/main_bg_f0f1f3_0affffff_radius_6"
        android:orientation="vertical"
        android:paddingBottom="@dimen/host_y16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.ximalaya.ting.android.main.albumModule.album.album3.view.AlbumTagsView3
            android:id="@+id/main_album3_novel_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/host_y16"
            android:fadingEdge="horizontal"
            android:fadingEdgeLength="23dp"
            android:requiresFadingEdge="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_height="30dp" />

        <TextView
            android:id="@+id/main_album3_novel_more_title_tv"
            android:textSize="14sp"
            android:textColor="@color/host_color_393942_dcdcdc"
            android:maxLines="2"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:lineSpacingExtra="4.4sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            app:layout_constraintTop_toBottomOf="@+id/main_album3_novel_tags"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/host_y14"
            app:layout_goneMarginTop="@dimen/host_y16"
            android:layout_marginStart="@dimen/host_x16"
            android:layout_marginEnd="@dimen/host_x16"
            android:layout_height="wrap_content" />

        <com.ximalaya.ting.android.main.albumModule.album.album3.view.AlbumMoreTextView3
            android:id="@+id/main_album3_novel_more_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/host_x16"
            android:layout_marginTop="@dimen/host_y8"
            android:layout_marginEnd="@dimen/host_x16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_album3_novel_more_title_tv"
            app:layout_goneMarginTop="@dimen/host_y10" />

        <LinearLayout
            android:id="@+id/main_ll_commercial_bar_in_intro"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_album3_novel_more_view"
            tools:visibility="visible" />

        <com.ximalaya.ting.android.main.albumModule.album.album3.view.AlbumLiveAndMcView3
            android:id="@+id/main_album3_novel_live_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="@dimen/host_y12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_ll_commercial_bar_in_intro"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>