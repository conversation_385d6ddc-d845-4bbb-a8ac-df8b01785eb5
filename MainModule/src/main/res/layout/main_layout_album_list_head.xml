<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/host_x16">

        <ImageView
            android:id="@+id/main_sort_iv"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginLeft="-13dp"
            android:contentDescription="排序"
            android:padding="14.5dp"
            android:scaleType="centerCrop"
            android:src="@drawable/host_ic_standard_order_top_to_down_20"
            android:tint="@color/host_color_999999_66666b" />

        <TextView
            android:id="@+id/main_sort_tv"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="-10dp"
            android:gravity="center_vertical"
            android:importantForAccessibility="no"
            android:text="从新到旧"
            android:textColor="@color/main_color_999999_66666b"
            android:textSize="14sp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/main_download_tv"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:drawableLeft="@drawable/host_ic_standard_download_rect_20"
            android:drawablePadding="4dp"
            android:drawableTint="@color/main_color_999999_66666b"
            android:gravity="center"
            android:paddingStart="13dp"
            android:text="下载"
            android:textColor="@color/main_color_999999_66666b"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/main_tv_page_selected_value"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_marginStart="16dp"
            android:drawableRight="@drawable/main_album_arrow_down"
            android:drawablePadding="3dp"
            android:gravity="center_vertical"
            android:paddingLeft="3dp"
            android:paddingRight="17dp"
            android:textColor="@color/main_color_999999_66666b"
            android:textSize="14sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/main_ll_new_user_mission_play_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="18dp"
        android:layout_marginRight="18dp"
        android:background="@drawable/main_bg_album_list_last_play"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="14dp"
        android:paddingBottom="10dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:scaleType="centerCrop"
            android:src="@drawable/main_ic_new_user_mission_play_golden" />

        <TextView
            android:id="@+id/main_album_mission_tip_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="8dp"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/main_color_d4863c"
            android:textSize="13sp"
            tools:text="新用户首次播放，立增 1 天 VIP 权益" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/main_ll_last_played_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="18dp"
        android:layout_marginRight="18dp"
        android:background="@drawable/main_bg_album_list_last_play"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="14dp"
        android:paddingBottom="10dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:scaleType="centerCrop"
            android:src="@drawable/host_album_ic_continue_play"
            android:tint="@color/host_color_xmRed" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="续播"
            android:textColor="@color/host_color_xmRed"
            android:textSize="13sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"
            android:text="|"
            android:textColor="@color/main_color_f86442_50"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/main_album_last_played_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="8dp"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/host_color_xmRed"
            android:textSize="13sp"
            tools:text="重返3000点，沪指涨1.84%两市成交额再破万亿" />
    </LinearLayout>

    <View
        android:id="@+id/main_view_header_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@+id/main_layout_header"
        android:background="@color/main_color_e8e8e8_2a2a2a" />
</LinearLayout>