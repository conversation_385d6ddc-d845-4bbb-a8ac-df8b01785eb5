<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_hsl_num"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/host_y3"
    android:layout_marginEnd="16dp"
    android:scrollbars="none"
    app:layout_constraintEnd_toEndOf="parent"
    android:layout_marginStart="16dp"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@+id/main_csl_base_info">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_listen_duration_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/main_tv_listen_duration_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:letterSpacing="-0.01"
                android:gravity="center"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/host_color_464652"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/main_tv_listen_duration_unit"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="1688" />

            <TextView
                android:id="@+id/main_tv_listen_duration_unit"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="2dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/host_color_80464652"
                android:textSize="10sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/main_tv_listen_duration_title"
                app:layout_constraintStart_toEndOf="@+id/main_tv_listen_duration_num"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="万"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/main_tv_listen_duration_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="2dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/host_color_80464652"
                android:textSize="10sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main_tv_listen_duration_unit"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="小时" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_fans_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/main_tv_fans_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:letterSpacing="-0.01"
                android:textColor="@color/host_color_464652"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/main_tv_fans_unit"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="200" />

            <TextView
                android:id="@+id/main_tv_fans_unit"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="2dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/host_color_80464652"
                android:textSize="10sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/main_tv_fans_num_title"
                app:layout_constraintStart_toEndOf="@+id/main_tv_fans_num"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="万"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/main_tv_fans_num_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="2dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:text="@string/main_fans"
                android:textColor="@color/host_color_80464652"
                android:textSize="10sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main_tv_fans_unit"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_follower_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/main_tv_follower_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:letterSpacing="-0.01"
                android:gravity="center"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/host_color_464652"
                android:textSize="13sp"
                app:layout_constraintEnd_toStartOf="@+id/main_tv_follower_unit"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="37" />

            <TextView
                android:id="@+id/main_tv_follower_unit"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="2dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/host_color_80464652"
                android:textSize="10sp"
                android:visibility="gone"
                app:layout_constraintBaseline_toBaselineOf="@+id/main_tv_follower_num"
                app:layout_constraintEnd_toStartOf="@+id/main_tv_follower_num_title"
                app:layout_constraintStart_toEndOf="@+id/main_tv_follower_num"
                tools:text="万"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/main_tv_follower_num_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="2dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:text="@string/main_follow"
                android:textColor="@color/host_color_80464652"
                android:textSize="10sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/main_tv_follower_unit"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</HorizontalScrollView>