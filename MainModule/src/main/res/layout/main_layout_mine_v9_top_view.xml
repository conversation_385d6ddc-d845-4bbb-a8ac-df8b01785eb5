<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:focusable="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:focusable="false"
        app:layout_constraintTop_toBottomOf="@+id/main_fl_head_ad">

        <Space
            android:id="@+id/main_space_top_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/host_y120"
            app:layout_constraintBottom_toTopOf="@id/main_csl_top_card"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/main_iv_top_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleType="centerCrop"
            android:importantForAccessibility="no"
            app:layout_constraintTop_toTopOf="@+id/main_space_top_bg"
            tools:background="@color/host_color_xmRed" />

        <FrameLayout
            android:id="@+id/main_scroll_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/host_title_bar_height"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/main_ll_switch_big"
            android:layout_width="110dp"
            android:layout_height="50dp"
            android:layout_marginStart="16dp"
            app:layout_constraintBottom_toTopOf="@id/main_csl_top_card"
            app:layout_constraintStart_toStartOf="parent"
            tools:background="@color/host_black">

            <LinearLayout
                android:id="@+id/main_ll_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:background="@drawable/main_bg_mine_identify_switch"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="10dp"
                android:paddingTop="4dp"
                android:paddingEnd="10dp"
                android:paddingBottom="4dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/main_mine_iv_identify_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/host_ic_n_changemode_n_line_regular_12"
                    tools:ignore="UseAppTint" />

                <TextView
                    android:id="@+id/main_mine_tv_identify_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="2dp"
                    android:text="切换为用户版"
                    android:textColor="@color/host_color_textColor_noDark"
                    android:textSize="11dp"
                    app:layout_constraintBottom_toBottomOf="@+id/main_title_bar"
                    app:layout_constraintStart_toStartOf="parent" />
            </LinearLayout>

        </FrameLayout>

        <FrameLayout
            android:id="@+id/main_csl_top_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:focusable="false"
            app:layout_constraintTop_toBottomOf="@+id/main_scroll_title_bar" />

        <ImageView
            android:id="@+id/main_iv_skeleton"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/main_color_ffffff_131313"
            android:src="@drawable/host_img_skeleton_header_mine_page"
            android:visibility="gone"
            app:layout_constraintDimensionRatio="1125:921"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/main_fl_head_ad"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="200dp"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>