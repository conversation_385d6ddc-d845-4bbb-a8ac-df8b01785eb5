<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_fl_bottom_commeny"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:layout_gravity="bottom"
    android:orientation="vertical"
    tools:background="@color/main_black">

    <ImageView
        android:id="@+id/main_iv_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginRight="4dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/main_v_right">

            <RelativeLayout
                android:id="@+id/main_srl_cover"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="16dp">

                <com.ximalaya.ting.android.framework.view.image.RoundImageView
                    android:id="@+id/main_riv_track_cover"
                    android:layout_width="38dp"
                    android:layout_height="38dp"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:scaleType="centerCrop"
                    android:src="@drawable/host_default_album"
                    app:corner_radius="20dp" />

                <com.ximalaya.ting.android.framework.view.image.RoundImageView
                    android:layout_width="38dp"
                    android:layout_height="38dp"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:scaleType="centerCrop"
                    android:src="#80000000"
                    app:corner_radius="20dp"
                    app:round_background="true" />

                <com.ximalaya.ting.android.host.view.bar.RoundProgressBar
                    android:id="@+id/main_progress_bar"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:cap="ROUND"
                    app:roundColor="#80999999"
                    app:roundProgressColor="#ffffff"
                    app:roundWidth="2dp"
                    app:style_special="LISTEN_TASK"
                    app:textIsDisplayable="false" />

                <ImageView
                    android:id="@+id/main_iv_bottom_control_bar_play_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:src="@drawable/main_ic_play_page_floating_control_selector" />

             </RelativeLayout>

            <LinearLayout
                android:id="@+id/main_layout_quora_input"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginLeft="16dp"
                android:gravity="center_vertical"
                android:paddingLeft="8dp"
                android:background="@drawable/main_bg_rect_1a15181f_radius_20"
                android:orientation="horizontal">

                <com.ximalaya.ting.android.framework.view.image.RoundImageView
                    android:id="@+id/main_iv_my_avatar"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginRight="6dp"
                    app:corner_radius="12dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/main_tv_quora_input"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:contentDescription="点击评论"
                    android:drawableLeft="@drawable/host_ic_write_new"
                    android:drawablePadding="4dp"
                    android:drawableTint="@color/host_color_e6ffffff"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:paddingRight="12dp"
                    android:text="提笔就是热评～"
                    android:textColor="@color/host_color_e6ffffff"
                    android:textSize="15sp" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/main_v_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <FrameLayout
            android:id="@+id/main_container_month_ticket"
            android:layout_width="50dp"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/main_iv_month_ticket"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginTop="7dp"
                android:layout_gravity="center_horizontal"
                android:tint="@color/framework_white_ffffff"
                android:src="@drawable/host_ic_yuepiao_n_n_line_regular_28" />

            <TextView
                android:id="@+id/main_tv_month_ticket"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|bottom"
                android:paddingBottom="9dp"
                android:text="月票"
                android:textColor="@color/main_white"
                android:textSize="10sp" />
        </FrameLayout>

            <FrameLayout
            android:id="@+id/main_container_like"
            android:layout_width="50dp"
            android:layout_height="match_parent">

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_lottie_like"
                android:layout_marginTop="4dp"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:padding="3dp"
                android:scaleType="centerInside"
                android:layout_gravity="center_horizontal"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/main_tv_like"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|bottom"
                android:paddingBottom="9dp"
                android:text="喜欢"
                android:textColor="@color/main_white"
                android:textSize="10sp" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/main_container_collect"
            android:layout_width="50dp"
            android:layout_height="match_parent">

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_lottie_collect"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:padding="3dp"
                android:scaleType="centerInside"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="4dp"
                tools:visibility="invisible" />

            <TextView
                android:id="@+id/main_tv_collect"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|bottom"
                android:paddingBottom="9dp"
                android:text="收藏"
                android:textColor="@color/main_white"
                android:textSize="10sp" />
        </FrameLayout>

            <FrameLayout
            android:id="@+id/main_container_jump_comment"
            android:layout_width="50dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp">

            <ImageView
                android:id="@+id/main_iv_jump_collect"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="7dp"
                android:tint="@color/host_color_ffffff"
                android:scaleType="fitXY"
                android:src="@drawable/host_ic_standard_comment" />

            <TextView
                android:id="@+id/main_tv_jump_comment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|bottom"
                android:paddingBottom="9dp"
                android:text="评论"
                android:textColor="@color/main_white"
                android:textSize="10sp" />
        </FrameLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>