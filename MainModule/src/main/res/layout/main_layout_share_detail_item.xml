<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:layout_width="56dp"
    android:layout_height="wrap_content">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_iv_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:scaleType="centerCrop"
        app:corner_radius="100dp"
        tools:src="@drawable/host_default_avatar_88"/>

    <TextView
        android:id="@+id/main_tv_name"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center_horizontal"
        android:layout_gravity="center_horizontal"
        android:lines="1"
        android:ellipsize="end"
        android:textColor="@color/host_color_333333_dcdcdc"
        android:textSize="10sp"
        tools:text="灰色月亮…" />

</LinearLayout>