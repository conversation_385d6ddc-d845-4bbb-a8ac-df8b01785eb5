<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingLeft="16dp"
    android:paddingRight="16dp"
    android:visibility="gone"
    tools:visibility="visible">
    <ImageView
        android:id="@+id/main_ad_image"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:src="@drawable/main_free_listen_share_icon_large"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="10dp"
        app:corner_radius="4dp" />

    <LinearLayout
        android:id="@+id/main_share_title_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/main_share_title"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:ellipsize="end"
                android:maxLines="1"
                tools:text="成功分享，再送30分钟时长"
                android:textColor="@color/host_color_444444_ffffff"
                android:textFontWeight="500"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/main_share_time_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:background="@drawable/main_bg_ffffff_1b1b1b_radius_22"
                android:paddingLeft="4dp"
                android:paddingTop="3dp"
                android:paddingRight="4dp"
                android:paddingBottom="3dp"
                android:textColor="#ff32BEAB"
                tools:text="+30分钟"
                android:textFontWeight="500"
                android:textSize="11sp"
                android:visibility="gone" />
        </LinearLayout>

        <TextView
            android:id="@+id/main_share_sub_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            tools:text="成功分享，再送30分钟时长"
            android:textColor="@color/host_color_8f8f8f_8d8d91"
            android:textSize="11sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/main_share_button"
        android:layout_width="64dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:background="@drawable/main_bg_3328ada0_radius_22"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:gravity="center"
        android:text="去分享"
        android:textColor="#ff32BEAB"
        android:textFontWeight="500"
        android:textSize="12sp" />
</LinearLayout>