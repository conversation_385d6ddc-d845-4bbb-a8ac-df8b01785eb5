<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_mot_recommend_reason"
    android:paddingBottom="30dp"
    android:background="@drawable/main_mot_recommend_bg_new"
    app:layout_constraintWidth_max="327dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/main_card_reason_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@+id/main_card_reason_close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:src="@drawable/main_mot_card_reason_title"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="6dp"
        android:layout_marginTop="15dp"
        android:textColor="@color/white"
        android:text="恭喜您！发现一本宝藏专辑"/>


    <ImageView
        android:id="@+id/main_card_reason_close"
        android:layout_width="44dp"
        android:layout_height="49dp"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:scaleType="centerInside"
        app:tint="@color/main_color_white_60"
        android:src="@drawable/main_ic_x_close_n_line_regular_24"
        />

    <com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
        android:id="@+id/main_reason_cover_layout"
        app:layout_constraintTop_toBottomOf="@+id/main_card_reason_title"
        app:layout_constraintStart_toStartOf="parent"
        app:albumCoverSize="36dp"
        app:albumCdRes="@drawable/main_ic_mot_cd_reason"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/main_card_reason_album_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/main_reason_cover_layout"
        app:layout_constraintStart_toEndOf="@+id/main_reason_cover_layout"
        app:layout_constraintEnd_toStartOf="@+id/main_card_reason_subscribe_btn"
        android:layout_marginEnd="20dp"
        app:layout_constraintBottom_toTopOf="@+id/main_card_reason_tags"
        app:layout_constraintVertical_chainStyle="packed"
        android:maxLines="1"
        android:textSize="14sp"
        android:includeFontPadding="false"
        android:layout_marginStart="8dp"
        android:textColor="@color/main_color_80ffffff"
        tools:text="5D环绕疗愈脑波"/>

    <LinearLayout
        android:id="@+id/main_card_reason_tags"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="20dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/main_card_reason_album_title"
        app:layout_constraintStart_toStartOf="@+id/main_card_reason_album_title"
        app:layout_constraintEnd_toStartOf="@+id/main_card_reason_subscribe_btn"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintBottom_toBottomOf="@+id/main_reason_cover_layout"
    />

    <TextView
        android:id="@+id/main_card_reason_subscribe_btn"
        android:layout_width="55dp"
        android:layout_height="28dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/main_reason_cover_layout"
        app:layout_constraintBottom_toBottomOf="@+id/main_reason_cover_layout"
        android:layout_marginEnd="16dp"
        android:drawableTint="@color/white"
        android:background="@drawable/main_bg_rect_19ffffff_radius_23"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:text="订阅" />

    <View
        android:id="@+id/main_card_dash"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="12dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/main_dash_19ffffff"
        app:layout_constraintTop_toBottomOf="@+id/main_reason_cover_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_card_reason_user_avatar"
        android:layout_width="18dp"
        android:layout_height="18dp"
        app:corner_radius="9dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_card_dash"
        android:src="@drawable/main_mot_card_reason_title"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"/>

    <TextView
        android:id="@+id/main_card_reason_user_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:gravity="start"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="16dp"
        android:textSize="11sp"
        android:textColor="@color/main_color_80ffffff"
        tools:text="听过本专辑的 阿米酱评论："
        app:layout_constraintTop_toTopOf="@+id/main_card_reason_user_avatar"
        app:layout_constraintBottom_toBottomOf="@+id/main_card_reason_user_avatar"
        app:layout_constraintStart_toEndOf="@+id/main_card_reason_user_avatar"
        app:layout_constraintEnd_toEndOf="parent"

    />

    <TextView
        android:id="@+id/main_card_reason_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:maxLines="3"
        android:gravity="start"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:textSize="14sp"
        android:textColor="@color/white"
        android:ellipsize="end"
        android:lineHeight="23dp"
        tools:text="从20集开始燃爆了，主角从最底层一步步爬上来的故事非常励志主角从最底层一步步爬上来的故事非常励志主角从最底层一步步爬上来的故事非常励志，强烈推荐! 主角从最底层一步步爬上来的故事非常励志"
        app:layout_constraintTop_toBottomOf="@+id/main_card_reason_user_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
    />

</androidx.constraintlayout.widget.ConstraintLayout>





