<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="28dp"
    android:paddingHorizontal="@dimen/host_x12"
    android:orientation="horizontal"
    android:background="@drawable/main_bg_y_play_playzone"
    tools:background="#80000000">

    <com.ximalaya.ting.android.host.view.text.MarqueeTextView3
        android:id="@+id/main_yellow_zone_text"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:text="余额不足，自动购买未成功"
        android:textColor="@color/host_color_ffffff_70"
        android:textSize="14sp"
        android:singleLine="true"
        android:ellipsize="marquee"
        android:marqueeRepeatLimit="marquee_forever"
        android:layout_marginLeft="4dp"
        android:gravity="center_vertical"/>

    <TextView
        android:id="@+id/main_yellow_zone_btn"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:text="立即开通"
        android:textSize="14sp"
        android:textColor="#F6BCA4"
        android:gravity="center"
        android:paddingLeft="12dp" />

    <ImageView
        android:id="@+id/main_yellow_zone_arrow"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:scaleType="fitXY"
        android:src="@drawable/host_ic_standard_arrow_right_12_12"
        android:layout_gravity="center_vertical"
        android:tint="#F6BCA4"/>

</LinearLayout>