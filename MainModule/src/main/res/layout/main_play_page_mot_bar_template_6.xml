<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_cl_buy_view_yellow_zone_y_v2"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    android:visibility="visible"
    tools:background="#10ff000f"
    tools:visibility="visible">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/main_view_bg"
        android:layout_width="0dp"
        android:layout_height="64dp"
        app:round="6dp"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/host_red" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/main_iv_logo"
        android:layout_width="wrap_content"
        tools:layout_width="50dp"
        android:layout_height="20dp"
        android:layout_marginStart="12dp"
        android:scaleType="fitXY"
        android:visibility="gone"
        tools:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/main_buy_view_yellow_zone_text"
        app:layout_constraintTop_toTopOf="@+id/main_buy_view_yellow_zone_text"
        tools:background="#90889911" />

    <TextView
        android:id="@+id/main_buy_view_yellow_zone_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textColor="@color/host_white"
        app:layout_constraintVertical_chainStyle="packed"
        android:textFontWeight="500"
        android:textSize="14sp"
        app:layout_goneMarginBottom="12dp"
        app:layout_goneMarginStart="12dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/main_buy_view_yellow_zone_sub_text"
        app:layout_constraintStart_toEndOf="@+id/main_iv_logo"
        app:layout_constraintEnd_toStartOf="@+id/main_fl_btn_container"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="会员音质特权体验会员音质特权体验会员音质特权体验会员音质特权体验" />

    <TextView
        android:id="@+id/main_buy_view_yellow_zone_sub_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="12dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:visibility="visible"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:alpha="0.6"
        android:textColor="@color/host_white"
        android:textFontWeight="400"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/main_fl_btn_container"
        app:layout_constraintTop_toBottomOf="@+id/main_buy_view_yellow_zone_text"
        tools:text="会员音质特权体验会员音质特权体验会员音质特权体验会员音质特权体验" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/main_fl_btn_container"
        android:layout_width="92dp"
        android:layout_height="52dp"
        android:scaleType="fitXY"
        tools:background="#FCE9E1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/main_buy_view_yellow_zone_btn"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:gravity="center"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textColor="#2E3441"
        android:textStyle="bold"
        android:fontFamily="sans-serif-light"
        android:textSize="12dp"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="@+id/main_fl_btn_container"
        app:layout_constraintBottom_toBottomOf="@+id/main_fl_btn_container"
        app:layout_constraintEnd_toEndOf="@+id/main_fl_btn_container"
        app:layout_constraintTop_toTopOf="@+id/main_fl_btn_container"
        tools:text="首页"
        tools:textColor="#2E3441"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/main_buy_view_yellow_zone_btn_close"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_gravity="top"
        android:paddingHorizontal="4dp"
        android:layout_marginTop="4dp"
        android:paddingVertical="4dp"
        android:scaleType="fitXY"
        android:src="@drawable/host_ic_x_close_n_line_regular_16"
        android:tint="@color/main_color_ffffff_alpha_50"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />
    
</androidx.constraintlayout.widget.ConstraintLayout>