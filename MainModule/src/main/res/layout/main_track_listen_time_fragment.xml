<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_listen_time_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/host_color_ffffff_1b1b1b">

    <ImageView
        android:id="@+id/main_top_image"
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:scaleType="centerCrop"
        tools:src="@drawable/main_free_listen_time_fragment_default_image" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/main_app_bar"
            android:layout_width="match_parent"
            android:layout_height="126dp"
            android:background="@android:color/transparent"
            app:elevation="0dp">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/main_ctb_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/transparent"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/main_tb_bar"
                    android:layout_width="match_parent"
                    android:layout_height="88dp"
                    app:layout_collapseMode="parallax" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <com.ximalaya.ting.android.main.view.NestedScrollViewWithListen
            android:id="@+id/main_listen_time_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/main_bg_ffffff_1b1b1b_radius_20_20_0_0"
                android:paddingTop="16dp">

                <LinearLayout
                    android:id="@+id/main_reward_time_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/main_total_listen_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/main_12corner_f0f0f0_282828"
                        android:paddingLeft="12dp"
                        android:paddingTop="4dp"
                        android:paddingRight="12dp"
                        android:paddingBottom="4dp"
                        android:text="本月累计获得xx分钟"
                        android:textColor="#8d8d91"
                        android:textFontWeight="400"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/main_all_day_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="14dp"
                        android:text="全天畅听中"
                        android:textColor="@color/main_color_ff0DD09F_ff20AF8B"
                        android:textFontWeight="600"
                        android:textSize="28sp"
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/main_remain_time_text_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="25dp"
                            android:layout_marginRight="8dp"
                            android:text="剩余"
                            android:textColor="@color/host_color_textColor"
                            android:textFontWeight="400"
                            android:textSize="13sp" />

                        <FrameLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="4dp">
                            <TextView
                                android:id="@+id/main_remain_time_hour"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/main_color_ff0DD09F_ff20AF8B"
                                android:textSize="44sp"
                                tools:text="01" />
                        </FrameLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="25dp"
                            android:layout_marginRight="10dp"
                            android:text="时"
                            android:textColor="@color/host_color_textColor"
                            android:textFontWeight="400"
                            android:textSize="13sp" />

                        <FrameLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="4dp">
                            <TextView
                                android:id="@+id/main_remain_time_minutes"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/main_color_ff0DD09F_ff20AF8B"
                                android:textSize="44sp"
                                tools:text="10" />
                        </FrameLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="25dp"
                            android:layout_marginRight="10dp"
                            android:text="分"
                            android:textColor="@color/host_color_textColor"
                            android:textFontWeight="400"
                            android:textSize="13sp" />

                        <FrameLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="4dp">
                            <TextView
                                android:id="@+id/main_remain_time_seconds"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/main_color_ff0DD09F_ff20AF8B"
                                android:textSize="44sp"
                                tools:text="22" />
                        </FrameLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="25dp"
                            android:text="秒"
                            android:textColor="@color/host_color_textColor"
                            android:textFontWeight="400"
                            android:textSize="13sp" />
                    </LinearLayout>

                    <RelativeLayout
                        android:id="@+id/main_time_line_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <HorizontalScrollView
                            android:id="@+id/main_time_line_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:scrollbars="none"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <com.ximalaya.ting.android.main.playpage.view.FreeListenTimeLineView
                                android:id="@+id/main_time_line_view"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </HorizontalScrollView>

                        <View
                            android:id="@+id/main_time_line_left_blur"
                            android:layout_width="26dp"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/main_time_line_layout"
                            android:layout_alignBottom="@id/main_time_line_layout"
                            android:background="@drawable/main_free_listen_time_line_left_blur"
                            android:visibility="gone" />

                        <View
                            android:id="@+id/main_time_line_right_blur"
                            android:layout_width="26dp"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/main_time_line_layout"
                            android:layout_alignBottom="@id/main_time_line_layout"
                            android:layout_alignParentRight="true"
                            android:background="@drawable/main_free_listen_time_line_right_blur"
                            android:visibility="gone" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/main_scene_tips_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginRight="20dp"
                        android:background="@drawable/host_6corner_f8f8fa_282828"
                        android:paddingLeft="14dp"
                        android:paddingTop="12dp"
                        android:paddingRight="14dp"
                        android:paddingBottom="12dp">

                        <TextView
                            android:id="@+id/main_scene_tips"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="提前领收听时长，听书不中断啦啊啊啦啦啦啦啦啦啦阿里啦啦啦"
                            android:textColor="@color/host_color_textColor"
                            android:textFontWeight="400"
                            android:textSize="13dp" />

                        <ImageView
                            android:id="@+id/main_scene_tips_icon"
                            android:layout_width="wrap_content"
                            android:layout_height="14dp"
                            android:layout_marginTop="2dp"
                            android:scaleType="centerCrop"
                            android:visibility="gone"
                            tools:src="@drawable/main_scene_tips_icon"
                            tools:visibility="visible" />
                    </RelativeLayout>

                </LinearLayout>

                <TextView
                    android:id="@+id/main_watch_video_tips"
                    android:layout_below="@id/main_reward_time_container"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="20dp"
                    android:background="@drawable/main_22_corner_ff0dd09f"
                    android:gravity="center"
                    android:text="看视频领时长"
                    android:textColor="#ffFFFFFF"
                    android:textFontWeight="600"
                    android:textSize="17sp" />

                <!-- 惊喜任务模块 -->
                <com.ximalaya.ting.android.main.view.ValuableTaskView
                    android:id="@+id/main_valuable_task_view"
                    android:layout_below="@id/main_watch_video_tips"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <LinearLayout
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_below="@id/main_valuable_task_view"
                    android:id="@+id/main_reward_time_task_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:layout_marginBottom="20dp"
                    android:paddingBottom="7dp"
                    android:background="@drawable/main_bg_1428ada0_1a28ada0_radius_8"
                    android:orientation="vertical">
                    <TextView
                        android:id="@+id/main_task_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="8dp"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textFontWeight="500"
                        android:text="做任务领更多时长"
                        android:textColor="@color/host_color_666666_dcdcdc"
                        android:textSize="12sp" />

                    <View
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:layout_marginBottom="9dp"
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/main_color_f0f0f0_80000000" />

                    <!--换端-->
                    <include
                        android:layout_marginBottom="9dp"
                        android:layout_marginTop="9dp"
                        android:id="@+id/main_listen_dp_ad_container"
                        layout="@layout/main_listen_dp_ad_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <!-- 分享-->
                    <include
                        android:layout_marginBottom="9dp"
                        android:layout_marginTop="9dp"
                        android:id="@+id/main_listen_share_container"
                        layout="@layout/main_listen_share_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                    android:id="@+id/main_tips_animation"
                    android:layout_width="59dp"
                    android:layout_height="59dp"
                    android:layout_alignTop="@id/main_watch_video_tips"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="15dp"
                    android:visibility="gone"
                    app:lottie_fileName="lottie/main_free_listen_float_tip.json"
                    app:lottie_loop="true"
                    tools:visibility="visible" />

                <LinearLayout
                    android:id="@+id/main_bottom_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/main_reward_time_task_container"
                    android:background="@color/main_color_f7f9fc_111111"
                    android:paddingBottom="50dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/main_listen_other_method"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <include
                            layout="@layout/main_listen_other_method_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                        <include
                            layout="@layout/main_listen_other_method_layout2_fragment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />
                    </LinearLayout>

                    <com.ximalaya.ting.android.main.view.FreeListenAlbumTabView
                        android:id="@+id/main_album_list_module_new"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="32dp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <LinearLayout
                        android:id="@+id/main_bottom_rule_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="14dp"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/main_customer_service_entry"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="我要投诉"
                            android:textColor="@color/host_color_bbbbbb_66666b"
                            android:textSize="15sp"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/main_rule_bottom"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="活动规则"
                            android:textColor="@color/host_color_bbbbbb_66666b"
                            android:textSize="15sp" />

                    </LinearLayout>
                </LinearLayout>

            </RelativeLayout>
        </com.ximalaya.ting.android.main.view.NestedScrollViewWithListen>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <RelativeLayout
        android:id="@+id/main_top_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="52dp"
        android:layout_marginBottom="12dp">

        <ImageView
            android:id="@+id/main_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:src="@drawable/main_ic_back_img_new" />

        <TextView
            android:id="@+id/main_top_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:alpha="0.0"
            android:text="免费领时长"
            android:textColor="@color/host_color_131313_ffffff"
            android:textFontWeight="500"
            android:textSize="17dp" />

        <TextView
            android:id="@+id/main_rule"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/main_back"
            android:layout_alignBottom="@id/main_back"
            android:layout_alignParentRight="true"
            android:layout_marginRight="16dp"
            android:gravity="center"
            android:text="规则"
            android:textColor="#3d3d3d"
            android:textFontWeight="400"
            android:textSize="15dp" />
    </RelativeLayout>

    <View
        android:id="@+id/main_divide_line"
        android:layout_width="match_parent"
        android:visibility="gone"
        android:layout_height="0.5dp"
        android:layout_marginTop="87dp"
        android:background="@color/main_color_dcdcdc_000000" />

    <!-- 底部悬浮会员栏 -->
    <RelativeLayout
        android:id="@+id/main_vip_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/main_color_ffffff_1B1B1B"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingBottom="36dp"
        android:visibility="gone"
        tools:visibility="visible">

        <View
            android:id="@+id/main_vip_divide_line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/host_color_f0f0f0_000000" />

        <LinearLayout
            android:id="@+id/main_vip_content_layout"
            android:layout_below="@id/main_vip_divide_line"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginTop="16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/main_bg_fffdf3ee_coner_8"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/main_vip_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:src="@drawable/main_free_listen_vip_icon" />

            <TextView
                android:id="@+id/main_vip_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:alpha="0.7"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="无需攒时长，享更多会员特权"
                android:textColor="@color/main_color_582C23_ffE4BAA6"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/main_vip_open_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:drawableEnd="@drawable/main_free_listen_vip_arrow"
                android:drawableTint="@color/main_color_582C23_ffE4BAA6"
                android:gravity="center"
                android:paddingLeft="12dp"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                android:text="开通会员"
                android:textColor="@color/main_color_582C23_ffE4BAA6"
                android:textSize="11sp" />
        </LinearLayout>

        <!-- 价格气泡，悬浮在按钮右上角，单独放置 -->
        <TextView
            android:id="@+id/main_vip_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="16dp"
            android:layout_alignParentRight="true"
            android:layout_alignTop="@id/main_vip_content_layout"
            android:layout_marginTop="-9dp"
            android:background="@drawable/main_free_listen_vip_price_bg"
            android:includeFontPadding="false"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:text="¥0.43/日"
            android:textColor="#ffFFFFFF"
            android:textSize="10sp" />
    </RelativeLayout>
</RelativeLayout>