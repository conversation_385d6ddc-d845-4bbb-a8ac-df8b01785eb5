<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/main_bg_rect_ffffff_radius_10">


    <ImageView
        android:id="@+id/main_iv_quotation"
        android:layout_width="54dp"
        android:layout_height="39dp"
        android:layout_marginLeft="7dp"
        android:layout_marginTop="20dp"
        android:src="@drawable/main_comment_poster_quotation_new" />

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_iv_avatar"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginLeft="21dp"
        android:layout_marginTop="45dp"
        android:layout_marginRight="4dp"
        android:src="@drawable/host_default_avatar_88"
        app:corner_radius="70dp" />

    <TextView
        android:id="@+id/main_tv_author"
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:layout_alignTop="@id/main_iv_avatar"
        android:gravity="center_vertical"
        android:layout_toRightOf="@id/main_iv_avatar"
        android:textColor="#000"
        android:textSize="13sp"
        tools:text="惟与卿乌瓦白" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:layout_alignBottom="@id/main_tv_author"
        android:layout_alignTop="@id/main_iv_avatar"
        android:gravity="center_vertical"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="24dp"
        android:layout_toRightOf="@id/main_tv_author"
        android:lines="1"
        android:text="发表了一条评价"
        android:textColor="#80000000"
        android:textSize="11sp" />

    <ImageView
        android:id="@+id/main_iv_quotation_left"
        android:layout_width="22dp"
        android:layout_height="16dp"
        android:layout_below="@+id/main_iv_avatar"
        android:layout_alignParentStart="true"
        android:layout_marginStart="24dp"
        android:layout_marginTop="17dp"
        android:src="@drawable/main_comment_poster_quotation"
        android:visibility="invisible"
        tools:visibility="invisible" />

    <RelativeLayout
        android:id="@+id/main_rl_score"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_iv_avatar"
        android:layout_marginTop="17dp"
        android:layout_marginBottom="6dp">

        <TextView
            android:id="@+id/main_tv_score_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_centerVertical="true"
            android:text="打分："
            android:textColor="#80000000"
            android:textSize="11sp" />

        <RatingBar
            android:id="@+id/main_rb_comment_album"
            style="@style/main_comment_poster_rating_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="2dp"
            android:layout_toEndOf="@+id/main_tv_score_hint"
            android:isIndicator="true"
            android:numStars="5"
            tools:rating="3.5" />

        <ImageView
            android:id="@+id/main_iv_new_score_emotion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24dp"
            android:layout_centerVertical="true"
            android:src="@drawable/main_ic_album_rate_recommend"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/main_tv_new_score_recommend_choice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/main_iv_new_score_emotion"
            android:layout_marginLeft="2dp"
            android:fontFamily="sans-serif-light"
            android:textStyle="bold"
            android:textColor="@color/host_color_ThinTextColor"
            android:textSize="15sp"
            android:text="推荐"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/main_tv_new_score_recommend_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/main_tv_new_score_recommend_choice"
            android:layout_marginLeft="8dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="11sp"
            android:textColor="@color/host_color_ThinTextColor"
            android:text="超赞！请大家一定要听呀！"
            android:visibility="gone"/>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/main_v_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_rl_score"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:layout_marginBottom="16dp"
        android:minHeight="80dp"
        tools:layout_height="100dp">

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:ellipsize="end"
            android:textColor="@color/main_color_white"
            android:textSize="14sp"
            tools:text="所有的成功都不是一蹴而就的，要有长久的努力与坚持才能成为一个自己想成为的理想模样。所有的成功都不是一蹴而就的，要有长久的努力与坚持才能成为一个自己想成为的理想模样。所有的成功都不是一蹴而就的，要有长久的努力与坚持才能成为一个自长久的努力与坚持才能才能才能才能" />
    </RelativeLayout>

    <View
        android:id="@+id/main_v_divider"
        android:layout_width="match_parent"
        android:layout_height="1.5dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_below="@id/main_v_content"
        android:background="@drawable/main_dash_dfdfdf"
        android:layout_marginBottom="26dp"
        android:layerType="software"
        tools:visibility="visible" />

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_below="@+id/main_v_divider"
        android:layout_alignParentStart="true"
        android:layout_marginStart="31dp"
        android:background="@drawable/main_comment_poster_album_bg" />

    <RelativeLayout
        android:id="@+id/main_v_track_cover"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_below="@id/main_v_divider"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="16dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_iv_track_cover"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_centerInParent="true"
            android:src="@drawable/host_default_album"
            app:corner_radius="6dp" />

        <TextView
            android:id="@+id/main_tv_play_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:background="@drawable/main_comment_poster_shadow"
            android:drawableLeft="@drawable/main_ic_recommend_album_in_module_play_count_new"
            android:drawablePadding="2dp"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingLeft="4dp"
            android:paddingTop="3dp"
            android:paddingBottom="3dp"
            android:textColor="@color/main_white"
            android:textSize="11sp"
            tools:text="1340" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/main_rl_qr_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/main_v_track_cover"
        android:layout_alignParentRight="true"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="26dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_share_poster_iv_qr_code"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="2dp"
            android:background="@drawable/main_bg_rect_white_radius_4"
            android:contentDescription="@string/main_content_description_qrcode"
            android:padding="3dp"
            android:src="@drawable/main_image_weixin_code"
            app:corner_radius="4dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/main_share_poster_iv_qr_code"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="3dp"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="长按查看专辑"
            android:textColor="#80000000"
            android:textSize="8sp" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/main_v_track_cover"
        android:layout_marginRight="8dp"
        android:layout_toLeftOf="@+id/main_rl_qr_layout"
        android:layout_toRightOf="@id/main_v_track_cover"
        android:gravity="center_vertical"
        android:minHeight="66dp">

        <TextView
            android:id="@+id/main_tv_album_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="#000000"
            android:textSize="13sp"
            tools:text="什么面条这么神奇？相声圈竟然用它收买人" />

        <RatingBar
            android:id="@+id/main_rb_comment_album_little"
            style="@style/main_comment_poster_album_rating_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/main_tv_album_title"
            android:layout_marginStart="2dp"
            android:layout_marginTop="7dp"
            android:isIndicator="true"
            android:numStars="5"
            tools:rating="3.5" />

        <android.widget.TextView
            android:id="@+id/main_tv_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/main_tv_album_title"
            android:layout_marginStart="4dp"
            android:layout_marginTop="5dp"
            android:layout_toEndOf="@+id/main_rb_comment_album_little"
            android:gravity="center"
            android:textColor="@color/main_color_ffd389"
            android:textSize="12sp"
            tools:text="7.3" />

        <LinearLayout
            android:id="@+id/main_ll_album_new_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/main_tv_album_title"
            android:layout_marginTop="4dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:id="@+id/main_iv_score_decoration_left"
                android:layout_width="5.6dp"
                android:layout_height="9.8dp"
                android:src="@drawable/main_ic_album_score_decoration_left" />

            <TextView
                android:id="@+id/main_tv_score_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="2dp"
                android:textColor="#FF4463"
                android:textSize="13.3dp"
                android:text="百听不厌" />

            <ImageView
                android:id="@+id/main_iv_score_decoration_right"
                android:layout_width="5.6dp"
                android:layout_height="9.8dp"
                android:src="@drawable/main_ic_album_score_decoration_right"
                android:layout_marginRight="6dp" />

            <TextView
                android:id="@+id/main_tv_album_new_score"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="11sp"
                android:textColor="@color/host_color_lightTextColor"
                tools:text="推荐值 85%" />

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>