<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:id="@+id/main_vg_video_player_container"
        android:layout_width="1px"
        android:layout_height="1px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
    />


    <LinearLayout
        android:id="@+id/main_video_loading_frame"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.ximalaya.ting.android.main.playpage.playy.view.PlayLoadingView
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:background="@drawable/main_ic_common_loading"
        />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="「视频」正在加载中…"
            android:textSize="13sp"
            android:layout_marginStart="5dp"
            android:textColor="@color/main_color_white"
        />
    </LinearLayout>

    <ImageView
        android:id="@+id/main_video_ai_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/main_video_ai_tag"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"
        android:layout_marginTop="130dp"
    />

    <TextView
        android:id="@+id/main_duanju_beian"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:textColor="@color/main_color_80ffffff"
        android:visibility="gone"
        android:layout_marginEnd="8dp"
        android:textSize="14sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <View
        android:id="@+id/main_video_top_mask"
        android:layout_width="0dp"
        android:layout_height="200dp"
        android:visibility="gone"
        android:background="@drawable/main_gradient_131313_top_down"
        app:layout_constraintTop_toTopOf="@id/main_vg_video_player_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
    />


    <View
        android:id="@+id/main_video_bottom_mask"
        android:layout_width="match_parent"
        android:layout_height="336dp"
        android:visibility="gone"
        android:background="@drawable/main_bg_gradient_video_bottom"
        app:layout_constraintBottom_toTopOf="@+id/main_video_bottom_mask_2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible"/>

    <View
        android:id="@+id/main_video_bottom_mask_2"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:visibility="gone"
        android:background="@color/main_color_131313"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible"/>

    <!--    android:background="@drawable/main_bg_gradient_center_00000000_66000000"-->


    <View
        android:id="@+id/main_video_full_mask_b"
        android:layout_width="0dp"
        android:layout_height="400dp"
        android:visibility="gone"
        android:background="@drawable/main_bg_gradient_00000000_66000000"
        app:layout_constraintBottom_toBottomOf="@id/main_vg_video_player_container"
        app:layout_constraintLeft_toLeftOf="@id/main_vg_video_player_container"
        app:layout_constraintRight_toRightOf="@id/main_vg_video_player_container"/>


    <ImageView
        android:id="@+id/main_iv_error"
        android:layout_width="80dp"
        android:layout_height="80dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_vg_video_player_container"
        app:layout_constraintBottom_toTopOf="@id/main_tv_error"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginBottom="8dp"
        android:src="@drawable/main_img_video_error"
    />

    <TextView
        android:id="@+id/main_tv_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_iv_error"
        app:layout_constraintBottom_toBottomOf="@id/main_vg_video_player_container"
        android:textColor="@color/main_white"
        android:textSize="14sp"
        android:text="当前视频无法播放"
    />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/main_group_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="main_tv_error,main_iv_error"
        android:visibility="invisible"
    />

    <TextView
        android:id="@+id/main_tv_no_network"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_vg_video_player_container"
        app:layout_constraintBottom_toTopOf="@id/main_tv_refresh_btn"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginBottom="8dp"
        android:text="网络未连接，请检查网络设置"
        android:textColor="@color/main_white"
        android:textSize="16sp"
    />

    <TextView
        android:id="@+id/main_tv_refresh_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_tv_no_network"
        app:layout_constraintBottom_toBottomOf="@id/main_vg_video_player_container"
        android:minWidth="114dp"
        android:minHeight="38dp"
        android:padding="8dp"
        android:gravity="center"
        android:text="刷新重试"
        android:textColor="@color/main_white"
        android:textSize="13sp"
        android:background="@drawable/main_bg_rect_1dp_stroke_80ffffff_corner100"
    />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/main_group_no_network"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="main_tv_no_network,main_tv_refresh_btn"
        android:visibility="gone"
    />


    <ImageView
        android:id="@+id/main_iv_play_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/main_vg_video_player_container"
        app:layout_constraintBottom_toBottomOf="@id/main_vg_video_player_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:src="@drawable/main_btn_video_play_detail_play"
        android:alpha="0.8"
        android:visibility="invisible"
        android:contentDescription="@string/main_play"
    />

    <View
        android:id="@+id/main_rl_commercial_info_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#e6000000"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/main_vg_video_player_container"
        app:layout_constraintBottom_toBottomOf="@id/main_vg_video_player_container"
        app:layout_constraintLeft_toLeftOf="@id/main_vg_video_player_container"
        app:layout_constraintRight_toRightOf="@id/main_vg_video_player_container"/>

    <RelativeLayout
        android:id="@+id/main_rl_commercial_info"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/main_vg_video_player_container"
        app:layout_constraintBottom_toBottomOf="@+id/main_vg_video_player_container"
        app:layout_constraintLeft_toLeftOf="@id/main_vg_video_player_container"
        app:layout_constraintRight_toRightOf="@id/main_vg_video_player_container"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/main_group_commercial_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="main_rl_commercial_info,main_rl_commercial_info_bg"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/main_bottom_progress_guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_end="50dp"
        android:orientation="horizontal"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/main_group_fullscreen_widgets"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="main_video_full_mask,main_video_full_mask_b"
    />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/main_vertical_video_mask"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="main_video_top_mask,main_video_bottom_mask,main_video_bottom_mask_2"
    />


    <ImageView
        android:id="@+id/main_play_video_landscape_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginEnd="8dp"
        android:visibility="gone"
        android:src="@drawable/main_ic_video_landscapescreen_n_n_line_32"
    />

    <FrameLayout
        android:id="@+id/main_seek_bar_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <com.ximalaya.ting.android.host.view.ScaleableSeekBar
            android:id="@+id/main_seek_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:contentDescription="播放进度条"
            android:max="100"
            android:maxHeight="2dp"
            android:minHeight="2dp"
            android:progress="0"
            android:progressDrawable="@drawable/host_audio_play_page_y_seekbar"
            android:thumbOffset="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:thumb="@drawable/main_play_page_v2_seekbar_thumb"
            app:scaleableSeekBar_ProgressScaleResource="@drawable/host_audio_play_page_y_seekbar"
            app:scaleableSeekBar_ThumbScaleResource="@drawable/main_play_page_y_seekbar_thumb_scale"
            app:scaleableSeekBar_KeyPointColor="@color/main_color_ccffffff"
            app:scaleableSeekBar_ProgressScaleHeight="6dp"
            app:scaleableSeekBar_RestoreTimeMS="500"/>
    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>