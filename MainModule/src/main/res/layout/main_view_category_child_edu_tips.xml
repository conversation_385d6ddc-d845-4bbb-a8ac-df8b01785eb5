<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/main_recommend_card_shadow_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:background="@drawable/main_bg_category_fiction_info_setting_tips"
        tools:visibility="visible"
        android:id="@+id/main_ll_already_set_info">

        <ImageView
            android:id="@+id/main_iv_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="14dp"
            android:contentDescription="@string/main_content_description_edit"
            android:src="@drawable/main_ic_category_recommend_edited" />

        <TextView
            android:id="@+id/main_tv_tips"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:textColor="@color/main_color_999999_888888"
            android:textSize="12sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginRight="5dp"
            tools:text="下面是为 0~2岁儿童 定制的专属内容" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/main_bg_category_child_info_setting_tips"
        android:id="@+id/main_cl_not_set_info"
        android:visibility="gone">

        <ImageView
            android:id="@+id/main_img_child_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:contentDescription="@string/main_content_description_boy_category"
            android:src="@drawable/main_ic_choose_grade"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/main_tv_set_child_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_weight="1"
            android:text="@string/main_choose_grade"
            android:textColor="#ffffff"
            android:textSize="13sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            app:layout_constraintBottom_toTopOf="@id/main_tv_desc"
            app:layout_constraintStart_toEndOf="@id/main_img_child_info"
            app:layout_constraintTop_toTopOf="@id/main_img_child_info" />

        <TextView
            android:id="@+id/main_tv_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/main_recommend_depend_on_grade"
            android:textColor="#99ffffff"
            android:textSize="11sp"
            app:layout_constraintBottom_toBottomOf="@id/main_img_child_info"
            app:layout_constraintStart_toStartOf="@id/main_tv_set_child_info"
            app:layout_constraintTop_toBottomOf="@id/main_tv_set_child_info" />

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginRight="16dp"
            android:src="@drawable/main_arrow_right_orange"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>