<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_main_hight_top_lay"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/main_hight_top_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:contentDescription="@string/main_iv_cd_close"
        android:paddingBottom="16dp"
        android:paddingTop="8dp"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:src="@drawable/main_ad_boardside_close"
        tools:visibility="visible"
        android:visibility="gone" />

    <com.ximalaya.ting.android.host.view.image.RatioImageView
        android:id="@+id/main_hight_top_img"
        android:layout_below="@id/main_hight_top_close"
        android:layout_marginLeft="32dp"
        android:layout_marginRight="32dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:contentDescription="@string/main_iv_cd_ad_icon"
        android:scaleType="fitXY"
        app:ratio="5"
        tools:src="@drawable/host_banner_defual_bg" />

    <ImageView
        android:id="@+id/main_hight_top_ad_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignRight="@id/main_hight_top_img"
        android:layout_below="@id/main_hight_top_img"
        android:layout_marginTop="2dp"/>

    <ImageView
        android:id="@+id/main_hight_light_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="-13dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:scaleX="1.2"
        android:scaleY="1.2"
        android:rotation="35"
        android:layout_alignTop="@id/main_hight_top_img"
        android:layout_alignBottom="@id/main_hight_top_img"
        android:src="@drawable/main_hight_light_icon" />

</RelativeLayout>