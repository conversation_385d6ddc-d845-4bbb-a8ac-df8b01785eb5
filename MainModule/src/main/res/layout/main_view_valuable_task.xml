<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/valuable_task_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/main_bg_ffffff_fff6f8_radius_8"
    android:orientation="vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="6dp"
        android:layout_marginTop="3dp"
        android:src="@drawable/main_high_price_task_icon" />
    <!-- 标题栏 -->
    <RelativeLayout
        android:id="@+id/main_title_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginRight="11dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/valuable_task_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="惊喜任务"
            android:textColor="@color/host_color_666666_dcdcdc"
            android:textFontWeight="500"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/valuable_task_countdown_pre"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/valuable_task_countdown"
            android:text="限时"
            android:textColor="@color/host_color_666666_dcdcdc"
            android:textFontWeight="400"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/valuable_task_countdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="4dp"
            android:text="23 : 59 :59"
            android:textColor="#ffFA5878"
            android:textFontWeight="500"
            android:textSize="11sp"
            android:visibility="gone"
            tools:visibility="visible" />

    </RelativeLayout>

    <View
        android:id="@+id/main_task_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@id/main_title_container"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8.5dp"
        android:layout_marginRight="16dp"
        android:background="@color/host_color_ffF3E9EC_80000000" />

    <!-- 任务内容区域 -->
    <RelativeLayout
        android:id="@+id/main_task_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_task_divider"
        android:gravity="center_vertical"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="15.6dp">

        <!-- 任务图标 -->
        <com.ximalaya.ting.android.framework.view.image.FlexibleRoundImageView
            android:id="@+id/valuable_task_icon"
            app:flexible_round_corner="all"
            app:flexible_round_corner_radius="4.5dp"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_centerVertical="true"
            android:scaleType="centerCrop" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="16dp"
            android:layout_toStartOf="@id/valuable_task_button"
            android:layout_toEndOf="@id/valuable_task_icon">

            <!-- 任务信息 -->
            <LinearLayout
                android:id="@+id/valuable_task_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/valuable_task_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/host_color_444444_ffffff"
                    android:textFontWeight="500"
                    android:textSize="14sp"
                    tools:text="下载并体验某应用啦啦啦啦啦啦啦啦啦" />

                <TextView
                    android:id="@+id/valuable_task_max_reward"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/main_31_corner_0dfa5878"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="3dp"
                    android:layout_marginLeft="4dp"
                    android:text="+60分钟"
                    android:textColor="#ffFF5073"
                    android:textFontWeight="500"
                    android:textSize="11sp"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

            <TextView
                android:id="@+id/valuable_task_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/valuable_task_info"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/host_color_8f8f8f_8d8d91"
                android:textSize="11sp"
                tools:text="打开后完成登陆可领取奖励啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊" />
        </RelativeLayout>

        <!-- 任务按钮 -->
        <TextView
            android:id="@+id/valuable_task_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/main_20_corner_33fa5878"
            android:gravity="center"
            android:paddingHorizontal="14dp"
            android:paddingVertical="6dp"
            android:text="去下载"
            android:textColor="#ffFA5878"
            android:textFontWeight="500"
            android:textSize="12sp" />

    </RelativeLayout>
</RelativeLayout>