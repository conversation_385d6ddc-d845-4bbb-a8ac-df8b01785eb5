<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/main_border"
        android:layout_width="0dp"
        android:layout_height="10dp"
        android:background="@color/framework_bg_color"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/main_whole_album_subsidy_exchange_free_listen_icon"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_marginLeft="5dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:src="@drawable/main_subsidy_coin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_border" />


    <TextView
        android:id="@+id/main_whole_album_subsidy_exchange_free_listen_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="15dp"
        android:singleLine="true"
        android:textColor="@color/main_color_333333_cfcfcf"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/main_whole_album_subsidy_exchange_free_listen_subtitle"
        app:layout_constraintLeft_toRightOf="@id/main_whole_album_subsidy_exchange_free_listen_icon"
        app:layout_constraintRight_toLeftOf="@id/main_whole_album_subsidy_exchange_free_listen_button"
        app:layout_constraintTop_toBottomOf="@id/main_border"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="40津贴兑换24小时免费畅听" />

    <TextView
        android:id="@+id/main_whole_album_subsidy_exchange_free_listen_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginRight="15dp"
        android:singleLine="true"
        android:textColor="@color/host_color_666666_cfcfcf"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/main_whole_album_subsidy_exchange_free_listen_title"
        app:layout_constraintRight_toLeftOf="@id/main_whole_album_subsidy_exchange_free_listen_count_down"
        app:layout_constraintTop_toBottomOf="@id/main_whole_album_subsidy_exchange_free_listen_title"
        tools:text="您账户内有60津贴" />


    <TextView
        android:id="@+id/main_whole_album_subsidy_exchange_free_listen_button"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="3dp"
        android:background="@drawable/main_orange_corner20"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="兑换免费听"
        android:textColor="#ffffffff"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/main_whole_album_subsidy_exchange_free_listen_count_down"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_border"
        app:layout_constraintVertical_chainStyle="packed" />


    <TextView
        android:id="@+id/main_whole_album_subsidy_exchange_free_listen_count_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="#F96142"
        android:textSize="11sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="@id/main_whole_album_subsidy_exchange_free_listen_button"
        app:layout_constraintTop_toBottomOf="@id/main_whole_album_subsidy_exchange_free_listen_button"
        tools:text="距结束 2天12:24:34" />


</androidx.constraintlayout.widget.ConstraintLayout>