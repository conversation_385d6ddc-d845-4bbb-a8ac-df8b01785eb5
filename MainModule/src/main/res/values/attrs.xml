<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <declare-styleable name="TimeAxisView">
        <attr name="axis_image_src" format="reference" />
        <attr name="line_point">
            <enum name="main_both" value="0" />
            <enum name="main_up" value="1" />
            <enum name="main_down" value="2" />
        </attr>
        <attr name="axis_image_mode">
            <enum name="main_top" value="0" />
            <enum name="main_center" value="1" />
            <enum name="main_bottom" value="2" />
        </attr>
        <attr name="line_color" format="color" />
        <attr name="line_thinkness" format="dimension" />
    </declare-styleable>

    <declare-styleable name="main_HorizontalMoreDataView">
        <attr name="main_moreViewTextSize" format="dimension"/>
        <attr name="main_moreViewText" format="string"/>
        <attr name="main_moreViewJumpText" format="string"/>
        <attr name="main_moreViewDiverWidth" format="integer"/>
        <attr name="main_moreViewHideMode" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="main_OverScrollLayout">
        <attr name="main_canOverScroll" format="boolean" />
        <attr name="main_animDuration" format="integer" />
        <attr name="main_overScrollSize" format="dimension" />
        <attr name="main_overScrollHeight" format="dimension" />
        <attr name="main_overScrollStateChangeSize" format="dimension" />
        <attr name="main_damping" format="float" />
        <attr name="main_textDamping" format="float" />
        <attr name="main_overScrollText" format="string" />
        <attr name="main_overScrollChangeText" format="string" />
        <attr name="main_textSize" format="dimension" />
        <attr name="main_textColor" format="reference|color" />
        <attr name="main_overScrollColor" format="reference|color" />
    </declare-styleable>

    <declare-styleable name="main_two_image_SwitchButton" tools:ignore="ResourceName">
        <attr name="kswThumbDrawable" format="reference"/>
        <attr name="kswThumbColor" format="color|reference"/>
        <attr name="kswThumbMargin" format="dimension|reference"/>
        <attr name="kswThumbMarginTop" format="dimension|reference"/>
        <attr name="kswThumbMarginBottom" format="dimension|reference"/>
        <attr name="kswThumbMarginLeft" format="dimension|reference"/>
        <attr name="kswThumbMarginRight" format="dimension|reference"/>
        <attr name="kswThumbWidth" format="dimension|reference"/>
        <attr name="kswThumbHeight" format="dimension|reference"/>
        <attr name="kswThumbRadius" format="dimension|reference"/>
        <attr name="kswBackRadius" format="dimension|reference"/>
        <attr name="kswBackDrawable" format="reference"/>
        <attr name="kswBackColor" format="color|reference"/>
        <attr name="kswFadeBack" format="boolean"/>
        <attr name="kswThumbRangeRatio" format="float"/>
        <attr name="kswAnimationDuration" format="integer"/>
        <attr name="kswTintColor" format="color|reference"/>
        <attr name="kswTextOn" format="string"/>
        <attr name="kswTextOff" format="string"/>
        <attr name="kswTextThumbInset" format="dimension"/>
        <attr name="kswTextExtra" format="dimension"/>
        <attr name="kswTextAdjust" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="TopImageButton" tools:ignore="ResourceName">
        <attr name="android:src" />
        <attr name="android:text" />
        <attr name="android:textColor" />
        <attr name="android:textSize" />
    </declare-styleable>

    <declare-styleable name="CommentQuoraInputLayout" tools:ignore="ResourceName">
        <attr name="themeType">
            <enum name="WhiteTheme" value="0" />
            <enum name="BlackTheme" value="1" />
            <enum name="LandWhiteTheme" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="DynamicVideoCutSeekBar" tools:ignore="ResourceName">
        <attr name="textSize" format="dimension"/>
        <attr name="inColor" format="color" />
        <attr name="outColor" format="color"/>
        <attr name="imageLow"  format="reference"/>
        <attr name="imageBig" format="reference"/>
        <attr name="imagewidth" format="dimension"/>
        <attr name="imageheight" format="dimension"/>
        <attr name="bigValue" format="integer" />
        <attr name="smallValue" format="integer"/>
    </declare-styleable>

    <declare-styleable name="DynamicVideoChooseCover" tools:ignore="ResourceName">
        <attr name="bigValueForChooseCover" format="integer" />
        <attr name="smallValueForChooseCover" format="integer"/>
    </declare-styleable>

    <declare-styleable name="GridViewWithDivider" tools:ignore="ResourceName">
        <attr name="dividerColor" format="color" />
    </declare-styleable>


    <declare-styleable name="main_BarcodeScannerView">
        <attr format="boolean" name="main_shouldScaleToFill"/>
    </declare-styleable>

    <declare-styleable name="OldAgeSelector">
        <attr name="oasStyle" format="integer" />
    </declare-styleable>

    <declare-styleable name="BottomOvalView">
        <attr name="bottomShape" format="enum">
            <enum name="oval" value="0"/>
            <enum name="rect" value="1"/>
        </attr>
    </declare-styleable>

    <declare-styleable name="Main_DubEntranceView">
        <attr name="start_color" format="color|reference"/>
        <attr name="center_color" format="color|reference"/>
        <attr name="end_color" format="color|reference"/>
        <attr name="animation_duration" format="integer|reference"/>
        <attr name="use_animation" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="AudioBottomContentView" tools:ignore="ResourceName">
        <attr name="show_in_cover" format="boolean"/>
        <attr name="hide_animal" format="boolean"/>
        <attr name="small_style" format="boolean"/>
        <attr name="full_doc_pause_scene" format="boolean"/>
        <attr name="radio_sound_ad" format="boolean"/>
        <attr name="x_horizontal" format="boolean"/>
        <attr name="y_style" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="XPlayAdCountDownSmallView" tools:ignore="ResourceName">
        <attr name="vertical_small_style" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="XAdDownloadInfoFloatView">
        <attr name="show_bottom" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="XAdDownloadInfoComplianceFloatView">
        <attr name="child_gravity" format="enum">
            <enum name="left" value="0"/>
            <enum name="center" value="1"/>
            <enum name="right" value="2"/>
        </attr>
    </declare-styleable>


    <declare-styleable name="NumberPickerView">
        <!--set number of item displayed, default is 3-->
        <attr name="npv_ShownCount" format="reference|integer" />
        <!--set if show divider, default is true-->
        <attr name="npv_ShowDivider" format="reference|boolean" />
        <attr name="npv_DividerColor" format="reference|color" />
        <attr name="npv_DividerMarginLeft" format="reference|dimension" />
        <attr name="npv_DividerMarginRight" format="reference|dimension" />
        <attr name="npv_DividerHeight" format="reference|dimension" />
        <attr name="npv_TextColorNormal" format="reference|color" />
        <attr name="npv_TextColorSelected" format="reference|color" />
        <attr name="npv_TextColorHint" format="reference|color" />
        <attr name="npv_TextSizeNormal" format="reference|dimension" />
        <attr name="npv_TextSizeSelected" format="reference|dimension" />
        <attr name="npv_TextSizeHint" format="reference|dimension" />
        <attr name="npv_TextArray" format="reference" />
        <attr name="npv_MinValue" format="reference|integer" />
        <attr name="npv_MaxValue" format="reference|integer" />
        <attr name="npv_WrapSelectorWheel" format="reference|boolean" />
        <attr name="npv_RespondChangeOnDetached" format="reference|boolean" />
        <attr name="npv_HintText" format="reference|string" />
        <attr name="npv_EmptyItemHint" format="reference|string" />
        <attr name="npv_MarginStartOfHint" format="reference|dimension" />
        <attr name="npv_MarginEndOfHint" format="reference|dimension" />
        <attr name="npv_ItemPaddingHorizontal" format="reference|dimension" />
        <attr name="npv_ItemPaddingVertical" format="reference|dimension" />
        <attr name="npv_RespondChangeInMainThread" format="reference|boolean" />
        <attr name="npv_TextEllipsize" format="reference|string" />

        <!--just used to measure maxWidth for wrap_content without hint,
            the string array will never be displayed.
            you can set this attr if you want to keep the wraped numberpickerview
            width unchanged when alter the content list-->
        <attr name="npv_AlternativeTextArrayWithMeasureHint" format="reference" />
        <attr name="npv_AlternativeTextArrayWithoutMeasureHint" format="reference" />
        <!--the max length of hint content-->
        <attr name="npv_AlternativeHint" format="reference|string" />
    </declare-styleable>
    <declare-styleable name="AvatarWaveView">
        <attr name="waveColor" format="color|reference" />
        <attr name="initialRadius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="main_fade_recycler_view">
        <attr name="main_is_left_fading" format="boolean" />
        <attr name="main_is_right_fading" format="boolean" />
        <attr name="main_is_top_fading" format="boolean" />
        <attr name="main_is_bottom_fading" format="boolean" />
    </declare-styleable>


    <declare-styleable name="main_TimePeriodSelected">
        <attr name="main_show_top_tv" format="boolean" />
    </declare-styleable>

    <!--viewpager指示器: BannerIndicator -->
    <declare-styleable name="BannerIndicator">
        <!--指示器之间的间隙-->
        <attr name="gap" format="dimension" />
        <!--指示器的宽度-->
        <attr name="slider_width" format="dimension" />
        <!--指示器的高度-->
        <attr name="slider_height" format="dimension" />
        <!--指示器大小变化方向-->
        <attr name="sleider_align">
            <enum name="Left" value="1" />
            <enum name="Center" value="0" />
            <enum name="Right" value="2" />
        </attr>
    </declare-styleable>

    <!--滚动选择器通用属性-->
    <declare-styleable name="ScrollPickerView">
        <!-- 中间item的背景-->
        <attr name="spv_center_item_background" format="reference|color" />
        <!-- 可见的item数量，默认为3个-->
        <attr name="spv_visible_item_count" format="integer" />
        <!-- 中间item的位置,默认为 mVisibleItemCount / 2-->
        <attr name="spv_center_item_position" format="integer" />
        <!-- 是否循环滚动，默认为true，开启-->
        <attr name="spv_is_circulation" format="boolean" />
        <!-- 不允许父组件拦截触摸事件，设置为true为不允许拦截，此时该设置才生效 -->
        <attr name="spv_disallow_intercept_touch" format="boolean" />
        <!-- 滚动的方向-->
        <attr name="spv_orientation" format="string">
            <enum name="horizontal" value="1" />
            <enum name="vertical" value="2" />
        </attr>
    </declare-styleable>

    <!--文字选择器-->
    <declare-styleable name="StringScrollPicker">
        <!--文字渐变大小-->
        <attr name="spv_min_text_size" format="dimension" />
        <attr name="spv_max_text_size" format="dimension" />
        <!--文字渐变颜色-->
        <attr name="spv_start_color" format="color" />
        <attr name="spv_end_color" format="color" />
        <!--文字最大行宽-->
        <attr name="spv_max_line_width" format="dimension" />
        <!--滚动震动效果-->
        <attr name="spv_vibrate" format="boolean" />
        <!--文字对齐方式-->
        <attr name="spv_alignment" format="enum">
            <enum name="center" value="1" />
            <enum name="left" value="2" />
            <enum name="right" value="3" />
        </attr>
    </declare-styleable>

    <declare-styleable name="main_TickerView">
        <attr name="main_ticker_animationDuration" format="reference|integer" />
        <attr name="main_ticker_animateMeasurementChange" format="reference|boolean" />
        <attr name="main_ticker_defaultCharacterList" format="enum">
            <enum name="number" value="1" />
            <enum name="alphabet" value="2" />
        </attr>
        <attr name="main_ticker_defaultPreferredScrollingDirection" format="enum">
            <enum name="any" value="0" />
            <enum name="up" value="1" />
            <enum name="down" value="2" />
        </attr>

        <!-- Custom implementations of common android text attributes -->
        <attr name="android:gravity" tools:ignore="ResourceName" />
        <attr name="android:shadowColor" tools:ignore="ResourceName" />
        <attr name="android:shadowDx" tools:ignore="ResourceName" />
        <attr name="android:shadowDy" tools:ignore="ResourceName" />
        <attr name="android:shadowRadius" tools:ignore="ResourceName" />
        <attr name="android:text" tools:ignore="ResourceName" />
        <attr name="android:textAppearance" tools:ignore="ResourceName" />
        <attr name="android:textColor" tools:ignore="ResourceName" />
        <attr name="android:textSize" tools:ignore="ResourceName" />
        <attr name="android:textStyle" tools:ignore="ResourceName" />
    </declare-styleable>

    <declare-styleable name="ReportSquareLayout" tools:ignore="ResourceName">
        <attr format="integer" name="reportSquareLayout_dependOn">
            <enum name="width" value="1"/>
            <enum name="height" value="2"/>
            <enum name="useMin" value="3"/>
        </attr>
    </declare-styleable>

    <declare-styleable name="ColouredLineView" tools:ignore="ResourceName">
        <attr name="main_line_width" format="dimension"/>
        <attr name="main_line_color" format="color"/>
    </declare-styleable>
    
    <declare-styleable name="HotCommentBoxSwitcher" tools:ignore="ResourceName">
        <attr name="hot_comment_lay" format="reference" />
    </declare-styleable>

    <declare-styleable name="MainKidRadiusCardView">
        <attr name="main_rcv_topLeftRadius" format="dimension" />
        <attr name="main_rcv_topRightRadius" format="dimension" />
        <attr name="main_rcv_bottomRightRadius" format="dimension" />
        <attr name="main_rcv_bottomLeftRadius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="MainKidCardFootGradientView">
        <attr name="main_kid_foot_gradient_start_color" format="color" />
        <attr name="main_kid_foot_gradient_end_color" format="color" />
    </declare-styleable>

    <declare-styleable name="MainRunNumberTextView">
        <attr name="main_rnt_duration" format="integer"/>
        <attr name="main_rnt_from_number" format="float"/>
        <attr name="main_rnt_to_number" format="float"/>
        <attr name="main_rnt_auto" format="boolean"/>
    </declare-styleable >

    <declare-styleable name="NoPaddingTextView" tools:ignore="ResourceName">
        <attr name="removeDefaultPadding" format="boolean" />
        <attr name="defaultPadding" format="dimension" />
    </declare-styleable>

    <declare-styleable name="MainExactWrapTextView">
        <attr name="mainWrapTextViewText" format="string" />
        <attr name="mainWrapTextViewTextSize" format="dimension" />
        <attr name="mainWrapTextViewTextColor" format="color" />
        <attr name="mainWrapTextViewLineSpacing" format="dimension" />
        <attr name="mainWrapTextViewMaxLines" format="integer" />
    </declare-styleable>
</resources>
