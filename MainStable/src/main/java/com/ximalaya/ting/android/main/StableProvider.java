package com.ximalaya.ting.android.main;

import android.app.Activity;
import android.net.Uri;
import android.text.StaticLayout;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.push.PushModel;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndData;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.playlet.fragment.PlayletPlayInfoFragment;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import org.json.JSONObject;

import java.util.List;


/**
 * Created by nali on 2024/10/16.
 *
 * <AUTHOR>
 */

public class StableProvider implements IStableProvider {

    public static final int DEFAULT_BACKGROUND_COLOR = 0xff444444;

    public static final int AlbumLoadErrorManagerRESULT_SUCCESS = 0;
    public static final int AlbumLoadErrorManagerRESULT_FAILED_DATA_ERROR = -1;
    public static final int AlbumLoadErrorManagerRESULT_FAILED_REQUEST_ERROR = -2;

    private static final StableProvider sInstance = new StableProvider();
    public static StableProvider getInstance() {
        return sInstance;
    }

    private static IStableProvider mStableProvider;

    public static void init(IStableProvider provider) {
        mStableProvider = provider;
    }

    @Override
    public void handleCalabashAdapterProvider(IMulitViewTypeViewAndData adapter, int mFromForCalabashLineAdapter, List<ItemModel> listData, int position) {
        mStableProvider.handleCalabashAdapterProvider(adapter, mFromForCalabashLineAdapter, listData, position);
    }

    @Override
    public void shareTrack(Activity activity, TrackM trackM, int shareTypeTrack) {
        mStableProvider.shareTrack(activity, trackM, shareTypeTrack);
    }

    @Override
    public String getDISCOVER_TYPE_ITING() {
        return mStableProvider.getDISCOVER_TYPE_ITING();
    }

    @Override
    public String getDISCOVER_TYPE_H5() {
        return mStableProvider.getDISCOVER_TYPE_H5();
    }

    @Override
    public BaseFragment2 getDubbingUserInfoFragment(long uid) {
        return mStableProvider.getDubbingUserInfoFragment(uid);
    }

    @Override
    public void shareTrackWithoutXdcs(Activity activity, Track track, String dstType, int shareType) {
        mStableProvider.shareTrackWithoutXdcs(activity, track, dstType, shareType);
    }

    @Override
    public void shareDubToDst(Activity activity, Track track, String dstName) {
        mStableProvider.shareDubToDst(activity, track, dstName);
    }

    @Override
    public void loadRecommendNetManagerLoadDataForNet(boolean isLoadMore, IDataCallBack<JSONObject> callBack) {
        mStableProvider.loadRecommendNetManagerLoadDataForNet(isLoadMore, callBack);
    }

    @Override
    public int getPlayPageManagerBackgroudColor() {
        return mStableProvider.getPlayPageManagerBackgroudColor();
    }

    @Override
    public BaseFragment2 newAnchorSpaceFragment(long uid) {
        return mStableProvider.newAnchorSpaceFragment(uid);
    }

    @Override
    public void setPushSettingOpenIncludeLogoutState(String key, boolean isPush) {
        mStableProvider.setPushSettingOpenIncludeLogoutState(key, isPush);
    }

    @Override
    public boolean isFromPaidAlbum(PlayableModel playableModel) {
        return mStableProvider.isFromPaidAlbum(playableModel);
    }

    @Override
    public boolean isFromVipAlbum(PlayableModel playableModel) {
        return mStableProvider.isFromVipAlbum(playableModel);
    }

    @Override
    public boolean isFreeTrack(PlayableModel playableModel) {
        return mStableProvider.isFreeTrack(playableModel);
    }

    @Override
    public void showNewUserScrollMissionTip() {
        mStableProvider.showNewUserScrollMissionTip();
    }

    @Override
    public BaseFragment2 newAnchorPrivacySettingFragment() {
        return mStableProvider.newAnchorPrivacySettingFragment();
    }

    @Override
    public BaseFragment2 newAdSettingFragment() {
        return mStableProvider.newAdSettingFragment();
    }

    @Override
    public List<String> searchUtilsParseList(String saleList) {
        return mStableProvider.searchUtilsParseList(saleList);
    }

    @Override
    public Class<? extends Fragment> getAlbumRateListFragmentV2() {
        return mStableProvider.getAlbumRateListFragmentV2();
    }

    @Override
    public BaseFragment2 getAlbumFragmentNew2(String title, long albumId, int from, int playSource, int unreadNum) {
        return mStableProvider.getAlbumFragmentNew2(title, albumId, from, playSource, unreadNum);
    }

    @Override
    public void handleIting(Activity activity, Uri uri) {
        mStableProvider.handleIting(activity, uri);
    }

    @Override
    public boolean handleITing(Activity activity, PushModel pm) {
        return mStableProvider.handleITing(activity, pm);
    }

    @Override
    public void pageTraceFail(BaseFragment2 fragment2) {
        mStableProvider.pageTraceFail(fragment2);
    }

    @Override
    public void pageTraceSuccess(BaseFragment2 fragment2) {
        mStableProvider.pageTraceSuccess(fragment2);
    }

    @Override
    public void pageMonitorReport(BaseFragment2 fragment, int result, String type, String message) {
        mStableProvider.pageMonitorReport(fragment, result, type, message);
    }

    @Override
    public StaticLayout getNormalLayout(String content, int width) {
        return mStableProvider.getNormalLayout(content, width);
    }

    @Override
    public StaticLayout getLimitLayout(String content, int width, IHandleOk onLookAllClick) {
        return mStableProvider.getLimitLayout(content, width, onLookAllClick);
    }

    @Override
    public void shareAlbum(Activity activity, AlbumM album, String currPage, ShareManager.Callback callback) {
        mStableProvider.shareAlbum(activity, album, currPage, callback);
    }

    @Override
    public View getHintArea(BaseFragment2 fragment2) {
        return mStableProvider.getHintArea(fragment2);
    }

    @Override
    public Fragment newInstancePlayletPlayInfoFragment(Track track, boolean isAutoShowKeyboard, PlayletPlayInfoFragment.ICommentClick iCommentFragment) {
        return mStableProvider.newInstancePlayletPlayInfoFragment(track, isAutoShowKeyboard, iCommentFragment);
    }

    @Override
    public BaseFragment2 newInstanceAlbumFragmentNew2(String title, String recSrc, String recTrack, long albumId, int from, int playSource, int unreadNum) {
        return mStableProvider.newInstanceAlbumFragmentNew2(title, recSrc, recTrack, albumId, from, playSource, unreadNum);
    }

    @Override
    public void addCategoryViewManager(List<TabCommonAdapter.FragmentHolder> fragmentHolderList) {
        mStableProvider.addCategoryViewManager(fragmentHolderList);
    }

    @Override
    public void showVoteDialogFragment(FragmentManager childFragmentManager, long albumId, long anchorUid, IHandleOk iHandleOk) {
        mStableProvider.showVoteDialogFragment(childFragmentManager, albumId, anchorUid, iHandleOk);
    }

    @Nullable
    @Override
    public BaseFragment2 newRoleCommentsFragment() {
        return mStableProvider.newRoleCommentsFragment();
    }

    @Override
    public void resetTempoAlbum(long albumId) {
        mStableProvider.resetTempoAlbum(albumId);
    }
}


