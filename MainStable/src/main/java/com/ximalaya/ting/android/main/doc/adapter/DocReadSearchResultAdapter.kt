package com.ximalaya.ting.android.main.doc.adapter

import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.model.localtts.LocalBookInfo
import com.ximalaya.ting.android.host.read.router.ReadSupportHostRouter
import com.ximalaya.ting.android.main.stable.R
import java.io.File
import java.util.Locale

class DocReadSearchResultAdapter(val list: List<LocalBookInfo>, val fragment2: BaseFragment2) :
    RecyclerView.Adapter<DocReadSearchResultAdapter.Holder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
        return Holder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.main_item_doc_read_search_res_layout, parent, false)
        )
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        val item = list[position]
        if (item.notValid()) {
            holder.ivCover!!.tag = null
            // 不可用
            holder.ivCover!!.setImageResource(R.drawable.main_img_local_book_default_cover)
        } else {
            if (!TextUtils.isEmpty(item.coverUrl)) {
                // 有封面
                holder.ivCover!!.tag = item.coverUrl
                val options = ImageManager.Options()
                options.targetWidth = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 60f)
                options.targetHeight =
                    BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 76f)
                ImageManager.from(BaseApplication.getMyApplicationContext())
                    .downloadBitmap(item.coverUrl, null,
                        ImageManager.DisplayCallback { lastUrl, bitmap ->
                            if (bitmap != null && holder.ivCover!!.tag is String) {
                                if (TextUtils.equals(
                                        lastUrl,
                                        holder.ivCover!!.tag as CharSequence
                                    )
                                ) {
                                    holder.ivCover!!.setImageBitmap(bitmap)
                                    return@DisplayCallback
                                }
                            }
                            holder.ivCover!!.setImageResource(R.drawable.main_ic_local_book_default_cover)
                        })
            } else {
                holder.ivCover!!.tag = null
                // 本地小说
                holder.ivCover!!.setImageResource(R.drawable.main_ic_local_book_default_cover)
            }
        }

        holder.tvTitle?.text = item.title
        val colorId =
            if (item.notValid()) com.ximalaya.ting.android.host.R.color.host_color_999999_8d8d91 else com.ximalaya.ting.android.host.R.color.host_color_131313_dcdcdc
        if (BaseApplication.getMyApplicationContext().resources != null) {
            holder.tvTitle!!.setTextColor(
                BaseApplication.getMyApplicationContext().resources.getColor(
                    colorId
                )
            )
        }

        holder.tvStatus?.text = item.status
        holder.itemView.setOnClickListener {
            if (item.notValid()) {
                ToastManager.showToast("书籍已失效")
                return@setOnClickListener
            }

            startLocalReadFragment(fragment2, item)
        }
    }

    private fun startLocalReadFragment(fragment: BaseFragment2, bookInfo: LocalBookInfo?) {
        if (bookInfo?.localOriginFilePath == null) {
            return
        }
        val path = bookInfo.localOriginFilePath
        if (TextUtils.isEmpty(path) || !File(path).exists()) {
            ToastManager.showToast("打开失败，本地文件不存在 $path")
            return
        }
        // 下载并安装ReadSupportBundle加载本地阅读页面
        ReadSupportHostRouter.getFragmentAction { onAction ->
            try {
                val localUrl = String.format(Locale.getDefault(), "file://%s", path)
                Log.w("local_book", "startLocalReadFragment localUrl $localUrl")
                val bundle = Bundle()
                bundle.putString("url", localUrl)
                bundle.putLong("bookId", bookInfo.bookId)
                var fragment2: BaseFragment2? = null
                if (onAction != null) {
                    fragment2 = onAction.newLocalReadFragment(bundle)
                }
                if (fragment2 != null) {
                    fragment.startFragment(fragment2)
                } else {
                    CustomToast.showFailToast("插件加载失败")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Log.e("local_book", "startLocalReadFragment failed " + e.message)
                CustomToast.showFailToast("插件加载失败")
            }
        }
    }

    class Holder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        var ivCover: ImageView? = null
        var tvStatus: TextView? = null
        var tvTitle: TextView? = null

        init {
            ivCover = convertView.findViewById(R.id.listen_item_iv_cover)
            tvTitle = convertView.findViewById(R.id.listen_item_tv_title)
            tvStatus = convertView.findViewById(R.id.listen_item_tv_status)
        }
    }
}