package com.ximalaya.ting.android.main.fragment.myspace.other.setting;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.liveav.lib.impl.zego.constants.XmZegoConstants;
import com.ximalaya.ting.android.main.fragment.base.BaseFragmentInMain;
import com.ximalaya.ting.android.main.stable.R;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ChooseLiveAudioDeviceModeFragment extends BaseFragmentInMain implements View.OnClickListener{
    private List<ImageView> mChooseMode = new ArrayList<>();

    public ChooseLiveAudioDeviceModeFragment(){
        super(true, null);
    }

    @Override
    protected String getPageLogicName() {
        return "直播音频设备模式";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("直播音频设备模式");

        findViewById(R.id.main_rl_live_device_mode_choose_default).setOnClickListener(this);
        findViewById(R.id.main_rl_live_device_mode_choose_auto).setOnClickListener(this);
        findViewById(R.id.main_rl_live_device_mode_choose_general).setOnClickListener(this);
        findViewById(R.id.main_rl_live_device_mode_choose_com).setOnClickListener(this);
        findViewById(R.id.main_rl_live_device_mode_choose_com2).setOnClickListener(this);

        ImageView mChooseModeDefault = findViewById(R.id.main_choose_default);
        ImageView mChooseModeAuto = findViewById(R.id.main_choose_auto);
        ImageView mChooseModeGeneral = findViewById(R.id.main_choose_general);
        ImageView mChooseModeCom = findViewById(R.id.main_choose_com);
        ImageView mChooseModeCom2 = findViewById(R.id.main_choose_com2);


        mChooseMode.add(mChooseModeDefault);
        mChooseMode.add(mChooseModeAuto);
        mChooseMode.add(mChooseModeGeneral);
        mChooseMode.add(mChooseModeCom);
        mChooseMode.add(mChooseModeCom2);

    }

    @Override
    protected void loadData() {
        selectAudioDeviceMode(MMKVUtil.getInstance().getInt(
                PreferenceConstantsInHost.KEY_LIVE_AUDIO_DEVICE_MODE, 0));
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_audio_device_mode;
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_rl_live_device_mode_choose_default) {
            selectAudioDeviceMode(0);
        } else if(id == R.id.main_rl_live_device_mode_choose_auto) {
            selectAudioDeviceMode(XmZegoConstants.AudioDeviceMode.Auto);
        } else if(id == R.id.main_rl_live_device_mode_choose_general) {
            selectAudioDeviceMode(XmZegoConstants.AudioDeviceMode.General);
        } else if (id == R.id.main_rl_live_device_mode_choose_com) {
            selectAudioDeviceMode(XmZegoConstants.AudioDeviceMode.Communication);
        } else if (id == R.id.main_rl_live_device_mode_choose_com2) {
            selectAudioDeviceMode(XmZegoConstants.AudioDeviceMode.Communication2);
        }
    }

    private void selectAudioDeviceMode(int mode){
        if(mChooseMode == null) {
            return;
        }

        int index = 0;
        switch (mode) {
            case 0:
                index = 0;
                break;
            case XmZegoConstants.AudioDeviceMode.Auto:
                index = 1;
                break;
            case XmZegoConstants.AudioDeviceMode.General:
                index = 2;
                break;
            case XmZegoConstants.AudioDeviceMode.Communication:
                index = 3;
                break;
            case XmZegoConstants.AudioDeviceMode.Communication2:
                index = 4;
                break;
            default:
                index = 0;
        }

        for (int i = 0; i < mChooseMode.size(); i++) {
            if(i == index) {
                mChooseMode.get(i).setVisibility(View.VISIBLE);
            } else {
                mChooseMode.get(i).setVisibility(View.INVISIBLE);
            }
        }

        MMKVUtil.getInstance().saveInt(PreferenceConstantsInHost.KEY_LIVE_AUDIO_DEVICE_MODE, mode);
    }
}
