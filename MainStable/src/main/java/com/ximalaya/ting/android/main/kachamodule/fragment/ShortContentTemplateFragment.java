package com.ximalaya.ting.android.main.kachamodule.fragment;

import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.main.kachamodule.adapter.TemplateLineAdapter;
import com.ximalaya.ting.android.main.kachamodule.listener.IShortContentInterface;
import com.ximalaya.ting.android.main.kachamodule.manager.ShortContentDownloadManager;
import com.ximalaya.ting.android.main.kachamodule.model.ShortContentTemplateModel;
import com.ximalaya.ting.android.main.kachamodule.model.TemplateGroup;
import com.ximalaya.ting.android.main.request.MainStableRequest;
import com.ximalaya.ting.android.main.stable.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class ShortContentTemplateFragment extends BaseFragment2 implements IShortContentInterface.IShortVideoModelDownloadListener {
    private long mAlbumId;
    private RecyclerView mRecyclerView;
    private List<TemplateGroup> mList = new ArrayList<>();
    private TemplateLineAdapter mAdapter;
    private ShortContentTemplateModel mDownloadModel;
    private int mPosition;
    private int mPositionInLine;
    private LinearLayoutManager mLayoutManager;

    @Override
    protected String getPageLogicName() {
        return "VideoClipTemplateFragment";
    }

    public static ShortContentTemplateFragment newInstance(long albumId) {
        ShortContentTemplateFragment fragment = new ShortContentTemplateFragment();
        Bundle bundle = new Bundle();
        bundle.putLong("albumId", albumId);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        if (getArguments() != null) {
            mAlbumId = getArguments().getLong("albumId");
        }
        mRecyclerView = findViewById(R.id.main_recycler_view);
        mLayoutManager = new LinearLayoutManager(mContext);
        mRecyclerView.setLayoutManager(mLayoutManager);
        mAdapter = new TemplateLineAdapter(mContext, mList);
        mAdapter.setCallback((view, model, position, positionInLine) -> {
            // 已下载 选中
            // 未下载 下载
            if (model != null) {
                if (mDownloadModel != null) {
                    mDownloadModel.setSelected(false);
                }
                mDownloadModel = model;
                mDownloadModel.setSelected(true);
                if (mDownloadModel.getDownloadState() == ShortContentTemplateModel.DOWNLOADED_STATE) {
                    mAdapter.notifyItemChanged(mPosition, mPositionInLine);
                    mAdapter.notifyItemChanged(position, positionInLine);
                } else {
                    if (!TextUtils.isEmpty(model.getMediaUrl())) {
                        ShortContentDownloadManager.getSingleInstance().requestDownloadModelVideo(model);
                    }
                }
                mPosition = position;
                mPositionInLine = positionInLine;
            }
        });
        mRecyclerView.setAdapter(mAdapter);
        View ivBack = findViewById(R.id.main_iv_back);
        ivBack.setOnClickListener(view -> finishFragment());

        View vTitle = findViewById(R.id.main_v_title);
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.KITKAT) {
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) vTitle.getLayoutParams();
            lp.topMargin = BaseUtil.dp2px(mContext, 20);
            vTitle.setLayoutParams(lp);
        }
    }

    @Override
    protected void loadData() {
        MainStableRequest.getVideoClipTemplateList(mAlbumId, new IDataCallBack<List<TemplateGroup>>() {
            @Override
            public void onSuccess(@Nullable final List<TemplateGroup> list) {
                doAfterAnimation(() -> {
                    if (list != null && !list.isEmpty()) {
                        mList.clear();
                        for (TemplateGroup group : list) {
                            if (group != null) {
                                TemplateGroup title = new TemplateGroup();
                                title.setId(-1);
                                title.setName(group.getName());
                                mList.add(title);
                                mList.add(group);
                            }
                        }
                        if (canUpdateUi()) {
                            mAdapter.notifyDataSetChanged();
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {}
        });
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_video_clip_template;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        StatusBarManager.setStatusBarColor(getWindow(), true);
    }

    @Override
    public void onPause() {
        StatusBarManager.setStatusBarColor(getWindow(), false);
        super.onPause();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ShortContentDownloadManager.getSingleInstance().addDownloadListener(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        ShortContentDownloadManager.getSingleInstance().removeDownloadListener(this);
    }

    @Override
    public void onTaskStart(ShortContentTemplateModel videoModel) {

    }

    @Override
    public void onTaskSuccess(ShortContentTemplateModel videoModel) {
        if (videoModel == null
                || mDownloadModel == null
                || videoModel.getId() != mDownloadModel.getId()) {
            return;
        }
        mDownloadModel.setDownloadState(ShortContentTemplateModel.DOWNLOADED_STATE);
        mDownloadModel.setModelSaveDirPath(videoModel.getModelSaveDirPath());
        HandlerManager.obtainMainHandler().post(() -> {
            if (canUpdateUi()) {
                mAdapter.notifyDataSetChanged();
            }
        });
    }

    @Override
    public void onTaskFailed(ShortContentTemplateModel videoModel) {
        if (videoModel == null
            || mDownloadModel == null
            || videoModel.getId() != mDownloadModel.getId()) {
            return;
        }
        mDownloadModel.setDownloadState(ShortContentTemplateModel.VALID_STATE);
        HandlerManager.obtainMainHandler().post(() -> {
            if (canUpdateUi()) {
                mAdapter.notifyDataSetChanged();
            }
        });
    }

    @Override
    public void onTaskProgress(ShortContentTemplateModel videoModel, int progress) {
        if (videoModel == null
            || mDownloadModel == null
            || videoModel.getId() != mDownloadModel.getId()) {
            return;
        }
        mDownloadModel.setDownloadState(ShortContentTemplateModel.DOWNLOADING_STATE);
        mDownloadModel.setProgress(progress);
        HandlerManager.obtainMainHandler().post(() -> {
            if (canUpdateUi()) {
                mAdapter.notifyItemChanged(mPosition, mPositionInLine);
            }
        });
    }

    @Override
    public boolean onBackPressed() {
        if (mDownloadModel != null) {
            setFinishCallBackData(true, mDownloadModel);
        } else {
            setFinishCallBackData(false, null);
        }
        return super.onBackPressed();
    }
}
