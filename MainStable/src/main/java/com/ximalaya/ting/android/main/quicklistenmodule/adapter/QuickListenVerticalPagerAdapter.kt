package com.ximalaya.ting.android.main.quicklistenmodule.adapter

import com.ximalaya.ting.android.host.adapter.base.BaseProviderMultiAdapter
import com.ximalaya.ting.android.host.data.model.QuickElementType
import com.ximalaya.ting.android.host.data.model.QuickListenModel
import com.ximalaya.ting.android.host.util.kt.isAvailable
import com.ximalaya.ting.android.main.quicklistenmodule.adapter.provider.AgentGuideCardProvider
import com.ximalaya.ting.android.main.quicklistenmodule.adapter.provider.SoundCardProvider
import com.ximalaya.ting.android.main.quicklistenmodule.adapter.provider.VideoCardProvider
import com.ximalaya.ting.android.main.quicklistenmodule.adapter.provider.XimaTenCardProvider
import com.ximalaya.ting.android.main.quicklistenmodule.fragment.QuickListenItemPageFragment

class QuickListenVerticalPagerAdapter(val baseFragment2: QuickListenItemPageFragment) : BaseProviderMultiAdapter<QuickListenModel>() {

    init {
        addItemProvider(XimaTenCardProvider(baseFragment2))
        addItemProvider(SoundCardProvider(baseFragment2))
        addItemProvider(VideoCardProvider(baseFragment2))
        addItemProvider(AgentGuideCardProvider(baseFragment2))
    }

    override fun getItemType(data: List<QuickListenModel>, position: Int): Int {
        if (data.size > position) {
            return when(if (data[position].elementType.isAvailable()) data[position].elementType else data[position].bizType) {
                QuickElementType.XIMA_TEN_CARD.elementType -> QuickElementType.XIMA_TEN_CARD.viewType
                QuickElementType.AUDIO_CARD.elementType -> QuickElementType.AUDIO_CARD.viewType
                QuickElementType.AGENT_GUIDE_CARD.elementType -> QuickElementType.AGENT_GUIDE_CARD.viewType
                QuickElementType.AGENT_RADIO_CARD.elementType -> QuickElementType.AGENT_RADIO_CARD.viewType
                QuickElementType.TRACK_COLLECTION_CARD.elementType -> QuickElementType.TRACK_COLLECTION_CARD.viewType
                QuickElementType.VIDEO.elementType -> QuickElementType.VIDEO.viewType
                else -> QuickElementType.AUDIO_CARD.viewType
            }
        }
        return QuickElementType.LOADING_CARD.viewType
    }
}
