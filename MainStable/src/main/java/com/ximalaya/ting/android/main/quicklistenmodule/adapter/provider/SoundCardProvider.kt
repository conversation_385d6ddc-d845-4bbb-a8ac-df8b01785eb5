package com.ximalaya.ting.android.main.quicklistenmodule.adapter.provider

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.graphics.Typeface
import android.net.Uri
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import com.google.gson.Gson
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.adapter.base.provider.BaseItemProvider
import com.ximalaya.ting.android.host.adapter.base.viewholder.BaseViewHolder
import com.ximalaya.ting.android.host.data.model.LocalExt
import com.ximalaya.ting.android.host.data.model.QuickElementType
import com.ximalaya.ting.android.host.data.model.QuickListenModel
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback
import com.ximalaya.ting.android.host.listener.SimpleAnimatorListener
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManager
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType
import com.ximalaya.ting.android.host.manager.share.ShareManager
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel
import com.ximalaya.ting.android.host.manager.share.panel.SharePanelType
import com.ximalaya.ting.android.host.manager.track.AgentRadioEventManage.doCollectAction
import com.ximalaya.ting.android.host.manager.track.AgentRadioParam
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage
import com.ximalaya.ting.android.host.model.ad.ShareAdRequestParams
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.common.RemoteTypefaceManager
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.kt.isAvailable
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.main.StableProvider
import com.ximalaya.ting.android.main.quicklistenmodule.dialog.QuickListenDocDialog
import com.ximalaya.ting.android.main.quicklistenmodule.fragment.QuickListenItemPageFragment
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenHomeHelper
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenTraceUtil
import com.ximalaya.ting.android.main.quicklistenmodule.view.CharacterFadeInScaleTextView
import com.ximalaya.ting.android.main.quicklistenmodule.view.IOnSeekBarChangeListener
import com.ximalaya.ting.android.main.quicklistenmodule.view.ListenProgressSeekBar
import com.ximalaya.ting.android.main.quicklistenmodule.view.doc.ReactDocCellView
import com.ximalaya.ting.android.main.stable.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created by nali on 2025/6/25.
 * <AUTHOR>
 */
class SoundCardProvider(val baseFragment2: QuickListenItemPageFragment): BaseItemProvider<QuickListenModel>() {
    override val itemViewType: Int
    get() = QuickElementType.AUDIO_CARD.viewType
    override val layoutId: Int
    get() = R.layout.main_quick_listen_sound_card_lay

    private var mTypeface: Typeface? = null

    init {
        mTypeface =
            Typeface.createFromAsset(ToolUtil.getCtx().assets, "fonts/XmlyNumberV1.0-Medium.otf")
    }

    override fun convert(helper: BaseViewHolder, item: QuickListenModel) {
        val curItemShow = helper.bindingAdapterPosition == baseFragment2.getCurrentItem()
        val mClose = helper.getView<ImageView>(R.id.main_close)
        if (!curItemShow && item.pageState == 1 && mClose.getTag(R.id.main_view_id_album_disable) == item.refId) {
            Logger.d("ReactDocCellView", "SoundCardProvider : convertReturn ${item.title}")
            return
        }
        mClose.setTag(R.id.main_view_id_album_disable, item.refId)
        item.pageState = if (curItemShow) 2 else 1

        Logger.d("ReactDocCellView", "SoundCardProvider : convert  ${item.refId}  ${item.title}")
        val mAiIcon: ImageView = helper.getView<ImageView>(R.id.main_ai_icon)
        val mDesc: TextView = helper.getView<TextView>(R.id.main_desc)
        val mDescLay: View = helper.getView(R.id.main_desc_lay)
        val mMusicHint: View = helper.getView(R.id.main_music_hint)
        val mTitle: TextView = helper.getView<TextView>(R.id.main_title)
        val mCoverLay: View = helper.getView(R.id.main_cover_lay)
        val mCover: RoundImageView = helper.getView<RoundImageView>(R.id.main_cover)
        val mCoverLotteView: XmLottieAnimationView = helper.getView<XmLottieAnimationView>(R.id.main_cover_play_lottie)
        val mCoverShadow: ImageView = helper.getView<ImageView>(R.id.main_cover_shadow)
        val mCoverPlayIcon: ImageView = helper.getView<ImageView>(R.id.main_play_icon)
        val mAlbumCover: AlbumCoverLayoutView = helper.getView<AlbumCoverLayoutView>(R.id.main_album_cover)
        val mAlbumTitle: TextView = helper.getView<TextView>(R.id.main_album_title)
        val mSubscr: TextView = helper.getView<TextView>(R.id.main_subscr)
        val mMoreIcon: ImageView = helper.getView<ImageView>(R.id.main_more_icon)
        val mMoreShare: ImageView = helper.getView<ImageView>(R.id.main_more_share)
        val mLikeLay: FrameLayout = helper.getView<FrameLayout>(R.id.main_like_lay)
        val mLikeLottie: XmLottieAnimationView = helper.getView<XmLottieAnimationView>(R.id.main_like_lottie)
        val mLikeIcon: ImageView = helper.getView<ImageView>(R.id.main_like_icon)
        val mSugLay: LinearLayout = helper.getView<LinearLayout>(R.id.main_sug_lay)
        val mAiSug: CharacterFadeInScaleTextView = helper.getView(R.id.main_ai_sug)
        val mAiSugArrow: ImageView = helper.getView<ImageView>(R.id.main_ai_sug_arrow)
        val mDoc: ReactDocCellView = helper.getView<ReactDocCellView>(R.id.main_doc)
        val mSeekBar: ListenProgressSeekBar = helper.getView(R.id.main_seek_bar)
        val mTopClickView: View = helper.getView(R.id.main_top_click_view)
        val mSeekBarMask: View = helper.getView(R.id.main_mask)
        val mSeekTimer: TextView = helper.getView(R.id.main_seek_timer)
        val mSeekDuration: TextView = helper.getView(R.id.main_seek_duration)
        val mGuideLikeLay: View = helper.getView(R.id.main_guide_like_lay)
        val mCoverMask: View = helper.getView(R.id.main_cover_mask)
        val mCoverMaskLay: View = helper.getView(R.id.main_cover_mask_lay)

        val curItem = if (item.isTrackCollect()) findItemInSubElement(PlayTools.getCurTrackId(ToolUtil.getCtx()), item) else item
        if (curItem == null) {
            return
        }

        val trackId = curItem.refId ?: 0

        val mHejiLay: View = helper.getView(R.id.main_heji_lay)
        val mHejiTitle: TextView = helper.getView(R.id.main_heji_title)
        val mHejiAll: TextView = helper.getView(R.id.main_heji_all)

        val isAgentRadio = item.isAgentRadio() && (item.extraInfo?.recReason?.refId ?: 0) > 0
        val isAigc = item.isAgentRadio() && item.extraInfo?.aigc == true
        val isFirstAigc = isAigc && (item.extraInfo?.isFirstTrack ?: false)
        val isCurPlaying = isCurPlaying(trackId)
        val isCurTrack = PlayTools.getCurTrackId(ToolUtil.getCtx()) == trackId

        val albumCover = if (curItem.surElement?.cover.isAvailable()) curItem.surElement?.cover!! else (if (isAigc) "https://imagev2.xmcdn.com/storages/7e14-audiofreehighqps/0B/EA/GAqhfD0MDHbdAAAKowO5ywAQ.png" else "")
        val coverUrl = if (curItem.cover.isAvailable()) curItem.cover!! else (if (isAigc) "https://imagev2.xmcdn.com/storages/7900-audiofreehighqps/B4/F5/GAqh_aQMFdosAAPidgO_nNfT.png" else "")

        if (isAgentRadio) {
            ImageManager.from(ToolUtil.getCtx()).displayImage(mAiIcon, "https://imagev2.xmcdn.com/storages/7e14-audiofreehighqps/0B/EA/GAqhfD0MDHbdAAAKowO5ywAQ.png", -1)
        }

        mAiIcon.visibility = if (isAgentRadio) View.VISIBLE else View.GONE
        mClose.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }

            // 快听Tab-内容流页-内容卡-不感兴趣  点击事件
            QuickListenTraceUtil.trace68023(
                trackId,
                baseFragment2.getQuickListenListData(),
                helper.bindingAdapterPosition,
                baseFragment2.getTabId(),
                baseFragment2.getShowFrom())

            val realPosition = Math.max(0, helper.bindingAdapterPosition)
            var itemPosition = realPosition
            val listData = baseFragment2.getQuickListenListData()
            if (listData != null && listData.size > realPosition) {
                val curData = QuickListenManager.getQuickListenModelFromListByTrackId(trackId, listData)
                val listenModel = listData.get(realPosition)
                if (listenModel.isXimaTen() || listenModel.isTrackCollect() || listenModel.isVideoCollect()) {
                    itemPosition = listenModel.subElements?.indexOf(curData) ?: 0
                }
            }
            // 数据处理和播放逻辑
            baseFragment2.getHomePageControl()?.negativeSoundCard(curItem.refId ?: 0, realPosition + 1, itemPosition + 1)

            baseFragment2.getHomePageControl()?.saveBehaviorToLocal(curItem.refId ?: 0, "负反馈")
        }

        val descText = curItem.extraInfo?.recReason?.text ?: curItem.recReason ?: ""
        val descSuffix = curItem.extraInfo?.recReason?.suffix ?: ""
        if (isAgentRadio) {
            SpanUtils.with(mDesc)
                .append(descText)
                .setForegroundColor(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_992c2c3c_5393ff))
                .appendSpace(4.dp)
                .append(descSuffix)
                .create()
            try {
                val agentAudioId = curItem.extraInfo?.recReason?.refId ?: 0
                val toUri = Uri.parse("iting://open?msg_type=94&bundle=rn_ai_chat&scene=FlashCreateAIRadio&agent=xiaoya&initialPage=AiRadioPlay&from=quickListen&aiRadioId=${agentAudioId}")
                mDescLay.setOnClickListener {
                    if (!OneClickHelper.getInstance().onClick(it)) {
                        return@setOnClickListener
                    }
                    QuickListenTraceUtil.trace68769(
                        trackId,
                        baseFragment2.getQuickListenListData(),
                        helper.bindingAdapterPosition,
                        baseFragment2.getTabId(),
                        baseFragment2.getShowFrom()
                    )
                    StableProvider.getInstance().handleIting(MainApplication.getMainActivity(), toUri)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        } else {
            SpanUtils.with(mDesc)
                .append(descText)
                .appendSpace(4.dp)
                .append(descSuffix)
                .create()
            mDescLay.setOnClickListener(null)
            mDescLay.isClickable = false
        }

        var isGuideLikeLayShow = MMKVUtil.getInstance().getBoolean("quick_listen_guide_like_lay_show", false)
        if (isFirstAigc && isCurPlaying && !isGuideLikeLayShow && curItem.extraInfo?.isLike != true) {
            mGuideLikeLay.visibility = View.VISIBLE
            item.extraInfo?.isFirstTrack = false
            MMKVUtil.getInstance().saveBoolean("quick_listen_guide_like_lay_show", true)
            mGuideLikeLay.postDelayed({ mGuideLikeLay.visibility = View.GONE }, 5000)
        } else {
            mGuideLikeLay.visibility = View.GONE
        }

        mTitle.setLineSpacing(10.dpFloat, 1f)

        RemoteTypefaceManager.setRemoteTypeface(
            RemoteTypefaceManager.RemoteTypeface.SourceHanSansCNHeavy,
            mTitle
        )
        mTitle.text = curItem.title

        val overMediumScreenDevice = QuickListenHomeHelper.isOverMediumScreenDevice()

        (mCoverLay.layoutParams as? MarginLayoutParams)?.let {
            it.topMargin = if (overMediumScreenDevice) 40.dp else if (QuickListenHomeHelper.isSmallScreenDevice()) 10.dp else 24.dp
        }

        mCover.layoutParams?.let {
            it.width = if (overMediumScreenDevice) 140.dp else 94.dp
            it.height = if (overMediumScreenDevice) 140.dp else 94.dp
        }

        mCoverShadow.scaleX = if (overMediumScreenDevice) 1f else 0.64f
        mCoverShadow.scaleY = if (overMediumScreenDevice) 1f else 0.64f
        (mCoverShadow.layoutParams as? MarginLayoutParams)?.bottomMargin = if (overMediumScreenDevice) -24.dp else 0

        mCoverLotteView.scaleX = if (overMediumScreenDevice) 1f else 0.64f
        mCoverLotteView.scaleY = if (overMediumScreenDevice) 1f else 0.64f

        ImageManager.from(ToolUtil.getCtx()).displayImage(mCover, coverUrl, R.drawable.host_default_album)
        
        Logger.log("SoundCardProvider : trackTitle = " + item.title + "   isCurTrack=" + isCurTrack + "   isCurPlaying=" + isCurPlaying)
        
        // 封面区动画
        changePlayingAnimator(isCurPlaying, isCurTrack, mCoverShadow, mCover, mCoverLotteView, mCoverPlayIcon)

        mTopClickView.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }

            mCoverMaskLay.visibility = View.GONE
            mCoverMask.visibility = View.GONE
            MMKVUtil.getInstance().saveLong(
                "main_quick_listen_cover_click_guide_mask", -2
            )

            QuickListenTraceUtil.trace67650(trackId,
                baseFragment2.getQuickListenListData(),
                helper.bindingAdapterPosition,
                XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying,
                baseFragment2.getTabId(), baseFragment2.getShowFrom())

            QuickListenHomeHelper.handlePlayOrPause(curItem.refId ?: 0, baseFragment2.getHomePageControl())
        }

        if (item.extraInfo?.isPureMusic == true) {
            mMusicHint.visibility = View.VISIBLE
            mDoc.visibility = View.INVISIBLE
            mMusicHint.setOnClickListener {
                if (OneClickHelper.getInstance().onClick(it)) {
                    QuickListenDocDialog.newInstance(trackId, true).show(baseFragment2.childFragmentManager)
                }
            }
        } else {
            mMusicHint.visibility = View.GONE
            mDoc.setDocInfo(
                trackId, curItem.title
                    ?: "", if (overMediumScreenDevice) 4 else 3)
            mDoc.setOnClickListener {
                if (OneClickHelper.getInstance().onClick(it)) {
                    QuickListenDocDialog.newInstance(trackId, false).show(baseFragment2.childFragmentManager)
                }
            }
            mDoc.visibility = View.VISIBLE
        }

        if (!isCurPlaying) {
            mDoc.docView.pause()
        }

        if (item.isTrackCollect()) {
            var indexOf = item.subElements?.indexOf(curItem) ?: 0
            if (indexOf < 0) {
                indexOf = 0
            }
            mHejiTitle.text = "合集·${indexOf + 1}/${(item.subElements?.size?.toString() ?: "")}"
            mHejiLay.visibility = View.VISIBLE
            if (indexOf == (item.subElements?.size ?: 0) - 1) {
                mSeekBar.setHejiContent(null)
            } else {
                mSeekBar.setHejiContent(mHejiAll)
            }
            mHejiLay.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }

                QuickListenTraceUtil.trace68570(
                    trackId,
                    baseFragment2.getQuickListenListData(),
                    helper.bindingAdapterPosition,
                    baseFragment2.getTabId(),
                    baseFragment2.getShowFrom()
                )

                baseFragment2.getHomePageControl()?.sendCardListShow(
                item.elementType ?: "", item.refId?.toString() ?: ""
            ) }
        } else {
            mSeekBar.setHejiContent(null)
            mHejiLay.visibility = View.GONE
        }

        mSeekBar.setTrackId(trackId)
        mSeekBar.onPlayProgress(XmPlayerManager.getInstance(ToolUtil.getCtx()).playCurrPositon, XmPlayerManager.getInstance(ToolUtil.getCtx()).duration)
        mSeekBar.setOnSeekBarListener(object : IOnSeekBarChangeListener {
            override fun onProgressChanged(progress: Double, fromUser: Boolean) {
                if (fromUser) {
                    handlerSeekBarMask(progress, curItem.duration ?: 0, mSeekTimer, mSeekDuration, mDoc)
                }
            }

            override fun onStartTrackingTouch(progress: Double) {
                QuickListenHomeHelper.getViewPager(mSeekBar)?.setCanSlide(false)
                mSeekBarMask.visibility = View.VISIBLE
                mSeekBar.setMinimumTrackTintColor(ContextCompat.getColor(ToolUtil.getCtx(),
                    R.color.host_color_662c2c3c_66ffffff))
                handlerSeekBarMask(progress, curItem.duration ?: 0, mSeekTimer, mSeekDuration, mDoc)
            }

            override fun onStopTrackingTouch(progress: Double) {
                QuickListenTraceUtil.trace68016(trackId, baseFragment2.getQuickListenListData(),
                    helper.bindingAdapterPosition,
                    baseFragment2.getTabId(),
                    baseFragment2.getShowFrom(),
                    (XmPlayerManager.getInstance(ToolUtil.getCtx()).duration * 1f * progress / 1000).toInt())

                QuickListenHomeHelper.getViewPager(mSeekBar)?.setCanSlide(true)
                mSeekBarMask.visibility = View.GONE
                mSeekBar.setMinimumTrackTintColor(ContextCompat.getColor(ToolUtil.getCtx(),
                    R.color.host_color_332c2c3c_33ffffff))
                XmPlayerManager.getInstance(ToolUtil.getCtx()).seekToByPercent(progress.toFloat())
            }
        })

        mSeekBar.setMinimumTrackTintColor(ContextCompat.getColor(ToolUtil.getCtx(),
            R.color.host_color_332c2c3c_33ffffff))
        mSeekBar.setMaximumTrackTintColor(ContextCompat.getColor(ToolUtil.getCtx(),
            R.color.host_color_0f2c2c3c_0fffffff))

        mSeekBar.setQuickListenHomePageControl(baseFragment2.getHomePageControl())
        mSeekBar.setDocView(mDoc)
        if (isCurTrack) {
            mSeekBar.onResume()
        } else {
            mSeekBar.onPause()
        }

        mAlbumCover.setAlbumCover(albumCover, 28.dp, 28.dp)
        mAlbumCover.updateCornerSize(1.dpFloat)
        mAlbumTitle.text = item.surElement?.title

        mAlbumCover.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            mAlbumTitle.callOnClick()
        }
        mAlbumTitle.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }

            QuickListenTraceUtil.traceClick(
                if (isAigc) 68771 else 69048,
                trackId,
                baseFragment2.getQuickListenListData(),
                helper.bindingAdapterPosition,
                baseFragment2.getTabId(),
                baseFragment2.getShowFrom(),
            )
            handleAlbumClick(isAigc, item)
        }

        mSubscr.visibility = if (item.surElement?.interact?.subscribed == 1)
            View.GONE else View.VISIBLE
        mSubscr.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }

            QuickListenTraceUtil.trace68022(
                trackId,
                baseFragment2.getQuickListenListData(),
                helper.bindingAdapterPosition,
                baseFragment2.getTabId(),
                baseFragment2.getShowFrom(),
                item.surElement?.interact?.subscribed == 1
            )

            handleAlbumSubscribe(isAigc, item) {
                mSubscr.visibility = if (item.surElement?.interact?.subscribed == 1)
                    View.GONE else View.VISIBLE
            }
        }

        mGuideLikeLay.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            mLikeLay.callOnClick()
        }

        val isLiked = curItem.extraInfo?.isLike == true
        mLikeIcon.isSelected = isLiked
        mLikeLay.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }

            QuickListenTraceUtil.trace68021(
                trackId,
                baseFragment2.getQuickListenListData(),
                helper.bindingAdapterPosition,
                baseFragment2.getTabId(),
                baseFragment2.getShowFrom(),
                !mLikeIcon.isSelected)
            mGuideLikeLay.visibility = View.GONE

            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(ToolUtil.getCtx())
                return@setOnClickListener
            }

            LikeTrackManage.toLikeOrUnLike(trackId, !mLikeIcon.isSelected,
                object : IDataCallBack<Boolean> {
                override fun onSuccess(data: Boolean?) {
                    baseFragment2.getHomePageControl()?.saveBehaviorToLocal(curItem.refId ?: 0,
                        if (!mLikeIcon.isSelected) "点赞" else "取消点赞")

                    mLikeLottie.setAnimation(if (mLikeIcon.isSelected)
                        "lottie/quick_listen/track_to_unlike.json"
                    else "lottie/quick_listen/track_to_like.json")
                    mLikeLottie.removeAllAnimatorListeners()
                    mLikeLottie.cancelAnimation()
                    mLikeLottie.visibility = View.VISIBLE
                    mLikeLottie.addAnimatorListener(object : SimpleAnimatorListener {
                        override fun onAnimationStart(animation: Animator?) {
                            super.onAnimationStart(animation)
                            mLikeIcon.visibility = View.GONE
                        }

                        override fun onAnimationEnd(animation: Animator) {
                            super.onAnimationEnd(animation)
                            mLikeIcon.visibility = View.VISIBLE
                            mLikeLottie.visibility = View.GONE
                        }
                    })

                    mLikeLottie.playAnimation()
                }

                override fun onError(code: Int, message: String?) {
                    CustomToast.showFailToast(message ?: "请稍后再试")
                }
            })
        }

        mMoreShare.visibility = if (isAigc) View.GONE else View.VISIBLE
        mMoreIcon.visibility = if (isAigc) View.GONE else View.VISIBLE
        mMoreShare.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            QuickListenTraceUtil.trace68912(
                trackId,
                baseFragment2.getQuickListenListData(),
                helper.bindingAdapterPosition,
                baseFragment2.getTabId(),
                baseFragment2.getShowFrom()
            )
            handleShare(trackId)
        }
        mMoreIcon.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            handleMore(helper, item)
        }

        if (item.sug.isAvailable()) {
            if (item.sugAnimator == true) {
                mAiSug.text = "问AI · ${item.sug}"
                mAiSugArrow.visibility = View.VISIBLE
                mSugLay.visibility = View.VISIBLE
            } else {
                item.sugAnimator = true
                mAiSug.visibility = View.GONE
                mAiSugArrow.visibility = View.GONE
                mSugLay.visibility = View.GONE
                mSugLay.layoutParams.height = 0
                mSugLay.layoutParams = mSugLay.layoutParams
                val sugAnimator = ValueAnimator.ofFloat(0f, 1f)
                sugAnimator.duration = 450
                val dp40 = 40.dp
                sugAnimator.addUpdateListener {
                    mSugLay.layoutParams.height = (it.animatedValue as Float * dp40).toInt()
                    mSugLay.layoutParams = mSugLay.layoutParams
                }
                sugAnimator.addListener(object : SimpleAnimatorListener {
                    override fun onAnimationStart(animation: Animator?) {
                        super.onAnimationStart(animation)
                        mSugLay.visibility = View.VISIBLE
                    }

                    override fun onAnimationEnd(animation: Animator?) {
                        super.onAnimationEnd(animation)
                        mAiSug.text = ""
                        mAiSug.visibility = View.VISIBLE
                        mAiSugArrow.visibility = View.VISIBLE
                        mAiSug.text = "问AI · ${item.sug}"
//                        mAiSug.startFadeInScaleAnimation(item.sug!!) {
//                            val animator = AnimatorSet()
//                            animator.playTogether(
//                                ObjectAnimator.ofFloat(mAiSugArrow, "alpha", 0f, 1f),
//                                ObjectAnimator.ofFloat(mAiSugArrow, "translationX", (-10).dp.toFloat(), 0f)
//                            )
//                            animator.addListener(object : SimpleAnimatorListener {
//                                override fun onAnimationStart(animation: Animator?) {
//                                    super.onAnimationStart(animation)
//                                    mAiSugArrow.visibility = View.VISIBLE
//                                }
//                            })
//                            animator.duration = 10
//                            animator.start()
//                        }
                    }
                })
                sugAnimator.start()
                QuickListenTraceUtil.traceShow(
                    68917,
                    trackId,
                    baseFragment2.getQuickListenListData(),
                    helper.bindingAdapterPosition,
                    baseFragment2.getTabId(),
                    baseFragment2.getShowFrom(),
                    mapOf("keyWord" to (item.sug?: ""))
                )
            }
            mSugLay.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                QuickListenTraceUtil.traceClick(
                    68916,
                    trackId,
                    baseFragment2.getQuickListenListData(),
                    helper.bindingAdapterPosition,
                    baseFragment2.getTabId(),
                    baseFragment2.getShowFrom(),
                    mapOf("keyWord" to (item.sug ?: ""))
                )
                baseFragment2.getHomePageControl()?.sendAiAgentQuery(item.sug)
            }
        } else {
            mSugLay.visibility = View.GONE
        }

        if (isCurPlaying
            && (MMKVUtil.getInstance().getLong("main_quick_listen_cover_click_guide_mask", -1) == -1L
                    || MMKVUtil.getInstance().getLong("main_quick_listen_cover_click_guide_mask", -1) == (curItem.refId ?: 0))) {
            MMKVUtil.getInstance().saveLong(
                "main_quick_listen_cover_click_guide_mask", curItem.refId ?: 0
            )

            if (mCoverMask.isGone) {
                HandlerManager.postOnUIThreadDelay4Kt(5000) {
                    MMKVUtil.getInstance().saveLong(
                        "main_quick_listen_cover_click_guide_mask", -2
                    )

                    mCoverMask.visibility = View.GONE
                    mCoverMaskLay.visibility = View.GONE
                }
            }

            mCoverMask.visibility = View.VISIBLE
            mCoverMaskLay.visibility = View.VISIBLE
        } else {
            mCoverMask.visibility = View.GONE
            mCoverMaskLay.visibility = View.GONE
        }
    }

    private fun isCurPlaying(trackId: Long): Boolean {
        return PlayTools.getCurTrackId(ToolUtil.getCtx()) == trackId
                && XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying
    }

    private fun handleMore(helper: BaseViewHolder, item: QuickListenModel) {
        item.localExt = LocalExt(baseFragment2.getTabTitle(),
            helper.bindingAdapterPosition,
            helper.bindingAdapterPosition,
            baseFragment2.getHomePageControl()?.getFrom() ?: "")
        baseFragment2.getHomePageControl()?.sendMoreControlShow(Gson().toJson(item))
    }

    private fun findItemInSubElement(curPlayTrackId: Long, item: QuickListenModel): QuickListenModel? {
        val curItem = item.subElements?.firstOrNull { it.refId == curPlayTrackId }
        if (curItem == null) {
            return item.subElements?.firstOrNull { it.refId == item.refId } ?: item
        }
        return curItem
    }

    private fun changePlayingAnimator(
        isCurPlaying: Boolean,
        isCurTrack: Boolean,
        mCoverShadow: ImageView,
        mCover: RoundImageView,
        mCoverLotteView: XmLottieAnimationView,
        mCoverPlayIcon: ImageView,
    ) {
        if (!isCurTrack) {
            mCoverLotteView.visibility = View.GONE
            mCoverShadow.visibility = View.GONE
            mCoverShadow.alpha = 1f
            mCoverPlayIcon.visibility = View.VISIBLE
            mCover.scaleX = 0.85f
            mCover.scaleY = 0.85f
            mCover.setTag(R.id.main_album_cover, false)
            cleanAnimator(mCover)
        } else {
            if (isCurPlaying) {
                if (mCover.getTag(R.id.main_album_cover) == true) {
                    return
                }

                cleanAnimator(mCover)

                val animationSet = AnimatorSet()
                animationSet.playTogether(
                    ObjectAnimator.ofFloat(mCoverShadow, "alpha", 0f, 1f),
                    ObjectAnimator.ofFloat(mCover, "scaleX", 0.85f, 1f),
                    ObjectAnimator.ofFloat(mCover, "scaleY", 0.85f, 1f))
                animationSet.addListener(object : SimpleAnimatorListener {
                    override fun onAnimationEnd(animation: Animator?) {
                        super.onAnimationEnd(animation)
                        mCoverLotteView.playAnimation()
                    }
                })
                animationSet.start()
                mCoverPlayIcon.visibility = View.GONE
                mCoverLotteView.visibility = View.VISIBLE
                mCoverShadow.alpha = 0f
                mCoverShadow.visibility = View.VISIBLE
                mCover.setTag(R.id.host_id_recommend_show_tag_tv, animationSet)
                mCover.setTag(R.id.main_album_cover, true)
            } else {
                if (mCover.getTag(R.id.main_album_cover) == false) {
                    return
                }

                cleanAnimator(mCover)

                val animationSet = AnimatorSet()
                animationSet.playTogether(
                    ObjectAnimator.ofFloat(mCover, "scaleX", 1f, 0.85f),
                    ObjectAnimator.ofFloat(mCover, "scaleY", 1f, 0.85f))
                animationSet.addListener(object : SimpleAnimatorListener {
                    override fun onAnimationEnd(animation: Animator?) {
                        super.onAnimationEnd(animation)
                        mCoverPlayIcon.visibility = View.VISIBLE
                    }
                })
                animationSet.start()
                mCoverLotteView.visibility = View.GONE
                mCoverShadow.visibility = View.GONE
                mCover.setTag(R.id.host_id_recommend_show_tag_tv, animationSet)
                mCover.setTag(R.id.main_album_cover, false)
            }
        }
    }

    private fun cleanAnimator(mCover: RoundImageView) {
        val animatorSet = mCover.getTag(R.id.host_id_recommend_show_tag_tv)
        if (animatorSet is AnimatorSet) {
            animatorSet.removeAllListeners()
            animatorSet.cancel()
            mCover.setTag(R.id.host_id_recommend_show_tag_tv, null)
        }
    }

    private fun handleShare(trackId: Long) {
        val params: MutableMap<String, String> = HashMap()
        params["device"] = "android"
        params["trackId"] = trackId.toString() + ""
        val curPage = "quickListenPage"
        val subType = 1154

        val mProgressDialog = MyProgressDialog(MainApplication.getMainActivity())
        mProgressDialog.setTitle("加载中...")
        mProgressDialog.delayShow()

        CommonRequestM.getTrackInfoDetail(params, object : IDataCallBack<TrackM?> {
            override fun onSuccess(trackM: TrackM?) {
                mProgressDialog.dismiss()
                if (trackM == null) {
                    return
                }

                val type = ICustomShareContentType.SHARE_TYPE_TRACK
                val sharePanelType = SharePanelType.FUNCTION_3
                val contentModel = ShareWrapContentModel(
                    sharePanelType, curPage, type
                )
                contentModel.soundInfo = trackM
                contentModel.mShowSuccessDialog = false
                contentModel.showFamilyInfo = true
                contentModel.isShowPosterHead = true
                contentModel.paramSubType = subType

                contentModel.isFromPlayPage = true
                contentModel.fromPage = curPage
                contentModel.trackForm = QuickListenTabAbManager.QUICK_LISTEN_BUNDLE_NAME
                contentModel.mShareAdRequestParams = ShareAdRequestParams(
                    AdManager.SHARE_AD_SOURCE_PAGE_SOUND, trackM.dataId.toString() + ""
                )

                ShareManager(MainApplication.getMainActivity(), contentModel) {
                    Logger.d("QuickListenShare", "share success")
                    QuickListenDataManager.getInstance().share(trackId)
                }.showSharePanelDialog()

            }

            override fun onError(code: Int, message: String) {
                mProgressDialog.dismiss()
            }
        })
    }


    private fun handleAlbumSubscribe(isAigc: Boolean, item: QuickListenModel, handleOk: IHandleOk) {
        val subscribeBizType = "50008"
        val followBizType = "16"
        val followSubBizType = "8"
        val isFavorite = item.surElement?.interact?.subscribed == 1
        val anchorId: Long = item.surElement?.anchor?.uid ?: -1
        val albumId = item.surElement?.refId ?: 0

        if (isAigc) {
            val agentAudioId: Long = item.surElement?.refId ?: 0
            val agentAudioSource = "KUAITING"

            if (agentAudioId > 0 && !TextUtils.isEmpty(agentAudioSource)) {
                val param = AgentRadioParam(agentAudioId, if (isFavorite) 1 else 0, agentAudioSource)
                doCollectAction(param, object : IDataCallBack<Boolean> {
                    override fun onSuccess(data: Boolean?) {
                        item.surElement?.interact?.subscribed = if (item.surElement?.interact?.subscribed != 1) 1 else 0
                        handleOk.onReady()
                    }

                    override fun onError(code: Int, message: String) {
                    }
                }, true)
                return
            }
        }

        val albumM = AlbumM()
        albumM.isFavorite = isFavorite
        albumM.id = albumId.toLong()
        albumM.uid = anchorId
        try {

            val followSubBizTypeInteger = followSubBizType.toInt()
            val param = AlbumCollectParam(
                "QuickListen", subscribeBizType.toInt(), followBizType.toInt(), followSubBizTypeInteger, albumM, true
            )
            param.showFollowDialog = false
            param.useNewFollowDialog = false

            AlbumEventManage.doCollectActionV3(
                param, baseFragment2, object : ICollectWithFollowStatusCallback {
                    override fun followDialogAction(status: Int) {
                    }

                    override fun onCollectSuccess(code: Int, isCollected: Boolean) {
                        item.surElement?.interact?.subscribed = if (isCollected) 1 else 0
                        handleOk.onReady()
                    }

                    override fun onError() {
                    }

                    override fun getFollowSubBizType(): Int {
                        return followSubBizTypeInteger
                    }
                })
        } catch (throwable: Throwable) {
            throwable.printStackTrace()
        }
    }

    private fun handleAlbumClick(isAigc: Boolean, item: QuickListenModel) {
        if ((item.surElement?.refId ?: 0) > 0) {
            if (isAigc) {
                try {
                    val rnUrl = "iting://open?msg_type=94&bundle=rn_ai_chat&scene=FlashCreateAIRadio" +
                            "&agent=xiaoya&initialPage=AiRadioPlay&from=quickListen&aiRadioId=${item.surElement?.refId}"
                    StableProvider.getInstance().handleIting(MainApplication.getMainActivity(),
                        Uri.parse(rnUrl))
                } catch (e: Exception) {
                }
                return
            }

            try {
                StableProvider.getInstance().handleIting(MainApplication.getMainActivity(),
                    Uri.parse("iting://open?msg_type=13&album_id=${item.surElement?.refId}"))
            } catch (e: Exception) {
            }
        }
    }



    private fun handlerSeekBarMask(
        progress: Double,
        duration: Long,
        mSeekTimer: TextView,
        mSeekDuration: TextView,
        mDoc: ReactDocCellView,
    ) {
        var curDuration = (XmPlayerManager.getInstance(ToolUtil.getCtx()).duration).toLong()
        if (curDuration == 0L) {
            curDuration = duration * 1000
        }
        val progressStr = TimeHelper.toTime((progress * curDuration) / 1000f)
        val durationStr = TimeHelper.toTime(((curDuration / 1000f).toDouble()))
        mSeekTimer.typeface = mTypeface
        mSeekDuration.typeface = mTypeface

        mSeekTimer.text = progressStr
        mSeekDuration.text = " / $durationStr"
        mDoc.docView.updateTime((progress * curDuration).toLong(), true)
    }
}