package com.ximalaya.ting.android.main.quicklistenmodule.adapter.provider

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import com.ximalaya.ting.android.alphamovie.AlphaMovieView
import com.ximalaya.ting.android.alphamovie.Constants
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.adapter.base.provider.BaseItemProvider
import com.ximalaya.ting.android.host.adapter.base.viewholder.BaseViewHolder
import com.ximalaya.ting.android.host.data.model.QuickElementType
import com.ximalaya.ting.android.host.data.model.QuickListenModel
import com.ximalaya.ting.android.host.listener.SimpleAnimatorListener
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenManager
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage
import com.ximalaya.ting.android.host.util.common.RemoteTypefaceManager
import com.ximalaya.ting.android.host.util.common.TimeHelper
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.kt.isAvailable
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.main.findModule.FindViewUtils
import com.ximalaya.ting.android.main.quicklistenmodule.fragment.QuickListenItemPageFragment
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenHomeHelper
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenTraceUtil
import com.ximalaya.ting.android.main.quicklistenmodule.view.IInterceptProgress
import com.ximalaya.ting.android.main.quicklistenmodule.view.IOnSeekBarChangeListener
import com.ximalaya.ting.android.main.quicklistenmodule.view.ListenProgressSeekBar
import com.ximalaya.ting.android.main.quicklistenmodule.view.ScaleableSeekBar
import com.ximalaya.ting.android.main.stable.R
import com.ximalaya.ting.android.main.util.SimpleDownloadTask
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager

/**
 * Created by nali on 2025/6/25.
 * <AUTHOR>
 */
class XimaTenCardProvider(val baseFragment2: QuickListenItemPageFragment): BaseItemProvider<QuickListenModel>() {
    override val itemViewType: Int
        get() = QuickElementType.XIMA_TEN_CARD.viewType
    override val layoutId: Int
        get() = R.layout.main_quick_listen_xima_ten_card_lay

    override fun convert(helper: BaseViewHolder, item: QuickListenModel) {
        val mAlpha: AlphaMovieView = helper.getView<AlphaMovieView>(R.id.main_alpha)
        val mTime: TextView = helper.getView<TextView>(R.id.main_time)
        val mTitle: TextView = helper.getView<TextView>(R.id.main_title)
        val mLine: View = helper.getView<View>(R.id.main_line)
        val mPlayBtn: ImageView = helper.getView<ImageView>(R.id.main_ximaten_play_btn)
        val mInformationCount: TextView = helper.getView<TextView>(R.id.main_information_count)
        val mSeekBar: ListenProgressSeekBar = helper.getView<ListenProgressSeekBar>(R.id.main_seek_bar)
        val mLinearLay: LinearLayout = helper.getView<LinearLayout>(R.id.main_linear_lay)
        val mDescLay: View = helper.getView(R.id.main_desc_lay)
        val mDescTitle: TextView = helper.getView(R.id.main_desc_title)
        val mDescContent: TextView = helper.getView(R.id.main_desc)
        val mLikeLay: View = helper.getView(R.id.main_like_lay)
        val mLikeBtn: ImageView = helper.getView(R.id.main_like)
        val mLikeTitle: TextView = helper.getView(R.id.main_like_title)

        val mUnLikeLay: View = helper.getView(R.id.main_unlike_lay)
        val mUnLikeBtn: ImageView = helper.getView(R.id.main_unlike)
        val mUnLikeTitle: TextView = helper.getView(R.id.main_unlike_title)

        val mSeekBarMask: View = helper.getView(R.id.main_mask)
        val mSeekTimer: TextView = helper.getView(R.id.main_seek_timer)
        val mSeekDuration: TextView = helper.getView(R.id.main_seek_duration)
        val mSeekMaskTitle: TextView = helper.getView(R.id.main_mask_title)

        val curItem = if (item.isXimaTen()) findItemInSubElement(PlayTools.getCurTrackId(ToolUtil.getCtx()), item) else item
        if (curItem == null) {
            return
        }

        if ((item.subElements?.first()?.extraInfo?.likeStatus ?: -1) == -1) {
            item.subElements?.forEach {
                it.extraInfo?.likeStatus = if (it.extraInfo?.isLike == true) 1 else 0
            }
        }

        QuickListenHomeHelper.calculateHighlightPositions(item)
        val trackId = curItem.refId ?: 0

        QuickListenHomeHelper.downloadVideo(object : SimpleDownloadTask.DownloadCallback {
            override fun onSuccess() {
                HandlerManager.postOnMainAuto {
                    if (baseFragment2.canUpdateUi()) {
                        setMovieView(mAlpha, QuickListenHomeHelper.getDownloadVideoFilePath())
                    }
                }
            }

            override fun onFailed() {
            }

            override fun onProgress(progress: Int) {
            }
        })

        val overMediumScreenDevice = QuickListenHomeHelper.isOverMediumScreenDevice()
        (mTime.layoutParams as? MarginLayoutParams)?.topMargin =
            if (overMediumScreenDevice) 64.dp else 44.dp

        mTime.text = item.subTitle
        RemoteTypefaceManager.setRemoteTypeface(
            RemoteTypefaceManager.RemoteTypeface.SourceHanSansCNHeavy,
            mTitle
        )
        mTitle.text = item.title

        if (QuickListenHomeHelper.isFirstEntryToday() || item.ximaTenAnimatoring == true) {
            if (item.ximaTenAnimatoring != true) {
                item.ximaTenAnimatoring = true
                mLinearLay.visibility = View.VISIBLE
                mDescLay.visibility = View.INVISIBLE
                if (mLinearLay.getTag(R.id.host_id_recommend_show_tag_cache) != item.subElements?.hashCode()) {
                    mLinearLay.setTag(R.id.host_id_recommend_show_tag_cache, item.subElements?.hashCode())
                    mLinearLay.removeAllViews()

                    item.subElements?.forEach {
                        val view = createXimaTenItem(it, overMediumScreenDevice)
                        val layoutParams = LinearLayout.LayoutParams(
                            ViewGroup.LayoutParams.WRAP_CONTENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT)
                            .apply { bottomMargin = 24.dp }

                        mLinearLay.addView(view, layoutParams)
                    }
                    mDescLay.visibility = View.GONE
                    mDescContent.visibility = View.GONE
                    mLikeLay.visibility = View.GONE
                    createXimaTenItemAnimator(mLinearLay, mDescLay, mDescTitle, mDescContent, mLikeLay, item)
                }
            }
        } else {
            mLinearLay.visibility = View.INVISIBLE
            mDescLay.visibility = View.VISIBLE
            mDescTitle.visibility = View.VISIBLE
            mDescContent.visibility = View.VISIBLE
            mLikeLay.visibility = View.VISIBLE
        }

        mDescTitle.text = curItem.title
        mDescContent.maxLines = if (QuickListenHomeHelper.isSmallScreenDevice()) 5 else 7
        mDescContent.text = if (curItem.summary.isAvailable()) curItem.summary else "暂无简介"
        mDescTitle.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            mInformationCount.callOnClick()
        }
        mDescContent.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            mInformationCount.callOnClick()
        }

        mPlayBtn.isSelected = !isCurPlaying(trackId)
        mPlayBtn.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            QuickListenHomeHelper.handlePlayOrPause(trackId, baseFragment2.getHomePageControl())
        }

        mPlayBtn.visibility = if (QuickListenTabAbManager.isNewUserForQuickListen) View.INVISIBLE else View.VISIBLE

        mInformationCount.text = "共${item.subElements?.size ?: 0}条资讯"
        mInformationCount.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            baseFragment2.getHomePageControl()?.sendCardListShow(
                item.elementType ?: "", item.refId?.toString() ?: ""
            )
        }

        mSeekBar.setTrackId(trackId)
        mSeekBar.onPlayProgress(XmPlayerManager.getInstance(ToolUtil.getCtx()).playCurrPositon, XmPlayerManager.getInstance(ToolUtil.getCtx()).duration)
        mSeekBar.setOnSeekBarListener(object : IOnSeekBarChangeListener {
            override fun onProgressChanged(progress: Double, fromUser: Boolean) {
                if (fromUser) {
                    handlerSeekBarMask(progress, mSeekTimer, mSeekDuration, mSeekMaskTitle, item)
                }
            }

            override fun onStartTrackingTouch(progress: Double) {
                QuickListenHomeHelper.getViewPager(mSeekBar)?.setCanSlide(false)
                mSeekBarMask.visibility = View.VISIBLE
                handlerSeekBarMask(progress, mSeekTimer, mSeekDuration, mSeekMaskTitle, item)
            }

            override fun onStopTrackingTouch(progress: Double) {
                QuickListenTraceUtil.trace68016(trackId, baseFragment2.getQuickListenListData(),
                    helper.bindingAdapterPosition,
                    baseFragment2.getTabId(),
                    baseFragment2.getShowFrom(),
                    (XmPlayerManager.getInstance(ToolUtil.getCtx()).duration * 1f * progress / 1000).toInt())

                QuickListenHomeHelper.getViewPager(mSeekBar)?.setCanSlide(true)
                mSeekBarMask.visibility = View.GONE
                handleSlidingComplete(progress, item)
            }
        })
        mSeekBar.setQuickListenHomePageControl(baseFragment2.getHomePageControl())

        if (mSeekBar.getTag(R.id.main_id_item_data) != item) {
            mSeekBar.setTag(R.id.main_id_item_data, item)

            var currentPosition = 0L

            // 计算高亮位置
            val positions = item.subElements?.mapIndexed { index, data ->
                val position = currentPosition / (item.totalDuration ?: 1).toFloat()
                currentPosition += data.duration ?: 0L
                // 只返回第二个元素开始的位置
                if (index > 0) {
                    ScaleableSeekBar.KeyPoint().apply {
                        id = data.refId ?: 0
                        progress = position
                    }
                } else {
                    null
                }
            }?.filterNotNull()

            if (positions != null) {
                mSeekBar.setKeyPoints(positions)
            }
        }

        mSeekBar.setMinimumTrackTintColor(
            ContextCompat.getColor(ToolUtil.getCtx(),
            R.color.main_color_4cffffff))
        mSeekBar.setMaximumTrackTintColor(
            ContextCompat.getColor(ToolUtil.getCtx(),
            R.color.host_color_11ffffff))

        mSeekBar.setInterceptProgress(object : IInterceptProgress {
            override fun interfaceValue(currPos: Int, duration: Int): Float {
                var previousDuration = 0L
                val mTrackId = PlayTools.getCurTrackId(ToolUtil.getCtx())
                run {
                    item.subElements?.forEach { quickListenModel ->
                        if (quickListenModel.refId == mTrackId) {
                            return@run
                        }
                        previousDuration += quickListenModel.duration ?: 0
                    }
                }

                if ((item.totalDuration ?: 0) > 0) {
                    return (previousDuration + currPos / 1000) * 1.0f / item.totalDuration!!
                }
                return 0f
            }
        })

        updateLikeStatus(curItem, mLikeLay, mUnLikeLay, mUnLikeTitle, mLikeBtn, mUnLikeBtn, mLikeTitle)

        mLikeLay.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }

            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(ToolUtil.getCtx())
                return@setOnClickListener
            }

            QuickListenTraceUtil.trace68021(
                trackId,
                baseFragment2.getQuickListenListData(),
                helper.bindingAdapterPosition,
                baseFragment2.getTabId(),
                baseFragment2.getShowFrom(),
                curItem.extraInfo?.isLike != true)
            handleLikeOrUnLike(curItem, curItem.extraInfo?.isLike != true)
        }

        mUnLikeLay.setOnClickListener {
            if (!OneClickHelper.getInstance().onClick(it)) {
                return@setOnClickListener
            }
            QuickListenTraceUtil.trace68255(
                trackId,
                baseFragment2.getQuickListenListData(),
                helper.bindingAdapterPosition,
                baseFragment2.getTabId(),
                baseFragment2.getShowFrom(),
                curItem.extraInfo?.likeStatus == 0
            )

            var needPlayNext = false
            if (curItem.extraInfo?.likeStatus == 0) {
                curItem.extraInfo?.likeStatus = 2
                needPlayNext = true
            } else {
                curItem.extraInfo?.likeStatus = 0
            }

            baseFragment2.getHomePageControl()?.saveBehaviorToLocal(curItem.refId ?: 0,
                if (curItem.extraInfo?.likeStatus == 1) "取消点赞" else "负反馈")

            updateLikeStatus(curItem, mLikeLay, mUnLikeLay, mUnLikeTitle, mLikeBtn, mUnLikeBtn, mLikeTitle)

            if (needPlayNext) {
                val realPosition = Math.max(0, helper.bindingAdapterPosition)
                var itemPosition = realPosition
                val listData = baseFragment2.getQuickListenListData()
                if (listData != null && listData.size > realPosition) {
                    val curData = QuickListenManager.getQuickListenModelFromListByTrackId(trackId, listData)
                    val listenModel = listData.get(realPosition)
                    if (listenModel.isXimaTen() || listenModel.isTrackCollect()) {
                        itemPosition = listenModel.subElements?.indexOf(curData) ?: 0
                    }
                }
                baseFragment2.getHomePageControl()?.negativeXimaTenData(curItem.refId ?: 0, realPosition + 1, itemPosition + 1)
                XmPlayerManager.getInstance(ToolUtil.getCtx()).playNext()
            }
        }
    }

    private fun updateLikeStatus(
        curItem: QuickListenModel, mLikeLay: View, mUnLikeLay: View,
        mUnLikeTitle: TextView, mLikeBtn: ImageView,
        mUnLikeBtn: ImageView, mLikeTitle: TextView,
    ) {
        if (curItem.extraInfo?.likeStatus == 0) {
            mLikeLay.visibility = View.VISIBLE
            mUnLikeLay.visibility = View.VISIBLE
            mUnLikeTitle.visibility = View.GONE

            mLikeBtn.isSelected = false
            mUnLikeBtn.isSelected = false
            mLikeTitle.text = "点赞"
        } else if (curItem.extraInfo?.likeStatus == 1) {
            mLikeLay.visibility = View.VISIBLE
            mUnLikeLay.visibility = View.GONE
            mUnLikeTitle.visibility = View.GONE

            mLikeBtn.isSelected = true
            mLikeTitle.text = "已点赞"
        } else if (curItem.extraInfo?.likeStatus == 2) {
            mLikeLay.visibility = View.GONE
            mUnLikeLay.visibility = View.VISIBLE
            mUnLikeTitle.visibility = View.VISIBLE

            mUnLikeBtn.isSelected = true
        }
    }

    private fun handleLikeOrUnLike(curData: QuickListenModel, isLike: Boolean) {
        LikeTrackManage.toLikeOrUnLike(curData.refId ?: 0, isLike,
            object : IDataCallBack<Boolean> {
                override fun onSuccess(data: Boolean?) {
                    curData.extraInfo?.isLike = isLike
                    curData.extraInfo?.likeStatus = if (isLike) 1 else 0

                    baseFragment2.getHomePageControl()?.saveBehaviorToLocal(curData.refId ?: 0,
                        if (curData.extraInfo?.likeStatus == 1) "点赞" else "负反馈")
                }

                override fun onError(code: Int, message: String?) {
                    if (code == -1) {
                        CustomToast.showFailToast(message ?: "请稍后再试")
                    } else {
                        CustomToast.showFailToast(message ?: "请稍后再试")
                    }
                }
            })
    }

    private fun isCurPlaying(trackId: Long): Boolean {
        return PlayTools.getCurTrackId(ToolUtil.getCtx()) == trackId
                && XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying
    }

    private fun findItemInSubElement(curPlayTrackId: Long, item: QuickListenModel): QuickListenModel? {
        val curItem = item.subElements?.firstOrNull { it.refId == curPlayTrackId }
        if (curItem == null) {
            return item.subElements?.firstOrNull { it.refId == item.refId } ?: item
        }
        return curItem
    }

    private fun createXimaTenItem(item: QuickListenModel, overMediumScreenDevice: Boolean): View {
        val view = LayoutInflater.from(ToolUtil.getCtx()).inflate(R.layout.main_quick_listen_xima_ten_item, null, false)
        val mDesc: TextView = view.findViewById(R.id.main_item_item)
        mDesc.maxLines = if (overMediumScreenDevice) 2 else 1
        mDesc.text = item.title
        return view
    }

    private fun setMovieView(alphaMovieView: AlphaMovieView?, mVideoFilePath: String?) {
        if (alphaMovieView == null || mVideoFilePath == null) return
        if (alphaMovieView.isGone || alphaMovieView.isReleased) {
            alphaMovieView.visibility = View.VISIBLE
            if (alphaMovieView.getMediaPlayer() != null) {
                alphaMovieView.forceResetStateToIdle()
                alphaMovieView.getMediaPlayer().setVolume(0f, 0f)
                alphaMovieView.setLooping(true)
                alphaMovieView.setTopLinearAlphaRange(0f)
                alphaMovieView.setScaleType(Constants.FIT_CENTER_INSIDE)
            }
            if (!TextUtils.isEmpty(mVideoFilePath)) {
                alphaMovieView.setVideoFromSD(mVideoFilePath)
            }
        }
    }

    private fun createXimaTenItemAnimator(
        linearLayout: LinearLayout, descLayout: View,
        descTitle: TextView, descContent: TextView, likeLay: View,
        item: QuickListenModel,
    ) {
        HandlerManager.postOnUIThreadDelay4Kt(5000) {
            val childCount = linearLayout.childCount
            if (childCount < 2) {
                return@postOnUIThreadDelay4Kt
            }

            val animatorSet = AnimatorSet()
            val animatorList = mutableListOf<Animator>()
            for (i in (childCount - 1) downTo 0) {
                linearLayout.getChildAt(i).pivotX = 0f
                linearLayout.getChildAt(i).pivotY = (linearLayout.getChildAt(i).height / 2).toFloat()
                val itemSet = AnimatorSet()

                if (i == 0) {
                    val itemText = linearLayout.getChildAt(i).findViewById<View>(R.id.main_item_item)
                    itemText.pivotX = 0f
                    itemText.pivotY = 0f
                    itemSet.playTogether(
                        ObjectAnimator.ofFloat(itemText, "scaleX", 1f, 1.14f),
                        ObjectAnimator.ofFloat(itemText, "scaleY", 1f, 1.14f)
                    )
                } else {
                    itemSet.playTogether(
                        ObjectAnimator.ofFloat(linearLayout.getChildAt(i), "translationY", 0f, 40.dpFloat),
                        ObjectAnimator.ofFloat(linearLayout.getChildAt(i), "scaleX", 1f, 0.5f),
                        ObjectAnimator.ofFloat(linearLayout.getChildAt(i), "scaleY", 1f, 0.5f),
                        ObjectAnimator.ofFloat(linearLayout.getChildAt(i), "alpha", 1f, 0f)
                    )
                }
                itemSet.duration = 333
                itemSet.startDelay = (90 * ((childCount - 1) - i)).toLong()
                animatorList.add(itemSet)
            }

            animatorSet.playTogether(animatorList)
            animatorSet.addListener(object : SimpleAnimatorListener {
                override fun onAnimationEnd(animation: Animator?) {
                    super.onAnimationEnd(animation)
                    linearLayout.visibility = View.INVISIBLE
                    descLayout.visibility = View.VISIBLE
                }
            })

            val afterAnimatorSet = AnimatorSet()
            descContent.pivotX = 0f
            descContent.pivotY = 0f
            afterAnimatorSet.playTogether(
                ObjectAnimator.ofFloat(descContent, "alpha", 0f, 1f),
                ObjectAnimator.ofFloat(descContent, "scaleX", 0.5f, 1f),
                ObjectAnimator.ofFloat(descContent, "scaleY", 0.5f, 1f),
            )

            afterAnimatorSet.addListener(object : SimpleAnimatorListener {
                override fun onAnimationStart(animation: Animator?) {
                    super.onAnimationStart(animation)
                    descContent.visibility = View.VISIBLE
                }
            })

            val likeLayoutAnimator = ObjectAnimator.ofFloat(likeLay, "alpha", 0f, 1f)
            likeLayoutAnimator.addListener(object : SimpleAnimatorListener {
                override fun onAnimationStart(animation: Animator?) {
                    super.onAnimationStart(animation)
                    likeLay.visibility = View.VISIBLE
                }
            })

            val allAnimatorSet = AnimatorSet()
            allAnimatorSet.playSequentially(animatorSet, afterAnimatorSet, likeLayoutAnimator)
            allAnimatorSet.addListener(object : SimpleAnimatorListener {
                override fun onAnimationEnd(animation: Animator?) {
                    super.onAnimationEnd(animation)
                    item.ximaTenAnimatoring = false
                }
            })
            allAnimatorSet.start()
        }
    }

     private fun handlerSeekBarMask(
         percent: Double,
         mSeekTimer: TextView,
         mSeekDuration: TextView,
         mSeekMaskTitle: TextView,
         data: QuickListenModel,
     ) {
         val trackAndPositionByProgress = QuickListenHomeHelper.getTrackAndPositionByProgress(
             data, percent.toFloat()
         )

         if (trackAndPositionByProgress == null) {
             return
         }

         val progressStr = TimeHelper.toTime(((data.totalDuration ?: 0) * percent))
         val durationStr = TimeHelper.toTime(((data.totalDuration ?: 0)).toDouble())

         FindViewUtils.setTypeface(mSeekTimer, "XmlyNumberV1.0-Regular.otf")
         FindViewUtils.setTypeface(mSeekDuration, "XmlyNumberV1.0-Regular.otf")

         mSeekTimer.text = progressStr
         mSeekDuration.text = " / $durationStr"
         mSeekMaskTitle.text = data.subElements?.get(trackAndPositionByProgress.index)?.title
     }

    private fun handleSlidingComplete(
        progress: Double,
        data: QuickListenModel,
    ) {
        val trackAndPosition = QuickListenHomeHelper.getTrackAndPositionByProgress(
            data, progress.toFloat()
        )

        if (trackAndPosition == null) {
            return
        }

        // 播放的和当前声音一致
        if (PlayTools.getCurTrackId(ToolUtil.getCtx()) != (trackAndPosition.data.refId ?: 0)) {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).saveSoundHistoryPos(
                trackAndPosition.data.refId ?: 0, 0
            )

            XmPlayerManager.getInstance(ToolUtil.getCtx()).play(trackAndPosition.index)
        } else {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).seekTo((trackAndPosition.position * 1000).toInt())
        }
    }
}

// 定义数据类来存储返回结果
data class TrackPosition(
    val data: QuickListenModel,
    val position: Long,    // 相对位置（毫秒）
    val index: Int,         // 索引
)
