package com.ximalaya.ting.android.main.quicklistenmodule.fragment

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.WorkerThread
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.updateLayoutParams
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager.widget.ViewPager
import com.airbnb.lottie.LottieProperty
import com.airbnb.lottie.model.KeyPath
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.framework.util.ViewUtil.PADDING_TOP
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.data.model.QuickElementType
import com.ximalaya.ting.android.host.data.model.QuickListenModel
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.IQuickListenTabFragment
import com.ximalaya.ting.android.host.listener.IMainTabClickCallback
import com.ximalaya.ting.android.host.manager.PlanTerminateManagerForQuickListen
import com.ximalaya.ting.android.host.manager.QuickListenFrom
import com.ximalaya.ting.android.host.manager.QuickListenParams
import com.ximalaya.ting.android.host.manager.QuickListenPerformance
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager
import com.ximalaya.ting.android.host.manager.TempDataManager
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.SimpleXmVideoPlayStatusListener
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.quicklisten.ICallback
import com.ximalaya.ting.android.host.manager.quicklisten.IPlayCallback
import com.ximalaya.ting.android.host.manager.quicklisten.IQuickListenPage
import com.ximalaya.ting.android.host.manager.quicklisten.KEY_QUICK_LISTEN_LAST_TAB_ID
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManager
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenManager
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenPlayUtil
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenTabsManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.rn.ReactNativeAdapter
import com.ximalaya.ting.android.host.manager.rn.VerticalViewPager
import com.ximalaya.ting.android.host.model.quicklisten.ELEMENT_TYPE_NEW_USER
import com.ximalaya.ting.android.host.model.quicklisten.ELEMENT_TYPE_RECOMMEND
import com.ximalaya.ting.android.host.model.quicklisten.QuickListenTab
import com.ximalaya.ting.android.host.quicklisten.view.CustomReactClipViewGroup
import com.ximalaya.ting.android.host.quicklisten.view.IAIContainer
import com.ximalaya.ting.android.host.quicklisten.view.QuickListenPlayStatusIcon
import com.ximalaya.ting.android.host.util.HomePageQuickListenGuideUtil
import com.ximalaya.ting.android.host.util.RouteServiceUtil
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.RemoteTypefaceManager
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.color
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.kt.isAvailable
import com.ximalaya.ting.android.host.util.kt.isNotAvailable
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.host.view.other.MyViewPagerCanDisableFillNeighbourTab
import com.ximalaya.ting.android.main.quicklistenmodule.adapter.QuickListenTabAdapter
import com.ximalaya.ting.android.main.quicklistenmodule.iinterface.IQuickListenHomePageControl
import com.ximalaya.ting.android.main.quicklistenmodule.iinterface.IQuickListenItemPageControl
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenConfigManager
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenHomeHelper
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenTraceUtil
import com.ximalaya.ting.android.main.quicklistenmodule.view.CustomSwipeRefreshLayout
import com.ximalaya.ting.android.main.quicklistenmodule.view.QuickListenSlidingTabStrips
import com.ximalaya.ting.android.main.quicklistenmodule.view.QuickListenTabWidget
import com.ximalaya.ting.android.main.request.MainStableRequest
import com.ximalaya.ting.android.main.stable.R
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException
import com.ximalaya.ting.android.opensdk.util.EasyConfigure
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask
import com.ximalaya.ting.android.player.video.listener.IXmVideoPlayStatusListener
import com.ximalaya.ting.android.read.widgets.localReader.util.GSON
import com.ximalaya.ting.android.routeservice.RouterServiceManager
import com.ximalaya.ting.android.routeservice.service.history.ICloudyHistory
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONArray
import org.json.JSONObject
import kotlin.math.abs

/**
 * Created by nali on 2025/6/20.
 * <AUTHOR>
 */
private const val TAG = "QuickListenHomePageFragment"
private const val BROADCAST = "ShortStreamBroadcast"

class QuickListenHomePageFragment : BaseFragment2(), IQuickListenTabFragment {
    private val mMainBackBtn: ImageView by lazy { findViewById<ImageView>(R.id.main_back_btn) }
    private val mMainSetting: ImageView by lazy { findViewById<ImageView>(R.id.main_setting) }
    private val mMainTabs: QuickListenSlidingTabStrips by lazy { findViewById(R.id.main_tabs) }
    private val mMainViewPager: MyViewPagerCanDisableFillNeighbourTab by lazy { findViewById(R.id.main_view_pager) }
    private val mTitleBar: View by lazy { findViewById<View>(R.id.main_title_bar_1) }
    private val mRefreshTips: TextView by lazy { findViewById<TextView>(R.id.main_refresh_tips) }
    private val mAiChatContainer: CustomReactClipViewGroup by lazy { findViewById(R.id.main_ai_chat_container) }
    private val mDownArrow: ImageView by lazy { findViewById(R.id.main_down_arrow) }
    private val mTitleRefreshHint: TextView by lazy { findViewById(R.id.main_title_refresh_hint) }
    private val mBottomLay: FrameLayout by lazy { findViewById(R.id.main_bottom_lay) }
    private val mPlayBtn: QuickListenPlayStatusIcon by lazy { findViewById(R.id.main_play_btn) }
    private var mRnView: IRNFunctionRouter.IRNEmbeddedView? = null
    private var mAdapter: QuickListenTabAdapter? = null

    private val mPlayStatusListeners = mutableSetOf<IXmPlayerStatusListener>()
    private val mFrom: String? by lazy { arguments?.getString("quick_listen_from") }
    private var mTabs: List<QuickListenTab>? = null
    private var mLastClickTime: Long = 0
    private var mFinishToShowPupop = false
    private var isFromTab: Boolean = false

    private var mPausedByAgentAudio = false
    private var mIsGotoAgent = false

    private var mQuickListenXiaoyaLay: IAIContainer? = null

    private var isResumed = false
    private var mSkipUpdatePlayStatus = 0L

    private var notifyPageChange = true
    private var mLastQuickListenTabClickTime = 0L

    override fun getPageLogicName(): String {
        return this::class.java.simpleName
    }

    companion object {
        fun newInstance(): QuickListenHomePageFragment {
            return QuickListenHomePageFragment()
        }
    }

    override fun initUi(savedInstanceState: Bundle?) {
        try {
            QuickListenHomeHelper.preloadVideoBundle()
            val quickListenParams = arguments?.getParcelable<QuickListenParams>("quick_listen_params")

            var tabId = quickListenParams?.tabId
            var trackId = quickListenParams?.trackId ?: 0
            val forceItems = quickListenParams?.forceItems

            isFromTab = arguments?.getBoolean("from_home_tab", false) ?: false
            val useRn = arguments?.getBoolean("quick_listen_tab_use_rn", false) ?: false
            var openFragmentBeforeIsPlaying: Boolean? = null
            if (TempDataManager.getInstance().containsBooleanKey(PreferenceConstantsInHost.KEY_OPEN_PLAY_FRAGMENT_BEFORE_PLAYING)) {
                openFragmentBeforeIsPlaying = TempDataManager.getInstance().getBoolean(PreferenceConstantsInHost.KEY_OPEN_PLAY_FRAGMENT_BEFORE_PLAYING)
            }
            TempDataManager.getInstance().removeBoolean(PreferenceConstantsInHost.KEY_OPEN_PLAY_FRAGMENT_BEFORE_PLAYING)
            Logger.log("QuickListenHomePageFragment : initUi openFragmentBeforeIsPlaying=$openFragmentBeforeIsPlaying ")
            MMKVUtil.getInstance().saveString(PreferenceConstantsInHost.KEY_OPEN_QUICK_LISTEN_DAY, DateTimeUtil.getNowDateStr())

            if (isFromTab) {
                val view = MainApplication.getMainActivity()?.findViewById<CustomReactClipViewGroup>(R.id.fragment_quick_listen_xiaoya_lay)
                mQuickListenXiaoyaLay = QuickListenHomeHelper.createAiContain(view)
            } else if (QuickListenTabAbManager.isNewUserForQuickListen) {
                mBottomLay.visibility = View.VISIBLE
                mPlayBtn.setOnClickListener(mOnClickSetting)
                updatePlayStatus(true)
                mQuickListenXiaoyaLay = QuickListenHomeHelper.createAiContain(mAiChatContainer)
            }

        // 如果是从推送或者习惯听进入的,首次退出后出现引导气泡
        if ((mFrom == QuickListenFrom.SCENE_LISTEN.from || mFrom == QuickListenFrom.PUSH.from) && !MMKVUtil.getInstance().getBoolean("quick_listen_is_opened")) {
            mFinishToShowPupop = true
        }

            if (tabId.isNotAvailable()) {
                runCatching {
                    val tabIdInt = QuickListenDataManager.getInstance().getInitTab(openFragmentBeforeIsPlaying)
                    if (tabIdInt <= 0) {
                        tabId = "-1"
                        trackId = 0
                    } else {
                        tabId = "$tabIdInt"
                        trackId = 0
                    }
                }.onFailure { it.printStackTrace() }
            }

            QuickListenDataManager.getInstance().setQuickListenFragment(this)

            mMainBackBtn.setOnClickListener(mOnClickSetting)
            mMainSetting.setOnClickListener(mOnClickSetting)

            val notDestroyTabIds = mutableListOf<String>()
            val fragmentHolderList: MutableList<TabCommonAdapter.FragmentHolder> = mutableListOf()
            var initPosition = 0
            val quickListenTabAdapter = QuickListenTabAdapter(childFragmentManager, fragmentHolderList)

            val fontDownloaded = RemoteTypefaceManager.hasDownload(RemoteTypefaceManager.RemoteTypeface.SourceHanSansCNHeavy)

            QuickListenTabsManager.INSTANCE.requestTabs { tabs ->
                mTabs = tabs

                kotlin.run {
                    tabs?.forEachIndexed { index, quickListenTab ->
                        if (tabId.isAvailable() && quickListenTab.id.toString() == tabId) {
                            initPosition = index
                        }
                    }
                }

                tabs?.forEachIndexed { index, quickListenTab ->
                    val bundle = Bundle()

                    if (initPosition == index && forceItems?.isNotEmpty() == true) {
                        QuickListenHomeHelper.handleForceItem(quickListenTab.id, forceItems)
                    bundle.putParcelableArrayList("forceItems", ArrayList(forceItems))
                }

                    if (!useRn || quickListenTab.elementType == ELEMENT_TYPE_NEW_USER || quickListenTab.elementType == ELEMENT_TYPE_RECOMMEND) {
                        notDestroyTabIds.add(quickListenTab.id.toString())
                    }
                    bundle.putInt("childPosition", index)
                    bundle.putLong("tabId", quickListenTab.id)
                    bundle.putString("tabName", quickListenTab.title)
                    bundle.putString("elementType", quickListenTab.elementType)
                    bundle.putBoolean("isFromTab", isFromTab)
                    bundle.putLong(
                        "selectPagePositionByTrackId", if (trackId > 0) trackId else QuickListenDataManager.getInstance().getLastTabPlayTrackId(quickListenTab.id)
                )
                if (openFragmentBeforeIsPlaying == true) {
                    bundle.putBoolean("openFragmentBeforeIsPlaying", true)
                }
                bundle.putBoolean("fontDownloaded", fontDownloaded)

                // 如果是新用户专区必须用native
                val isRnPage = useRn && quickListenTab.elementType != ELEMENT_TYPE_NEW_USER
                val clazz = if (isRnPage) QuickListenItemRnPageFragment::class.java else QuickListenItemPageFragment::class.java

                    fragmentHolderList.add(
                        TabCommonAdapter.FragmentHolder(
                            clazz, quickListenTab.title + if (ConstantsOpenSdk.isDebug && isRnPage) "RN" else "", bundle, "${quickListenTab.id}"
                    )
                )
            }
            quickListenTabAdapter.notifyDataSetChanged()
            mMainTabs.notifyDataSetChanged()

                if (isFromTab) {
                    changeRootTabBarTheme(getCurTab()?.elementType == ELEMENT_TYPE_NEW_USER)
                }

                if (setPageIndexInited) {
                    setPageIndexForInit(initPosition)
                }
            }

            quickListenTabAdapter.setShouldNotDestroyFragmentId(notDestroyTabIds)
            mAdapter = quickListenTabAdapter
            mMainViewPager.disableFillNeighbourTab(true)
            HandlerManager.postOnUIThreadDelay4Kt(1000) {
                try {
                    mMainViewPager.disableFillNeighbourTab(false)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            mMainViewPager.adapter = quickListenTabAdapter
            mMainViewPager.offscreenPageLimit = 1
            mMainViewPager.addOnPageChangeListener(mPageChangeListener)

            mMainTabs.setViewPager(mMainViewPager)
            mMainTabs.setTabTextSizeScaleRate(1.13f)
            mMainTabs.setOnTabClickListener { position ->
                mTabs?.getOrNull(position)?.let {
                    QuickListenTraceUtil.trace69162(it.id, it.title, position + 1, true)
                }
                mLastClickTime = System.currentTimeMillis()
                handleTabClick(position)
            }

            if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
                mTitleBar.layoutParams?.height = (mTitleBar.layoutParams?.height
                ?: 0) + BaseUtil.getStatusBarHeight(context)
                ViewUtil.onlySetViewPaddingOne(mTitleBar, BaseUtil.getStatusBarHeight(context), PADDING_TOP)
            }
            setTabTheme(false)

            setPageIndexInited = true
            setPageIndexForInit(initPosition)

            if (isFromTab && getCurTab()?.elementType == ELEMENT_TYPE_NEW_USER) {
                changeRootTabBarTheme(true)
            }

            addAiChatFragment()

            updateBottomPlayBtnBg()

            QuickListenConfigManager.getInstance().updateConfig()
            QuickListenHomeHelper.initPlayedTrackBehaviors()

            if (isFromTab) {
                mMainBackBtn.visibility = View.GONE
                ToolUtil.getMainActivity()?.addMainTabClickCallback(mTabClick)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            XDCSCollectUtil.statErrorToXDCS("quick_listen_error", Log.getStackTraceString(e))
            finishFragment()
        }
    }

    private var setPageIndexInited = false
    private fun setPageIndexForInit(initPosition: Int) {
        mMainViewPager.setCurrentItem(initPosition, false)
        if (initPosition == 0) {
            mTabs?.getOrNull(0)?.let {
                QuickListenTraceUtil.trace69162(it.id, it.title, 1, false)
            }
        }
        MMKVUtil.getInstance().saveLong(KEY_QUICK_LISTEN_LAST_TAB_ID, mTabs?.getOrNull(initPosition)?.id ?: 0)
    }

    private val mTabClick = IMainTabClickCallback { checkedId ->
        if (checkedId == ToolUtil.getMainActivity()?.quickListenRadioButton?.id) {
            mLastQuickListenTabClickTime = System.currentTimeMillis()
        }
    }

    override fun setViewPager(viewPager: VerticalViewPager?, tabId: Long, jsonArray: JSONArray?) {
        val pageControl = getFragmentAtTabId(tabId)
        if (pageControl != null) {
            pageControl.setViewPager(viewPager, tabId, jsonArray)
        }
    }

    private fun getFragmentAtTabId(tabId: Long): IQuickListenItemPageControl? {
        val adapter = mMainViewPager.adapter as? TabCommonAdapter?
        val count = adapter?.count ?: 0
        if (adapter != null && count > 0) {
            for (i in 0 until count) {
                val control = adapter.getFragmentAtPosition(i) as? IQuickListenItemPageControl
                if (control?.getTabId() == tabId) {
                    return control
                }
            }
        }
        return null
    }

    override fun setViewPager(viewPager: VerticalViewPager?) {
    }

    override fun getRequestId(tabId: Long): Int {
        val adapter = mMainViewPager.adapter as? TabCommonAdapter?
        val count = adapter?.count ?: 0
        if (adapter != null && count > 0) {
            for (i in 0 until count) {
                val control = adapter.getFragmentAtPosition(i) as? IQuickListenItemPageControl
                if (control?.getTabId() == tabId) {
                    return control.getRequestId(tabId)
                }
            }
        }
        return -1
    }

    override fun updateTab(
        needPlay: Boolean, tabId: Long,
        playList: MutableList<out JSONObject>,
        position: Int, willPlayRefId: Long,
    ) {
        getCurrentChildFragment()?.updateDataList(playList, position, tabId, needPlay, willPlayRefId)
    }

    override fun playTrack(tabId: Long, trackId: Long) {
        getCurrentChildFragment()?.playTrack(tabId, trackId)
    }

    override fun sendAiAgentQuery(tabId: Long, text: String?) {
        sendAiAgentQueryToRn(text, tabId)
    }

    override fun saveBehaviorToLocal(trackId: Long, behaveType: String?) {
        QuickListenHomeHelper.saveBehaviorToLocal(trackId, behaveType ?: "")
    }

    override fun sendCloseDetailPop() {
        if (getCurrentChildFragment()?.isRnPage() == true) {
            (getCurrentChildFragment() as? QuickListenItemRnPageFragment)?.getRnFragment()?.let {
                try {
                    Router.getActionRouter<RNActionRouter>(Configure.BUNDLE_RN)!!.functionAction.sendEvent(
                        it.reactContext, "CloseDetailPop", null)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    private val mICallback = object : ICallback {
        override fun playListChange(list: MutableList<JSONObject>, tabId: Long, lastRefId: Long) {
            val curTabId = mAdapter?.getFragmentHolderAtPosition(mMainViewPager.currentItem)?.args
                ?.getLong("tabId", -1) ?: -1L
            if (curTabId == tabId) {
                getCurrentChildFragment()?.insertDataList(list, tabId, lastRefId)
            } else {
                Logger.d(TAG, "playListChange tabId mismatch: current=$curTabId, expected=$tabId")
                if (ConstantsOpenSdk.isDebug) {
                    throw RuntimeException("playListChange tabId mismatch: current=$curTabId, expected=$tabId")
                }
            }
        }
    }

    private var mRnViewInflate = false

    private val rnReceiver = object: BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == BROADCAST) {
                val action = intent.getStringExtra("eventName")
                val map = intent.getSerializableExtra("data") as? HashMap<*, *>
                Logger.d(TAG, "onReceive action = $action, map = $map")
                if (action == "agentModalState") {
                    if (map?.get("isShow") == true) {
                        mRnViewInflate = true
                        getAiContainer()?.setFullScreen(true)
                    } else {
                        mRnViewInflate = false
                        getAiContainer()?.setFullScreen(false)
                    }
                } else if (action == "agentFeedback") {
                    kotlin.runCatching {
                        if (map?.get("op") == "shortContentPositiveFeedback") {
                            val dataMap = map?.get("data") as? Map<*, *>?
                            val agentRecommendId = dataMap?.get("agentRecommendId") as? String?
//                            val trackId = (((dataMap?.get("items") as? List<*>?)
//                                ?.getOrNull(0) as? Map<*, *>?)
//                                ?.get("trackId") as? Double?)?.toLong() ?: -1L
                            val curChildFragment = getCurrentChildFragment()
                            val refId = if (curChildFragment?.isRnPage() == true) {
                                getCurrentChildFragment()?.getCurModel()?.optLong("refId", -1L) ?: -1L
                            } else {
                                getCurrentChildFragment()?.getCurrQuickListenModel()?.refId ?: -1L
                            }
                            if (!TextUtils.isEmpty(agentRecommendId) && refId > 0) {
                                feedBackPositiveXiaoya(refId, agentRecommendId ?: "")
                            } else {
                                QuickListenDataManager.getInstance()
                                    .logToFile(TAG, "agentFeedback error: agentRecommendId=$agentRecommendId, refId=$refId")
                            }
                        } else if (map?.get("op") == "shortContentNegativeFeedback") {
                            val dataMap = map?.get("data") as? Map<*, *>?
                            val trackId = (dataMap?.get("trackId") as? Double?)?.toLong() ?: -1L
                            if (trackId > 0) {
                                feedBackXiaoya(trackId)
                            } else {
                                QuickListenDataManager.getInstance()
                                    .logToFile(TAG, "agentFeedback error: trackId=$trackId")
                            }
                        }
                    }.onFailure { it.printStackTrace() }
                }
            }
        }
    }

    private fun getAiContainer(): IAIContainer? {
        if (isFromTab || QuickListenTabAbManager.isNewUserForQuickListen) {
            return mQuickListenXiaoyaLay
        } else {
            return mAiChatContainer
        }
    }

    private fun addAiChatFragment() {
        if (ToolUtil.getDebugSystemProperty("debug.quick_listen_closeai", "-1") == "1") {
            return
        }

        getAiContainer()?.setVisibility(View.INVISIBLE)
        getAiContainer()?.setEnable(false)

        val reactView = ReactNativeAdapter.newRNEmbeddedView(mContext)
        mRnView = reactView

        reactView?.let {
            it.setLoadBundleListener(object : IRNFunctionRouter.ILoadBundleListener {
                override fun onLoadBundleSucceed() {
                    Logger.d(TAG, "onLoadBundleSucceed isEnable=true")
                    getAiContainer()?.setVisibility(View.VISIBLE)
                    getAiContainer()?.setEnable(true)
                }

                override fun onLoadBundleError(bundleName: String) {
                    Logger.d(TAG, "onLoadBundleError $bundleName isEnable=false")
                    getAiContainer()?.setVisibility(View.INVISIBLE)
                    getAiContainer()?.setEnable(false)
                }
            })
            it.setDataReceiver(object : IRNFunctionRouter.IDataListener {
                override fun onDataReceived(data: HashMap<String, Any>?) {
                    Logger.d(TAG, "onReceiveData $data")
                    // 处理接收到的数据
                }

                override fun requireData(): Bundle? = null
            })
            val lp = ViewGroup.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            getAiContainer()?.addView(it.asView(), lp)
            try {
                val bundle = Bundle()
                bundle.putString("bundle", QuickListenTabAbManager.EXPAND_QUICK_LISTEN_BUNDLE_NAME)
                bundle.putBoolean("isInFloatingDialog", false)
                bundle.putString("transparent", "1")
                bundle.putString("initialPage", "AiAgentChat")
                bundle.putBoolean("canSlide", false)
                bundle.putString("sceneFlashShowMode", if (QuickListenTabAbManager.isNewUserForQuickListen) "下底导" else "普通")

                val ipValue = ToolUtil.getDebugSystemProperty("debug.rn_quick_listen_debugip", "")
                if (ipValue.isAvailable() && ipValue.contains(":")) {
                    it.debugLoadBundle(ipValue, QuickListenTabAbManager.EXPAND_QUICK_LISTEN_BUNDLE_NAME, bundle)
                    getAiContainer()?.setVisibility(View.VISIBLE)
                    getAiContainer()?.setEnable(true)
                } else {
                    it.loadBundle(activity, QuickListenTabAbManager.EXPAND_QUICK_LISTEN_BUNDLE_NAME, bundle)
                }

            } catch (throwable: Throwable) {
                throwable.printStackTrace()
            }

            getAiContainer()?.addTouchView()
        }
    }

    private val mPageChangeListener = object : ViewPager.OnPageChangeListener {
        private var mLastScrolledPosition = -1
        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            var willPosition = 0
            if (positionOffset > 0.5) {
                willPosition = position + 1
            } else {
                willPosition = position
            }
            Logger.log("QuickListenHomePageFragment : onPageScrolled  $position   $positionOffset   willPosition=${willPosition}")
            if (mLastScrolledPosition != willPosition) {
                mLastScrolledPosition = willPosition

                getChildFragment(mLastScrolledPosition)?.let {
                    if (it.isRnPage()) {
                        setTabTheme(QuickListenHomeHelper.isXimaTen(it.getCurModel()))
                        return
                    }

                    if (mTabs?.getOrNull(mLastScrolledPosition)?.elementType == ELEMENT_TYPE_NEW_USER) {
                        setTabTheme(true)
                        return
                    }

                    val quickListenListData = it.getQuickListenListData()
                    if (quickListenListData.isNullOrEmpty()) {
                        setTabTheme(false)
                        return
                    }
                    val quickListenModel = quickListenListData.getOrNull(it.getCurrentItem())
                    if (quickListenModel == null) {
                        setTabTheme(false)
                        return
                    }
                    setTabTheme(quickListenModel.isXimaTen())
                }
            }
        }

        override fun onPageSelected(position: Int) {
            getCurrentChildFragment()?.getTabId()?.let {
                Logger.log("QuickListenHomePageFragment : onPageSelected  tabId=$it")
                MMKVUtil.getInstance().saveLong(KEY_QUICK_LISTEN_LAST_TAB_ID, it)
            }

            if (notifyPageChange) {
                val curChildFragment = getCurrentChildFragment()
                if (curChildFragment?.isRnPage() == true) {
                    if (curChildFragment?.isPageInited() == true) {
                        onPageSelectedChange(position, getCurrentChildFragment()?.getCurrentItem() ?: 0, true)
                    }
                } else {
                    onPageSelectedChange(
                        position,
                        getCurrentChildFragment()?.getCurrentItem() ?: 0,
                        true
                    )
                }

            }
            if (System.currentTimeMillis() - mLastClickTime > 1000) {
                mTabs?.getOrNull(position)?.let {
                    QuickListenTraceUtil.trace69162(it.id, it.title, position + 1, false)
                }
            }

            updateBottomPlayBtnBg()

            if (isFromTab) {
                changeRootTabBarTheme(getCurTab()?.elementType == ELEMENT_TYPE_NEW_USER)
            }

            // 如果是种草专区tab, 直接取消
            if (getCurTab()?.elementType == ELEMENT_TYPE_NEW_USER) {
                PlanTerminateManagerForQuickListen.forceCancel()
            }
        }

        override fun onPageScrollStateChanged(state: Int) {
        }
    }

    private val mOnClickSetting: View.OnClickListener = View.OnClickListener { v ->
        when (v?.id) {
            R.id.main_back_btn -> finishFragment()
            R.id.main_setting -> {
                val params = hashMapOf<String, Any>("supportAIRadio" to "1")
                params["isVideoTab"] = getCurTab()?.elementType == ELEMENT_TYPE_NEW_USER
                mRnView?.sendMessageToJS("sendMoreSettingShow", params)
            }
            R.id.main_play_btn -> {
                if (getCurrentChildFragment()?.getVideoControl(getCurrentChildFragment()?.getCurrentItem() ?: 0) != null) {
                    getCurrentChildFragment()?.getVideoControl(getCurrentChildFragment()?.getCurrentItem() ?: 0)?.let {
                        if (it.isPlaying) {
                            it.pause()
                        } else {
                            it.start()
                        }
                    }
                } else {
                    PlayTools.playOrPause(ToolUtil.getCtx(),
                        PauseReason.Business.QUICK_LISTEN_USER_PAUSE)
                }
            }
        }
    }

    override fun loadData() {
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_quick_listen_homepage_fragment
    }

    val mPageControl = object : IQuickListenHomePageControl {
        var mLastPullDistance: Float = 0f
        var isBlue: Boolean = false
        override fun onPagePull(pullDistance: Float) {
            if (mLastPullDistance == pullDistance) {
                return
            }
            mLastPullDistance = pullDistance

            if (pullDistance == 0f) {
                resetRefreshState()
                return
            }

            Logger.log("QuickListenHomePageFragment : onPagePull  =$pullDistance")
            mMainTabs.alpha = (1 - pullDistance)
            mMainTabs.translationY = mMainTabs.height * pullDistance

            mRefreshTips.translationY = -mRefreshTips.height * (1 - pullDistance)
            mRefreshTips.alpha = pullDistance
            mRefreshTips.visibility = View.VISIBLE
        }

        override fun onRefresh(refreshView: CustomSwipeRefreshLayout) {
            handleRefresh()
        }

        override fun onRefreshComplete(childPosition: Int) {
            setTabRefreshState(childPosition, false)
        }

        override fun changeTabTheme(isBlue: Boolean) {
            setTabTheme(isBlue)
        }

        override fun addPlayerStatusListener(listen: IXmPlayerStatusListener) {
            <EMAIL>(listen)
        }

        override fun removePlayerStatusListener(listen: IXmPlayerStatusListener) {
            <EMAIL>(listen)
        }

        override fun onChildPageSelected(childPosition: Int, position: Int) {
            onPageSelectedChange(childPosition, position, false)
        }

        override fun childPosition(): Int {
            return mMainViewPager.currentItem
        }

        override fun curTabId(): Long {
            return getCurTab()?.id ?: -1L
        }

        override fun sendAiAgentQuery(sug: String?) {
            sendAiAgentQueryToRn(sug, curTabId())
        }

        override fun sendCardListShow(cardType: String, refId: String) {
            try {
                val map = mutableMapOf<String, Any>()
                map["cardType"] = cardType
                map["refId"] = refId
                if (cardType == QuickElementType.VIDEO.elementType) {
                    getCurrentChildFragment()?.getVideoControl(getCurrentChildFragment()?.getCurrentItem() ?: 0)?.let {
                        map["isVideoPlaying"] = it.isPlaying
                    }
                }
                map["cardIndex"] = getCurrentChildFragment()?.getCurrentItem() ?: 0
                map["tabId"] = getCurrentChildFragment()?.getTabId() ?: 0
                map["tabName"] = getCurTab()?.title ?: ""
                mRnView?.sendMessageToJS("sendCardListShow", map)
            } catch (e: Exception) {
            }
        }

        override fun sendMoreControlShow(dataString: String) {
            try {
                val map = mutableMapOf<String, Any>()
                map["dataString"] = dataString
                mRnView?.sendMessageToJS("sendMoreControlShow", map)
            } catch (e: Exception) {
            }
        }

        override fun getFrom(): String? {
            return mFrom
        }

        override fun saveBehaviorToLocal(trackId: Long, behaveType: String) {
            val elementType = mTabs?.getOrNull(mMainViewPager.currentItem)?.elementType
            if (elementType != ELEMENT_TYPE_RECOMMEND) {
                return
            }
            QuickListenHomeHelper.saveBehaviorToLocal(trackId, behaveType)
        }

        override fun negativeXimaTenData(trackId: Long, positionNew: Int, itemPosition: Int) {
            val tabId = mAdapter?.getFragmentHolderAtPosition(mMainViewPager.currentItem)?.args
                ?.getLong("tabId", -1) ?: -1L

            val cacheData = QuickListenDataManager.getInstance().cache(tabId)
            var itemData: JSONObject? = null
            cacheData?.forEach {
                if ("XimaTen" == it.optString("elementType")) {
                    val jsonArr = it.optJSONArray("subElements")
                    if (jsonArr != null) {
                        val size = jsonArr.length()
                        for (i in 0 until size) {
                            val jsonItem = jsonArr.optJSONObject(i)
                            if (jsonItem != null && jsonItem.optLong("refId") == trackId) {
                                itemData = jsonItem
                                itemData?.put("elementType", "XimaTen")
                            }
                        }
                    }
                }
            }
            if (itemData != null) {
                val tabName = getCurTab()?.title
                val itemFragment = getCurrentChildFragment()
                val localExt = JSONObject().apply {
                    put("tabName", tabName)
                    put("positionNew", positionNew)
                    put("itemPosition", itemPosition)
                    put("showFrom", mFrom ?: "")
                }
                itemData?.put("localExt", localExt)
                MyAsyncTask.execute {
                    mRnView?.sendMessageToJS(
                        "sendNegativeFeedback",
                        hashMapOf<String, Any?>("dataString" to itemData.toString())
                    )
                }
            }
        }

        override fun negativeSoundCard(trackId: Long, positionNew: Int, itemPosition: Int) {
            val tabId = mAdapter?.getFragmentHolderAtPosition(mMainViewPager.currentItem)?.args
                ?.getLong("tabId", -1) ?: -1L

            val cacheData = QuickListenDataManager.getInstance().cache(tabId)
            val itemData = cacheData?.find { it.optLong("refId", -1L) == trackId }
            if (itemData != null) {
                val tabName = getCurTab()?.title
                val itemFragment = getCurrentChildFragment()
                val localExt = JSONObject().apply {
                    put("tabName", tabName)
                    put("positionNew", positionNew)
                    put("itemPosition", itemPosition)
                    put("showFrom", mFrom ?: "")
                }
                itemData.put("localExt", localExt)
            }
            MyAsyncTask.execute {
                mRnView?.sendMessageToJS("sendNegativeFeedback", hashMapOf<String, Any?>("dataString" to itemData.toString()))
            }

            QuickListenDataManager.getInstance()
                .negativeXimaTenDataAndPlay(tabId, trackId, object : IPlayCallback {
                    override fun playQuickListenList(
                        needPlay: Boolean,
                        playList: List<JSONObject>,
                        position: Int,
                        willPlayRefId: Long,
                    ) {
                        updateTabData(playList, position, tabId, needPlay, willPlayRefId)
                    }
                })

//            batchDislike(trackId)
        }

        override fun showRefreshTips() {
            <EMAIL>()
        }

        override fun setParentCurrentItem(position: Int) {
            mMainViewPager.currentItem = position
        }

        override fun forcePlayTrackId(trackId: Long) {
            getCurrentChildFragment()?.let {
                if (!it.isRnPage()) {
                    it.getCurrQuickListenModel()?.let { curModel ->
                        playWithTrackIdSceneId(curModel, false, it.getQuickListenListData(), trackId)
                    }
                }
            }
        }

        override fun getVideoPlayStateListener(): IXmVideoPlayStatusListener {
            return mVideoPlayStatusListener
        }

        override fun skipUpdatePlayStatus() {
            mSkipUpdatePlayStatus = System.currentTimeMillis()
        }
    }

    override fun updateTabListData(list: List<JSONObject>?, index: Int, tabId: Long, needPlay: Boolean, willPlayRefId: Long) {
        if (!canUpdateUi()) {
            return
        }
        updateTabData(list, index, tabId, needPlay, willPlayRefId)
    }

    private fun updateTabData(list: List<JSONObject>?, index: Int, tabId: Long, needPlay: Boolean, willPlayRefId: Long) {
        getCurrentChildFragment()?.updateDataList(list, index, tabId, needPlay, willPlayRefId)
    }

    private fun feedBackPositiveXiaoya(refId: Long, agentRecommendId: String) {
        val tabId = mAdapter?.getFragmentHolderAtPosition(mMainViewPager.currentItem)?.args
            ?.getLong("tabId", -1) ?: -1L

        CommonRequestM.feedBackPositiveXiaoya(
            agentRecommendId,
            object : IDataCallBack<List<JSONObject>> {
                override fun onSuccess(list: List<JSONObject>?) {
                    QuickListenDataManager.getInstance().logToFile(TAG, "feedBackPositiveXiaoya success list=${list?.size}")
                    if (list?.isNotEmpty() == true) {
                        QuickListenDataManager.getInstance()
                            .insertListAndPlay(tabId, refId, list, object : IPlayCallback {
                                override fun playQuickListenList(
                                    needPlay: Boolean,
                                    playList: List<JSONObject>,
                                    position: Int,
                                    willPlayRefId: Long,
                                ) {
                                    updateTabData(playList, position, tabId, needPlay, willPlayRefId)
                                }
                            })
                    }
                }

                override fun onError(code: Int, message: String?) {
                    QuickListenDataManager.getInstance().logToFile(TAG, "feedBackPositiveXiaoya error code=$code, message=$message")
                }
            })
    }

    private fun feedBackXiaoya(refId: Long) {
        val tabId = mAdapter?.getFragmentHolderAtPosition(mMainViewPager.currentItem)?.args
            ?.getLong("tabId", -1) ?: -1L

        QuickListenDataManager.getInstance().negativeXimaTenDataAndPlayXiaoya(tabId, refId,
            object : IPlayCallback {
                override fun playQuickListenList(
                    needPlay: Boolean,
                    playList: List<JSONObject>,
                    position: Int,
                    willPlayRefId: Long,
                ) {
                    updateTabData(playList, position, tabId, needPlay, willPlayRefId)
                }
            })
    }

    private fun resetRefreshState() {
        mMainTabs.alpha = 1.0f
        mMainTabs.translationY = 0f

        mRefreshTips.translationY = 0f
        mRefreshTips.alpha = 1f
        mRefreshTips.visibility = View.GONE
    }

    override fun isShowPlayButton(): Boolean = false

    private fun onPageSelectedChange(tabPosition: Int, position: Int, tabPositionChange: Boolean) {
        if (tabPosition != mMainViewPager.currentItem) {
            return
        }

        val curFragment = getCurrentChildFragment()
        if (curFragment?.isRnPage() == true) {
            playOnPageSelected(curFragment, position)
            return
        }

        getCurrentChildFragment()?.let {
            val quickListenListData = it.getQuickListenListData()
            if (quickListenListData.isNullOrEmpty()) {
                return
            }
            val quickListenModel = quickListenListData.getOrNull(position)
            if (quickListenModel == null) {
                Logger.log("QuickListenHomePageFragment : onPageSelectedChange quickListenModel is null")
                return
            }

            setTabTheme(quickListenModel.isXimaTen())

            playWithTrackIdSceneId(quickListenModel, tabPositionChange, quickListenListData)
        }
    }

    private fun playOnPageSelected(pageControl: IQuickListenItemPageControl, cardPosition: Int) {
        val tabId = pageControl.getTabId()
        val listenData = QuickListenDataManager.getInstance().cache(tabId)
        val card = listenData?.getOrNull(cardPosition)
        setTabTheme(QuickListenHomeHelper.isXimaTen(card))
        val willPlayTrackId = QuickListenDataManager.getInstance().getPlayIndex(listenData, cardPosition)
        Logger.d(TAG, "playOnPageSelected tabId=$tabId, willPlayTrackId=$willPlayTrackId")

        val xmPlayerManager = XmPlayerManager.getInstance(ToolUtil.getCtx())
        if (xmPlayerManager.isQuickListen && xmPlayerManager.quickListenTabId == tabId) {
            if (PlayTools.getCurTrackId(ToolUtil.getCtx()) == willPlayTrackId) {
                xmPlayerManager.play()
                Logger.d(TAG, "playOnPageSelected-1")
                return
            } else {
                val index = xmPlayerManager.getTrackIndex(willPlayTrackId)
                if (index >= 0) {
                    xmPlayerManager.play(index)
                    Logger.d(TAG, "playOnPageSelected-2")
                    return
                }
            }
        }
        val tracks = PlayTools.convertToCommonTrackList(listenData, tabId)
        val playIndex = tracks?.tracks?.indexOfFirst { it.dataId == willPlayTrackId } ?: 0
        QuickListenDataManager.getInstance().logToFile(TAG, "playOnPageSelected willPlayRefId=$willPlayTrackId, playIndex=$playIndex")
        PlayTools.playQuickListenListNew(tracks, playIndex, false)

        postOnUiThread {
            pageControl.setItemPosition(cardPosition)
        }
    }

    private fun playWithTrackIdSceneId(
        quickListenModel: QuickListenModel,
        tabPositionChange: Boolean,
        quickListenListData: List<QuickListenModel>,
        forcePlayTrackId: Long = 0,
    ) {
        val willPlayTrackId = if (forcePlayTrackId > 0) {
            forcePlayTrackId
        } else {
            QuickListenManager.findLastCollectTrackIdFromMemory(quickListenListData, quickListenModel)
        }

        val indexOfTrackId = XmPlayerManager.getInstance(ToolUtil.getCtx()).indexOfTrackId(willPlayTrackId)

        val forceUpdatePlayList = tabPositionChange || indexOfTrackId == -1
                || !XmPlayerManager.getInstance(ToolUtil.getCtx()).isQuickListen

        if (quickListenModel.isVideo()) {
            if (forceUpdatePlayList) {
                forceUpdatePlayList(quickListenListData, willPlayTrackId, false)
            } else {
                XmPlayerManager.getInstance(ToolUtil.getCtx()).setPlayIndex(indexOfTrackId)
            }

            val videoControl = getCurrentChildFragment()?.getVideoControl(
                getCurrentChildFragment()?.getCurrentItem() ?: 0
            )
            if (videoControl == null) {
                getCurrentChildFragment()?.notifyCurShowChange()
            } else {
                Logger.log("QuickListenHomePageFragment : playWithTrackIdSceneId  willPlayTrackId == ${willPlayTrackId}")
                if ((videoControl.view.parent as? View)?.getTag(R.id.main_video_info) != willPlayTrackId) {
                    getCurrentChildFragment()?.notifyCurShowChange()
                } else if ((videoControl.view.getTag(R.id.main_video_play_url_set) as? String).isAvailable()) {
                    videoControl.start()
                } else {
                    getCurrentChildFragment()?.notifyCurShowChange()
                }
            }
            Logger.log("QuickListenHomePageFragment : playWithTrackIdSceneId   $indexOfTrackId  $willPlayTrackId")
            
            return
        }

        if (forceUpdatePlayList) {
            forceUpdatePlayList(quickListenListData, willPlayTrackId, true)
        } else {
            if (indexOfTrackId == XmPlayerManager.getInstance(ToolUtil.getCtx()).currentIndex
                && XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying) {
                return
            }
            XmPlayerManager.getInstance(ToolUtil.getCtx()).play(indexOfTrackId)
            Logger.d(TAG, "playWithTrackIdSceneId: play with index $indexOfTrackId  " +
                    "willPlayTrackId=${willPlayTrackId}")
        }
    }

    private fun forceUpdatePlayList(quickListenListData: List<QuickListenModel>, willPlayTrackId: Long, willPlay: Boolean) {
        val tracks = QuickListenHomeHelper.convertQuickListenModelToTrack(quickListenListData)
        val playIndex = QuickListenHomeHelper.trackIndexInList(tracks, willPlayTrackId)
        if (playIndex >= 0) {
            val playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
            if (tracks != null) {
                for (track in tracks) {
                    // 快听的特殊业务标识
                    track.channelGroupId = 9999
                }
            }
            val curPosition = mMainViewPager.currentItem
            val tabId = mAdapter?.getFragmentHolderAtPosition(curPosition)?.args?.getLong("tabId", -1) ?: -1L
            val commonTrackList = QuickListenPlayUtil.buildPlayCommonTrackList(tracks, tabId)
            if (willPlay) {
                playerManager.playList(commonTrackList, playIndex)
            } else {
                playerManager.setPlayList(commonTrackList, playIndex)
            }
            Logger.d(TAG, "playWithTrackIdSceneId: playList with index $playIndex, tabId=$tabId, willPlayTrackId=${willPlayTrackId}")
        }
    }

    private fun setTabTheme(isBlue: Boolean) {
        var isBlueReal = isBlue
        if (getCurTab()?.elementType == ELEMENT_TYPE_NEW_USER) {
            isBlueReal = true
        }

        if (mMainTabs.getTag(R.id.banner_data_key) == isBlueReal) {
            return
        }

        mMainTabs.setTag(R.id.banner_data_key, isBlueReal)

        if (BaseFragmentActivity.sIsDarkMode || isBlueReal) {
            mMainTabs.apply {
                setActivateTextColor(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_ffffff))
                setDeactivateTextColor(ContextCompat.getColor(ToolUtil.getCtx(), R.color.main_color_8cffffff))
                indicatorColor = ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_ffffff)
            }
            mMainBackBtn.imageTintList = ColorStateList.valueOf(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_ffffff))
            mMainSetting.imageTintList = ColorStateList.valueOf(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_ffffff))
            StatusBarManager.setStatusBarColor(window, false)
            updateLottieTabTheme(true)
            mRefreshTips.setTextColor(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_ffffff))
        } else {
            mMainTabs.apply {
                setActivateTextColor(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_2c2c3c))
                setDeactivateTextColor(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_8c2c2c3c))
                indicatorColor = ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_2c2c3c)
            }

            mMainBackBtn.imageTintList = ColorStateList.valueOf(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_2c2c3c))
            mMainSetting.imageTintList = ColorStateList.valueOf(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_2c2c3c))
            StatusBarManager.setStatusBarColor(window, true)
            updateLottieTabTheme(false)
            mRefreshTips.setTextColor(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_2c2c3c))
        }
    }

    private fun updateLottieTabTheme(isBlue: Boolean) {
        for (i in 0 until mMainTabs.getTabChildCount()) {
            (mMainTabs.getChildView(i) as? QuickListenTabWidget)?.let {
                for (i in 0 until it.childCount) {
                    val child = it.getChildAt(i)
                    if (child is XmLottieAnimationView) {
                        child.addValueCallback(
                            KeyPath("**"),
                            LottieProperty.COLOR_FILTER
                        ) {
                            PorterDuffColorFilter(
                                Color.parseColor(if (isBlue) "#ffffff" else "#802C2C3C"),
                                PorterDuff.Mode.SRC_IN
                            )
                        }
                    }
                }
            }
        }
    }

    private fun handleTabClick(position: Int) {
        if (mMainViewPager.currentItem == position) {
            getChildRefreshView()?.let {
                if (it.isRefreshing) {
                    return
                }
                it.isRefreshing = true
                handleRefresh()
            }
        }
    }

    private fun handleRefresh() {
        mDownArrow.visibility = View.GONE
        mTitleRefreshHint.visibility = View.GONE
        resetRefreshState()
        setTabRefreshState(mMainViewPager.currentItem, true)
    }

    private fun setTabRefreshState(position: Int, isRefreshing: Boolean) {
        try {
            (mMainTabs.getChildView(position) as? QuickListenTabWidget)?.let {
                for (i in 0 until it.childCount) {
                    val child = it.getChildAt(i)
                    if (child is XmLottieAnimationView) {
                       child.visibility = if (isRefreshing) View.VISIBLE else View.GONE
                       if (isRefreshing) {
                        child.playAnimation()
                       } else {
                        child.cancelAnimation()
                       }
                    } else {
                        child.visibility = if (isRefreshing) View.INVISIBLE else View.VISIBLE
                    }
                }
            }
            mMainTabs.indicatorHeight = if (isRefreshing) 0 else BaseUtil.dp2px(ToolUtil.getCtx(), 3f)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getCurrentChildFragment(): IQuickListenItemPageControl? {
        return (mMainViewPager.adapter as? TabCommonAdapter)?.getFragmentAtPosition(mMainViewPager.currentItem)
                as? IQuickListenItemPageControl
    }

    private fun getChildFragment(position: Int): IQuickListenItemPageControl? {
        return (mMainViewPager.adapter as? TabCommonAdapter)?.getFragmentAtPosition(position)
                as? IQuickListenItemPageControl
    }

    private fun getChildRefreshView(): CustomSwipeRefreshLayout? {
        val quickListenItemPageFragment = getCurrentChildFragment()
        return quickListenItemPageFragment?.getRefreshView()
    }

    override fun getCurTabId() = getCurTab()?.id ?: -1L

    private fun getCurTab() = mTabs?.getOrNull(mMainViewPager.currentItem)

    private fun getTabIndexByTabId(tabId: Long): Int {
        return mTabs?.indexOfFirst { tabId == it.id } ?: -1
    }

    override fun onPause() {
        super.onPause()
        QuickListenManager.setQuickPage(null)
        XmPlayerManager.getInstance(ToolUtil.getCtx()).removePlayerStatusListener(mPlayStatusListener)
        LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(rnReceiver)

        mRnView?.onActivityPause(activity)
        removeAgentAudioBroadCast()

        if (EasyConfigure.getBoolean("quick_listen_hide_bottom_after_pause", true)) {
            if (isFromTab || QuickListenTabAbManager.isNewUserForQuickListen) {
                getAiContainer()?.setVisibility(View.GONE)
            }
        }
    }

    override fun onMyResume() {
        super.onMyResume()
        QuickListenManager.setQuickPage(mQuickListenPage)
        QuickListenDataManager.getInstance().setCallback(mICallback)
        QuickListenDataManager.getInstance().setQuickListenFragment(this)
        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(rnReceiver, IntentFilter(BROADCAST))

        XmPlayerManager.getInstance(ToolUtil.getCtx()).addPlayerStatusListener(mPlayStatusListener)

        if (isResumed && isFromTab) {
            val initTab = QuickListenDataManager.getInstance().getInitTab(true)
            if (initTab > 0 && getCurTab()?.id != initTab) {
                val tabIndexByTabId = getTabIndexByTabId(initTab)
                if (tabIndexByTabId >= 0) {
                    notifyPageChange = false
                    mMainViewPager.currentItem = tabIndexByTabId
                    notifyPageChange = true
                }
            }
        }

        if (getCurrentChildFragment()?.getQuickListenListData()?.isNotEmpty() == true
            && XmPlayerManager.getInstance(ToolUtil.getCtx()).isQuickListen) {

            val trackId = XmPlayerManager.getInstance(ToolUtil.getCtx()).currSound.dataId

            val position = QuickListenManager.getQuickListenListIndexByTrackId(trackId, getCurrentChildFragment()?.getQuickListenListData())
            if (position >= 0 && getCurrentChildFragment()?.getCurrentItem() != position) {
                getCurrentChildFragment()?.setCurrentItem(position)
            }
        }
        getCurrentChildFragment()?.notifyCurShowChange()
        HandlerManager.postOnUIThread { traceOnTabShow() }

        mRnView?.onActivityResume(activity)
        listenOpenAgentAudioBroadCast()
        if (mIsGotoAgent) {
            if (mPausedByAgentAudio) {
                Logger.log("QuickListenHomePageFragment : mPausedByAgentAudio=${mPausedByAgentAudio}")
                onPageSelectedChange(
                    mMainViewPager.currentItem, getCurrentChildFragment()?.getCurrentItem() ?: 0, false
                )
            }
            mIsGotoAgent = false
            mPausedByAgentAudio = false
        } else {
            if (isFromTab) {
                var gotoHome = false
                if (isResumed) {
                    if (!XmPlayerManager.getInstance(ToolUtil.getCtx()).isQuickListen) {
                        if (System.currentTimeMillis() - QuickListenManager.mLastClickQuickListenTabTime < 500) {
                            getCurrentChildFragment()?.let {
                                if (!it.isRnPage()) {
                                    it.getCurrQuickListenModel()?.let { curModel ->
                                        playWithTrackIdSceneId(curModel, false, it.getQuickListenListData(), 0)
                                    }
                                } else {
                                    playOnPageSelected(it, it.getCurrentItem())
                                }
                            }
                        } else {
                            gotoHome = true
                            HandlerManager.postOnUIThreadDelay4Kt(300) {
                                ToolUtil.getMainActivity()?.goHome()
                            }
                        }
                    } else {
                        if (abs(System.currentTimeMillis() - mLastQuickListenTabClickTime) < 600
                            && !XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying) {
                            XmPlayerManager.getInstance(ToolUtil.getCtx()).play()
                        }
                    }

                }

                if (!gotoHome) {
                    changeRootTabBarTheme(getCurTab()?.elementType == ELEMENT_TYPE_NEW_USER)
                }
            }
        }

        isResumed = true
    }

    override fun onDestroyView() {
        super.onDestroyView()

        mRnView?.onActivityDestroy(activity)
    }

    private val mPlayStatusListener = object : IXmPlayerStatusListener {
        override fun onPlayStart() {
            mPlayStatusListeners.forEach {
                it.onPlayStart()
            }

            getCurrentChildFragment()?.notifyAdjacencyItemChange()
            updatePlayStatus(true)
            updateBehavior(1)

            if (getCurrentChildFragment()?.getCurrQuickListenModel()?.isVideo() == true && XmPlayerManager.getInstance(ToolUtil.getCtx()).isQuickListen) {
                if (getCurrentChildFragment()?.getVideoControl(getCurrentChildFragment()?.getCurrentItem() ?: 0) != null) {
                    getCurrentChildFragment()?.getVideoControl(getCurrentChildFragment()?.getCurrentItem() ?: 0)?.let {
                        if (!it.isPlaying) {
                            Logger.log("QuickListenHomePageFragment : 兜底策略命中了")
                            it.start()
                        }
                    }
                }
            }
        }

        override fun onPlayPause() {
            mPlayStatusListeners.forEach {
                it.onPlayPause()
            }

            getCurrentChildFragment()?.notifyAdjacencyItemChange()
            updatePlayStatus(true)
            updateBehavior(2)
        }

        override fun onPlayStop() {
            mPlayStatusListeners.forEach {
                it.onPlayStop()
            }

            getCurrentChildFragment()?.notifyAdjacencyItemChange()
            updatePlayStatus(false)
            updateBehavior(3)
        }

        override fun onSoundPlayComplete() {
            mPlayStatusListeners.forEach {
                it.onSoundPlayComplete()
            }
            updatePlayStatus(false)
            getCurrentChildFragment()?.notifyAdjacencyItemChange()
        }

        override fun onSoundPrepared() {
            mPlayStatusListeners.forEach {
                it.onSoundPrepared()
            }
        }

        override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
            mPlayStatusListeners.forEach {
                it.onSoundSwitch(lastModel, curModel)
            }
            updatePlayStatus(false)
            handleOnSoundSwitch(curModel)
        }

        override fun onBufferingStart() {
            mPlayStatusListeners.forEach {
                it.onBufferingStart()
            }
        }

        override fun onBufferingStop() {
            mPlayStatusListeners.forEach {
                it.onBufferingStop()
            }
        }

        override fun onBufferProgress(percent: Int) {
            mPlayStatusListeners.forEach {
                it.onBufferProgress(percent)
            }
        }

        override fun onPlayProgress(currPos: Int, duration: Int) {
            mPlayStatusListeners.forEach {
                it.onPlayProgress(currPos, duration)
            }
        }

        override fun onError(exception: XmPlayerException?): Boolean {
            mPlayStatusListeners.forEach {
                it.onError(exception)
            }
            return false
        }
    }

    private var mRequestSug : Runnable? = null

    private fun createRequestSug(model: QuickListenModel): Runnable {
        return Runnable {
            MainStableRequest.getQuickListenSug(model.refId ?: 0, object : IDataCallBack<String> {
                override fun onSuccess(data: String?) {
                    if (data.isAvailable()) {
                        var processedData = data

                        if (processedData != null && processedData.length > 2) {
                            while (processedData?.endsWith('?') == true || processedData?.endsWith('？') == true) {
                                processedData = processedData.slice(0 .. processedData.length - 2)
                            }
                            model.sug = processedData
                            getCurrentChildFragment()?.notifyCurShowChange()
                        }
                    }
                }

                override fun onError(code: Int, message: String?) {
                    Logger.log("QuickListenHomePageFragment : onError 请求的声音和页面的声音一样")
                }
            })
        }
    }

    private fun addPlayStatusListener(listener: IXmPlayerStatusListener) {
        mPlayStatusListeners.add(listener)
    }

    private fun removePlayStatusListener(listener: IXmPlayerStatusListener) {
        mPlayStatusListeners.remove(listener)
    }

    private fun handleOnSoundSwitch(curModel: PlayableModel?) {
        if (getCurrentChildFragment()?.isRnPage() == false) {
            val index = QuickListenManager.getQuickListenListIndexByTrackId(curModel?.dataId ?: 0L,
                getCurrentChildFragment()?.getQuickListenListData())


            // 说明是切换通知栏或者自动播放到下一曲了
            if (getCurrentChildFragment()?.getCurrentItem() != index && index >= 0) {
                getCurrentChildFragment()?.setCurrentItem(index)

                getCurrentChildFragment()?.getCurrQuickListenModel()?.let {
                    getCurrentChildFragment()?.notifyCurShowChange()
                }

                if (getCurrentChildFragment()?.getCurrQuickListenModel()?.isVideo() == true) {
                    getCurrentChildFragment()?.getVideoControl(
                        getCurrentChildFragment()?.getCurrentItem() ?: 0
                    )?.let {
                        if ((it.view.getTag(R.id.main_video_play_url_set) as? String).isAvailable() && !it.isPlaying) {
                            it.start()
                        }
                    }
                }
                return
            }

            getCurrentChildFragment()?.getCurrQuickListenModel()?.let {
                // 如果是10条和合集需要更新当前页面的数据
                if (it.isXimaTen() || it.isTrackCollect() || it.isVideoCollect()) {
                    getCurrentChildFragment()?.notifyCurShowChange()
                }
            }

                handleSug()

            QuickListenTraceUtil.trace68055(
                curModel?.dataId?: 0,
                getCurrentChildFragment()?.getQuickListenListData() ?: emptyList(),
                getCurrentChildFragment()?.getCurrentItem() ?: 0,
                getCurrentChildFragment()?.getTabId() ?: 0,
                mFrom ?: "",
                getCurrentChildFragment()?.getVideoControl(getCurrentChildFragment()?.getCurrentItem() ?: 0)?.currentPosition
            )
        }
    }

    private fun handleSug() {
        mRequestSug?.let {
            HandlerManager.removeCallbacks(it)
        }

        getCurrentChildFragment()?.getCurrQuickListenModel()?.let {
            if (it.refId != PlayTools.getCurTrackId(ToolUtil.getCtx())) {
                return
            }

            // 合集声音不请求sug
            if (it.isTrackCollect() || it.isXimaTen() || it.isVideo()) {
                return
            }

            if (it.sug.isAvailable()) {
                return
            }

            mRequestSug = createRequestSug(it)
            HandlerManager.postOnUIThreadDelay4Kt(5000, mRequestSug)
        }
    }

    override fun onBackPressed(): Boolean {
        if (mRnViewInflate && mRnView != null) {
            mRnView?.sendMessageToJS("hideAiChatPanel", "")
            return true
        }
        return super.onBackPressed()
    }

    override fun tabClickRefresh() {
        handleTabClick(mMainViewPager.currentItem)
    }

    override fun onDestroy() {
        super.onDestroy()
        QuickListenHomeHelper.mDocLruCache.evictAll()
        QuickListenManager.setQuickPage(null)
        QuickListenHomeHelper.clearBehavior()

        if (mFinishToShowPupop) {
            HomePageQuickListenGuideUtil.showGuidePop()
            MMKVUtil.getInstance().saveBoolean("quick_listen_is_opened", true)
        }
        HandlerManager.removeCallbacks(mPlayUpdatePlayBtnRunnable)
        HandlerManager.removeCallbacks(mPauseUpdatePlayBtnRunnable)
        ToolUtil.getMainActivity()?.removeMainTabClickCallback(mTabClick)
    }

    override fun darkStatusBar(): Boolean {
        if (mMainTabs.getTag(R.id.banner_data_key) == true) {
            return false
        }
        return super.darkStatusBar()
    }

    private val mQuickListenPage = object : IQuickListenPage {
        @WorkerThread
        override fun getListSource(cardType: String, refId: Long): String? {
            val quickListenModel = getCurrentChildFragment()?.getQuickListenListData()?.firstOrNull {
                if (cardType == QuickElementType.TRACK_COLLECTION_CARD.elementType && it.isTrackCollect() && refId == it.refId) {
                    true
                } else if (cardType == QuickElementType.XIMA_TEN_CARD.elementType && it.isXimaTen() && refId == it.refId) {
                    true
                } else if (cardType == QuickElementType.VIDEO.elementType && it.isVideoCollect() && refId == it.refId) {
                    true
                } else {
                    false
                }
            }

            try {
                if (quickListenModel != null) {
                    return GSON.toJson(quickListenModel)
                }
            } catch (e: Exception) {
            }

            return null
        }

        @WorkerThread
        override fun sendPlaySoundInList(cardType: String, refId: Long) {
            val quickListenListData = getCurrentChildFragment()?.getQuickListenListData()
            if (quickListenListData != null) {
                var quickListenModel: QuickListenModel? = null
                kotlin.run {
                    quickListenListData.forEach {
                        if ((cardType == QuickElementType.TRACK_COLLECTION_CARD.elementType && it.isTrackCollect())
                            || (cardType == QuickElementType.XIMA_TEN_CARD.elementType && it.isXimaTen())
                            || it.isVideoCollect()) {
                            it.subElements?.forEach {
                                if (refId == it.refId) {
                                    quickListenModel = it
                                    return@run
                                }
                            }
                        } else if (refId == it.refId) {
                            quickListenModel = it
                            return@run
                        }
                    }
                }

                quickListenModel?.let {
                    playWithTrackIdSceneId(it, false, quickListenListData, refId.toLong())
                }
            }
        }

        override fun setRNTouchPassThrough(passThrough: Boolean) {
            Logger.d(TAG, "setRNTouchPassThrough passThrough=$passThrough")
            HandlerManager.postOnMainAuto { getAiContainer()?.setFullScreen(!passThrough) }
        }

        override fun setNonPenetratingHeight(height: Int) {
            Logger.d(TAG, "setNonPenetratingHeight height=$height")
            HandlerManager.postOnMainAuto { getAiContainer()?.setNonPenetratingHeight(height) }
        }
    }

    private fun updateBehavior(isPlayStart: Int = 1) {
        if (mTabs?.getOrNull(mMainViewPager.currentItem)?.elementType != ELEMENT_TYPE_RECOMMEND) {
            return
        }

        QuickListenHomeHelper.getCurPlayingId(isPlayStart, XmPlayerManager.getInstance(ToolUtil.getCtx()).duration)
    }

    private fun traceOnTabShow() {
        if (mMainTabs.getChildAt(0) is ViewGroup) {
            val viewGroup = mMainTabs.getChildAt(0) as ViewGroup
            val childCount = viewGroup.childCount
            var firstVisibleViewPos = -1
            for (i in 0 until childCount) {
                val view = viewGroup.getChildAt(i)
                if (ViewStatusUtil.viewIsRealShowing(view)) {
                    if (firstVisibleViewPos < 0) {
                        firstVisibleViewPos = i
                    }
                    mTabs?.getOrNull(i)?.let {
                        QuickListenTraceUtil.trace69163(it.id, it.title, i + 1)
                    }
                } else if (firstVisibleViewPos >= 0) {
                    return
                }
            }
        }
    }

    private fun sendAiAgentQueryToRn(sug: String?, tabId: Long) {
        if (sug.isAvailable()) {
            try {
                val map = mutableMapOf<String, Any>()
                map.put("query", sug ?: "")
                map.put("tabId", tabId)
                mRnView?.sendMessageToJS("sendAiAgentQuery", map)
            } catch (e: Exception) {
            }
        }
    }

    private fun showRefreshTips() {
        if (mMainTabs.scrollX != 0 || mMainViewPager.currentItem != 0) {
            return
        }

        if (mMainTabs.getChildAt(0) is ViewGroup) {
            val viewGroup = mMainTabs.getChildAt(0) as ViewGroup
            val childCount = viewGroup.childCount
            if (childCount > 0) {
                val view = viewGroup.getChildAt(0)
                val rect = Rect()
                view.getGlobalVisibleRect(rect)
                if (rect.isEmpty || rect.bottom == 0) {
                    return
                }

                val centerX = (rect.right + rect.left) / 2

                mDownArrow.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    marginStart = centerX - 6.dp
                }

                mTitleRefreshHint.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    marginStart = rect.left
                }

                HandlerManager.postOnUIThreadDelay4Kt(5000) {
                    if (canUpdateUi()) {
                        mDownArrow.visibility = View.GONE
                        mTitleRefreshHint.visibility = View.GONE
                    }
                }

                mDownArrow.visibility = View.VISIBLE
                mTitleRefreshHint.visibility = View.VISIBLE
                mTitleRefreshHint.setOnClickListener {
                    mDownArrow.visibility = View.GONE
                    mTitleRefreshHint.visibility = View.GONE

                    handleTabClick(mMainTabs.currentItem)
                }
            }
        }
    }

    private val mListenOpenAgentAudioBroadCast = object: BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "quick_listen_open_agent_audio") {
                mIsGotoAgent = true
                if (XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying
                    || getCurrentChildFragment()?.getVideoControl(getCurrentChildFragment()?.getCurrentItem() ?: 0)?.isPlaying == true) {
                    mPausedByAgentAudio = true
                }
            }
        }
    }

    private fun listenOpenAgentAudioBroadCast() {
        LocalBroadcastManager.getInstance(ToolUtil.getCtx()).registerReceiver(mListenOpenAgentAudioBroadCast,
            IntentFilter("quick_listen_open_agent_audio"))
    }

    private fun removeAgentAudioBroadCast() {
        LocalBroadcastManager.getInstance(ToolUtil.getCtx()).unregisterReceiver(mListenOpenAgentAudioBroadCast)
    }

    override fun enableAdapt(): Boolean {
        return false
    }

    override fun onRefresh() {
    }

    private fun updatePlayStatus(nowUpdate: Boolean) {
        updatePlayStatus(XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying, nowUpdate)
    }

    private fun updateBottomPlayBtnBg() {
        if (mBottomLay.visibility == View.VISIBLE) {
            if (getCurTab()?.elementType == ELEMENT_TYPE_NEW_USER) {
                mBottomLay.setBackgroundColor(R.color.main_color_121212.color)
                mPlayBtn.setTheme(true)
            } else {
                mBottomLay.setBackgroundColor(R.color.framework_color_ffffff_121212.color)
                mPlayBtn.setTheme(false)
            }
        }
    }

    private fun changeRootTabBarTheme(isNewUser: Boolean) {
        (MainApplication.getMainActivity() as? MainActivity)?.let {
            it.forceUpdateTabTheme(isNewUser, false)
        }
    }

    private val mVideoPlayStatusListener = object : SimpleXmVideoPlayStatusListener() {
        override fun onStart(videoSourceUrl: String?) {
            super.onStart(videoSourceUrl)
            if (QuickListenTabAbManager.isNewUserForQuickListen) {
                HandlerManager.removeCallbacks(mVideoStartStatusUpdate)
                HandlerManager.postOnUIThreadDelay4Kt(300, mVideoStartStatusUpdate)
            }

            if (getCurrentChildFragment()?.getVideoControl(getCurrentChildFragment()?.getCurrentItem() ?: 0)?.isPlaying == true) {
                QuickListenPerformance.onPlayStart()
            }
        }

        override fun onRenderingStart(videoSourceUrl: String?, renderingSpentMilliSec: Long) {
            super.onRenderingStart(videoSourceUrl, renderingSpentMilliSec)

            QuickListenPerformance.onPlayStart()
        }

        override fun onPause(videoSourceUrl: String?, playedTime: Long, duration: Long) {
            super.onPause(videoSourceUrl, playedTime, duration)
            if (QuickListenTabAbManager.isNewUserForQuickListen) {
                HandlerManager.removeCallbacks(mVideoStartStatusUpdate)
                if (abs(System.currentTimeMillis() - mSkipUpdatePlayStatus) > 200) {
                    ToolUtil.getMainActivity()?.playBarFragment?.refreshPlaying(false, true)
                }
                updatePlayStatus(false, true)
            }

            updateVideoHistory()
        }

        override fun onComplete(videoSourceUrl: String?, duration: Long) {
            super.onComplete(videoSourceUrl, duration)

            if (QuickListenTabAbManager.isNewUserForQuickListen) {
                HandlerManager.removeCallbacks(mVideoStartStatusUpdate)
                if (abs(System.currentTimeMillis() - mSkipUpdatePlayStatus) > 200) {
                    ToolUtil.getMainActivity()?.playBarFragment?.refreshPlaying(false, false)
                }
                updatePlayStatus(false, false)
            }
        }
    }

    private val mVideoStartStatusUpdate = Runnable {
        ToolUtil.getMainActivity()?.playBarFragment?.refreshPlaying(true, true)
        ToolUtil.getMainActivity()?.playBarFragment?.setShouldRefreshWhenPrepare(false)
        updatePlayStatus(true, true)
    }


    private fun updatePlayStatus(isPlaying: Boolean, updateNow: Boolean) {
        HandlerManager.removeCallbacks(mPlayUpdatePlayBtnRunnable)
        HandlerManager.removeCallbacks(mPauseUpdatePlayBtnRunnable)

        val runnable = if (isPlaying) mPlayUpdatePlayBtnRunnable else mPauseUpdatePlayBtnRunnable
        if (updateNow) {
            runnable.run()
        } else {
            HandlerManager.postOnUIThreadDelay(runnable, 1000)
        }
    }

    private val mPlayUpdatePlayBtnRunnable = Runnable { mPlayBtn.isPlaying(true) }
    private val mPauseUpdatePlayBtnRunnable = Runnable { mPlayBtn.isPlaying(false) }

    private var updateHistoryTime = 0L
    private fun updateVideoHistory() {
        if (System.currentTimeMillis() - updateHistoryTime > 5000) {
            updateHistoryTime = System.currentTimeMillis()

            RouteServiceUtil.getHistoryManagerForMain().putSound(PlayTools.getCurTrack(ToolUtil.getCtx()))

            val historyManager = RouterServiceManager.getInstance().getService(ICloudyHistory::class.java)
            historyManager?.syncCloudHistory(false)
        }
    }
}