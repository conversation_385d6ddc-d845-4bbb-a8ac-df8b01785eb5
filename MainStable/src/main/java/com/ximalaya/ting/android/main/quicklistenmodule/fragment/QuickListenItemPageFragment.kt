package com.ximalaya.ting.android.main.quicklistenmodule.fragment

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.children
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.reflect.FieldUtils
import com.ximalaya.ting.android.framework.reflect.MethodUtils
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.framework.util.ViewUtil.PADDING_TOP
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.data.model.QuickElementType
import com.ximalaya.ting.android.host.data.model.QuickElementTypeMap
import com.ximalaya.ting.android.host.data.model.QuickListenModel
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.QuickListenForceItem
import com.ximalaya.ting.android.host.manager.QuickListenPerformance
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager
import com.ximalaya.ting.android.host.manager.TrackCollectManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.quicklisten.IItemFragmentCallback
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManager
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenManager
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenVideoUtil
import com.ximalaya.ting.android.host.manager.rn.VerticalViewPager
import com.ximalaya.ting.android.host.manager.track.AgentRadioCollectListener
import com.ximalaya.ting.android.host.manager.track.AgentRadioEventManage
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage.TrackLikeStatusListener
import com.ximalaya.ting.android.host.model.quicklisten.ELEMENT_TYPE_NEW_USER
import com.ximalaya.ting.android.host.model.quicklisten.ELEMENT_TYPE_RECOMMEND
import com.ximalaya.ting.android.host.util.XimaTenDataUtil
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.color
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.kt.isAvailable
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.quicklistenmodule.adapter.QuickListenVerticalPagerAdapter
import com.ximalaya.ting.android.main.quicklistenmodule.iinterface.IQuickListenHomePageControl
import com.ximalaya.ting.android.main.quicklistenmodule.iinterface.IQuickListenItemPageControl
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenConfigManager
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenHomeHelper
import com.ximalaya.ting.android.main.quicklistenmodule.manager.QuickListenTraceUtil
import com.ximalaya.ting.android.main.quicklistenmodule.view.CustomSwipeRefreshLayout
import com.ximalaya.ting.android.main.stable.R
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.util.EasyConfigure
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmplaysdk.IMediaPlayerControl
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONArray
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.lang.reflect.Field
import kotlin.math.absoluteValue


/**
 * Created by nali on 2025/6/20.
 * <AUTHOR>
 */
private const val TAG = "QuickListenItemPageFragment"

private const val QUICK_LISTEN_LAST_REQUEST_TIME_PREFIX = "key_quick_listen_last_request_time_"
const val QUICK_LISTEN_LAST_REQUEST_TIME = "key_quick_listen_last_request_time"
// 存储键名
private const val LAST_ENTRY_DATE_KEY = "key_daily_news_last_entry_date_2"
// 上次请求时间
const val LAST_REQUEST_TIME_KEY = "key_daily_news_last_request_time_2"
private const val quick_listen_agent_guide_show_time = "key_quick_listen_agent_guide_show_time"

class QuickListenItemPageFragment: BaseFragment2(), IQuickListenItemPageControl {
    private val mMainViewPager: ViewPager2 by lazy { findViewById<ViewPager2>(R.id.main_view_pager) }
    private val mMainSwiperRefresh: CustomSwipeRefreshLayout by lazy { findViewById<CustomSwipeRefreshLayout>(R.id.main_swipe_refresh) }
    private val mTitleBar: View by lazy { findViewById<View>(R.id.main_title_bar) }
    private val mRootLay: View by lazy { findViewById<View>(R.id.main_root_layout) }

    private val swipeTipsContainer: FrameLayout by lazy { findViewById<FrameLayout>(R.id.main_swpie_hint_container) }
    val swipeTipsText: TextView by lazy { findViewById<TextView>(R.id.main_swpie_hint_text) }

    private val mBottomHeight: Int = 116.dp
    private val mAdapter = QuickListenVerticalPagerAdapter(this)
    private var mChildPosition: Int = 0
    private var mSelectPagePositionByTrackId: Long = 0
    private var isFromTab: Boolean = false

    private var mMaxCardIndex = -1
    private var agentGuideDateLimitRef = 0
    private var agentGuideCountLimitRef = 0
    private var mTabId = -1L
    private var mTabName = ""
    private var mTabType: String? = null
    private var mForceItems: List<QuickListenForceItem>? = null
    private var notifyPageChange = true

    private var mPageState: Int = ViewPager2.SCROLL_STATE_IDLE
    private var mNotifyCurRunnable: Runnable? = null
    private var mNotifyAdjacencyRunnable: Runnable? = null

    private var mVideoControlMap = mutableMapOf<Int, WeakReference<IMediaPlayerControl>>()
    private var isPaused = false

    private var mPageViewTime = 0L

    override fun getPageLogicName(): String = this::class.java.simpleName

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        mChildPosition = arguments?.getInt("childPosition") ?: 0
        mTabId = arguments?.getLong("tabId", -1L) ?: -1L
        mTabName = arguments?.getString("tabName", "") ?: ""
        mTabType = arguments?.getString("elementType", null) ?: null
        mSelectPagePositionByTrackId = arguments?.getLong("selectPagePositionByTrackId") ?: 0
        isFromTab = arguments?.getBoolean("isFromTab") ?: false
        mForceItems = arguments?.getParcelableArrayList("forceItems")
        if (isFromTab) {
            QuickListenDataManager.mPauseTabId.remove(mTabId)
            QuickListenDataManager.mTabDataChangeList.remove(mTabId)
        }

        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun initUi(savedInstanceState: Bundle?) {
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            mTitleBar.layoutParams?.height = (mTitleBar.layoutParams?.height ?: 0) + BaseUtil.getStatusBarHeight(context)
            ViewUtil.onlySetViewPaddingOne(mTitleBar, BaseUtil.getStatusBarHeight(context), PADDING_TOP)
        }

        if (isFromTab) {
            ViewUtil.onlySetViewPaddingOne(mRootLay, 3.dp, ViewUtil.PADDING_BOTTOM)
        }

        if (mTabType == ELEMENT_TYPE_NEW_USER) {
            mRootLay.setBackgroundResource(R.color.host_color_000000)
            mMainSwiperRefresh.updateLayoutParams<ConstraintLayout.LayoutParams> {
                topToBottom = ConstraintLayout.LayoutParams.UNSET
                topToTop = ConstraintLayout.LayoutParams.PARENT_ID
            }
        } else {
            mRootLay.setBackgroundColor(R.color.framework_color_ffffff_121212.color)
        }

        mMainViewPager.orientation = ViewPager2.ORIENTATION_VERTICAL
        val recyclerView: RecyclerView = mMainViewPager.getChildAt(0) as RecyclerView
        recyclerView.setPadding(mMainViewPager.getPaddingLeft(), 0, 0, if (isFromTab || QuickListenTabAbManager.isNewUserForQuickListen) 0 else mBottomHeight)
        recyclerView.setClipToPadding(false)
        (recyclerView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        mMainViewPager.offscreenPageLimit = 1
        mMainViewPager.adapter = mAdapter

        mMainSwiperRefresh.setOnRefreshListener(object : CustomSwipeRefreshLayout.OnRefreshListener {
            override fun onRefresh() {
                getHomePageControl()?.onRefresh(mMainSwiperRefresh)
                loadDataImpl(true)
            }

            override fun onPullProgressListener(pullDistance: Float) {
                getHomePageControl()?.onPagePull(pullDistance)
            }
        })

        mMainViewPager.adapter?.registerAdapterDataObserver(object :
            RecyclerView.AdapterDataObserver() {
            override fun onChanged() {
                super.onChanged()

                if (notifyPageChange) {
                    getHomePageControl()?.onChildPageSelected(mChildPosition, mMainViewPager.currentItem)
                }

                getQuickListenListData().getOrNull(mMainViewPager.currentItem)?.let {
                    setTabTheme(it.isXimaTen())
                }
                QuickListenVideoUtil.setVideoControl(mVideoControlMap.getOrDefault(mMainViewPager.currentItem, null)?.get())
            }
        })

        mMainViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            var mLastPageState = ViewPager.SCROLL_STATE_IDLE
            var lastPosition = -1
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)

                val preIndex = position - 1
                if (preIndex >= 0) {
                    mVideoControlMap.getOrDefault(preIndex, null)?.get()?.pause()
                }

                val nextIndex = position + 1
                if (nextIndex >= 0 && nextIndex < mAdapter.itemCount) {
                    mVideoControlMap.getOrDefault(preIndex, null)?.get()?.pause()
                }

                QuickListenVideoUtil.setVideoControl(mVideoControlMap.getOrDefault(position, null)?.get())
                setMaxCardIndex(position, "")

                updateTrackInfoAndPlay(position)
                if (notifyPageChange) {
                    if (lastPosition != -1) {
                        QuickListenTraceUtil.trace67649(PlayTools.getCurTrackId(ToolUtil.getCtx()),
                            getQuickListenListData(), lastPosition, false, lastPosition < position,
                            mTabId,
                            getVideoControl(getCurrentItem())?.currentPosition)
                    }

                    QuickListenTraceUtil.trace67648(mTabId, getHomePageControl()?.getFrom() ?: "",
                        getQuickListenListData().getOrNull(position))

                    getHomePageControl()?.onChildPageSelected(mChildPosition, position)
                }

                getQuickListenListData().getOrNull(position)?.let {
                    setTabTheme(it.isXimaTen())

                    if (it.isAgentGuide()) {
                        QuickListenTraceUtil.trace68820(mTabId)
                    } else if (it.isTrackCollect()) {
                        QuickListenTraceUtil.trace68571(
                            QuickListenManager.getCurQuickListenModel(it)?.refId ?: 0,
                            getQuickListenListData(),
                            position,
                            mTabId,
                            getHomePageControl()?.getFrom() ?: ""
                        )
                    } else if (it.isVideo()) {
                        val trackId = if (it.isVideoCollect()) {
                            QuickListenManager.getCurQuickListenModel(it)?.refId ?: 0
                        } else {
                            it.refId ?: 0
                        }

                        QuickListenTraceUtil.traceShow(69373,
                            trackId,
                            getQuickListenListData(),
                            position,
                            mTabId,
                            getHomePageControl()?.getFrom() ?: "")
                    }
                }

                lastPosition = position
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                mPageState = state
                checkPageScrollState()

                // 松手了
                if (mLastPageState == ViewPager.SCROLL_STATE_DRAGGING && state == ViewPager.SCROLL_STATE_SETTLING) {
                    getQuickListenListData()?.getOrNull(getCurrentItem())?.let {
                        QuickListenPerformance.onSlideStart(getCurrentItem(),
                            QuickListenTraceUtil.getCardType(it))
                    }
                } else if (mLastPageState == ViewPager.SCROLL_STATE_SETTLING && state == ViewPager.SCROLL_STATE_IDLE) { // 滚动停止
                    getQuickListenListData()?.getOrNull(getCurrentItem())?.let {
                        QuickListenPerformance.onSlideStop(QuickListenTraceUtil.getCardType(it))
                    }
                }

                mLastPageState = state
            }
        })

        AlbumEventManage.addListener(mCollectListener)
        LikeTrackManage.addListener(mTrackLikeStatusListener)
        AgentRadioEventManage.addListener(mAgentRadioCollectListener)
        TrackCollectManager.getInstance().addListener(mTrackCollectListener)

        runCatching {
            val config = ConfigureCenter.getInstance().getString("toc", "quick_listen_rn_config", "")
            if (config.isAvailable()) {
                val jsonConfig = JSONObject(config)
                val dateLimit = jsonConfig.optInt("AGENT_GUIDE_DATE_LIMIT", 0)
                val countLimit = jsonConfig.optInt("AGENT_GUIDE_COUNT_LIMIT", 0)
                if (jsonConfig.optBoolean("SUPPORT_AGENT_GUIDE", false)) {
                    val agent_config_version = QuickListenConfigManager.getInstance().agent_config_version
                    val versionArr = jsonConfig.optJSONArray("VERSION_AGENT_GUIDE")
                    var enable = false
                    if (versionArr != null && versionArr.length() > 0) {
                        for (i in 0 until versionArr.length()) {
                            val version = versionArr.optString(i, "")
                            if (version == agent_config_version) {
                                enable = true
                            }
                        }
                    }
                    updateConfigAgent(enable, dateLimit, countLimit)
                } else {
                    updateConfigAgent(false, dateLimit, countLimit)
                }
            }
        }.onFailure { it.printStackTrace() }
    }

    private fun updateConfigAgent(enable: Boolean, dateLimit: Int, countLimit: Int) {
        agentGuideDateLimitRef = dateLimit
        agentGuideCountLimitRef = countLimit
        if (!enable) {
            agentGuideDateLimitRef = -1
            agentGuideCountLimitRef = -1
        }
    }

    private fun checkPageScrollState() {
        if (mPageState == ViewPager2.SCROLL_STATE_IDLE) {
            HandlerManager.removeCallbacks(mCheckPageSettingState)
            try {
                if (mNotifyAdjacencyRunnable != null) {
                    mNotifyCurRunnable = null
                    mNotifyAdjacencyRunnable?.run()
                    mNotifyAdjacencyRunnable = null
                } else if (mNotifyCurRunnable != null) {
                    mNotifyCurRunnable?.run()
                    mNotifyCurRunnable = null
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun updateTrackInfoAndPlay(position: Int) {
        if (mTabType != ELEMENT_TYPE_RECOMMEND) {
            return
        }
        val card = mAdapter.getItemOrNull(position)
        if (card == null) {
            return
        }
        val curTime = System.currentTimeMillis()
        if (card.isAgentGuide()) {
            setAgentGuideShowTime(curTime)
        }
        // 判断是否适合引导创建AI电台
        val supportAgentGuideRef = QuickListenConfigManager.getInstance().supportAgentGuideRef
        if (supportAgentGuideRef != true) {
            return
        }
        val lastShowTime = getAgentGuideShowTime()
        val dayMs = 24 * 60 * 60 * 1000;
        if (lastShowTime > 0 &&
            agentGuideDateLimitRef > 0 &&
            curTime - lastShowTime < agentGuideDateLimitRef * dayMs
        ) {
            Logger.d(TAG, "setNeedAgentGuide - ${QuickListenConfigManager.getInstance().needAgentGuideRef}")
            if (QuickListenConfigManager.getInstance().needAgentGuideRef) {
                QuickListenConfigManager.getInstance().setNeedAgentGuide(false)
            }
            // 在冷却期内，不显示引导
            QuickListenConfigManager.getInstance().setNeedAgentGuide(false)
            return
        }

        if (agentGuideCountLimitRef > 0 && (mMaxCardIndex+1) >= agentGuideCountLimitRef && !QuickListenConfigManager.getInstance().needAgentGuideRef) {
            Logger.d(TAG, "setNeedAgentGuide2 - ${QuickListenConfigManager.getInstance().needAgentGuideRef}")
            QuickListenConfigManager.getInstance().setNeedAgentGuide(true)
        }
    }

    private fun setAgentGuideShowTime(time: Long) {
        MMKVUtil.getInstance().saveLong(quick_listen_agent_guide_show_time, time)
    }

    private fun getAgentGuideShowTime(): Long {
        return MMKVUtil.getInstance().getLong(quick_listen_agent_guide_show_time, 0L)
    }

    // 记录卡片滑动的最大深度，用于负反馈时移除列表操作
    private fun setMaxCardIndex(index: Int, source: String) {
        /// 从+2卡片开始移除
        val tNewIndex = index
        if (tNewIndex >= 0 && mMaxCardIndex < tNewIndex) {
//            console.log(`setMaxCardIndex >>> ${tNewIndex} ${source} old index >>> ${maxCardIndexRef.current}`)
            mMaxCardIndex = tNewIndex
        }
    }

    override fun loadData() {
        loadDataImpl(false)
    }

    private var mIsLoading = false

    private fun loadDataImpl(isRefresh: Boolean = false) {
        if (mIsLoading) {
            return
        }
        mIsLoading = true

        val firstLoading = !isRefresh

        if (firstLoading) {
            onPageLoadingCompleted(LoadCompleteType.LOADING)
        }

        if (isRefresh) {
            XmRequestPage.resetPageUniqueRequestId(QuickListenTraceUtil.getRequestPageId(mTabId))
        }

        val useCache = firstLoading && QuickListenDataManager.getInstance().canUseCache(mTabId, arguments?.getBoolean("openFragmentBeforeIsPlaying"))

        var forceItem: MutableList<QuickListenForceItem>? = null
        if (!mForceItems.isNullOrEmpty()) {
            // 从新拷贝一份
            forceItem = mForceItems?.toMutableList()
        }
        mForceItems = null

        // 如果mForceItems 不为空, 不使用缓存
        QuickListenDataManager.getInstance().requestXimaTenAndPlayNew(useCache, true, true, null, mTabId, mTabType == ELEMENT_TYPE_RECOMMEND, object : IDataCallBack<List<JSONObject?>?> {
            override fun onSuccess(data: List<JSONObject?>?) {
                mIsLoading = false
                if (isRefresh) {
                    mMainSwiperRefresh.isRefreshing = false
                    getHomePageControl()?.onRefreshComplete(mChildPosition)
                }
                val listData = XimaTenDataUtil.convertDataToQuickListenModel(mTabId, data)
                if (listData == null || listData.isEmpty()) {
                    onPageLoadingCompleted(LoadCompleteType.NOCONTENT)
                    Logger.d(TAG, "loadData onSuccess data is null or empty")
                    return
                }
                onPageLoadingCompleted(LoadCompleteType.OK)
                // 十条卡，声音小于5条时，应该隐藏十条卡
                // 过滤掉不符合要求的 elementType
                var resListData = listData.filter { item ->
                    val result = if (item.isXimaTen()) {
                        (item.subElements?.size ?: 0) >= 5
                    } else {
                        true
                    }
                    result
                }.filter { item ->
                    item.elementType.isNullOrEmpty() || QuickElementTypeMap.contains(item.elementType)
                }

                Logger.d(TAG, "loadData onSuccess data size ${data?.size}, result size ${resListData.size}")

                // 数据去重：普通内容卡用refId，十条卡用title
                val elementSet = hashSetOf<Long>()
                val ximaTenSet = hashSetOf<String?>()
                if (useCache && resListData.isNotEmpty()) {
                    resListData = resListData.filter {
                        if (it.isXimaTen()) {
                            // 十条卡，使用title去重
                            if (!ximaTenSet.contains(it.title)) {
                                ximaTenSet.add(it.title)
                                true
                            } else {
                                false
                            }
                        } else {
                            // 普通内容卡，使用refId去重
                            if (!elementSet.contains(it.refId ?: 0L)) {
                                elementSet.add(it.refId ?: 0L)
                                true
                            } else {
                                false
                            }
                        }
                    }
                }
                Logger.d(TAG, "loadData onSuccess2 data size ${data?.size}, result size ${resListData.size}")

                var positionIndex = 0
                var dataType: String? = QuickElementType.AUDIO_CARD.elementType

                // 新请求的数据，设置上次请求时间
                if (useCache == false) {
//                    MMKVUtil.getInstance().saveLong(LAST_REQUEST_TIME_KEY, System.currentTimeMillis())
//                    MMKVUtil.getInstance().saveLong(QUICK_LISTEN_LAST_REQUEST_TIME, System.currentTimeMillis())

                    MMKVUtil.getInstance().saveLong("$LAST_REQUEST_TIME_KEY$mTabId", System.currentTimeMillis())
                    MMKVUtil.getInstance().saveLong("$QUICK_LISTEN_LAST_REQUEST_TIME_PREFIX$mTabId", System.currentTimeMillis())

//                    if (mSelectPagePositionByTrackId > 0) {
//                    }
                    notifyPageChange = false

                    setCurrentItemImpl(0, false)
                    mAdapter.setList(resListData)
                    notifyPageChange = true
                    if (mSelectPagePositionByTrackId > 0) {
                        val position = QuickListenManager.getQuickListenListIndexByTrackId(mSelectPagePositionByTrackId, resListData)
                        val playModel = QuickListenManager.getQuickListenModelFromListByTrackId(mSelectPagePositionByTrackId, resListData)
                        dataType = if (playModel?.parentElementType.isAvailable()) playModel?.parentElementType else playModel?.elementType
                        Logger.log("QuickListenItemPageFragment : onSuccess playWithTrackIdSceneId getQuickListenListIndexByTrackId $position   $mSelectPagePositionByTrackId")
                        if (position >= 0) {
                            setCurrentItemImpl(position, false)
                        }
                        mSelectPagePositionByTrackId = 0
                        positionIndex = position
                    }
                } else {
                    if (mSelectPagePositionByTrackId > 0) {
                        notifyPageChange = false
                    }
                    setCurrentItemImpl(0, false)
                    mAdapter.setList(resListData)
                    notifyPageChange = true
                    // resetCardIndex(res)
                    if (mSelectPagePositionByTrackId > 0) {
                        val position = QuickListenManager.getQuickListenListIndexByTrackId(mSelectPagePositionByTrackId, resListData)
                        val playModel = QuickListenManager.getQuickListenModelFromListByTrackId(mSelectPagePositionByTrackId, resListData)
                        dataType = if (playModel?.parentElementType.isAvailable()) playModel?.parentElementType else playModel?.elementType
                        Logger.log("QuickListenItemPageFragment : onSuccess playWithTrackIdSceneId  getQuickListenListIndexByTrackId $position  $mSelectPagePositionByTrackId")
                        if (position >= 0) {
                            setCurrentItemImpl(position, false)
                        }
                        mSelectPagePositionByTrackId = 0
                        positionIndex = position
                    }
                }

                QuickListenPerformance.firstInitPlay(dataType ?: QuickElementType.AUDIO_CARD.elementType, positionIndex,
                    XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying)

                QuickListenPerformance.onRenderComplete()
            }

            override fun onError(code: Int, message: String?) {
                mIsLoading = false

                if (isRefresh) {
                    mMainSwiperRefresh.isRefreshing = false
                    getHomePageControl()?.onRefreshComplete(mChildPosition)
                }
                Logger.e(TAG, "onError code: $code, message: $message")
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
            }
        },
            object : IItemFragmentCallback {
                override fun play(tabId: Long, autoPlay: Boolean) {
                    val curTabId = getHomePageControl()?.curTabId() ?: -1L
                    if (curTabId == tabId) {
                        val trackId =
                            QuickListenDataManager.getInstance().getLastTabPlayTrackId(tabId)
                        val data = QuickListenDataManager.getInstance().cache(tabId)
                        PlayTools.playQuickListenListWithTrackId(data, trackId, false, tabId, !isPaused)
                    }
                }
            }, forceItem, getHomePageControl()?.getFrom())
//        val homePageTabData = XimaTenDataManager.getInstance().getItemTabData()
//        mAdapter.setList(homePageTabData)
    }

    override fun getContainerLayoutId(): Int = R.layout.main_quick_listen_item_page_fragment

    fun getHomePageControl(): IQuickListenHomePageControl? {
        return (parentFragment as? QuickListenHomePageFragment)?.mPageControl
    }

    override fun getRefreshView(): CustomSwipeRefreshLayout {
        return mMainSwiperRefresh
    }

    override fun getCurrQuickListenModel(): QuickListenModel? {
        if (mAdapter.itemCount <= 0) {
            return null
        }
        return mAdapter.getItem(mMainViewPager.currentItem)
    }

    override fun getCurrentItem(): Int {
        return mMainViewPager.currentItem
    }

    override fun notifyCurShowChange() {
        val runnable = Runnable {
            runCatching {
                mAdapter.notifyItemChanged(mMainViewPager.currentItem)
            }
        }

        mNotifyCurRunnable = null
        if (mPageState == ViewPager2.SCROLL_STATE_IDLE) {
            runnable.run()
        } else {
            mNotifyCurRunnable = runnable
        }
    }

    override fun getCurModel() = null

    override fun notifyAdjacencyItemChange() {
        val runnable = Runnable {
            val currentItem = mMainViewPager.currentItem
            var prevItem = currentItem - 1
            if (prevItem < 0) {
                prevItem = 0
            }
            var nextItem = currentItem + 1
            if (nextItem >= mAdapter.itemCount) {
                nextItem = mAdapter.itemCount - 1
            }
            runCatching {
                mAdapter.notifyItemRangeChanged(prevItem, nextItem - prevItem + 1)
            }
        }

        mNotifyAdjacencyRunnable = null
        if (mPageState == ViewPager2.SCROLL_STATE_IDLE) {
            runnable.run()
        } else {
            mNotifyAdjacencyRunnable = runnable
        }
    }

    override fun getQuickListenListData(): List<QuickListenModel> {
        return mAdapter.data
    }

    override fun getRequestId(tabId: Long) = -1

    override fun isRnPage() = false

    override fun isPageInited() = false

    override fun playTrack(cardPosition: Int) {
    }

    override fun playTrack(tabId: Long, trackId: Long) {
    }

    override fun setItemPosition(cardPosition: Int) {
    }

    override fun setViewPager(viewPager: VerticalViewPager?, tabId: Long, jsonArray: JSONArray?) {
    }

    override fun getVideoControl(index: Int): IMediaPlayerControl? {
        return mVideoControlMap.getOrDefault(index, null)?.get()
    }

    override fun updateDataList(list: List<JSONObject>?, index: Int, tabId: Long, needPlay: Boolean, willPlayRefId: Long) {
        if (list == null || list.size <= 0) {
            return
        }
        val targetIndex = if (index >= list.size) 0 else Math.max(0, index)
        MyAsyncTask.execute {
            val listData = XimaTenDataUtil.convertDataToQuickListenModel(mTabId, list)
            val tracks = if (needPlay) PlayTools.convertToCommonTrackList(list, tabId) else null
            postOnUiThread {
                mAdapter.setList(listData)
                setCurrentItem(targetIndex)
                if (needPlay) {
                    val targetRefId = QuickListenDataManager.getInstance().getPlayIndex(list, targetIndex)
                    val playIndex = tracks?.tracks?.indexOfFirst { it.dataId == targetRefId } ?: 0
                    QuickListenDataManager.getInstance().logToFile(TAG, "QuickListenDataManager willPlayRefId=$willPlayRefId, targetRefId=$targetRefId, playIndex=$playIndex")
                    PlayTools.playQuickListenListNew(tracks, playIndex, false)
                }
            }
        }
    }

    override fun insertDataList(list: List<JSONObject>?, tabId: Long, lastRefId: Long) {
        if (list == null || list.size <= 0) {
            return
        }
        MyAsyncTask.execute {
            val listData = XimaTenDataUtil.convertDataToQuickListenModel(tabId, list)
            postOnUiThread {
                mAdapter.addData(listData)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        AlbumEventManage.removeListener(mCollectListener)
        LikeTrackManage.removeListener(mTrackLikeStatusListener)
        AgentRadioEventManage.removeListener(mAgentRadioCollectListener)
        TrackCollectManager.getInstance().removeListener(mTrackCollectListener)
    }

    private var mLoadingView: View? = null

    override fun getLoadingView(): View {
        var loadingView = mLoadingView
        if (loadingView != null) {
            return loadingView
        }

        if (mTabType == ELEMENT_TYPE_NEW_USER) {
            loadingView = LayoutInflater.from(context)
                .inflate(R.layout.main_quick_listen_item_video_loading_view, null, false)
        } else {
            loadingView = LayoutInflater.from(context)
                .inflate(R.layout.main_quick_listen_item_loading_bg_view, null, false)
        }

        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            ViewUtil.onlySetViewPaddingOne(loadingView, BaseUtil.getStatusBarHeight(context) +
                    resourcesSafe.getDimensionPixelSize(R.dimen.host_title_bar_height), PADDING_TOP
            )
        }

        loadingView.findViewById<View>(R.id.main_quick_listen_loading_bottom_height)?.updateLayoutParams<LinearLayout.LayoutParams> {
            height = if (isFromTab) 13.dp else 10.dp
        }

        loadingView.findViewById<View>(R.id.main_quick_listen_bottom_height)?.visibility =
            if (QuickListenTabAbManager.isNewUserForQuickListen) View.GONE else View.VISIBLE
        mLoadingView = loadingView
        return loadingView
    }

    private var mNoContentView: View? = null

    override fun getNoContentView(): View {
        var noContentView = mNoContentView
        if (noContentView != null) {
            return noContentView
        }

        if (mTabType == ELEMENT_TYPE_NEW_USER) {
            mNoContentView = getNetworkErrorView()
            return mNoContentView!!
        }

        noContentView = LayoutInflater.from(context)
            .inflate(R.layout.main_quick_listen_item_net_error_bg_view, null, false)
        if (mTabType == ELEMENT_TYPE_RECOMMEND) {
            noContentView.findViewById<View>(R.id.main_quick_listen_btn_no_net).setOnClickListener {
                loadDataImpl(true)
            }
        } else {
            noContentView.findViewById<TextView>(R.id.main_quick_listen_tv_no_net_tips)?.let {
                it.text = "暂无更多内容"
            }
            noContentView.findViewById<TextView>(R.id.main_quick_listen_tv_no_net_sub_tips)?.let {
                it.visibility = View.GONE
            }
            noContentView.findViewById<TextView>(R.id.main_quick_listen_btn_no_net)?.let {
                it.text = "去「推荐」收听"
                it.setOnClickListener {
                    getHomePageControl()?.setParentCurrentItem(0)
                }
            }
        }

        noContentView.findViewById<ImageView>(R.id.main_quick_listen_err_iv)?.let {
            it.setImageResource(R.drawable.host_icon_load_no_data)
        }

        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            ViewUtil.onlySetViewPaddingOne(noContentView, BaseUtil.getStatusBarHeight(context) +
                    resourcesSafe.getDimensionPixelSize(R.dimen.host_title_bar_height), PADDING_TOP
            )
        }

        noContentView.findViewById<View>(R.id.main_quick_listen_bottom_lay)?.visibility =
            if (QuickListenTabAbManager.isNewUserForQuickListen) View.GONE else View.VISIBLE
        mNoContentView = noContentView
        return noContentView
    }

    private var mNetErrorView: View? = null

    override fun getNetworkErrorView(): View {
        var netErrorView = mNetErrorView
        if (netErrorView != null) {
            return netErrorView
        }
        if (mTabType == ELEMENT_TYPE_NEW_USER) {
            netErrorView = LayoutInflater.from(context)
                .inflate(R.layout.main_quick_listen_item_video_net_error_bg_view, null, false)
        } else {
            netErrorView = LayoutInflater.from(context)
                .inflate(R.layout.main_quick_listen_item_net_error_bg_view, null, false)
        }

        netErrorView.findViewById<View?>(R.id.main_quick_listen_btn_no_net).setOnClickListener {
            loadDataImpl(true)
        }
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            ViewUtil.onlySetViewPaddingOne(netErrorView, BaseUtil.getStatusBarHeight(context) +
                    resourcesSafe.getDimensionPixelSize(R.dimen.host_title_bar_height), PADDING_TOP
            )
        }

        netErrorView.findViewById<View>(R.id.main_quick_listen_loading_bottom_height)?.updateLayoutParams<LinearLayout.LayoutParams> {
            height = if (isFromTab) 13.dp else 10.dp
        }

        netErrorView.findViewById<View>(R.id.main_quick_listen_bottom_lay)?.visibility =
            if (QuickListenTabAbManager.isNewUserForQuickListen) View.GONE else View.VISIBLE

        mNetErrorView = netErrorView
        return netErrorView
    }

    override fun addLoadStateView(
        parent: ViewGroup?,
        addView: View?,
        lp: LayoutParams?,
        type: LoadCompleteType?,
    ) {
        if (type == LoadCompleteType.LOADING || type == LoadCompleteType.NOCONTENT || type == LoadCompleteType.NETWOEKERROR) {
            super.addLoadStateView(
                parent,
                addView,
                LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT),
                type
            )
        } else {
            super.addLoadStateView(parent, addView, lp, type)
        }
    }

    private val mTrackLikeStatusListener = TrackLikeStatusListener { isLikeNow, trackId ->
        var needUpdate = false
        getQuickListenListData().forEach {
            if (it.isXimaTen() || it.isTrackCollect()) {
                it.subElements?.firstOrNull { it.refId == trackId }?.extraInfo?.let {
                    it.isLike = isLikeNow
                    it.likeStatus = if (isLikeNow) 1 else 0
                    needUpdate = true
                }
            } else if (it.refId == trackId) {
                it.extraInfo?.isLike = isLikeNow
                needUpdate = true
            }
        }

        if (needUpdate && mChildPosition == getHomePageControl()?.childPosition()) {
            notifyCurShowChange()
        }
    }

    private val mCollectListener = AlbumEventManage.CollectListener { collect, id ->
        var needUpdate = false
        getQuickListenListData().forEach {
            if (!it.isAgentRadio() && it.surElement?.refId == id) {
                it.surElement?.interact?.subscribed = if (collect) 1 else 0
                needUpdate = true
            }
        }

        if (needUpdate && mChildPosition == getHomePageControl()?.childPosition()) {
            notifyCurShowChange()
        }
    }

    private val mAgentRadioCollectListener: AgentRadioCollectListener = object :
        AgentRadioCollectListener {
        override fun onFail(collect: Boolean, id: Long) {

        }

        override fun onCollectChanged(collect: Boolean, id: Long) {
            var needUpdate = false
            getQuickListenListData().forEach {
                if (it.isAgentRadio() && it.surElement?.refId == id) {
                    it.surElement?.interact?.subscribed = if (collect) 1 else 0
                    needUpdate = true
                }
            }

            if (needUpdate && mChildPosition == getHomePageControl()?.childPosition()) {
                notifyCurShowChange()
            }
        }
    }

    private val mTrackCollectListener = TrackCollectManager.CollectListener { collect, trackId ->
        var needUpdate = false
        getQuickListenListData().forEach {
            if (it.isXimaTen() || it.isTrackCollect()) {
                it.subElements?.firstOrNull { it.refId == trackId }?.extraInfo?.let {
                    it.isCollect = collect
                    needUpdate = true
                }
            } else if (it.refId == trackId) {
                it.extraInfo?.isCollect = collect
                needUpdate = true
            }
        }

        if (needUpdate && mChildPosition == getHomePageControl()?.childPosition()) {
            notifyCurShowChange()
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)

        if (!isVisibleToUser && isResumed) {
            if (mTabType != ELEMENT_TYPE_NEW_USER) {
                notifyAdjacencyItemChange()
            }

            if (mAdapter.itemCount > 0) {
                QuickListenTraceUtil.trace67648(mTabId, getHomePageControl()?.getFrom() ?: "",
                    getQuickListenListData().getOrNull(mMainViewPager.currentItem))
            }
        }

        if (isResumed) {
            if (isVisibleToUser) {
                onPageView()
            } else {
                onPageExit()
            }
        }

        if (isResumed) {
            if (isVisibleToUser) {
                onPageShow()
            } else {
                onPageHide()
            }
        }
    }

    override fun setCurrentItem(index: Int) {
        if (mAdapter.itemCount > index) {
            this.notifyPageChange = false
            QuickListenTraceUtil.trace67649(PlayTools.getCurTrackId(ToolUtil.getCtx()),
                getQuickListenListData(), mMainViewPager.currentItem, true, true, mTabId,
                getVideoControl(getCurrentItem())?.currentPosition)

            QuickListenTraceUtil.trace67648(mTabId, getHomePageControl()?.getFrom() ?: "",
                getQuickListenListData().getOrNull(index))

            setCurrentItemImpl(index, true)

            if (mPageState == ViewPager2.SCROLL_STATE_SETTLING) {
                HandlerManager.postOnUIThreadDelay4Kt(500, mCheckPageSettingState)
            }

            this.notifyPageChange = true
        }
    }

    private fun setCurrentItemImpl(index: Int, smoothScroll: Boolean) {
        if (mMainViewPager.isFakeDragging) {
            mMainViewPager.endFakeDrag()
        }

        mMainViewPager.setCurrentItem(index, smoothScroll)
    }

    private val mCheckPageSettingState = Runnable {
        if (mPageState == ViewPager2.SCROLL_STATE_SETTLING) {
            mPageState = ViewPager2.SCROLL_STATE_IDLE
            checkPageScrollState()
        }
    }

    override fun getTabId(): Long {
        return mTabId
    }

    fun getTabTitle(): String {
        return mTabName
    }

    override fun onMyResume() {
        super.onMyResume()
        onPageView()
        onPageShow()

        if (mAdapter.itemCount > 0) {
            QuickListenTraceUtil.trace67648(mTabId, getHomePageControl()?.getFrom() ?: "",
                getQuickListenListData().getOrNull(mMainViewPager.currentItem))
        }
        isPaused = false
    }

    override fun onPause() {
        super.onPause()
        onPageHide()
        if (userVisibleHint) {
            onPageExit()

            if (!isPaused) {
                HandlerManager.removeCallbacks(mPausePageRunnable)
                mVideoControlMap.getOrDefault(getCurrentItem(), null)?.get()?.let {
                    if (it.isPlaying) {
                        getHomePageControl()?.skipUpdatePlayStatus()
                        it.pause()

                        HandlerManager.postOnUIThreadDelay4Kt(300, mPausePageRunnable)
                    }
                }
            }
        }
        isPaused = true
    }

    private fun onPageShow() {
        if (isFromTab) {
            if (QuickListenDataManager.mTabDataChangeList.contains(mTabId)) {
                QuickListenDataManager.mTabDataChangeList.remove(mTabId)
                val list = QuickListenDataManager.getInstance().cache(mTabId)
                updateDataList(list, QuickListenDataManager.getInstance().getCardIndex(list,
                    QuickListenDataManager.getInstance().getLastTabPlayTrackId(mTabId)),
                    mTabId,
                    QuickListenHomeHelper.needPlayOnInTabPageResume(mTabId), -1)
            } else if (isResumed && EasyConfigure.getBoolean("quick_listen_force_update_data", false)) {
                val list = QuickListenDataManager.getInstance().cache(mTabId)
                updateDataList(list, QuickListenDataManager.getInstance().getCardIndex(list,
                    QuickListenDataManager.getInstance().getLastTabPlayTrackId(mTabId)),
                    mTabId,
                    QuickListenHomeHelper.needPlayOnInTabPageResume(mTabId), -1)
            }

            QuickListenDataManager.mPauseTabId.remove(mTabId)
        }

        HandlerManager.removeCallbacks(mPausePageRunnable)
        HandlerManager.removeCallbacks(mResumePageRunnable)
        HandlerManager.postOnUIThreadDelay4Kt(300, mResumePageRunnable)
    }

    private fun onPageHide() {
        if (isFromTab) {
            QuickListenDataManager.mPauseTabId.add(mTabId)
        }

        HandlerManager.removeCallbacks(mPausePageRunnable)
        HandlerManager.removeCallbacks(mResumePageRunnable)
    }

    private val mResumePageRunnable = Runnable {
        QuickListenVideoUtil.setVideoControl(mVideoControlMap.getOrDefault(mMainViewPager.currentItem, null)?.get())

        val playing = XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying
        val quickListen = XmPlayerManager.getInstance(ToolUtil.getCtx()).isQuickListen
        Logger.log("QuickListenItemPageFragment : mResumePageRunnable恢复播放 1   playing=${playing}  quickListen=${quickListen}")

        if (playing && quickListen) {
            Logger.log("QuickListenItemPageFragment : mResumePageRunnable恢复播放 2")
            mVideoControlMap.getOrDefault(getCurrentItem(), null)?.get()?.let {
                Logger.log("QuickListenItemPageFragment : mResumePageRunnable恢复播放 3")
//                XmPlayerManager.getInstance(ToolUtil.getCtx()).pause(PauseReason.Business.QUICK_LISTEN_VIDEO_RESUME)
                it.start()
            }
        }
    }

    private val mPausePageRunnable = Runnable {
        XmPlayerManager.getInstance(ToolUtil.getCtx()).play()
    }



    fun getShowFrom(): String {
        return getHomePageControl()?.getFrom() ?: ""
    }

    private var visible = false

    private fun onPageView() {
        if (!isAdded) {
            return
        }
        if (!visible) {
            mPageViewTime = System.currentTimeMillis()
            QuickListenTraceUtil.trace67645(mTabId, mTabName,
                getHomePageControl()?.getFrom() ?: "", this)
        }
        visible = true
    }

    private fun onPageExit() {
        if (visible) {
            if (mPageViewTime > 0) {
                QuickListenTraceUtil.trace69385(mTabName, System.currentTimeMillis() - mPageViewTime)
                mPageViewTime = 0
            }

            HandlerManager.removeCallbacks(mCheckSwipeHintRunnable)

            QuickListenTraceUtil.trace67646(mTabId, mTabName,
                getHomePageControl()?.getFrom() ?: "", this)
        }
        visible = false
    }

    private fun setTabTheme(isBlue: Boolean) {
        getHomePageControl()?.changeTabTheme(isBlue)

        if (mTabType == ELEMENT_TYPE_NEW_USER) {
            mTitleBar.setBackgroundResource(R.color.host_transparent)
            return
        }

        if (BaseFragmentActivity.sIsDarkMode || isBlue) {
            if (isBlue) {
                mTitleBar.setBackgroundResource(R.color.host_color_1b4b9f)
            } else {
                mTitleBar.setBackgroundResource(R.color.host_color_222426)
            }
        } else {
            mTitleBar.setBackgroundResource(R.color.host_color_f0f1f3)
        }
    }

    override fun onPageLoadingCompleted(loadCompleteType: LoadCompleteType?) {
        if (loadCompleteType == LoadCompleteType.OK) {
            if (mTabType != ELEMENT_TYPE_NEW_USER) {
                mRootLay.setBackgroundResource(R.color.host_color_d3d4d4_323232)
            }

            if (mTabType == ELEMENT_TYPE_NEW_USER) {
                HandlerManager.postOnUIThreadDelay4Kt(5000, mCheckSwipeHintRunnable)
            }
        } else {
            setTabTheme(false)
        }
        super.onPageLoadingCompleted(loadCompleteType)
    }

    fun saveVideoControl(position: Int, mediaControl: IMediaPlayerControl) {
        mVideoControlMap[position] = WeakReference(mediaControl)
        if (position == mMainViewPager.currentItem) {
            QuickListenVideoUtil.setVideoControl(mVideoControlMap.getOrDefault(position, null)?.get())
        }
    }


    private val swipeHintOffset = (-52).dp

    private fun reflectEndFakeDrag() {
        runCatching {
            val mScrollEventAdapterField: Field? = FieldUtils.getField(ViewPager2::class.java, "mScrollEventAdapter")
            val mScrollEventAdapterObj: Any? = mScrollEventAdapterField?.get(mMainViewPager)
            MethodUtils.invokeMethod(mScrollEventAdapterObj, "notifyEndFakeDrag")
        }
    }

    private fun hideSwipeTipsContainer() {
        if (!swipeTipsContainer.isShown) {
            return
        }

        val animator = ValueAnimator.ofFloat(0f, swipeHintOffset.toFloat().absoluteValue)
        animator.setDuration(250)
        animator.interpolator = DecelerateInterpolator()
        ViewStatusUtil.setVisible(View.VISIBLE, swipeTipsContainer)
        val currentY = swipeTipsContainer.translationY
        animator.addUpdateListener { a: ValueAnimator ->
            val animatedValue = a.animatedValue as Float
            swipeTipsContainer.translationY = animatedValue + currentY
        }
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator?) {
                ViewStatusUtil.setVisible(View.GONE, swipeTipsContainer)
            }

            override fun onAnimationCancel(animation: Animator?) {
                ViewStatusUtil.setVisible(View.GONE, swipeTipsContainer)
            }
        })
        animator.start()
    }

    private fun showSwipeHint(content: String, isFirstGuide: Boolean) {
        if (mMainViewPager.beginFakeDrag()) {
            val recyclerView = mMainViewPager.children.first() as RecyclerView
            val fakeScrollListener = object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    hideSwipeTipsContainer()
                    recyclerView.removeOnScrollListener(this)
                }

                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {

                }
            }
            recyclerView.addOnScrollListener(fakeScrollListener)

            val animator = ValueAnimator.ofFloat(0f, swipeHintOffset.toFloat())
            animator.setDuration(500)
            animator.interpolator = DecelerateInterpolator()

            val lastPos = floatArrayOf(0f)

            ViewStatusUtil.setVisible(View.VISIBLE, swipeTipsContainer)
            val offsetAbs = swipeHintOffset.toFloat().absoluteValue
            swipeTipsContainer.translationY = offsetAbs
            swipeTipsText.text = content
            animator.addUpdateListener { a: ValueAnimator ->
                val current = a.animatedValue as Float
                val delta = current - lastPos[0]
                mMainViewPager.fakeDragBy(delta)
                lastPos[0] = current

                swipeTipsContainer.translationY = offsetAbs + current
            }

            animator.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator?) {
                    reflectEndFakeDrag()
                }

                override fun onAnimationCancel(animation: Animator?) {
                    reflectEndFakeDrag()
                }
            })
            animator.start()

            MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInHost.KEY_QUICK_LISTEN_SHOW_SWIPE + isFirstGuide, true)
        }
    }

    private fun resetSwipeHint() {
        mMainViewPager.beginFakeDrag()
        mMainViewPager.endFakeDrag()
    }

    fun checkCanShowSwipeHint(content: String, isFirstGuide: Boolean) {
        if (ToolUtil.getDebugSystemProperty("debug.quick_listen.force_show_swipe", "-1") != "1" && isFirstGuide) {
            if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.KEY_QUICK_LISTEN_SHOW_SWIPE + isFirstGuide)) {
                return
            }
        }

        Logger.log("QuickListenItemPageFragment : checkCanShowSwipeHint  $isFirstGuide  ${getCurrentItem()}   $mPageState")
        
        if ((isFirstGuide && getCurrentItem() != 0) || mPageState != ViewPager2.SCROLL_STATE_IDLE) {
            return
        }

        HandlerManager.removeCallbacks(mCheckSwipeHintRunnable)
        HandlerManager.removeCallbacks(mShowSwipeHintRunnable)
        showSwipeHint(content, isFirstGuide)

        if (isFirstGuide) {
            HandlerManager.postOnUIThreadDelay4Kt(4000, mShowSwipeHintRunnable)
        }
    }

    private val mShowSwipeHintRunnable = Runnable {
        resetSwipeHint()
    }

    private val mCheckSwipeHintRunnable = Runnable {
        checkCanShowSwipeHint("上滑收听更多精彩内容", true)
    }

    override fun enableAdapt(): Boolean {
        return false
    }
}