package com.ximalaya.ting.android.main.quicklistenmodule.iinterface

import com.ximalaya.ting.android.host.data.model.QuickListenModel
import com.ximalaya.ting.android.host.manager.rn.VerticalViewPager
import com.ximalaya.ting.android.main.quicklistenmodule.view.CustomSwipeRefreshLayout
import com.ximalaya.ting.android.xmplaysdk.IMediaPlayerControl
import org.json.JSONArray
import org.json.JSONObject

/**
 * Created by nali on 2025/7/9.
 * <AUTHOR>
 */
interface IQuickListenItemPageControl {
    fun notifyCurShowChange()

    fun getRefreshView(): CustomSwipeRefreshLayout

    fun getQuickListenListData(): List<QuickListenModel>

    fun getCurrentItem(): Int

    fun setCurrentItem(index: Int)

    fun getTabId(): Long

    fun insertDataList(list: List<JSONObject>?, tabId: Long, lastRefId: Long)

    fun notifyAdjacencyItemChange()

    fun getCurModel(): JSONObject?

    fun getCurrQuickListenModel(): QuickListenModel?

    fun updateDataList(list: List<JSONObject>?, index: Int, tabId: Long, needPlay: Boolean, willPlayRefId: Long)

    fun getRequestId(tabId: Long): Int

    fun isRnPage(): Boolean

    fun isPageInited(): Boolean

    fun playTrack(cardPosition: Int)

    fun playTrack(tabId: Long, trackId: Long)

    fun setItemPosition(cardPosition: Int)

    fun setViewPager(viewPager: VerticalViewPager?, tabId: Long, jsonArray: JSONArray?)

    fun getVideoControl(index: Int): IMediaPlayerControl?
}