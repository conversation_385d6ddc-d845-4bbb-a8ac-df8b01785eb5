package com.ximalaya.ting.android.main.roleModule.fragment

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.view.TitleBar
import com.ximalaya.ting.android.main.request.MainStableUrlConstants
import com.ximalaya.ting.android.main.stable.R;
import com.ximalaya.ting.android.main.roleModule.adapter.RoleGifsRealTimeRankAdapter
import com.ximalaya.ting.android.main.roleModule.manager.trace54704
import com.ximalaya.ting.android.main.roleModule.manager.trace55933
import com.ximalaya.ting.android.main.roleModule.manager.trace55934
import com.ximalaya.ting.android.main.roleModule.model.RoleAnchorRankModel
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack

/**
 * Created by nali on 2023/6/7.
 * 角色守护实时榜
 * <AUTHOR>
 */
class RoleGifsRealTimeRankFragment : BaseFragment2(), IRefreshLoadMoreListener {

    override fun getPageLogicName(): String {
        return this::class.java.simpleName
    }

    private val mListview: RefreshLoadMoreListView by lazy { findViewById<RefreshLoadMoreListView>(R.id.main_listview) }
    private var mAdapter: RoleGifsRealTimeRankAdapter? = null

    override fun initUi(savedInstanceState: Bundle?) {
        setTitle("守护实时榜")
        mListview.setOnRefreshLoadMoreListener(this)
        mAdapter = RoleGifsRealTimeRankAdapter(context, null)
        mListview.setAdapter(mAdapter)
    }

    companion object {
        fun getInstance(): RoleGifsRealTimeRankFragment {
            return RoleGifsRealTimeRankFragment()
        }
    }

    override fun setTitleBar(titleBar: TitleBar?) {
        super.setTitleBar(titleBar)

        val secretaryAction = TitleBar.ActionType(
            "save", TitleBar.RIGHT, R.string.main_explain, 0, com.ximalaya.ting.android.host.R.color.host_color_333333_dcdcdc, TextView::class.java
        )
        secretaryAction.setFontSize(15)
        titleBar?.addAction(secretaryAction) {
            trace54704("守护实时榜", null, null, "说明")

            startFragment(NativeHybridFragment::class.java,
                Bundle().apply {
                    putString(BundleKeyConstants.KEY_EXTRA_URL, "https://pages.ximalaya.com/mkt/act/2a0c187aea8a7ecc")
                    putBoolean(NativeHybridFragment.SHOW_SHARE_BTN, false)
            }, null)
        }
        titleBar?.update()
    }

    override fun loadData() {
        onPageLoadingCompleted(LoadCompleteType.LOADING)

        CommonRequestM.getData(MainStableUrlConstants.getInstanse().queryAnchorRank(), null,
            object : TypeToken<ArrayList<RoleAnchorRankModel>>() {}.type,
            object : IDataCallBack<ArrayList<RoleAnchorRankModel>> {
                override fun onSuccess(data: ArrayList<RoleAnchorRankModel>?) {
                    if (!canUpdateUi()) {
                        return
                    }

                    if (data.isNullOrEmpty()) {
                        onPageLoadingCompleted(LoadCompleteType.NOCONTENT)
                        return
                    }

                    mAdapter?.listData = data
                    mAdapter?.notifyDataSetChanged()
                    onPageLoadingCompleted(LoadCompleteType.OK)
                    mListview.onRefreshComplete(false)
                    mListview.setFootViewText("- 喜爱没有结尾，快去守护喜爱的主播吧 -")
                    mListview.setFooterTextSize(11)
                    mListview.setFooterTextViewColor(ContextCompat.getColor(ToolUtil.getCtx(), R.color.host_color_8f8f8f_66666b))
                }

                override fun onError(code: Int, message: String?) {
                    onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
                }
            }
        )
    }

    override fun onRefresh() {
        super.onRefresh()
        loadData()
    }

    override fun onMore() {
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_role_real_time_page
    }

    override fun getTitleBarResourceId(): Int {
        return R.id.main_title_bar_1
    }

    override fun onMyResume() {
        super.onMyResume()
        trace55933("守护实时榜")
    }

    override fun onPause() {
        super.onPause()
        trace55934("守护实时榜")
    }

    override fun getNoContentView(): View {
        return layoutInflater.inflate(R.layout.main_layout_mailbox_no_content, null)
    }

    override fun isShowPlayButton(): Boolean {
        return false
    }
}