<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:ptr="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/main_radio_header"
        layout="@layout/main_view_accessibility_radio_list_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
         />

    <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
        android:id="@+id/main_lv_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:divider="@null"
        android:dividerHeight="0dp"
        android:focusableInTouchMode="false"
        android:listSelector="@color/host_transparent"
        android:overScrollMode="always"
        android:paddingBottom="200dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_radio_header"
        ptr:ptrDrawable="@drawable/host_ic_loading_circle"
        ptr:ptrHeaderTextColor="@color/radio_text_medium"
        ptr:ptrShowIndicator="false" />

</androidx.constraintlayout.widget.ConstraintLayout>