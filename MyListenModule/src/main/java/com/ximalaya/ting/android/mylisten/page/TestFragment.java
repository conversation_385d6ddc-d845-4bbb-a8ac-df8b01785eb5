package com.ximalaya.ting.android.mylisten.page;

import android.os.Bundle;
import android.widget.TextView;

import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.mylisten.R;

/**
 * Created by ZhuPeipei on 2020/12/31 13:36.
 */
public class TestFragment extends IMainFunctionAction.AbstractListenNoteFragment {
    @Override
    protected String getPageLogicName() {
        return "TestFragment";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        TextView tv = findViewById(R.id.listen_tv_info);
        tv.setText("我是来自我听的页面");
    }

    @Override
    protected void loadData() {

    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.listen_fra_fake_mylisten_bundle;
    }

    @Override
    public void onRefresh() {

    }

    @Override
    public void clickRefresh() {

    }
}
