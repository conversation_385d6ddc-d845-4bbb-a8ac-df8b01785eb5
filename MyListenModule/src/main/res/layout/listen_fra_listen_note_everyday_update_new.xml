<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:rll="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/listen_ll_fra_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/listen_listenNoteList_title"
        android:layout_width="0dp"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@drawable/host_gray_underline_white_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/listen_listenNoteList_title">

        <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
            android:id="@+id/listen_listenNote_listView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:divider="@null"
            android:dividerHeight="0dp"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:listSelector="@color/host_transparent"
            android:overScrollMode="always"
            android:paddingBottom="150dp"
            android:scrollbars="none"
            rll:ptrDrawable="@drawable/host_ic_loading_circle"
            rll:ptrHeaderTextColor="@color/host_color_666666_888888"
            rll:ptrShowIndicator="false" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>