package com.ximalaya.android.nativecomponentsdk.creator.customAlbum.creativeTeam

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.android.componentelementarysdk.model.module.defaultModel.customAlbum.CreativeTeamModuleModel
import com.ximalaya.android.componentelementarysdk.model.pageInfo.PageInfo
import com.ximalaya.android.componentelementarysdk.util.SdkBaseUtil
import com.ximalaya.android.componentelementarysdk.util.SdkProxyFunctionUtil
import com.ximalaya.android.nativecomponentsdk.R
import com.ximalaya.android.nativecomponentsdk.creator.base.BaseNativeModuleViewHelper
import com.ximalaya.android.nativecomponentsdk.creator.trainingCamp.course.CourseModuleViewHelper
import com.ximalaya.android.nativecomponentsdk.creator.trainingCamp.course.CourseViewHolder

class CreativeTeamItemAdapter(private val data: CreativeTeamModuleModel, private val helper: CreativeTeamModuleViewHelper?): RecyclerView.Adapter<CreativeTeamItemAdapter.ViewHolder>(), View.OnClickListener {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view: View = LayoutInflater.from(parent.context).inflate(R.layout.universal_n_view_custom_album_creative_team_item, null)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        SdkBaseUtil.Common.safelyGetItemFromList(data.creativeTeams, position)?.let { item ->
            SdkProxyFunctionUtil.displayImage(holder.portrait, item.logoPic, com.ximalaya.android.componentelementarysdk.R.drawable.universal_ic_default_portrait)
            SdkBaseUtil.ViewUtil.setTextString(item.nickName, holder.title)
            SdkBaseUtil.ViewUtil.setTextString(item.role, holder.subTitle)

            SdkBaseUtil.ViewUtil.setTagToView(position, com.ximalaya.android.componentelementarysdk.R.id.universal_tag_position, holder.itemView)
            SdkBaseUtil.ViewUtil.setClickListener(item, com.ximalaya.android.componentelementarysdk.R.id.universal_tag_click_model, this, holder.itemView)

            val extraInfo: Pair<Int, Long> = Pair((1 + position), item.userId)
            helper?.markPointFunction(CreativeTeamModuleViewHelper.TYPE_SHOW_TEAM_MEMBER_ITEM, extraInfo)
        }
    }

    override fun getItemCount(): Int {
        return data.creativeTeams?.size?: 0
    }

    override fun onClick(v: View?) {
        if (!SdkBaseUtil.Common.onClick(v)) {
            return
        }
        val tag: Any? = v?.getTag(com.ximalaya.android.componentelementarysdk.R.id.universal_tag_click_model)
        if (tag is CreativeTeamModuleModel.CreativeTeamItem) {
            val positionTag: Any? = v?.getTag(com.ximalaya.android.componentelementarysdk.R.id.universal_tag_position)
            if (positionTag is Int) {
                val extraInfo: Pair<Int, Long> = Pair((1 + positionTag), tag.userId)
                helper?.markPointFunction(CreativeTeamModuleViewHelper.TYPE_CLICK_TEAM_MEMBER_ITEM, extraInfo)
            }
            SdkProxyFunctionUtil.jumpUrl(tag.mainPageUrl)
        }
    }

    class ViewHolder(wholeView: View): RecyclerView.ViewHolder(wholeView) {
        val portrait: ImageView? = itemView.findViewById(R.id.universal_id_anchor_portrait)
        val title: TextView? = itemView.findViewById(R.id.universal_id_title)
        val subTitle: TextView? = itemView.findViewById(R.id.universal_id_sub_title)
    }
}