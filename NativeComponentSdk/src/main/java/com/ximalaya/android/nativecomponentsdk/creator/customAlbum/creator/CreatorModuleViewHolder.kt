package com.ximalaya.android.nativecomponentsdk.creator.customAlbum.creator

import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.android.nativecomponentsdk.R
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.view.recyclerview.LinearItemDecoration

/**
 * Created by <PERSON><PERSON>u on 2023/12/19.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class CreatorModuleViewHolder(val rootView: View) {
    companion object {
        val VIEW_HOLDER_TAG = R.id.universal_n_id_view_holder
    }

    private val rvCreators: RecyclerView? = rootView.findViewById(R.id.universal_id_rv_creators)
    val groupCreators: Group? = rootView.findViewById(R.id.universal_id_group_creators)
    val groupCharacter: Group? = rootView.findViewById(R.id.universal_id_group_role)
    val tvMoreCharacter: TextView? = rootView.findViewById(R.id.universal_id_more_role)
    val rvCharacter: RecyclerView? = rootView.findViewById(R.id.universal_id_rv_role)
    val divider: View? = rootView.findViewById(R.id.universal_id_divider)
    val creatorsAdapter = CreatorModuleCreatorAdapter()
    val charactersAdapter = CreatorModuleCharacterAdapter()

    init {
        initRvCreators()
        initRvRole()
    }

    private fun initRvCreators() {
        rvCreators?.let {
            it.layoutManager = LinearLayoutManager(rootView.context, RecyclerView.HORIZONTAL, false)
            it.addItemDecoration(LinearItemDecoration(21.dp, 16.dp))
            it.adapter = creatorsAdapter
        }
    }

    private fun initRvRole() {
        rvCharacter?.let {
            it.addItemDecoration(LinearItemDecoration(12.dp, 16.dp))
            it.adapter = charactersAdapter
        }
    }
}