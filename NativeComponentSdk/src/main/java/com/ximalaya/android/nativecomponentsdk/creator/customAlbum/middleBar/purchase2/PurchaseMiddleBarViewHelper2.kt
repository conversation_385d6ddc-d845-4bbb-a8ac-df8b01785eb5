package com.ximalaya.android.nativecomponentsdk.creator.customAlbum.middleBar.purchase2

import android.graphics.Color
import android.os.CountDownTimer
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import com.ximalaya.android.componentelementarysdk.constant.ModuleConstant
import com.ximalaya.android.componentelementarysdk.model.module.defaultModel.customAlbum.middleBar.PayableInfo
import com.ximalaya.android.componentelementarysdk.model.module.defaultModel.customAlbum.middleBar.PayableInfo.NewFreeCrowdPayableGuideModel
import com.ximalaya.android.nativecomponentsdk.R
import com.ximalaya.android.nativecomponentsdk.creator.base.BaseNativeModuleViewHelper
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import java.util.regex.Pattern

class PurchaseMiddleBarViewHelper2(dataProvider: BaseDataProvider): BaseNativeModuleViewHelper<BaseNativeModuleViewHelper.BaseDataProvider>(dataProvider) {

    companion object {
        const val TYPE_SHOW_COMBINE_SUB_AREA = 1
        const val TYPE_CLICK_COMBINE_SUB_AREA = 2
        const val TYPE_SHOW_MAIN_AREA = 3
        const val TYPE_CLICK_MAIN_AREA = 4
        // const val TYPE_CLICK_PURCHASE_DIALOG_BTN = 5
        // const val TYPE_SHOW_PURCHASE_DIALOG_BTN = 6
        const val TYPE_SHOW_SEE_DETAIL_AREA = 7
        const val TYPE_CLICK_SEE_DETAIL_AREA = 8
        const val NUMBER_PATTERNS = "[0-9]+(\\.[0-9]{1,3})?"

    }

    private var countDown: CountDownTimer? = null
    fun resetCountDown() {
        countDown?.cancel()
    }

    fun setCountDown(countDown: CountDownTimer?) {
        this.countDown = countDown
    }

    fun getTitleColor(type: String?): String {
        return when(type) {
            PayableInfo.BasePayableGuideModel.TYPE_VIP -> if(BaseFragmentActivity.sIsDarkMode) "#FFCBB1" else "#461717"
            PayableInfo.BasePayableGuideModel.TYPE_XIMI -> "#4E46C8"
            PayableInfo.BasePayableGuideModel.TYPE_CHILDREN_VIP -> "#732F06"
            PayableInfo.BasePayableGuideModel.TYPE_MASTER_CLASS_VIP -> "#333949"
            PayableInfo.BasePayableGuideModel.TYPE_PLATINUM_VIP -> if(BaseFragmentActivity.sIsDarkMode) "#F1D2A6" else "#794A16"
            else -> "#732F06"
        }
    }

    fun getBtnBgColor(type: String?): String? {
        return when(type) {
            PayableInfo.BasePayableGuideModel.TYPE_VIP -> "#FFCBB1"
            PayableInfo.BasePayableGuideModel.TYPE_XIMI -> "#6F67FF"
            PayableInfo.BasePayableGuideModel.TYPE_CHILDREN_VIP -> "#FFAD4A"
            PayableInfo.BasePayableGuideModel.TYPE_MASTER_CLASS_VIP -> "#333949"
            PayableInfo.BasePayableGuideModel.TYPE_PLATINUM_VIP -> if(BaseFragmentActivity.sIsDarkMode) "#1fE8C695" else "#FFE2B7"
            else -> "#F09068"
        }
    }
    fun getBtnTextColor(type: String?): String {
        return when(type) {
            PayableInfo.BasePayableGuideModel.TYPE_VIP -> "#461717"
            PayableInfo.BasePayableGuideModel.TYPE_XIMI -> "#FFFFFF"
            PayableInfo.BasePayableGuideModel.TYPE_CHILDREN_VIP -> "#FFFFFF"
            PayableInfo.BasePayableGuideModel.TYPE_MASTER_CLASS_VIP -> "#FFFFFF"
            PayableInfo.BasePayableGuideModel.TYPE_PLATINUM_VIP -> if(BaseFragmentActivity.sIsDarkMode) "#F3D7AE" else "#794A16"
            else -> "#F09068"
        }
    }

    fun getViewBgColor1(type: String?): String? {
        return when(type) {
            PayableInfo.BasePayableGuideModel.TYPE_VIP -> if (BaseFragmentActivity.sIsDarkMode) "#1aFC8859" else "#FFF3ED"
            PayableInfo.BasePayableGuideModel.TYPE_XIMI -> "#F4F6FF"
            PayableInfo.BasePayableGuideModel.TYPE_CHILDREN_VIP -> "#FFF9E7"
            PayableInfo.BasePayableGuideModel.TYPE_MASTER_CLASS_VIP -> "#EDF1FD"
            PayableInfo.BasePayableGuideModel.TYPE_PLATINUM_VIP -> if(BaseFragmentActivity.sIsDarkMode) "#1fE8C695" else "#FFF7EB"
            else -> "#FFF2EA"
        }
    }

    fun getViewBgColor2(type: String?): String? {
        return when(type) {
            PayableInfo.BasePayableGuideModel.TYPE_VIP -> if (BaseFragmentActivity.sIsDarkMode) "#1aFC8859" else "#FFF3ED"
            PayableInfo.BasePayableGuideModel.TYPE_XIMI -> "#F4F6FF"
            PayableInfo.BasePayableGuideModel.TYPE_CHILDREN_VIP -> "#FFF9E7"
            PayableInfo.BasePayableGuideModel.TYPE_MASTER_CLASS_VIP -> "#EDF1FD"
            PayableInfo.BasePayableGuideModel.TYPE_PLATINUM_VIP -> if(BaseFragmentActivity.sIsDarkMode) "#1fE8C695" else "#FFF7EB"
            else -> "#FFE9DC"
        }
    }

    fun getViewSecondBgColor1(type: String?): String? {
        return when(type) {
            PayableInfo.BasePayableGuideModel.TYPE_VIP -> "#FFE2D1"
            PayableInfo.BasePayableGuideModel.TYPE_XIMI -> "#E7EEFF"
            PayableInfo.BasePayableGuideModel.TYPE_CHILDREN_VIP -> "#FEF0CF"
            PayableInfo.BasePayableGuideModel.TYPE_MASTER_CLASS_VIP -> "#DBE4F3"
            else -> "#FFE2D1"
        }
    }

    fun getViewSecondBgColor2(type: String?): String? {
        return when(type) {
            PayableInfo.BasePayableGuideModel.TYPE_VIP -> "#FFE8DA"
            PayableInfo.BasePayableGuideModel.TYPE_XIMI -> "#E7EEFF"
            PayableInfo.BasePayableGuideModel.TYPE_CHILDREN_VIP -> "#FEF0CF"
            PayableInfo.BasePayableGuideModel.TYPE_MASTER_CLASS_VIP -> "#DBE4F3"
            else -> "#FFE8DA"
        }
    }

    fun getBgIconRes(type: String?): Int {
        return when(type) {
            PayableInfo.BasePayableGuideModel.TYPE_VIP -> R.drawable.universal_ic_vip_logo_2
            PayableInfo.BasePayableGuideModel.TYPE_XIMI -> R.drawable.universal_ic_ximi_logo
            PayableInfo.BasePayableGuideModel.TYPE_CHILDREN_VIP -> R.drawable.universal_ic_children_vip_logo
            PayableInfo.BasePayableGuideModel.TYPE_MASTER_CLASS_VIP -> R.drawable.universal_ic_master_class_vip_logo
            else -> 0
        }
    }

    fun getCountDownTextColor(type: String?): String {
        return when(type) {
            PayableInfo.BasePayableGuideModel.TYPE_VIP -> "#F09068"
            PayableInfo.BasePayableGuideModel.TYPE_XIMI -> "#6F67FF"
            PayableInfo.BasePayableGuideModel.TYPE_CHILDREN_VIP -> "#FFA400"
            PayableInfo.BasePayableGuideModel.TYPE_MASTER_CLASS_VIP -> "#766DA1"
            PayableInfo.BasePayableGuideModel.TYPE_PLATINUM_VIP -> if(BaseFragmentActivity.sIsDarkMode) "#F1D2A6" else "#794A16"
            else -> "#F09068"
        }
    }

    fun getAlbumId(): Long {
        return dataProvider.getPageInfo()?.spuId?: 0
    }

    override fun markPointFunction(type: Int, extraInfo: Any?) {
        val pageTitle: String = if (ModuleConstant.PageNameConstant.PAGE_NAME_PURCHASED == dataProvider.getPageInfo()?.pageName) "afterSale" else "beforeSale"
        when(type) {
            TYPE_SHOW_COMBINE_SUB_AREA -> {
                val spuId: String = ((extraInfo as? Array<*>)?.get(0) as? String)?: ""
                val code: String = ((extraInfo as? Array<*>)?.get(1) as? String)?: ""
                val categoryId: Long = ((extraInfo as? Array<*>)?.get(2) as? Long)?: 0

                // 付费专辑详情页（新）-原价购买区  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(48446)
                    .setServiceId("slipPage")
                    .put("currPage", "payAlbumDetail(New)")
                    .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId?:0}")
                    .put("pageTitle", "beforeSale")
                    .put("spuId", spuId)
                    .put("code", code)
                    .put("categoryId", "$categoryId")
                    .put(XmRequestIdManager.CONT_ID, "${dataProvider.getPageInfo()?.spuId?:0}")
                    .put(XmRequestIdManager.CONT_TYPE, "payAlbumDetail(New)")
                    .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)
                    .createTrace()
            }
            TYPE_CLICK_COMBINE_SUB_AREA -> {
                val spuId: String = ((extraInfo as? Array<*>)?.get(0) as? String)?: ""
                val code: String = ((extraInfo as? Array<*>)?.get(1) as? String)?: ""
                val categoryId: Long = ((extraInfo as? Array<*>)?.get(2) as? Long)?: 0

                // 付费专辑详情页（新）-原价购买区  点击事件
                XMTraceApi.Trace()
                    .click(48445) // 用户点击时上报
                    .put("currPage", "payAlbumDetail(New)")
                    .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId?:0}")
                    .put("pageTitle", "beforeSale")
                    .put("spuId", spuId)
                    .put("code", code)
                    .put("categoryId", "$categoryId")
                    .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)
                    .createTrace()
            }
            TYPE_SHOW_MAIN_AREA -> {
                var spuId: String = ""
                var code: String =""
                var categoryId: Long = 0
                var doubleEntrace = false
                var extraMap = emptyMap<String,String>()
                if (extraInfo is NewFreeCrowdPayableGuideModel) {
                    doubleEntrace = extraInfo.showAdEntrance
                } else if (extraInfo is Array<*>) {
                    spuId = ((extraInfo as? Array<*>)?.get(0) as? String) ?: ""
                    code = ((extraInfo as? Array<*>)?.get(1) as? String) ?: ""
                    categoryId = ((extraInfo as? Array<*>)?.get(2) as? Long) ?: 0
                } else if (extraInfo is Map<*, *>) {
                    extraMap = extraInfo as MutableMap<String, String>
                }


                // 付费专辑详情页（新）-主购买区  控件曝光
                // 付费专辑详情页（新）-主购买区  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(48444)
                    .setServiceId("slipPage")
                    .put("currPage", "payAlbumDetail(New)")
                    .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId?:0}")
                    .put("pageTitle", "beforeSale")
                    .put("spuId", spuId)
                    .put("code", code)
                    .put("categoryId", "$categoryId")
                    .put("isDoubleEntrance", "$doubleEntrace")
                    .put(extraMap)
                    .put(XmRequestIdManager.CONT_ID, "${dataProvider.getPageInfo()?.spuId?:0}")
                    .put(XmRequestIdManager.CONT_TYPE, "payAlbumDetail(New)")
                    .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)
                    .createTrace()
            }
            TYPE_CLICK_MAIN_AREA -> {
                var spuId: String = ""
                var code: String =""
                var categoryId: Long = 0
                var doubleEntrace = false
                var extraMap = emptyMap<String,String>()
                if (extraInfo is NewFreeCrowdPayableGuideModel) {
                    doubleEntrace = extraInfo.showAdEntrance
                } else if (extraInfo is Array<*>) {
                    spuId = ((extraInfo as? Array<*>)?.get(0) as? String) ?: ""
                    code = ((extraInfo as? Array<*>)?.get(1) as? String) ?: ""
                    categoryId = ((extraInfo as? Array<*>)?.get(2) as? Long) ?: 0
                } else if (extraInfo is Map<*, *>) {
                    extraMap = extraInfo as MutableMap<String, String>
                }

                // 付费专辑详情页（新）-主购买区  点击事件
                XMTraceApi.Trace()
                    .click(48443) // 用户点击时上报
                    .put("currPage", "payAlbumDetail(New)")
                    .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId?:0}")
                    .put("pageTitle", "beforeSale")
                    .put("spuId", spuId)
                    .put("code", code)
                    .put("categoryId", "$categoryId")
                    .put("isDoubleEntrance", "$doubleEntrace")
                    .put("entranceType", "会员入口")
                    .put(extraMap)
                    .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)
                    .createTrace()
            }
            /*TYPE_CLICK_PURCHASE_DIALOG_BTN -> {
                if (extraInfo is PriceInfo.LayerBehavior) {
                    // 付费专辑详情页（新）-购买浮窗-按钮  点击事件
                    XMTraceApi.Trace()
                        .click(45166) // 用户点击时上报
                        .put("currPage", "payAlbumDetail(New)")
                        .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId ?: 0}")
                        .put("Item", extraInfo.button?.buttonText?: "")
                        .put("userGroupId", extraInfo.dataAnalysis?: "")
                        .put("type", extraInfo.type?: "") // vip 表示购买会员、主播会员，purchase表示买专辑，url 表示自定义选项
                        .put("pageTitle", "beforeSale")
                        .createTrace()
                }
            }
            TYPE_SHOW_PURCHASE_DIALOG_BTN -> {
                if (extraInfo is PriceInfo.LayerBehavior) {
                    // 付费专辑详情页（新）-购买浮窗-按钮  控件曝光
                    XMTraceApi.Trace()
                        .setMetaId(45167)
                        .setServiceId("slipPage")
                        .put("currPage", "payAlbumDetail(New)")
                        .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId ?: 0}")
                        .put("userGroupId", extraInfo.dataAnalysis?: "")
                        .put("Item", extraInfo.button?.buttonText?: "")
                        .put("type", extraInfo.type?: "") // vip 表示购买会员、主播会员，purchase表示买专辑，url 表示自定义选项
                        .put("pageTitle", "beforeSale")
                        .createTrace()
                }
            }*/
            TYPE_SHOW_SEE_DETAIL_AREA -> {
                // 付费专辑详情页（新）-查看明细  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(48448)
                    .setServiceId("slipPage")
                    .put("currPage", "payAlbumDetail(New)")
                    .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId ?: 0}")
                    .put("pageTitle", "beforeSale")
                    .put(XmRequestIdManager.CONT_ID, "${dataProvider.getPageInfo()?.spuId ?: 0}")
                    .put(XmRequestIdManager.CONT_TYPE, "payAlbumDetail(New)")
                    .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)
                    .createTrace()
            }
            TYPE_CLICK_SEE_DETAIL_AREA -> {
                // 付费专辑详情页（新）-查看明细  点击事件
                XMTraceApi.Trace()
                    .click(48447) // 用户点击时上报
                    .put("currPage", "payAlbumDetail(New)")
                    .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId ?: 0}")
                    .put("pageTitle", "beforeSale")
                    .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)
                    .createTrace()
            }
        }
    }

    fun convertVipBarText(text: String?): CharSequence {
        text ?: return ""
        val pattern = Pattern.compile(NUMBER_PATTERNS)
        val matcher = pattern.matcher(text)
        var startIndex = 0
        if (matcher.find(startIndex)) {
            val spannableString: SpannableString = SpannableString(text)
            try {
                while (matcher.find(startIndex)) {
                    val group = matcher.group()
                    if (group.isNullOrEmpty()) {
                        break
                    }
                    startIndex = text.indexOf(group, startIndex, true)
                    val endIndex = startIndex + group.length
                    if (0 <= startIndex && endIndex <= text.length) {
                        spannableString.setSpan(
                            ForegroundColorSpan(Color.parseColor("#FF4444")),
                            startIndex,
                            endIndex,
                            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                        )
                    }
                    startIndex = endIndex
                }
            } catch (e: Exception) {

            }
            return spannableString
        } else {
            return text
        }
    }
}