package com.ximalaya.android.nativecomponentsdk.creator.customAlbum.relatedContent

import com.ximalaya.android.componentelementarysdk.constant.ModuleConstant
import com.ximalaya.android.componentelementarysdk.model.module.defaultModel.customAlbum.RelativeContentModuleModel
import com.ximalaya.android.nativecomponentsdk.creator.base.BaseNativeModuleViewHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

class RelativeContentModuleViewHelper(dataProvider: BaseDataProvider): BaseNativeModuleViewHelper<BaseNativeModuleViewHelper.BaseDataProvider>(dataProvider) {
    companion object {
        const val TYPE_SHOW_LISTEN_LIST_AREA = 1
        const val TYPE_CLICK_LISTEN_LIST_AREA = 2
        const val TYPE_SHOW_RELATIVE_LIST_ITEM = 3
        const val TYPE_CLICK_RELATIVE_LIST_ITEM = 4

    }

    override fun markPointFunction(type: Int, extraInfo: Any?) {
        val pageTitle: String = if (ModuleConstant.PageNameConstant.PAGE_NAME_PURCHASED == dataProvider.getPageInfo()?.pageName) "afterSale" else "beforeSale"
        when(type) {
            TYPE_SHOW_LISTEN_LIST_AREA -> {
                // 付费专辑详情页（新）-关联听单  控件曝光
                XMTraceApi.Trace()
                    .setMetaId(47624)
                    .setServiceId("slipPage")
                    .put("currPage", "payAlbumDetail(New)")
                    .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId ?: 0}")
                    .put("pageTitle", pageTitle)
                    .put(XmRequestIdManager.CONT_ID, "${dataProvider.getPageInfo()?.spuId ?: 0}")
                    .put(XmRequestIdManager.CONT_TYPE, "payAlbumDetail(New)")
                    .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)
                    .createTrace()
            }

            TYPE_CLICK_LISTEN_LIST_AREA -> {
                if (extraInfo is Long) {
                    // 付费专辑详情页（新）-关联听单  点击事件
                    XMTraceApi.Trace()
                        .click(47623) // 用户点击时上报
                        .put("currPage", "payAlbumDetail(New)")
                        .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId ?: 0}")
                        .put("pageTitle", pageTitle)
                        .put("specialId", "$extraInfo")
                        .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)
                        .createTrace()
                }
            }

            TYPE_SHOW_RELATIVE_LIST_ITEM -> {
                if (extraInfo is Array<*>) {
                    val position: Int = (extraInfo.get(0) as? Int)?: return
                    val item: RelativeContentModuleModel.ContentItem = (extraInfo.get(1) as? RelativeContentModuleModel.ContentItem)?: return
                    // 付费专辑详情页（新）-作品关联模块  控件曝光
                    val trace: XMTraceApi.Trace = XMTraceApi.Trace()
                    trace.setMetaId(47622)
                        .setServiceId("slipPage")
                        .put("currPage", "payAlbumDetail(New)")
                        .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId ?: 0}")
                        .put("pageTitle", pageTitle)
                        .put("tagTitle", item.tag?: "") // 根据实际对应模块标签名传
                        .put("specialId", "-1")
                        .put("positionNew", "$position") // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                        .put(XmRequestIdManager.CONT_ID, "${dataProvider.getPageInfo()?.spuId ?: 0}")
                        .put(XmRequestIdManager.CONT_TYPE, "payAlbumDetail(New)")
                        .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)
                    when (item.contentType) {
                        RelativeContentModuleModel.ContentItem.CONTENT_TYPE_ALBUM -> {
                            trace.put("productId", "-1") // 若有则传，若无则传-1
                                .put("trackId", "-1") // 若有则传，若无则传-1
                                .put("albumId", "${item.contentId}") // 若有则传，若无则传-1
                        }
                        RelativeContentModuleModel.ContentItem.CONTENT_TYPE_TRACK -> {
                            trace.put("productId", "-1") // 若有则传，若无则传-1
                                .put("trackId", "${item.contentId}") // 若有则传，若无则传-1
                                .put("albumId", "-1") // 若有则传，若无则传-1
                        }
                        RelativeContentModuleModel.ContentItem.CONTENT_TYPE_PRODUCT -> {
                            trace.put("productId", "${item.contentId}") // 若有则传，若无则传-1
                                .put("trackId", "-1") // 若有则传，若无则传-1
                                .put("albumId", "-1") // 若有则传，若无则传-1
                        }
                        else -> {}
                    }
                    trace.createTrace()
                }
            }

            TYPE_CLICK_RELATIVE_LIST_ITEM -> {
                if (extraInfo is Array<*>) {
                    val position: Int = (extraInfo.get(0) as? Int)?: return
                    val item: RelativeContentModuleModel.ContentItem = (extraInfo.get(1) as? RelativeContentModuleModel.ContentItem)?: return
                    // 付费专辑详情页（新）-作品关联模块  控件曝光
                    val trace: XMTraceApi.Trace = XMTraceApi.Trace()
                    // 付费专辑详情页（新）-作品关联模块  点击事件
                    // 付费专辑详情页（新）-作品关联模块  点击事件
                    trace.click(47621) // 用户点击时上报
                        .put("currPage", "payAlbumDetail(New)")
                        .put("currAlbumId", "${dataProvider.getPageInfo()?.spuId ?: 0}")
                        .put("pageTitle", pageTitle)
                        .put("positionNew", "$position") // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                        .put("tagTitle", item.tag?: "") // 根据实际对应模块标签名传
                        .put(XmRequestIdManager.XM_REQUEST_ID, dataProvider.getPageInfo()?.requestId)

                    when (item.contentType) {
                        RelativeContentModuleModel.ContentItem.CONTENT_TYPE_ALBUM -> {
                            trace.put("productId", "-1") // 若有则传，若无则传-1
                                .put("trackId", "-1") // 若有则传，若无则传-1
                                .put("albumId", "${item.contentId}") // 若有则传，若无则传-1
                        }
                        RelativeContentModuleModel.ContentItem.CONTENT_TYPE_TRACK -> {
                            trace.put("productId", "-1") // 若有则传，若无则传-1
                                .put("trackId", "${item.contentId}") // 若有则传，若无则传-1
                                .put("albumId", "-1") // 若有则传，若无则传-1
                        }
                        RelativeContentModuleModel.ContentItem.CONTENT_TYPE_PRODUCT -> {
                            trace.put("productId", "${item.contentId}") // 若有则传，若无则传-1
                                .put("trackId", "-1") // 若有则传，若无则传-1
                                .put("albumId", "-1") // 若有则传，若无则传-1
                        }
                        else -> {}
                    }
                    trace.createTrace()
                }
            }
        }
    }
}