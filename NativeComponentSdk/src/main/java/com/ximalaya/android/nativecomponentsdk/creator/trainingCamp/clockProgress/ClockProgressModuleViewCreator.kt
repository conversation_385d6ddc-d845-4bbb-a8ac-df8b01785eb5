package com.ximalaya.android.nativecomponentsdk.creator.trainingCamp.clockProgress

import android.view.View
import com.ximalaya.android.componentelementarysdk.constant.ModuleConstant
import com.ximalaya.android.componentelementarysdk.model.config.BaseConfigModel
import com.ximalaya.android.componentelementarysdk.model.module.BaseModuleModel
import com.ximalaya.android.componentelementarysdk.model.module.defaultModel.trainingCamp.ClockProgressModuleModel
import com.ximalaya.android.componentelementarysdk.model.pageInfo.PageInfo
import com.ximalaya.android.componentelementarysdk.util.SdkBaseUtil
import com.ximalaya.android.componentelementarysdk.util.SdkProxyFunctionUtil
import com.ximalaya.android.nativecomponentsdk.R
import com.ximalaya.android.nativecomponentsdk.creator.base.BaseNativeModuleViewCreator
import com.ximalaya.android.nativecomponentsdk.creator.base.BaseNativeModuleViewHelper

class ClockProgressModuleViewCreator: BaseNativeModuleViewCreator(), View.OnClickListener {

    companion object {
        const val NEWS_TRAINING_CAMP_CLOCK_PROGRESS_INFO: String = "NEWS_TRAINING_CAMP_CLOCK_PROGRESS_INFO"
    }

    private val dataProvider: ClockProgressDataProvider = ClockProgressDataProvider(pageInfo)

    override fun getModuleType(): String {
        return ModuleConstant.Type.TYPE_CLOCK_PROGRESS
    }

    override fun getViewLayoutRes(): Int {
        return R.layout.universal_n_module_training_camp_clock_progress
    }

    override fun checkIsValidData(pageInfo: PageInfo, baseConfigModuleModel: BaseConfigModel?, baseDataModuleModel: BaseModuleModel?): Boolean {
        if (baseDataModuleModel !is ClockProgressModuleModel) {
            return false
        }
        return baseDataModuleModel.localIsValidFlag
    }

    override fun bindConfigToView(view: View?, baseConfigModuleModel: BaseConfigModel?, pageInfo: PageInfo): View? {
        return view
    }

    override fun bindDataToView(view: View?, baseDataModuleModel: BaseModuleModel?, pageInfo: PageInfo): View? {
        if (!isOriginalView(view)) {
            return view
        }
        if (checkIsValidData(pageInfo, null, baseDataModuleModel)) {
            SdkBaseUtil.ViewUtil.setVisibility(View.VISIBLE, view)
        } else {
            SdkBaseUtil.ViewUtil.setVisibility(View.GONE, view)
        }
        if (baseDataModuleModel !is ClockProgressModuleModel) {
            return view
        }

        SdkProxyFunctionUtil.displayImage(view?.findViewById(R.id.universal_id_icon), baseDataModuleModel.icon, com.ximalaya.android.componentelementarysdk.R.drawable.universal_ic_clock_activity_process_logo)
        SdkBaseUtil.ViewUtil.setTextString(baseDataModuleModel.title, view?.findViewById(R.id.universal_id_title))
        SdkBaseUtil.ViewUtil.setTextString("${baseDataModuleModel.completeNum}/${baseDataModuleModel.totalNum}", view?.findViewById(R.id.universal_id_sub_title))

        helper.toString()
        return view
    }

    override fun setListenOnView(view: View?, baseConfigModuleModel: BaseConfigModel?, baseDataModuleModel: BaseModuleModel?, pageInfo: PageInfo): View? {
        if (!isOriginalView(view)) {
            return view
        }

        if (baseDataModuleModel !is ClockProgressModuleModel) {
            return view
        }

        SdkBaseUtil.ViewUtil.setClickListener(baseDataModuleModel, com.ximalaya.android.componentelementarysdk.R.id.universal_tag_click_model, this, view?.findViewById(R.id.universal_id_function_btn))
        return view
    }

    override fun onClick(v: View?) {
        if (!SdkBaseUtil.Common.onClick(v)) {
            return
        }
        val tag: Any? = v?.getTag(com.ximalaya.android.componentelementarysdk.R.id.universal_tag_click_model)
        when (v?.id?: 0) {
            R.id.universal_id_function_btn -> {
                if (tag is ClockProgressModuleModel) {
                    SdkProxyFunctionUtil.jumpUrl(tag.shareUrl)
                }
            }
        }
    }

    override fun buildHelper(): BaseNativeModuleViewHelper<out BaseNativeModuleViewHelper.BaseDataProvider>? {
        return ClockProgressModuleViewHelper(dataProvider)
    }
}