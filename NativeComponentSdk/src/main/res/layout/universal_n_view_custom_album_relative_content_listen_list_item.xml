<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="4dp"
    app:cardBackgroundColor="@color/universal_color_transparent"
    app:cardElevation="0dp"
    tools:ignore="MissingDefaultResource">

    <ImageView
        android:id="@+id/universal_id_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY" />

</androidx.cardview.widget.CardView>