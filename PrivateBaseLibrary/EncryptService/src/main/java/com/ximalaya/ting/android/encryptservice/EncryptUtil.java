package com.ximalaya.ting.android.encryptservice;

import android.content.Context;
import android.support.annotation.IntDef;
import android.text.TextUtils;

import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.annotation.Retention;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.lang.annotation.RetentionPolicy.SOURCE;

/**
 * 签名工具类
 * Created by roc on 2016/5/3.
 * <AUTHOR>
 */
public class EncryptUtil {

    //是否打开上传自定义native实现的xmly uuid
    public static boolean XI_OPEN = true;

    private static boolean hasLoadLib = false;
    private static class HolderClass {
        private final static EncryptUtil instance = new EncryptUtil();
    }

    private EncryptUtil() {
    }


    /**
     * 必须要调用这个方法初始化 否则使用出错
     *
     * @param context
     */
    public void init(Context context, ILibLoader libLoader) {
        if (context == null) {
            return;
        }

        if (!hasLoadLib) {
            hasLoadLib = true;
            String libName = "encrypt";

            if(libLoader != null) {
                libLoader.loadLib(context, libName);
            }else {
                System.loadLibrary(libName);
            }
        }
    }

    public void init(Context context){
        init(context, null);
    }

    public static synchronized EncryptUtil getInstance(Context context) {

        return HolderClass.instance;
    }

    public String encryptByKey(Context context, String enData) {
        try {
            return encryptByPublicKey(context, enData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public byte[] decryptByKey(Context context, byte[] enData) {
        try {
            return decryptByPublicKey(context, enData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new byte[0];
    }

    public String getPlaySignature(Context context, Map<String, String> specificParams) {
        return getSignatureV1(context, specificParams, PLAY);
    }

    public String getPaySignature(Context context, Map<String, String> specificParams) {
        return getSignatureV1(context, specificParams, PAY);
    }

    public String getGiftSignature(Context context, Map<String, String> specificParams) {
        return getSignatureV1(context, specificParams, GIFT);
    }

    public String getPluginSignature(Context context, Map<String, String> specificParams) {
        return getSignatureV1(context, specificParams, PLUGIN);
    }

    public String getRecordSignature(Context context, Map<String, String> specificParams) {
        return getSignatureV1(context, specificParams, RECORD);
    }

    //许多业务的签名使用了同一个key（录音上传，下载等等）。使用同一个函数
    public String getCommonSignature(Context context, Map<String, String> specificParams) {
        return getSignatureV1(context, specificParams, COMMON);
    }

    public void getJsSdkSignature(Context context,Map<String, String> specificParams) {
        if(specificParams == null){
            return;
        }
        String result = getJsSdkSignatureNative(context, specificParams);
        specificParams.put("sig", result);
    }

    public void addImageSignature(Context context, Map<String, String> params){
        if(params == null){
            return;
        }

        String result = getSignatureV2(context, params);
        params.put("signature", result);
    }

    public byte[] decryptByKey2(Context context, byte[] enData) {
        byte[] bytes = new byte[0];
        try {
            bytes = decryptByPublicKey2(context, enData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bytes;
    }

    public String decryptRc4ByPublicKeyN(Context context, String enData) {
        String result = null;
        try {
            result = decryptRc4ByPublicKey(context, enData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public byte[] decryptByKey2Weike(Context context,byte[] enData) {
        byte[] bytes = new byte[0];
        try {
            bytes = decryptByPublicKey2Weike(context, enData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bytes;
    }

    public String decryptRc4ByPublicKeyWeikeL(Context context,String enData) {
        String result = null;
        try {
            result = decryptRc4ByPublicKeyWeike(context, enData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public String getInfoNative(Context context){
        return getPInfoNative(context);
    }

    public String getXID(Context context){
        return getXIDNative(context);
    }

    /**
     * 获取加密的xid,包含uuid,androidID,IMEI,macAddress,serial,timestamp
     * @param context
     * @return
     */
    public String getDInfo(Context context){
        return getDInfoNative(context);
    }

    public static final int PLAY = 0;
    public static final int PAY = 1;
    public static final int GIFT = 2;
    public static final int RECORD = 3;
    public static final int COMMON = 4;
    public static final int PLUGIN = 5;

    @Retention(SOURCE)
    @IntDef({PLAY, PAY, GIFT, RECORD, COMMON, PLUGIN})
    public @interface SignatureType {
    }

    private String getSignatureV1(Context context, Map<String, String> specificParams,
                                  @SignatureType int type) {

        Map<String, String> map = new HashMap<>(specificParams);

        int size = map.size();
        String keyArr[] = new String[size];
        String valueArr[] = new String[size];
        int i = 0;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (!TextUtils.isEmpty(key) && !TextUtils.isEmpty(value)) {
                keyArr[i] = key;
                valueArr[i] = value;
                i++;
            }
        }
        String result = null;
        switch (type) {
            case GIFT:
            case PLAY:
                result = getPlaySignatureNative(context, keyArr, valueArr, i);
                break;
            case PAY:
                result = getPaySignatureNative(context, keyArr, valueArr, i);
                break;
            case PLUGIN:
                result = getGiftSignatureNative(context, keyArr, valueArr, i);
                break;
            case COMMON:
            case RECORD:
                result = getRecordSignatureNative(context, keyArr, valueArr, i);
                break;
        }
        specificParams.put("signature", result);
        return result;
    }


    /**
     * 新版请求签名函数，会隐藏key到图片中
     * @param context
     * @param params
     * @return
     */
    public String getSignatureV2(Context context, Map<String,String> params){

        if(params.isEmpty()){
            return "";
        }
        StringBuilder sb=new StringBuilder();
        String[] keys= new String[params.size()];
        params.keySet().toArray(keys);
        List<String> keyList= Arrays.asList(keys);
        Collections.sort(keyList);
        for(int i = 0;i<keyList.size();i++){
            String key = keyList.get(i);
            String value = params.get(key);
            if(!TextUtils.isEmpty(key) && !TextUtils.isEmpty(value)) {  //过滤空的键值对，value为空时服务端收不到对应的key
                sb.append(key).append("=").append(value);
                if (i != (keyList.size() - 1)) {
                    sb.append("&");
                }
            }
        }
        String paramsStr = sb.toString();

        Logger.i("MainActivityLog","paramsStr:"+paramsStr);

        return getSNative(context,paramsStr);
    }

    //new start
    private native String getPlaySignatureNative(Context context, String keyArr[], String valueArr[], int length);

    private native String getPaySignatureNative(Context context, String keyArr[], String valueArr[], int length);

    private native String getGiftSignatureNative(Context context, String keyArr[], String valueArr[], int length);

    private native String getRecordSignatureNative(Context context, String keyArr[], String valueArr[], int length);

    private native String getJsSdkSignatureNative(Context context,Map<String,String> hashMap);

    //new end

    private native String encryptByPublicKey2(Context context, String enData) throws Exception;

    private native byte[] decryptByPublicKey2(Context context, byte[] enData) throws Exception;

    private native String encryptByPublicKey(Context context, String enData) throws Exception;

    private native byte[] decryptByPublicKey(Context context, byte[] enData) throws Exception;

    // 广告
    public native String decryptByPublicKey3(Context context, String enData) throws Exception;

    private native String decryptRc4ByPublicKey(Context context, String enData) throws Exception;

    public native String encryptByPublicKeyNative(String enData);

    public native String decryptByPrivateKeyNative(String enData);

    public native String decryptByPublicKeyNative(String enDate);

    public native String getXIDNative(Context context);

    public native String getDInfoNative(Context context);

    public native String getPInfoNative(Context context);

    public native String getSNative(Context context,String params);

    //微课Key1
    public native byte[] decryptByPublicKey2Weike(Context context, byte[] enData) throws Exception;
    //微课Key2
    public native String decryptRc4ByPublicKeyWeike(Context context, String enData) throws Exception;

    public native int getTracerPid();
}
