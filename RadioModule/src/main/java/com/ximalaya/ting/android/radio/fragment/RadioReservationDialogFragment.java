package com.ximalaya.ting.android.radio.fragment;

import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.radio.R;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2020/8/5
 */
public class RadioReservationDialogFragment extends BaseDialogFragment implements View.OnClickListener {

    private View mView;
    private boolean mMaskIsShow = false; //解决fragment重复添加crash问题
    private IOnCancelSuccessListener mIOnCancelSuccessListener;

    public void setOnCancelSuccessListener(IOnCancelSuccessListener IOnCancelSuccessListener) {
        mIOnCancelSuccessListener = IOnCancelSuccessListener;
    }

    public static RadioReservationDialogFragment newInstance() {
        Bundle args = new Bundle();
        RadioReservationDialogFragment fragment = new RadioReservationDialogFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater,
                             @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (getDialog() != null) {
            getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
            Window window = getDialog().getWindow();
            if (window != null) {
                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                mView = inflater.inflate(R.layout.radio_dialog_reservation, ((ViewGroup) window.findViewById(android.R.id.content)), false);//此处必须是android.R.id.main_content
                window.setLayout(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                window.getDecorView().setPadding(0, 0, 0, 0); //消除边距

            }
            mView.findViewById(R.id.radio_reservation_close).setOnClickListener(this);
            mView.findViewById(R.id.radio_reservation_btn).setOnClickListener(this);

            setCancelable(true);
        }
        return mView;
    }


    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (id == R.id.radio_reservation_close) {
            dismiss();
        } else if (id == R.id.radio_reservation_btn) {
            //取消预约
            if (mIOnCancelSuccessListener != null) {
                mIOnCancelSuccessListener.onCancel();
            }
        }
    }

    public interface IOnCancelSuccessListener {
        void onCancel();
    }


    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        mMaskIsShow = false;
        mIOnCancelSuccessListener = null;
    }

    @Override
    public int show(FragmentTransaction transaction, String tag) {
        if (mMaskIsShow) {
            return 0;
        }
        mMaskIsShow = true;
        return super.show(transaction, tag);
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        if (mMaskIsShow) {
            return;
        }
        mMaskIsShow = true;
        super.show(manager, tag);
    }


}
