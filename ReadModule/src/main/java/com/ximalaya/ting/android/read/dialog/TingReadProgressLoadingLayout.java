package com.ximalaya.ting.android.read.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.libra.Color;
import com.libra.TextUtils;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.read.R;
import com.ximalaya.ting.android.read.widgets.pageview.PageStyle;
import com.ximalaya.ting.android.read.widgets.pageview.TingReadSettingManager;


/**
 * 统一Loading封装
 */

public class TingReadProgressLoadingLayout {

    public static void showProgressView(View rootView) {
        ViewGroup loadingRootView = rootView.findViewById(R.id.loading_root_view);
        ViewGroup loadingView = rootView.findViewById(R.id.loading_view);
        loadingRootView.setVisibility(View.VISIBLE);
        loadingRootView.setOnClickListener(v -> {
        });

        TextView tvTitle = loadingRootView.findViewById(R.id.tv_loading);
        try {
            XmLottieAnimationView loadingAnimalView = loadingRootView.findViewById(R.id.read_loading_view);
            loadingAnimalView.setImageAssetsFolder("lottie/host_loading/");
            loadingAnimalView.setAnimation("lottie/host_loading/loading.json");
            loadingAnimalView.loop(true);
        } catch (Exception e) {
            e.printStackTrace();
        }

        PageStyle pageStyle = TingReadSettingManager.getInstance().getPageStyle();
        GradientDrawable drawable = (GradientDrawable) loadingView.getBackground();
        if (pageStyle == PageStyle.ANCIENT) {
            drawable.setColor(Color.parseColor("#f2F9F5E9"));
            tvTitle.setTextColor(Color.parseColor("#665F5C"));
        } else if (pageStyle == PageStyle.EYESHIELD) {
            drawable.setColor(Color.parseColor("#f2E6F3E6"));
            tvTitle.setTextColor(Color.parseColor("#565E55"));
        } else if (pageStyle == PageStyle.PINK) {
            drawable.setColor(Color.parseColor("#f2FFF4F1"));
            tvTitle.setTextColor(Color.parseColor("#626057"));
        } else if (pageStyle == PageStyle.NIGHT || pageStyle == PageStyle.PLAY_PAGE) {
            drawable.setColor(Color.parseColor("#f21B1B1B"));
            tvTitle.setTextColor(Color.parseColor("#8D8D91"));
        } else {
            drawable.setColor(Color.parseColor("#f2FFFFFF"));
            tvTitle.setTextColor(Color.parseColor("#666666"));
        }
        loadingView.setBackground(drawable);
    }

    public static void hiddenProgressView(View rootView) {
        ViewGroup loadingRootView = rootView.findViewById(R.id.loading_root_view);
        loadingRootView.setVisibility(View.GONE);
    }
}
