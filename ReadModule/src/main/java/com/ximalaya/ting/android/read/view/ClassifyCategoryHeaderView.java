package com.ximalaya.ting.android.read.view;

import android.content.Context;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.read.R;
import com.ximalaya.ting.android.read.bean.cate.GenderBean;
import com.ximalaya.ting.android.read.bean.classify.ClassifyFilterBean;
import com.ximalaya.ting.android.read.utils.ReadHomePageHistoryManager;
import com.ximalaya.ting.android.read.utils.ScreenUtils;
import com.ximalaya.ting.android.read.utils.XMUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @CreateDate: 2022/2/16 11:16 上午
 * @Author: ypp
 * @Description:
 */
public class ClassifyCategoryHeaderView extends FrameLayout {

    public static final int FILTER_GENDER = 0x10;
    public static final int FILTER_CATEGORY = 0x11;
    public static final int FILTER_NUM = 0x12;
    public static final int FILTER_FINISH = 0x13;
    public static final int FILTER_VIP = 0x14;


    private LinearLayout rootView;
    private Map<Integer, String> selectMap = new LinkedHashMap<>();
    private Map<Integer, List> filterMap = new LinkedHashMap<>();
    private Map<Integer, LinearLayout> filterViews = new HashMap<>();

    public ClassifyCategoryHeaderView(Context context) {
        this(context, null);
    }

    public ClassifyCategoryHeaderView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ClassifyCategoryHeaderView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public Map<Integer, String> getSelectMap() {
        if (selectMap == null) {
            selectMap = new HashMap<>();
        }
        return selectMap;
    }


    private void resetParams() {
        filterMap.clear();
        filterViews.clear();
    }

    public void resetBottomSelectParams() {
        if (selectMap != null) {
            Iterator<Integer> iterator = selectMap.keySet().iterator();
            while (iterator.hasNext()) {
                Integer next = iterator.next();
                if (next != FILTER_GENDER) {
                    selectMap.put(next, null);
                }
            }
        }
    }


    public String getSelectStr() {
        if (selectMap != null && !selectMap.isEmpty()) {
            List<String> sumStrList = new ArrayList<>();
            Set<Map.Entry<Integer, String>> entries = selectMap.entrySet();
            Iterator<Map.Entry<Integer, String>> iterator = entries.iterator();
            while (iterator.hasNext()) {
                Map.Entry<Integer, String> next = iterator.next();
                int key = next.getKey();
                String value = next.getValue();
                if (key == FILTER_GENDER) {
                    String genderStr = getGenderStr(value);
                    if (!TextUtils.isEmpty(genderStr)) {
                        sumStrList.add(genderStr);
                    }
                } else {
                    String cateStr = getBottomCateName(key, value);
                    if (!TextUtils.isEmpty(cateStr) && !TextUtils.equals(cateStr, "全部")) {
                        sumStrList.add(cateStr);
                    }
                }
            }
            if (XMUtils.isListValid(sumStrList)) {
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < sumStrList.size(); i++) {
                    builder.append(sumStrList.get(i));
                    if (i >= 0 && i < sumStrList.size() - 1) {
                        builder.append("-");
                    }
                }
                return builder.toString().trim();
            }

        }
        return null;
    }

    public String getModuleNameStr() {
        if (selectMap != null && !selectMap.isEmpty()) {
            List<String> sumStrList = new ArrayList<>();
            Set<Map.Entry<Integer, String>> entries = selectMap.entrySet();
            Iterator<Map.Entry<Integer, String>> iterator = entries.iterator();
            while (iterator.hasNext()) {
                Map.Entry<Integer, String> next = iterator.next();
                int key = next.getKey();
                String value = next.getValue();
                if (key != FILTER_GENDER) {
                    String cateStr = getBottomCateName(key, value);
                    if (!TextUtils.isEmpty(cateStr)) {
                        sumStrList.add(cateStr);
                    }
                }
            }
            if (XMUtils.isListValid(sumStrList)) {
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < sumStrList.size(); i++) {
                    builder.append(sumStrList.get(i));
                    if (i >= 0 && i < sumStrList.size() - 1) {
                        builder.append("-");
                    }
                }
                return builder.toString().trim();
            }

        }
        return "";
    }


    public String getGenderStrWithKey() {
        if (selectMap != null) {
            String value = selectMap.get(FILTER_GENDER);
            if (!TextUtils.isEmpty(value)) {
                return getGenderStr(value);
            }
        }
        return "";
    }

    public String getCateNameWithKey(int key) {
        if (selectMap != null && key != FILTER_GENDER) {
            String value = selectMap.get(key);
            if (!TextUtils.isEmpty(value)) {
                return getBottomCateName(key, value);
            }
        }
        return "";
    }


    /**
     * 获取 性别栏名称
     *
     * @param value
     * @return
     */
    private String getGenderStr(String value) {
        List list = filterMap.get(FILTER_GENDER);
        if (XMUtils.isListValid(list)) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i) instanceof GenderBean && TextUtils.equals(value, ((GenderBean) list.get(i)).getGender())) {
                    return ((GenderBean) list.get(i)).getName();
                }
            }
        }
        return null;
    }

    /**
     * 获取其他栏筛选航
     *
     * @param type
     * @param value
     * @return
     */
    private String getBottomCateName(int type, String value) {
        if (filterMap.get(type) != null && XMUtils.isListValid(filterMap.get(type))) {
            List list = filterMap.get(type);
            for (int i = 0; i < list.size(); i++) {
                Object o = list.get(i);
                if (o != null && o instanceof ClassifyFilterBean.DataBean.OrderBean) {
                    if (TextUtils.equals(value, ((ClassifyFilterBean.DataBean.OrderBean) o).getValue())) {
                        return ((ClassifyFilterBean.DataBean.OrderBean) o).getDesc();
                    }
                }
            }
        }
        return null;
    }


    public String getTagLevel(int filterType) {
        if (filterType == FILTER_CATEGORY) {
            return "1";
        } else if (filterType == FILTER_NUM) {
            return "2";
        } else if (filterType == FILTER_FINISH) {
            return "3";
        } else if (filterType == FILTER_VIP) {
            return "4";
        }
        return "";
    }


    /**
     * 设置筛选的数据
     *
     * @param bean
     * @param listener
     */
    public void setFilterData(ClassifyFilterBean.DataBean bean, String iTingGenderName, String iTIngGenderId, IFilterClickListener listener) {
        resetParams();
        rootView.removeAllViews();
        //类型分别
        if (bean != null) {
            if (XMUtils.isListValid(bean.getGenderList())) {
                filterMap.put(FILTER_GENDER, bean.getGenderList());
                //初始化性别栏默认值
                initDefaultGenderFilter(bean, iTingGenderName, iTIngGenderId);
            }
            if (XMUtils.isListValid(bean.getCategory())) {
                filterMap.put(FILTER_CATEGORY, bean.getCategory());
            }
            if (XMUtils.isListValid(bean.getWordNum())) {
                filterMap.put(FILTER_NUM, bean.getWordNum());
            }
            if (XMUtils.isListValid(bean.getIsFinish())) {
                filterMap.put(FILTER_FINISH, bean.getIsFinish());
            }
            if (XMUtils.isListValid(bean.getIsVipOrFree())) {
                filterMap.put(FILTER_VIP, bean.getIsVipOrFree());
            }

            if (!filterMap.isEmpty()) {
                //初始化默认选项
                initDefaultBottomSelectPara();
                Iterator<Map.Entry<Integer, List>> iterator = filterMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Integer, List> next = iterator.next();
                    int type = next.getKey();
                    List itemList = next.getValue();
                    HorizontalScrollView scrollView = new HorizontalScrollView(getContext());
                    scrollView.setOverScrollMode(View.OVER_SCROLL_NEVER);
                    scrollView.setHorizontalScrollBarEnabled(false);
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                    params.topMargin = ScreenUtils.dpToPx(16);
                    LinearLayout linearLayout = new LinearLayout(getContext());
                    linearLayout.setGravity(Gravity.CENTER_VERTICAL | Gravity.LEFT);
                    linearLayout.setOrientation(LinearLayout.HORIZONTAL);
                    scrollView.addView(linearLayout);
                    rootView.addView(scrollView, params);
                    filterViews.put(type, linearLayout);
                    try {
                        addFilterItemList(type, itemList, listener);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 初始化默认数据
     *
     * @param bean
     * @param iTingGenderName
     * @param iTingGenderId
     */
    private void initDefaultGenderFilter(ClassifyFilterBean.DataBean bean, String iTingGenderName, String iTingGenderId) {
        if (TextUtils.isEmpty(selectMap.get(FILTER_GENDER))) {
            //优先使用Iting的配置性别参数
            //其次使用选择性别弹窗的配置
            //最终使用默认第一个
            String genderIdFinal = null;
            if (!TextUtils.isEmpty(iTingGenderName) || !TextUtils.isEmpty(iTingGenderId)) {
                for (int i = 0; i < bean.getGenderList().size(); i++) {
                    GenderBean genderBean = bean.getGenderList().get(i);
                    if (genderBean != null) {
                        if (TextUtils.equals(iTingGenderName, genderBean.getName()) || TextUtils.equals(iTingGenderId, genderBean.getGender())) {
                            genderIdFinal = genderBean.getGender();
                            selectMap.put(FILTER_GENDER, bean.getGenderList().get(i).getGender());
                            break;
                        }
                    }
                }
            }
            //假如没有找到Iting匹配的gender
            String userSelectGender = ReadHomePageHistoryManager.getUserSelectGender();
            if (TextUtils.isEmpty(genderIdFinal) && !TextUtils.isEmpty(userSelectGender)) {
                for (int i = 0; i < bean.getGenderList().size(); i++) {
                    GenderBean genderBean = bean.getGenderList().get(i);
                    if (genderBean != null) {
                        if (TextUtils.equals(userSelectGender, genderBean.getName())) {
                            genderIdFinal = genderBean.getGender();
                            selectMap.put(FILTER_GENDER, bean.getGenderList().get(i).getGender());
                            break;
                        }
                    }
                }
            }
            //默认取第一个
            if (TextUtils.isEmpty(genderIdFinal) && bean.getGenderList().get(0) != null) {
                selectMap.put(FILTER_GENDER, bean.getGenderList().get(0).getGender());
            }
        }
    }


    private void initDefaultBottomSelectPara() {
        Iterator<Map.Entry<Integer, List>> iterator = filterMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, List> next = iterator.next();
            int key = next.getKey();
            List list = next.getValue();
            if (key != FILTER_GENDER && XMUtils.isListValid(list) && list.get(0) != null && list.get(0) instanceof ClassifyFilterBean.DataBean.OrderBean) {
                selectMap.put(key, ((ClassifyFilterBean.DataBean.OrderBean) list.get(0)).getValue());
            }
        }
    }


    private void addFilterItemList(int type, List itemList, IFilterClickListener listener) {
        LinearLayout linearLayout = filterViews.get(type);
        if (linearLayout != null && linearLayout.getChildCount() > 0) {
            linearLayout.removeAllViews();
        }
        //是否是性别栏目
        boolean isGenderRow = type == FILTER_GENDER;
        for (int j = 0; j < itemList.size(); j++) {
            Object object = itemList.get(j);
            String value = isGenderRow ? ((GenderBean) object).getGender() : ((ClassifyFilterBean.DataBean.OrderBean) object).getValue();
            String desc = isGenderRow ? ((GenderBean) object).getName() : ((ClassifyFilterBean.DataBean.OrderBean) object).getDesc();
            String xmRequestId = isGenderRow ? ((GenderBean) object).getXmRequestId() : ((ClassifyFilterBean.DataBean.OrderBean) object).getXmRequestId();
            TextView textView = new TextView(getContext());
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            textView.setMaxLines(1);
            textView.setText(desc);
            textView.setGravity(Gravity.CENTER);
            if (isGenderRow) {
                textView.setPadding(ScreenUtils.dpToPx(28), ScreenUtils.dpToPx(4), ScreenUtils.dpToPx(28), ScreenUtils.dpToPx(4));
                if (TextUtils.equals(value, selectMap.get(FILTER_GENDER))) {
                    textView.setTextColor(ContextCompat.getColor(getContext(), R.color.read_color_ffffff_000000));
                    textView.setBackground(ContextCompat.getDrawable(getContext(), R.drawable.read_bg_home_item_rank_tag));
                    textView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                } else {
                    textView.setTextColor(ContextCompat.getColor(getContext(), R.color.read_color_333333_dcdcdc));
                    textView.setBackground(ContextCompat.getDrawable(getContext(), R.drawable.read_bg_home_item_rank_tag_unselect));
                    textView.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                }
            } else {
                textView.setPadding(ScreenUtils.dpToPx(19), ScreenUtils.dpToPx(4), ScreenUtils.dpToPx(19), ScreenUtils.dpToPx(4));
                if (TextUtils.equals(selectMap.get(type), value)) {
                    textView.setTextColor(ContextCompat.getColor(getContext(), R.color.read_color_FF4C2E));
                    textView.setBackground(ContextCompat.getDrawable(getContext(), R.drawable.read_bg_home_category_filter_item));
                    textView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                } else {
                    textView.setTextColor(ContextCompat.getColor(getContext(), R.color.read_color_333333_dcdcdc));
                    textView.setBackground(null);
                    textView.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                }
                if (TextUtils.equals(desc, "限免")) {
                    textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.read_ic_categoty_free_limit, 0);
                    textView.setCompoundDrawablePadding(ScreenUtils.dpToPx(3));
                } else {
                    textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                    textView.setCompoundDrawablePadding(ScreenUtils.dpToPx(0));
                }
            }
            textView.setOnClickListener(v -> {
                selectMap.put(type, value);
                try {
                    addFilterItemList(type, itemList, listener);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (listener != null) {
                    listener.onItemClick(xmRequestId, type, selectMap);
                }
            });
            LinearLayout.LayoutParams itemParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            itemParams.rightMargin = ScreenUtils.dpToPx(17);
            if (linearLayout != null) {
                linearLayout.addView(textView, itemParams);
            }
        }
    }


    private void init(Context context) {
        View view = View.inflate(context, R.layout.read_layout_top_category, this);
        //男生
        rootView = view.findViewById(R.id.container);
        rootView.setPadding(ScreenUtils.dpToPx(16), 0, 0, ScreenUtils.dpToPx(16));
    }


    public interface IFilterClickListener {
        void onItemClick(String xmRequestId, int clickChannel, Map<Integer, String> selectMap);
    }


}
