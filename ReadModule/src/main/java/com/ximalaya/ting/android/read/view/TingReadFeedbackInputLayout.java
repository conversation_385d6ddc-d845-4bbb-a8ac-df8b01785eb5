package com.ximalaya.ting.android.read.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.View;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.view.other.EmotionSelector;
import com.ximalaya.ting.android.read.R;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> feiwen
 * date   : 2021/8/26
 * desc   :
 */
public class TingReadFeedbackInputLayout extends RelativeLayout implements View.OnClickListener {

    private EditText mEtInput;
    private EmotionSelector mEmotionSelector;
    private IAiFeedbackListener mAiFeedbackListener;
    private Context mContext;
    private IViewOnVisibilityChangeListener mOnVisibilityChangeListener;
    private TextView mTvSend;
    private String mOriginStr;

    private View rootView;
    private TextView tvTitle;
    private View viewLine;
    private View viewInput;
    private View llPreOrNext;
    private TextView mTvPreSentence;
    private TextView mTvNextSentence;

    public TingReadFeedbackInputLayout(Context context) {
        super(context);
        mContext = context;
        initUI();
    }

    public TingReadFeedbackInputLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initUI();
    }

    public TingReadFeedbackInputLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initUI();
    }

    private void initUI() {
        if (mContext == null) {
            mContext = getContext();
        }
        View contentView = View.inflate(mContext, R.layout.read_ting_read_feedback_input_layout, this);
        rootView = contentView.findViewById(R.id.read_ll_root_view);
        tvTitle = contentView.findViewById(R.id.read_tv_title);
        viewLine = contentView.findViewById(R.id.read_view_line_sentence);
        viewInput = contentView.findViewById(R.id.read_rl_input);

        llPreOrNext = contentView.findViewById(R.id.read_ll_pre_or_next);
        llPreOrNext.setVisibility(VISIBLE);
        mEtInput = contentView.findViewById(R.id.read_et_input);
        mTvSend = contentView.findViewById(R.id.read_tv_send);
        mTvPreSentence = contentView.findViewById(R.id.read_tv_pre_sentence);
        mTvNextSentence = contentView.findViewById(R.id.read_tv_next_sentence);

        mEmotionSelector = contentView.findViewById(R.id.read_emotion_selector);
        mEmotionSelector.setEditText(mEtInput, true);
        mTvPreSentence.setOnClickListener(this);
        mTvNextSentence.setOnClickListener(this);
        mEmotionSelector.setOnEditContentListener(new EmotionSelector.OnEditContentListener() {
            @Override
            public void insert(String emotion, Drawable drawble) {

            }

            @Override
            public void remove() {

            }

            @Override
            public void onClick() {
                if (mAiFeedbackListener != null) {
                    mAiFeedbackListener.onEditTextClick(mEtInput);
                }
            }
        });
        contentView.setOnClickListener(v -> {
        });
        mEtInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                updateEtInputUI();
            }
        });
    }

    public void setNightModel(boolean isNight) {
        if (isNight) {
            rootView.setBackgroundResource(R.drawable.read_ting_read_radius_10_color_1e1e1e);
            tvTitle.setTextColor(Color.parseColor("#cfcfcf"));
            mTvPreSentence.setTextColor(Color.parseColor("#cfcfcf"));
            mTvNextSentence.setTextColor(Color.parseColor("#cfcfcf"));
            llPreOrNext.setBackgroundResource(R.drawable.read_bg_btn_2a2a2a_corner_100);
            viewLine.setBackgroundColor(Color.parseColor("#666666"));

            viewInput.setBackgroundResource(R.drawable.read_bg_rect_2a2a2a_radius_4);
            mEtInput.setBackgroundResource(R.drawable.read_corner8_solid_2a2a2a);
            mEtInput.setTextColor(Color.parseColor("#cfcfcf"));
            mEtInput.setHighlightColor(Color.parseColor("#44302e"));

            mTvSend.setTextColor(Color.parseColor("#cfcfcf"));
            mTvSend.setBackgroundResource(R.drawable.read_bg_444444_radius_29);
        } else {
            rootView.setBackgroundResource(R.drawable.read_ting_read_radius_10_color_ffffff);
            tvTitle.setTextColor(Color.parseColor("#333333"));
            mTvPreSentence.setTextColor(Color.parseColor("#333333"));
            mTvNextSentence.setTextColor(Color.parseColor("#333333"));
            llPreOrNext.setBackgroundResource(R.drawable.read_bg_btn_cccccc_corner_100);
            viewLine.setBackgroundColor(Color.parseColor("#e0e0e0"));

            viewInput.setBackgroundResource(R.drawable.read_bg_rect_f5f5f5_radius_4);
            mEtInput.setBackgroundResource(R.drawable.read_corner8_solid_f3f4f5);
            mEtInput.setHighlightColor(Color.parseColor("#ffede8"));

            mTvSend.setTextColor(Color.parseColor("#ffffff"));
            mTvSend.setBackgroundResource(R.drawable.read_bg_dddddd_radius_29);
        }
    }

    public void setEmotionSelectorVisibility(int visibility) {
        mEmotionSelector.setVisibility(visibility);
    }

    public void cancelWatch() {
        mEmotionSelector.cancleWatch();
    }

    public void hideEmotionPanel() {
        mEmotionSelector.hideEmotionPanel();
    }

    public void hideSoftInput() {
        mEmotionSelector.hideSoftInput();
    }

    public void toggleSoftInput() {
        mEmotionSelector.toggleSoftInput();
    }

    private void doSendComment(View view) {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mContext);
            return;
        }
        String text = mEtInput.getEditableText().toString();
        if (mAiFeedbackListener != null) {
            mAiFeedbackListener.onSendClick(view, text);
        }
    }

    public void setKeyboardListener(final EmotionSelector.IKeyboardListener keyboardListener) {
        mEmotionSelector.setKeyboardListener(new EmotionSelector.IKeyboardListener2() {
            @Override
            public void toggle(boolean show, boolean requestEmotion) {
                if (keyboardListener != null) {
                    if (keyboardListener instanceof EmotionSelector.IKeyboardListener2) {
                        ((EmotionSelector.IKeyboardListener2) keyboardListener).toggle(show,
                                requestEmotion);
                    }
                }
                if (!show) {
                    onKeyboardHide();
                } else {
                    onKeyboardShow();
                }
            }

            @Override
            public void toggle(boolean show) {
                if (keyboardListener != null) {
                    keyboardListener.toggle(show);
                }
                if (!show) {
                    onKeyboardHide();
                } else {
                    onKeyboardShow();
                }
            }
        });
    }

    private void onKeyboardHide() {
    }

    private void onKeyboardShow() {
    }

    public void setText(String text) {
        text = text == null ? "" : text;
        mOriginStr = text;
        mEtInput.setText(text);
        mEtInput.setSelection(text.length());
    }

    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
        if (changedView == this && mOnVisibilityChangeListener != null) {
            mOnVisibilityChangeListener.onVisibilityChanged(visibility);
        }
    }

    public void setOnVisibilityChangeListener(IViewOnVisibilityChangeListener onVisibilityChangeListener) {
        mOnVisibilityChangeListener = onVisibilityChangeListener;
    }

    public void setAiFeedbackListener(IAiFeedbackListener listener) {
        mAiFeedbackListener = listener;
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (id == R.id.read_tv_send) {
            doSendComment(mEtInput);
        } else if (id == R.id.read_tv_pre_sentence) {
            if (mAiFeedbackListener != null) {
                mAiFeedbackListener.onPreClick(v);
            }
        } else if (id == R.id.read_tv_next_sentence) {
            if (mAiFeedbackListener != null) {
                mAiFeedbackListener.onNextClick(v);
            }
        }
    }

    private void updateEtInputUI() {
        if (mEtInput == null || mEtInput.getText() == null || mTvSend == null) {
            return;
        }

        String s = mEtInput.getText().toString();
        if (checkCommentValid(s)) {
            Drawable drawable = ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.read_bg_ff4646_radius_29, mContext.getTheme());
            mTvSend.setBackground(drawable);
            mTvSend.setOnClickListener(this);
        } else {
            Drawable drawable = ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.read_bg_dddddd_radius_29, mContext.getTheme());
            mTvSend.setBackground(drawable);
            mTvSend.setOnClickListener(null);
        }
    }

    private boolean checkCommentValid(String str) {
        if (TextUtils.isEmpty(str) || str.equals(mOriginStr)) {
            return false;
        }

        return checkSpecialChar(str);
    }

    public boolean checkSpecialChar(String str) {
        try {
            String regex3 = "^[\\u4E00-\\u9FA5，。,\\x20\\.\\?？:\\：\\！!\\“\\”\\'\\‘\\’\\;；（）\\(\\)——\\-《》、\\%¥$€\\@\\~\\&\\*×÷\\=\\<\\>\\≤\\≥……\\+A-Za-z0-9]+$";
            Pattern p = Pattern.compile(regex3);
            Matcher m = p.matcher(str);
            boolean match = m.matches();
            if (!match) {
                CustomToast.showToast("输入包含特殊字符，无法提交哦");
            }
            return match;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public interface IAiFeedbackListener {
        void onSendClick(View view, CharSequence charSequence);

        default void onPreClick(View view) {

        }

        default void onNextClick(View view) {

        }

        default void onEditTextClick(View view) {

        }
    }

    public interface IViewOnVisibilityChangeListener {
        void onVisibilityChanged(int visibility);
    }
}
