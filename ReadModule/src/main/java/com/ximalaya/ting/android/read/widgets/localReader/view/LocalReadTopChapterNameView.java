package com.ximalaya.ting.android.read.widgets.localReader.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.read.widgets.pageview.PageStyle;
import com.ximalaya.ting.android.read.widgets.pageview.ReadSettingManager;
import com.ximalaya.ting.android.read.R;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * @CreateDate: 2022/4/14 6:04 下午
 * @Author: ypp
 * @Description: 阅读器顶部标题
 */
public class LocalReadTopChapterNameView extends FrameLayout {

    private TextView tv_title;
    private FrameLayout fl_scroll_title;

    private String lastChapterName;
    private IExitCallback mExitCallback;

    public LocalReadTopChapterNameView(@NonNull Context context) {
        this(context, null);
    }

    public LocalReadTopChapterNameView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LocalReadTopChapterNameView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        View bottomView = LayoutInflater.from(BaseApplication.getMyApplicationContext()).inflate(R.layout.read_local_top_chapter_namei, this);
        fl_scroll_title = bottomView.findViewById(R.id.fl_scroll_title);
        tv_title = bottomView.findViewById(R.id.tv_title);
        //
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            fl_scroll_title.setPadding(0, BaseUtil.getStatusBarHeight(getContext()), 0, 0);
        }
        initPageStyle();
    }

    public void initPageStyle() {
        PageStyle pageStyle = ReadSettingManager.getInstance().getPageStyle();
        if (fl_scroll_title != null) {
            fl_scroll_title.setBackgroundColor(pageStyle.getBgColor(getContext()));
        }
        if (tv_title != null) {
            tv_title.setTextColor(pageStyle.getFontColor(getContext()));
        }
    }

    public void setChapterName(String chapterName) {
        if (!TextUtils.isEmpty(chapterName)) {
            if (!TextUtils.equals(chapterName, lastChapterName)) {
                tv_title.setText(chapterName);
                lastChapterName = chapterName;
            }
        }
    }

    private void initExitBtn() {
        if (mExitCallback != null) {
            View view = findViewById(R.id.read_tv_exit);
            if (view != null) {
                view.setVisibility(View.VISIBLE);
                view.setOnClickListener(v -> {
                    if (OneClickHelper.getInstance().onClick(v)) {
                        if (mExitCallback != null) {
                            mExitCallback.exit();
                        }
                    }
                });
                if (tv_title != null) {
                    tv_title.setVisibility(View.INVISIBLE);
                }
            }
        }
    }

    public void setExitCallback(IExitCallback exitCallback) {
        mExitCallback = exitCallback;
        initExitBtn();
    }

    public interface IExitCallback {
        void exit();
    }
}
