package com.ximalaya.ting.android.read.widgets.pageview;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.host.read.manager.TingTextToReaderManager;
import com.ximalaya.ting.android.read.R;
import com.ximalaya.ting.android.read.bean.TingReadChapterBean;
import com.ximalaya.ting.android.read.fragment.TingReadFragment;
import com.ximalaya.ting.android.read.listener.ITingNewUserReadReward;
import com.ximalaya.ting.android.read.manager.ParaComCacheManager;
import com.ximalaya.ting.android.read.widgets.theme.ThemeImageView;
import com.ximalaya.ting.android.read.widgets.theme.ThemeLinearLayout;
import com.ximalaya.ting.android.read.widgets.theme.ThemeTextView;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;


public class TingReadTitleBarView extends ThemeLinearLayout implements View.OnClickListener {

    private final Context mContext;

    private ThemeLinearLayout mLlContent;
    private ThemeTextView mTvStatusBarView;
    private ThemeImageView mIvBack;

    private TextView mTvTitle;

    private ThemeImageView mIvNewUserPack;
    private ThemeImageView mIvParaCom;
    private ThemeImageView mIvReadMenu;

    private TingReadChapterBean mReadChapterBean;

    private TingReadFragment mTingReadFragment;

    private OnReadTitleListener mOnReadTitleListener;

    public static boolean isReportBack = false;
    public static boolean isReportNewUserPack = false;

    public static void initReset() {
        isReportBack = false;
        isReportNewUserPack = false;
    }

    public TingReadTitleBarView(Context context) {
        this(context, null);
    }

    public TingReadTitleBarView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TingReadTitleBarView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        LayoutInflater.from(context).inflate(R.layout.read_title_bar_ting_reader, this);
        initView();
        setListener();
        setPageStyle();
    }

    private void initView() {
        mLlContent = findViewById(R.id.ll_content);
        mTvStatusBarView = findViewById(R.id.textTop);
        mIvBack = findViewById(R.id.iv_read_title_back);
        mTvTitle = findViewById(R.id.tv_title);
        mIvNewUserPack = findViewById(R.id.iv_read_new_user_pack);
        mIvParaCom = findViewById(R.id.iv_read_para_com);
        mIvReadMenu = findViewById(R.id.iv_read_menu);

        // 适配刘海
        LayoutParams layoutParams = (LayoutParams) mTvStatusBarView.getLayoutParams();
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            layoutParams.height = BaseUtil.getStatusBarHeight(mContext);
            mTvStatusBarView.setLayoutParams(layoutParams);
        }
    }

    private void setListener() {
        mIvBack.setOnClickListener(this);
        mIvParaCom.setOnClickListener(this);
        mIvReadMenu.setOnClickListener(this);
        mTvTitle.setOnClickListener(this);
    }

    public void setPageStyle() {
        PageStyle pageStyle = TingReadSettingManager.getInstance().getPageStyle();
        // 不需要设置背景  因为添加了到了pageView中, 会挡住选中按钮所以注释这个代码
//        mLlContent.setBackgroundColor(pageStyle.getBgColor(mContext));
        mTvStatusBarView.setBackgroundColor(pageStyle.getBgColor(mContext));

        if (TingReadSettingManager.getInstance().isDarkModel()) {
            mIvBack.setColorFilter(Color.WHITE);
            mIvReadMenu.setColorFilter(Color.WHITE);
            mTvTitle.setTextColor(Color.WHITE);
        } else {
            mIvBack.setColorFilter(Color.parseColor("#333333"));
            mIvReadMenu.setColorFilter(Color.parseColor("#333333"));
            mTvTitle.setTextColor(Color.parseColor("#333333"));
        }

        setParaComIcon();
    }

    public void setParaComIcon() {
        if (!ParaComCacheManager.INSTANCE.isOpenParaCommentFromOnline()) {
            mIvParaCom.setVisibility(View.GONE);
            return;
        } else {
            mIvParaCom.setVisibility(View.VISIBLE);
        }

        if (ParaComCacheManager.INSTANCE.isOpenParaCommentFromLocal()) {
            mIvParaCom.setImageDrawable(ContextCompat.getDrawable(getContext(), R.drawable.read_ic_ting_read_para_comment_on));
        } else {
            mIvParaCom.setImageDrawable(ContextCompat.getDrawable(getContext(), R.drawable.read_ic_ting_read_para_comment_off));
        }

        if (TingReadSettingManager.getInstance().isDarkModel()) {
            mIvParaCom.setColorFilter(Color.WHITE);
        } else {
            mIvParaCom.setColorFilter(Color.parseColor("#333333"));
        }
    }

    public void setTitle(String title) {
//        mTvTitle.setText(title);
    }

    public void setTingReadFragment(TingReadFragment mTingReadFragment) {
        this.mTingReadFragment = mTingReadFragment;
        if (mTingReadFragment != null) {
            mReadChapterBean = mTingReadFragment.getCurTingReadChapterBean();
            if (mReadChapterBean != null) {
                setTitle(mReadChapterBean.getTrackName());
            }

            mOnReadTitleListener = mTingReadFragment.mTitleListener;

            if (!isReportBack) {
                isReportBack = true;
                // 边听边读模式-收起到肚脐眼  控件曝光
                new XMTraceApi.Trace()
                        .setMetaId(61487)
                        .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                        .put("currAlbumId", String.valueOf(mTingReadFragment.getCurPlayAlbumId()))
                        .put("currTrackId", String.valueOf(mTingReadFragment.getPlayTrackId()))
                        .put(XmRequestIdManager.CONT_ID, String.valueOf(mTingReadFragment.getPlayTrackId()))
                        .put(XmRequestIdManager.CONT_TYPE, "book")
                        .put(XmRequestIdManager.IS_DUPLICATE_VIEW, "2")  //请求不相关的界面曝光埋点
                        .put("currPage", "边听边读模式")
                        .put("exploreType", "边听边读模式") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                        .createTrace();
            }

            ITingNewUserReadReward newUserReadReward = mTingReadFragment.getNewUserReadReward();
            if (newUserReadReward != null && newUserReadReward.isAllowShowRedPack()) {
                if (!isReportNewUserPack) {
                    isReportNewUserPack = true;
                    // 边听边读模式-新用户激励挂件-现金  控件曝光
                    new XMTraceApi.Trace()
                            .setMetaId(59870)
                            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                            .put("currAlbumId", String.valueOf(mTingReadFragment.getCurPlayAlbumId()))
                            .put("currTrackId", String.valueOf(mTingReadFragment.getPlayTrackId()))
                            .put(XmRequestIdManager.CONT_ID, String.valueOf(mTingReadFragment.getPlayTrackId()))
                            .put(XmRequestIdManager.CONT_TYPE, "book")
                            .put(XmRequestIdManager.IS_DUPLICATE_VIEW, "2")  //请求不相关的界面曝光埋点
                            .put("currPage", "边听边读模式")
                            .put("exploreType", "边听边读模式") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
                            .createTrace();
                }
                mIvNewUserPack.setVisibility(View.VISIBLE);
                mIvNewUserPack.setOnClickListener(v -> {
                    // 边听边读模式-新用户激励挂件-现金  点击事件
                    new XMTraceApi.Trace().click(59869) // 用户点击时上报
                            .put("currAlbumId", String.valueOf(mTingReadFragment.getCurPlayAlbumId()))
                            .put("currTrackId", String.valueOf(mTingReadFragment.getPlayTrackId()))
                            .put(XmRequestIdManager.CONT_ID, String.valueOf(mTingReadFragment.getPlayTrackId()))
                            .put(XmRequestIdManager.CONT_TYPE, "book")
                            .put("currPage", "边听边读模式")
                            .createTrace();
                    newUserReadReward.showRewardGuideView();
                });
            } else {
                isReportNewUserPack = false;
                mIvNewUserPack.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        if (v == null || mContext == null) {
            return;
        }
        int id = v.getId();
        if (id == R.id.iv_read_title_back || id == R.id.tv_title) {
            if (mTingReadFragment != null) {
                mTingReadFragment.finishCurPage(false);
            } else {
                ((Activity) mContext).onBackPressed();
            }

            if (mTingReadFragment != null) {
                // 边听边读模式-收起到肚脐眼  点击事件
                new XMTraceApi.Trace()
                        .click(61486) // 用户点击时上报
                        .put("currAlbumId", String.valueOf(mTingReadFragment.getCurPlayAlbumId()))
                        .put("currTrackId", String.valueOf(mTingReadFragment.getPlayTrackId()))
                        .put(XmRequestIdManager.CONT_ID, String.valueOf(mTingReadFragment.getPlayTrackId()))
                        .put(XmRequestIdManager.CONT_TYPE, "book")
                        .put("currPage", "边听边读模式")
                        .createTrace();
            }

        } else if (id == R.id.iv_read_menu) {
            if (mOnReadTitleListener != null) {
                mOnReadTitleListener.showMoreMenu();
            }
        } else if (id == R.id.iv_read_para_com) {
            boolean isOpen = ParaComCacheManager.INSTANCE.isOpenParaCommentFromLocal();
            ParaComCacheManager.INSTANCE.saveOpenParaCommentToLocal(!isOpen);
            setParaComIcon();

            if (ParaComCacheManager.INSTANCE.isOpenParaCommentFromLocal()) {
                ToastManager.showToast("已显示评论内容");
            } else {
                ToastManager.showToast("已隐藏评论内容");
            }

            if (mOnReadTitleListener != null) {
                mOnReadTitleListener.clickParaCom();
            }

            if (mReadChapterBean != null) {
                // 边听边读模式-段评展示开关  点击事件
                new XMTraceApi.Trace()
                        .click(60828) // 用户点击时上报
                        .put("currAlbumId", String.valueOf(mReadChapterBean.getAlbumId()))
                        .put("currTrackId", String.valueOf(mReadChapterBean.getTrackId()))
                        .put(XmRequestIdManager.CONT_ID, String.valueOf(mReadChapterBean.getTrackId()))
                        .put(XmRequestIdManager.CONT_TYPE, "book")
                        .put(XmRequestIdManager.XM_REQUEST_ID, mReadChapterBean.getRequestId())
                        .put("currPage", "边听边读模式")
                        .createTrace();
            }
        }
    }

    public interface OnReadTitleListener {

        void showMoreMenu();

        void clickParaCom();

        @TingTextToReaderManager.TingReaderEnterFrom
        int getEnterFrom();
    }


}
