<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/readsupport_color_white_121212"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/read_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/readsupport_title_bar_height"
        android:background="@drawable/readsupport_gray_underline_white_bg" />

    <ViewStub
        android:id="@+id/vs_network_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:inflatedId="@+id/vs_network_error"
        android:layout="@layout/readsupport_layout_network_exception" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:overScrollMode="never"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:paddingTop="20dp">

            <TextView
                android:id="@+id/tv_book_state"
                style="@style/readsupport_tv_base_middle_dark"
                android:layout_marginStart="15dp"
                android:textColor="@color/readsupport_color_333333_cfcfcf"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="作者努力更新中" />

            <TextView
                android:id="@+id/tv_tips"
                style="@style/readsupport_tv_base_less_middle_gray"
                android:layout_marginTop="5dp"
                android:textColor="@color/readsupport_color_666666_888888"
                app:layout_constraintLeft_toLeftOf="@id/tv_book_state"
                app:layout_constraintTop_toBottomOf="@id/tv_book_state"
                tools:text="加入书架 更新内容抢先看" />

            <TextView
                android:id="@+id/tv_add_bookshelf"
                style="@style/readsupport_tv_base_less_middle_dark"
                android:layout_width="100dp"
                android:layout_height="36dp"
                android:layout_marginTop="15dp"
                android:background="@drawable/read_solid_color_f5f5f5_2a2a2a_corner_23dp"
                android:gravity="center"
                android:text="@string/readsupport_add_bookshelf"
                android:textColor="@color/readsupport_color_333333_cfcfcf"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="@id/tv_book_state"
                app:layout_constraintTop_toBottomOf="@id/tv_tips" />

            <TextView
                android:id="@+id/tv_book_mall"
                style="@style/readsupport_tv_base_less_middle_dark"
                android:layout_width="100dp"
                android:layout_height="36dp"
                android:layout_marginTop="15dp"
                android:background="@drawable/read_solid_color_f5f5f5_2a2a2a_corner_23dp"
                android:gravity="center"
                android:text="@string/readsupport_to_book_mall"
                android:textColor="@color/readsupport_color_333333_cfcfcf"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@id/tv_add_bookshelf"
                app:layout_constraintRight_toLeftOf="@id/tv_ting"
                app:layout_constraintTop_toBottomOf="@id/tv_tips" />

            <TextView
                android:id="@+id/tv_ting"
                style="@style/readsupport_tv_base_less_middle_white"
                android:layout_width="100dp"
                android:layout_height="36dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="15dp"
                android:background="@drawable/read_solid_color_ea6347_corner_23dp"
                android:gravity="center"
                android:text="@string/readsupport_ting"
                android:textStyle="bold"
                android:visibility="invisible"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_tips" />

            <TextView
                android:id="@+id/tv_similar_title"
                style="@style/readsupport_tv_base_middle_dark"
                android:layout_width="0dp"
                android:layout_marginStart="15dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="15dp"
                android:textStyle="bold"
                android:textColor="@color/readsupport_color_333333_cfcfcf"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider_line"
                tools:text="《倒卖凶宅那几年》同类作品" />

            <View
                android:id="@+id/divider_line"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginStart="15dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="15dp"
                android:background="@color/readsupport_color_e8e8e8_2a2a2a"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_add_bookshelf" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_similar_book"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7.5dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_similar_title" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tv_bottom_hint"
        style="@style/readsupport_tv_base_less_middle_dark"
        android:layout_gravity="center"
        android:padding="20dp"
        android:text="@string/readsupport_book_end_install_qiji_app_tips"
        android:textColor="@color/readsupport_color_c98d5a"
        android:textStyle="bold" />
</LinearLayout>