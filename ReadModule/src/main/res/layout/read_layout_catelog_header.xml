<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/readsupport_color_ffffff_131313">

    <TextView
        android:id="@+id/textTitle"
        style="@style/readsupport_tv_title_ver"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="14dp"
        android:fontFamily="sans-serif-medium"
        android:paddingHorizontal="@dimen/read_home_padding_hor"
        android:text="简介"
        android:textSize="17sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/textContent"
        style="@style/readsupport_tv_sub_title_ver_666"
        android:layout_marginTop="14dp"
        android:lineSpacingMultiplier="1.2"
        android:maxLines="1000"
        android:paddingHorizontal="@dimen/read_home_padding_hor"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="@id/textTitle"
        app:layout_constraintTop_toBottomOf="@id/textTitle"
        tools:text="喜马拉雅年度巨制，有声专辑5.4亿播放，带您看遍尔虞我诈的江湖世界。您看遍尔虞我诈的江湖世界我诈的江湖世界。您看遍尔虞我诈的江湖世界……" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginTop="16dp"
        android:background="@color/readsupport_color_F6F7F8_000000"
        app:layout_constraintTop_toBottomOf="@id/textContent" />

    <TextView
        android:id="@+id/textCatalog"
        style="@style/readsupport_tv_title_ver"
        android:layout_height="48dp"
        android:fontFamily="sans-serif-medium"
        android:paddingHorizontal="@dimen/read_home_padding_hor"
        android:text="目录"
        android:textSize="17sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

    <ImageView
        android:id="@+id/imgOrder"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginRight="16dp"
        android:src="@drawable/read_host_read_ic_catalogue_inverted_order_normal"
        app:layout_constraintBottom_toBottomOf="@id/textCatalog"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/textCatalog" />


</androidx.constraintlayout.widget.ConstraintLayout>