package com.ximalaya.ting.android.record.adapter.album;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.adapter.BaseRecyclerViewAdapter;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.data.model.ai.AiAlbumInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xiaolei on 2022/5/11.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13701664636
 */
public class AiAlbumAdapter extends BaseRecyclerViewAdapter<AiAlbumInfo, RecyclerView.ViewHolder> {

    private final List<AiAlbumInfo> mList = new ArrayList<>();
    private AiAlbumAdapter.ItemClickListener mItemClickListener;

    public void addData(List<AiAlbumInfo> list) {
        if (!ToolUtil.isEmptyCollects(list)) {
            mList.addAll(list);
        }
    }

    public void clearData() {
        mList.clear();
    }

    @Override
    public AiAlbumInfo getItem(int position) {
        if (position < 0 || position > mList.size() - 1) {
            return null;
        }
        return mList.get(position);
    }

    @Override
    public void onClick(View view, AiAlbumInfo o, int position, RecyclerView.ViewHolder holder) {
        int id = view.getId();
        if (id == R.id.record_tv_open) {
            if (mItemClickListener != null) {
                mItemClickListener.onOpenClicked(position, o);
            }
        } else if (mItemClickListener != null) {
            if (o.isAiDocOpen()) {
                mItemClickListener.onItemClicked(position, o);
            }
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new AlbumViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.record_item_ai_album, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof AlbumViewHolder) {
            AlbumViewHolder vh = (AlbumViewHolder) holder;
            AiAlbumInfo item = getItem(position);
            if (item == null) {
                return;
            }
            String title = item.getAlbumTitle();
            if (title != null) {
                vh.mRecordTvTitle.setText(title);
            }
            if (item.isAiDocOpen()) {
                vh.mRecordTvOpen.setVisibility(View.INVISIBLE);
                vh.mRecordTvState.setText("已开启");
                vh.mRecordTvState.setSelected(true);
            } else {
                vh.mRecordTvOpen.setVisibility(View.VISIBLE);
                vh.mRecordTvState.setText("未开启");
                vh.mRecordTvState.setSelected(false);
            }
            ImageManager.from(vh.itemView.getContext()).displayImage(vh.mRecordRivPic, item.getAlbumCoverPath(), null);
            setClickListener(vh.itemView, item, position, vh);
            setClickListener(vh.mRecordTvOpen, item, position, vh);
        }
    }

    @Override
    public int getItemCount() {
        return mList.size();
    }

    public void setItemClickListener(AiAlbumAdapter.ItemClickListener clickListener) {
        mItemClickListener = clickListener;
    }

    public void notifyItemUpdate(AiAlbumInfo param) {
        try {
            int index = mList.indexOf(param);
            if (index >= 0 && index < mList.size()) {
                notifyItemChanged(index);
            }
        }catch (Exception ignored){}
    }

    public interface ItemClickListener {
        void onItemClicked(int position, AiAlbumInfo object);
        void onOpenClicked(int position, AiAlbumInfo object);
    }

    static class AlbumViewHolder extends RecyclerView.ViewHolder {
        private RoundImageView mRecordRivPic;
        private TextView mRecordTvOpen;
        private TextView mRecordTvTitle;
        private TextView mRecordTvState;
        public AlbumViewHolder(@NonNull View itemView) {
            super(itemView);

            mRecordRivPic = itemView.findViewById(R.id.record_riv_pic);
            mRecordTvOpen = itemView.findViewById(R.id.record_tv_open);
            mRecordTvTitle = itemView.findViewById(R.id.record_tv_title);
            mRecordTvState = itemView.findViewById(R.id.record_tv_state);
        }
    }
}
