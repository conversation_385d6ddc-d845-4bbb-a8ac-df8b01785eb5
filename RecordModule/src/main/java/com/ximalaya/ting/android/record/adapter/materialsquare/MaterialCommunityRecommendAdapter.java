package com.ximalaya.ting.android.record.adapter.materialsquare;

import android.graphics.Bitmap;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.ZoneActionRouter;
import com.ximalaya.ting.android.host.util.ZoneBundleInterceptKt;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.constants.ConstantValues;
import com.ximalaya.ting.android.record.data.model.community.Community;

import java.util.List;

/**
 * Created by wenbin.liu on 2020-02-21
 *
 * <AUTHOR>
 */
public class MaterialCommunityRecommendAdapter extends RecyclerView.Adapter<MaterialCommunityRecommendAdapter.MaterialCommunityRecommnedItemHolder> {

    private List<Community> mData;
    private BaseFragment2 mFragment;

    public MaterialCommunityRecommendAdapter(List<Community> data, BaseFragment2 fragment) {
        mData = data;
        mFragment = fragment;
    }

    @NonNull
    @Override
    public MaterialCommunityRecommnedItemHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.record_material_community_recommend_item, parent, false);
        return new MaterialCommunityRecommnedItemHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull final MaterialCommunityRecommnedItemHolder holder, int position) {
        if (holder == null || ToolUtil.isEmptyCollects(mData) || mFragment == null) {
            return;
        }
        final Community bean = mData.get(position);
        if (bean == null) {
            return;
        }
        holder.tvName.setText(bean.getName());
        ImageManager.from(mFragment.getContext()).displayImage(holder.ivCover, bean.getLogo(), R.drawable.record_community_logo_default, new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                if (bitmap != null  && mFragment.canUpdateUi()) {
                    holder.ivCover.setImageBitmap(bitmap);
                }
            }
        });
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ZoneBundleInterceptKt.afterZoneModuleLoaded(() -> {
                    try {
                        BaseFragment2 fragment = Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE).getFragmentAction().newCommunityHomepageFragment(bean.getId());
                        if (fragment != null && mFragment.getActivity() instanceof MainActivity) {
                            ((MainActivity) mFragment.getActivity()).startFragment(fragment, ConstantValues.TAG_COMMUNITY_HOME_PAGE, 0, 0);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        });
    }

    @Override
    public int getItemCount() {
        if (!ToolUtil.isEmptyCollects(mData)) {
            return mData.size();
        }
        return 0;
    }

    static class MaterialCommunityRecommnedItemHolder extends RecyclerView.ViewHolder {
        private ImageView ivCover;
        private TextView tvName;

        public MaterialCommunityRecommnedItemHolder(View itemView) {
            super(itemView);
            this.ivCover = itemView.findViewById(R.id.record_material_community_recommend_iv);
            this.tvName = itemView.findViewById(R.id.record_material_community_recommend_tv);
        }
    }
}
