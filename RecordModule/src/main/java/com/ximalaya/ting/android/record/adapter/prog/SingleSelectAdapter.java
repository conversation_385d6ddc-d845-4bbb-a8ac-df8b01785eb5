package com.ximalaya.ting.android.record.adapter.prog;

import android.content.Context;
import android.graphics.Typeface;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.constants.KaChaNoteType;
import com.ximalaya.ting.android.record.constants.StatusType;
import com.ximalaya.ting.android.record.constants.VideoType;

import java.util.List;

/**
 * Created by wenbin.liu on 2020-08-13
 *
 * <AUTHOR>
 */
public class SingleSelectAdapter extends HolderAdapter<SingleSelectAdapter.Item> {

    private int C_666666;
    private int C_FE5E3F;

    public SingleSelectAdapter(Context context, List<Item> listData) {
        super(context, listData);
        C_666666 = context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_666666_cfcfcf);
        C_FE5E3F = context.getResources().getColor(R.color.record_color_FE5E3F);
    }

    @Override
    public void onClick(View view, Item item, int position, BaseViewHolder holder) {

    }

    @Override
    public int getConvertViewId() {
        return R.layout.record_item_single_select;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new VH(convertView);
    }

    public void setSelectType(Object object) {
        if (ToolUtil.isEmptyCollects(listData)) {
            return;
        }
        if ((listData.get(0).data instanceof StatusType && object instanceof StatusType) ||
                (listData.get(0).data instanceof VideoType && object instanceof VideoType) ||
                (listData.get(0).data instanceof KaChaNoteType && object instanceof KaChaNoteType)) {
            for (Item item : listData) {
                item.isSelect = (item.data == object);
            }
        }
        notifyDataSetChanged();
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, Item item, int position) {
        if (item == null || !(holder instanceof VH)) {
            return;
        }
        VH vh = (VH)holder;
        String name = "";
        if (item.data instanceof StatusType) {
            name = ((StatusType) item.data).getName();
            if (item.count >= 0) {
                name += " " + item.count;
            }
        }  else if (item.data instanceof VideoType) {
            name = ((VideoType) item.data).getName();
        } else if (item.data instanceof KaChaNoteType) {
            name = ((KaChaNoteType)item.data).getName();
        }
        vh.tvName.setText(name);
        vh.tvName.setTextColor(item.isSelect ? C_FE5E3F : C_666666);
        vh.tvName.setTypeface(item.isSelect ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
        vh.ivSelect.setVisibility(item.isSelect ? View.VISIBLE : View.INVISIBLE);
    }

    private static class VH extends BaseViewHolder {
        private TextView tvName;
        private ImageView ivSelect;

        public VH(View itemView) {
            tvName = itemView.findViewById(R.id.record_option_name_tv);
            ivSelect = itemView.findViewById(R.id.record_select_iv);
        }
    }

    public static class Item {
        public Object data;
        public boolean isSelect;
        public int count;

        public Item(Object data, boolean isSelect, int count) {
            this.data = data;
            this.isSelect = isSelect;
            this.count = count;
        }

        public Item(Object data, boolean isSelect) {
           this(data, isSelect, 0);
        }
    }
}
