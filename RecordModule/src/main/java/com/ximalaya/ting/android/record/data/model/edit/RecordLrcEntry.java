package com.ximalaya.ting.android.record.data.model.edit;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.host.view.lrcview.LrcEntry;
import com.ximalaya.ting.android.record.R;

import java.util.List;

/**
 * Created by wenbin.liu on 2021/11/9
 *
 * <AUTHOR>
 */
public class RecordLrcEntry extends LrcEntry {

    private boolean mSelect;
    @SerializedName(value = "start")
    private long mOriginStartTime;
    @SerializedName(value = "end")
    private long mOriginEndTime;
    private int mOriginIndex;
    // 裁剪时间。指定部分删除字幕长度
    private int curTime;
    private int mCurHeight = -1;

    @SerializedName(value = "textType")
    private int textType;  // 1普通2口癖词3语气词4重复词

    @SerializedName(value = "wordVOList")
    private List<AsrProblemWordInfo> wordVOList;

    private transient TextView mTimeTv;

    private transient TextView mProblemContentTv;
    private transient ImageView mCurArrow;
    private transient TextView mContentTv;
    private transient View mRoot;

    public RecordLrcEntry(String text, long originStartTime, long originEndTime) {
        super(originStartTime, text);
        this.mOriginStartTime = originStartTime;
        this.mOriginEndTime = originEndTime;
        setLrcStyle(LrcEntry.LRC_STYLE_TIMESTAMP);
    }

    public RecordLrcEntry(RecordLrcEntry other) {
        this(other.getText(), other.getOriginStartTime(), other.getOriginEndTime());
        setSelect(other.isSelect());
        setOriginIndex(other.getOriginIndex());
        setWordVOList(other.getWordVOList());
        setTextType(other.getTextType());
    }

    public long getOriginStartTime() {
        return mOriginStartTime;
    }

    public long getOriginEndTime() {
        return mOriginEndTime;
    }

    public void setOriginStartTime(long originStartTime) {
        mOriginStartTime = originStartTime;
    }

    public void setOriginEndTime(long originEndTime) {
        mOriginEndTime = originEndTime;
    }

    public int getOriginIndex() {
        return mOriginIndex;
    }

    public void setOriginIndex(int originIndex) {
        mOriginIndex = originIndex;
    }

    public boolean isSelect() {
        return mSelect;
    }

    public void setSelect(boolean select) {
        mSelect = select;
    }

    public int getCurTime() {
        return curTime;
    }

    public void setCurTime(int curTime) {
        this.curTime = curTime;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj == this) return true;
        if (!(obj instanceof RecordLrcEntry)) return false;
        RecordLrcEntry other = (RecordLrcEntry)obj;
        return other.mOriginStartTime == this.mOriginStartTime
                && other.mOriginEndTime == this.mOriginEndTime;
    }

    @Override
    public String toString() {
        return "RecordLrcEntry{" +
                "start=" + mOriginStartTime +
                ", end=" + mOriginEndTime +
                ", index=" + mOriginIndex +
                ", text=" + getText() +
                '}';
    }

    @Override
    public void setTime(long time) {
        super.setTime(time);
        updateTimeTxt();
    }

    public void setTimeOnly(long time) {
        super.setTime(time);
    }

    public void updateTimeTxt() {
        if (mTimeTv == null) {
            return;
        }
        mTimeTv.setText(TimeHelper.toTime((double)getTime() / 1000));
    }

    public void init(Context context, int maxWidth) {
        if (mRoot == null) {
            mRoot = LayoutInflater.from(context).inflate(R.layout.record_lrc_entry_layout, null, false);
        }
        if (mTimeTv == null) {
            mTimeTv = mRoot.findViewById(R.id.record_time_tv);
            mTimeTv.setText(TimeHelper.toTime((double) getTime() / 1000));
        }
        if (mProblemContentTv == null) {
            mProblemContentTv = mRoot.findViewById(R.id.record_tv_problem_content);
        }
        if (mCurArrow == null) {
            mCurArrow = mRoot.findViewById(R.id.record_iv_arrow);
        }
        if (mContentTv == null) {
            mContentTv = mRoot.findViewById(R.id.record_content_tv);
        }
        updateContextText();
        if (mCurHeight <= 0) {
            mRoot.measure(View.MeasureSpec.makeMeasureSpec(maxWidth, View.MeasureSpec.AT_MOST),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
            mRoot.layout(0, 0, mRoot.getMeasuredWidth(), mRoot.getMeasuredHeight());
        }
        mCurHeight = mRoot.getMeasuredHeight();
    }

    public void updateContextText() {
        if (mContentTv == null || mProblemContentTv == null) {
            return;
        }
        if (textType > 1) {
            setProblemText();
        } else {
            mContentTv.setVisibility(View.VISIBLE);
            mProblemContentTv.setVisibility(View.GONE);
            mContentTv.setText((TextUtils.isEmpty(getText()) ? "（空白）" : getText()));
        }
    }

    private void setProblemText() {
        if (mContentTv == null || mProblemContentTv == null) return;
        mContentTv.setVisibility(View.GONE);
        mProblemContentTv.setVisibility(View.VISIBLE);
        int res = 0;
        int bgRes = 0;
        switch (textType) {
            case AsrProblemWordInfo.TYPE_KOU_PI:
                res = R.drawable.record_ic_asr_flag_koupi;
                bgRes = R.drawable.record_drawable_asr_koupi;
                break;
            case AsrProblemWordInfo.TYPE_YU_QI:
                res = R.drawable.record_ic_asr_flag_yuqici;
                bgRes = R.drawable.record_drawable_asr_koupi;
                break;
            case AsrProblemWordInfo.TYPE_CHONG_FU:
                res = R.drawable.record_ic_asr_flag_chongfu;
                bgRes = R.drawable.record_drawable_asr_koupi;
                break;
            case AsrProblemWordInfo.TYPE_TING_DUN:
                res = R.drawable.record_ic_asr_flag_tingdun;
                bgRes = R.drawable.record_drawable_asr_tingdun;
                break;
            default:
                break;
        }
        mProblemContentTv.setCompoundDrawablesWithIntrinsicBounds(res, 0, 0, 0);
        if (bgRes > 0) {
            mProblemContentTv.setBackgroundResource(bgRes);
        }
        if (textType == AsrProblemWordInfo.TYPE_TING_DUN) {
            mProblemContentTv.setText(TextUtils.isEmpty(getText()) ? "       （空白）" : "       " + getText());
        } else {
            mProblemContentTv.setText(TextUtils.isEmpty(getText()) ? "（空白）" : getText());
        }
    }

    public View getRoot() {
        return mRoot;
    }

    public void setContentColor(int color) {
        if (mTimeTv == null || mContentTv == null) {
            return;
        }
        mTimeTv.setTextColor(color);
//        mContentTv.setTextColor(color);
    }

    public void setCurArrow(boolean isCur) {
        if (mCurArrow == null) {
            return;
        }
        mCurArrow.setVisibility(isCur ? View.VISIBLE : View.INVISIBLE);
//        if (mProblemContentTv != null && mProblemContentTv.getVisibility() == View.VISIBLE) {
//            mProblemContentTv.setSelected(isCur);
//        }
    }


    public View getRootView() {
        return mRoot;
    }

    public void setRootView(View mRoot) {
        this.mRoot = mRoot;
    }

    public TextView getTimeTv() {
        return mTimeTv;
    }

    public void setTimeTv(TextView mTimeTv) {
        this.mTimeTv = mTimeTv;
    }

    public TextView getProblemContentTv() {
        return mProblemContentTv;
    }

    public void setProblemContentTv(TextView mProblemContentTv) {
        this.mProblemContentTv = mProblemContentTv;
    }

    public ImageView getCurArrow() {
        return mCurArrow;
    }

    public void setCurArrow(ImageView mCurArrow) {
        this.mCurArrow = mCurArrow;
    }

    public TextView getContentTv() {
        return mContentTv;
    }

    public void setContentTv(TextView mContentTv) {
        this.mContentTv = mContentTv;
    }

    public int getCurHeight() {
        return mCurHeight;
    }

    public void setCurHeight(int mCurHeight) {
        this.mCurHeight = mCurHeight;
    }


    @Override
    public int getHeight() {
        if (mCurHeight == -1) {
            return super.getHeight();
        }
        return mCurHeight;
    }

    public int getTextType() {
        return textType;
    }

    public void setTextType(int textType) {
        this.textType = textType;
    }

    public List<AsrProblemWordInfo> getWordVOList() {
        return wordVOList;
    }

    public void setWordVOList(List<AsrProblemWordInfo> wordVOList) {
        this.wordVOList = wordVOList;
    }

    public RecordLrcEntry clone() {
        return new RecordLrcEntry(this);
    }
}
