package com.ximalaya.ting.android.record.data.model.record;

import static com.ximalaya.ting.android.record.data.model.record.SourceType.DIALOGUE;
import static com.ximalaya.ting.android.record.data.model.record.SourceType.NORMAL;
import static com.ximalaya.ting.android.record.data.model.record.SourceType.TOPIC_AI_COMPERE;
import static com.ximalaya.ting.android.record.data.model.record.SourceType.TOPIC_BRIEF;
import static com.ximalaya.ting.android.record.data.model.record.SourceType.TOPIC_BRIEF_OUTLINE;
import static com.ximalaya.ting.android.record.data.model.record.SourceType.TOPIC_WORD_OUTLINE;
import static com.ximalaya.ting.android.record.data.model.record.SourceType.TT_PRODUCE;
import static com.ximalaya.ting.android.record.data.model.record.SourceType.UPLOAD;

import androidx.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * Created by xiaolei on 2023/12/27.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13701664636
 */
@Retention(RetentionPolicy.SOURCE)
@StringDef({UPLOAD, NORMAL, TOPIC_BRIEF, TOPIC_AI_COMPERE, TOPIC_BRIEF_OUTLINE, TOPIC_WORD_OUTLINE, DIALOGUE, TT_PRODUCE})
public @interface SourceType {
    public static final String UPLOAD = "upload";
    public static final String NORMAL = "normal";
    public static final String TOPIC_BRIEF = "topic_brief";
    public static final String TOPIC_BRIEF_OUTLINE = "topic_brief_outline";
    public static final String TOPIC_AI_COMPERE = "topic_AI_compere";
    public static final String TOPIC_WORD_OUTLINE = "topic_word_outline";
    public static final String DIALOGUE = "dialogue";
    public static final String TT_PRODUCE = "tts_produce";
}
