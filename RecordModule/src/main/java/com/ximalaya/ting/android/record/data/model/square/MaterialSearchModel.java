package com.ximalaya.ting.android.record.data.model.square;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.ximalaya.ting.android.host.model.materialsquare.DubMaterialBean;

import java.util.List;

/**
 * Created by zhangkaikai on 2018/10/18.
 *
 * <AUTHOR>
 */
public class MaterialSearchModel {

    /**
     * pageSize : 30
     * pageNo : 1
     * totalPage : 1
     * totalCount : 4
     * result : [{"materialId":100216,"name":"新增素材测试","surfaceUrl":"http://fdfs.test.ximalaya.com/group1/M00/E5/DD/wKgD3lttNM6Aapk6AAAr4kD9ZDI37.jpeg","description":"","tagMap":null,"collected":null,"demoTrackId":838186,"pictureCount":1},{"materialId":100408,"name":"新增定时任务","surfaceUrl":"http://fdfs.test.ximalaya.com/group1/M01/4A/A9/wKgDplu9z-eAEb2NAAAmWx-jABc040.png","description":"","tagMap":null,"collected":null,"demoTrackId":870663,"pictureCount":3},{"materialId":100220,"name":"又测试新增了呦呦呦33","surfaceUrl":"http://fdfs.test.ximalaya.com/group1/M01/3C/ED/wKgDplttRDmAX506AADuhj8rbWM257.jpg","description":"","tagMap":null,"collected":null,"demoTrackId":854018,"pictureCount":2},{"materialId":100409,"name":"新增定时任务未上架前编辑时间","surfaceUrl":"http://fdfs.test.ximalaya.com/group1/M01/4A/A9/wKgDplu90DiAb5SPAABGDC3RVEQ458.png","description":"","tagMap":null,"collected":null,"demoTrackId":870625,"pictureCount":3}]
     */

    private int pageSize;
    private int pageNo;
    private int totalPage;
    private int totalCount;
    private List<DubMaterialBean> result;

    public static MaterialSearchModel parseData(String content) {
        if (TextUtils.isEmpty(content)) {
            return null;
        }
        MaterialSearchModel model = null;
        Gson gson = new Gson();
        JsonObject jsonObject;
        try {
            jsonObject = new Gson().fromJson(content, JsonObject.class);
            if (jsonObject == null) {
                return null;
            }
            model = gson.fromJson(
                    gson.toJson(jsonObject.getAsJsonObject("data")), MaterialSearchModel.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return model;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<DubMaterialBean> getResult() {
        return result;
    }

    public void setResult(List<DubMaterialBean> result) {
        this.result = result;
    }

    public boolean hasMore() {
        return totalCount >= pageSize;
    }
}
