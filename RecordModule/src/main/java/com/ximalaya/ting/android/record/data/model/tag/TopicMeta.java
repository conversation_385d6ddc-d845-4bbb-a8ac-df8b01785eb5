package com.ximalaya.ting.android.record.data.model.tag;

import com.ximalaya.ting.android.record.R;

import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Rn传递到录音页的话题元数据
 *
 * Created by xiaole<PERSON> on 5/27/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13701664636
 */
public class TopicMeta implements ITopicMeta{
    private long id;
    private String title;
    private long hot;
    private long tagId;
    private long metadataId;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Override
    public long getPId() {
        return getTagId();
    }

    @NotNull
    public String getTitle() {
        return title==null?"":title;
    }

    @NotNull
    @Override
    public String getHotString() {
        String sHot = "";
        if (hot == 0L) {
            return sHot;
        }
        try {
            if (Math.abs(hot) > 9999) {
                sHot = BigDecimal.valueOf(hot).divide(new BigDecimal(10000), 1, RoundingMode.HALF_UP).toPlainString() + "万";
            } else {
                sHot = String.valueOf(hot);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return String.format("%s 热度", sHot);
    }

    @Override
    public int getIcon() {
        return R.drawable.record_ic_track_topic;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public long getHot() {
        return hot;
    }

    public void setHot(long hot) {
        this.hot = hot;
    }

    public long getTagId() {
        return tagId;
    }

    public void setTagId(long tagId) {
        this.tagId = tagId;
    }

    public long getMetadataId() {
        return metadataId;
    }

    public void setMetadataId(long metadataId) {
        this.metadataId = metadataId;
    }
}
