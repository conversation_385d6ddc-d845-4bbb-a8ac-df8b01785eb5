package com.ximalaya.ting.android.record.fragment;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.ColorInt;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants.Group_tob;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.album.AlbumClickInfo;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.view.layout.FlowLayout;
import com.ximalaya.ting.android.host.view.list.NotifyingScrollView;
import com.ximalaya.ting.android.host.view.richtext.RichText;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.constants.UrlConstantsForRecord;
import com.ximalaya.ting.android.record.fragment.album.CreateAlbumFragment;
import com.ximalaya.ting.android.record.fragment.dialog.AlbumClickRateExplanationDialogFragment;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR> on 2018/5/7.
 */

public class AlbumClickRateImproveFragment extends BaseFragment2 {

    private static final String TAG = AlbumClickRateImproveFragment.class.getSimpleName();
    private static final String MORE_ALBUM_COVER_EXAMPLE_URL = "http://hybrid.ximalaya" +
            ".com/anchor-skill/index/collect?themeid=14";

    private static final String BUNDLE_KEY_ALBUM_ID = "album_id";
    private static final String BUNDLE_KEY_ALBUM_CLICK_INFO = "album_click_info";

    private static final String INTRO_EXAMPLE = "内容简介：《睡前读给宝贝听》将精选出适合2-8对宝宝及幼儿聆听的儿歌、诗歌、故事等。" +
            "在学习语言的启蒙阶段，助宝贝一臂之力！\n" +
            "\n" +
            "播出时间：每周二、周四、周六20:00\n" +
            "\n" +
            "作者/主播简介：" +
            "乐哥（卢顺）毕业于中央戏剧学院相声班。曾任中央电视台《动画城》、《大风车》、《绿野仙踪》等栏目主持人、演员、配音演员。\n" +
            "\n" +
            "专辑目录/大纲：（教育培训或有声书类建议填写）";

    private AlbumClickInfo mAlbumClickInfo;
    private AlbumM mAlbumM;
    private ImageView mIvBackBtn;
    private TextView mIvTitle;
    private ImageView mIvTopBg;
    private NotifyingScrollView mScrollViewContent;
    private ImageView mIvCover;
    private TextView mTvAlbumTitle;
    private TextView mTvAlbumSubTitle;
    private TextView mTvPlayCount;
    private TextView mTvTrackCount;
    private TextView mTvClickRate;
    private DecimalFormat mRateFormat;
    private TextView mTvRelativeClickRateLabel;
    private TextView mTvRelativeClickRate;
    private ImageView mIvMyAlbumCover;
    private ImageView mIvAlbumCoverExample;
    private LinearLayout mLlMoreAlbumCoverExample;
    private TextView mTvMyAlbumTitle;
    private LinearLayout mLlAlbumTitleExample1;
    private TextView mTvAlbumTitleExample1;
    private LinearLayout mLLAlbumTitleExample2;
    private TextView mTvAlbumTitleExample2;
    private TextView mTvMyAlbumSubtitle;
    private LinearLayout mLlAlbumSubtitleExample1;
    private TextView mTvAlbumSubtitleExample1;
    private LinearLayout mLLAlbumSubtitleExample2;
    private TextView mTvAlbumSubtitleExample2;
    private TextView mTvNoTag;
    private FlowLayout mFlowLayoutTag;
    private TextView mTvMyIntro;
    private TextView mTvIntroExample;
    private View mVTitleBar;
    private long mAlbumId;
    private boolean isFirstEnter = true;

    public static AlbumClickRateImproveFragment newInstance(long albumId, AlbumClickInfo albumClickInfo) {
        AlbumClickRateImproveFragment fragment = new AlbumClickRateImproveFragment();
        Bundle arguments = new Bundle();
        arguments.putLong(BUNDLE_KEY_ALBUM_ID, albumId);
        arguments.putParcelable(BUNDLE_KEY_ALBUM_CLICK_INFO, albumClickInfo);
        fragment.setArguments(arguments);
        return fragment;
    }

    public static AlbumClickRateImproveFragment newInstance(long albumId) {
        return newInstance(albumId, null);
    }

    @Override
    protected String getPageLogicName() {
        if (getClass() != null) {
            return getClass().getSimpleName();
        }
        return "";
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle(R.string.record_improve_album_click_rate);
        mIvTopBg = (ImageView) findViewById(R.id.record_iv_top_bg);
        mScrollViewContent = (NotifyingScrollView) findViewById(R.id.record_scrollview_content);

        LinearLayout llContent = (LinearLayout) findViewById(R.id.record_ll_content);
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) llContent.getLayoutParams();
        layoutParams.topMargin = layoutParams.topMargin + BaseUtil.getStatusBarHeight(getContext());
        llContent.setLayoutParams(layoutParams);

        mIvCover = (ImageView) findViewById(R.id.record_iv_cover);
        mTvAlbumTitle = (TextView) findViewById(R.id.record_tv_album_title);
        mTvAlbumSubTitle = (TextView) findViewById(R.id.record_tv_album_subtitle);
        mTvPlayCount = (TextView) findViewById(R.id.record_tv_play_count);
        mTvTrackCount = (TextView) findViewById(R.id.record_tv_track_count);
        mTvClickRate = (TextView) findViewById(R.id.record_tv_click_rate);
        mTvRelativeClickRateLabel = (TextView) findViewById(R.id.record_tv_relative_rate_label);
        mTvRelativeClickRate = (TextView) findViewById(R.id.record_tv_relative_rate);
        mIvMyAlbumCover = (ImageView) findViewById(R.id.record_iv_my_album_cover);
        mIvAlbumCoverExample = (ImageView) findViewById(R.id.record_iv_album_cover_example);
        mLlMoreAlbumCoverExample = (LinearLayout) findViewById(R.id.record_ll_more_album_cover_example);
        mTvMyAlbumTitle = (TextView) findViewById(R.id.record_tv_my_album_title);
        mLlAlbumTitleExample1 = (LinearLayout) findViewById(R.id.record_ll_album_title_example_1);
        mTvAlbumTitleExample1 = (TextView) findViewById(R.id.record_tv_album_title_example_1);
        mLLAlbumTitleExample2 = (LinearLayout) findViewById(R.id.record_ll_album_title_example_2);
        mTvAlbumTitleExample2 = (TextView) findViewById(R.id.record_tv_album_title_example_2);
        mTvMyAlbumSubtitle = (TextView) findViewById(R.id.record_tv_my_album_subtitle);
        mLlAlbumSubtitleExample1 = (LinearLayout) findViewById(R.id.record_ll_album_subtitle_example_1);
        mTvAlbumSubtitleExample1 = (TextView) findViewById(R.id.record_tv_album_subtitle_example_1);
        mLLAlbumSubtitleExample2 = (LinearLayout) findViewById(R.id.record_ll_album_subtitle_example_2);
        mTvAlbumSubtitleExample2 = (TextView) findViewById(R.id.record_tv_album_subtitle_example_2);
        mTvNoTag = (TextView) findViewById(R.id.record_tv_no_tag);
        mFlowLayoutTag = (FlowLayout) findViewById(R.id.record_flowlayout_tag);
        mTvMyIntro = (TextView) findViewById(R.id.record_tv_my_intro);
        mTvIntroExample = (TextView) findViewById(R.id.record_tv_intro_example);
        mVTitleBar = findViewById(R.id.record_title_bar);

        adjustAlbumCovetItemSize();

        mLlMoreAlbumCoverExample.setOnClickListener(mOnClickListener);
        mScrollViewContent.setOnScrollChangedListener(mOnScrollChangedListener);
        findViewById(R.id.record_tv_edit_intro_btn).setOnClickListener(mOnClickListener);
        findViewById(R.id.record_tv_edit_cover_btn).setOnClickListener(mOnClickListener);
        findViewById(R.id.record_tv_edit_title_btn).setOnClickListener(mOnClickListener);
        findViewById(R.id.record_tv_edit_subtitle_btn).setOnClickListener(mOnClickListener);
        findViewById(R.id.record_tv_edit_tag_btn).setOnClickListener(mOnClickListener);
        findViewById(R.id.record_tv_click_rate_label).setOnClickListener(mOnClickListener);

        changeTitleBarAndStatusBarColor(true);

        new UserTracking().setItem("优化专辑页")
                .setUserId(UserInfoMannage.getUid())
                .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_VIEW_ITEM);
    }

    private void adjustAlbumCovetItemSize() {
        int width = (BaseUtil.getScreenWidth(getContext())
                - 2 * getResourcesSafe().getDimensionPixelSize(R.dimen
                .record_fra_album_click_rate_improve_content_margin)
                - 4 * getResourcesSafe().getDimensionPixelSize(R.dimen
                .record_fra_album_click_rate_improve_cover_item_margin))
                / 3;
        //noinspection SuspiciousNameCombination,UnnecessaryLocalVariable
        int height = width;
        ViewGroup.LayoutParams lpMyAlbumCover = mIvMyAlbumCover.getLayoutParams();
        lpMyAlbumCover.width = width;
        lpMyAlbumCover.height = height;
        mIvMyAlbumCover.setLayoutParams(lpMyAlbumCover);

        ViewGroup.LayoutParams lpAlbumCoverExample = mIvAlbumCoverExample.getLayoutParams();
        lpAlbumCoverExample.width = width;
        lpAlbumCoverExample.height = height;
        mIvAlbumCoverExample.setLayoutParams(lpAlbumCoverExample);

        ViewGroup.LayoutParams lpLlMoreAlbumCoverExample = mLlMoreAlbumCoverExample.getLayoutParams();
        lpLlMoreAlbumCoverExample.width = width;
        lpLlMoreAlbumCoverExample.height = height;
        mLlMoreAlbumCoverExample.setLayoutParams(lpLlMoreAlbumCoverExample);
    }

    @Override
    protected void loadData() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        Bundle arguments = getArguments();

        if (arguments != null && arguments.containsKey(BUNDLE_KEY_ALBUM_CLICK_INFO)) {
            mAlbumClickInfo = arguments.getParcelable(BUNDLE_KEY_ALBUM_CLICK_INFO);
        }
        if (mAlbumClickInfo == null) {
            mAlbumClickInfo = new AlbumClickInfo(null);
            mAlbumClickInfo.setClickRate(-Double.MAX_VALUE);
            mAlbumClickInfo.setComparedCategoryRate(-Double.MAX_VALUE);
        }
        if (arguments != null && arguments.containsKey(BUNDLE_KEY_ALBUM_ID) && (arguments.getLong(BUNDLE_KEY_ALBUM_ID) > 0)) {
            mAlbumId = arguments.getLong(BUNDLE_KEY_ALBUM_ID);
            loadAlbumData(mAlbumId);
        } else {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.record_fra_album_click_rate_improve;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.record_title_bar;
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        mIvBackBtn = (ImageView) titleBar.getActionView(TitleBar.ActionType.BACK);
        mIvTitle = (TextView) titleBar.getActionView(TitleBar.ActionType.TITLE);
    }

    @Override
    protected boolean darkStatusBar() {
        return false;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        if (!isFirstEnter) {
            loadAlbumData(mAlbumId);
        }
        isFirstEnter = false;
    }

    private void loadAlbumData(long albumId) {
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
        params.put("isQueryTagList", "true");
        CommonRequestM.getAlbumSimpleInfo(params, new IDataCallBack<AlbumM>() {
            @Override
            public void onSuccess(@Nullable AlbumM albumM) {
                if (canUpdateUi()) {
                    if (albumM != null) {
                        onPageLoadingCompleted(LoadCompleteType.OK);
                        mAlbumM = albumM;
                        // 这个接口中增加了clickInfo字段
                        if (mAlbumM.getClickInfo() != null) {
                            mAlbumClickInfo = mAlbumM.getClickInfo();
                        }
                        updateUI();
                    } else if (mAlbumM == null) {
                        onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                if (mAlbumM == null && canUpdateUi()) {
                    onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
                }
                Logger.e(TAG, "Failed to load album info due to error " + code + " (" + message + ")");
            }
        }, true);
    }

    private void updateUI() {
        if (mAlbumM == null) {
            return;
        }

        mScrollViewContent.setVisibility(View.VISIBLE);
        updateTopBg();
        ImageManager.from(getContext()).displayImage(this, mIvCover, mAlbumM.getValidCover()
                , R.drawable.record_default_album_cover_rounded);
        mTvAlbumTitle.setText(mAlbumM.getAlbumTitle());
        if (!TextUtils.isEmpty(mAlbumM.getCustomTitle())) {
            mTvAlbumSubTitle.setText(mAlbumM.getCustomTitle());
        } else {
            mTvAlbumSubTitle.setText(R.string.record_you_have_not_set_subtitle);
        }
        mTvPlayCount.setText(StringUtil.getFriendlyNumStr(mAlbumM.getPlayCount()));
        String trackCountStr = StringUtil.getFriendlyNumStr(mAlbumM.getIncludeTrackCount()) + "集";
        mTvTrackCount.setText(trackCountStr);

        updateClickRateInfo();

        ImageManager.from(getContext()).displayImage(mIvMyAlbumCover, mAlbumM.getValidCover()
                , R.drawable.record_default_album_cover_rounded, mIvMyAlbumCover.getWidth(), mIvMyAlbumCover
                        .getHeight());

        updateMyAlbumTitleAndExampleUI();
        updateMyAlbumSubtitleAndExampleUI();
        updateMyTagUI();

        updateIntro();
    }

    private void updateIntro() {
        mTvIntroExample.setText(INTRO_EXAMPLE);
        if (!TextUtils.isEmpty(mAlbumM.getIntroRich())) {
            RichText.from(getContext(), mAlbumM.getIntroRich())
                    .async(true)
                    .setIsloadSmail(NetworkType.isConnectMOBILE(getContext()))
                    .into(mTvMyIntro);
            mTvMyIntro.setTextColor(getResourcesSafe().getColor(R.color.record_color_666666));
        } else {
            mTvMyIntro.setText(R.string.record_you_have_not_set_intro);
            mTvMyIntro.setTextColor(getResourcesSafe().getColor(R.color.record_color_999999));
        }
    }

    private void updateTopBg() {
        mIvTopBg.setTag(com.ximalaya.ting.android.framework.R.id.framework_blur_image, true);
        mIvTopBg.setTag(com.ximalaya.ting.android.framework.R.id.framework_blur_radius, 50);
        mIvTopBg.setTag(com.ximalaya.ting.android.framework.R.id.framework_blur_lightness, 0);
        final int targetWidth = mIvTopBg.getWidth();
        final int targetHeight = mIvTopBg.getHeight();
        // 需求：在模糊之前，先把图片放大10倍，然后只取中间位置。
        // 但是，先放大再取中间部分，担心占用太大内存。因此先取需要中间位置，再放大。
        ImageManager.Transformation transformation = new ImageManager.Transformation() {
            @Override
            public Bitmap transfrom(Bitmap source) {
                if (source != null && targetWidth > 0 && targetHeight > 0) {
                    int sourceWidth = source.getWidth();
                    int sourceHeight = source.getHeight();
                    int scale = 10; // 放大倍数
                    int targetWidthBeforeScale = Math.min(targetWidth / scale, sourceWidth); // 不能超出source的大小
                    int targetHeightBeforeScale = Math.min(targetHeight / scale, sourceHeight);
                    int startX = (sourceWidth - targetWidthBeforeScale) / 2;
                    int startY = (sourceHeight - targetHeightBeforeScale) / 2;
                    Bitmap clippedBitmap = Bitmap.createBitmap(source, startX, startY, targetWidthBeforeScale
                            , targetHeightBeforeScale);
                    Bitmap targetBitmap = Bitmap.createScaledBitmap(clippedBitmap, targetWidth, targetHeight, false);
                    clippedBitmap.recycle();
                    return targetBitmap;
                }
                return null;
            }

            @Nullable
            @Override
            public String key() {
                return null;
            }
        };
        ImageManager.from(getContext()).displayImage(mIvTopBg, mAlbumM.getValidCover()
                , -1, targetWidth, targetHeight, null, transformation);
    }

    private void updateClickRateInfo() {
        if (mRateFormat == null) {
            mRateFormat = new DecimalFormat("#.#");
        }
        String clickRateStr = (mAlbumClickInfo.getClickRate() != -Double.MAX_VALUE)
                ? mRateFormat.format(mAlbumClickInfo.getClickRate()) + "%" : "--";
        mTvClickRate.setText(clickRateStr);
        String relativeRateStr = (mAlbumClickInfo.getComparedCategoryRate() != -Double.MAX_VALUE)
                ? mRateFormat.format(Math.abs(mAlbumClickInfo.getComparedCategoryRate())) + "%" : "--";
        mTvRelativeClickRate.setText(relativeRateStr);
        mTvRelativeClickRate.setVisibility(View.VISIBLE);
        if (mAlbumClickInfo.getComparedCategoryRate() > 0) {
            mTvRelativeClickRateLabel.setText(R.string.record_click_rate_higher_than_other_of_same_category);
            mTvRelativeClickRate.setCompoundDrawablesWithIntrinsicBounds(R.drawable
                    .record_ic_big_arrow_up_album_click_rate, 0, 0, 0);
            mTvRelativeClickRate.setTextColor(getResourcesSafe().getColor(R.color.record_color_f86442));
        } else if (mAlbumClickInfo.getComparedCategoryRate() < 0) {
            mTvRelativeClickRateLabel.setText(R.string.record_click_rate_lower_than_other_of_same_category);
            mTvRelativeClickRate.setCompoundDrawablesWithIntrinsicBounds(R.drawable
                    .record_ic_big_arrow_down_album_click_rate, 0, 0, 0);
            mTvRelativeClickRate.setTextColor(getResourcesSafe().getColor(R.color.record_color_5184fb));
        } else {
            mTvRelativeClickRateLabel.setText(R.string.record_click_rate_same_with_other_of_same_category);
            mTvRelativeClickRate.setVisibility(View.GONE);
        }
    }

    private void changeTitleBarAndStatusBarColor(boolean isLight) {
        if (isLight) {
            mIvBackBtn.setImageResource(R.drawable.record_btn_white_back_selector);
            mIvTitle.setTextColor(Color.WHITE);
            StatusBarManager.setStatusBarColor(getWindow(), false);
        } else {
            mIvBackBtn.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_btn_orange_back_selector);
            mIvTitle.setTextColor(Color.BLACK);
            StatusBarManager.setStatusBarColor(getWindow(), true);
        }
    }

    private void updateMyAlbumTitleAndExampleUI() {
        mTvMyAlbumTitle.setText(mAlbumM.getAlbumTitle());
        String s = ConfigureCenter.getInstance().getString(Group_tob.GROUP_NAME, Group_tob.ITEM_EXAMPLE_TITLE, null);
        if (!TextUtils.isEmpty(s)) {
            String[] exampleTitles = s.split(";");
            if (exampleTitles.length >= 1) {
                mLlAlbumTitleExample1.setVisibility(View.VISIBLE);
                mTvAlbumTitleExample1.setText(exampleTitles[0]);
            }
            if (exampleTitles.length >= 2) {
                mLLAlbumTitleExample2.setVisibility(View.VISIBLE);
                mTvAlbumTitleExample2.setText(exampleTitles[1]);
            }
        }
    }

    private void updateMyAlbumSubtitleAndExampleUI() {
        if (!TextUtils.isEmpty(mAlbumM.getCustomTitle())) {
            mTvMyAlbumSubtitle.setText(mAlbumM.getCustomTitle());
            mTvMyAlbumSubtitle.setTextColor(getResourcesSafe().getColor(R.color.record_color_666666));
        } else {
            mTvMyAlbumSubtitle.setText(R.string.record_you_have_not_set_subtitle);
            mTvMyAlbumSubtitle.setTextColor(getResourcesSafe().getColor(R.color.record_color_999999));
        }
        String s = ConfigureCenter.getInstance().getString(Group_tob.GROUP_NAME, Group_tob.ITEM_EXAMPLE_CUSTOMTITLE, null);
        if (!TextUtils.isEmpty(s)) {
            String[] exampleSubtitles = s.split(";");
            if (exampleSubtitles.length >= 1) {
                mLlAlbumSubtitleExample1.setVisibility(View.VISIBLE);
                mTvAlbumSubtitleExample1.setText(exampleSubtitles[0]);
            }
            if (exampleSubtitles.length >= 2) {
                mLLAlbumSubtitleExample2.setVisibility(View.VISIBLE);
                mTvAlbumSubtitleExample2.setText(exampleSubtitles[1]);
            }
        }
    }

    private void updateMyTagUI() {
        String tags = mAlbumM.getAlbumTags();
        if (TextUtils.isEmpty(tags)) {
            mTvNoTag.setVisibility(View.VISIBLE);
            mFlowLayoutTag.setVisibility(View.INVISIBLE);
        } else {
            mTvNoTag.setVisibility(View.INVISIBLE);
            mFlowLayoutTag.setVisibility(View.VISIBLE);

            new ParseTagsTask(tags, new IDataCallBack<List<String>>() {
                @Override
                public void onSuccess(@Nullable List<String> object) {
                    if (canUpdateUi()) {
                        if (object != null && object.size() > 0) {
                            addTagViews(object);
                        } else {
                            mTvNoTag.setVisibility(View.VISIBLE);
                            mFlowLayoutTag.setVisibility(View.INVISIBLE);
                        }
                    }
                }

                @Override
                public void onError(int code, String message) {

                }
            }).myexec();
        }
    }

    private void addTagViews(List<String> tags) {
        if (tags == null || tags.size() <= 0) {
            return;
        }
        mFlowLayoutTag.removeAllViews();
        for (String tag : tags) {
            TextView tvTag = (TextView) LayoutInflater.from(getContext())
                    .inflate(R.layout.record_view_tag, mFlowLayoutTag, false);
            tvTag.setText(tag);
            mFlowLayoutTag.addView(tvTag);
        }
    }

    private void gotoAlbumEditFragment(int gotoWhichSubItemEditPage) {
        // 我们这里的接口没有isPublic数据，默认先设为public。专辑编辑页里面还回再请求数据的。
        mAlbumM.setPublic(true);
        startFragment(CreateAlbumFragment.newInstance(mAlbumM, gotoWhichSubItemEditPage
                , CreateAlbumFragment.FROM_IMPROVE_ALBUM_CLICK_RATE));
    }

    private void showClickRateExplanationDialog() {
        new AlbumClickRateExplanationDialogFragment().show(getChildFragmentManager(), null);
    }

    private View.OnClickListener mOnClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (!OneClickHelper.getInstance().onClick(v)) {
                return;
            }
            int i = v.getId();
            if (i == R.id.record_ll_more_album_cover_example) {
                BaseFragment mWebFragment = NativeHybridFragment
                        .newInstance(MORE_ALBUM_COVER_EXAMPLE_URL, true);
                startFragment(mWebFragment);
            } else if (i == R.id.record_tv_edit_intro_btn) {
                // 目前手机端还不支持编辑简介，引导到电脑端编辑
                String richIntro = mAlbumM.getIntroRich();
                String intro = mAlbumM.getIntro();
                if (checkIsRich(richIntro, intro)) {
                    String url = ConfigureCenter.getInstance().getString(Group_tob.GROUP_NAME,
                            Group_tob.ITEM_EDIT_ALBUM_BRIEF, UrlConstantsForRecord.getInstance().getEditIntroductionUrl());
                    startFragment(NativeHybridFragment
                            .newInstance(url, true));
                } else {
                    gotoAlbumEditFragment(CreateAlbumFragment.GOTO_INTRO_EDIT_PAGE);
                }
                new UserTracking().setSrcPage("优化专辑页")
                        .setSrcModule("页面按钮")
                        .setItem("button")
                        .setItemId("修改简介")
                        .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            } else if (i == R.id.record_tv_edit_cover_btn) {
                gotoAlbumEditFragment(CreateAlbumFragment.GOTO_COVER_EDIT_PAGE);
                new UserTracking().setSrcPage("优化专辑页")
                        .setSrcModule("页面按钮")
                        .setItem("button")
                        .setItemId("更换封面")
                        .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            } else if (i == R.id.record_tv_edit_title_btn) {
                gotoAlbumEditFragment(CreateAlbumFragment.GOTO_TITLE_EDIT_PAGE);
                new UserTracking().setSrcPage("优化专辑页")
                        .setSrcModule("页面按钮")
                        .setItem("button")
                        .setItemId("修改标题")
                        .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            } else if (i == R.id.record_tv_edit_subtitle_btn) {
                gotoAlbumEditFragment(CreateAlbumFragment.GOTO_SUBTITLE_EDIT_PAGE);
                new UserTracking().setSrcPage("优化专辑页")
                        .setSrcModule("页面按钮")
                        .setItem("button")
                        .setItemId("修改副标题")
                        .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            } else if (i == R.id.record_tv_edit_tag_btn) {
                gotoAlbumEditFragment(CreateAlbumFragment.GOTO_TAG_EDIT_PAGE);
                new UserTracking().setSrcPage("优化专辑页")
                        .setSrcModule("页面按钮")
                        .setItem("button")
                        .setItemId("修改标签")
                        .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
            } else if (i == R.id.record_tv_click_rate_label) {
                showClickRateExplanationDialog();
            }
        }
    };

    private boolean checkIsRich(String richIntro, String intro) {
        if (android.text.TextUtils.isEmpty(richIntro)) {
            return false;
        }

        // 手机端上传时候填简介会同时填这两个字段
        if (!android.text.TextUtils.isEmpty(intro) && richIntro.equals(intro)) {
            return false;
        }

        // 包含一些基础的HTML标签
        return richIntro.contains("<p") && richIntro.contains("</p>")
            || richIntro.contains("<div") && richIntro.contains("</div>")
            || richIntro.contains("<span") && richIntro.contains("</span>");

    }

    private NotifyingScrollView.OnScrollChangedListener mOnScrollChangedListener
            = new NotifyingScrollView.OnScrollChangedListener() {
        @Override
        public void onScrollChanged(ScrollView v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
            Logger.i(TAG, "scrollX: " + scrollX + ", scrollY: " + scrollY + ", oldScrollX: "
                    + oldScrollX + ", oldScrollY: " + oldScrollY);
            scrollY = (scrollY < 0) ? 0 : scrollY;
            if (scrollY <= 0) {
                changeTitleBarAndStatusBarColor(true);
            } else {
                changeTitleBarAndStatusBarColor(false);
            }
            int titleBarHeight = mVTitleBar.getHeight();
            float alpha = Math.min(scrollY * 1f / titleBarHeight, 1);
            @ColorInt int color = (int) (alpha * 255.0f + 0.5f) << 24 | 0x00ffffff;
            mVTitleBar.setBackgroundColor(color);
        }
    };

    private static class ParseTagsTask extends MyAsyncTask<Void, Void, List<String>> {
        private String mTagString;
        private IDataCallBack<List<String>> mCallBack;

        ParseTagsTask(String tagString, IDataCallBack<List<String>> callBack) {
            mTagString = tagString;
            mCallBack = callBack;
        }

        @Override
        protected List<String> doInBackground(Void... voids) {
            List<String> tagStrings = null;
            List<String> tags = new ArrayList<>();
            try {
                Gson gson = new Gson();
                tagStrings = gson.fromJson(mTagString, new TypeToken<List<String>>() {
                }.getType());
            } catch (JsonSyntaxException e) {
                e.printStackTrace();
            }

            if (tagStrings != null) {
                for (String string : tagStrings) {
                    JSONObject object;
                    try {
                        object = new JSONObject(string);
                        JSONArray value = object.optJSONArray("value");

                        if (value != null) {
                            for (int i = 0; i < value.length(); i++) {
                                tags.add(value.getString(i));
                            }
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
            return tags;
        }

        @Override
        protected void onPostExecute(List<String> strings) {
            if (mCallBack != null) {
                mCallBack.onSuccess(strings);
            }
        }
    }
}
