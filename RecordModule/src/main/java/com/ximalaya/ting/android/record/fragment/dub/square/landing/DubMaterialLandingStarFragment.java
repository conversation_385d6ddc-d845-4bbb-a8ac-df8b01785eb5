package com.ximalaya.ting.android.record.fragment.dub.square.landing;

import android.Manifest;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Parcelable;
import androidx.viewpager.widget.ViewPager.SimpleOnPageChangeListener;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.FrameLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import com.ximalaya.ting.android.framework.adapter.TabCommonAdapter;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.model.play.DubTransferModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.view.other.MyViewPager;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.data.model.square.MaterialLandingInfo;
import com.ximalaya.ting.android.record.fragment.dub.DubMaterialDownloadFragment;
import com.ximalaya.ting.android.record.fragment.dub.ImageDubFragment;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by zhangkaikai on 2020-02-20.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 */
public class DubMaterialLandingStarFragment extends BaseFragment2 implements RadioGroup.OnCheckedChangeListener {

    public static final String BUNDLE_KEY_GENERAL_RANK_INFO = "bundle_key_general_rank_info";
    public static final String BUNDLE_KEY_RANK_PAGE_STATE = "bundle_key_rank_page_state";
    public static final String BUNDLE_KEY_TEMPLATE_ID = "bundle_key_template_id";

    private RadioGroup mRadioGroup;
    private MyViewPager mViewPager;

    private int mLastTabPos;
    private int mLastCheckId;
    private boolean mIsUnderTab;
    private MaterialLandingInfo mLandingInfo;
    private ITransActionListener mActionListener;
    private RadioButton mWeekRadioButton;
    private RadioButton mGeneralRadioButton;

    @Override
    protected String getPageLogicName() {
        return this.getClass().getSimpleName();
    }

    public ITransActionListener getActionListener() {
        return mActionListener;
    }

    public void setActionListener(ITransActionListener actionListener) {
        mActionListener = actionListener;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        initParams();
        initViewPager();
        LayoutInflater.from(mContext).inflate(mIsUnderTab ? R.layout.record_material_landing_star_tab_big
                : R.layout.record_material_landing_star_tab_small,
            (FrameLayout) findViewById(R.id.record_dub_material_landing_star_content_view), true);
        mRadioGroup = findViewById(R.id.record_dub_material_landing_star_rg);
        mWeekRadioButton = findViewById(R.id.record_dub_material_landing_star_week_rb);
        mGeneralRadioButton = findViewById(R.id.record_dub_material_landing_star_general_rb);
        mRadioGroup.setOnCheckedChangeListener(this);
    }

    private void initParams() {
        if (mActionListener == null && getParentFragment() instanceof DubMaterialLandingContentFragment) {
            setActionListener(((DubMaterialLandingContentFragment) getParentFragment()).getActionListener());
        }

        Bundle bundle = getArguments();
        if (bundle == null) {
            return;
        }
        mLandingInfo =
            (MaterialLandingInfo) bundle.getSerializable(DubMaterialLandingFragment.BUNDLE_KEY_GENERAL_LANDING_INFO);
        mIsUnderTab = bundle.getBoolean(DubMaterialLandingContentFragment.BUNDLE_KEY_IS_UNDER_TAB, false);
    }

    private void initViewPager() {
        mViewPager = findViewById(R.id.record_dub_material_landing_star_vp);
        final ArrayList<TabCommonAdapter.FragmentHolder> fragmentList = new ArrayList<>();
        Bundle bundle1 = new Bundle();
        bundle1.putInt(BUNDLE_KEY_RANK_PAGE_STATE, DubMaterialRankingFragment.PAGE_WEEK_STATE);
        if (mLandingInfo != null) {
            bundle1.putLong(BUNDLE_KEY_TEMPLATE_ID, mLandingInfo.getMaterialId());
        }
        fragmentList.add(new TabCommonAdapter.FragmentHolder(DubMaterialRankingFragment.class, "周榜", bundle1));
        Bundle bundle2 = new Bundle();
        bundle2.putInt(BUNDLE_KEY_RANK_PAGE_STATE, DubMaterialRankingFragment.PAGE_GENERAL_STATE);
        if (mLandingInfo != null) {
            bundle2.putLong(BUNDLE_KEY_TEMPLATE_ID, mLandingInfo.getMaterialId());
        }
        if (mLandingInfo != null && !ToolUtil.isEmptyCollects(mLandingInfo.getPage())) {
            bundle2.putParcelableArrayList(BUNDLE_KEY_GENERAL_RANK_INFO,
                (ArrayList<? extends Parcelable>) (mLandingInfo.getPage()));
        }
        fragmentList.add(new TabCommonAdapter.FragmentHolder(DubMaterialRankingFragment.class, "总榜", bundle2));
        TabCommonAdapter pagerAdapter = new TabCommonAdapter(getChildFragmentManager(), fragmentList);
        mViewPager.setOffscreenPageLimit(2);
        mViewPager.setAdapter(pagerAdapter);
        mViewPager.setCurrentItem(1);
        mLastTabPos = 1;
        mLastCheckId = R.id.record_dub_material_landing_star_general_rb;
        mViewPager.addOnPageChangeListener(new SimpleOnPageChangeListener() {

            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                if (mActionListener == null) {
                    return;
                }
                if (position == 0) {
                    if (positionOffset >= 0) {
                        mActionListener.setCanSlide(true);
                    } else {
                        mActionListener.setCanSlide(false);
                    }
                } else {
                    mActionListener.setCanSlide(false);
                }
            }

            @Override
            public void onPageSelected(int position) {
                if (mLastTabPos == position) {
                    return;
                }
                if (position == 0 && mLastCheckId != R.id.record_dub_material_landing_star_week_rb) {
                    mRadioGroup.check(R.id.record_dub_material_landing_star_week_rb);
                } else if (position == 1 && mLastCheckId != R.id.record_dub_material_landing_star_general_rb) {
                    mRadioGroup.check(R.id.record_dub_material_landing_star_general_rb);
                }
                mLastTabPos = position;
                new UserTracking()
                    .setSrcPage("趣配音素材落地页")
                    .setSrcModule("dubRank")
                    .setItem("button")
                    .setItemId(position == 0 ? "周榜" : "总榜")
                    .setDubMaterialId(mLandingInfo == null ? 0 : mLandingInfo.getMaterialId())
                    .setId("5244")
                    .statIting(XDCSCollectUtil.SERVICE_PAGE_CLICK);
            }
        });
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        if (mLastCheckId == checkedId) {
            return;
        }
        if (checkedId == R.id.record_dub_material_landing_star_week_rb) {
            mViewPager.setCurrentItem(0);
            mWeekRadioButton.setTypeface(Typeface.DEFAULT_BOLD);
            mGeneralRadioButton.setTypeface(Typeface.DEFAULT);
        } else {
            mViewPager.setCurrentItem(1);
            mWeekRadioButton.setTypeface(Typeface.DEFAULT);
            mGeneralRadioButton.setTypeface(Typeface.DEFAULT_BOLD);
        }
        mLastCheckId = checkedId;
        new UserTracking()
            .setSrcPage("趣配音素材落地页")
            .setSrcModule("dubRank")
            .setItem("button")
            .setItemId(checkedId == R.id.record_dub_material_landing_star_week_rb ? "周榜" : "总榜")
            .setDubMaterialId(mLandingInfo == null ? 0 : mLandingInfo.getMaterialId())
            .setId("5244")
            .statIting(XDCSCollectUtil.SERVICE_PAGE_CLICK);
    }

    @Override
    protected void loadData() {}

    @Override
    protected View getLoadingView() {
        return null;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.record_fra_material_landing_star;
    }
}
