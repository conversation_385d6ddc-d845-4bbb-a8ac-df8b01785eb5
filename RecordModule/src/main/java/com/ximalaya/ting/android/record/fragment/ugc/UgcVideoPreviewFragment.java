package com.ximalaya.ting.android.record.fragment.ugc;

import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.IVideoPlayer;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.IVideoSource;
import com.ximalaya.ting.android.player.video.listener.IXmVideoPlayStatusListener;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.VideoActionRouter;
import com.ximalaya.ting.android.host.model.feed.VideoInfoBean;
import com.ximalaya.ting.android.host.util.view.TitleBar;
import com.ximalaya.ting.android.host.util.view.TitleBar.ActionType;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.data.model.ugc.UgcData;
import com.ximalaya.ting.android.record.data.model.ugc.UgcVideoTransData;
import com.ximalaya.ting.android.record.util.RecordBundleUtils;
import com.ximalaya.ting.android.record.view.dub.DubVideoPayerProxy;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * Created by zhangkaikai on 2019-07-29.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 * @Description ugc用户视频预览
 */
public class UgcVideoPreviewFragment extends BaseFragment2
    implements IXmVideoPlayStatusListener {

    public static final String CURRPAGE = "videoPreview";
    public static final String UPLOAD_TYPE = "uploadType";
    private FrameLayout mFlPlayerContainer;

    private VideoInfoBean mVideoInfoBean;
    private DubVideoPayerProxy mPlayerProxy;

    public static UgcVideoPreviewFragment newInstance(VideoInfoBean info) {
        UgcVideoPreviewFragment fragment = new UgcVideoPreviewFragment();
        fragment.mVideoInfoBean = info;
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        return getClass().getSimpleName();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("素材预览");

        TextView tvEliminate = findViewById(R.id.record_ugc_video_preview_eliminate_tv);
        tvEliminate.setOnClickListener(view -> {
            new XMTraceApi.Trace()
                .click(5476)
                .put("currPage", CURRPAGE)
                .put("Item", "立即消音")
                .put(UPLOAD_TYPE, "mobileUpload")
                .createTrace();
            startEliminate();
        });
    }

    private void startEliminate() {
        if (RecordBundleUtils.checkVideoNoneAudioChannel(mVideoInfoBean.getPath())) {
            CustomToast.showFailToast("暂不支持此视频, 请选择其他视频!");
            return;
        }

        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mContext);
            return;
        }
        UgcVideoTransData transData = new UgcVideoTransData();
        transData.info = mVideoInfoBean;
        transData.startCutPosition = 0;
        transData.endCutPosition = mVideoInfoBean.getDuration();

        UgcData ugcData = new UgcData();
        ugcData.ugcTransData = transData;

        startFragment(UgcVideoEliminateEditFragment.newInstance(ugcData));
        finish();
    }

    private void initVideoBundle() {
        mFlPlayerContainer = findViewById(R.id.record_ugc_video_preview_container);
        doAfterAnimation(() -> Router.getActionByCallback(Configure.BUNDLE_VIDEO, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (Configure.videoBundleModel.bundleName.equals(bundleModel.bundleName)
                    && mVideoInfoBean != null) {
                    initVideoPlayer();
                    startVideoPlay();
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                CustomToast.showDebugFailToast("video bundle install error");
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        }));
    }

    private void initVideoPlayer() {
        IVideoPlayer videoPlayer = null;
        try {
            videoPlayer = Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction().getVideoPlayerForDub(getActivity());
            if (videoPlayer != null) {
                videoPlayer.setRenderViewBackground(mContext.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_transparent));
                IVideoSource originalVideoSource = Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction()
                    .getVideoSource(mVideoInfoBean.getClass().getName(), mVideoInfoBean.getPath());
                videoPlayer.setVideoSource(originalVideoSource);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (videoPlayer instanceof View) {
            View playerView = (View) videoPlayer;
            FrameLayout.LayoutParams params =
                new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            playerView.setLayoutParams(params);
            mFlPlayerContainer.addView(playerView);
            mPlayerProxy = new DubVideoPayerProxy(videoPlayer);
            mPlayerProxy.setXmVideoPlayStatusListener(this);
            mPlayerProxy.setPlayMode(DubVideoPayerProxy.MATERIAL_LANDING_MODE);
        }
    }

    @Override
    protected void loadData() {
        doAfterAnimation(this::initVideoBundle);
    }

    @Override
    protected void setTitleBar(TitleBar titleBar) {
        titleBar.getTitleBar().setBackgroundColor(Color.TRANSPARENT);
        ActionType traceBack = new ActionType("back4trace", TitleBar.LEFT, 0,
            R.drawable.record_arrow_white_normal_left, 0, ImageView.class);
        titleBar.addAction(traceBack, view -> {
            new XMTraceApi.Trace()
                .click(5474)
                .put("currPage", CURRPAGE)
                .put("Item", "return")
                .put(UPLOAD_TYPE, "mobileUpload")
                .createTrace();
            finishFragment();
        });

        titleBar.removeView(ActionType.BACK);
        titleBar.update();

        View titleView = titleBar.getTitle();
        if (titleView instanceof TextView) {
            ((TextView) titleView).setTextColor(Color.WHITE);
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.record_fra_ugc_video_preivew;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.record_ugc_video_preview_title;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public void onDestroy() {
        if (mPlayerProxy != null) {
            mPlayerProxy.stop();
        }
        super.onDestroy();
    }

    @Override
    public void onPause() {
        pauseVideoPlay();
        new XMTraceApi.Trace()
            .pageExit2(5472)
            .put(UPLOAD_TYPE, "mobileUpload")
            .createTrace();
        super.onPause();
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        if (RecordBundleUtils.isCurrentFragment(getActivity(), this)) {
            new XMTraceApi.Trace()
                .pageView(5471, CURRPAGE)
                .put("currPage", CURRPAGE)
                .put(UPLOAD_TYPE, "mobileUpload")
                .createTrace();
        }
        if (mPlayerProxy != null && !mPlayerProxy.isPlaying()) {
            startVideoPlay();
        }
    }

    private void startVideoPlay() {
        if (!canUpdateUi() || mPlayerProxy == null || mPlayerProxy.isPlaying()) {
            return;
        }
        mPlayerProxy.seekTo(0);
        mPlayerProxy.start();
    }

    private void pauseVideoPlay() {
        if (mPlayerProxy == null) {
            return;
        }
        if (mPlayerProxy.isPlaying()) {
            mPlayerProxy.stop();
        }
    }

    @Override
    public void onStart(String videoSourceUrl) {
        new XMTraceApi.Trace()
            .clickButton(5473)
            .put("currPage", CURRPAGE)
            .put("Item", "play")
            .put(UPLOAD_TYPE, "mobileUpload")
            .createTrace();
    }

    @Override
    public void onPause(String videoSourceUrl, long playedTime, long duration) {
        new XMTraceApi.Trace()
            .clickButton(5473)
            .put("currPage", CURRPAGE)
            .put("Item", "pause")
            .put(UPLOAD_TYPE, "mobileUpload")
            .createTrace();
    }

    @Override
    public void onStop(String videoSourceUrl, long playedTime, long duration) {

    }

    @Override
    public void onComplete(String videoSourceUrl, long duration) {
        if (mPlayerProxy != null) {
            mPlayerProxy.restart();
        }
    }

    @Override
    public void onError(String videoSourceUrl, long playedTime, long duration) {
        CustomToast.showFailToast("播放失败！！！");
    }

    @Override
    public void onProgress(String videoSourceUrl, long curPosition, long duration) {
    }

    @Override
    public void onRenderingStart(String videoSourceUrl, long renderingSpentMilliSec) {

    }

    @Override
    public void onBlockingStart(String videoSourceUrl) {

    }

    @Override
    public void onBlockingEnd(String videoSourceUrl) {

    }
}
