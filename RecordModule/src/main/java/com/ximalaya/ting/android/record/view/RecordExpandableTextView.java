package com.ximalaya.ting.android.record.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.Paint;
import android.widget.TextView;
import android.text.DynamicLayout;
import android.text.Layout;
import android.text.Selection;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.method.Touch;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import com.ximalaya.ting.android.record.R;

import java.util.Locale;

/**
 * Created by zhangkaikai on 2019-07-09.
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @PhoneNumber 15721173906
 * @Description 可展开的textview
 */
public class RecordExpandableTextView extends TextView {

    public static final int STATUS_EXPAND = 0;
    public static final int STATUS_CONTRACT = 1;
    public static final String Space = " ";
    public static final String DEFAULT_CONTENT = "";
    private static final int DEF_MAX_LINE = 3;
    public static String TEXT_CONTRACT = "收起";
    public static String TEXT_EXPEND = "展开";
    private static int retryTime = 0;
    private TextPaint mPaint;
    /**
     * 记录当前的model
     */
    private ExpandableStatusFix mModel;
    /**
     * 计算的layout
     */
    private DynamicLayout mDynamicLayout;
    //hide状态下，展示多少行开始省略
    private int mLimitLines;
    private int currentLines;
    private int mWidth;
    /**
     * 展开或者收回事件监听
     */
    private OnExpandOrContractClickListener expandOrContractClickListener;
    /**
     * 是否需要收起
     */
    private boolean mNeedContract;
    /**
     * 是否需要展开功能
     */
    private boolean mNeedExpend;
    /**
     * 是否需要永远将展开或收回显示在最右边
     */
    private boolean mNeedAlwaysShowRight;
    /**
     * 是否需要动画 默认开启动画
     */
    private boolean mNeedAnimation;
    private int mLineCount;
    private CharSequence mContent;
    /**
     * 展开的文案
     */
    private String mExpandString;
    /**
     * 收起的文案
     */
    private String mContractString;
    /**
     * 在收回和展开前面添加的内容
     */
    private String mEndExpandContent;
    /**
     * 在收回和展开字体颜色
     */
    private int mExpandTextColor;
    //是否AttachedToWindow
    private boolean isAttached;
    private OnGetLineCountListener onGetLineCountListener;

    public RecordExpandableTextView(Context context) {
        this(context, null);
    }

    public RecordExpandableTextView(Context context, AttributeSet attrs) {
        this(context, attrs, -1);
    }

    public RecordExpandableTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs, defStyleAttr);
        setMovementMethod(LocalLinkMovementMethod.getInstance());
        addOnAttachStateChangeListener(new OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                if (!isAttached) {
                    doSetContent();
                }
                isAttached = true;
            }

            @Override
            public void onViewDetachedFromWindow(View v) {}
        });
    }

    private void init(AttributeSet attrs, int defStyleAttr) {
        if (attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs,
                R.styleable.RecordExpandableTextView, defStyleAttr, 0);

            mLimitLines = a.getInt(R.styleable.RecordExpandableTextView_record_max_line, DEF_MAX_LINE);
            mNeedExpend = a.getBoolean(R.styleable.RecordExpandableTextView_record_need_expand, true);
            mNeedContract = a.getBoolean(R.styleable.RecordExpandableTextView_record_need_contract, true);
            mNeedAnimation = a.getBoolean(R.styleable.RecordExpandableTextView_record_need_animation, true);
            mNeedAlwaysShowRight = a.getBoolean(R.styleable.RecordExpandableTextView_record_need_always_showright, false);
            mExpandString = TEXT_EXPEND;
            mContractString = TEXT_CONTRACT;
            mExpandTextColor = a.getColor(R.styleable.RecordExpandableTextView_record_expand_color,
                Color.parseColor("#999999"));
            currentLines = mLimitLines;
            a.recycle();
        }
        mPaint = getPaint();
        mPaint.setStyle(Paint.Style.FILL_AND_STROKE);
    }

    private SpannableStringBuilder setRealContent(CharSequence content) {
        //用来计算内容的大小
        mDynamicLayout = new DynamicLayout(content, mPaint, mWidth, Layout.Alignment.ALIGN_NORMAL,
            1.2f, 0.0f, true);
        //获取行数
        mLineCount = mDynamicLayout.getLineCount();

        if (onGetLineCountListener != null) {
            onGetLineCountListener.onGetLineCount(mLineCount, mLineCount > mLimitLines);
        }
        return dealLink(content, true);
    }

    /**
     * 设置追加的内容
     */
    public void setEndExpendContent(String endExpendContent) {
        this.mEndExpandContent = endExpendContent;
    }

    /**
     * 设置内容
     */
    public void setContent(final String content) {
        mContent = content;
        if (isAttached) {
            doSetContent();
        }
    }

    /**
     * 实际设置内容的
     */
    private void doSetContent() {
        if (mContent == null) {
            return;
        }
        currentLines = mLimitLines;

        if (mWidth <= 0 && getWidth() > 0) {
            mWidth = getWidth() - getPaddingLeft() - getPaddingRight();
        }

        if (mWidth <= 0) {
            if (retryTime > 10) {
                setText(DEFAULT_CONTENT);
            }
            this.post(new Runnable() {
                @Override
                public void run() {
                    retryTime++;
                    setContent(mContent.toString());
                }
            });
        } else {
            setRealContent(mContent.toString());
        }
    }

    /**
     * 设置最后的收起文案
     */
    private String getExpandEndContent() {
        if (TextUtils.isEmpty(mEndExpandContent)) {
            return String.format(Locale.getDefault(), "  %s", mContractString);
        } else {
            return String.format(Locale.getDefault(), "  %s  %s", mEndExpandContent, mContractString);
        }
    }

    /**
     * 设置展开的文案
     */
    private String getHideEndContent() {
        if (TextUtils.isEmpty(mEndExpandContent)) {
            return String.format(Locale.getDefault(), mNeedAlwaysShowRight ? "  %s" : "...  %s", mExpandString);
        } else {
            return String.format(Locale.getDefault(), mNeedAlwaysShowRight ? "  %s  %s" : "...  %s  %s",
                mEndExpandContent, mExpandString);
        }
    }

    /**
     * 处理文字中的链接问题
     */
    private SpannableStringBuilder dealLink(CharSequence content, boolean ignoreMore) {
        SpannableStringBuilder ssb = new SpannableStringBuilder();
        //获取存储的状态
        if (mModel != null) {
            if (mModel.getStatus() == STATUS_CONTRACT) {
                currentLines = mLimitLines + ((mLineCount - mLimitLines));
            } else if (mNeedContract) {
                currentLines = mLimitLines;
            }
        }
        //处理折叠操作
        if (ignoreMore) {
            if (currentLines < mLineCount) {
                int index = currentLines - 1;
                int endPosition = mDynamicLayout.getLineEnd(index);
                int startPosition = mDynamicLayout.getLineStart(index);
                float lineWidth = mDynamicLayout.getLineWidth(index);

                String endString = getHideEndContent();

                //计算原内容被截取的位置下标
                int fitPosition = getFitPosition(endString, endPosition, startPosition, lineWidth,
                    mPaint.measureText(endString), 0);
                String substring = content.toString().substring(0, fitPosition);
                if (substring.endsWith("\n")) {
                    substring = substring.substring(0, substring.length() - "\n".length());
                }
                ssb.append(substring);

                if (mNeedAlwaysShowRight) {
                    //计算一下最后一行有没有充满
                    float lastLineWidth = 0;
                    for (int i = 0; i < index; i++) {
                        lastLineWidth += mDynamicLayout.getLineWidth(i);
                    }
                    lastLineWidth = lastLineWidth / (index);
                    float emptyWidth = lastLineWidth - lineWidth - mPaint.measureText(endString);
                    if (emptyWidth > 0) {
                        float measureText = mPaint.measureText(Space);
                        int count = 0;
                        while (measureText * count < emptyWidth) {
                            count++;
                        }
                        count = count - 1;
                        for (int i = 0; i < count; i++) {
                            ssb.append(Space);
                        }
                    }
                }

                //在被截断的文字后面添加 展开 文字
                ssb.append(endString);
                int expendLength = TextUtils.isEmpty(mEndExpandContent) ? 0 : 2 + mEndExpandContent.length();
                ssb.setSpan(new ClickableSpan() {
                                @Override
                                public void onClick(View widget) {
                                    if (mModel != null) {
                                        mModel.setStatus(STATUS_CONTRACT);
                                        action();
                                    } else {
                                        action();
                                    }
                                    if (expandOrContractClickListener != null) {
                                        expandOrContractClickListener.onClick(STATUS_EXPAND);
                                    }
                                }

                                @Override
                                public void updateDrawState(TextPaint ds) {
                                    super.updateDrawState(ds);
                                    ds.setColor(mExpandTextColor);
                                    ds.setUnderlineText(false);
                                }
                            }, ssb.length() - mExpandString.length() - expendLength, ssb.length(),
                    Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            } else {
                ssb.append(content);
                if (mNeedContract) {
                    String endString = getExpandEndContent();
                    if (mNeedAlwaysShowRight) {
                        //计算一下最后一行有没有充满
                        int index = mDynamicLayout.getLineCount() - 1;
                        float lineWidth = mDynamicLayout.getLineWidth(index);
                        float lastLineWidth = 0;
                        for (int i = 0; i < index; i++) {
                            lastLineWidth += mDynamicLayout.getLineWidth(i);
                        }
                        lastLineWidth = lastLineWidth / (index);
                        float emptyWidth = lastLineWidth - lineWidth - mPaint.measureText(endString);
                        if (emptyWidth > 0) {
                            float measureText = mPaint.measureText(Space);
                            int count = 0;
                            while (measureText * count < emptyWidth) {
                                count++;
                            }
                            count = count - 1;
                            for (int i = 0; i < count; i++) {
                                ssb.append(Space);
                            }
                        }
                    }

                    ssb.append(endString);
                    int expendLength = TextUtils.isEmpty(mEndExpandContent) ? 0 : 2 + mEndExpandContent.length();
                    ssb.setSpan(new ClickableSpan() {
                                    @Override
                                    public void onClick(View widget) {
                                        if (mModel != null) {
                                            mModel.setStatus(STATUS_EXPAND);
                                            action();
                                        } else {
                                            action();
                                        }
                                        if (expandOrContractClickListener != null) {
                                            expandOrContractClickListener.onClick(STATUS_CONTRACT);
                                        }
                                    }

                                    @Override
                                    public void updateDrawState(TextPaint ds) {
                                        super.updateDrawState(ds);
                                        ds.setColor(mExpandTextColor);
                                        ds.setUnderlineText(false);
                                    }
                                }, ssb.length() - mContractString.length() - expendLength, ssb.length(),
                        Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                } else {
                    if (!TextUtils.isEmpty(mEndExpandContent)) {
                        ssb.append(mEndExpandContent);
                        ssb.setSpan(new ForegroundColorSpan(mExpandTextColor),
                            ssb.length() - mEndExpandContent.length(), ssb.length(),
                            Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    }
                }
            }
        } else {
            ssb.append(content);
            if (!TextUtils.isEmpty(mEndExpandContent)) {
                ssb.append(mEndExpandContent);
                ssb.setSpan(new ForegroundColorSpan(mExpandTextColor), ssb.length() - mEndExpandContent.length(),
                    ssb.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        //将内容设置到控件中
        setText(ssb);
        return ssb;
    }

    /**
     * 获取需要插入的空格
     */
    private int getFitSpaceCount(float emptyWidth, float endStringWidth) {
        float measureText = mPaint.measureText(Space);
        int count = 0;
        while (endStringWidth + measureText * count < emptyWidth) {
            count++;
        }
        return --count;
    }

    /**
     * 执行展开和收回的动作
     */
    private void action() {
        boolean isHide = currentLines < mLineCount;
        if (mNeedAnimation) {
            ValueAnimator valueAnimator = ValueAnimator.ofFloat(0, 1);
            final boolean finalIsHide = isHide;
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    Float value = (Float) animation.getAnimatedValue();
                    if (finalIsHide) {
                        currentLines = mLimitLines + (int) ((mLineCount - mLimitLines) * value);
                    } else {
                        if (mNeedContract) {
                            currentLines = mLimitLines + (int) ((mLineCount - mLimitLines) * (1 - value));
                        }
                    }
                    setText(setRealContent(mContent));
                }
            });
            valueAnimator.setDuration(100);
            valueAnimator.start();
        } else {
            if (isHide) {
                currentLines = mLimitLines + ((mLineCount - mLimitLines));
            } else {
                if (mNeedContract) {
                    currentLines = mLimitLines;
                }
            }
            setText(setRealContent(mContent));
        }
    }

    /**
     * 计算原内容被裁剪的长度
     *
     * @param endPosition 指定行最后文字的位置
     * @param startPosition 指定行文字开始的位置
     * @param lineWidth 指定行文字的宽度
     * @param endStringWith 最后添加的文字的宽度
     * @param offset 偏移量
     */
    private int getFitPosition(String endString, int endPosition, int startPosition, float lineWidth,
        float endStringWith, float offset) {
        //最后一行需要添加的文字的字数
        int position = (int) ((lineWidth - (endStringWith + offset)) * (endPosition - startPosition) / lineWidth);
        if (position <= endString.length()) {
            return endPosition;
        }
        //计算最后一行需要显示的正文的长度
        float measureText = mPaint.measureText(mContent.toString().substring(startPosition, startPosition + position));
        //如果最后一行需要显示的正文的长度比最后一行的长减去“展开”文字的长度要短就可以了  否则加个空格继续算
        if (measureText <= lineWidth - endStringWith) {
            return startPosition + position;
        } else {
            return getFitPosition(endString, endPosition, startPosition, lineWidth, endStringWith,
                offset + mPaint.measureText(Space));
        }
    }

    /**
     * 绑定状态
     */
    public void bind(ExpandableStatusFix model) {
        mModel = model;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        boolean res = super.onTouchEvent(event);
        //防止选择复制的状态不消失
        if (action == MotionEvent.ACTION_UP) {
            this.setTextIsSelectable(false);
        }
        return res;
    }

    public OnGetLineCountListener getOnGetLineCountListener() {
        return onGetLineCountListener;
    }

    public void setOnGetLineCountListener(OnGetLineCountListener onGetLineCountListener) {
        this.onGetLineCountListener = onGetLineCountListener;
    }

    public boolean isNeedContract() {
        return mNeedContract;
    }

    public void setNeedContract(boolean mNeedContract) {
        this.mNeedContract = mNeedContract;
    }

    public boolean isNeedExpend() {
        return mNeedExpend;
    }

    public void setNeedExpend(boolean mNeedExpend) {
        this.mNeedExpend = mNeedExpend;
    }

    public boolean isNeedAnimation() {
        return mNeedAnimation;
    }

    public void setNeedAnimation(boolean mNeedAnimation) {
        this.mNeedAnimation = mNeedAnimation;
    }

    public int getExpandableLineCount() {
        return mLineCount;
    }

    public void setExpandableLineCount(int mLineCount) {
        this.mLineCount = mLineCount;
    }

    public int getExpandTextColor() {
        return mExpandTextColor;
    }

    public void setExpandTextColor(int mExpandTextColor) {
        this.mExpandTextColor = mExpandTextColor;
    }

    public String getExpandString() {
        return mExpandString;
    }

    public void setExpandString(String mExpandString) {
        this.mExpandString = mExpandString;
    }

    public String getContractString() {
        return mContractString;
    }

    public void setContractString(String mContractString) {
        this.mContractString = mContractString;
    }

    public boolean isNeedAlwaysShowRight() {
        return mNeedAlwaysShowRight;
    }

    public void setNeedAlwaysShowRight(boolean mNeedAlwaysShowRight) {
        this.mNeedAlwaysShowRight = mNeedAlwaysShowRight;
    }

    public OnExpandOrContractClickListener getExpandOrContractClickListener() {
        return expandOrContractClickListener;
    }

    public void setExpandOrContractClickListener(OnExpandOrContractClickListener expandOrContractClickListener) {
        this.expandOrContractClickListener = expandOrContractClickListener;
    }

    public interface OnGetLineCountListener {

        /**
         * lineCount 预估可能占有的行数 canExpand 是否达到可以展开的条件
         */
        void onGetLineCount(int lineCount, boolean canExpand);
    }

    public interface OnExpandOrContractClickListener {

        void onClick(int type);
    }

    public interface ExpandableStatusFix {

        int getStatus();

        void setStatus(int status);
    }

    public static class LocalLinkMovementMethod extends LinkMovementMethod {

        static LocalLinkMovementMethod sInstance;


        public static LocalLinkMovementMethod getInstance() {
            if (sInstance == null) {
                sInstance = new LocalLinkMovementMethod();
            }

            return sInstance;
        }

        @Override
        public boolean onTouchEvent(TextView widget,
            Spannable buffer, MotionEvent event) {
            int action = event.getAction();

            if (action == MotionEvent.ACTION_UP ||
                action == MotionEvent.ACTION_DOWN) {
                int x = (int) event.getX();
                int y = (int) event.getY();

                x -= widget.getTotalPaddingLeft();
                y -= widget.getTotalPaddingTop();

                x += widget.getScrollX();
                y += widget.getScrollY();

                Layout layout = widget.getLayout();
                int line = layout.getLineForVertical(y);
                int off = layout.getOffsetForHorizontal(line, x);

                ClickableSpan[] link = buffer.getSpans(off, off, ClickableSpan.class);

                if (link.length != 0) {
                    if (action == MotionEvent.ACTION_UP) {
                        link[0].onClick(widget);
                    } else {
                        Selection.setSelection(buffer, buffer.getSpanStart(link[0]), buffer.getSpanEnd(link[0]));
                    }
                    return true;
                } else {
                    Selection.removeSelection(buffer);
                    Touch.onTouchEvent(widget, buffer, event);
                    return false;
                }
            }
            return Touch.onTouchEvent(widget, buffer, event);
        }
    }
}
