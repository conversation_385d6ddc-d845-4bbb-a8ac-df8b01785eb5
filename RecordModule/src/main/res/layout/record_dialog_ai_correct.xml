<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/record_root_view"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/record_10corner_ffffff_282828"
    tools:ignore="ResourceName">

    <LinearLayout
        android:id="@+id/record_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="27dp"
        android:paddingRight="27dp"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingTop="32dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-light"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            android:textColor="@color/host_color_333333_ffffff"
            android:textSize="16sp"
            android:textStyle="bold"
            android:text="AI文稿\n只支持在web端纠错哦～" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginBottom="12dp"
            android:src="@drawable/record_bg_ai_correct" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="13sp"
            android:textColor="@color/host_color_999999_8d8d91"
            android:gravity="center"
            android:text="纠错路径：创作中心-我的作品-AI文稿" />

        <TextView
            android:id="@+id/record_copy_link_btn"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginTop="31dp"
            android:layout_marginBottom="40dp"
            android:background="@drawable/host_line_graide_ff4b61_ffadb5"
            android:fontFamily="sans-serif-light"
            android:paddingHorizontal="52dp"
            android:gravity="center"
            android:text="复制修改链接"
            android:textSize="15sp"
            android:textColor="@color/host_white"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </LinearLayout>

    <ImageView
        android:id="@+id/record_close_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:src="@drawable/host_ic_x_close_n_line_regular_20"
        app:layout_constraintEnd_toEndOf="@id/record_layout"
        app:layout_constraintTop_toTopOf="@id/record_layout"
        tools:ignore="UseAppTint" />

</androidx.constraintlayout.widget.ConstraintLayout>