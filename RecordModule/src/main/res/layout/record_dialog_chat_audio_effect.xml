<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/record_bg_music_dialog"
    android:paddingLeft="21dp"
    android:paddingRight="21dp"
    android:layout_width="match_parent"
    android:layout_height="133dp">

<!--    <FrameLayout-->
<!--        android:id="@+id/record_time_bar_container"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        tools:layout_height="21dp"-->
<!--        android:layout_marginTop="@dimen/host_common_margin_15"-->
<!--        android:layout_gravity="center_horizontal"/>-->

<!--    <TextView-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginTop="24dp"-->
<!--        android:text="声音特效"-->
<!--        android:textColor="@color/host_color_333333"-->
<!--        android:textSize="16sp"-->
<!--        android:textStyle="bold" />-->

<!--    <androidx.recyclerview.widget.RecyclerView-->
<!--        android:id="@+id/record_audio_effect_rv"-->
<!--        android:layout_marginTop="20dp"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"-->
<!--        tools:orientation="horizontal"-->
<!--        tools:itemCount="4"-->
<!--        tools:listitem="@layout/record_item_toolbox"/>-->

    <LinearLayout
        android:layout_marginTop="24dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:orientation="vertical"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="耳机监听"
                android:textColor="@color/host_color_333333"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="开启后从耳机里可以听到自己的声音"
                android:textSize="12sp"
                android:textColor="@color/host_color_666666" />
        </LinearLayout>

        <com.ximalaya.ting.android.host.view.bar.SwitchButton
            android:id="@+id/record_earpods_sb"
            android:checked="false"
            android:clickable="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </LinearLayout>

</LinearLayout>