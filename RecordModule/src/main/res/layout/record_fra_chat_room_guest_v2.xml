<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/record_color_f2f4fa">

    <ImageView
        android:id="@+id/record_iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="15dp"
        android:layout_marginTop="18dp"
        android:paddingVertical="5dp"
        android:src="@drawable/record_icon_chat_room_exit"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/record_tv_room_name_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginTop="30dp"
        android:layout_marginEnd="30dp"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/host_black"
        android:textSize="18sp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/record_iv_back" />

    <LinearLayout
        android:id="@+id/record_ll_retry_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/record_bg_cc000000_6dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/record_iv_back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/record_iv_back">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="center"
            android:layout_marginStart="10dp"
            android:src="@drawable/record_icon_sound_wave" />

        <TextView
            android:id="@+id/record_tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:paddingVertical="7dp"
            android:paddingStart="8dp"
            android:text="您有上传失败的录音，点击"
            android:textColor="@color/host_color_white"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/record_tv_retry_upload"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="重新上传"
            android:textColor="#FF4B61"
            android:textSize="12sp" />

        <ImageView
            android:id="@+id/record_iv_upload_close"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/record_icon_chat_tip_close" />

    </LinearLayout>


    <include
        android:id="@+id/record_cv_notice"
        layout="@layout/record_layout_chat_notice"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/record_iv_back"
        app:layout_constraintWidth_percent="0.8" />


    <GridView
        android:id="@+id/record_member_gv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:columnWidth="100dp"
        android:gravity="center"
        android:numColumns="4"
        android:paddingLeft="25dp"
        android:paddingTop="32dp"
        android:paddingRight="25dp"
        android:paddingBottom="30dp"
        android:scrollbars="none"
        android:stretchMode="spacingWidthUniform"
        android:verticalSpacing="12dp"
        app:layout_constraintBottom_toTopOf="@+id/record_cl_bottom_record_region"
        app:layout_constraintTop_toBottomOf="@id/record_cv_notice"
        tools:listitem="@layout/record_item_chat_room_item" />

    <TextView
        android:id="@+id/record_tv_exit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:drawableTop="@drawable/record_ic_chat_room_exit"
        android:drawablePadding="10dp"
        android:gravity="center_horizontal"
        android:text="退出"
        android:textColor="@color/host_color_333333"
        android:textSize="14sp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.75"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/record_cb_mic" />

    <include
        android:id="@+id/record_cl_bottom_record_region"
        layout="@layout/record_fra_chat_room_guest_bottom_view"
        android:layout_width="match_parent"
        android:layout_height="230dp"
        app:layout_constraintBottom_toBottomOf="parent" />

    <CheckBox
        android:id="@+id/record_cb_mic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@null"
        android:drawableTop="@drawable/record_ic_chat_room_mic_black"
        android:gravity="center_horizontal"
        android:text="解除静音"
        android:textColor="@color/record_color_111111"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/record_tv_status"
        app:layout_constraintEnd_toStartOf="@id/record_tv_status"
        app:layout_constraintStart_toStartOf="@+id/record_cl_bottom_record_region"
        app:layout_constraintTop_toTopOf="@+id/record_tv_status" />

    <TextView
        android:id="@+id/record_tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:background="@drawable/record_bg_wait_record_f5f5f5"
        android:drawableStart="@drawable/record_circle_chat_status_v2"
        android:drawablePadding="4dp"
        android:enabled="false"
        android:gravity="center"
        android:paddingHorizontal="9dp"
        android:paddingVertical="8dp"
        android:text="等待录制"
        android:textColor="@color/record_text_chat_status_v2"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/record_cl_bottom_record_region"
        app:layout_constraintEnd_toEndOf="@+id/record_cl_bottom_record_region"
        app:layout_constraintStart_toStartOf="@+id/record_cl_bottom_record_region" />

    <TextView
        android:id="@+id/record_tv_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="00:00"
        android:textColor="@color/record_color_111111"
        android:textSize="24sp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/record_tv_status"
        app:layout_constraintEnd_toEndOf="@+id/record_cl_bottom_record_region"
        app:layout_constraintStart_toStartOf="@+id/record_cl_bottom_record_region"
        app:layout_constraintTop_toTopOf="@+id/record_tv_status" />


    <com.ximalaya.ting.android.record.view.waveview.AudioVolumeView
        android:id="@+id/record_wave_volume"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_marginBottom="12dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/record_tv_status" />
</androidx.constraintlayout.widget.ConstraintLayout>