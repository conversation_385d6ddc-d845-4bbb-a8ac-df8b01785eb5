<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.record.view.RecordRadiusCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:record_rcv_topLeftRadiu="8dp"
    app:record_rcv_topRightRadiu="8dp">

    <FrameLayout
        android:id="@+id/record_v_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/record_iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|top"
        android:contentDescription="关闭弹窗"
        android:padding="15dp"
        android:visibility="invisible"
        android:src="@drawable/record_ic_ai_close" />
</com.ximalaya.ting.android.record.view.RecordRadiusCardView>