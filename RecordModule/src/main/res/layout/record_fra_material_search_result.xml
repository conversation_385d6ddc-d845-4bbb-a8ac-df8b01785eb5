<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/record_search_result_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/framework_color_ffffff_111111">

    <include
        layout="@layout/host_no_content_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        tools:visibility="visible" />

    <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
        android:id="@+id/record_lv_online_picture_template_sub"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="55dp"
        android:background="@null"
        android:clipToPadding="false"
        android:divider="@null"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:footerDividersEnabled="false"
        android:headerDividersEnabled="false"
        android:overScrollMode="always"
        android:scrollbars="none" />

    <LinearLayout
        android:id="@+id/record_layout_material_filter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/framework_color_ffffff_111111"
        android:orientation="vertical"
        android:visibility="invisible"
        tools:visibility="visible">

        <include
            layout="@layout/record_layout_material_filter_header"
            android:layout_width="match_parent"
            android:layout_height="55dp" />
    </LinearLayout>
</RelativeLayout>

