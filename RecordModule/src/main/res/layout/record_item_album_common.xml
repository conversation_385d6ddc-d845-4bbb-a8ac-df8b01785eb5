<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/host_bg_list_selector"
    android:descendantFocusability="blocksDescendants"
    android:paddingTop="15dp">

    <FrameLayout
        android:id="@+id/record_iv_album_cover_fl"
        android:layout_marginLeft="15dp"
        android:layout_marginBottom="15dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <!--专辑图-->
        <ImageView
            android:id="@+id/record_iv_album_cover"
            style="@style/record_album_item_cover"
            android:contentDescription="@string/record_cover"/>

        <TextView
            android:id="@+id/record_iv_album_offline"
            android:layout_width="match_parent"
            android:layout_height="16dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:gravity="center"
            android:text="已下架"
            android:textColor="@color/host_color_ffffff"
            android:textSize="11sp"
            android:layout_gravity="bottom"
            android:background="#B3000000" />
    </FrameLayout>

    <!--专辑标题-->
    <TextView
        android:id="@+id/record_tv_album_title"
        style="@style/record_album_item_title"
        tools:text="@string/record_app_name"
        tools:textColor="@color/host_color_333333" />

    <Space
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:layout_marginRight="13dp" />


    <!--专辑信息容器-->
    <LinearLayout
        android:id="@+id/record_layout_album_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/record_tv_album_title"
        android:layout_below="@+id/record_tv_album_title"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:orientation="horizontal" />


    <!--副标题-->
    <TextView
        android:id="@+id/record_tv_album_subtitle"
        style="@style/record_album_item_subtitle"
        android:visibility="invisible"
        tools:visibility="visible" />

    <View
        style="@style/record_album_item_border"
        android:layout_below="@+id/record_tv_album_subtitle" />

    <TextView
        android:id="@+id/record_tv_last_selection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/record_bg_4corner_1aea6347"
        android:textSize="10sp"
        android:textColor="@color/host_color_ea6347"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingTop="4dp"
        android:paddingStart="4dp"
        android:paddingEnd="4dp"
        android:paddingBottom="4dp"
        android:layout_centerVertical="true"
        android:layout_alignParentRight="true"
        android:layout_marginEnd="12dp"
        android:text="上次选择"
        android:visibility="invisible"
        tools:visibility="visible" />

</RelativeLayout>