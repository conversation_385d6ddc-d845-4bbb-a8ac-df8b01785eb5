<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp">

    <ImageView
        android:id="@+id/record_ugc_top_sign_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="18dp"
        android:scaleType="fitXY"
        android:src="@drawable/record_icon_ugc_sign_bg"
        app:layout_constraintDimensionRatio="H,343:175"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/record_tv_top_sign_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="8dp"
        android:scaleType="fitXY"
        android:src="@drawable/record_icon_ugc_sign_discover"
        app:layout_constraintDimensionRatio="H,176:28"
        app:layout_constraintStart_toStartOf="@+id/record_ugc_top_sign_bg"
        app:layout_constraintTop_toTopOf="@+id/record_ugc_top_sign_bg" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/record_ll_top_growth_record"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="12dp"
        app:layout_constraintEnd_toEndOf="@+id/record_ugc_top_sign_bg"
        app:layout_constraintTop_toTopOf="@+id/record_ugc_top_sign_bg">

        <TextView
            android:id="@+id/record_tv_top_growth_record"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginEnd="8dp"
            android:drawableEnd="@drawable/record_icon_ugc_sign_arrow"
            android:paddingVertical="16dp"
            android:text="我的声音小记"
            android:textColor="@color/record_color_444444"
            android:textSize="12sp"
            android:translationY="-4.2dp"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/record_ll_top_sign_mood"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="12dp"
        android:layout_marginVertical="12dp"
        android:background="@drawable/record_bg_8corner_ffffff"
        android:backgroundTint="@color/host_color_ffffff"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/record_ugc_top_sign_bg"
        app:layout_constraintEnd_toEndOf="@+id/record_ugc_top_sign_bg"
        app:layout_constraintStart_toStartOf="@+id/record_ugc_top_sign_bg"
        app:layout_constraintTop_toBottomOf="@+id/record_tv_top_sign_title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/record_tv_top_sign_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:layout_marginTop="8dp"
            app:layout_constraintEnd_toEndOf="@+id/record_rv_top_sign_mood"
            app:layout_constraintStart_toStartOf="@+id/record_rv_top_sign_mood"
            app:layout_constraintTop_toBottomOf="@+id/record_tv_top_sign_title">

            <TextView
                android:id="@+id/record_tv_top_rank_sign_day"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingEnd="12dp"
                android:text="已打卡33天"
                android:textColor="@color/record_color_444444"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/record_tv_top_sign_no"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="当前排名第4名"
                android:textColor="@color/record_color_444444"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@+id/record_tv_top_rank_sign_day"
                app:layout_constraintStart_toEndOf="@+id/record_tv_top_rank_sign_day"
                app:layout_constraintTop_toTopOf="@+id/record_tv_top_rank_sign_day" />

            <TextView
                android:id="@+id/record_tv_top_sign_rank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:text="查看排行榜"
                android:textColor="@color/record_color_444444"
                android:textSize="12sp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="@+id/record_tv_top_rank_sign_day"
                app:layout_constraintEnd_toStartOf="@+id/record_tv_top_sign_rank_arrow"
                app:layout_constraintTop_toTopOf="@+id/record_tv_top_rank_sign_day" />


            <ImageView
                android:id="@+id/record_tv_top_sign_rank_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:src="@drawable/record_icon_ugc_sign_arrow"
                app:layout_constraintBottom_toBottomOf="@+id/record_tv_top_sign_rank"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/record_tv_top_sign_rank" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/record_rv_top_sign_mood"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="8dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="7"
            tools:listitem="@layout/record_item_ugc_sign_mood_layout" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>