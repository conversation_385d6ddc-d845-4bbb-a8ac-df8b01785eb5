<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/record_bg_8corner_ffffff">

    <ImageView
        android:id="@+id/record_iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:src="@drawable/record_ic_close_album_dialog"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/record_ll_album_suggest"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:orientation="vertical"
        android:paddingTop="12dp"
        android:paddingBottom="4dp"
        app:layout_constraintEnd_toStartOf="@id/record_iv_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/record_tv_title_length"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/record_ic_album_suggest_star"
            android:drawablePadding="12dp"
            android:text="建议标题在3个字以上，以展示更多信息"
            android:textColor="@color/record_color_ae7359"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/record_tv_cover_resolution"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:drawableStart="@drawable/record_ic_album_suggest_star"
            android:drawablePadding="12dp"
            android:text="封面分辨率较低，建议更换高分辨率图片"
            android:textColor="@color/record_color_ae7359"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/record_tv_intro"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:drawableStart="@drawable/record_ic_album_suggest_star"
            android:drawablePadding="12dp"
            android:text="建议字数较少，建议填写更多简介信息"
            android:textColor="@color/record_color_ae7359"
            android:textSize="12sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>