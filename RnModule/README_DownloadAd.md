# 下载广告视图使用说明

## 概述

下载广告视图是一个新的React Native组件，用于展示下载类型的广告任务。用户可以通过安装和打开推荐的应用来获得金币奖励。

## 功能特性

1. **广告展示**: 展示下载类型的广告，包含应用图标、名称、大小、描述等信息
2. **下载功能**: 点击"去安装"按钮可以下载应用（支持站内下载器和应用商店跳转）
3. **奖励系统**: 下载完成后自动发放金币奖励并显示弹窗
4. **状态切换**: 下载完成后按钮自动切换为"去打开"状态
5. **额外奖励**: 打开应用后可获得额外金币奖励
6. **自动刷新**: 完成整个流程后自动请求新的广告任务

## 文件结构

```
RnBundle/RnModule/src/main/java/com/ximalaya/ting/android/reactnative/
├── modules/
│   ├── DownloadAdViewManager.java    # 视图管理器
│   └── DownloadAdModule.java         # 模块接口
└── example/
    └── DownloadAdExample.js          # 使用示例

TingMainHost/TingMainApp/src/com/ximalaya/ting/android/host/
├── manager/ad/
│   └── RnDownloadAdManager.java      # 广告管理器
├── view/ad/
│   └── RnDownloadAdView.java         # 广告视图
└── res/
    ├── layout/
    │   └── host_rn_download_ad_view_layout.xml  # 布局文件
    └── drawable/
        ├── main_drawable_download_btn_bg.xml    # 下载按钮背景
        ├── main_drawable_open_btn_bg.xml        # 打开按钮背景
        └── main_drawable_download_ad_bg.xml     # 广告背景
```

## 使用方法

### 1. React Native组件使用

```javascript
import React, { useState, useEffect } from 'react';
import {
  View,
  NativeModules,
  NativeEventEmitter,
  requireNativeComponent,
} from 'react-native';

const { DownloadAd } = NativeModules;
const DownloadAdView = requireNativeComponent('DownloadAdView');

const MyComponent = () => {
  const [adHeight, setAdHeight] = useState(0);
  const [adData, setAdData] = useState({
    slot_id: 16,           // 广告位ID
    rewardCoin: 500,       // 奖励金币数
    sourceName: 'welfareCenter', // 来源名称
    operationId: 'download_ad_' + Date.now(), // 操作ID
  });

  useEffect(() => {
    // 监听奖励事件
    const eventEmitter = new NativeEventEmitter(DownloadAd);
    const rewardSuccessSubscription = eventEmitter.addListener(
      'onRewardSuccess',
      (event) => {
        console.log('奖励成功:', event);
      }
    );

    const rewardFailSubscription = eventEmitter.addListener(
      'onRewardFail',
      (event) => {
        console.log('奖励失败:', event);
      }
    );

    return () => {
      rewardSuccessSubscription.remove();
      rewardFailSubscription.remove();
    };
  }, []);

  return (
    <DownloadAdView
      style={{ height: adHeight }}
      bindData={adData}
      onHeightChange={(event) => setAdHeight(event.nativeEvent.height)}
      onAdClick={() => console.log('广告被点击')}
    />
  );
};
```

### 2. 参数说明

#### bindData 参数
- `slot_id`: 广告位ID，用于请求特定类型的广告
- `rewardCoin`: 奖励金币数，显示在按钮上
- `sourceName`: 来源名称，用于统计和追踪
- `operationId`: 操作ID，用于避免重复加载

#### 事件回调
- `onHeightChange`: 广告高度变化回调
- `onAdClick`: 广告点击回调

#### 奖励事件
- `onRewardSuccess`: 奖励发放成功事件
- `onRewardFail`: 奖励发放失败事件

## 工作流程

1. **初始化**: 组件创建时请求下载类型广告
2. **展示**: 显示广告内容，按钮状态为"去安装"
3. **下载**: 用户点击按钮开始下载应用
4. **奖励**: 下载完成后自动发放金币并显示弹窗
5. **状态切换**: 按钮更新为"去打开"状态
6. **打开应用**: 用户点击打开应用
7. **额外奖励**: 发放额外金币并显示toast提示
8. **刷新**: 自动请求新的广告任务

## 注意事项

1. **权限要求**: 需要网络权限和安装未知来源应用的权限
2. **下载监听**: 实际项目中需要集成具体的下载管理器
3. **奖励接口**: 使用现有的rewardGoldCoin接口
4. **错误处理**: 包含网络异常、下载失败等错误处理
5. **内存管理**: 组件销毁时自动清理资源

## 扩展功能

1. **下载进度**: 可以添加下载进度显示
2. **多语言支持**: 支持不同语言的文案
3. **主题定制**: 支持不同的UI主题
4. **统计上报**: 添加详细的数据统计
5. **A/B测试**: 支持不同的展示策略

## 调试

在开发模式下，可以通过以下方式调试：

1. 查看日志输出
2. 使用React Native调试工具
3. 检查网络请求
4. 验证奖励发放

## 相关接口

- `AggregationManager.getInstance().requestAd()`: 请求广告
- `AdRequest.getIncentiveRewardGoldCoin()`: 发放金币奖励
- `RewardCoinDialogFragment`: 奖励弹窗
- `CustomToast`: 提示信息

