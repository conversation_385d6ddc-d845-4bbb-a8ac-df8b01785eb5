apply plugin: 'com.android.library'
//apply plugin: 'com.ximalaya.gradle.makeroute'
apply plugin: 'kotlin-android'

apply from: project.rootDir.absolutePath + '/util.gradle'
ext {
    extChannels = "ceshi"
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    aaptOptions.cruncherEnabled = false

    lintOptions {
        // set to true to turn off analysis progress reporting by lint
//        quiet true
        // if true, stop the gradle build if errors are found
        abortOnError false
        // if true, only report errors
//        ignoreWarnings false
//        checkAllWarnings true
//        checkReleaseBuilds false
        lintConfig file("lint.xml")
    }
    defaultConfig {
//        applicationId "com.ximalaya.ting.android.main"
        minSdkVersion Integer.parseInt(rootProject.ext.minSdkVersion)
        targetSdkVersion Integer.parseInt(rootProject.ext.targetSdkVersion)
        versionCode 1
        versionName "1.0"
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [moduleName: project.getName(), packageName: "com.ximalaya.ting.android.reactnative"]
            }
        }
    }

    sourceSets {
        main {
            java.srcDirs = ['src/main/java']
            aidl.srcDirs = ['src/main/java']
            jniLibs.srcDirs = ['libs']
            if (project.property('isReleaseDebug').toBoolean()) {
                manifest.srcFile "src/main/debug/AndroidManifest.xml"
            }
        }

    }

    signingConfigs {
        debug {
            storeFile = new File(rootDir, "key")
            storePassword = "123456"
            keyAlias = "aitsuki"
            keyPassword = "123456"
        }
        relealse {
            storeFile = new File("${rootDir}/TingMainHost/Application/build-files/", "ximalaya.keystore")
            storePassword "111111"
            keyAlias "ximalaya"
            keyPassword "111111"
            v2SigningEnabled false
        }
    }

    buildTypes {
        debug {
            // 显示Log
//            buildConfigField "boolean", "LOG_DEBUG", "true"
//
//            versionNameSuffix "-debug"
            minifyEnabled false
//            zipAlignEnabled false
//            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }

        release {
            // 不显示Log
//            buildConfigField "boolean", "LOG_DEBUG", "true"
            //混淆
            minifyEnabled false
            //Zipalign优化
//            zipAlignEnabled true

            // 移除无用的resource文件
//            shrinkResources true
            //前一部分代表系统默认的android程序的混淆文件，该文件已经包含了基本的混淆声明
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //签名
            signingConfig signingConfigs.relealse
        }
    }

    resourcePrefix "rn_"
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    api project(':TingMainHost:TingMainApp')

     api ('com.ximalaya.reactnative:api:*******.debug.16') {
//     api ('com.ximalaya.reactnative:api:*******.debug.7') {
        exclude group: "androidx.activity"
        exclude group: "androidx.fragment"
        exclude group: "androidx.annotation"
        exclude group: "androidx.appcompat"
        exclude group: "androidx.arch.core"
        exclude group: "androidx.core"
        exclude group: "androidx.lifecycle"
        exclude group: 'com.google.code.gson', module: 'gson'
        exclude group: "com.handmark.pulltorefresh.library"
        exclude group: "com.github.bumptech.glide"
        changing = true
    }
    api 'com.ximalaya.ting.android.common:xperformance:1.0.6'

//    api project(':xm_rn')
    annotationProcessor 'com.ximalaya.reactnative:compiler:*******-SNAPSHOT'
    // 动画支持 只在这里添加依赖即可
    api 'com.facebook.fresco:animated-gif:2.0.0'
    api rootProject.ext.xmDependencies.xmTransition

    api 'com.ximalaya.ting.android.common:recorder_player:0.0.1'
    api 'com.ximalaya.ting.android.common:flash_list:0.0.1'
    api 'com.ximalaya.ting.android.common:react-native-image-capinsets-next:0.0.1'
}
