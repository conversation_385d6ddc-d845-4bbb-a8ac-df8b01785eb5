package com.ximalaya.ting.android.reactnative;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.MessageQueue;
import android.preference.PreferenceManager;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.dooboolab.audiorecorderplayer.RNAudioRecorderPlayerPackage;
import com.facebook.debug.debugoverlay.model.DebugOverlayTag;
import com.facebook.debug.holder.Printer;
import com.facebook.debug.holder.PrinterHolder;
import com.facebook.react.ReactPackage;
import com.facebook.react.views.text.ReactTextShadowNodeHelper;
import com.facebook.systrace.PerfMonitor;
import com.shopify.reactnative.flash_list.
        ReactNativeFlashListPackage;
import com.shopify.reactnativeperformance.ReactNativePerformancePackage;
import com.ximalaya.reactnative.BundleBaseInfo;
import com.ximalaya.reactnative.ENV;
import com.ximalaya.reactnative.IErrorListener;
import com.ximalaya.reactnative.IFileCorruptUploader;
import com.ximalaya.reactnative.IHttpProvider;
import com.ximalaya.reactnative.ILocalCompileConfig;
import com.ximalaya.reactnative.ILocationProvider;
import com.ximalaya.reactnative.INewServerConfigProvider;
import com.ximalaya.reactnative.IOnConfigRefreshSuccessListener;
import com.ximalaya.reactnative.IWidgetUsageListener;
import com.ximalaya.reactnative.IXmImeiProvider;
import com.ximalaya.reactnative.OverrideColorScheme;
import com.ximalaya.reactnative.RNConfig;
import com.ximalaya.reactnative.XMReactNativeApi;
import com.ximalaya.reactnative.services.CustomFontManager;
import com.ximalaya.reactnative.services.apm.RNApmManager;
import com.ximalaya.reactnative.services.apm.listener.ApmUploadListener;
import com.ximalaya.ting.android.apm.XmApm;
import com.ximalaya.ting.android.apm.stat.LocalFileManager;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.base.IConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.core.Warehouse;
import com.ximalaya.ting.android.framework.arouter.facade.template.IInterceptorGroup;
import com.ximalaya.ting.android.framework.arouter.facade.template.IProviderGroup;
import com.ximalaya.ting.android.framework.arouter.facade.template.IRouteRoot;
import com.ximalaya.ting.android.framework.autosize.AutoSizeConfig;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager;
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.listener.IApplication;
import com.ximalaya.ting.android.host.manager.bundleframework.route.RouterConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.freeflow.FreeFlowService;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.universal.IReporter;
import com.ximalaya.ting.android.host.util.HomeRnUtils;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.RemoteTypefaceManager;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.startup.StartupOptManager;
import com.ximalaya.ting.android.locationservice.LocationService;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.reactnative.alarm.AlarmPackage;
import com.ximalaya.ting.android.reactnative.degrade.DegradeHandler;
import com.ximalaya.ting.android.reactnative.footprint.FootPrintPackage;
import com.ximalaya.ting.android.reactnative.ksong.KSongReactNaivePackage;
import com.ximalaya.ting.android.reactnative.manager.RNActivityActionImpl;
import com.ximalaya.ting.android.reactnative.manager.RNFragmentActionImpl;
import com.ximalaya.ting.android.reactnative.manager.RNFunctionActionImpl;
import com.ximalaya.ting.android.reactnative.modules.business.ReactViewDataHolder;
import com.ximalaya.ting.android.reactnative.modules.home.RecommendPackage;
import com.ximalaya.ting.android.reactnative.modules.thirdParty.fastImage.FastImageViewPackage;
import com.ximalaya.ting.android.reactnative.modules.thirdParty.lottie.LottiePackage;
import com.ximalaya.ting.android.reactnative.modules.thirdParty.slider.ReactSliderPackage;
import com.ximalaya.ting.android.reactnative.modules.thirdParty.svg.SvgPackage;
import com.ximalaya.ting.android.reactnative.modules.viewshot.RNViewShotPackage;
import com.ximalaya.ting.android.reactnative.packages.RNReactPackage;
import com.ximalaya.ting.android.reactnative.playpage.PlayPagePackage;
import com.ximalaya.ting.android.reactnative.support.X86Installer;
import com.ximalaya.ting.android.reactnative.trace.RNPerformance;
import com.ximalaya.ting.android.reactnative.trace.RNTrace;
import com.ximalaya.ting.android.reactnative.utils.AlarmUtil;
import com.ximalaya.ting.android.reactnative.utils.RNSettings;
import com.ximalaya.ting.android.reactnative.utils.RNUtils;
import com.ximalaya.ting.android.route.handle.XmUriRouterManager;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.model.RNInfo;
import com.ximalaya.ting.android.xmutil.BaseDeviceUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import androidx.annotation.NonNull;
import dk.madslee.imageCapInsets.RCTImageCapInsetPackage;

import static com.ximalaya.ting.android.host.util.constant.AppConstants.isDebugSvrTest;

//import com.ximalaya.ting.android.host.util.common.MyLocationManager;

/**
 * Created by Travis on 2018/6/5 下午2:25.
 *
 * <AUTHOR>
 */
public class RNApplication implements IApplication<RNActionRouter>,
        Application.ActivityLifecycleCallbacks,
        IOnConfigRefreshSuccessListener,
        IConfigureCenter.ConfigFetchCallback {
    public String topBundleName;
    private Context mContext;
    private Handler mHandler;
    private boolean devResourceMerged;
    private volatile boolean inited;
    private static RNApplication instance;

    public static final String APPLICATION_PACKAGE_NAME = "com.ximalaya.ting.android.reactnative";
    public static final String APP_ROUTER_ROOT_CLASS_NAME = "ARouter$$Root$$RnModule";
    public static final String APP_ROUTER_PROVIDERS_CLASS_NAME = "ARouter$$Providers$$RnModule";
    public static final String APP_ROUTER_INTERCEPTORS_CLASS_NAME = "ARouter$$Interceptors$$RnModule";

    //上一次在异常时上报文件的时间
    private long lastUploadFileTime;
    //app 启动的时间
    private long appStartupTimeMillis;

    @Override
    public void attachBaseContext(Context context) {
        mContext = context.getApplicationContext();
        mHandler = HandlerManager.obtainMainHandler();
        appStartupTimeMillis = System.currentTimeMillis();
    }

    @NonNull
    @Override
    public Class<RNActionRouter> onCreateAction() {
        return RNActionRouter.class;
    }

    @Override
    public void onCreate(RNActionRouter actionRouter) {
        ((Application) BaseApplication.getMyApplicationContext()).registerActivityLifecycleCallbacks(this);
        actionRouter.addAction(RouterConstant.FRAGMENT_ACTION, new RNFragmentActionImpl());
        actionRouter.addAction(RouterConstant.ACTIVITY_ACTION, new RNActivityActionImpl());
        actionRouter.addAction(RouterConstant.FUNCTION_ACTION, new RNFunctionActionImpl());
        instance = this;

        try {
            String rootClassName = APPLICATION_PACKAGE_NAME + "." + APP_ROUTER_ROOT_CLASS_NAME;
            ((IRouteRoot) (Class.forName(rootClassName).getConstructor().newInstance())).loadInto(Warehouse.groupsIndex);
        } catch (Exception e) {
        }

        try {
            String providersClassName = APPLICATION_PACKAGE_NAME + "." + APP_ROUTER_PROVIDERS_CLASS_NAME;
            ((IProviderGroup) (Class.forName(providersClassName).getConstructor().newInstance())).loadInto(Warehouse.providersIndex);
        } catch (Exception e) {
        }

        try {
            String interceptorsClassName = APPLICATION_PACKAGE_NAME + "." + APP_ROUTER_INTERCEPTORS_CLASS_NAME;
            ((IInterceptorGroup) (Class.forName(interceptorsClassName).getConstructor().newInstance())).loadInto(Warehouse.interceptorsIndex);
        } catch (Exception e) {
        }

        XmUriRouterManager.getInstance().addBundleRouteHandler(Configure.rnBundleModel.bundleName, new RnBundleRouteHandler());
        if (AlarmUtil.getRepeatAlarmSetting(BaseApplication.getMyApplicationContext()) != null) {
            AlarmUtil.startRepeatAlarm(BaseApplication.getMyApplicationContext());
        }
    }

    @Override
    public void initApp() {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            initSync();
        } else {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    initSync();
                }
            }).start();
        }

    }

    @Override
    public void exitApp() {

    }

    @Override
    public void onSuccess() {
        refreshXMTrace();
    }

    public static RNApplication getInstance() {
        return instance;
    }

    private synchronized void initSync() {
        if (!RNUtils.isX86Arch()) {
            initRNApi();
        }
    }

    private void initRNApi() {
        if (inited) {
            return;
        }
        List<ReactPackage> list = new ArrayList<>();
        list.add(new RNReactPackage());
        list.add(new KSongReactNaivePackage());
        list.add(new SvgPackage());
        list.add(new FastImageViewPackage());
        list.add(new RNViewShotPackage());
        list.add(new LottiePackage());

        list.add(new RNAudioRecorderPlayerPackage());
        list.add(new ReactNativeFlashListPackage());
        list.add(new RCTImageCapInsetPackage());
        list.add(new PlayPagePackage());
        list.add(new AlarmPackage());
        list.add(new FootPrintPackage());
        list.add(new RecommendPackage());
        list.add(new ReactSliderPackage());
        list.add(new ReactNativePerformancePackage());

        int requestEnvironment = SharedPreferencesUtil.getInstance(mContext)
                .getInt(PreferenceConstantsInHost.TINGMAIN_KEY_REQUEST_ENVIRONMENT,
                        !ConstantsOpenSdk.isDebug || !isDebugSvrTest ? AppConstants.ENVIRONMENT_ON_LINE : AppConstants.ENVIRONMENT_TEST);
        boolean isOnline = requestEnvironment == AppConstants.ENVIRONMENT_ON_LINE || requestEnvironment == AppConstants.ENVIRONMENT_UAT;
        ENV env = ENV.ONLINE;
        if (requestEnvironment == AppConstants.ENVIRONMENT_UAT) {
            env = ENV.UAT;
        } else if (requestEnvironment == AppConstants.ENVIRONMENT_TEST) {
            env = ENV.TEST;
        }
        String channel = BaseDeviceUtil.getChannelInApk(mContext);

        int defaultLayerType = RNSettings.getDefaultLayerTypeFromSetting();

        boolean enableResourceOpt = RNSettings.getResourceLoadOptSwitchFromSetting();
        boolean enableRetry = RNSettings.getEnableRetryFromSetting();

        boolean enablePreloadPlayPage = RNSettings.getEnablePreloadPlayPage();
        String bizPreload = enablePreloadPlayPage ? "1" : "0";
        Log.d("z_rn", "initRNApi, enablePreloadPlayPage: " + enablePreloadPlayPage);

        boolean enablePreloadRecommend = RNSettings.getHomeRNPreloadFromSetting();
        String bizRecommendPreload = enablePreloadRecommend ? "1" : "0";
        Log.d("z_rn", "initRNApi, enablePreloadRecommend: " + enablePreloadRecommend);

        RNConfig.Builder builder = new RNConfig.Builder(MainApplication.getInstance().realApplication,
                Constants.KEY_STATISTICS, isOnline ? Constants.URL_STATISTICS : Constants.URL_STATISTICS_TEST, 1)
                .reactPackages(list).autoPreload(true)
                .enableDebug(ConstantsOpenSdk.isDebug)
                .enableResourceLoadOpt(enableResourceOpt)
                .enableRetryAfterError(enableRetry)
                .enableAccessFile(RNSettings.getEnableAccessFile())
                .defaultLayerType(defaultLayerType)
                .overrideColorScheme(buildOverrideColorScheme())
                .channel(channel).deviceId(DeviceUtil.getDeviceToken(mContext))
                .imeiProvider(new IXmImeiProvider() {
                    @Override
                    public String getImei() {
                        return BaseDeviceUtil.getIMEI(mContext);
                    }
                })
                .widgetUsageListener(reactNativeWidgetUsageListener)
                .enableCorruptFileVerify(RNSettings.getEnableVerifyCorruptFileFromSetting())
                .uploadFileIfLoadError(RNSettings.getEnableUploadFileIfLoadErrorFromSetting())
                .useCopyInsteadOfRename(RNSettings.getEnableUseCopyInsteadOfRenameFromSetting())
                .onlyFirstInstallUseJSBundle(RNSettings.onlyFirstInstallUseJSBundle())
                .bizPreload(bizPreload)
                .appStartupTimeMillis(appStartupTimeMillis)
                .localCompileConfig(localCompileConfig)
                .errorListener(errorListener)
                .disableVersionCheck(RNSettings.getDisableVersionCheckFromSetting())
                .httpProvider(new IHttpProvider() {
                    @Override
                    public Map<String, String> getHeader() {
                        StringBuilder sb = new StringBuilder();
                        ConnectivityManager connectivityManager = SystemServiceManager.getConnectivityManager(BaseApplication.getMyApplicationContext());
                        TelephonyManager telephonyManager = SystemServiceManager.getTelephonyManager(BaseApplication.getMyApplicationContext());
                        sb.append("c-oper=");
                        int operator = NetworkType.getOperator(telephonyManager);
                        try {
                            String mobileOperatorName = CommonRequestM.getInstanse().getMobileOperatorName(operator);
                            if (!TextUtils.isEmpty(mobileOperatorName)) {
                                sb.append(mobileOperatorName);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        sb.append(";");
                        String netWorkType = NetworkUtils.getNetworkClass(mContext, connectivityManager);
                        if (!TextUtils.isEmpty(netWorkType)) {
                            sb.append("net-mode=");
                            sb.append(netWorkType);
                            sb.append(";");
                        }
                        if (FreeFlowService.getDeviceFreeFlowHistory()) {
                            sb.append("pcdnFree=1;");
                        }
                        Map<String, String> header = new HashMap<>();
                        header.put("cookie", sb.toString());
                        return header;
                    }
                })
                .env(env).isLazyNativeModules(true);
        if (ConstantsOpenSdk.isDebug) {
            builder.showPageInitData(SharedPreferencesUtil.getInstance(mContext).getBoolean("rn_show_init"));
            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(MainApplication.getInstance().realApplication);
            if (preferences.getBoolean("debug_mock_toggle", false)) {
                builder.mockUrl(preferences.getString("debug_http_check_mock", null));
            }

            PrinterHolder.setPrinter(mDebugPrinter);

            PerfMonitor.setDebug(true);
        }

        setReactTextViewCompatWidth();

        boolean engineUseHermes = RNSettings.checkHermesSwitch();
        builder.enableHermes(engineUseHermes);
        Logger.d("z_test", "initRNApi, enableHermes: " + engineUseHermes);

        builder.locationProvider(new ILocationProvider() {
            @Override
            public double getLatitude() {
                return LocationService.getInstance().getLatitude(mContext);//MyLocationManager.getInstance(mContext).getLatitude();
            }

            @Override
            public double getLongitude() {
                return LocationService.getInstance().getLongitude(mContext);//MyLocationManager.getInstance(mContext).getLongitude();
            }
        });
        builder.newServerConfigProvider(new INewServerConfigProvider() {
            @Override
            public boolean useNewServer() {
                return !ConfigureCenter.getInstance().getBool("sys", "is_use_old_dog_portal_server", false);
            }
        });
        if (RNUtils.isX86Arch()) {
            File file = X86Installer.getInstance().x86SoFolder();
            if (file == null || !file.exists()) {
                return;
            }
            builder.soPath(file.getAbsolutePath());
        } else if (!RNUtils.isX86Arch() && ConstantsOpenSdk.isBundleFrameWork) {
            builder.soPath(Configure.rnBundleModel.libraryPath);
            Logger.e("reactnative", Configure.rnBundleModel.libraryPath);
        }
        XMReactNativeApi.setOnConfigRefreshSuccessListener(this);
        XMReactNativeApi.init(builder.build());
        inited = true;
        ConfigureCenter.getInstance().registerConfigFetchCallback(this);
        setStatisticsConfig();
        RNApmManager.getInstance().setApmUploadListener(new ApmUploadListener() {
            @Override
            public void upload(String type, String subType, String log) {
                if (ConstantsOpenSdk.isDebug) {
                    Log.e("1111111111111", "type:" + type + "  subType:" + subType + "  log:" + log);
                }
                XmLogger.log(type, subType, log);
            }
        });
//        refreshXMTrace();
        DegradeHandler.getInstance().init();

        RNPerformance.init(BaseApplication.getTopActivity());

        if (enablePreloadRecommend) {
            preloadRecommendPage();
        }

        if (enablePreloadPlayPage) {
            if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()) {
                StartupOptManager.addMainLooperIdleHandler(new MessageQueue.IdleHandler() {
                    @Override
                    public boolean queueIdle() {
                        preloadPlayPage();
                        return false;
                    }
                });
            } else {
                preloadPlayPage();
            }
        }

        boolean enablePreloadQuickListen = RNSettings.getQuickListenRNPreloadFromSetting();
        Log.d("z_rn", "initRNApi, enablePreloadQuickListen: " + enablePreloadQuickListen);
        if (enablePreloadQuickListen) {
            if (HomeRecommendPageLoadingOptimizationManager.INSTANCE.isNeedForcePreloadRecommendPage()) {
                StartupOptManager.addMainLooperIdleHandler(new MessageQueue.IdleHandler() {
                    @Override
                    public boolean queueIdle() {
                        preloadQuickListen();
                        return false;
                    }
                });
            } else {
                preloadQuickListen();
            }
        }

        RNTrace.initAPICallTrace();

        //添加自定义的字体下载目录
        CustomFontManager.getInstance().addDownloadDirPath(RemoteTypefaceManager.INSTANCE.getMLocalDir());
    }

    private void preloadPlayPage() {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                XMReactNativeApi.preloadReactInstance("rn_player_page_v2");
                RNTrace.updateBundleInfo("rn_player_page_v2", null);
            }
        });
    }

    private void preloadRecommendPage() {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                XMReactNativeApi.preloadReactInstance(HomeRnUtils.getRNHomeBundleName());
                RNTrace.updateBundleInfo(HomeRnUtils.getRNHomeBundleName(), null);
            }
        });
    }

    private void preloadQuickListen() {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                XMReactNativeApi.preloadReactInstance(QuickListenTabAbManager.QUICK_LISTEN_BUNDLE_NAME);
                RNTrace.updateBundleInfo(QuickListenTabAbManager.QUICK_LISTEN_BUNDLE_NAME, null);
            }
        });
    }

    /**
     * 折叠屏的 text view 需要处理一个行高计算问题
     */
    private void setReactTextViewCompatWidth() {
        boolean foldScreen = BaseUtil.isFoldScreen(BaseApplication.getMyApplicationContext());
        Log.d("z_width", "setReactTextViewCompatWidth   foldScreen: "+ foldScreen);
        if (!foldScreen) {
            return;
        }
        int width = RNSettings.getReactTextViewCompatWidth();
        Log.d("z_width", "setReactTextViewCompatWidth  width: "+ width);
        if (width <= 0) {
            return;
        }
        ReactTextShadowNodeHelper.setCompatWidth(width);
        ReactTextShadowNodeHelper.setIsDebug(ConstantsOpenSdk.isDebug);
    }

    private OverrideColorScheme buildOverrideColorScheme() {

        boolean enable = RNSettings.enableCustomColorScheme();
        boolean sIsDarkMode = BaseFragmentActivity2.sIsDarkMode;
        Logger.w("z_rn", "buildOverrideColorScheme, sIsDarkMode: " + sIsDarkMode + ", enable: " + enable);

        if (!enable) {
            return null;
        }

        return new OverrideColorScheme() {
            @Override
            public String getScheme() {
                if (BaseFragmentActivity2.sIsDarkMode) {
                    return "dark";
                }
                return "light";
            }
        };
    }


    private void refreshXMTrace() {
        List<BundleBaseInfo> bundleList = XMReactNativeApi.getRemoteBundleInfo();
        int bundleSize = -1;
        if (bundleList != null && (bundleSize = bundleList.size()) > 0) {
            RNInfo[] rnInfos = new RNInfo[bundleSize];
            for (int i = 0; i < bundleSize; i++) {
                BundleBaseInfo baseInfo = bundleList.get(i);
                rnInfos[i] = new RNInfo(baseInfo.version, baseInfo.name);
            }
            XMTraceApi.getInstance().setRnBundles(rnInfos);
        }
    }

    private void setStatisticsConfig() {
        XMReactNativeApi.enableStatistics(ConfigureCenter.getInstance().getBool("android", "mermaid:rn:enable", true));
        XMReactNativeApi.setStatisticsSampling(ConfigureCenter.getInstance().getJsonString("android", "mermaid:rn:sampling", null));
        XMReactNativeApi.useGlide(ConfigureCenter.getInstance().getBool("android", "rnglide", true));
    }

    public boolean inited() {
        return inited;
    }

    @Override
    public void onUpdateSuccess() {
//        setStatisticsConfig();
        ReactViewDataHolder.getInstance().updateConfig();

    }

    @Override
    public void onRequestError() {

    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
    }

    @Override
    public void onActivityStarted(Activity activity) {
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    if (inited) {
                        XMReactNativeApi.refresh();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public void onActivityResumed(Activity activity) {

    }

    @Override
    public void onActivityPaused(Activity activity) {

    }

    @Override
    public void onActivityStopped(Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(Activity activity) {

    }

    private Printer mDebugPrinter = new Printer() {
        public static final String PRINT_TAG = "rn_printer";
        @Override
        public void logMessage(DebugOverlayTag tag, String message, Object... args) {
            String msg = message + Arrays.toString(args);
            Log.w(PRINT_TAG + tag.name, msg);
        }

        @Override
        public void logMessage(DebugOverlayTag tag, String message) {
            Log.w(PRINT_TAG + tag.name, message);
        }

        @Override
        public boolean shouldDisplayLogMessage(DebugOverlayTag tag) {
            return false;
        }
    };

    private long lastReportTime;
    private IWidgetUsageListener reactNativeWidgetUsageListener = new IWidgetUsageListener() {
        @Override
        public void onWidgetUsed(String widgetName, String bundleName) {
            boolean reportWebViewUsage = ABTest.getBoolean("rn_webview_usage_report", false);
            if (!reportWebViewUsage) {
                return;
            }

            try {
                boolean usageReport = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, "rn_widget_usage_report");
                debugLog("onWidgetUsed opened fromSetting: " + usageReport);
                if (!usageReport) {
                    return;
                }
            } catch (Throwable ignore) {}

            if (System.currentTimeMillis() - lastReportTime < 800) {
                return;
            }

            if (TextUtils.isEmpty(bundleName) && !TextUtils.isEmpty(topBundleName)) {
                bundleName = topBundleName;
            }
            debugLog("onWidgetUsed: " + widgetName + ", " + bundleName);

            XDCSCollectUtil.statErrorToXDCS("onWidgetUsed_" + widgetName, bundleName);
            lastReportTime = System.currentTimeMillis();
        }
    };

    private static void debugLog(String s) {
        if (ConstantsOpenSdk.isDebug) {
            Log.d("z_test", s);
        }
    }

    private final IErrorListener errorListener = new IErrorListener() {
        @Override
        public void onCommonError(String s, Map<String, Object> map) {
            try {
                String data = map != null ? JSON.toJSONString(map) : "empty";
                IReporter.DEFAULT.directReport("installBundleError", data, s, null);
            } catch (Throwable ignore) {}
        }

        @Override
        public void onLoadErrorShow(String s) {
// RN 重试页  页面展示
            new XMTraceApi.Trace()
                    .pageView(56408, " rnRetry") // 页面出现在用户视野时上报一条埋点，包括离开页面后返回、息屏后亮屏等
                    .put("currPage", " rnRetry")
                    .createTrace();
        }

        @Override
        public void onLeaveLoadError() {
// RN 重试页  页面离开
            new XMTraceApi.Trace()
                    .pageExit2(56409) // 页面离开在用户视野时上报一条埋点，包括锁屏、回到主页面等
                    .put("currPage", " rnRetry")
                    .createTrace();
        }

        @Override
        public void onReloadClicked() {
// RN 重试页-重试按钮  点击事件
            new XMTraceApi.Trace()
                    .click(56410) // 用户点击时上报
                    .put("currPage", " rnRetry")
                    .put("type", "手动")
                    .createTrace();
        }
    };

    private final ILocalCompileConfig localCompileConfig = new ILocalCompileConfig() {
        @Override
        public boolean notNeedCompile(String s) {
            //不需要编译的黑名单
            List<String> blackList = RNSettings.getEnableCompileBlackListFromSetting();
            if (blackList != null && blackList.contains(s)) {
                return true;
            }
            return false;
        }

        @Override
        public boolean enableLocalCompileHermes() {
            return RNSettings.checkHermesSwitch() && RNSettings.enableLocalCompileHermes();
        }

        public boolean isSettingOpen(String s, boolean b) {
            return RNSettings.getBooleanFromSetting(s, b);
        }
    };

    //用来排查问题，上报本地文件到 APM，以分析到哪一步损坏的
    private final IFileCorruptUploader fileUploader = new IFileCorruptUploader() {
        @Override
        public void onFileCorrupt(String path) {
            sendXDCSMsg("findFileCorrupt >>> " + path);
        }

        @Override
        public void uploadFiles(File[] files, final Runnable callback) {
            if (files == null) {
                return;
            }

            if (System.currentTimeMillis() - lastUploadFileTime < 3000) {
                return;
            }

            lastUploadFileTime = System.currentTimeMillis();

            debugLog("bugfix uploadFiles begin, size: " + files.length);
            final AtomicInteger atomicInteger = new AtomicInteger(files.length);
            for (File file : files) {
                LocalFileManager.getInstance().dumpZipFile(new XmApm.IDumpFileListener() {
                    @Override
                    public void onSuccess() {
                        int i = atomicInteger.decrementAndGet();
                        debugLog("bugfix uploadFiles onSuccess " + i);
                        if (i <= 0 && callback != null) {
                            HandlerManager.postOnUIThread(new Runnable() {
                                @Override
                                public void run() {
                                    callback.run();
                                }
                            });
                        }

                        sendXDCSMsg("uploadFileForRNDebug >>> index:" + i);
                    }

                    @Override
                    public void onError(String msg) {
                        int i = atomicInteger.decrementAndGet();
                        debugLog("bugfix uploadFiles onError " + i);
                        if (i <= 0 && callback != null) {
                            HandlerManager.postOnUIThread(new Runnable() {
                                @Override
                                public void run() {
                                    callback.run();
                                }
                            });
                        }
                    }
                }, "", file.getAbsolutePath(), true);
            }
        }
    };

    private void sendXDCSMsg(String msg) {
        XDCSCollectUtil.statErrorToXDCS("rn_start_error_analysis", msg);
    }
}
