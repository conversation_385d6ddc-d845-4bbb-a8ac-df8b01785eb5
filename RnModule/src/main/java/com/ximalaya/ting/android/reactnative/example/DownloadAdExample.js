import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  NativeModules,
  NativeEventEmitter,
  requireNativeComponent,
} from 'react-native';

const { DownloadAd } = NativeModules;
const DownloadAdView = requireNativeComponent('DownloadAdView');

const DownloadAdExample = () => {
  const [adHeight, setAdHeight] = useState(0);
  const [adData, setAdData] = useState({
    slot_id: 16, // 下载广告位ID
    rewardCoin: 500, // 奖励金币数
    sourceName: 'welfareCenter', // 来源名称
    operationId: 'download_ad_' + Date.now(), // 操作ID
  });

  useEffect(() => {
    // 监听奖励成功事件
    const eventEmitter = new NativeEventEmitter(DownloadAd);
    const rewardSuccessSubscription = eventEmitter.addListener(
      'onRewardSuccess',
      (event) => {
        console.log('下载广告奖励成功:', event);
        // 处理奖励成功逻辑
      }
    );

    const rewardFailSubscription = eventEmitter.addListener(
      'onRewardFail',
      (event) => {
        console.log('下载广告奖励失败:', event);
        // 处理奖励失败逻辑
      }
    );

    return () => {
      rewardSuccessSubscription.remove();
      rewardFailSubscription.remove();
    };
  }, []);

  const handleHeightChange = (event) => {
    const { height } = event.nativeEvent;
    setAdHeight(height);
  };

  const handleAdClick = (event) => {
    console.log('下载广告被点击');
    // 处理广告点击逻辑
  };

  const handleRefreshAd = () => {
    // 刷新广告数据
    setAdData({
      ...adData,
      operationId: 'download_ad_' + Date.now(),
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>下载广告示例</Text>
      
      <View style={styles.adContainer}>
        <DownloadAdView
          style={[styles.adView, { height: adHeight }]}
          bindData={adData}
          onHeightChange={handleHeightChange}
          onAdClick={handleAdClick}
        />
      </View>

      <View style={styles.buttonContainer}>
        <Text style={styles.button} onPress={handleRefreshAd}>
          刷新广告
        </Text>
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>广告高度: {adHeight}px</Text>
        <Text style={styles.infoText}>奖励金币: {adData.rewardCoin}</Text>
        <Text style={styles.infoText}>来源: {adData.sourceName}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  adContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 20,
    overflow: 'hidden',
  },
  adView: {
    width: '100%',
  },
  buttonContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    color: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
});

export default DownloadAdExample;

