package com.ximalaya.ting.android.reactnative.manager;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.fragment.app.Fragment;

import com.facebook.react.ReactRootView;
import com.ximalaya.reactnative.XMReactNativeApi;
import com.ximalaya.reactnative.debug.DebugBundleManager;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.fragment.ManageFragment;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFragmentRouter;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.reactnative.RNApplication;
import com.ximalaya.ting.android.reactnative.fragment.RNFireworkFragment;
import com.ximalaya.ting.android.reactnative.fragment.ReactFragment;
import com.ximalaya.ting.android.reactnative.fragment.ReactHybridFragment;
import com.ximalaya.ting.android.reactnative.fragment.debug.DebugSettingFragment;
import com.ximalaya.ting.android.reactnative.fragment.debug.ReactTestFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Travis on 2018/6/5 下午4:34.
 *
 * <AUTHOR>
 */
public class RNFragmentActionImpl implements IRNFragmentRouter {
    private static final String NAME_RN = "rn";
    private static final String NAME_RN_TEST = "rntest";
    private static final String[] SINGLE_RN_LIST = {"rn_kg", "rn_asmr", "rn_assistant","rn_mc", "rn_ticket", "rn_ai_chat", "rn_quick_listen"};

    @Override
    public BaseFragment newRNFragment(String fragmentName, Bundle data) {
        ReactRootView.sBegin = System.currentTimeMillis();

        boolean canSlide = data == null ? false : "1".equals(data.getString("canSlide", null));
        if (NAME_RN.equals(fragmentName)) {
            String bundleName = data == null ? null : data.getString("bundle");
            if (TextUtils.isEmpty(bundleName)) {
                return null;
            }
            RNFunctionActionImpl.addTestIpIfDebug(data, bundleName);
            DebugBundleManager.getInstance().setDebugBundleFromParams(data);
            if (RNApplication.getInstance().inited()) {
                XMReactNativeApi.preloadReactInstance(bundleName);
            }
            ReactFragment fragment = new ReactFragment(canSlide);
            fragment.setArguments(data);
            return fragment;
        } else if (NAME_RN_TEST.equals(fragmentName) && ConstantsOpenSdk.isDebug) {
            ReactTestFragment fragment = new ReactTestFragment(canSlide);
            fragment.setArguments(data);
            return fragment;
        }
        return null;
    }

    @Override
    public BaseFragment newRNFragment(String fragmentName, Bundle data, ILoadBundleErrorInterceptor interceptor) {
        if (NAME_RN.equals(fragmentName)) {
            String bundleName = data == null ? null : data.getString("bundle");
            if (TextUtils.isEmpty(bundleName)) {
                return null;
            }
            RNFunctionActionImpl.addTestIpIfDebug(data, bundleName);
            DebugBundleManager.getInstance().setDebugBundleFromParams(data);
            if (RNApplication.getInstance().inited()) {
                XMReactNativeApi.preloadReactInstance(bundleName);
            }
            boolean canSlide = data.getBoolean("canSlide", true);
            ReactFragment fragment = ReactFragment.getInstance(canSlide, interceptor);
            fragment.setArguments(data);
            return fragment;
        } else if (NAME_RN_TEST.equals(fragmentName)) {
            String bundleName = data == null ? null : data.getString("bundle");
            if (TextUtils.isEmpty(bundleName)) {
                return null;
            }
            boolean canSlide = data.getBoolean("canSlide", true);
            ReactTestFragment fragment = new ReactTestFragment(canSlide);
            fragment.setArguments(data);
            return fragment;
        }
        return null;
    }

    @Override
    public void startRNFragment(Activity activity, String fragmentName, Bundle data) {
        if (activity instanceof MainActivity) {
            ManageFragment manageFragment = ((MainActivity) activity).getManageFragment();
            List<ManageFragment.MySoftReference> stacks = new ArrayList<>(manageFragment.mStacks);
            boolean isRemove = false;
            String bundleName = data == null ? null : data.getString("bundle");
            Fragment needStartFragment = null;
            retry:
            for (int i = 0; i < stacks.size(); i++) {
                ManageFragment.MySoftReference reference = stacks.get(i);
                Fragment fgm = reference.get();
                if (fgm instanceof ReactFragment) {
                    for (String name : SINGLE_RN_LIST) {
                        if (TextUtils.equals(((ReactFragment) fgm).getBundleName(), name) &&
                                TextUtils.equals(name, bundleName)) {
                            needStartFragment = fgm;
                            ((ReactFragment) fgm).setArguments2(data);
                            isRemove = true;
                            continue retry;
                        }
                    }
                }
                // 移除两个k歌页面之间的fragment栈
                if (isRemove) {
                    manageFragment.removeFragmentFromStacks(fgm, false);
                }
            }
            boolean keepPlayFra = XPlayPageRef.get() != null && data != null && "true".equals(data.getString("keepPlayFra", ""));
            boolean noShowRepeat = data != null && "true".equals(data.getString("noShowRepeat", ""));
            if (needStartFragment == null) {
                for (String name : SINGLE_RN_LIST) {
                    if (data != null && TextUtils.equals(data.getString("bundle"), name) && !keepPlayFra) {
                        ((MainActivity) activity).hidePlayFragmentWillShow(null, false);
                    }
                }
                BaseFragment baseFragment = newRNFragment(fragmentName, data);
                if (keepPlayFra && XPlayPageRef.get() != null) {
                    XPlayPageRef.get().showFragmentOnPlayPage(baseFragment, noShowRepeat);
                } else {
                    ((MainActivity) activity).startFragment(baseFragment);
                }
            }
        }
    }

    @Override
    public void startRNFragment(Activity activity, String fragmentName, Bundle data, int inAnim, int outAnim) {
        if (activity instanceof MainActivity) {
            ManageFragment manageFragment = ((MainActivity) activity).getManageFragment();
            List<ManageFragment.MySoftReference> stacks = new ArrayList<>(manageFragment.mStacks);
            boolean isRemove = false;
            String bundleName = data == null ? null : data.getString("bundle");
            Fragment needStartFragment = null;
            retry:
            for (int i = 0; i < stacks.size(); i++) {
                ManageFragment.MySoftReference reference = stacks.get(i);
                Fragment fgm = reference.get();
                if (fgm instanceof ReactFragment) {
                    for (String name : SINGLE_RN_LIST) {
                        if (TextUtils.equals(((ReactFragment) fgm).getBundleName(), name) &&
                                TextUtils.equals(name, bundleName)) {
                            needStartFragment = fgm;
                            ((ReactFragment) fgm).setArguments2(data);
                            isRemove = true;
                            continue retry;
                        }
                    }
                }
                // 移除两个k歌页面之间的fragment栈
                if (isRemove) {
                    manageFragment.removeFragmentFromStacks(fgm, false);
                }
            }
            boolean keepPlayFra = XPlayPageRef.get() != null && data != null && "true".equals(data.getString("keepPlayFra", ""));
            boolean noShowRepeat = data != null && "true".equals(data.getString("noShowRepeat", ""));
            if (needStartFragment == null) {
                for (String name : SINGLE_RN_LIST) {
                    if (data != null && TextUtils.equals(data.getString("bundle"), name) && !keepPlayFra) {
                        ((MainActivity) activity).hidePlayFragmentWillShow(null, false);
                    }
                }
                BaseFragment baseFragment = newRNFragment(fragmentName, data);
                if (keepPlayFra && XPlayPageRef.get() != null) {
                    XPlayPageRef.get().showFragmentOnPlayPage(baseFragment, noShowRepeat);
                } else {
                    ((MainActivity) activity).startFragment(baseFragment, inAnim, outAnim);
                }
            }
        }
    }

    @Override
    public BaseFragment newHybridFragment(String url, boolean showTitle) {
        return ReactHybridFragment.newInstance(url, showTitle);
    }

    @Override
    public BaseFragment newDebugSettingFragment() {
        return new DebugSettingFragment();
    }

    @Override
    public BaseFragment newRNFireworkFragment(Bundle data) {
        RNFireworkFragment fragment = new RNFireworkFragment();
        fragment.setArguments(data);
        return fragment;
    }
}
