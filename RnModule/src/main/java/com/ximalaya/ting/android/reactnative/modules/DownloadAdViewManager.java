package com.ximalaya.ting.android.reactnative.modules;

import android.widget.FrameLayout;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.react.uimanager.events.RCTEventEmitter;
import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.manager.ad.RnDownloadAdManager;
import com.ximalaya.ting.android.host.view.ad.RnDownloadAdView;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

import java.util.Map;

/**
 * 为rn提供下载广告视图
 */
public class DownloadAdViewManager extends SimpleViewManager<FrameLayout> {
    private static final String NAME = "DownloadAdView";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    protected RnDownloadAdView createViewInstance(final ThemedReactContext reactContext) {
        return new RnDownloadAdView(reactContext);
    }

    @ReactProp(name = "bindData")
    public void bindData(final RnDownloadAdView layout, ReadableMap data) {
        int positionId = data.getInt("positionId");
        String positionName = data.getString("positionName");
        String sourceName = data.getString("sourceName");

        RnDownloadAdManager.loadRnDownloadAd(layout, positionId, positionName, sourceName, new RnDownloadAdManager.ILoadDownloadAdCallback() {
            @Override
            public void onLoadSuccess(int adHeight) {
                if (ConstantsOpenSdk.isDebug) {
                    CustomToast.showFailToast("onHeightChange height =" + adHeight);
                }
                WritableMap event = Arguments.createMap();
                event.putInt("height", adHeight);
                ThemedReactContext reactContext = (ThemedReactContext) layout.getContext();
                reactContext.getJSModule(RCTEventEmitter.class).receiveEvent(layout.getId(), "onHeightChange", event);
            }

            @Override
            public void onLoadFail() {
                if (ConstantsOpenSdk.isDebug) {
                    CustomToast.showFailToast("onHeightChange height =0");
                }
                WritableMap event = Arguments.createMap();
                event.putInt("height", 0);
                ThemedReactContext reactContext = (ThemedReactContext) layout.getContext();
                reactContext.getJSModule(RCTEventEmitter.class).receiveEvent(layout.getId(), "onHeightChange", event);
            }
        });
    }

    @Override
    public Map<String, Object> getExportedCustomDirectEventTypeConstants() {
        return MapBuilder.<String, Object>builder()
                .put("onHeightChange", MapBuilder.of("registrationName", "onHeightChange"))
                .build();
    }
}

