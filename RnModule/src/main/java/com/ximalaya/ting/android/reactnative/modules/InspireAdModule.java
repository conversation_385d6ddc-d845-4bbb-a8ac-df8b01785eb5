package com.ximalaya.ting.android.reactnative.modules;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.common.LifecycleState;
import com.facebook.react.module.annotations.ReactModule;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.ximalaya.ting.android.adsdk.external.feedad.IAdModel;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.manager.ad.RnDownloadAdManager;
import com.ximalaya.ting.android.host.manager.ad.RnInspireAdManager;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.reactnative.utils.RNUtils;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

/**
 * 为rn提供唤端广告视图时，需要通过module发送唤端奖励通知
 */
@ReactModule(name = InspireAdModule.NAME)
public class InspireAdModule extends ReactContextBaseJavaModule {
    public static final String NAME = "InspireAd";

    public InspireAdModule(ReactApplicationContext reactContext) {
        super(reactContext);
        RnInspireAdManager.setCurrentRewardCallback(new RnInspireAdManager.IRewardInspireAdCallback() {
            @Override
            public void onRewardSuccess(IAdModel adModel, int rewardCoin, String sourceName) {
                try {
                    WritableMap event = Arguments.createMap();
                    if (adModel != null) {
                        event.putString("adId", adModel.getAdid() + "");
                        event.putString("adResponseId", adModel.getResponseId() + "");
                        event.putString("ecpm", adModel.getEcpm());
                        event.putInt("encryptType", adModel.getEncryptType());
                        event.putInt("rewardCoin", rewardCoin);
                        event.putString("sourceName", sourceName);
                    }
                    if (ConstantsOpenSdk.isDebug) {
                        CustomToast.showFailToast("onRewardSuccess");
                    }
                    RNUtils.sendEvent(getReactApplicationContext(), "onRewardSuccess", event);
                } catch (Exception e) {
                    Logger.logToFile("RnInspireAd", "onRewardSuccess exception =" + e.getMessage());
                    e.printStackTrace();
                }
            }

            @Override
            public void onRewardFail(String msg, String sourceName) {
                WritableMap event = Arguments.createMap();
                event.putString("msg", msg);
                event.putString("sourceName", sourceName);
                if (ConstantsOpenSdk.isDebug) {
                    CustomToast.showFailToast("onRewardFail");
                }
                RNUtils.sendEvent(getReactApplicationContext(), "onRewardFail", event);
            }
        });

        RnDownloadAdManager.setCurrentRewardCallback(new RnDownloadAdManager.IRewardDownloadAdCallback() {
            @Override
            public void onInstallSuccess(Advertis advertis) {
                if (advertis == null) {
                    return;
                }
                WritableMap event = Arguments.createMap();
                event.putString("adId", advertis.getAdid() + "");
                event.putString("adResponseId", advertis.getResponseId() + "");
                event.putString("rewardCoin", advertis.getPriceSecondBanner() + "");
                if (ConstantsOpenSdk.isDebug) {
                    CustomToast.showFailToast("onInstallSuccess");
                }
                RNUtils.sendEvent(getReactApplicationContext(), "onInstallSuccess", event);
            }

            @Override
            public void onOpenSuccess(Advertis advertis) {
                WritableMap event = Arguments.createMap();
                event.putString("adId", advertis.getAdid() + "");
                event.putString("adResponseId", advertis.getResponseId() + "");
                event.putString("rewardCoin", advertis.getPriceSecondBanner() + "");
                if (ConstantsOpenSdk.isDebug) {
                    CustomToast.showFailToast("onOpenSuccess");
                }
                RNUtils.sendEvent(getReactApplicationContext(), "onOpenSuccess", event);
            }
        });
    }

    @Override
    public String getName() {
        return NAME;
    }

    @ReactMethod
    public void onAdClose() {
        RnInspireAdManager.resetInspireAd();
    }

    @ReactMethod
    public void openTask() {
        RnDownloadAdManager.onRnDialogClick();
    }
}
