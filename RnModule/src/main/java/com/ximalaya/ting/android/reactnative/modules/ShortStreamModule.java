package com.ximalaya.ting.android.reactnative.modules;


import android.app.Activity;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.module.annotations.ReactModule;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.dialog.PlayContinueAlertDialog;
import com.ximalaya.ting.android.host.fragment.IQuickListenTabFragment;
import com.ximalaya.ting.android.host.manager.PlanTerminateManagerForQuickListen;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.quicklisten.ICallback;
import com.ximalaya.ting.android.host.manager.quicklisten.IPlayCallback;
import com.ximalaya.ting.android.host.manager.quicklisten.IQuickListenPage;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManager;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenManager;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenPerformanceManager;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenPerformanceManagerKt;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.manager.PlanTerminateManagerForPlayForQuickListen;
import com.ximalaya.ting.android.opensdk.player.manager.QuickListenForPlayProcessUtil;
import com.ximalaya.ting.android.opensdk.util.EasyConfigure;
import com.ximalaya.ting.android.reactnative.R;
import com.ximalaya.ting.android.reactnative.modules.thirdParty.slider.ReactSlider;
import com.ximalaya.ting.android.reactnative.utils.RNUtils;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ReactModule(name = ShortStreamModule.NAME)
public class ShortStreamModule extends ReactContextBaseJavaModule {
    public static final String NAME = "ShortStream";
    private static final String BROADCAST = "ShortStreamBroadcast";

    public ShortStreamModule(ReactApplicationContext reactApplicationContext) {
        super(reactApplicationContext);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @ReactMethod
    public void getSliderSupport(Promise promise){

        try {
            Class<ReactSlider> reactSliderClass = ReactSlider.class;
            promise.resolve("1");
        } catch (Throwable e) {
            e.printStackTrace();
        }
        promise.resolve(null);
    }

    @ReactMethod
    public void localLog(String log, Promise promise){
        try {
            Logger.d(NAME, log);
            XDCSCollectUtil.statErrorToXDCS("QuickListen", log);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        promise.resolve(null);
    }

    @ReactMethod
    public void setPlayRate(float rate) {
        XmPlayerManager.getInstance(ToolUtil.getCtx()).setQuickListenTempo(rate);
    }

    @ReactMethod
    public void setEnableSkipHead(boolean enable) {
        QuickListenForPlayProcessUtil.saveEnableSkipHead(ToolUtil.getCtx(), enable);
    }

    @ReactMethod
    public void getPlayRate(Promise promise) {
        promise.resolve(QuickListenForPlayProcessUtil.getQuickListenTempForMain(ToolUtil.getCtx()));
    }

    @ReactMethod
    public void getEnableSkipHead(Promise promise) {
        promise.resolve(QuickListenForPlayProcessUtil.getEnableSkipHead(ToolUtil.getCtx()));
    }

    @ReactMethod
    public void popupRateView() {
        IMainFunctionAction action = null;
        try {
            MainActionRouter router = Router.getActionRouter(Configure.BUNDLE_MAIN);
            if (router != null) {
                action = router.getFunctionAction();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (action != null) {
            action.openQuickListenTempo(new IMainFunctionAction.IQuickListenTempoDialogCallBack() {
                @Override
                public void onSpeedSelected(float tempo) {

                }
            });
        }
    }

    @ReactMethod
    public void setAutoCloseParams(ReadableMap map, Promise promise) {
        int type = RNUtils.optIntFromRMap(map, "type");
        int value = RNUtils.optIntFromRMap(map, "value");

        if (type < 0) {
            return;
        }

        String lastPlanTerminateData = new JSONObject(map.toHashMap()).toString();

        if (type != 0) {
            MMKVUtil.getInstance().saveString(PreferenceConstantsInOpenSdk.KEY_LAST_QUICK_LISTEN_PLAN_TERMINAL, lastPlanTerminateData);

            if (type == 1 || type == 2) {
                int terminalType = -1;

                if (type == 2) {
                    if (value == 1) {
                        terminalType = PlanTerminateManagerForPlayForQuickListen.TIMER_CURRENT;
                    }
                } else {
                    if (value == 15) {
                        terminalType = PlanTerminateManagerForPlayForQuickListen.TIMER_15_MIN;
                    } else if (value == 30) {
                        terminalType = PlanTerminateManagerForPlayForQuickListen.TIMER_30_MIN;
                    } else if (value == 60) {
                        terminalType = PlanTerminateManagerForPlayForQuickListen.TIMER_60_MIN;
                    } else if (value == 90) {
                        terminalType = PlanTerminateManagerForPlayForQuickListen.TIMER_90_MIN;
                    } else if (value == 45) {
                        terminalType = PlanTerminateManagerForPlayForQuickListen.TIMER_45_MIN;
                    } else {
                        terminalType = value;
                    }
                }

                if (terminalType >= 0) {
                    PlanTerminateManagerForQuickListen.startTimer(terminalType);
                }
            } else if (type == 3) {
                PlanTerminateManagerForQuickListen.startCustomTimer((long) value * 60 * 1000);
            }
        } else {
            PlanTerminateManagerForQuickListen.forceCancel();
        }
        promise.resolve("");
    }

    @ReactMethod
    public void getAutoCloseParams(Promise promise) {
        if (promise == null) {
            return;
        }

        if (PlanTerminateManagerForQuickListen.isTiming()) {
            try {
                String string = MMKVUtil.getInstance().getString(PreferenceConstantsInOpenSdk.KEY_LAST_QUICK_LISTEN_PLAN_TERMINAL);
                if (!TextUtils.isEmpty(string)) {
                    promise.resolve(RNUtils.jsonToReact(new JSONObject(string)));
                } else {
                    promise.resolve("");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            try {
                promise.resolve(RNUtils.jsonToReact(new JSONObject("{\"type\":0}")));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @ReactMethod
    public void getLastAutoCloseParams(Promise promise) {
        if (promise != null) {
            try {
                String string = MMKVUtil.getInstance().getString(PreferenceConstantsInOpenSdk.KEY_LAST_QUICK_LISTEN_PLAN_TERMINAL);
                if (!TextUtils.isEmpty(string)) {
                    promise.resolve(RNUtils.jsonToReact(new JSONObject(string)));
                } else {
                    promise.resolve("");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @ReactMethod
    public void setEnableAgentGuide(boolean enable, Promise promise) {
        MMKVUtil.getInstance().saveBoolean("enable_agent_guide", enable);
        if (promise != null) {
            promise.resolve("");
        }
    }

    @ReactMethod
    public void setNeedAgentGuide(boolean enable, Promise promise) {
        MMKVUtil.getInstance().saveBoolean("quick_listen_need_agent_guide", enable);
        if (promise != null) {
            promise.resolve("");
        }
    }

    @ReactMethod
    public void getShowFrom(Promise promise) {
        if (promise != null) {
            promise.resolve(MMKVUtil.getInstance().getString("quick_listen_from", "底tab"));
        }
    }

    @ReactMethod
    public void sendMessage(String eventName, ReadableMap params) {
        Logger.d(NAME, "eventName" + eventName + ", params: " + params);
        if (params != null && !TextUtils.isEmpty(eventName)) {
            HashMap<String, Object> map = params.toHashMap();
            Intent intent = new Intent(BROADCAST).putExtra("data", map).putExtra("eventName", eventName);
            LocalBroadcastManager.getInstance(getReactApplicationContext()).sendBroadcast(intent);
        }
    }

    @ReactMethod
    public void getListSource(String cardType, String refId, Promise promise) {
        Logger.log("ShortStreamModule : getListSource   cardType=" + cardType + "   refId=" + refId);
        IQuickListenPage quickListenPage = QuickListenManager.getQuickListenPage();
        if (quickListenPage != null && refId != null) {
            try {
                promise.resolve(quickListenPage.getListSource(cardType, Long.parseLong(refId)));
            } catch (Exception e) {
            }
        }
    }

    @ReactMethod
    public void sendPlaySoundInList(final String cardType, final double refId, final Promise promise) {
        Logger.log("ShortStreamModule : sendPlaySoundInList   cardType=" + cardType + "   refId=" + refId);
        HandlerManager.postOnMainAuto(new Runnable() {
            @Override
            public void run() {
                IQuickListenPage quickListenPage = QuickListenManager.getQuickListenPage();
                if (quickListenPage != null) {
                    try {
                        quickListenPage.sendPlaySoundInList(cardType, (long) refId);
                    } catch (Exception e) {
                    }
                }
                promise.resolve(true);
            }
        });
    }

    @ReactMethod
    public void setRNTouchPassThrough(boolean passThrough) {
        Logger.logToFile(NAME, "ShortStreamModule : setRNTouchPassThrough   passThrough=" + passThrough);
        IQuickListenPage quickListenPage = QuickListenManager.getQuickListenPage();
        if (quickListenPage != null) {
            quickListenPage.setRNTouchPassThrough(passThrough);
        }
    }

    @ReactMethod
    public void setNonPenetratingHeight(int height) {
        Logger.logToFile(NAME, "ShortStreamModule : setNonPenetratingHeight   height=" + height);
        IQuickListenPage quickListenPage = QuickListenManager.getQuickListenPage();
        if (quickListenPage != null) {
            quickListenPage.setNonPenetratingHeight(height);
        }
    }

    @ReactMethod
    public void tryResumeTrackPlay(Promise promise) {
        XmPlayerManager xmPlayerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext());
        boolean pauseByUser = QuickListenDataManager.Companion.getInstance().pauseByUser();
        boolean isQuickListen = xmPlayerManager.isQuickListen();
        Logger.logToFile(XmPlayerModule.NAME, "tryResumeTrackPlay pauseByUser=" + pauseByUser + ",isQuickListen=" + isQuickListen);
        if (!pauseByUser && isQuickListen) {
            xmPlayerManager.play();
            promise.resolve(true);
        } else {
            promise.resolve(false);
        }
    }

    private final ICallback mCallback = new ICallback() {
        @Override
        public void playListChange(@NonNull List<JSONObject> list, long tabId, long lastRefId) {
            QuickListenDataManager.Companion.getInstance().logToFile(NAME,
                    "playListChange tabId=" + tabId + ",list=" + (list != null ? list.size() : -1));
            try {
                JSONArray jsonArray = new JSONArray(list);
                String list_str = jsonArray.toString();

                Map<String, Object> params = new HashMap<>();
                params.put("tabId", String.valueOf(tabId));
                params.put("syncType", "0"); // 0,1
                params.put("list", list_str); // list
                params.put("trackId", String.valueOf(lastRefId));
                RNUtils.sendEvent(getReactApplicationContext(), "onSyncDataList", params);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    @ReactMethod
    public void requestData(ReadableMap map, final Promise promise) {
//        QuickListenDataManager.Companion.getInstance().setCallbackRN(mCallback);
        String tabIdStr = RNUtils.optStringFromRMap(map, "tabId");
        long tabId = -1;
        try {
            tabId = Long.parseLong(tabIdStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Logger.logToFile(NAME, "requestData tabIdStr=" + tabIdStr + ",tabId=" + tabId);
        if (tabId <= 0) {
            promise.reject("tabId 无效");
            return;
        }

        WeakReference<IQuickListenTabFragment> fragmentWR = QuickListenDataManager.Companion.getInstance().getMQuickListenFragmentWR();
        if (fragmentWR != null && fragmentWR.get() != null) {
            IQuickListenTabFragment fragment = fragmentWR.get();
            int requestId = fragment.getRequestId(tabId);
            QuickListenDataManager.Companion.getInstance().requestQuickListenData(false, requestId, tabId, new IDataCallBack<List<JSONObject>>() {
                @Override
                public void onSuccess(@Nullable List<JSONObject> data) {
                    try {
                        JSONArray jsonArray = new JSONArray(data);
                        promise.resolve(jsonArray.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onError(int code, String message) {
                    promise.reject(code + "", message);
                }
            }, null, null);
        } else {
            promise.reject("页面被销毁");
        }
    }

    long lastClickTime = 0;

    @ReactMethod
    public void negativeData(ReadableMap map, final Promise promise) {
        if (Math.abs(System.currentTimeMillis() - lastClickTime) < 2000
                && EasyConfigure.getBoolean("quick_listen_negative_check_time", true)) {
            return;
        }
        lastClickTime = System.currentTimeMillis();
//        {tabId: 'xx', trackIds: ['xx'] }
        long tabId = -1;
        long trackId = -1;
        try {
            String tabIdStr = RNUtils.optStringFromRMap(map, "tabId");
            ReadableArray array = RNUtils.optArrayFromRMap(map, "trackIds");
            trackId = (long) array.getDouble(0);
            tabId = Long.parseLong(tabIdStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Logger.logToFile(NAME, "negativeData tabId=" + tabId + ",trackId=" + trackId);
        if (tabId <= 0) {
            promise.reject("tabId 无效");
            return;
        }
        if (trackId <= 0) {
            promise.reject("trackId 无效");
            return;
        }
        final long finalTabId = tabId;
        QuickListenDataManager.Companion.getInstance().negativeQuickListenRn(tabId, trackId, new IPlayCallback() {
            @Override
            public void playQuickListenList(boolean needPlay, @NonNull List<? extends JSONObject> playList, int position, long willPlayRefId) {
                WeakReference<IQuickListenTabFragment> fragmentWR = QuickListenDataManager.Companion.getInstance().getMQuickListenFragmentWR();
                if (fragmentWR != null && fragmentWR.get() != null) {
                    IQuickListenTabFragment tabFragment = fragmentWR.get();
                    tabFragment.updateTab(needPlay, finalTabId, playList, position, willPlayRefId);
                }
            }
        });
    }

    @ReactMethod
    public void playTrack(ReadableMap map, final Promise promise) {
//        NativeModules.ShortStream.playTrack({tabId: ' 全部', trackId: 'xxx'})
        long tabId = -1;
        long trackId = -1;
        try {
            String tabIdStr = RNUtils.optStringFromRMap(map, "tabId");
            String trackIdStr = RNUtils.optStringFromRMap(map, "trackId");
            tabId = Long.parseLong(tabIdStr);
            trackId = Long.parseLong(trackIdStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Logger.logToFile(NAME, "playTrack tabId=" + tabId + ",trackId=" + trackId);
        if (tabId <= 0) {
            promise.reject("tabId 无效");
            return;
        }
        if (trackId <= 0) {
            promise.reject("trackId 无效");
            return;
        }
        WeakReference<IQuickListenTabFragment> fragmentWR = QuickListenDataManager.Companion.getInstance().getMQuickListenFragmentWR();
        if (fragmentWR != null && fragmentWR.get() != null) {
            IQuickListenTabFragment tabFragment = fragmentWR.get();
            tabFragment.playTrack(tabId, trackId);
        }
    }

    @ReactMethod
    public void pauseTrack(ReadableMap map, final Promise promise) {
//        NativeModules.ShortStream.pauseTrack({tabId: ' 全部'})
        long tabId = -1;
        try {
            String tabIdStr = RNUtils.optStringFromRMap(map, "tabId");
            tabId = Long.parseLong(tabIdStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Logger.logToFile(NAME, "pauseTrack tabId=" + tabId);
        if (tabId <= 0) {
            promise.reject("tabId 无效");
            return;
        }
        XmPlayerManager xmPlayerManager = XmPlayerManager.getInstance(ToolUtil.getCtx());
        if (xmPlayerManager.isPlaying()) {
            xmPlayerManager.pause(PauseReason.Business.QUICK_LISTEN_USER_PAUSE_RN);
        }
    }

    @ReactMethod
    public void sendAiAgentQuery(ReadableMap map, final Promise promise) {
        long tabId = -1;
        String text = "";
        try {
            String tabIdStr = RNUtils.optStringFromRMap(map, "tabId");
            text = RNUtils.optStringFromRMap(map, "text");
            tabId = Long.parseLong(tabIdStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Logger.logToFile(NAME, "sendAiAgentQuery tabId=" + tabId + ",text=" + text);
        if (tabId <= 0) {
            promise.reject("tabId 无效");
            return;
        }
        if (TextUtils.isEmpty(text)) {
            promise.reject("trackId 无效");
            return;
        }

        WeakReference<IQuickListenTabFragment> fragmentWR = QuickListenDataManager.Companion.getInstance().getMQuickListenFragmentWR();
        if (fragmentWR != null && fragmentWR.get() != null) {
            IQuickListenTabFragment tabFragment = fragmentWR.get();
            tabFragment.sendAiAgentQuery(tabId, text);
        }
    }

    @ReactMethod
    public void onPageSelected(int cardIndex, String tabId, String refId) {
        Logger.d(NAME, "onPageSelected cardIndex=" + cardIndex + ",tabId=" + tabId + ",refId=" + refId);
    }

    @ReactMethod
    public void pageInitToNative() {
        Logger.d(NAME, "pageInitToNative");
        QuickListenPerformanceManager.Companion.getInstance().stateChanged(QuickListenPerformanceManagerKt.STATE_RN_PAGE_INIT);
    }

    @ReactMethod
    public void pageUnMountToNative() {
        Logger.d(NAME, "pageUnMountToNative");
    }

    @ReactMethod
    public void saveBehaviorToLocal(ReadableMap map, final Promise promise) {
        long trackId = -1;
        String behaveType = "";
        try {
            behaveType = RNUtils.optStringFromRMap(map, "behaveType");
            double trackIdStr = RNUtils.optDoubleFromRMap(map, "trackId");
            trackId = (long) trackIdStr;

            WeakReference<IQuickListenTabFragment> fragmentWR =
                    QuickListenDataManager.Companion.getInstance().getMQuickListenFragmentWR();
            if (fragmentWR != null && fragmentWR.get() != null) {
                IQuickListenTabFragment tabFragment = fragmentWR.get();
                Logger.log("ShortStreamModule : saveBehaviorToLocal " + trackId + "   " + behaveType);
                tabFragment.saveBehaviorToLocal(trackId, behaveType);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        promise.resolve("");
    }

    @ReactMethod
    public void getMixWithOthersStyle(final Promise promise) {
        if (promise != null) {
            promise.resolve(MMKVUtil.getInstance(PlayerConstants.FILENAME_XM_PLAYER_AUDIO_FOCUS_DATA)
                    .getBoolean(PlayerConstants.TINGMAIN_KEY_PLAY_CONTINUE_WHEN_IINTERRUPTED)
            );
        }
    }

    @ReactMethod
    public void setMixWithOthersStyle(final boolean value, final Promise promise) {
        if (promise == null) {
            return;
        }
        HandlerManager.postOnMainAuto(new Runnable() {
            @Override
            public void run() {
                if (value) {
                    PlayContinueAlertDialog alert = new PlayContinueAlertDialog();
                    alert.setTitle("注意");
                    alert.setMessage(ToolUtil.getCtx().getString(R.string.host_play_continue));
                    alert.setOkBtn("确认开启", new IHandleOk() {
                        @Override
                        public void onReady() {
                            MMKVUtil.getInstance(PlayerConstants.FILENAME_XM_PLAYER_AUDIO_FOCUS_DATA)
                                    .saveBoolean(PlayerConstants.TINGMAIN_KEY_PLAY_CONTINUE_WHEN_IINTERRUPTED, value);
                            promise.resolve(true);
                        }
                    });
                    alert.setCancelBtn("取消", new IHandleOk() {
                        @Override
                        public void onReady() {
                            promise.resolve(false);
                        }
                    });
                    Activity topActivity = MainApplication.getTopActivity();
                    if (topActivity instanceof BaseFragmentActivity) {
                        alert.showAlert(((BaseFragmentActivity) topActivity).getSupportFragmentManager());
                    }
                } else {
                    MMKVUtil.getInstance(PlayerConstants.FILENAME_XM_PLAYER_AUDIO_FOCUS_DATA)
                            .saveBoolean(PlayerConstants.TINGMAIN_KEY_PLAY_CONTINUE_WHEN_IINTERRUPTED, value);
                    promise.resolve(true);
                }
            }
        });

    }
}
