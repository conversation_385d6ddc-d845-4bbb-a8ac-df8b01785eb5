package com.ximalaya.ting.android.reactnative.modules;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.module.annotations.ReactModule;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.base.IMediaPlayer;
import com.ximalaya.ting.android.opensdk.player.simplePlayer.base.SimpleMediaPlayerFactory;
import com.ximalaya.ting.android.reactnative.utils.RNUtils;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by hebin on 2019-05-08.
 */
@ReactModule(name = SimplePlayerModule.NAME)
public class SimplePlayerModule extends ReactContextBaseJavaModule {

    public static final String NAME = "SimplePlayer";
    private Map<String, IMediaPlayer> players = new HashMap<>();
    private Map<String, Boolean> urlPreparedMap = new ConcurrentHashMap<>();

    public SimplePlayerModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @ReactMethod
    public void play(final String url) {

        IMediaPlayer player = players.get(url);
        if (player == null) {
            player = SimpleMediaPlayerFactory.INS.createPlayer(getCurrentActivity(), SimpleMediaPlayerFactory.PlayerType.MEDIA_TYPE_SIMPLE);
        }
        Logger.d("z_debug", "SimplePlayer play " + player + ", url:" + url);
        try {
            players.put(url, player);
            player.reset();
            player.setDataSource(url);
            final IMediaPlayer finalPlayer = player;
            player.setOnPreparedListener(new IMediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(IMediaPlayer iMediaPlayer) {
                    Logger.d("z_debug", "SimplePlayer onPrepared " + url);
                    urlPreparedMap.put(url, true);
                    finalPlayer.start();
                    WritableMap params = Arguments.createMap();
                    params.putString("url", url);
                    RNUtils.sendEvent(getReactApplicationContext(), "onStartPlay", params);
                }
            });
            player.prepareAsync();

            player.setOnCompletionListener(new IMediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(IMediaPlayer iMediaPlayer) {
                    WritableMap params = Arguments.createMap();
                    params.putString("url", url);
                    RNUtils.sendEvent(getReactApplicationContext(), "onPlayEnd", params);
                }
            });

            player.setOnErrorListener(new IMediaPlayer.OnErrorListener() {
                @Override
                public boolean onError(IMediaPlayer iMediaPlayer, int i, int i2) {
                    Logger.d("z_debug", "SimplePlayer onError " + i + i2 + ", " + url);
                    WritableMap params = Arguments.createMap();
                    params.putString("url", url);
                    params.putString("error", i + "___" + i2);
                    RNUtils.sendEvent(getReactApplicationContext(), "onError", params);
                    return false;
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void pause(String url, Promise promise) {
        Logger.d("z_debug", "SimplePlayer pause " + url);
        if (players.containsKey(url)) {
            IMediaPlayer player = players.get(url);
            if (player != null) {
                player.pause();
                promise.resolve(true);
                return;
            }
        }
        promise.reject(Boolean.FALSE.toString(), "the url hasn't started");
    }

    @ReactMethod
    public void release(String url) {
        if (players.containsKey(url)) {
            IMediaPlayer player = players.remove(url);
            if (player != null) {
                player.pause();
                player.release();
                urlPreparedMap.put(url, false);
            }
        }
    }

    @ReactMethod
    public void getCurrentTime(String url, Promise promise){
        Boolean urlPrepared = urlPreparedMap.get(url);
        Logger.d("z_debug", "SimplePlayer getCurrentTime " + url + ", urlPrepared: " + urlPrepared);

        if (urlPrepared == null || !urlPrepared) {
            promise.resolve(0);
            return;
        }
        if (players.containsKey(url)) {
            IMediaPlayer player = players.get(url);
            if (player != null) {
                double currentPosition = player.getCurrentPosition();
                promise.resolve(currentPosition /1000);
                return;
            }
        }
        promise.reject(Boolean.FALSE.toString(), "the url hasn't started");
    }

    @ReactMethod
    public void getDuration(String url, Promise promise){
        Boolean urlPrepared = urlPreparedMap.get(url);
        Logger.d("z_debug", "SimplePlayer getDuration " + url + ", urlPrepared: "  +urlPrepared);

        if (urlPrepared == null || !urlPrepared) {
            promise.resolve(0);
            return;
        }
        if (players.containsKey(url)) {
            IMediaPlayer player = players.get(url);
            if (player != null) {
                double duration = player.getDuration();
                promise.resolve(duration /1000);
                return;
            }
        }
        promise.reject(Boolean.FALSE.toString(), "the url hasn't started");
    }
}
