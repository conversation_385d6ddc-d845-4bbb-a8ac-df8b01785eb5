package com.ximalaya.ting.android.reactnative.modules.ad;

import android.os.Build;
import android.text.TextUtils;

import com.ximalaya.ting.android.adsdk.XmAdSDK;
import com.ximalaya.ting.android.adsdk.base.log.AdLogger;
import com.ximalaya.ting.android.adsdk.bridge.importsdk.IXmLogger;
import com.ximalaya.ting.android.adsdk.bridge.importsdk.ImportSDKHelper;
import com.ximalaya.ting.android.adsdk.util.ContextUtils;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class RnAdTrace {

    private static final String TYPE = "RnAdEvent";

    public static void traceEvent(Advertis advertis, String event) {
        traceEvent(advertis, event, -1, "");
    }

    public static void traceEvent(Advertis advertis, String event, int code, String msg) {
        HashMap<String, String> commonData = new HashMap<>();
        commonData.put("type", TYPE);

        HashMap<String, String> optData = new HashMap<>();
        optData.put("event", event);
        optData.put("code", String.valueOf(code));
        if (TextUtils.isEmpty(msg)) {
            optData.put("message", msg);
        }
        if (advertis != null){
            optData.put("realLink", advertis.getRealLink());
            optData.put("dpRealLink", advertis.getDpRealLink());
            optData.put("dpMarketUrl", advertis.getDpMarketUrl());
        }
        report(advertis, optData, commonData);
    }

    public static void traceEvent(Advertis advertis, String event, HashMap<String, String> map) {
        HashMap<String, String> commonData = new HashMap<>();
        commonData.put("type", TYPE);

        Map<String, String> optData = new HashMap();
        if (map != null) {
            optData.putAll(map);
        }
        optData.put("event", event);
        if (advertis != null){
            optData.put("realLink", advertis.getRealLink());
            optData.put("dpRealLink", advertis.getDpRealLink());
            optData.put("dpMarketUrl", advertis.getDpMarketUrl());
        }
        report(advertis, optData, commonData);
    }

    private static void report(Advertis adModel, Map<String, String> optData, Map<String, String> commonData) {
        IXmLogger builder = ImportSDKHelper.newXmLogger("XmAd", "adTrace");
        try {
            if (optData == null) {
                optData = new HashMap<>();
            }
            JSONObject jsonObject = new JSONObject();
            for (Map.Entry<String, String> next : optData.entrySet()) {
                String key = next.getKey();
                String value = next.getValue();
                jsonObject.put(key, value);
            }
            builder.put("opt", jsonObject.toString());
        } catch (Throwable e) {}

        try {
            if (commonData != null) {
                for (Map.Entry<String, String> next : commonData.entrySet()) {
                    String key = next.getKey();
                    String value = next.getValue();
                    builder.put(key, value);
                }
            }
        } catch (Throwable e) {}
        if (adModel != null) {
            try {
                builder.put("slotid", adModel.getAdPositionId() + "");
                builder.put("planid", adModel.getPlanId() + "");
                builder.put("downloadAppname", adModel.getDownloadAppName());
                builder.put("downloadPkgname", adModel.getAppPackageName());
                builder.put("responseid", adModel.getResponseId() + "");
                builder.put("isdsp", AdManager.isThirdAd(adModel) + "");
                builder.put("dspPositionId", adModel.getDspPositionId());
                builder.put("adType", adModel.getAdtype() + "");
                builder.put("adItemId", adModel.getAdid() + "");
            } catch (Throwable e) {
                builder.put("exception", e.toString());
            }
        }
        try {
            builder.put("os", "Android");
            builder.put("os_version", Build.VERSION.RELEASE);
            builder.put("app_vn", ContextUtils.getAppVersionName());
            builder.put("app_vc", ContextUtils.getAppVersionCode() + "");
            if (XmAdSDK.getInstance().getAdConfig().getXmSelfConfig() != null) {
                String uid = XmAdSDK.getInstance().getAdConfig().getXmSelfConfig().uid();
                if (!"0".equals(uid)) {
                    builder.put("uid", uid);
                }
            }
        } catch (Throwable e) {}

        if (XmAdSDK.getInstance().getAdConfig() != null) {
            builder.put("adAppId", XmAdSDK.getInstance().getAdConfig().getAppId());
        }
        if (ConstantsOpenSdk.isDebug) {
            AdLogger.log("RnAdEvent=" + builder.getLogContent());
        }
        builder.toLog();
    }
}
