package com.ximalaya.ting.android.reactnative.modules.aiagent

import androidx.annotation.Keep

@Keep
data class AiAgentInitConfig(
    val connectType: Int,
    val productId: String? = null,
    val productSecret: String? = null
) {
    fun isChange(
        connectType: Int,
        productId: String? = null,
        productSecret: String? = null
    ): Boolean {
        return this.connectType != connectType ||
                this.productId != productId ||
                this.productSecret != productSecret
    }
}