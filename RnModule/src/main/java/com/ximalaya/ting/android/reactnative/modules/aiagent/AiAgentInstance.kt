package com.ximalaya.ting.android.reactnative.modules.aiagent

import android.Manifest
import android.net.Uri
import android.util.Base64
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.bridge.WritableMap
import com.facebook.react.common.LifecycleState
import com.ximalaya.aiagentconnect.sdk.connection.config.AppConfig
import com.ximalaya.aiagentconnect.sdk.connection.listener.IConnectStateListener
import com.ximalaya.aiagentconnect.sdk.connection.protocol.Directive
import com.ximalaya.aiagentconnect.sdk.logger.XYLogger
import com.ximalaya.aiagentconnect.sdk.logger.XYTrace
import com.ximalaya.ting.android.aiagent.AgentBuilder
import com.ximalaya.ting.android.aiagent.IRealTimeCallStatusListener
import com.ximalaya.ting.android.aiagent.XmAgent
import com.ximalaya.ting.android.aiagent.bean.config.DebugConfig
import com.ximalaya.ting.android.aiagent.bean.config.WebSocketConfig
import com.ximalaya.ting.android.aiagent.bean.directive.InvokeAppPayload
import com.ximalaya.ting.android.aiagent.bean.directive.SpeechTtsPayload
import com.ximalaya.ting.android.aiagent.core.record.Callback
import com.ximalaya.ting.android.aiagent.core.record.IPermissionRequest
import com.ximalaya.ting.android.aiagent.message.IMessageDirectiveListener
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ai.radio.AiRadioOnlinePlayUtil
import com.ximalaya.ting.android.host.manager.ai.radio.AiRadioPlayHelper
import com.ximalaya.ting.android.host.manager.ai.radio.IRadioPlayerListener
import com.ximalaya.ting.android.host.manager.ai.radio.RadioPlayHistoryUtil
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.common.ConcurrentHashSet
import com.ximalaya.ting.android.host.util.common.DeviceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.other.PermissionManage
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.reactnative.modules.aiagent.ChatxmlyAiAgentModule.Companion.NAME
import com.ximalaya.ting.android.reactnative.utils.RNUtils
import com.ximalaya.ting.android.xmabtest.ABTest
import org.json.JSONArray
import java.lang.ref.WeakReference
import java.text.SimpleDateFormat
import java.util.Collections
import java.util.Locale
import java.util.concurrent.atomic.AtomicInteger

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2025/4/27
 */
object AiAgentInstance {

    private var rnInitConfig: AiAgentInitConfig? = null
    private val pingCount = AtomicInteger(0)
    private var lastToken: String? = null
    private var abUseFullCallMode = ABTest.getString("agent_use_full_call_mode", "0") == "1"
    private var abUseRtc = ABTest.getString("agent_use_rtc_server_v1", "0") == "1"
    private val saveMessageSet = Collections.synchronizedList(mutableListOf<String>())
    private var saveMessageId: String? = null
    private val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.CHINA)

    private var reactContexts: ConcurrentHashSet<ReactApplicationContext> = ConcurrentHashSet()
    var wkAgent: WeakReference<XmAgent> = WeakReference(null)
    private var disableSendBinary = false
    private var lastSendMessageId = ""
    private var intent: String = ""

    private val useRtc by lazy {
        ConfigureCenter.getInstance()
            .getBool(CConstants.Group_android.GROUP_NAME, "key_aiagent_use_rtc_v3", true)
    }
    val useFullCallMode by lazy {
        ConfigureCenter.getInstance()
            .getBool(CConstants.Group_android.GROUP_NAME, "key_aiagent_use_full_call_mode_v2", true)
    }

    private fun useFullCallMode(): Boolean {
        if (ConstantsOpenSdk.isDebug) {
            val debugValue = ToolUtil.getSystemProperty("debug.ai_agent_full_call_mode", "0")
            if (debugValue == "1") {
                return true
            } else if (debugValue == "-1") {
                return false
            }
        }
        return useFullCallMode && abUseFullCallMode
    }


    private var connectStateListener: IConnectStateListener = object : IConnectStateListener {
        override fun onConnectFail(errorCode: Int) {
            sendEvent("AiAgentConnectError", errorCode)
        }

        override fun onAudioConnectFail(errorCode: Int) {
            sendEvent("AiAgentAudioChannelConnectError", errorCode)
        }

        override fun onConnected() {
            sendEvent("AiAgentConnectState", true)
        }

        override fun onAudioConnected() {
            super.onAudioConnected()
            sendEvent("AiAgentAudioChannelConnectState", true)
        }

        override fun onDisconnected() {
            sendEvent("AiAgentConnectState", false)
        }

        override fun onAudioDisconnected() {
            sendEvent("AiAgentAudioChannelConnectState", false)
        }
    }
    private var radioPlayerListener: IRadioPlayerListener = object : IRadioPlayerListener {
        override fun onPlayStart() {
            sendEvent("AiAgentPlayerStatusBegin", createAgentMap())
        }

        override fun onPlayPause() {
            sendEvent("AiAgentPlayerStatusPause", createAgentMap())
        }

        override fun onPlayProgress(currPos: Int, duration: Int) {
            val params = createAgentMap()
            params.putString("currentTime", currPos.toString())
            sendEvent("AiAgentPlayerStatusProgress", params, false)
        }

        override fun onPlayEnd() {
            val params = createAgentMap()
            params.putBoolean("end", false)
            sendEvent("AiAgentPlayerStatusEnd", params)
        }

        override fun onPlayFinish() {
            super.onPlayFinish()
            // 完播直接清除历史记录
            RadioPlayHistoryUtil.removePlayHistory(AiRadioPlayHelper.getCurPart()?.radioId)
            val params = createAgentMap()
            params.putBoolean("end", true)
            sendEvent("AiAgentPlayerStatusEnd", params)
        }

        override fun onPlayStop() {
            val params = createAgentMap()
            sendEvent("AiAgentPlayerStatusStop", params)
        }

        override fun onPlayError(msg: String) {
            val params = createAgentMap()
            params.putString("msg", msg)
            sendEvent("AiAgentPlayerStatusError", params)
        }
    }

    private var directiveListener: IMessageDirectiveListener =
        object : IMessageDirectiveListener() {

            override fun onBinaryMessage(data: ByteArray) {
                super.onBinaryMessage(data)
                if (!disableSendBinary) {
//                dlog("onBinaryMessage ${this@ChatxmlyAiAgentModule}")
                    sendEvent(
                        "AiAgentReceiveBufferBinary",
                        Base64.encodeToString(data, Base64.DEFAULT),
                        false
                    )
                }
            }

            override fun onAllMessage(message: String) {
                // 临时代码，屏蔽aiRadio的消息
                if (message.contains("\"moduleType\":\"list\"") && message.contains("ttsStart|ttsEnd|ttsFinish".toRegex())) {
                    if (message.contains("ttsStart")) {
//                        dlog("ttsStart $disableSendBinary")
                        disableSendBinary = true
                    }
                    if (message.contains("ttsEnd")) {
//                        dlog("ttsEnd $disableSendBinary")
                        disableSendBinary = false
                    }
                } else {
                    sendEvent("AiAgentReceiveMessage", message, false)
                    if (saveMessageId.isNullOrEmpty()
                            .not() && message.contains(saveMessageId!!) && (saveMessageSet.lastOrNull() != message)
                    ) {
                        saveMessageSet.add(message)
                    }
                }
            }

            override fun onInvokeApp(directive: Directive<InvokeAppPayload>) {
                super.onInvokeApp(directive)
                intent = directive.payload?.chatXmlyIntent ?: ""
            }

            override fun onMsgTTsStart(directive: Directive<SpeechTtsPayload>) {
                super.onMsgTTsStart(directive)
                if (lastSendMessageId != directive.messageId && directive.messageId.isNullOrEmpty()
                        .not()
                ) {
                    lastSendMessageId = directive.messageId ?: ""
                    sendEvent("AiAgentTtsStartTest", Arguments.createMap().apply {
                        putString("id", directive.messageId)
                        putString("contentType", directive.payload?.contentType)
                        putString(
                            "sendTime",
                            simpleDateFormat.format(System.currentTimeMillis())
                        )
                        putString("intent", intent)
                        putString("text", directive.payload?.text)
                    }, false)
                }
            }
        }
    private var callStatusListener: IRealTimeCallStatusListener =
        object : IRealTimeCallStatusListener {
            override fun onStatusChanged(status: XmAgent.CallState) {
                sendEvent("AiRealTimeCallStatus", status.name)
            }

            override fun onStatusChanged(status: XmAgent.CallState, text: String) {}
        }

    fun init(
        context: ReactApplicationContext,
        connectType: Int,
        channelMode: String,
        onlyServer: Boolean,
        productId: String? = null,
        productSecret: String? = null
    ) {
        reactContexts.add(context)
        reactContexts.removeAll { !it.hasActiveCatalystInstance() }
        dlog("active context size = ${reactContexts.size}")
        // 每次初始化重新开始ping
        pingCount.set(0)
        sendPingAndDelay()
        // 每次使用前检查token，不一致则重置连接
        if (lastToken != UserInfoMannage.getToken()) {
            lastToken = null
        }
        val forceInit =
            rnInitConfig == null || (rnInitConfig!!.isChange(connectType, productId, productSecret))
        if (!forceInit && wkAgent.get() != null) {
            dlog("SDK已初始化，无需再次初始化")
//            mXmAgent = wkAgent?.get()
//            removeListener()
//            addListener()
            return
        }
        rnInitConfig = AiAgentInitConfig(connectType, productId, productSecret)
        val context = BaseApplication.getMyApplicationContext()
        val appConfig = AppConfig(
            productId = productId ?: "S_PROD16_1549",
            productSecret = productSecret ?: "830e17b572c54a3d9d012608bca53ba6",
            sn = DeviceUtil.getDeviceToken(context),
            appVersion = DeviceUtil.getVersionName(context),
            packageName = DeviceUtil.getPackageName(context)
        )
        val mXmAgent = AgentBuilder()
            .context(BaseApplication.getMyApplicationContext())
            .appConfig(appConfig)
            .fullCallMode(useFullCallMode())
            .connectType(connectType)
            .webSocketConfig(
                WebSocketConfig(
                    channelMode = channelMode,
                    onlyServer = onlyServer
                )
            )
            .debugConfig(
                DebugConfig(
                    enableLog = true,
                    logWrite2File = true
                )
            )
            .headerParamsSupplier { builder, url ->
                CommonRequestM.getInstanse().addHeader(builder, null, url);
            }
            .permissionRequest(object : IPermissionRequest {
                override fun requestAudioPermission(callback: Callback) {
                    val activity = BaseApplication.getTopActivity() as? BaseFragmentActivity2
                        ?: return
                    PermissionManage.checkPermissionWithoutDialog(
                        activity, activity,
                        object : LinkedHashMap<String?, Int?>() {
                            init {
                                put(
                                    Manifest.permission.RECORD_AUDIO,
                                    R.string.host_voice_wake_deny_record_perm
                                )
                            }
                        }, object : IMainFunctionAction.IPermissionListener {
                            override fun havedPermissionOrUseAgree() {
                                callback.resolve(true)
                            }

                            override fun userReject(noRejectPermiss: Map<String, Int>) {
//                            CustomToast.showFailToast(R.string.host_voice_wake_deny_record_perm)
                                callback.resolve(false)
                            }
                        }, null
                    )
                }
            })
            .build()
        mXmAgent.initSDK()
        wkAgent = WeakReference(mXmAgent)
        removeListener()
        addListener()
        AiRadioPlayHelper.initAiRadioPlayer()
        XYTrace.callback = object : XYTrace.XYTraceCallback {
            override fun trace(actionKey: String, map: Map<String, String>?) {
                XDCSCollectUtil.statErrorToXDCS(
                    NAME,
                    "key >>> $actionKey , map>>>${map.toString()}"
                )
            }
        }
    }

    fun connect() {
        dlog("connected=${wkAgent.get()?.isConnected()}")
        if (wkAgent.get()?.isConnected() == true) {
            dlog("当前已连接，无需再次连接")
            return
        }
        if (lastToken == null || lastToken != UserInfoMannage.getToken()) {
            lastToken = UserInfoMannage.getToken()
            wkAgent.get()?.setToken(UserInfoMannage.getUid().toString(), UserInfoMannage.getToken())
        }
        wkAgent.get()?.connect()
    }

    fun useRTCServer(): Boolean {
        return useRtc && abUseRtc
    }

    private fun addListener() {
        wkAgent.get()?.addConnectStateListener(connectStateListener)
        wkAgent.get()?.addDirectiveListener(directiveListener)
        wkAgent.get()?.addRealTimeCallStatusListener(callStatusListener)
        AiRadioPlayHelper.addPlayListener(radioPlayerListener)
        AiRadioOnlinePlayUtil.startListen()
    }

    private fun removeListener() {
        wkAgent.get()?.removeConnectStateListener(connectStateListener)
        wkAgent.get()?.removeDirectiveListener(directiveListener)
        wkAgent.get()?.removeRealTimeCallStatusListener(callStatusListener)
        AiRadioPlayHelper.removePlayListener(radioPlayerListener)
        AiRadioOnlinePlayUtil.removeListener()
    }

    fun release() {
        lastToken = null
        wkAgent.get()?.releaseSDK()
        wkAgent.clear()
    }

    private fun sendEvent(eventName: String, data: Any?, withLog: Boolean = true) {
        runCatching {
            if (withLog) {
                dlog("sendEvent: event=$eventName , data=$data , context resume size = ${reactContexts.filter { it.lifecycleState == LifecycleState.RESUMED }}")
            }
            reactContexts.filter { it.lifecycleState == LifecycleState.RESUMED }.forEach {
                RNUtils.sendEvent(it, eventName, data)
            }
        }
    }

    private fun createAgentMap(): WritableMap {
        val params = Arguments.createMap().apply {
            putString("partId", AiRadioPlayHelper.getCurPart()?.partId ?: "")
            putString("radioId", AiRadioPlayHelper.getCurPart()?.radioId ?: "")
            putString("episodeId", AiRadioPlayHelper.getCurPart()?.episodeId ?: "")
        }
        return params
    }

    private fun dlog(msg: String) {
        XYLogger.log(NAME, msg)
    }

    /**
     * ping-pong
     * */
    private val pingRunnable = Runnable {
        val currentCount = pingCount.incrementAndGet()
//        dlog("ping count $currentCount ${this@ChatxmlyAiAgentModule}")
        if (currentCount > 10) {
            dlog("ping timeout >>>")
            pingExit()
            return@Runnable
        } else {
            sendPingAndDelay()
        }
    }

    private fun pingExit() {
        HandlerManager.removeCallbacks(pingRunnable)
        AiRadioPlayHelper.agentPageState = ""
//        removeListener()
        AiRadioPlayHelper.checkDisconnectAgent()
    }

    private fun sendPingAndDelay() {
        dlog("ping rn >>>")
        HandlerManager.removeCallbacks(pingRunnable)
        HandlerManager.postOnUIThreadDelay(pingRunnable, 3 * 1000)
        AiRadioPlayHelper.agentPageState = "resume"
        sendEvent(
            "AiAgentSendPingMessageEvent",
            null,false
        )
    }

    fun rnPong() {
        dlog("rn pong ${pingCount.get()}>>> ")
        pingCount.set(0)
    }

    /**
     * ping-pong end
     * */
    fun saveMessageToMemory(messageId: String) {
        dlog("saveMessageToMemory >>> $messageId")
        saveMessageId = messageId
        saveMessageSet.clear()
    }

    @ReactMethod
    fun getMemoryMessage(index: Int, messageId: String): ReadableArray? {
        if (saveMessageId != messageId || index >= saveMessageSet.size) {
            dlog("getMemoryMessage >>> messageId or index error ,$saveMessageId==$messageId $index==${saveMessageSet.size}")
            return null
        }
        val jsonArray = JSONArray()
        val result = kotlin.runCatching {
            saveMessageSet.subList(index, saveMessageSet.size).forEach {
                jsonArray.put(it)
            }
            RNUtils.jsonToReact(jsonArray)
        }
            .onFailure { dlog("getMemoryMessage error , messageById >>> $it , index >>> $index , ${it.message}") }
            .getOrNull()
        dlog("getMemoryMessage $saveMessageId==$messageId >>> size=${result?.size()},total=${saveMessageSet.size}")
        if (ConstantsOpenSdk.isDebug) {
            for (i in 0 until jsonArray.length()) {
                dlog("getMemoryMessageItem $i>>> ${Uri.decode(jsonArray[i] as? String)}")
            }
        }
        return result
    }
}