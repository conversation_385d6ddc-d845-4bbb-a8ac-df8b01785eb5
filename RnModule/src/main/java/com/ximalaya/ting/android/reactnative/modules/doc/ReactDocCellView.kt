package com.ximalaya.ting.android.reactnative.modules.doc

import android.content.res.ColorStateList
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.WritableMap
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.events.RCTEventEmitter
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.async.AsyncRequest
import com.ximalaya.ting.android.host.manager.doc.CompactParagraph
import com.ximalaya.ting.android.host.manager.doc.DocLoadResult
import com.ximalaya.ting.android.host.manager.doc.DocLoader
import com.ximalaya.ting.android.host.manager.doc.DocParser
import com.ximalaya.ting.android.host.manager.doc.NoSupportException
import com.ximalaya.ting.android.host.manager.doc.view.DocCompactParagraphView
import com.ximalaya.ting.android.host.model.play.PlayPageTabAndSoundInfo
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.view.awaitLayout
import com.ximalaya.ting.android.host.view.CommonLoadingView
import com.ximalaya.ting.android.reactnative.R
import kotlinx.coroutines.*

/**
 * Created by yang.zhang on 2025/4/23
 */
class ReactDocCellView(
    private val reactContext: ThemedReactContext
) : FrameLayout(reactContext) {

    private val TAG = "ReactDocCellView"
    private var debugTitle: String? = null

    private var jobs = mutableMapOf<Int, Job>()

    private var rawDocResult: DocLoadResult? = null

    private var mShowLines: Int = 4

    val docView = DocCompactParagraphView(reactContext).apply {
        if (BaseFragmentActivity.sIsDarkMode) {
            normalTextColor = 0x4CFFFFFF.toInt()
            activateTextColor = 0xE6FFFFFF.toInt()
        } else {
            normalTextColor = 0x4c2c2c3c.toInt()
            activateTextColor = 0xFF2C2C3C.toInt()
        }
    }

    private var noContentView =  LinearLayout(context).apply {
        addView(ImageView(context).apply {
            val tintColor = if (BaseFragmentActivity.sIsDarkMode) {
                0x80ffffff.toInt()
            } else {
                0x802c2c3c.toInt()
            }
            val d = resources.getDrawable(R.drawable.rn_icon_no_content).apply {
                setTintList(ColorStateList.valueOf(tintColor))
            }
            setImageDrawable(d)
        }, LayoutParams(14.dp, 14.dp))

        addView(TextView(context).apply {
            text = "暂无文稿内容"
            if (BaseFragmentActivity.sIsDarkMode) {
                setTextColor(0x80ffffff.toInt())
            } else {
                setTextColor(0x802c2c3c.toInt())
            }
            setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
        }, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
            leftMargin = 5.dp
        })
        gravity = Gravity.CENTER_VERTICAL
    }

    private var loadingView = LinearLayout(reactContext).apply {
        addView(CommonLoadingView(context).apply {
            val tintColor = if (BaseFragmentActivity.sIsDarkMode) {
                0x80ffffff.toInt()
            } else {
                0x802c2c3c.toInt()
            }
            val d = resources.getDrawable(R.drawable.rn_ic_doc_loading).apply {
                setTintList(ColorStateList.valueOf(tintColor))
            }
            background = d
        }, LayoutParams(14.dp, 14.dp))

        addView(TextView(context).apply {
            text = "文案加载中…"
            if (BaseFragmentActivity.sIsDarkMode) {
                setTextColor(0x80ffffff.toInt())
            } else {
                setTextColor(0x802c2c3c.toInt())
            }
            setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
        }, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
            leftMargin = 5.dp
        })
        gravity = Gravity.CENTER_VERTICAL
    }

    private var errorView = LinearLayout(reactContext).apply {
        orientation = LinearLayout.VERTICAL
        gravity = Gravity.CENTER_HORIZONTAL

        addView(TextView(context).apply {
            text = "当前加载失败，请点击重试"
            if (BaseFragmentActivity.sIsDarkMode) {
                setTextColor(0x80ffffff.toInt())
            } else {
                setTextColor(0x802c2c3c.toInt())
            }
            setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
        }, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT))

        addView(TextView(context).apply retryText@{
            text = "重试"
            if (BaseFragmentActivity.sIsDarkMode) {
                setTextColor(0xb2ffffff.toInt())
            } else {
                setTextColor(0xB22c2c3c.toInt())
            }
            setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
            <EMAIL> = GradientDrawable().apply {
                shape = GradientDrawable.RECTANGLE
                setColor(Color.TRANSPARENT)
                cornerRadius = 28.dpFloat
                setStroke(BaseUtil.dp2px(context, 0.5f),
                    if (BaseFragmentActivity.sIsDarkMode) {
                        0x33ffffff.toInt()
                    } else {
                        0x332c2c3c.toInt()
                    }
                )
            }
            setPadding(30.dp, 5.dp, 30.dp, 5.dp)
        }, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
            topMargin = 12.dp
            gravity = Gravity.CENTER
        })
    }

    private val lifecycleScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    var mTrackId: Long = 0L
        private set

    init {
        addView(docView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
        addView(loadingView, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
            gravity = Gravity.CENTER
        })

//        errorView.setOnClickListener {
//            showloading()
//            if (mTrackId > 0L) {
//                startLoad(mTrackId)
//            }
//        }
    }

    fun reload() {
        if (mTrackId > 0L) {
            showloading()
            Log.d(TAG, "reload mTrackId = $mTrackId")
            startLoad(mTrackId)
        }
    }

    private fun showloading() {
        removeView(errorView)
        removeView(noContentView)
        addView(loadingView, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
            gravity = Gravity.CENTER
        })
    }

    private fun hideLoading() {
        removeView(loadingView)
    }

    private fun hideError() {
        removeView(errorView)
    }

    private fun hideNoContent() {
        removeView(noContentView)
    }

    private fun showError() {
        removeView(loadingView)
        removeView(noContentView)
        if (errorView.parent == null) {
            addView(errorView, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                gravity = Gravity.CENTER
            })
        }
    }

    private fun showNoContent() {
        removeView(loadingView)
        removeView(errorView)
        if (noContentView.parent == null) {
            addView(noContentView, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                gravity = Gravity.CENTER
            })
        }
        docView.visibility = View.INVISIBLE
    }


    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        if (errorView.isShown) {
            requestDisallowInterceptTouchEvent(true)
            return true
        }
        return super.onInterceptTouchEvent(ev)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        Log.w(TAG, "$mTrackId: $debugTitle; onAttachedToWindow")
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        Log.w(TAG, "$mTrackId: $debugTitle; onDetachedFromWindow")
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        post { requestLayout() }
    }

    override fun requestLayout() {
        super.requestLayout()
        if (isLayoutRequested) {
            post {
                measure(
                    MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY),
                    MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY),
                )
                layout(left, top, right, bottom)
            }
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        if (changed) {
            val runningJobs = jobs.filterKeys { key-> key != width }
            if (runningJobs.isNotEmpty()) {
                runningJobs.forEach { it.value.cancel() }
                runningJobs.keys.forEach { jobs.remove(it) }

                val currentJob = jobs[width]
                if (currentJob != null) {
                    Log.w(TAG, "$mTrackId: $debugTitle; onLayout, width = ${width}, running, ignore current job;")
                    return
                }
                Log.d(TAG, "$mTrackId: $debugTitle; onLayout,  width = ${width}, start render;")

                render(mTrackId, rawDocResult, width).also { job ->
                    jobs[width] = job
                }
            }
        }
    }

    fun setDocInfo(
        trackId: Long,
        contentType: String,
        title: String,
        content: String,
        showLines: Int
    ) {
        if (mTrackId != trackId) {
            mShowLines = showLines
            mTrackId = trackId
            debugTitle = title
            if (trackId > 0L) {
                startLoad(trackId)
            }
        }
    }

    private fun sendHeightChange(heightPx: Int) {
        val event: WritableMap = Arguments.createMap().apply {
            putInt("height", heightPx)
        }
        reactContext
            .getJSModule(RCTEventEmitter::class.java)
            .receiveEvent(id, "onHeightChange", event)
    }

    private fun sendRemove() {
        reactContext
            .getJSModule(RCTEventEmitter::class.java)
            .receiveEvent(id, "onRemove", null)
    }

    private fun sendLoadFailed() {
        reactContext
            .getJSModule(RCTEventEmitter::class.java)
            .receiveEvent(id, "onLoadFailed", null)
    }

    private fun startLoad(trackId: Long) {
        lifecycleScope.launch {
            val docInfo = withContext(Dispatchers.Default) {
                withTimeoutOrNull(15_000L) {
                    kotlin.runCatching { loadRawDocInfo(trackId) }.getOrNull()
                }
            }
            Log.d(TAG, "$trackId: $debugTitle; loadDocInfo finish;")
            rawDocResult = docInfo?.getOrNull()

            if (docInfo == null || (docInfo.isFailure && docInfo.exceptionOrNull() is NoSupportException)) {
                withContext(Dispatchers.Main) {
                    Log.d(TAG, "$trackId: $debugTitle; 没有内容;")
                    showNoContent()
                }
                return@launch
            }

            awaitLayout()
            val job = render(trackId, docInfo?.getOrNull(), width)
            jobs[width] = job
        }
    }

    private suspend fun loadDocInfo(trackId: Long): Result<List<CompactParagraph>>? {
        val playingSoundInfo = loadPlayingInfo(trackId)
        if (playingSoundInfo == null) {
            Log.w(TAG, "$trackId: $debugTitle; loadPlayingInfo null;")
            return null
        }

        val docResult = DocLoader.loadRaw(playingSoundInfo)
        if (docResult.isFailure) {
            Log.w(TAG, "$trackId: $debugTitle; loadRaw isFailure;")
            return null
        }

        val result = docResult.getOrNull() ?: return null

        Log.d(TAG, "$trackId: $debugTitle; awaitLayout start;")
        awaitLayout()
        Log.d(TAG, "$trackId: $debugTitle; awaitLayout end;")
        return DocParser.compactParagraph(
            result,
            mShowLines,
            width ?: 0,
            docView.textSize.toFloat() ?: 0f,
            docView.letterSpacing ?: 0f
        )
    }

    private suspend fun loadRawDocInfo(trackId: Long): Result<DocLoadResult>? {
        val playingSoundInfo = loadPlayingInfo(trackId)
        if (playingSoundInfo == null) {
            Log.w(TAG, "$trackId: $debugTitle; loadPlayingInfo null;")
            return null
        }

        val docResult = DocLoader.loadRaw(playingSoundInfo)
        if (docResult.isFailure) {
            Log.w(TAG, "$trackId: $debugTitle; loadRaw isFailure;")
            return null
        }

        return docResult
    }

    private fun render(trackId: Long, docResult: DocLoadResult?, viewWidth: Int): Job {
        return lifecycleScope.launch {
            val parsedResult = if (docResult == null) null else withContext(Dispatchers.Default) {
                Log.d(TAG, "$trackId: $debugTitle; awaitLayout start;")
                Log.d(TAG, "$trackId: $debugTitle; awaitLayout end;")
                DocParser.compactParagraph(
                    docResult,
                    mShowLines,
                    viewWidth,
                    docView.textSize.toFloat() ?: 0f,
                    docView.letterSpacing ?: 0f
                )
            }

            withContext(Dispatchers.Main) {
                if (parsedResult != null && parsedResult.isSuccess) {
                    Log.d(TAG, "$trackId: $debugTitle; parse doc success;")
                    val result = parsedResult.getOrNull()
                    if (result != null) {
                        docView.setEntryList(result)
                    }
                    hideLoading()
                    hideError()
                    hideNoContent()
                } else {
                    Log.e(TAG, "$trackId: $debugTitle; loadDocInfo error;")
                    showError()
                    sendLoadFailed()
                }
            }
        }
    }

    private suspend fun loadPlayingInfo(trackId: Long): PlayingSoundInfo? {
        val url = ToolUtil.addTsToUrl(String.format(UrlConstants.getInstanse().playPageTabAndInfoUrlFormat, trackId));
        val params: MutableMap<String, String> = HashMap()
        params["isDefault"] = "0"
        params["device"] = "android"
        val tabsInfo = runCatching { AsyncRequest.get<PlayPageTabAndSoundInfo>(url, params) }.getOrNull()
        return tabsInfo?.soundInfo
    }

}
