package com.ximalaya.ting.android.reactnative.modules.doc

import android.content.res.ColorStateList
import android.util.TypedValue
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.WritableMap
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.events.RCTEventEmitter
import com.ximalaya.ting.android.adsdk.util.ToastUtils
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.host.async.AsyncRequest
import com.ximalaya.ting.android.host.manager.doc.DocLoader
import com.ximalaya.ting.android.host.manager.doc.DocParser
import com.ximalaya.ting.android.host.manager.doc.NatureParagraph
import com.ximalaya.ting.android.host.manager.doc.view.ScrollableDocView
import com.ximalaya.ting.android.host.model.play.PlayPageTabAndSoundInfo
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.reactnative.R
import kotlinx.coroutines.*

/**
 * Created by yang.zhang on 2025/4/23
 */
class ReactDocFullScrollView(
    private val reactContext: ThemedReactContext
) : FrameLayout(reactContext) {

    private val job = SupervisorJob()
    private val lifecycleScope = CoroutineScope(Dispatchers.Main + job)

    private val docView = ScrollableDocView(reactContext)

    var mTrackId: Long = 0L
        private set

    init {
        addView(docView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
    }

    private var noContentView =  LinearLayout(context).apply {
        addView(ImageView(context).apply {
            val tintColor = if (BaseFragmentActivity.sIsDarkMode) {
                0x80ffffff.toInt()
            } else {
                0x802c2c3c.toInt()
            }
            val d = resources.getDrawable(R.drawable.rn_quick_listen_no_doc_icon).apply {
                setTintList(ColorStateList.valueOf(tintColor))
            }
            setImageDrawable(d)
        }, LayoutParams(14.dp, 14.dp))

        addView(TextView(context).apply {
            text = "暂无文稿内容"
            if (BaseFragmentActivity.sIsDarkMode) {
                setTextColor(0x80ffffff.toInt())
            } else {
                setTextColor(0x802c2c3c.toInt())
            }
            setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
        }, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
            leftMargin = 5.dp
        })
        gravity = Gravity.CENTER_VERTICAL
    }


    fun onDestory() {
        job.cancel()
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        return super.dispatchTouchEvent(ev)
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        requestDisallowInterceptTouchEvent(true)
        return true
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        return docView.dispatchTouchEvent(event)
    }

    override fun requestLayout() {
        super.requestLayout()
        if (isLayoutRequested) {
            post {
                measure(
                    MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY),
                    MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY),
                )
                layout(0, 0, right, bottom)
            }
        }
    }

    fun updateTime(time: Long, isDrag: Boolean) {
        if (mTrackId > 0L) {
            docView.natureParagraphView.updateTime(time, false)
        }
    }

    fun pause() {
        docView.natureParagraphView.pause()
    }

    fun setDocInfo(
        trackId: Long,
        contentType: String,
        title: String,
        content: String
    ) {
        if (mTrackId != trackId) {
            mTrackId = trackId
            if (trackId > 0L) {
                startLoadNature(trackId)
            }
        }
    }

    private fun sendHeightChange(heightPx: Int) {
        val event: WritableMap = Arguments.createMap().apply {
            putInt("height", heightPx)
        }
        reactContext
            .getJSModule(RCTEventEmitter::class.java)
            .receiveEvent(id, "onHeightChange", event)
    }

    private fun sendRemove() {
        reactContext
            .getJSModule(RCTEventEmitter::class.java)
            .receiveEvent(id, "onRemove", null)
    }

    private fun startLoadNature(trackId: Long) {
        lifecycleScope.launch {
            val docInfo = withContext(Dispatchers.Default) {
                val playingSoundInfo = loadPlayingInfo(trackId)
                if (playingSoundInfo != null) {
                    loadDocInfoNature(playingSoundInfo)
                } else {
                    null
                }
            }

            withContext(Dispatchers.Main) {
                if (docInfo != null && docInfo.isSuccess) {
                    val result = docInfo.getOrNull()
                    if (result != null) {
                        docView.natureParagraphView.setEntryList(result)

                        delay(200)
                        docView.natureParagraphView.measure(
                            MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY),
                            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
                        )
                        docView.natureParagraphView.layout(
                            0,
                            0,
                            width,
                            docView.natureParagraphView.measuredHeight
                        )
                        return@withContext
                    }
                }
                showNoContent()
            }
        }
    }

    private fun showNoContent() {
        if (noContentView.parent == null) {
            addView(noContentView, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                gravity = Gravity.CENTER
            })
        }
        docView.visibility = View.INVISIBLE
    }

    private suspend fun loadDocInfoNature(playingSoundInfo: PlayingSoundInfo): Result<List<NatureParagraph>>? {
        val docResult = DocLoader.loadRaw(playingSoundInfo)
        if (docResult.isFailure) {
            return null
        }

        val result = docResult.getOrNull()?: return null
        return DocParser.natureParagraph(result)
    }

    private suspend fun loadPlayingInfo(trackId: Long): PlayingSoundInfo? {
        val url = ToolUtil.addTsToUrl(String.format(UrlConstants.getInstanse().playPageTabAndInfoUrlFormat, trackId));
        val params: MutableMap<String, String> = HashMap()
        params["isDefault"] = "0"
        params["device"] = "android"
        val tabsInfo = runCatching { AsyncRequest.get<PlayPageTabAndSoundInfo>(url, params) }.getOrNull()
        return tabsInfo?.soundInfo
    }
}
