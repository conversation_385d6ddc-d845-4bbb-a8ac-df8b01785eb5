package com.ximalaya.ting.android.reactnative.modules.shortVideo

import android.content.Context
import android.graphics.Bitmap
import android.support.rastermill.FrameSequenceDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.constraintlayout.utils.widget.ImageFilterView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.manager.ImageManager.GifDisplayCallback
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.reactnative.R
import com.ximalaya.ting.android.read.utils.checkActivity
import com.ximalaya.ting.android.xmutil.MD5.md5

class RnShortVideoCoverView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var mIvCover: ImageFilterView? = null
    private var mIvStaticCover: ImageFilterView? = null

    private var curFrameCount = 0

    private var staticCoverUrl: String? = null
    private var gifCover: String? = null

    var isAllowedPlay = false

    var position = 0

    init {
        LayoutInflater.from(context).inflate(R.layout.rn_item_short_video_cover_layout, this)

        mIvCover = findViewById(R.id.main_iv_video_cover)
        mIvStaticCover = findViewById(R.id.main_iv_video_static_cover)
    }

    fun initStaticCover(staticCoverUrl: String?) {
        this.staticCoverUrl = staticCoverUrl

        ViewUtil.setViewVisibilitySafe(mIvStaticCover, View.VISIBLE)
        ViewUtil.setViewVisibilitySafe(mIvCover, View.INVISIBLE)

        displayStaticCover()
    }

    fun initGifCover(gifCover: String?) {
        this.gifCover = gifCover
    }

    fun playGifCover() {
        isAllowedPlay = true
        startAnimal("playGifCover")
    }

    fun pauseGifCover() {
        isAllowedPlay = false
        stopAnimal("pauseGifCover")
    }

    private fun displayStaticCover() {
        ImageManager.from(context).displayImage(
            mIvStaticCover,
            staticCoverUrl,
            R.drawable.rn_bg_default_short_video_cover
        )
    }

    private fun getGifCoverHashCode(): String {
        if (gifCover.isNullOrEmpty()) {
            return ""
        }
        return md5(gifCover!!.toByteArray()) ?: ""
    }


    private fun displayGifCover(from: String) {
        if (gifCover.isNullOrEmpty()) {
            return
        }

        displayWebpAnimPic(mIvCover!!, gifCover!!, object : GifDisplayCallback {
            override fun onCompleteDisplay(lastUrl: String?, bitmap: Bitmap?) {
                // 绑定的时候可以播放  然后用户滑动了  这时候是不允许播放的
                bitmap?.let {
                    initCoverView(from)
                }
            }

            override fun onGifLoaded(var1: FrameSequenceDrawable?) {
                var1?.let {
                    mIvCover?.setTag(R.id.main_iv_video_cover, getGifCoverHashCode())
                    // 自己管控生命周期
                    var1.setHandleSetVisible(false)
                    initCoverView(from)
                }
            }
        })
    }

    private fun initCoverView(from: String) {
        if (!mIvStaticCover?.context.checkActivity()) {
            return
        }

        if (!isAllowedPlay) {
            ViewUtil.setViewVisibilitySafe(mIvStaticCover, View.VISIBLE)
            ViewUtil.setViewVisibilitySafe(mIvCover, View.INVISIBLE)
            stopAnimal(from)
        } else {
            ViewUtil.setViewVisibilitySafe(mIvStaticCover, View.INVISIBLE)
            ViewUtil.setViewVisibilitySafe(mIvCover, View.VISIBLE)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (mIvCover == null || !mIvCover?.context.checkActivity()) {
            return
        }

        startAnimal("Attached")
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopAnimal("Detached")
    }

    private fun startAnimal(from: String = "") {
        if (!isAllowedPlay) {
            return
        }
        // Log.i("dqq1", "startAnimal from:$from position:$position curFrameCount:$curFrameCount")
        val cacheHashCode = mIvCover?.getTag(R.id.main_iv_video_cover) as? String?
        var isNeedLoad = false
        if (cacheHashCode.isNullOrEmpty() || cacheHashCode != getGifCoverHashCode()) {
            isNeedLoad = true
        }

        val drawable = (mIvCover?.drawable as? FrameSequenceDrawable)

        if (drawable == null || isNeedLoad) {
            displayGifCover("startAnimal")
        } else {
            initCoverView(from)
            if (curFrameCount < 0) {
                curFrameCount = 0
            }
            drawable.start(curFrameCount)
        }
    }

    private fun stopAnimal(from: String = "") {
        // Log.i("dqq1", "stopAnimal from:$from position:$position ")
        (mIvCover?.drawable as? FrameSequenceDrawable)?.run {
            curFrameCount = nextFrameToDecode
            stop()
        }
    }

    private fun displayWebpAnimPic(
        iv: ImageFilterView,
        url: String,
        callback: GifDisplayCallback?
    ) {
        ImageManager.from(context).displayImage(
            null,
            iv,
            url,
            -1,
            0,
            0,
            0,
            callback,
            null,
            true
        );
    }
}