/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.ximalaya.ting.android.reactnative.modules.thirdParty.slider;

import android.graphics.PorterDuffColorFilter;
import android.os.Build;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.util.Log;
import android.view.View;
import android.widget.SeekBar;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.LayoutShadowNode;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.UIManagerModule;
import com.facebook.react.uimanager.ViewProps;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.yoga.YogaMeasureFunction;
import com.facebook.yoga.YogaMeasureMode;
import com.facebook.yoga.YogaMeasureOutput;
import com.facebook.yoga.YogaNode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;

/**
 * Manages instances of {@code ReactSlider}.
 */
public class ReactSliderManager extends SimpleViewManager<ReactSlider> {
  private static final String TAG = "ReactSliderManager";
  public static final String REACT_CLASS = "RNCSlider";

  static class ReactSliderShadowNode extends LayoutShadowNode implements
          YogaMeasureFunction {

    private int mWidth;
    private int mHeight;
    private boolean mMeasured;

    private ReactSliderShadowNode() {
      initMeasureFunction();
    }

    private void initMeasureFunction() {
      setMeasureFunction(this);
    }

    @Override
    public long measure(
            YogaNode node,
            float width,
            YogaMeasureMode widthMode,
            float height,
            YogaMeasureMode heightMode) {
      if (!mMeasured) {
        SeekBar reactSlider = new ReactSlider(getThemedContext(), null);
        final int spec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
        reactSlider.measure(spec, spec);
        mWidth = reactSlider.getMeasuredWidth();
        mHeight = reactSlider.getMeasuredHeight();
        mMeasured = true;
        Log.d(TAG, String.format("ShadowNode测量: width=%d, height=%d", mWidth, mHeight));
      }

      return YogaMeasureOutput.make(mWidth, mHeight);
    }
  }

  private static final SeekBar.OnSeekBarChangeListener ON_CHANGE_LISTENER =
          new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekbar, int progress, boolean fromUser) {
              ReactContext reactContext = (ReactContext) seekbar.getContext();
              double realProgress = ((ReactSlider)seekbar).toRealProgress(progress);
              Log.d(TAG, String.format("进度变化: progress=%d, realProgress=%.2f, fromUser=%b",
                      progress, realProgress, fromUser));
              reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher().dispatchEvent(
                      new ReactSliderEvent(
                              seekbar.getId(),
                              realProgress, fromUser));
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekbar) {
              ReactContext reactContext = (ReactContext) seekbar.getContext();
              ((ReactSlider)seekbar).isSliding(true);
              double progress = ((ReactSlider)seekbar).toRealProgress(seekbar.getProgress());
              Log.d(TAG, String.format("开始滑动: progress=%.2f", progress));
              reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher().dispatchEvent(
                      new ReactSlidingStartEvent(
                              seekbar.getId(),
                              progress));
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekbar) {
              ReactContext reactContext = (ReactContext) seekbar.getContext();
              ((ReactSlider)seekbar).isSliding(false);
              double progress = ((ReactSlider)seekbar).toRealProgress(seekbar.getProgress());
              Log.d(TAG, String.format("停止滑动: progress=%.2f", progress));
              reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher().dispatchEvent(
                      new ReactSlidingCompleteEvent(
                              seekbar.getId(),
                              progress));
              reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher().dispatchEvent(
                      new ReactSliderEvent(
                              seekbar.getId(),
                              progress,
                              !((ReactSlider)seekbar).isSliding()));
            }
          };

  @Override
  public String getName() {
    return REACT_CLASS;
  }

  @Override
  public LayoutShadowNode createShadowNodeInstance() {
    return new ReactSliderShadowNode();
  }

  @Override
  public Class getShadowNodeClass() {
    return ReactSliderShadowNode.class;
  }

  @Override
  protected ReactSlider createViewInstance(ThemedReactContext context) {
    ReactSlider slider = new ReactSlider(context, null);
    Log.d(TAG, "创建新的Slider实例");

    if (Build.VERSION.SDK_INT >= 21) {
      /**
       * The "splitTrack" parameter should have "false" value,
       * otherwise the SeekBar progress line doesn't appear when it is rotated.
       */
      slider.setSplitTrack(false);
    }

    // 确保 progressDrawable 被正确初始化
    if (slider.getProgressDrawable() == null) {
      Log.d(TAG, "初始化progressDrawable");
      slider.setProgressDrawable(androidx.appcompat.content.res.AppCompatResources.getDrawable(
              context,
              android.R.drawable.progress_horizontal
      ));
    }

    return slider;
  }

  @ReactProp(name = ViewProps.ENABLED, defaultBoolean = true)
  public void setEnabled(ReactSlider view, boolean enabled) {
    Log.d(TAG, String.format("设置enabled: %b", enabled));
    view.setEnabled(enabled);
  }

  @ReactProp(name = "value", defaultDouble = 0d)
  public void setValue(ReactSlider view, double value) {
    if (view.isSliding() == false) {
      Log.d(TAG, String.format("设置value: %.2f, isSliding=%b", value, view.isSliding()));
      view.setValue(value);
      if (view.isAccessibilityFocused() && Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
        view.setupAccessibility((int)value);
      }
    }
  }

  @ReactProp(name = "minimumValue", defaultDouble = 0d)
  public void setMinimumValue(ReactSlider view, double value) {
    Log.d(TAG, String.format("设置minimumValue: %.2f", value));
    view.setMinValue(value);
  }

  @ReactProp(name = "maximumValue", defaultDouble = 1d)
  public void setMaximumValue(ReactSlider view, double value) {
    Log.d(TAG, String.format("设置maximumValue: %.2f", value));
    view.setMaxValue(value);
  }

  @ReactProp(name = "step", defaultDouble = 0d)
  public void setStep(ReactSlider view, double value) {
    Log.d(TAG, String.format("设置step: %.2f", value));
    view.setStep(value);
  }

  @ReactProp(name = "thumbTintColor", customType = "Color")
  public void setThumbTintColor(ReactSlider view, Integer color) {
    Log.d(TAG, String.format("设置thumbTintColor: %d", color));
    if (view.getThumb() != null) {
      if (color == null) {
        view.getThumb().clearColorFilter();
      } else {
        view.getThumb().setColorFilter(color, PorterDuff.Mode.SRC_IN);
      }
    }
  }

  @ReactProp(name = "minimumTrackTintColor", customType = "Color")
  public void setMinimumTrackTintColor(ReactSlider view, Integer color) {
    Log.d(TAG, String.format("设置minimumTrackTintColor: %d", color));
    Drawable progressDrawable = view.getProgressDrawable();
    Drawable progressScaleDrawable = view.getProgressScaleDrawable();

    setMinimumTrackTintColor(view, color, progressDrawable);
    setMinimumTrackTintColor(view, color, progressScaleDrawable);

  }

  private void setMinimumTrackTintColor(ReactSlider view, Integer color, Drawable progressDrawable) {
    if (progressDrawable == null) {
      Log.w(TAG, "setMinimumTrackTintColor: progressDrawable is null");
      return;
    }
    LayerDrawable drawable = (LayerDrawable) progressDrawable.getCurrent();
    if (drawable == null) {
      Log.w(TAG, "setMinimumTrackTintColor: drawable is null");
      return;
    }
    Drawable progress = drawable.findDrawableByLayerId(android.R.id.progress);
    if (color == null) {
      progress.clearColorFilter();
    } else {
      if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P) {
        progress.setColorFilter(new PorterDuffColorFilter((int)color, PorterDuff.Mode.SRC_IN));
      }
      else {
        progress.setColorFilter(color, PorterDuff.Mode.SRC_IN);
      }
    }
  }

  @ReactProp(name = "thumbImage")
  public void setThumbImage(ReactSlider view, @Nullable ReadableMap source) {
    String uri = null;
    if (source != null) {
      uri = source.getString("uri");
    }
    Log.d(TAG, String.format("设置thumbImage: %s", uri));
    view.setThumbImage(uri);
  }

  @ReactProp(name = "maximumTrackTintColor", customType = "Color")
  public void setMaximumTrackTintColor(ReactSlider view, Integer color) {
    Log.d(TAG, String.format("设置maximumTrackTintColor: %d", color));
    Drawable progressDrawable = view.getProgressDrawable();
    Drawable progressScaleDrawable = view.getProgressScaleDrawable();

    setMaximumTrackTintColor(view, color, progressDrawable);
    setMaximumTrackTintColor(view, color, progressScaleDrawable);

  }

  public void setMaximumTrackTintColor(ReactSlider view, Integer color, Drawable progressDrawable) {
    if (progressDrawable == null) {
      Log.w(TAG, "setMaximumTrackTintColor: progressDrawable is null");
      return;
    }
    LayerDrawable drawable = (LayerDrawable) progressDrawable.getCurrent();
    if (drawable == null) {
      Log.w(TAG, "setMaximumTrackTintColor: drawable is null");
      return;
    }
    Drawable background = drawable.findDrawableByLayerId(android.R.id.background);
    if (color == null) {
      background.clearColorFilter();
    } else {
      if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P) {
        background.setColorFilter(new PorterDuffColorFilter((int)color, PorterDuff.Mode.SRC_IN));
      }
      else {
        background.setColorFilter(color, PorterDuff.Mode.SRC_IN);
      }
    }
  }

  @ReactProp(name = "inverted", defaultBoolean = false)
  public void setInverted(ReactSlider view, boolean inverted) {
    Log.d(TAG, String.format("设置inverted: %b", inverted));
    if (inverted) view.setScaleX(-1f);
    else view.setScaleX(1f);
  }

  @ReactProp(name = "accessibilityUnits")
  public void setAccessibilityUnits(ReactSlider view, String accessibilityUnits) {
    view.setAccessibilityUnits(accessibilityUnits);
  }

  @ReactProp(name = "accessibilityIncrements")
  public void setAccessibilityIncrements(ReactSlider view, ReadableArray accessibilityIncrements) {
    List objectList = accessibilityIncrements.toArrayList();
    List<String> stringList = new ArrayList<>();
    for(Object item: objectList) {
      stringList.add((String)item);
    }
    view.setAccessibilityIncrements(stringList);
  }

  @Override
  protected void addEventEmitters(final ThemedReactContext reactContext, final ReactSlider view) {
    view.setOnSeekBarChangeListenerForRN(ON_CHANGE_LISTENER);
  }

  @Override
  public Map getExportedCustomDirectEventTypeConstants() {
    return MapBuilder.of(ReactSlidingCompleteEvent.EVENT_NAME, MapBuilder.of("registrationName", "onRNCSliderSlidingComplete"),
            ReactSlidingStartEvent.EVENT_NAME, MapBuilder.of("registrationName", "onRNCSliderSlidingStart"));
  }
}
