package com.ximalaya.ting.android.reactnative.modules.webview;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import com.facebook.react.bridge.ReactContext;
import com.facebook.react.uimanager.ThemedReactContext;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.hybrid.XmSimpleHybridView;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.reactnative.R;
import com.ximalaya.ting.android.reactnative.utils.RNUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

public class NativeHybridWebView extends FrameLayout {
    public static final String TAG  = "NativeHybridWebView";

    private XmSimpleHybridView mXmSimpleHybridView;

    public NativeHybridWebView(@NonNull ThemedReactContext context) {
        super(context);
        init(context);
    }

    public NativeHybridWebView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public NativeHybridWebView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public NativeHybridWebView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);

        init(context);
    }

    private void init(Context context) {

        View view = LayoutInflater.from(context).inflate(R.layout.rn_layout_webview_container, this);
        mXmSimpleHybridView = view.findViewById(R.id.rn_hybrid_view);

        if (context instanceof ThemedReactContext) {
            BaseFragment2 fragment2 = RNUtils.queryFragmentByReactContext((ReactContext) context);
            if (fragment2 == null) {
                fragment2 = (BaseFragment2) RNUtils.getCurFragment(BaseApplication.getTopActivity());
            }

            if (fragment2 == null) {
                MainActivity activity = ToolUtil.getMainActivity();
                if (activity != null) {
                    Fragment currentTopFragment = activity.getCurrentTopFragment();
                    if (currentTopFragment instanceof BaseFragment2) {
                        fragment2 = (BaseFragment2) currentTopFragment;
                    }
                }
            }
            mXmSimpleHybridView.attach(fragment2);

//            mXmSimpleHybridView.loadData("https://mobile.ximalaya.com/gatekeeper/play-page-introduce/v2?device=android&isToListenStatus=false&isPageXDark=false&isPlaying=false&isCurrentTrack=false&timeline=&showStartTime=0&source=home&gotoPlayPage=0&trackId=841426434&openComment=false&auto_play=false&app=iting&version=********&xm_offline_skip=1");
        }
    }


    public void loadUrl(String url) {
        Log.d(TAG, "loadUrl: " + url);
        if (mXmSimpleHybridView == null) {
            return;
        }

        mXmSimpleHybridView.loadData(url);
    }
}
