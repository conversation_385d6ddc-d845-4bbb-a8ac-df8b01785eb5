package com.ximalaya.ting.android.reactnative.packages;

import android.util.Log;

import com.facebook.react.TurboReactPackage;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.module.annotations.ReactModule;
import com.facebook.react.module.annotations.ReactModuleList;
import com.facebook.react.module.model.ReactModuleInfo;
import com.facebook.react.module.model.ReactModuleInfoProvider;
import com.facebook.react.turbomodule.core.interfaces.TurboModule;
import com.facebook.react.uimanager.ViewManager;
import com.ximalaya.reactnative.XMReactNativeApi;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.reactnative.modules.AbTestModule;
import com.ximalaya.ting.android.reactnative.modules.AccountModule;
import com.ximalaya.ting.android.reactnative.modules.AlarmModule;
import com.ximalaya.ting.android.reactnative.modules.BusinessModule;
import com.ximalaya.ting.android.reactnative.modules.ChatxmlyMVPModule;
import com.ximalaya.ting.android.reactnative.modules.ChatxmlyRoleModule;
import com.ximalaya.ting.android.reactnative.modules.CommentInputModule;
import com.ximalaya.ting.android.reactnative.modules.ConfigModule;
import com.ximalaya.ting.android.reactnative.modules.DeviceInfoModule;
import com.ximalaya.ting.android.reactnative.modules.DeviceOrientationModule;
import com.ximalaya.ting.android.reactnative.modules.DownloadModule;
import com.ximalaya.ting.android.reactnative.modules.EncryptModule;
import com.ximalaya.ting.android.reactnative.modules.EnvModule;
import com.ximalaya.ting.android.reactnative.modules.FileModule;
import com.ximalaya.ting.android.reactnative.modules.HttpModule;
import com.ximalaya.ting.android.reactnative.modules.ImageModule;
import com.ximalaya.ting.android.reactnative.modules.InspireAdModule;
import com.ximalaya.ting.android.reactnative.modules.LoadingViewManager;
import com.ximalaya.ting.android.reactnative.modules.MixPlayerModule;
import com.ximalaya.ting.android.reactnative.modules.NavBarModule;
import com.ximalaya.ting.android.reactnative.modules.PageModule;
import com.ximalaya.ting.android.reactnative.modules.PaymentModule;
import com.ximalaya.ting.android.reactnative.modules.PubSubModule;
import com.ximalaya.ting.android.reactnative.modules.RNViewTagCaptureModule;
import com.ximalaya.ting.android.reactnative.modules.InspireAdViewManager;
import com.ximalaya.ting.android.reactnative.modules.DownloadAdViewManager;
import com.ximalaya.ting.android.reactnative.modules.RnUserSetting;
import com.ximalaya.ting.android.reactnative.modules.ShareModule;
import com.ximalaya.ting.android.reactnative.modules.ShortStreamModule;
import com.ximalaya.ting.android.reactnative.modules.ShortcutModule;
import com.ximalaya.ting.android.reactnative.modules.SimpleBufferPlayerModule;
import com.ximalaya.ting.android.reactnative.modules.SimplePlayerModule;
import com.ximalaya.ting.android.reactnative.modules.StorageModule;
import com.ximalaya.ting.android.reactnative.modules.SystemTraceModule;
import com.ximalaya.ting.android.reactnative.modules.WidgetModule;
import com.ximalaya.ting.android.reactnative.modules.XMApmModule;
import com.ximalaya.ting.android.reactnative.modules.XMBroadcastModule;
import com.ximalaya.ting.android.reactnative.modules.XMElderlyRNBridgeModule;
import com.ximalaya.ting.android.reactnative.modules.XMNovelTabRNBridgeModule;
import com.ximalaya.ting.android.reactnative.modules.XMRecordModule;
import com.ximalaya.ting.android.reactnative.modules.XMStatisticsModule;
import com.ximalaya.ting.android.reactnative.modules.XMStatusBarModule;
import com.ximalaya.ting.android.reactnative.modules.XMSubscribeBridgeModule;
import com.ximalaya.ting.android.reactnative.modules.XMToastModule;
import com.ximalaya.ting.android.reactnative.modules.XMTraceModule;
import com.ximalaya.ting.android.reactnative.modules.XMUniversalFireworkModule;
import com.ximalaya.ting.android.reactnative.modules.XYAssistantModule;
import com.ximalaya.ting.android.reactnative.modules.XmPlayerModule;
import com.ximalaya.ting.android.reactnative.modules.ad.AdModule;
import com.ximalaya.ting.android.reactnative.modules.aiagent.AiAgentSettingModule;
import com.ximalaya.ting.android.reactnative.modules.aiagent.ChatxmlyAiAgentModule;
import com.ximalaya.ting.android.reactnative.modules.alphamovie.XMAlphaMovieManager;
import com.ximalaya.ting.android.reactnative.modules.blur.BlurViewManager;
import com.ximalaya.ting.android.reactnative.modules.business.BusinessInfoModule;
import com.ximalaya.ting.android.reactnative.modules.commonframe.ReactNestViewWrapperViewManager;
import com.ximalaya.ting.android.reactnative.modules.doc.ReactDocCellManager;
import com.ximalaya.ting.android.reactnative.modules.doc.ReactDocDetailManager;
import com.ximalaya.ting.android.reactnative.modules.embedded.EmbeddedViewCommunicateModule;
import com.ximalaya.ting.android.reactnative.modules.gif.GifViewManager;
import com.ximalaya.ting.android.reactnative.modules.image.ReactImageManager;
import com.ximalaya.ting.android.reactnative.modules.nest.NestedScrollViewHeaderManager;
import com.ximalaya.ting.android.reactnative.modules.nest.NestedScrollViewManager;
import com.ximalaya.ting.android.reactnative.modules.nestscroll.ReactNestedScrollViewManager;
import com.ximalaya.ting.android.reactnative.modules.pulltorefresh.SwipeRefreshLayoutManager;
import com.ximalaya.ting.android.reactnative.modules.scrollView.LikeScrollViewManager;
import com.ximalaya.ting.android.reactnative.modules.shortVideo.ShortVideoCoverViewManager;
import com.ximalaya.ting.android.reactnative.modules.stick.ReactContentViewManager;
import com.ximalaya.ting.android.reactnative.modules.stick.ReactHeaderViewManager;
import com.ximalaya.ting.android.reactnative.modules.stick.ReactScrollViewWrapperViewManager;
import com.ximalaya.ting.android.reactnative.modules.stick.StickNavLayoutViewManager;
import com.ximalaya.ting.android.reactnative.modules.vedio.ReactVedioPlayerViewManager;
import com.ximalaya.ting.android.reactnative.modules.verticalpageview.ReactVerticalPreviewViewPagerManager;
import com.ximalaya.ting.android.reactnative.modules.viewpagerwrap.ReactViewPagerWrapManager;
import com.ximalaya.ting.android.reactnative.modules.viewpagerwrap.XMRNPageWrapViewModule;
import com.ximalaya.ting.android.reactnative.modules.visibilitytracker.VisibilityTrackerManager;
import com.ximalaya.ting.android.reactnative.modules.visibilitytracker.VisibilityTrackerModule;
import com.ximalaya.ting.android.reactnative.modules.webview.NativeHybridWebViewManager;
import com.ximalaya.ting.android.reactnative.modules.wheelView.WheelViewManager;
import com.ximalaya.ting.android.reactnative.modules.xmvideo.XMVideoPlayerManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by hebin on 2018/6/9.
 */
@ReactModuleList(nativeModules = {
        AccountModule.class, DeviceInfoModule.class, HttpModule.class, PageModule.class, EnvModule.class,
        PaymentModule.class, ShareModule.class, XmPlayerModule.class, XMStatisticsModule.class,
        XMRecordModule.class,
        EncryptModule.class, DownloadModule.class, ImageModule.class, StorageModule.class,
        FileModule.class, NavBarModule.class, XMToastModule.class, XMStatusBarModule.class,
        XMTraceModule.class, RNViewTagCaptureModule.class, SystemTraceModule.class, RnUserSetting.class,
        MixPlayerModule.class, ConfigModule.class, AlarmModule.class, SimplePlayerModule.class, SimpleBufferPlayerModule.class,
        ChatxmlyMVPModule.class, AiAgentSettingModule.class, ChatxmlyAiAgentModule.class, ChatxmlyRoleModule.class, ShortcutModule.class, XMBroadcastModule.class, XMApmModule.class,
        CommentInputModule.class, BusinessModule.class, InspireAdModule.class, AbTestModule.class, VisibilityTrackerModule.class, XMSubscribeBridgeModule.class,
        XYAssistantModule.class, BusinessInfoModule.class, XMUniversalFireworkModule.class, EmbeddedViewCommunicateModule.class,
        WidgetModule.class, XMElderlyRNBridgeModule.class, XMNovelTabRNBridgeModule.class, DeviceOrientationModule.class, AdModule.class,
        PubSubModule.class, ShortStreamModule.class, XMRNPageWrapViewModule.class
})
public class RNReactPackage extends TurboReactPackage {
    @Override
    public NativeModule getModule(String name, ReactApplicationContext reactContext) {
        if (ConstantsOpenSdk.isDebug) {
            Log.i("z_rn_trace", "getModule: " + name + ", " + Log.getStackTraceString(new Throwable("")));
        }
        switch (name) {
            case AccountModule.NAME:
                return new AccountModule(reactContext);
            case DeviceInfoModule.NAME:
                return new DeviceInfoModule(reactContext);
            case DeviceOrientationModule.NAME:
                return new DeviceOrientationModule(reactContext);
            case HttpModule.NAME:
                return new HttpModule(reactContext);
            case PageModule.NAME:
                return new PageModule(reactContext);
            case EnvModule.NAME:
                return new EnvModule(reactContext);
            case PaymentModule.NAME:
                return new PaymentModule(reactContext);
            case ShareModule.NAME:
                return new ShareModule(reactContext);
            case XmPlayerModule.NAME:
                return new XmPlayerModule(reactContext);
            case XMStatisticsModule.NAME:
                return new XMStatisticsModule(reactContext);
            case XMRecordModule.NAME:
                return new XMRecordModule(reactContext);
            case EncryptModule.NAME:
                return new EncryptModule(reactContext);
            case DownloadModule.NAME:
                return new DownloadModule(reactContext);
            case ImageModule.NAME:
                return new ImageModule(reactContext);
            case StorageModule.NAME:
                return new StorageModule(reactContext);
            case FileModule.NAME:
                return new FileModule(reactContext);
            case NavBarModule.NAME:
                return new NavBarModule(reactContext);
            case XMToastModule.NAME:
                return new XMToastModule(reactContext);
            case XMStatusBarModule.NAME:
                return new XMStatusBarModule(reactContext);
            case XMTraceModule.NAME:
                return new XMTraceModule(reactContext);
            case RNViewTagCaptureModule.NAME:
                return new RNViewTagCaptureModule(reactContext);
            case SystemTraceModule.NAME:
                return new SystemTraceModule(reactContext);
            case RnUserSetting.NAME:
                return new RnUserSetting(reactContext);
            case MixPlayerModule.NAME:
                return new MixPlayerModule(reactContext);
            case ConfigModule.NAME:
                return new ConfigModule(reactContext);
            case AlarmModule.NAME:
                return new AlarmModule(reactContext);
            case SimplePlayerModule.NAME:
                return new SimplePlayerModule(reactContext);
            case SimpleBufferPlayerModule.NAME:
                return new SimpleBufferPlayerModule(reactContext);
            case ChatxmlyMVPModule.NAME:
                return new ChatxmlyMVPModule(reactContext);
            case AiAgentSettingModule.NAME:
                return new AiAgentSettingModule(reactContext);
            case ChatxmlyAiAgentModule.NAME:
                return new ChatxmlyAiAgentModule(reactContext);
            case ChatxmlyRoleModule.NAME:
                return new ChatxmlyRoleModule(reactContext);
            case ShortcutModule.NAME:
                return new ShortcutModule(reactContext);
            case XMSubscribeBridgeModule.NAME:
                return new XMSubscribeBridgeModule(reactContext);
            case XMBroadcastModule.NAME:
                return new XMBroadcastModule(reactContext);
            case XMApmModule.NAME:
                return new XMApmModule(reactContext);
            case CommentInputModule.NAME:
                return new CommentInputModule(reactContext);
            case BusinessModule.NAME:
                return new BusinessModule(reactContext);
            case InspireAdModule.NAME:
                return new InspireAdModule(reactContext);
            case AbTestModule.NAME:
                return new AbTestModule(reactContext);
            case VisibilityTrackerModule.NAME:
                return new VisibilityTrackerModule(reactContext);
            case XYAssistantModule.NAME:
                return new XYAssistantModule(reactContext);
            case BusinessInfoModule.NAME:
                return new BusinessInfoModule(reactContext);
            case XMUniversalFireworkModule.NAME:
                return new XMUniversalFireworkModule(reactContext);
            case EmbeddedViewCommunicateModule.NAME:
                return new EmbeddedViewCommunicateModule(reactContext);
            case WidgetModule.NAME:
                return new WidgetModule(reactContext);
            case XMElderlyRNBridgeModule.NAME:
                return new XMElderlyRNBridgeModule(reactContext);
            case XMNovelTabRNBridgeModule.NAME:
                return new XMNovelTabRNBridgeModule(reactContext);
            case AdModule.NAME:
                return new AdModule(reactContext);
            case PubSubModule.NAME:
                return new PubSubModule(reactContext);
            case ShortStreamModule.NAME:
                return new ShortStreamModule(reactContext);
            case XMRNPageWrapViewModule.NAME:
                return new XMRNPageWrapViewModule(reactContext);

        }
        return null;
    }

    @Override
    public List<ViewManager> createViewManagers(ReactApplicationContext reactContext) {
        List<ViewManager> viewManagers = new ArrayList<>();
        viewManagers.add(new LoadingViewManager());
        viewManagers.add(new LikeScrollViewManager());
        viewManagers.add(new GifViewManager());
        viewManagers.add(new WheelViewManager());
        viewManagers.add(new ReactVedioPlayerViewManager());
        viewManagers.add(new XMVideoPlayerManager());
        viewManagers.add(new VisibilityTrackerManager());
        if (XMReactNativeApi.useGlide()) {
            viewManagers.add(new ReactImageManager());
        }
        viewManagers.add(new SwipeRefreshLayoutManager());
        viewManagers.add(new BlurViewManager());
        viewManagers.add(new ReactContentViewManager());
        viewManagers.add(new ReactHeaderViewManager());
        viewManagers.add(new ReactScrollViewWrapperViewManager());
        viewManagers.add(new StickNavLayoutViewManager());
        viewManagers.add(new ReactNestedScrollViewManager());
        viewManagers.add(new ReactNestViewWrapperViewManager());
        viewManagers.add(new XMAlphaMovieManager());
        viewManagers.add(new ShortVideoCoverViewManager());
        viewManagers.add(new NestedScrollViewHeaderManager());
        viewManagers.add(new NestedScrollViewManager());
        viewManagers.add(new InspireAdViewManager());
        viewManagers.add(new DownloadAdViewManager());
        viewManagers.add(new ReactDocCellManager());
        viewManagers.add(new ReactDocDetailManager());
        viewManagers.add(new NativeHybridWebViewManager());
        viewManagers.add(new ReactVerticalPreviewViewPagerManager());
        viewManagers.add(new ReactViewPagerWrapManager());
        return viewManagers;
    }

    @Override
    public ReactModuleInfoProvider getReactModuleInfoProvider() {
        try {
            Class<?> reactModuleInfoProviderClass =
                    Class.forName("com.ximalaya.ting.android.reactnative.packages.RNReactPackage$$ReactModuleInfoProvider");
            return (ReactModuleInfoProvider) reactModuleInfoProviderClass.newInstance();
        } catch (ClassNotFoundException e) {
            // In OSS case, the annotation processor does not run. We fall back on creating this byhand
            Class<? extends NativeModule>[] moduleList =
                    new Class[]{
                            AccountModule.class, DeviceInfoModule.class, HttpModule.class, PageModule.class, EnvModule.class,
                            PaymentModule.class, ShareModule.class, XmPlayerModule.class, XMStatisticsModule.class,
                            XMRecordModule.class,
                            EncryptModule.class, DownloadModule.class, ImageModule.class, StorageModule.class,
                            FileModule.class, NavBarModule.class, XMToastModule.class, XMStatusBarModule.class,
                            XMTraceModule.class, RNViewTagCaptureModule.class, SystemTraceModule.class, RnUserSetting.class,
                            MixPlayerModule.class, ConfigModule.class, AlarmModule.class, SimplePlayerModule.class, SimpleBufferPlayerModule.class,
                            ChatxmlyMVPModule.class, AiAgentSettingModule.class , ChatxmlyAiAgentModule.class, ChatxmlyRoleModule.class, ShortcutModule.class, XMBroadcastModule.class, XMApmModule.class,
                            CommentInputModule.class, BusinessModule.class, InspireAdModule.class, XYAssistantModule.class, XMUniversalFireworkModule.class,
                            BusinessInfoModule.class, EmbeddedViewCommunicateModule.class, WidgetModule.class, XMSubscribeBridgeModule.class,
                            DeviceOrientationModule.class, AdModule.class, XMNovelTabRNBridgeModule.class, PubSubModule.class, ShortStreamModule.class
                    };

            final Map<String, ReactModuleInfo> reactModuleInfoMap = new HashMap<>();
            for (Class<? extends NativeModule> moduleClass : moduleList) {
                ReactModule reactModule = moduleClass.getAnnotation(ReactModule.class);

                reactModuleInfoMap.put(
                        reactModule.name(),
                        new ReactModuleInfo(
                                reactModule.name(),
                                moduleClass.getName(),
                                reactModule.canOverrideExistingModule(),
                                reactModule.needsEagerInit(),
                                reactModule.hasConstants(),
                                reactModule.isCxxModule(),
                                TurboModule.class.isAssignableFrom(moduleClass)));
            }

            return new ReactModuleInfoProvider() {
                @Override
                public Map<String, ReactModuleInfo> getReactModuleInfos() {
                    return reactModuleInfoMap;
                }
            };
        } catch (InstantiationException e) {
            throw new RuntimeException(
                    "No ReactModuleInfoProvider for CoreModulesPackage$$ReactModuleInfoProvider", e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(
                    "No ReactModuleInfoProvider for CoreModulesPackage$$ReactModuleInfoProvider", e);
        }
    }
}
