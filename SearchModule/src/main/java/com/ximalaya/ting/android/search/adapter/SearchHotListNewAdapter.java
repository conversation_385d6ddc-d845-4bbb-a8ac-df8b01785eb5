package com.ximalaya.ting.android.search.adapter;

import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.adapter.MyFragmentStatePagerAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.model.search.HotListCategory;
import com.ximalaya.ting.android.host.model.search.SearchHotList;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.search.base.BaseSearchHotWordFragment;
import com.ximalaya.ting.android.search.base.ISearchContext;
import com.ximalaya.ting.android.search.page.SearchHotListDetailFragmentNew;

import java.lang.ref.WeakReference;
import java.util.Iterator;
import java.util.Set;

/**
 *  热搜详情页
 */

public class SearchHotListNewAdapter extends MyFragmentStatePagerAdapter {

    private SearchHotList mData;
    private ISearchContext mSearchContext;
    private SparseArray<WeakReference<BaseFragment>> mFragmentRefs;
    private boolean useReact = true;

    public SearchHotListNewAdapter(FragmentManager fragmentManager, SearchHotList searchHotList) {
        super(fragmentManager);
        this.mFragmentRefs = new SparseArray<>();
        this.mData = searchHotList;
        useReact = ConfigureCenter.getInstance().getBool("toc", "use_rn_search_rank", true);
    }

    @Override
    public Fragment getItem(int position) {
        if (useReact) {
            return getReactItem(position);
        }

        BaseFragment fragment;
        WeakReference<BaseFragment> weakReference;
        if (mFragmentRefs.size() > position) {
            weakReference = mFragmentRefs.get(position);
            if (weakReference != null) {
                fragment = weakReference.get();
                if (fragment != null) {
                    return fragment;
                }
            }
        }
        HotListCategory hotListCategory = null;
        if (mData != null && !ToolUtil.isEmptyCollects(mData.getCategorys())) {
            hotListCategory = mData.getCategorys().get(position);
        }
        SearchHotListDetailFragmentNew fragmentDetail = SearchHotListDetailFragmentNew.newInstance(hotListCategory);
        if (mSearchContext != null) {
            fragmentDetail.setSearchContext(mSearchContext);
        }
        fragment = fragmentDetail;
        weakReference = new WeakReference<>(fragment);
        mFragmentRefs.put(position, weakReference);
        return fragment;
    }

    private BaseFragment getReactItem(int position) {
        BaseFragment fragment;
        WeakReference<BaseFragment> weakReference;
        if (mFragmentRefs.size() > position) {
            weakReference = mFragmentRefs.get(position);
            if (weakReference != null) {
                fragment = weakReference.get();
                if (fragment != null) {
                    return fragment;
                }
            }
        }
        HotListCategory hotListCategory = null;
        if (mData != null && !ToolUtil.isEmptyCollects(mData.getCategorys())) {
            hotListCategory = mData.getCategorys().get(position);
        }

        boolean hasDebugBundleInfo = false;
        final Bundle bundle = new Bundle();
        bundle.putBoolean("inTab", true);
        bundle.putBoolean("canSlide", false);
        String fragmentName = "rn";

        if (ConstantsOpenSdk.isDebug && BaseUtil.getIntSystemProperties("debug.rn_hot_rank") == 1) {
//            String debugUrl = "iting://open?msg_type=94&bundle=rn_hot_rank&mb___debug_bundle=http%3A%2F%2F10.146.2.115%3A8981%2Frn-bundle%2Fpackage.zip%3FbundleId%3DYGVinHM_wV";
            String debugUrl = "iting://open?msg_type=94&__debug=1&bundle=rn_hot_rank&ip=************:8081&__ip=************:8081";
            //BaseUtil.getStrSystemProperties("debug.category_rn_url");
            if (debugUrl != null && !debugUrl.isEmpty()) {
                Uri uri = Uri.parse(debugUrl);
                Set<String> keySet = uri.getQueryParameterNames();
                if (keySet != null && !keySet.isEmpty()) {
                    Iterator<String> iterator = keySet.iterator();
                    while (iterator.hasNext()) {
                        String key = iterator.next();
                        bundle.putString(key, uri.getQueryParameter(key));
                    }
                }
                boolean isDebug = ConstantsOpenSdk.isDebug && ("1".equals(bundle.getString("__debug")));
                fragmentName = isDebug ? "rntest" : "rn";

                bundle.putString("fragmentName", fragmentName);
                hasDebugBundleInfo = true;
            }
        }

        if (!hasDebugBundleInfo) {
            fragmentName = "rn";
            bundle.putString("fragmentName", "rn");
            bundle.putString("bundle", "rn_hot_rank");
        }

        if (hotListCategory != null) {
            bundle.putString("categoryId", hotListCategory.getCategoryId() + "");
            int contentId= hotListCategory.getContentId();
            if (contentId > 0){
                bundle.putInt("contentId", hotListCategory.getContentId());
                hotListCategory.setContentId(-1);
            }

            String categoryName = hotListCategory.getCategoryName();
            if (categoryName != null && !categoryName.isEmpty()) {
                bundle.putString("categoryName", categoryName);
            }
        }

        BaseFragment rnFragment = null;
        try {
            RNActionRouter router = Router.getActionRouter(Configure.BUNDLE_RN);
            if (router != null && router.getFragmentAction() != null) {
                rnFragment = router.getFragmentAction().newRNFragment(fragmentName, bundle);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        fragment = rnFragment;
        weakReference = new WeakReference<>(fragment);
        mFragmentRefs.put(position, weakReference);

        return fragment;
    }

    @Override
    public int getCount() {
        if (mData == null || ToolUtil.isEmptyCollects(mData.getCategorys())) {
            return 0;
        }
        return mData.getCategorys().size();
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        super.destroyItem(container, position, object);
        mFragmentRefs.remove(position);
    }

    @Override
    public CharSequence getPageTitle(int position) {
        if (mData == null || ToolUtil.isEmptyCollects(mData.getCategorys())) {
            return "";
        }
        if (mData.getCategorys().size() <= position) {
            return "";
        }
        if (mData.getCategorys().get(position) == null) {
            return "";
        }
        if (TextUtils.isEmpty(mData.getCategorys().get(position).getCategoryName())) {
            return "";
        }
        return mData.getCategorys().get(position).getCategoryName();
    }

    public void setSearchContext(ISearchContext context) {
        this.mSearchContext = context;
    }

}
