package com.ximalaya.ting.android.search.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.search.SearchHotWord;
import com.ximalaya.ting.android.host.util.HotRankNumUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.search.R;
import com.ximalaya.ting.android.search.SearchConstants;
import com.ximalaya.ting.android.search.base.ISearchContext;
import com.ximalaya.ting.android.search.utils.SearchTagUtil;
import com.ximalaya.ting.android.search.utils.SearchTraceUtils;
import com.ximalaya.ting.android.search.utils.SearchUiUtils;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmtrace.widget.IRecyclerViewAdapter;
import java.util.List;

public class SearchHotListTextAdapter extends RecyclerView.Adapter<SearchHotListTextAdapter.ViewHolder>
        implements IRecyclerViewAdapter {
    public static final int TYPE_NORMAL = 0;
    public static final int TYPE_SPECIAL = 1;
    private final Context mContext;
    private final int mType;
    private final String mTabName;
    private final List<SearchHotWord> mSearchHotWordList;
    private ISearchContext mSearchContext;
    private int adPosition;

    public SearchHotListTextAdapter(Context context, List<SearchHotWord> searchHotWordList, String tabName, int type) {
        this.mContext = context;
        this.mSearchHotWordList = searchHotWordList;
        this.mTabName = tabName;
        this.mType = type;
        this.adPosition = -1;
    }

    public void setSearchContext(ISearchContext searchContext) {
        this.mSearchContext = searchContext;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(
                R.layout.search_item_search_hot_list_text, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int position) {
        if (ToolUtil.isEmptyCollects(mSearchHotWordList)) return;
        SearchHotWord hotListWord = getItem(position);
        if (hotListWord == null) {
            return;
        }
        if (viewHolder.tvRank.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) viewHolder.tvRank.getLayoutParams();
            params.topMargin = position < 3 ? BaseUtil.dp2px(mContext, mType == TYPE_NORMAL ? 2 : 3) : 0;
            params.rightMargin = BaseUtil.dp2px(mContext, 1);
            params.width = hotListWord.isAdWord() ? BaseUtil.dp2px(mContext, 6) : ViewGroup.MarginLayoutParams.WRAP_CONTENT;
            params.height = hotListWord.isAdWord() ? BaseUtil.dp2px(mContext, 6) : ViewGroup.MarginLayoutParams.WRAP_CONTENT;
            viewHolder.tvRank.setLayoutParams(params);
        }
        viewHolder.tvRank.setText(String.valueOf(position + ((position > adPosition && adPosition != -1) ? 0 : 1)));
        if (hotListWord.isAdWord()) {
            adPosition = viewHolder.getBindingAdapterPosition();
            viewHolder.tvRank.setText("");
            viewHolder.ivRank.setVisibility(View.INVISIBLE);
            viewHolder.tvRank.setBackgroundResource(R.drawable.search_hot_search_ad_index_bg);
        } else {
            if (position >= 3) {
                viewHolder.ivRank.setVisibility(View.INVISIBLE);
                viewHolder.tvRank.setBackgroundResource(R.drawable.search_hot_search_rank_index_bg);
            } else {
                boolean isNormal = mType == TYPE_NORMAL;
                @DrawableRes int topRankingResId = isNormal ? R.drawable.search_normal_rank_first : R.drawable.search_hot_rank_first_new;
                switch (position) {
                    case 1:
                        topRankingResId = isNormal ? R.drawable.search_normal_rank_second : R.drawable.search_hot_rank_second_new;
                        break;
                    case 2:
                        topRankingResId = isNormal ? R.drawable.search_normal_rank_third : R.drawable.search_hot_rank_third_new;
                        break;
                    default:
                        break;
                }
                viewHolder.ivRank.setImageResource(topRankingResId);
                viewHolder.ivRank.setVisibility(View.VISIBLE);
                viewHolder.tvRank.setBackgroundResource(0);
            }
        }
        String displayWord = !TextUtils.isEmpty(hotListWord.getDisplayWord()) ? hotListWord.getDisplayWord() : hotListWord.getSearchWord();
        viewHolder.tvTitle.setText(displayWord);
        if (hotListWord.isAdWord()) {
            viewHolder.tvHotIndex.setVisibility(View.GONE);
        } else {
            viewHolder.tvHotIndex.setText(HotRankNumUtil.formatNumberWithUnit(hotListWord.getHotScore()));
            viewHolder.tvHotIndex.setVisibility(View.VISIBLE);
        }

        int resId = SearchTagUtil.getRankTagDrawableRes(hotListWord);
        String tag = SearchTagUtil.getRankTagNameStr(hotListWord, true);

        if (resId > 0) {
            viewHolder.tvTitle.setCompoundDrawablesWithIntrinsicBounds(0, 0, resId, 0);
            viewHolder.tvTitle.setCompoundDrawablePadding(BaseUtil.dp2px(mContext, 4));
        } else {
            viewHolder.tvTitle.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
        if (!TextUtils.isEmpty(hotListWord.getIntro())) {
            String intro = String.format("%s", hotListWord.getIntro());
            SearchUiUtils.setTextWithVisibleStateAuto(viewHolder.tvIntro, intro);
            viewHolder.tvTitle.setMaxEms(13);
        } else {
            SearchUiUtils.setVisible(View.GONE, viewHolder.tvIntro);
        }
        SearchTraceUtils.traceHotWordExposure(viewHolder.itemView, mTabName,
                SearchConstants.TRACE_PAGE_SEARCH_ENTRANCE, hotListWord.getDisplayWord(),
                hotListWord.getSearchWord(), String.valueOf(hotListWord.getHotScore()),
                tag, position + 1, hotListWord.getXmRequestId());
        //广告词曝光
        if (hotListWord.isAdWord() && hotListWord.getAdInfo()!= null) {
           Advertis advertis = hotListWord.getAdInfo();
            AdManager.adRecord(mContext,advertis,
                    AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW ,
                            AppConstants.AD_POSITION_NAME_HOT_SEARCH_LIST).showType(Advertis.SHOW_TYPE_STATIC).build());
        }
        final String finalTag = tag;
        viewHolder.itemView.setOnClickListener(v -> {
            if (mSearchContext != null) {
                mSearchContext.onItemClick(v, hotListWord, SearchConstants.HOT_WORD, 1, position);
            }
            new XMTraceApi.Trace()
                    .click(2902)
                    .put("currPage", "search")
                    .put("searchWord", hotListWord.getSearchWord())
                    .put("tabName", mTabName)
                    .put("isThrough", String.valueOf(hotListWord.isIsThrough()))
                    .put("throughType", String.valueOf(hotListWord.getThroughType()))
                    .put("link", hotListWord.getUrl())
                    .put("position", String.valueOf(position + 1))
                    .put("abTest", "b")
                    .put("busId", hotListWord.getBusId())
                    .put("productId", hotListWord.getProductId())
                    .put("requestId", hotListWord.getRequestId())
                    .put(XmRequestIdManager.XM_REQUEST_ID, hotListWord.getXmRequestId())
                    .createTrace();
            SearchTraceUtils.traceHotWordClick(mTabName, SearchConstants.TRACE_PAGE_SEARCH_ENTRANCE,
                    hotListWord.getDisplayWord(), hotListWord.getSearchWord(),
                    String.valueOf(hotListWord.getHotScore()), finalTag, position, hotListWord.getXmRequestId());
            if (hotListWord.isAdWord() && hotListWord.getAdInfo() != null) {
                Advertis advertis = hotListWord.getAdInfo();
                AdReportModel.Builder builder = AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                AppConstants.AD_POSITION_NAME_HOT_SEARCH_LIST).
                        onlyClickRecord(true).
                        showType(Advertis.SHOW_TYPE_STATIC);
                AdManager.handlerAdClick(mContext, advertis, builder.build());
            }
        });
        new XMTraceApi.Trace()
                .setMetaId(28550)
                .setServiceId("slipPage")
                .put("tabName", mTabName)
                .put("searchWord", hotListWord.getSearchWord())
                .put("isThrough", String.valueOf(hotListWord.isIsThrough()))
                .put("throughType", String.valueOf(hotListWord.getThroughType()))
                .put("link", hotListWord.getUrl())
                .put("position", String.valueOf(position + 1))
                .put("currPage", "search")
                .put("abTest", "b")
                .put("busId", hotListWord.getBusId())
                .put("productId", hotListWord.getProductId())
                .put(XmRequestIdManager.CONT_TYPE, "searchWord")
                .put(XmRequestIdManager.CONT_ID, hotListWord.getSearchWord())
                .put(XmRequestIdManager.XM_REQUEST_ID, hotListWord.getXmRequestId())
                .createTrace();

    }

    @Override
    public int getItemCount() {
        return 10;
    }

    @Override
    public SearchHotWord getItem(int position) {
        if (mSearchHotWordList != null && position >= 0 && position < mSearchHotWordList.size()) {
            return mSearchHotWordList.get(position);
        }
        return null;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        private final ImageView ivRank;
        private final TextView tvRank;
        private final TextView tvTitle;
        private final TextView tvIntro;
        private final TextView tvHotIndex;

        private ViewHolder(View convertView) {
            super(convertView);
            ivRank = convertView.findViewById(R.id.search_iv_item_rank);
            tvRank = convertView.findViewById(R.id.search_item_search_hot_position);
            Typeface typeface = Typeface.createFromAsset(convertView.getContext().getResources().getAssets(), "fonts" +
                    "/LiveNumber-Bold.ttf");
            tvRank.setTypeface(typeface);
            tvTitle = convertView.findViewById(R.id.search_item_search_hot_title);
            tvIntro = convertView.findViewById(R.id.search_tv_intro);
            tvHotIndex = convertView.findViewById(R.id.search_hot_index);
        }
    }
}
