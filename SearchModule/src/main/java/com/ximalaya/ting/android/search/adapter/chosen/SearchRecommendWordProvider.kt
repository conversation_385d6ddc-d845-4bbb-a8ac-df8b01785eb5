package com.ximalaya.ting.android.search.adapter.chosen

import android.animation.ValueAnimator
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieCompositionFactory
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.model.search.SearchHotWord
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.view.XmLottieDrawable
import com.ximalaya.ting.android.host.view.layout.FlowLayout
import com.ximalaya.ting.android.host.view.layout.FlowLayout.IFLowListener
import com.ximalaya.ting.android.host.view.layout.FlowLayout.LayoutParams
import com.ximalaya.ting.android.host.view.layout.FlowLayout.LineDefinition
import com.ximalaya.ting.android.host.view.layout.FlowLayout.VISIBLE
import com.ximalaya.ting.android.search.R
import com.ximalaya.ting.android.search.SearchConstants
import com.ximalaya.ting.android.search.base.BaseSearchRecyclerViewAdapterProxy
import com.ximalaya.ting.android.search.base.ISearchDataContext
import com.ximalaya.ting.android.search.model.SearchItem
import com.ximalaya.ting.android.search.utils.SearchTagUtil.getHotWordTagDrawable
import com.ximalaya.ting.android.search.utils.SearchTraceUtils
import com.ximalaya.ting.android.search.utils.SearchUiUtils

class SearchRecommendWordProvider(searchDataContext: ISearchDataContext?)
    : BaseSearchRecyclerViewAdapterProxy<SearchRecommendWordProvider.ViewHolder?, SearchItem?>(searchDataContext) {
    private var mHandleClickListener: IHandleClickListener? = null
    private var mRecommendLiveView: TextView? = null
    private var mLiveLottieDrawable: XmLottieDrawable? = null

    fun setHandleClick(handleClickListener: IHandleClickListener?) {
        this.mHandleClickListener = handleClickListener
    }

    override fun bindView(holder: ViewHolder?, data: SearchItem?, extra: Any?, convertView: View?, position: Int) {
        if (holder == null || data == null) {
            return
        }
        val hotWordResult = data.hotWordResult ?: return
        if (hotWordResult.hotWordList.isNullOrEmpty()) {
            return
        }
        SearchUiUtils.setTextSizeWithLargeScreen(holder.tvTitle)
        SearchUiUtils.setVisible(if (position == 0 || data.needTopGradient) View.VISIBLE else View.GONE, holder.vTopBg)
        holder.tvTitle.text = data.title
        holder.flRecommendWord.line = 2
        addHotWordView(holder.flRecommendWord, hotWordResult.hotWordList, hotWordResult.liveWordList, data)
        SearchTraceUtils.traceCardExposure(holder.itemView, SearchConstants.TRACE_TYPE_RECOMMEND_WORD_CARD,
                SearchConstants.TRACE_PAGE_CHOSEN, data.modulePosition, data.abInfo, data.xmRequestId)
    }

    private fun addHotWordView(root: FlowLayout, hotWordList: List<SearchHotWord?>,
                               liveWordList: List<SearchHotWord?>?, data: SearchItem) {
        root.removeAllViews()
        for (i in hotWordList.indices) {
            val hotWord = hotWordList[i]
            if (hotWord == null || hotWord.searchWord.isNullOrEmpty()) {
                continue
            }
            val lp = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            lp.bottomMargin = 8.dp
            lp.rightMargin = 8.dp
            val item = buildRecommendChildView(hotWord, i, data)
            if (item != null) {
                root.addView(item, lp)
                SearchTraceUtils.traceSearchWordView(item, SearchConstants.TRACE_TYPE_RECOMMEND_WORD_CARD,
                        SearchConstants.TRACE_PAGE_CHOSEN, SearchTraceUtils.getKeyWord(), hotWord.searchWord, hotWord.abInfo, data.xmRequestId)
            }
        }
        val liveWord = liveWordList?.getOrNull(0)
        if (liveWord != null && !liveWord.searchWord.isNullOrEmpty()) {
            if (mRecommendLiveView == null) {
                createRecommendLiveView(liveWord)
                mRecommendLiveView?.measure(0, 0)
            }
            val lp = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            lp.bottomMargin = 8.dp
            lp.rightMargin = 8.dp
            setOnClickEvent(mRecommendLiveView, liveWord, hotWordList.size, data)
            SearchUiUtils.removeViewParent(mRecommendLiveView)
            root.addView(mRecommendLiveView, lp)
            initFlowListener(root, liveWord, data)
        } else {
            root.setFLowListener(null)
        }
    }

    private fun initFlowListener(root: FlowLayout, liveWord: SearchHotWord, data: SearchItem) {
        root.setFLowListener(object : IFLowListener {
            override fun newLine(newLineIndex: Int, newLine: LineDefinition, index: Int): Boolean {
                return false
            }

            override fun onNewLineBreak(index: Int, child: View, currentLine: LineDefinition) {
                if (mRecommendLiveView == null) {
                    createRecommendLiveView(liveWord)
                    mRecommendLiveView?.measure(0, 0)
                }
                val views = currentLine.views
                if (views.isNullOrEmpty() || views.contains(mRecommendLiveView)) {
                    return
                }
                SearchUiUtils.removeViewParent(mRecommendLiveView)
                SearchUiUtils.setVisible(VISIBLE, mRecommendLiveView)
                fitRecommendLiveView(index, root, currentLine, liveWord, data)
            }
        })
    }

    private fun buildRecommendChildView(searchHotWord: SearchHotWord, position: Int, data: SearchItem): View? {
        if (context == null) {
            return null
        }
        val item = TextView(context)
        item.text = searchHotWord.searchWord
        item.textSize = if (BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext()) <= 720) 11f else 12f
        item.setTextColor(ContextCompat.getColor(context, R.color.host_color_444444_acacaf))
        item.setBackgroundResource(R.drawable.search_recommend_word_item_bg)
        item.ellipsize = TextUtils.TruncateAt.END
        item.setSingleLine()
        val drawable = getHotWordTagDrawable(searchHotWord)
        if (drawable != null) {
            SearchUiUtils.setDrawable(item, 2, drawable)
            item.compoundDrawablePadding = BaseUtil.dp2px(context, 4f)
        }
        setOnClickEvent(item, searchHotWord, position, data)
        return item
    }

    private fun createRecommendLiveView(searchHotWord: SearchHotWord) {
        if (mRecommendLiveView == null) {
            mRecommendLiveView = TextView(context)
            mRecommendLiveView?.apply {
                text = searchHotWord.searchWord
                textSize = if (BaseUtil.getScreenWidth(BaseApplication.getMyApplicationContext()) <= 720) 11f else 12f
                setTextColor(ContextCompat.getColorStateList(context, R.color.host_color_444444_acacaf))
                setBackgroundResource(R.drawable.search_recommend_word_item_bg)
                ellipsize = TextUtils.TruncateAt.END
                setSingleLine()
                val drawable: Drawable = ColorDrawable()
                drawable.setBounds(0, 0, 13.dp, 10.dp)
                //先给textview设置一个空的drawable，以便计算该textview的宽度时能预留直播动画的宽度
                setCompoundDrawables(null, null, drawable, null)
                compoundDrawablePadding = BaseUtil.dp2px(context, 2f)
            }
            mLiveLottieDrawable = XmLottieDrawable()
            val lottiePath = "search_ic_streamer_live.json"
            LottieCompositionFactory.fromAsset(context, lottiePath).addListener { composition ->
                mLiveLottieDrawable?.apply {
                    setComposition(composition)
                    scale = 1.0f
                    mRecommendLiveView?.setCompoundDrawablesWithIntrinsicBounds(null, null, this, null)
                    repeatCount = ValueAnimator.INFINITE
                    playAnimation()
                }
            }.addFailureListener { mRecommendLiveView?.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null) }
        }
    }

    private fun addRecommendLiveView(index: Int, layout: FlowLayout, currentLine: LineDefinition,
                                     liveWord: SearchHotWord, data: SearchItem) {
        val removedView = layout.getChildAt(index)
        if (removedView != null) {
            SearchUiUtils.removeViewParent(removedView)
        }
        currentLine.removeView(currentLine.views.size - 1)
        fitRecommendLiveView(index, layout, currentLine, liveWord, data)
    }

    private fun fitRecommendLiveView(index: Int, layout: FlowLayout, currentLine: LineDefinition,
                                     liveWord: SearchHotWord, data: SearchItem) {
        var indexNew = index
        mRecommendLiveView?.apply {
            if (currentLine.canFit(this)) {
                val config = currentLine.config
                val lp = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                lp.bottomMargin = 8.dp
                val rightRest = currentLine.restLength() - lp.leftMargin - measuredWidth
                lp.rightMargin = if (rightRest > 0) rightRest else 0
                layoutParams = lp
                setOnClickEvent(this, liveWord, indexNew, data)
                lp.setFields(config.orientation, this)
                layout.addView(this, indexNew)
                SearchTraceUtils.traceSearchWordView(this, SearchConstants.TRACE_TYPE_RECOMMEND_WORD_CARD,
                        SearchConstants.TRACE_PAGE_CHOSEN, SearchTraceUtils.getKeyWord(), liveWord.searchWord, liveWord.abInfo, liveWord.xmRequestId)
            } else {
                addRecommendLiveView(--indexNew, layout, currentLine, liveWord, data)
            }
        }
    }

    private fun setOnClickEvent(view: View?, hotWord: SearchHotWord, position: Int, data: SearchItem) {
        view?.setOnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            SearchTraceUtils.traceSearchWordClick(SearchConstants.TRACE_TYPE_RECOMMEND_WORD_CARD,
                    SearchConstants.TRACE_PAGE_CHOSEN, SearchTraceUtils.getKeyWord(), hotWord.searchWord, hotWord.abInfo, data.xmRequestId)
            SearchTraceUtils.traceCardClick(SearchConstants.TRACE_TYPE_RECOMMEND_WORD_CARD,
                    SearchConstants.TRACE_PAGE_CHOSEN, data.modulePosition, data.abInfo, data.xmRequestId)
            mHandleClickListener?.onHandleClick(v, hotWord,
                    SearchConstants.RESULT_RECOMMEND_WORD, 0, position)
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.search_item_recommend_word_2024
    }

    override fun buildHolder(convertView: View): ViewHolder {
        return ViewHolder(convertView)
    }

    interface IHandleClickListener {
        fun onHandleClick(v: View, hotWord: SearchHotWord, type: Int, pageId: Int, position: Int)
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val vTopBg: View? = view.findViewById(R.id.search_v_top_bg)
        val tvTitle: TextView = view.findViewById(R.id.search_tv_title)
        val flRecommendWord: FlowLayout = view.findViewById(R.id.search_fl_recommend_word)
    }
}