package com.ximalaya.ting.android.search.adapter.chosen.aiassistant

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.search.R
import com.ximalaya.ting.android.search.base.ISearchDataContext
import com.ximalaya.ting.android.search.model.SearchItem
import com.ximalaya.ting.android.search.model.aiassistant.EventSourceResponse

class AiFinalAnswerAdapterProxy(searchDataContext: ISearchDataContext?, labelForTrace: String?, searchItem: SearchItem?)
    :BaseAiContentViewAdapterProxy(searchDataContext, labelForTrace, searchItem) {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return AiFinalIntroViewHolder(
            LayoutInflater.from(parent.context)
            .inflate(R.layout.search_item_ai_final_answer, parent, false))
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, eventSourceResponse: EventSourceResponse) {
        val aiFinalIntroViewHolder = holder as AiFinalIntroViewHolder
        aiFinalIntroViewHolder.tvIntro.text = eventSourceResponse.data?.finalAnswer?.text
    }

    class AiFinalIntroViewHolder(val view: View) : RecyclerView.ViewHolder(view){
        var tvIntro: TextView = view.findViewById(R.id.search_tv_ai_intro)
    }
}