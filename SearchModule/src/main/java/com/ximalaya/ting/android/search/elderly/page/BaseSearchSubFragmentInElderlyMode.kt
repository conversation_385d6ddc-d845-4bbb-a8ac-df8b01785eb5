package com.ximalaya.ting.android.search.elderly.page

import com.ximalaya.ting.android.search.base.BaseSearchFragment
import com.ximalaya.ting.android.search.base.ISearchDataContext
import com.ximalaya.ting.android.search.base.ISearchDataSubContext

/**
 * 老年模式搜索tab基类
 *
 * <AUTHOR>
 */
abstract class BaseSearchSubFragmentInElderlyMode<T> : BaseSearchFragment<T>(), ISearchDataSubContext {
    @JvmField
    protected var isFirstLoadTab = true
    @JvmField
    protected var keyword: String? = null

    private var currentPositionInTabList = 0

    @JvmField
    protected var mSearchDataContext: ISearchDataContext? = null

    override fun onLoadOk(data: T): LoadCompleteType? {
        val loadCompleteType = super.onLoadOk(data)
        isFirstLoadTab = false
        return loadCompleteType
    }

    fun setSearchDataContext(searchDateContext: ISearchDataContext?) {
        if (mSearchDataContext != searchDateContext) {
            mSearchDataContext = searchDateContext
        }
    }

    open fun onGetPageItem(position: Int) {
        currentPositionInTabList = position
    }

    fun isFirstTabInPageAdapter(): Boolean {
        return currentPositionInTabList == 0
    }
}