package com.ximalaya.ting.android.search.model;

import android.text.TextUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 搜索趣配音
 */
public class SearchDub {
    private long trackId;
    private long countPlay;
    private String coverPath;
    private String name;
    private String iting;
    private int dubCategoryId;
    private long duration;
    private long uid;
    private long templateId;
    private String pk;
    private String highLightTitle; //高亮主标题
    private String highLightTitle2; //高亮副标题
    private String abInfo;
    private String xmRequestId;

    public SearchDub(String json) {
        if (TextUtils.isEmpty(json)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(json);
            if (jsonObject.has("trackId")) {
                setTrackId(jsonObject.optLong("trackId"));
            }
            if (jsonObject.has("countPlay")) {
                setCountPlay(jsonObject.optLong("countPlay"));
            }
            if (jsonObject.has("coverPath")) {
                setCoverPath(jsonObject.optString("coverPath"));
            }
            if (jsonObject.has("name")) {
                setName(jsonObject.optString("name"));
            }
            if (jsonObject.has("iting")) {
                setIting(jsonObject.optString("iting"));
            }
            if (jsonObject.has("dubCategoryId")) {
                setDubCategoryId(jsonObject.optInt("dubCategoryId"));
            }
            if (jsonObject.has("duration")) {
                setDuration(jsonObject.optLong("duration"));
            }
            if (jsonObject.has("uid")) {
                setUid(jsonObject.optLong("uid"));
            }
            if (jsonObject.has("templateId")) {
                setTemplateId(jsonObject.optLong("templateId"));
            }
            if (jsonObject.has("pk")) {
                setPk(jsonObject.optString("pk"));
            }
            if (jsonObject.has("highLightTitle")) {
                setHighLightTitle(jsonObject.optString("highLightTitle"));
            }
            if (jsonObject.has("highLightTitle2")) {
                setHighLightTitle2(jsonObject.optString("highLightTitle2"));
            }
            if (jsonObject.has("abInfo")) {
                setAbInfo(jsonObject.optString("abInfo"));
            }
            if (jsonObject.has("xmRequestId")) {
                setXmRequestId(jsonObject.optString("xmRequestId"));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public String getXmRequestId() {
        return xmRequestId;
    }

    public void setXmRequestId(String xmRequestId) {
        this.xmRequestId = xmRequestId;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public long getTrackId() {
        return trackId;
    }

    public void setTrackId(long trackId) {
        this.trackId = trackId;
    }

    public long getCountPlay() {
        return countPlay;
    }

    public void setCountPlay(long countPlay) {
        this.countPlay = countPlay;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIting() {
        return iting;
    }

    public void setIting(String iting) {
        this.iting = iting;
    }

    public int getDubCategoryId() {
        return dubCategoryId;
    }

    public void setDubCategoryId(int dubCategoryId) {
        this.dubCategoryId = dubCategoryId;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(long templateId) {
        this.templateId = templateId;
    }

    public String getPk() {
        return pk;
    }

    public void setPk(String pk) {
        this.pk = pk;
    }

    public String getHighLightTitle() {
        return highLightTitle;
    }

    public void setHighLightTitle(String highLightTitle) {
        this.highLightTitle = highLightTitle;
    }

    public String getHighLightTitle2() {
        return highLightTitle2;
    }

    public void setHighLightTitle2(String highLightTitle2) {
        this.highLightTitle2 = highLightTitle2;
    }

    public String getAbInfo() {
        return abInfo;
    }

    public void setAbInfo(String abInfo) {
        this.abInfo = abInfo;
    }
}
