package com.ximalaya.ting.android.search.model

import org.json.JSONObject

/**
 * Created by WolfXu on 2024/6/17.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
data class SearchTag(
    var title: String? = null,
    var showAll: Boolean = true,
    var abInfo: String? = null,
    @Transient var initList: List<SearchItemModel>? = null, // 手动解析
    @Transient var extendList: MutableList<SearchItemModel>? = null, // 手动解析
    @Transient var totalList: ArrayList<SearchItemModel> = arrayListOf(),
    @Transient var xmRequestId: String? = null,
    @Transient var isSelected: Boolean = false
) {
    fun parseInitAndExtendList(jsonObject: JSONObject, xmRequestId: String) {
        val initListArray = jsonObject.optJSONArray("initList")
        if (initListArray != null && initListArray.length() > 0) {
            val itemList: MutableList<SearchItemModel> = ArrayList()
            for (i in 0 until initListArray.length()) {
                val initItemModel = SearchItemModel.parse(initListArray.optString(i), xmRequestId)
                if (initItemModel != null) {
                    initItemModel.indexOfList = i
                    initItemModel.xmRequestId = xmRequestId
                    itemList.add(initItemModel)
                }
            }
            initList = itemList
            totalList.addAll(itemList)
        }
        val extendListArray = jsonObject.optJSONArray("extendList")
        if (extendListArray != null && extendListArray.length() > 0) {
            val itemList: MutableList<SearchItemModel> = ArrayList()
            for (i in 0 until extendListArray.length()) {
                val extendItemModel =
                    SearchItemModel.parse(extendListArray.optString(i), xmRequestId)
                if (extendItemModel != null) {
                    extendItemModel.xmRequestId = xmRequestId
                    itemList.add(extendItemModel)
                }
            }
            extendList = itemList
        }
    }
}