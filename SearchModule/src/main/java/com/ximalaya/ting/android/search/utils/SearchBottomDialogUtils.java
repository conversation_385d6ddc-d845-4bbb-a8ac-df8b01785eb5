package com.ximalaya.ting.android.search.utils;

import android.app.Activity;
import android.text.TextUtils;
import android.widget.ImageView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.adapter.BaseBottonDialogAdapter;
import com.ximalaya.ting.android.host.data.model.message.RequestError;
import com.ximalaya.ting.android.host.download.ActionCallBack;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IBottomDialogItemClickListener;
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.account.HomePageModel;
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.anchor.Anchor;
import com.ximalaya.ting.android.host.model.dialog.BaseDialogModel;
import com.ximalaya.ting.android.host.model.dialog.ItemType;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.DownloadTools;
import com.ximalaya.ting.android.host.view.BaseBottomDialog;
import com.ximalaya.ting.android.main.downloadModule.quality.ChooseTrackQualityDialog;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.search.SearchConstants;
import com.ximalaya.ting.android.search.dialog.SearchMoreFuncDialogFragment;
import com.ximalaya.ting.android.search.model.SearchDub;
import com.ximalaya.ting.android.search.model.SearchVideo;
import com.ximalaya.ting.android.search.out.SearchRouterUtils;
import com.ximalaya.ting.android.search.page.sub.SearchAnchorFragment;
import com.ximalaya.ting.android.search.page.sub.SearchChosenFragment;
import com.ximalaya.ting.android.search.page.sub.SearchTrackPartFragment;
import com.ximalaya.ting.android.search.page.sub.SearchUserFragment;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;

/**
 * Created by ervin.li on 2019/1/14.
 * 搜索长按底部弹出框的处理工具
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SearchBottomDialogUtils {
    private static final String SHARE_ITEM_TITLE = "分享";
    private static final String FOLLOW_ITEM_TITLE = "关注";
    private static final String SIMILAR_ITEM_TITLE = "找相似";
    private static final String COLLECT_ALBUM_ITEM_TITLE = "订阅专辑";
    private static final String SEE_ALBUM_ITEM_TITLE = "查看专辑";
    private static final String DOWNLOAD_ITEM_TITLE = "下载";
    private static final String SEE_ANCHOR_TITLE = "查看主播";
    private static final String COLLECT_ALBUM_ITEM_TITLE_NEW = "订阅";
    private static final String COLLECTED_ALBUM_ITEM_TILTE = "已订阅";

    public static List<BaseDialogModel> getUserLongClickedBottomBaseDialogModel(BaseFragment2 fragment2) {
        List<BaseDialogModel> baseDialogModels = new ArrayList<>();
        int color = fragment2.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_333333_dcdcdc);
        baseDialogModels.add(new BaseDialogModel(ItemType.SHARE, SHARE_ITEM_TITLE, 0));
        baseDialogModels.add(new BaseDialogModel(ItemType.FOCUS, FOLLOW_ITEM_TITLE, 1));
        return baseDialogModels;
    }

    public static List<BaseDialogModel> getTrackLongClickedBottomBaseDialogModel(BaseFragment2 fragment2, Track track) {
        List<BaseDialogModel> baseDialogModels = new ArrayList<>();
        int color = fragment2.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_333333_dcdcdc);
        if (track.isHasCopyRight() && (!track.isPayTrack() || track.isAuthorized())) {
            baseDialogModels.add(new BaseDialogModel(ItemType.DOWNLOAD, DOWNLOAD_ITEM_TITLE, 0));
        }
        if (track.isIndependentTrack()) {
            baseDialogModels.add(new BaseDialogModel(ItemType.SHOW_ANCHOR, SEE_ANCHOR_TITLE, 1));
        } else {
            baseDialogModels.add(new BaseDialogModel(ItemType.SHOW_ALBUM, SEE_ALBUM_ITEM_TITLE, 1));
        }
        return baseDialogModels;
    }


    public static List<BaseDialogModel> getAlbumLongClickedBottomBaseDialogModel(BaseFragment2 fragment2, AlbumM albumM) {
        List<BaseDialogModel> baseDialogModels = new ArrayList<>();
        if (albumM.isFavorite()) {
            baseDialogModels.add(new BaseDialogModel(ItemType.FOLLOW_CANCEL, COLLECTED_ALBUM_ITEM_TILTE, 0));
        } else {
            baseDialogModels.add(new BaseDialogModel(ItemType.FOLLOW_NEW, COLLECT_ALBUM_ITEM_TITLE_NEW, 0));
        }
        if (!albumM.isPaid() || albumM.isAuthorized()) {
            baseDialogModels.add(new BaseDialogModel(ItemType.DOWNLOAD, DOWNLOAD_ITEM_TITLE, 1));
        }
        baseDialogModels.add(new BaseDialogModel(ItemType.FIND_SIMILAR, SIMILAR_ITEM_TITLE, 2));
        return baseDialogModels;
    }

    public static List<BaseDialogModel> getVideoLongClickedBottomBaseDialogModel(BaseFragment2 fragment2) {
        List<BaseDialogModel> baseDialogModels = new ArrayList<>();
        int color = fragment2.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_333333_dcdcdc);
        baseDialogModels.add(new BaseDialogModel(ItemType.SHOW_ALBUM, SEE_ALBUM_ITEM_TITLE, 0));
        baseDialogModels.add(new BaseDialogModel(ItemType.SHARE, SHARE_ITEM_TITLE, 1));
        return baseDialogModels;
    }

    public static List<BaseDialogModel> getDubLongClickedBottomBaseDialogModel(BaseFragment2 fragment2, SearchDub dub) {
        List<BaseDialogModel> baseDialogModels = new ArrayList<>();
        int color = fragment2.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_333333_dcdcdc);
        baseDialogModels.add(new BaseDialogModel(ItemType.SHARE, SHARE_ITEM_TITLE, 1));
        if (dub.getUid() > 0) {
            baseDialogModels.add(new BaseDialogModel(ItemType.FOCUS, FOLLOW_ITEM_TITLE, 2));
        }
        return baseDialogModels;
    }

    public static void onTrackBottomDialogItemClicked(BaseFragment2 fragment, Track info, BaseDialogModel baseDialogModel) {
        boolean paramsAllow = fragment == null || fragment.getActivity() == null
                || info == null || info.getAlbum() == null || baseDialogModel == null
                || TextUtils.isEmpty(baseDialogModel.title) || info.getAnnouncer() == null;
        if (paramsAllow)
            return;
        String currPage;
        String tabName;
        if (fragment instanceof SearchTrackPartFragment) {
            currPage = "searchTrackPart";
            tabName = "片段";
        } else {
            currPage = "searchTrack";
            tabName = "声音";
            SearchTraceUtils.traceWithSearchPageClick("searchResult", "searchTrack", "声音条", "button", baseDialogModel.title, new AbstractMap.SimpleEntry<>("trackId", String.valueOf(info.getDataId())), new AbstractMap.SimpleEntry<>("id", String.valueOf(5408)));
        }
        new XMTraceApi.Trace()
                .setMetaId(17543)
                .setServiceId("dialogClick")
                .put("currPage", currPage)
                .put("tabName", tabName)
                .put("searchWord", SearchTraceUtils.getKeyWord())
                .put("trackId", String.valueOf(info.getDataId()))
                .put("item_name", baseDialogModel.title)
                .createTrace();
        if (TextUtils.equals(baseDialogModel.title, DOWNLOAD_ITEM_TITLE)) {
            downloadSingleTrack(fragment, info);
        } else if (TextUtils.equals(baseDialogModel.title, SEE_ALBUM_ITEM_TITLE)) {
            seeAlbumDetails(info.getAlbum().getAlbumId(), fragment);
        } else if (TextUtils.equals(baseDialogModel.title, SEE_ANCHOR_TITLE)) {
            seeAnchorDetails(info.getAnnouncer().getAnnouncerId(), fragment);
        }
    }

    private static void downloadSingleTrack(BaseFragment2 fragment2, Track track) {
        if (RouteServiceUtil.getDownloadService().isDownloaded(track)) {
            CustomToast.showFailToast("已下载");
            return;
        }
        if (RouteServiceUtil.getDownloadService().isTrackQualitySettingActive()) {
            DownloadTools.downloadSound(fragment2, track, ConstantsOpenSdk.PLAY_FROM_NONE);
        } else {
            ChooseTrackQualityDialog.newInstance(fragment2.getContext(), track, new ActionCallBack() {
                @Override
                public void onConfirm() {
                    DownloadTools.downloadSound(fragment2, track, ConstantsOpenSdk.PLAY_FROM_NONE);
                }

                @Override
                public void onCancel() {

                }
            }).show();
        }
    }

    private static void onDubBottomDialogItemClicked(BaseFragment2 fragment, SearchDub dub, BaseDialogModel baseDialogModel) {
        boolean paramsAllow = fragment == null || fragment.getActivity() == null || dub == null || baseDialogModel == null || TextUtils.isEmpty(baseDialogModel.title);
        if (paramsAllow)
            return;
        new XMTraceApi.Trace()
                .setMetaId(17543)
                .setServiceId("dialogClick")
                .put("currPage", "searchDub")
                .put("tabName", "趣配音")
                .put("searchWord", SearchTraceUtils.getKeyWord())
                .put("trackId", String.valueOf(dub.getTrackId()))
                .put("item_name", baseDialogModel.title)
                .createTrace();
        SearchTraceUtils.traceWithSearchPageClick("searchResult", "searchDub", "趣配音", "button", baseDialogModel.title, new AbstractMap.SimpleEntry<>("dubId", String.valueOf(dub.getTrackId())), new AbstractMap.SimpleEntry<>("id", String.valueOf(6447)));
        if (TextUtils.equals(baseDialogModel.title, SHARE_ITEM_TITLE)) {
            TrackM track = new TrackM();
            track.setDataId(dub.getTrackId());
            track.setTrackTitle(dub.getName());
            track.setCoverUrlMiddle(dub.getCoverPath());
            shareDub(fragment, track);
        } else if (TextUtils.equals(baseDialogModel.title, FOLLOW_ITEM_TITLE)) {
            Anchor anchor = new Anchor();
            anchor.setUid(dub.getUid());
            anchor.setFollowed(false);
            follow(fragment, anchor);
        }
    }

    public static void onVideoBottomDialogItemClicked(BaseFragment2 fragment, SearchVideo video, BaseDialogModel baseDialogModel) {
        boolean paramsAllow = fragment == null || fragment.getActivity() == null || video == null || baseDialogModel == null || TextUtils.isEmpty(baseDialogModel.title);
        if (paramsAllow)
            return;
        SearchTraceUtils.traceWithSearchPageClick("searchResult", "searchVideo", "视频条", "button", baseDialogModel.title, new AbstractMap.SimpleEntry<>("trackId", String.valueOf(video.getTrackId())), new AbstractMap.SimpleEntry<>("id", String.valueOf(6445)));
        if (TextUtils.equals(baseDialogModel.title, SEE_ALBUM_ITEM_TITLE)) {
            seeAlbumDetails(video.getAlbumId(), fragment);
        } else if (TextUtils.equals(baseDialogModel.title, SHARE_ITEM_TITLE)) {
            TrackM track = new TrackM();
            track.setDataId(video.getTrackId());
            track.setTrackTitle(video.getName());
            shareVideo(fragment, track);
        } else if (TextUtils.equals(baseDialogModel.title, FOLLOW_ITEM_TITLE)) {
            Anchor anchor = new Anchor();
            anchor.setUid(video.getUid());
            anchor.setFollowed(false);
            follow(fragment, anchor);
        }
    }

    public static void onUserBottomDialogItemClicked(BaseFragment2 fragment, Anchor anchor, BaseDialogModel baseDialogModel) {
        if (fragment == null || baseDialogModel == null || anchor == null || TextUtils.isEmpty(baseDialogModel.title))
            return;
        String currPage;
        String tabName;
        if (fragment instanceof SearchUserFragment) {
            currPage = "searchUser2";
            tabName = "用户";
            SearchTraceUtils.traceWithSearchPageClick("searchResult", "searchCustomer", "用户条", "button", baseDialogModel.title, new AbstractMap.SimpleEntry<>("userId", String.valueOf(anchor.getUid())), new AbstractMap.SimpleEntry<>("id", String.valueOf(5416)));
        } else {
            currPage = "searchUser1";
            tabName = "主播";
            SearchTraceUtils.traceWithSearchPageClick("searchResult", "searchUser", "主播条", "button", baseDialogModel.title, new AbstractMap.SimpleEntry<>("anchorId", String.valueOf(anchor.getUid())), new AbstractMap.SimpleEntry<>("id", String.valueOf(5412)));
        }
        new XMTraceApi.Trace()
                .setMetaId(17543)
                .setServiceId("dialogClick")
                .put("currPage", currPage)
                .put("tabName", tabName)
                .put("searchWord", SearchTraceUtils.getKeyWord())
                .put("anchorId", String.valueOf(anchor.getUid()))
                .put("item_name", baseDialogModel.title)
                .createTrace();
        if (TextUtils.equals(baseDialogModel.title, FOLLOW_ITEM_TITLE)) {
            follow(fragment, anchor);
        } else if (TextUtils.equals(baseDialogModel.title, SHARE_ITEM_TITLE)) {
            shareUser(fragment.getActivity(), anchor);
        }
    }

    public static void onAlbumBottomDialogItemClicked(BaseFragment2 baseFragment2, AlbumM albumM, BaseDialogModel baseDialogModel, ICollectWithFollowStatusCallback iCollectWithFollowStatusCallback) {
        if (baseFragment2 == null || baseDialogModel == null || TextUtils.isEmpty(baseDialogModel.title))
            return;
        String itemId = TextUtils.equals(baseDialogModel.title, COLLECT_ALBUM_ITEM_TITLE) ? "订阅" : baseDialogModel.title;
        String currPage;
        String tabName;
        currPage = "searchAlbum";
        tabName = "专辑";
        SearchTraceUtils.traceWithSearchPageClick("searchResult", "searchAlbum", "专辑条", "button", itemId, new AbstractMap.SimpleEntry<>("albumId", String.valueOf(albumM.getId())), new AbstractMap.SimpleEntry<>("id", String.valueOf(5357)));
        new XMTraceApi.Trace()
                .setMetaId(17543)
                .setServiceId("dialogClick")
                .put("currPage", currPage)
                .put("tabName", tabName)
                .put("searchWord", SearchTraceUtils.getKeyWord())
                .put("albumId", String.valueOf(albumM.getId()))
                .put("item_name", baseDialogModel.title)
                .createTrace();
        if (TextUtils.equals(baseDialogModel.title, COLLECT_ALBUM_ITEM_TITLE) || baseDialogModel.type == ItemType.FOLLOW_NEW) {
            doCollect(baseFragment2, albumM, iCollectWithFollowStatusCallback);
        } else if (baseDialogModel.type == ItemType.FOLLOW_CANCEL) {
            doUnCollect(baseFragment2, albumM, iCollectWithFollowStatusCallback);
        } else if (TextUtils.equals(baseDialogModel.title, DOWNLOAD_ITEM_TITLE)) {
            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(baseFragment2.getContext());
                return;
            }
            try {
                baseFragment2.startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).
                        getFragmentAction().newBatchDownloadFragment(albumM.getId(), albumM.isPaid()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (TextUtils.equals(baseDialogModel.title, SIMILAR_ITEM_TITLE)) {
            try {
                baseFragment2.startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN)
                        .getFragmentAction().newSimilarRecommendFragment(albumM.getId(), "相似推荐"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static void doCollect(final BaseFragment fragment, final AlbumM data, ICollectWithFollowStatusCallback iCollectWithFollowStatusCallback) {
        if (fragment == null || data == null) return;
//        boolean isSubscribe;
//        if (UserInfoMannage.hasLogined()) {
//            isSubscribe = data.isFavorite();
//        } else {
//            isSubscribe = false;
//        }
//        if (isSubscribe) {
//            CustomToast.showFailToast(R.string.search_search_fail_collect_album);
//            return;
//        }
        // 设置为false，如果已经订阅接口会自动处理，解决订阅后，在别的地方取消订阅，回来无法订阅的问题
        data.setFavorite(false);

        if (fragment instanceof BaseFragment2) {
            String currPage = SearchConstants.TRACE_PAGE_CHOSEN;
            if (fragment instanceof SearchChosenFragment) {
            } else {
                currPage = SearchConstants.TRACE_PAGE_ALBUM;
            }
            AlbumCollectParam albumCollectParam = new AlbumCollectParam(
                    currPage,
                    SearchConstants.SEARCH_LONG_CLICK_SUBSCRIBE_BIZTYPE,
                    AnchorFollowManage.FOLLOW_BIZ_TYPE_SEARCH_TOP_USER,
                    SearchConstants.SEARCH_SUBBIZTYPE_ANCHOR_LONG_CLICK,
                    data,
                    true);
            albumCollectParam.setShowFollowDialog(false);
            AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(albumCollectParam, (BaseFragment2) fragment, new ICollectWithFollowStatusCallback() {
                @Override
                public void followDialogAction(int status) {
                }

                @Override
                public void onCollectSuccess(int code, boolean isCollected) {
                    if (iCollectWithFollowStatusCallback != null){
                        iCollectWithFollowStatusCallback.onCollectSuccess(code,isCollected);
                    } else {
                        if (!fragment.canUpdateUi()) return;
                        if (code == 0) {
                            data.setFavorite(isCollected);
                        } else  if (code == RequestError.CODE_ALBUM_ALREADY_SUBSCRIBE) {
                            CustomToast.showToast("亲，您已经订阅过该专辑了哦");
                        }
                    }
                }
                @Override
                public void onError() {
                    if (iCollectWithFollowStatusCallback != null){
                        iCollectWithFollowStatusCallback.onError();
                    }
                }
            }, "searchResult");

//            AlbumEventManage.doCollectActionV3(albumCollectParam, (BaseFragment2) fragment, new ICollectWithFollowStatusCallback() {
//                @Override
//                public void followDialogAction(int status) {
//                }
//
//                @Override
//                public void onCollectSuccess(int code, boolean isCollected) {
//                    if (iCollectWithFollowStatusCallback != null){
//                        iCollectWithFollowStatusCallback.onCollectSuccess(code,isCollected);
//                    } else {
//                        if (!fragment.canUpdateUi()) return;
//                        if (code == 0) {
//                            data.setFavorite(isCollected);
//                            CustomToast.showSuccessToast(isCollected ? R.string.host_collect_success : R.string.host_cancel_collect_success);
//                        }
//                    }
//                }
//                @Override
//                public void onError() {
//                    if (iCollectWithFollowStatusCallback != null){
//                        iCollectWithFollowStatusCallback.onError();
//                    }
//                }
//            });
        }
    }

    private static void doUnCollect(final BaseFragment fragment, final AlbumM data, ICollectWithFollowStatusCallback iCollectWithFollowStatusCallback) {
        if (fragment == null || data == null) return;
        data.setFavorite(true);

        if (fragment instanceof BaseFragment2) {
            String currPage = SearchConstants.TRACE_PAGE_CHOSEN;
            if (fragment instanceof SearchChosenFragment) {
            } else {
                currPage = SearchConstants.TRACE_PAGE_ALBUM;
            }
            AlbumCollectParam albumCollectParam = new AlbumCollectParam(
                    currPage,
                    SearchConstants.SEARCH_LONG_CLICK_SUBSCRIBE_BIZTYPE,
                    AnchorFollowManage.FOLLOW_BIZ_TYPE_SEARCH_TOP_USER,
                    SearchConstants.SEARCH_SUBBIZTYPE_ANCHOR_LONG_CLICK,
                    data,
                    true);
            albumCollectParam.setShowFollowDialog(false);
            AlbumEventManage.doCollectActionV3WithLoginCheckDeferredAction(albumCollectParam, (BaseFragment2) fragment, new ICollectWithFollowStatusCallback() {
                @Override
                public void followDialogAction(int status) {
                }

                @Override
                public void onCollectSuccess(int code, boolean isCollected) {
                    if (iCollectWithFollowStatusCallback != null){
                        iCollectWithFollowStatusCallback.onCollectSuccess(code,isCollected);
                    } else {
                        if (!fragment.canUpdateUi()) return;
                        if (code == 0) {
                            data.setFavorite(isCollected);
                        } else  if (code == RequestError.CODE_ALBUM_ALREADY_SUBSCRIBE) {
                            CustomToast.showToast("亲，您还没有订阅过该专辑哦");
                        }
                    }
                }
                @Override
                public void onError() {
                    if (iCollectWithFollowStatusCallback != null){
                        iCollectWithFollowStatusCallback.onError();
                    }
                }
            }, "searchResult");
        }
    }


    private static void seeAlbumDetails(long albumId, BaseFragment2 fragment) {
        if (fragment == null || fragment.getActivity() == null) return;
        AlbumEventManage.startMatchAlbumFragment(albumId, AlbumEventManage.FROM_SEARCH,
                ConstantsOpenSdk.PLAY_FROM_SEARCH, null, null, -1, fragment.getActivity());
    }

    private static void seeAnchorDetails(long announcerId, BaseFragment2 fragment) {
        if (fragment == null || announcerId <= 0) return;
        fragment.startFragment(SearchRouterUtils.newAnchorSpaceFragment(
                announcerId, ConstantsOpenSdk.PLAY_FROM_SEARCH), null);
    }

    private static void follow(final BaseFragment2 fragment, final Anchor anchor) {
        String className = fragment.getClass().getSimpleName();
        int subBizType = SearchConstants.SEARCH_SUBBIZTYPE_ANCHOR_LONG_CLICK;
        if (!TextUtils.isEmpty(className) && className.contains("SearchUserFragment")) {
            subBizType = SearchConstants.SEARCH_SUBBIZTYPE_USER;
        }
        AnchorFollowManage.srcPage = "search";
        boolean isFollowed = anchor.isFollowed() || anchor.getFollowingStatus() == 1 || anchor.getFollowingStatus() == 2;
        AnchorFollowManage.followV3(fragment.getActivity(), anchor.getUid(), false,
                AnchorFollowManage.FOLLOW_BIZ_TYPE_SEARCH, subBizType, new IDataCallBack<Boolean>() {
                    @Override
                    public void onSuccess(@Nullable Boolean object) {
                        if (!fragment.canUpdateUi()) return;
                        if (object != null) {
                            if (object && !isFollowed) {
                                CustomToast.showSuccessToast("关注成功");
                            }
                            anchor.setFollowed(object);
                            anchor.setFollowingStatus(object ? 1 : 3);
                            anchor.setNeedRefreshFollowStatus(true);
                            if (fragment instanceof SearchAnchorFragment) {
                                ((SearchAnchorFragment) fragment).refreshFollowStatus();
                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                    }
                }, true);
    }

    private static void shareTrack(BaseFragment2 fragment2, Track trackM) {
        if (fragment2 != null && fragment2.getActivity() != null && trackM != null) {
            SearchRouterUtils.shareTrack(fragment2.getActivity(), trackM, ICustomShareContentType.SHARE_TYPE_TRACK);
        }
    }

    private static void shareVideo(BaseFragment2 fragment2, Track trackM) {
        if (fragment2 != null && fragment2.getActivity() != null && trackM != null) {
            SearchRouterUtils.shareVideo(fragment2.getActivity(), trackM);
        }
    }

    private static void shareDub(BaseFragment2 fragment2, Track trackM) {
        if (fragment2 != null && fragment2.getActivity() != null && trackM != null) {
            SearchRouterUtils.shareDub(fragment2.getActivity(), trackM);
        }
    }

    private static void shareUser(Activity activity, Anchor anchor) {
        if (activity == null || anchor == null) return;
        ShareWrapContentModel contentModel = new ShareWrapContentModel(ICustomShareContentType.SHARE_TYPE_USER);
        HomePageModel homePageModel = new HomePageModel();
        homePageModel.setNickname(anchor.getNickName());
        homePageModel.setUid(anchor.getUid());
        contentModel.uid = anchor.getUid();
        contentModel.homeModel = homePageModel;
        new ShareManager(activity, contentModel).showShareDialog();
    }

    public static List<BaseDialogModel> getBottomBaseDialogModel(Object itemLongClickedData, BaseFragment2 fragment2) {
        if (itemLongClickedData == null || fragment2 == null) return null;
        if (itemLongClickedData instanceof AlbumM) {
            return SearchBottomDialogUtils.getAlbumLongClickedBottomBaseDialogModel(fragment2, (AlbumM) itemLongClickedData);
        } else if (itemLongClickedData instanceof Track) {
            return SearchBottomDialogUtils.getTrackLongClickedBottomBaseDialogModel(fragment2, (Track) itemLongClickedData);
        } else if (itemLongClickedData instanceof Anchor) {
            return SearchBottomDialogUtils.getUserLongClickedBottomBaseDialogModel(fragment2);
        } else if (itemLongClickedData instanceof SearchVideo) {
            return SearchBottomDialogUtils.getVideoLongClickedBottomBaseDialogModel(fragment2);
        } else if (itemLongClickedData instanceof SearchDub) {
            return SearchBottomDialogUtils.getDubLongClickedBottomBaseDialogModel(fragment2, (SearchDub) itemLongClickedData);
        }
        return null;
    }

    public static void onBottomDialogItemClicked(BaseFragment2 baseFragment2, Object itemLongClickedData, BaseDialogModel baseDialogModel, ICollectWithFollowStatusCallback iCollectWithFollowStatusCallback) {
        if (itemLongClickedData == null) return;
        if (itemLongClickedData instanceof AlbumM) {
            onAlbumBottomDialogItemClicked(baseFragment2, (AlbumM) itemLongClickedData, baseDialogModel, iCollectWithFollowStatusCallback);
        } else if (itemLongClickedData instanceof Track) {
            onTrackBottomDialogItemClicked(baseFragment2, (Track) itemLongClickedData, baseDialogModel);
        } else if (itemLongClickedData instanceof Anchor) {
            onUserBottomDialogItemClicked(baseFragment2, (Anchor) itemLongClickedData, baseDialogModel);
        } else if (itemLongClickedData instanceof SearchVideo) {
            onVideoBottomDialogItemClicked(baseFragment2, (SearchVideo) itemLongClickedData, baseDialogModel);
        } else if (itemLongClickedData instanceof SearchDub) {
            onDubBottomDialogItemClicked(baseFragment2, (SearchDub) itemLongClickedData, baseDialogModel);
        }
    }

    public static void showBottomDialog(final Object itemLongClickedData, final BaseFragment2 fragment2) {
        showBottomDialog(itemLongClickedData, fragment2, null);
    }

    public static void showBottomDialog(final Object itemLongClickedData, final BaseFragment2 fragment2, final ICollectWithFollowStatusCallback iCollectWithFollowStatusCallback) {
        final List<BaseDialogModel> models = getBottomBaseDialogModel(itemLongClickedData, fragment2);
        boolean showNewDialog = (itemLongClickedData instanceof Track || itemLongClickedData instanceof AlbumM);
        if (fragment2 == null || ToolUtil.isEmptyCollects(models))
            return;
        Activity activity = fragment2.getActivity();
        if (activity == null) {
            activity = BaseApplication.getTopActivity();
        }
        if (activity == null || activity.isFinishing()) return;
        if (itemLongClickedData instanceof Track) {
            String currPage;
            String tabName;
            if (fragment2 instanceof SearchTrackPartFragment) {
                currPage = "searchTrackPart";
                tabName = "片段";
            } else {
                currPage = "searchTrack";
                tabName = "声音";
                SearchTraceUtils.traceSearchResultLongPress("searchTrack", "声音条", "track", String.valueOf(((Track) itemLongClickedData).getDataId()), String.valueOf(5407));
            }
            new XMTraceApi.Trace()
                    .setMetaId(16931)
                    .setServiceId("dialogView")
                    .put("currPage", currPage)
                    .put("searchWord", SearchTraceUtils.getKeyWord())
                    .put("trackId", String.valueOf(((Track) itemLongClickedData).getDataId()))
                    .put("tabName", tabName)
                    .createTrace();
        } else if (itemLongClickedData instanceof Anchor) {
            String currPage;
            String tabName;
            if (fragment2 instanceof SearchUserFragment) {
                currPage = "searchUser2";
                tabName = "用户";
                SearchTraceUtils.traceSearchResultLongPress("searchCustomer", "用户条", "user", String.valueOf(((Anchor) itemLongClickedData).getUid()), String.valueOf(5415));
            } else {
                currPage = "searchUser1";
                tabName = "主播";
                SearchTraceUtils.traceSearchResultLongPress("searchUser", "主播条", "anchor", String.valueOf(((Anchor) itemLongClickedData).getUid()), String.valueOf(5411));
            }
            new XMTraceApi.Trace()
                    .setMetaId(16931)
                    .setServiceId("dialogView")
                    .put("currPage", currPage)
                    .put("searchWord", SearchTraceUtils.getKeyWord())
                    .put("anchorId", String.valueOf(((Anchor) itemLongClickedData).getUid()))
                    .put("tabName", tabName)
                    .createTrace();
        } else if (itemLongClickedData instanceof AlbumM) {
            String currPage;
            String tabName;
            currPage = "searchAlbum";
            tabName = "专辑";
            SearchTraceUtils.traceSearchResultLongPress("searchAlbum", "专辑条", "album", String.valueOf(((AlbumM) itemLongClickedData).getId()), String.valueOf(5356));
            new XMTraceApi.Trace()
                    .setMetaId(16931)
                    .setServiceId("dialogView")
                    .put("currPage", currPage)
                    .put("searchWord", SearchTraceUtils.getKeyWord())
                    .put("albumId", String.valueOf(((AlbumM) itemLongClickedData).getId()))
                    .put("tabName", tabName)
                    .createTrace();
        } else if (itemLongClickedData instanceof SearchDub) {
            SearchTraceUtils.traceSearchResultLongPress("searchDub", "趣配音", "dub", String.valueOf(((SearchDub) itemLongClickedData).getTrackId()), String.valueOf(6446));
            new XMTraceApi.Trace()
                    .setMetaId(16931)
                    .setServiceId("dialogView")
                    .put("currPage", "searchDub")
                    .put("searchWord", SearchTraceUtils.getKeyWord())
                    .put("trackId", String.valueOf(((SearchDub) itemLongClickedData).getTrackId()))
                    .put("tabName", "趣配音")
                    .createTrace();
        } else if (itemLongClickedData instanceof SearchVideo) {
            SearchTraceUtils.traceSearchResultLongPress("searchVideo", "视频条", "trackVideo", String.valueOf(((SearchVideo) itemLongClickedData).getTrackId()), String.valueOf(6444));
        }
        if (showNewDialog && fragment2 != null) {
            SearchMoreFuncDialogFragment dialogFragment = SearchMoreFuncDialogFragment.newInstance(itemLongClickedData, models, (dialog, model) -> {
                dialog.dismiss();
                if (model == null) {
                    return;
                }
                onBottomDialogItemClicked(fragment2, itemLongClickedData, model, iCollectWithFollowStatusCallback);
            });
            dialogFragment.show(fragment2.getChildFragmentManager(), null);
            if (BaseApplication.getMainActivity() != null) {
                try {
                    SystemServiceManager.setVibrator(BaseApplication.getMainActivity(), 50);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            BaseBottonDialogAdapter bottonDialogAdapter = new BaseBottonDialogAdapter(activity, models) {
                @Override
                public void bindExtraView(BaseViewHolder holder, BaseDialogModel baseDialogModel, int position) {

                }
            };
            bottonDialogAdapter.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            BaseBottomDialog bottomDialog = new BaseBottomDialog(activity, models, new IBottomDialogItemClickListener() {
                @Override
                public void onItemClick(BaseBottomDialog dialog, BaseDialogModel model) {
                    dialog.dismiss();
                    if (model == null) {
                        return;
                    }
                    onBottomDialogItemClicked(fragment2, itemLongClickedData, model, iCollectWithFollowStatusCallback);
                }
            });
            bottomDialog.show();
        }
    }
}
