package com.ximalaya.ting.android.search.utils

import com.ximalaya.ting.android.configurecenter.ConfigureCenter

object SearchRecordIconAnimalUtil {

    private var isFixAnimal: Boolean? = null

    fun isAllowFixAnimal(): Boolean {
        if (isFixAnimal != null) {
            return isFixAnimal!!
        }

        isFixAnimal = ConfigureCenter.getInstance().getBool(
            "toc", "key_fix_search_record_icon_animal", true
        )
        return isFixAnimal!!
    }
}