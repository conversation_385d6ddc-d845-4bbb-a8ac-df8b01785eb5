package com.ximalaya.ting.android.search.view

import android.content.Context
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.Typeface
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.PopupWindow
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.view.layout.FlowLayout
import com.ximalaya.ting.android.search.R
import com.ximalaya.ting.android.search.SearchConstants
import com.ximalaya.ting.android.search.model.MainLabel
import com.ximalaya.ting.android.search.model.SearchLabel
import com.ximalaya.ting.android.search.model.SearchLabelData
import com.ximalaya.ting.android.search.model.SearchPaidFilterData
import com.ximalaya.ting.android.search.model.SearchSortFilterData
import com.ximalaya.ting.android.search.utils.SearchTraceUtils
import com.ximalaya.ting.android.search.utils.SearchUiUtils
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

class SearchNormalFilterPopupWindow(private val context: Context,
                                    private val searchLabels: MutableList<SearchLabel>? = null,
                                    private val mainLabel: MainLabel? = null,
                                    private var mQueryLimit: Int = 3,
                                    private val labelList: List<SearchLabelData>?,
                                    private val sortFilterDataList: List<SearchSortFilterData>?,
                                    private val paidFilterDataList: List<SearchPaidFilterData>?,
                                    private val listener: IOnItemClickListener?,
                                    private val core: String) : PopupWindow() {

    private lateinit var mFlLabel: FlowLayout
    private lateinit var mSearchFlLabel: FlowLayout
    private lateinit var mTvSortFilter: TextView
    private lateinit var mRgSortFilter: RadioGroup
    private lateinit var mTvPaidFilter: TextView
    private lateinit var mRgPaidFilter: RadioGroup
    private lateinit var mTvReset: TextView
    private lateinit var mTvConfirm: TextView
    private lateinit var mOutView: View
    private var mVgContainer: ViewGroup? = null

    private var mLastSelectedPosition = -1
    private var mLastSelectedSortFilterItemPos = -1
    private var mLastSelectedPaidFilterItemPos = -1

    private var mNeedRefreshSortFilterItem = true
    private var mNeedRefreshPaidFilterItem = true

    init {
        initView()
    }

    private fun initView() {
        width = WindowManager.LayoutParams.MATCH_PARENT
        //height = WindowManager.LayoutParams.WRAP_CONTENT
        height = BaseUtil.getScreenHeight(context) - BaseUtil.dp2px(context,45f + 44f) - BaseUtil.getStatusBarHeight(context)

        val layoutId = R.layout.search_layout_normal_filter_2024
        contentView = LayoutInflater.from(context).inflate(layoutId, null, false)
        mFlLabel = contentView.findViewById(R.id.search_fl_label)
        mSearchFlLabel = contentView.findViewById(R.id.search_fl_search_label)
        mTvSortFilter = contentView.findViewById(R.id.search_tv_comprehensive_sort)
        mRgSortFilter = contentView.findViewById(R.id.search_rg_sort_filter)
        mTvPaidFilter = contentView.findViewById(R.id.search_tv_paid)
        mRgPaidFilter = contentView.findViewById(R.id.search_rg_paid_filter)
        mTvReset = contentView.findViewById(R.id.search_tv_reset)
        mTvConfirm = contentView.findViewById(R.id.search_tv_confirm)
        mOutView = contentView.findViewById(R.id.search_view_out)
        mVgContainer = contentView.findViewById(R.id.search_vg_container)

        mOutView.setOnClickListener {
            dismiss()
        }
        mVgContainer?.setBackgroundResource(R.drawable.search_normal_filter_bg_2024)

        //区分泛搜索的标签
        if (searchLabels.isNullOrEmpty()) {
            SearchUiUtils.setVisible(View.GONE, mSearchFlLabel)
        } else {
            SearchUiUtils.setVisible(View.VISIBLE, mSearchFlLabel)
            for (index in searchLabels.indices) {
                val lp = FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT, 28.dp)
                lp.bottomMargin = 8.dp
                lp.rightMargin = 8.dp
                val labelData = searchLabels[index]
                val labelTv = buildSearchLabelView(labelData, index)
                mSearchFlLabel.addView(labelTv, lp)
            }
        }

        if (labelList.isNullOrEmpty()) {
            SearchUiUtils.setVisible(View.GONE, mFlLabel)
        } else {
            SearchUiUtils.setVisible(View.VISIBLE, mFlLabel)
            for (index in labelList.indices) {
                val lp = FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT, 28.dp)
                lp.bottomMargin = 8.dp
                lp.rightMargin = 8.dp
                val labelData = labelList[index]
                val labelTv = buildLabelView(labelData, index)
                mFlLabel.addView(labelTv, lp)
            }
        }

        if (sortFilterDataList.isNullOrEmpty()) {
            SearchUiUtils.setVisible(View.GONE, mTvSortFilter, mRgSortFilter)
        } else {
            SearchUiUtils.setVisible(View.VISIBLE, mTvSortFilter, mRgSortFilter)
            val sortFilterButtons = initFilterRadioButton(mRgSortFilter)
            setSortFilterRadioButtonText(sortFilterDataList, sortFilterButtons)
        }
        if (searchLabels.isNullOrEmpty() && labelList.isNullOrEmpty() && sortFilterDataList.isNullOrEmpty().not()) {
            SearchUiUtils.setVisible(View.GONE, mTvSortFilter)
            SearchUiUtils.setMargin(mTvSortFilter, 0, 1)
            SearchUiUtils.setPadding(mVgContainer, 16.dp, 0, 16.dp, 0)
        }

        if (paidFilterDataList.isNullOrEmpty()) {
            SearchUiUtils.setVisible(View.GONE, mTvPaidFilter, mRgPaidFilter)
        } else {
            SearchUiUtils.setVisible(View.VISIBLE, mTvPaidFilter, mRgPaidFilter)
            val paidFilterButtons = initFilterRadioButton(mRgPaidFilter)
            setPaidFilterRadioButtonText(paidFilterDataList, paidFilterButtons)
        }

        mTvReset.setOnClickListener { v ->
            if (OneClickHelper.getInstance().onClick(v)) {
                updateSelectedSortFilterItem(0)
                if (mRgPaidFilter.visibility == View.VISIBLE &&
                        mRgPaidFilter.checkedRadioButtonId != R.id.search_rb_all) {
                    mNeedRefreshPaidFilterItem = false
                    mRgPaidFilter.check(R.id.search_rb_all)
                }
                updateSelectedLabelItem(0)
                listener?.onResetClick()
            }
        }

        mTvConfirm.setOnClickListener { v ->
            if (OneClickHelper.getInstance().onClick(v)) {
                dismiss()
            }
        }
        updateResetBtnStatus()

        isFocusable = true
        isOutsideTouchable = false
        animationStyle = com.ximalaya.ting.android.host.R.style.host_popup_window_animation
        setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        setOnDismissListener {
            listener?.onDismiss()
        }
    }

    fun updateResetBtnStatus() {
        mTvReset.isEnabled = (mFlLabel.visibility == View.VISIBLE && mLastSelectedPosition != 0)
                || (mRgSortFilter.visibility == View.VISIBLE && mLastSelectedSortFilterItemPos != 0)
                || (mRgPaidFilter.visibility == View.VISIBLE && mLastSelectedPaidFilterItemPos != 0)
                || hasSearchLabelSelected()
    }

    private fun hasSearchLabelSelected(): Boolean{
        return mSearchFlLabel.visibility == View.VISIBLE
                && (searchLabels?.count { searchLabel -> searchLabel.status == 1 } ?: 0) > 0
    }

    private fun buildLabelView(labelData: SearchLabelData, position: Int): TextView {
        val tv = TextView(context)
        tv.text = labelData.labelName
        tv.setTextColor(context.resources.getColorStateList(R.color.search_label_color_selector_2024))
        tv.setBackgroundResource(R.drawable.search_label_background_selector_2024)
        tv.textSize = 13f
        tv.setSingleLine()
        tv.gravity = Gravity.CENTER
        tv.setPadding(16.dp, 0, 16.dp, 0)
        tv.includeFontPadding = false
        tv.setOnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            updateSelectedLabelItem(position)
            listener?.onLabelItemClick(labelData, position)
            traceItemClick(labelData.labelName, position)
        }
        if (labelData.isSelected) {
            mLastSelectedPosition = position
            setTypeFace(tv, true)
        } else {
            setTypeFace(tv, false)
        }
        return tv
    }

    private fun buildSearchLabelView(labelData: SearchLabel, position: Int): TextView {
        val tv = TextView(context)
        tv.text = labelData.showName
        tv.setTextColor(context.resources.getColorStateList(R.color.search_label_color_selector_2024))
        tv.compoundDrawablePadding = BaseUtil.dp2px(context, 4f)
        tv.setBackgroundResource(R.drawable.search_label_background_selector_2024)
        tv.textSize = 13f
        tv.setSingleLine()
        tv.gravity = Gravity.CENTER
        tv.setPadding(16.dp, 0, 12.dp, 0)
        tv.includeFontPadding = false
        tv.setOnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            traceSearchItemClick(labelData.showName, position, labelData.status == 1)
            val selectCount = searchLabels?.count { searchLabel -> searchLabel.status == 1 } ?: 0
            if (selectCount < mQueryLimit || labelData.status == 1) {
                labelData.status = if (labelData.status == 1) 0 else 1
                updateSelectedSearchLabelItem(position)
                listener?.onSearchLabelItemClick(searchLabels, mainLabel)
            } else {
                CustomToast.showToast("最多选择${mQueryLimit}个标签")
            }
        }
        if (labelData.status == 1) {
            setSearchTypeFace(tv, true)
        } else {
            setSearchTypeFace(tv, false)
        }
        return tv
    }

    private fun setTypeFace(labelTv: TextView, isSelected: Boolean) {
        if (isSelected) {
            labelTv.isSelected = true
            labelTv.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
        } else {
            labelTv.isSelected = false
            labelTv.typeface = Typeface.create("", Typeface.NORMAL)
        }
    }

    //泛搜索专用
    private fun setSearchTypeFace(labelTv: TextView, isSelected: Boolean) {
        if (isSelected) {
            labelTv.isSelected = true
            labelTv.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
            val drawable = context.resources.getDrawable(R.drawable.search_ic_close_red_n_line_regular_8)
            drawable.setTintMode(PorterDuff.Mode.SRC_IN)
            drawable.setTint(if (BaseFragmentActivity.sIsDarkMode) Color.parseColor("#1b1b1b") else Color.parseColor("#ff4444"))
            labelTv.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null)
        } else {
            val drawable = context.resources.getDrawable(R.drawable.search_ic_add_n_line_regular_8)
            drawable.setTintMode(PorterDuff.Mode.SRC_IN)
            drawable.setTint(if (BaseFragmentActivity.sIsDarkMode) Color.parseColor("#dcdcdc") else Color.parseColor("#666666"))
            labelTv.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null)
        }
    }

    fun updateSelectedLabelItem(position: Int) {
        if (labelList.isNullOrEmpty() || mLastSelectedPosition == position) {
            return
        }
        val lastLabelData = labelList.getOrNull(mLastSelectedPosition)
        lastLabelData?.apply {
            if (isSelected) {
                isSelected = false
            }
            val lastSelectedTv = mFlLabel.getChildAt(mLastSelectedPosition)
            if (lastSelectedTv is TextView) {
                setTypeFace(lastSelectedTv, false)
            }
        }
        val curLabelData = labelList.getOrNull(position)
        curLabelData?.apply {
            if (!isSelected) {
                isSelected = true
            }
            val curSelectedTv = mFlLabel.getChildAt(position)
            if (curSelectedTv is TextView) {
                setTypeFace(curSelectedTv, true)
            }
        }
        mLastSelectedPosition = position
        updateResetBtnStatus()
    }

    /**
     * 泛搜索的label
     */
    private fun updateSelectedSearchLabelItem(position: Int) {
        if (searchLabels.isNullOrEmpty()) {
            return
        }
        val curLabelData = searchLabels.getOrNull(position)
        curLabelData?.apply {
            val curSelectedTv = mSearchFlLabel.getChildAt(position)
            if (curSelectedTv is TextView) {
                setSearchTypeFace(curSelectedTv,  status == 1)
            }
        }
        updateResetBtnStatus()
    }

    fun resetSearchLabels(searchLabels: MutableList<SearchLabel>?) {
        this.searchLabels?.clear()
        if (searchLabels != null) {
            this.searchLabels?.addAll(searchLabels)
        }
        mSearchFlLabel.removeAllViews()
        if (searchLabels.isNullOrEmpty()) {
            SearchUiUtils.setVisible(View.GONE, mSearchFlLabel)
        } else {
            SearchUiUtils.setVisible(View.VISIBLE, mSearchFlLabel)
            for (index in searchLabels.indices) {
                val lp = FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT, 28.dp)
                lp.bottomMargin = 8.dp
                lp.rightMargin = 8.dp
                val labelData = searchLabels[index]
                val labelTv = buildSearchLabelView(labelData, index)
                mSearchFlLabel.addView(labelTv, lp)
            }
        }
        updateResetBtnStatus()
    }

    fun updateSelectedSortFilterItem(position: Int) {
        if (mRgSortFilter.visibility == View.VISIBLE && position >= 0 && position < mRgSortFilter.childCount) {
            val curCheckedId = mRgSortFilter.checkedRadioButtonId
            val destCheckedId = mRgSortFilter.getChildAt(position).id
            if (curCheckedId != destCheckedId) {
                mNeedRefreshSortFilterItem = false
                mRgSortFilter.check(destCheckedId)
            }
        }
    }

    private fun initFilterRadioButton(radioGroup: RadioGroup): Array<RadioButton?>? {
        if (radioGroup.childCount == 0) {
            return null
        }
        val radioButtons = arrayOfNulls<RadioButton>(radioGroup.childCount)
        for (index in 0 until radioGroup.childCount) {
            radioButtons[index] = radioGroup.getChildAt(index) as RadioButton
        }
        return radioButtons
    }

    private fun setSortFilterRadioButtonText(sortFilterDataList: List<SearchSortFilterData>,
                                             radioButtons: Array<RadioButton?>?) {
        if (radioButtons.isNullOrEmpty() || sortFilterDataList.isEmpty()) {
            return
        }
        for (index in sortFilterDataList.indices) {
            if (index < radioButtons.size) {
                val radioButton = radioButtons[index] ?: continue
                val sortFilterData = sortFilterDataList[index]
                radioButton.tag = sortFilterData
                radioButton.text = sortFilterData.displayName
                if (sortFilterData.isSelected) {
                    mLastSelectedSortFilterItemPos = index
                    radioButton.isChecked = true
                    radioButton.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
                } else {
                    radioButton.isChecked = false
                    radioButton.typeface = Typeface.create("", Typeface.NORMAL)
                }
                radioButton.setOnCheckedChangeListener { buttonView, isChecked ->
                    sortFilterData.isSelected = isChecked
                    if (isChecked) {
                        buttonView?.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
                        mLastSelectedSortFilterItemPos = index
                        updateResetBtnStatus()
                        if (mNeedRefreshSortFilterItem) {
                            listener?.onSortItemClick(sortFilterData, index)
                            SearchTraceUtils.traceFilterItemClicked(sortFilterData.displayName, "综合排序", core)
                        } else {
                            mNeedRefreshSortFilterItem = true
                        }
                    } else {
                        buttonView?.typeface = Typeface.create("", Typeface.NORMAL)
                    }
                }
                radioButton.visibility = View.VISIBLE
                AutoTraceHelper.bindData(radioButton, AutoTraceHelper.MODULE_DEFAULT, sortFilterData)
            }
        }
    }

    private fun setPaidFilterRadioButtonText(paidFilterDataList: List<SearchPaidFilterData>,
                                             radioButtons: Array<RadioButton?>?) {
        if (radioButtons.isNullOrEmpty() || paidFilterDataList.isEmpty()) {
            return
        }
        for (index in paidFilterDataList.indices) {
            if (index < radioButtons.size) {
                val radioButton = radioButtons[index] ?: continue
                val paidFilterData = paidFilterDataList[index]
                radioButton.tag = paidFilterData
                radioButton.text = paidFilterData.displayName
                if (paidFilterData.isSelected) {
                    mLastSelectedPaidFilterItemPos = index
                    radioButton.isChecked = true
                    radioButton.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
                } else {
                    radioButton.isChecked = false
                    radioButton.typeface = Typeface.create("", Typeface.NORMAL)
                }
                radioButton.setOnCheckedChangeListener { buttonView, isChecked ->
                    paidFilterData.isSelected = isChecked
                    if (isChecked) {
                        buttonView?.typeface = Typeface.create("sans-serif-light", Typeface.BOLD)
                        mLastSelectedPaidFilterItemPos = index
                        updateResetBtnStatus()
                        if (mNeedRefreshPaidFilterItem) {
                            listener?.onPaidItemClick(paidFilterData)
                            SearchTraceUtils.traceFilterItemClicked(paidFilterData.displayName, "是否付费", core)
                        } else {
                            mNeedRefreshPaidFilterItem = true
                        }
                    } else {
                        buttonView?.typeface = Typeface.create("", Typeface.NORMAL)
                    }
                }
                radioButton.visibility = View.VISIBLE
                AutoTraceHelper.bindData(radioButton, AutoTraceHelper.MODULE_DEFAULT, paidFilterData)
            }
        }
    }

    private fun traceItemClick(tagName: String, position: Int) {
        XMTraceApi.Trace()
                .click(33833)
                .put("tagName", tagName)
                .put("searchWord", SearchTraceUtils.getKeyWord())
                .put("currPage", core)
                .put("position", (position + 1).toString())
                .put("type", "展开")
                .createTrace()
    }

    private fun traceFilterExposure() {
        if (!labelList.isNullOrEmpty()) {
            for (index in labelList.indices) {
                traceExposure(labelList[index].labelName, index)
            }
        }

        if (!searchLabels.isNullOrEmpty()) {
            for (index in searchLabels.indices) {
                traceSearchExposure(searchLabels[index].showName, index, searchLabels[index].status == 1)
            }
        }
    }

    private fun traceExposure(tagName: String, position: Int) {
        XMTraceApi.Trace()
                .setMetaId(33834)
                .setServiceId("slipPage")
                .put("tagName", tagName)
                .put("searchWord", SearchTraceUtils.getKeyWord())
                .put("currModule", "标签")
                .put("currPage", core)
                .put("exploreType", "1")
                .put("position", (position + 1).toString())
                .put("type", "展开")
                .put(XmRequestIdManager.IS_DUPLICATE_VIEW, "2")
                .createTrace()
    }

    //泛搜索专用
    private fun traceSearchItemClick(tagName: String, position: Int, isSelected: Boolean) {
        // 搜索结果页-标签词  点击事件
        XMTraceApi.Trace()
            .click(54014) // 用户点击时上报
            .put("currPage", SearchConstants.TRACE_PAGE_ALBUM)
            .put("type", SearchConstants.TRACE_TYPE_SELECT_LABEL) // 泛搜索卡片
            .put("TagWords", tagName)
            .put("searchWord", SearchTraceUtils.getKeyWord())
            .put("positionNew", ""+(position+1)) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put("status", if (isSelected) "已选择" else "未选择") // 已选择｜未选择
            //.put("strategy", ""+abInfo)
            .createTrace()
    }

    private fun traceSearchExposure(tagName: String, position: Int, isSelected: Boolean) {
        // 搜索结果页-标签词  控件曝光
        XMTraceApi.Trace()
            .setMetaId(54015)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", SearchConstants.TRACE_PAGE_ALBUM)
            .put("exploreType", "searchResult") // 0-滑动；1-首屏曝光；2-返回页面后的控件曝光
            .put("type", SearchConstants.TRACE_TYPE_SELECT_LABEL) // 泛搜索卡片
            .put("TagWords", tagName)
            .put("searchWord", SearchTraceUtils.getKeyWord())
            .put("positionNew", ""+(position + 1)) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
            .put("status", if (isSelected) "已选择" else "未选择") // 已选择｜未选择
            //.put("strategy", ""+abInfo)
            .createTrace()
    }

    fun show(anchor: View) {
        anchor.post {
            val location = IntArray(2)
            anchor.getLocationInWindow(location)
            showAtLocation(anchor, Gravity.TOP, 0, location[1])
            traceFilterExposure()
        }
    }

    interface IOnItemClickListener {
        fun onLabelItemClick(labelData: SearchLabelData, position: Int)

        fun onSearchLabelItemClick(searchLabels: MutableList<SearchLabel>?, mainLabel: MainLabel?)

        fun onSortItemClick(sortFilterData: SearchSortFilterData, position: Int)

        fun onPaidItemClick(paidFilterData: SearchPaidFilterData)

        fun onResetClick()

        fun onDismiss()
    }

}