<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.host.view.StickyNavLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:psts="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_search_result_sticky_nav"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@null"
    android:orientation="vertical">

    <View
        android:id="@+id/host_id_stickynavlayout_topview_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/host_color_lineColor2_black" />

    <include
        android:id="@id/host_id_stickynavlayout_topview"
        layout="@layout/search_search_data_head_view"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@id/host_id_stickynavlayout_indicator"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:translationZ="1dp">

        <com.ximalaya.ting.android.search.view.SearchPagerSlidingTabStrip
            android:id="@+id/search_id_stickynavlayout_indicator_tab"
            style="@style/host_my_pager_sliding_tab_strip_style"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/search_iv_wen_ai"
            android:background="@null"
            android:fadingEdge="horizontal"
            android:visibility="visible"
            psts:pstsActivateTextColor="@color/search_color_111111_cfcfcf"
            psts:pstsDeactivateTextColor="@color/search_color_666666_cfcfcf"
            psts:pstsIndicatorColor="@color/search_color_F86442"
            psts:pstsShouldExpand="false"
            psts:pstsSmoothScroll="false"
            psts:pstsTabPaddingLeftRight="20dp"
            psts:pstsTabSwitch="true"
            psts:pstsUnderlineHeight="6dp"
            psts:pstsUnderlineColor="@color/host_transparent"
            psts:pstsIndicatorCornerRadius="1dp"
            psts:pstsIndicatorWidth="20dp"
            psts:pstsIndicatorHeight="4dp" />

        <!--<include
            android:id="@+id/search_layout_right_filter_select"
            layout="@layout/search_layout_tab_filter_select"
            android:layout_width="54dp"
            android:layout_height="45dp"
            android:layout_alignParentRight="true"
            android:visibility="gone"
            tools:visibility="visible" />-->

        <ImageView
            android:id="@+id/search_shadow_wen_ai"
            android:layout_width="30dp"
            android:visibility="gone"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/search_iv_wen_ai"
            android:background="@drawable/search_bg_filter_divider_shadow_shape" />

        <ImageView
            android:id="@+id/search_iv_wen_ai"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentTop="true"
            android:visibility="gone"
            android:paddingStart="10dp"
            tools:visibility="visible"
            android:contentDescription="@string/host_wen_ai_icon_entrance"
            tools:background="@color/host_color_green"
            android:background="@color/host_color_f8f8f8_131313"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:paddingEnd="16dp"
            android:src="@drawable/search_ic_wen_ai" />

    </RelativeLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/search_vg_immersive_banner_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="invisible">

            <View
                android:id="@+id/search_v_immersive_banner_bg_solid"
                android:layout_width="match_parent"
                android:layout_height="24dp"
                />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/search_iv_immersive_banner_bg"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:scaleType="centerCrop"
                />
                <View
                    android:visibility="gone"
                    android:id="@+id/search_v_mask_bg"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:layout_alignTop="@id/search_iv_immersive_banner_bg" />
            </RelativeLayout>
        </LinearLayout>

        <com.ximalaya.ting.android.host.view.other.MyViewPager
            android:id="@id/host_id_stickynavlayout_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            tools:background="@color/search_color_f3f4f5" />

        <View
            android:id="@+id/search_v_immersive_banner_mask"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:background="@color/host_color_ffffff_121212"
            android:layout_marginTop="-160dp"
            android:visibility="invisible"
            />

    </FrameLayout>

</com.ximalaya.ting.android.host.view.StickyNavLayout>
