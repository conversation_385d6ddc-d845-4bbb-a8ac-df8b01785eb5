<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="66dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/search_iv_ip_cover"
        android:layout_width="66dp"
        android:layout_height="66dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:corner_radius="100dp"
        tools:src="@drawable/host_ic_avatar_default"
        />

    <TextView
        android:id="@+id/search_tv_ip_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/search_iv_ip_cover"
        android:layout_marginTop="4dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:textSize="12sp"
        android:textColor="@color/host_color_listTitleColor"
        android:gravity="center_horizontal"
        tools:text="米小圈"
        />

</androidx.constraintlayout.widget.ConstraintLayout>