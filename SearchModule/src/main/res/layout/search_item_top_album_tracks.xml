<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="145dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_f8f8fa_282828_radius_4">

    <TextView
        android:id="@+id/search_tv_track_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        android:lines="2"
        android:ellipsize="end"
        android:paddingVertical="8dp"
        android:layout_marginHorizontal="12dp"
        android:textSize="13sp"
        android:textColor="@color/host_color_listTitleColor"
        tools:text="庆余年第二季 | 002 饭碗CP情人节撒糖"
        />

</androidx.constraintlayout.widget.ConstraintLayout>