package com.ximalaya.ting.android.opensdk.model.advertis;

import android.os.Parcel;
import android.os.Parcelable;

public class BrandData implements Parcelable {
    private String title;
    private String picUrl;
    private String videoUrl;
    private String realLink;
    private String dpLink;

    public BrandData() {
    }

    protected BrandData(Parcel in) {
        picUrl = in.readString();
        title = in.readString();
        videoUrl = in.readString();
        realLink = in.readString();
        dpLink = in.readString();
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getRealLink() {
        return realLink;
    }

    public void setRealLink(String realLink) {
        this.realLink = realLink;
    }

    public String getDpLink() {
        return dpLink;
    }

    public void setDpLink(String dpLink) {
        this.dpLink = dpLink;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.picUrl);
        dest.writeString(this.title);
        dest.writeString(this.videoUrl);
        dest.writeString(this.realLink);
        dest.writeString(this.dpLink);
    }

    public void readFromParcel(Parcel source) {
        this.picUrl = source.readString();
        this.title = source.readString();
        this.videoUrl = source.readString();
        this.realLink = source.readString();
        this.dpLink = source.readString();
    }

    public static final Creator<BrandData> CREATOR = new Creator<BrandData>() {
        @Override
        public BrandData createFromParcel(Parcel in) {
            return new BrandData(in);
        }

        @Override
        public BrandData[] newArray(int size) {
            return new BrandData[size];
        }
    };
}
