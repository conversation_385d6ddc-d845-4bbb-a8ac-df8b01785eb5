/**
 * TrackHotList.java
 * com.ximalaya.ting.android.opensdk.datatrasfer.dto.track
 *
 * Function： TODO
 *
 *   ver     date      		author
 * ──────────────────────────────────
 *   		 2015-5-6 		jack.qin
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.model.track;

import com.google.gson.annotations.SerializedName;

/**
 * ClassName:TrackHotList Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 *
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-5-6 上午11:39:01
 *
 * @see
 */
public class TrackHotList extends CommonTrackList<Track>
{
	@SerializedName("current_page")
	private int currentPage;
	@SerializedName("category_id")
	private int categoryId;
	@SerializedName("tag_name")
	private String tagName;

	public int getCategoryId()
	{
		return categoryId;
	}

	public void setCategoryId(int categoryId)
	{
		this.categoryId = categoryId;
	}

	public String getTagName()
	{
		return tagName;
	}

	public void setTagName(String tagName)
	{
		this.tagName = tagName;
	}

	public int getCurrentPage()
	{
		return currentPage;
	}

	public void setCurrentPage(int currentPage)
	{
		this.currentPage = currentPage;
	}

	@Override
	public String toString()
	{
		return "TrackHotList [currentPage=" + currentPage + ", categoryId="
				+ categoryId + ", tagName=" + tagName + "]";
	}

}
