package com.ximalaya.ting.android.opensdk.model.xdcs;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by nali on 2017/6/13.
* <AUTHOR>
 */

public class XdcsRecord {
    public List<XdcsEvent> events = new ArrayList<>();
    public long sendTime;
    public static XdcsRecord createXdcsRecord(List<XdcsEvent> events) {
        XdcsRecord xdcsRecord = new XdcsRecord();
        if (events != null) {
            xdcsRecord.events = new ArrayList<>(events);
        }
        xdcsRecord.sendTime = System.currentTimeMillis();
        return xdcsRecord;
    }
    public String nonce;
    public String signature;
}
