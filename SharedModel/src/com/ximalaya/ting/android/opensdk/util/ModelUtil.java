/**
 * ModeUtil.java
 * com.ximalaya.ting.android.opensdk.datatrasfer
 *
 *
 *   ver     date      		author
 * ---------------------------------------
 *   		 2015-5-25 		chadwii
 *
 * Copyright (c) 2015, chadwii All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.util;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.Announcer;
import com.ximalaya.ting.android.opensdk.model.live.program.Program;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.live.schedule.LiveAnnouncer;
import com.ximalaya.ting.android.opensdk.model.live.schedule.Schedule;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
/**
 * <AUTHOR>
 */
public class ModelUtil {

	/** 将排期表中的节目转换成track */
	public static Track scheduleToTrack(Schedule schedule) {
		if (schedule == null) {
			return null;
		}
		Track track = new Track();
		track.setStartTime(schedule.getStartTime());
		track.setEndTime(schedule.getEndTime());
		track.setKind(PlayableModel.KIND_SCHEDULE);
		track.setPlayUrl32(schedule.getListenBackUrl());
		track.setPlayUrl64(schedule.getListenBackUrl());
		/** schedule_id */
		track.setDataId(schedule.getDataId());

		Program pro = schedule.getRelatedProgram();
		track.setProgramId(pro.getProgramId());
		track.setScheduleId(schedule.getDataId());
		track.setTrackTitle(pro.getProgramName());
		track.setCoverUrlLarge(pro.getBackPicUrl());
		track.setCoverUrlMiddle(pro.getBackPicUrl());
		track.setCoverUrlSmall(pro.getBackPicUrl());
		track.setRadioRate24AacUrl(pro.getRate24AacUrl());
		track.setRadioRate24TsUrl(pro.getRate24TsUrl());
		track.setRadioRate64AacUrl(pro.getRate64AacUrl());
		track.setRadioRate64TsUrl(pro.getRate64TsUrl());
		track.setUpdatedAt(pro.getUpdateAt());
		track.setRadioId(schedule.getRadioId());
		track.setRadioName(schedule.getRadioName());
		track.setPlayCount(schedule.getRadioPlayCount());
		track.setChannelId(schedule.getChannelId());
		track.setChannelName(schedule.getChannelName());
		track.setChannelPic(schedule.getChannelPic());

		if (pro.getAnnouncerList() != null && pro.getAnnouncerList().size() > 0) {
			StringBuilder allAnnouncerName = new StringBuilder();
			for (LiveAnnouncer announcer : pro.getAnnouncerList()) {
				// 广播的作者字段是list 声音只有一个，转换过程中会丢失导致显示bug，这里对name进行拼接，保证显示正常
				// 如果后续有需求需要对作者的头像及id做操作，再做修改
				if (announcer != null && !TextUtils.isEmpty(announcer.getNickName()) &&
						!announcer.getNickName().equalsIgnoreCase("null")) {
					allAnnouncerName.append(announcer.getNickName()).append(" ");
				}
			}
			LiveAnnouncer la = pro.getAnnouncerList().get(0);
			Announcer an = liveAnnouncerToAnnouncer(la);
			an.setNickname(allAnnouncerName.toString().trim());
			track.setAnnouncer(an);
		}
		return track;
	}

	/**
	 * radio转track isActivity 表示是否是活动直播
	 * */
	public static Track radioToTrack(Radio radio, boolean isActivity) {
		if (radio == null) {
			return null;
		}
		Track track = new Track();
		track.setDataId(radio.getDataId());
		track.setKind(PlayableModel.KIND_RADIO);
		track.setTrackTitle(radio.getRadioName());
		track.setTrackIntro(radio.getRadioDesc());
		track.setScheduleId(radio.getScheduleID());
		track.setRadioRate24AacUrl(radio.getRate24AacUrl());
		track.setRadioRate24TsUrl(radio.getRate24TsUrl());
		track.setRadioRate64AacUrl(radio.getRate64AacUrl());
		track.setRadioRate64TsUrl(radio.getRate64TsUrl());
		track.setPlayCount(radio.getRadioPlayCount());
		track.setCoverUrlLarge(radio.getCoverUrlLarge());
		track.setCoverUrlSmall(radio.getCoverUrlSmall());
		track.setUpdatedAt(radio.getUpdateAt());
		track.setPlayCount(radio.getRadioPlayCount());
		track.setExtra(isActivity); // 是否是活动直播
		track.setChannelId(radio.getChannelId());
		track.setChannelName(radio.getChannelName());
		track.setChannelPic(radio.getChannelPic());
		try {
			track.setStartTime(format.format(new Date(radio.getStartTime())));
			track.setEndTime(format.format(new Date(radio.getEndTime())));
		}catch (Exception e){

		}
		track.setTrackActivityId(radio.getActivityId());
		return track;
	}

	static SimpleDateFormat format =  new SimpleDateFormat("yy:MM:dd:HH:mm");

	public static Radio trackToRadio(Track track) {
		if (track == null) {
			return null;
		}
		Radio radio = new Radio();
		radio.setDataId(track.getDataId());
		radio.setKind(PlayableModel.KIND_RADIO);
		radio.setRadioName(track.getTrackTitle());
		radio.setScheduleID(track.getScheduleId());
		radio.setRate24AacUrl(track.getRadioRate24AacUrl());
		radio.setRate24TsUrl(track.getRadioRate24TsUrl());
		radio.setRate64AacUrl(track.getRadioRate64AacUrl());
		radio.setRate64TsUrl(track.getRadioRate64TsUrl());
		radio.setRadioPlayCount(track.getPlayCount());
		radio.setCoverUrlSmall(track.getCoverUrlSmall());
		radio.setCoverUrlLarge(track.getCoverUrlLarge());
		radio.setUpdateAt(track.getUpdatedAt());
		radio.setRadioPlayCount(track.getPlayCount());
		radio.setRadioDesc(track.getTrackIntro());
		radio.setActivityLive(track.getExtra());
		radio.setChannelId(track.getChannelId());
		radio.setChannelName(track.getChannelName());
		radio.setChannelPic(track.getChannelPic());
		try {
			radio.setStartTime(format.parse(track.getStartTime()).getTime());
			radio.setEndTime(format.parse(track.getEndTime()).getTime());
		} catch (Exception e) {
		}
		radio.setActivityId(track.getTrackActivityId());
		return radio;
	}

	public static Schedule trackToSchedule(Track track) {
		if (track == null) {
			return null;
		}
		Schedule schedule = new Schedule();
		schedule.setDataId(track.getDataId());
		schedule.setKind(PlayableModel.KIND_SCHEDULE);
		schedule.setStartTime(track.getStartTime());
		schedule.setEndTime(track.getEndTime());
		schedule.setUpdateAt(track.getUpdatedAt());
		schedule.setListenBackUrl(TextUtils.isEmpty(track.getPlayUrl64()) ? track
				.getPlayUrl32() : track.getPlayUrl64());
		Program pro = new Program();
		pro.setProgramId(track.getProgramId());
		pro.setProgramName(track.getTrackTitle());
		pro.setBackPicUrl(TextUtils.isEmpty(track.getCoverUrlSmall()) ? track
				.getCoverUrlLarge() : track.getCoverUrlSmall());
		pro.setRate24AacUrl(track.getRadioRate24AacUrl());
		pro.setRate64AacUrl(track.getRadioRate64AacUrl());
		pro.setRate24TsUrl(track.getRadioRate24TsUrl());
		pro.setRate64TsUrl(track.getRadioRate64TsUrl());
		LiveAnnouncer la = announcerToLiveAnnouncer(track.getAnnouncer());
		if (la != null) {
			pro.setAnnouncerList(new ArrayList<LiveAnnouncer>(Arrays.asList(la)));
		}
		schedule.setRelatedProgram(pro);
		schedule.setRadioId(track.getRadioId());
		schedule.setRadioName(track.getRadioName());
		schedule.setRadioPlayCount(track.getPlayCount());
		schedule.setChannelId(track.getChannelId());
		schedule.setChannelName(track.getChannelName());
		schedule.setChannelPic(track.getChannelPic());
		return schedule;
	}

	@SuppressLint("SimpleDateFormat")
	public static List<Track> toTrackList(List<Schedule> list) {
		if (list == null) {
			return null;
		}
		List<Track> tracks = new ArrayList<Track>();
		for (Schedule schedule : list) {
			tracks.add(scheduleToTrack(schedule));
		}
		return tracks;
	}

	public static LiveAnnouncer announcerToLiveAnnouncer(Announcer announcer) {
		if (announcer == null) {
			return null;
		}
		LiveAnnouncer la = new LiveAnnouncer();
		la.setLiveAnnouncerId(announcer.getAnnouncerId());
		la.setNickName(announcer.getNickname());
		la.setAvatarUrl(announcer.getAvatarUrl());
		return la;
	}

	public static Announcer liveAnnouncerToAnnouncer(LiveAnnouncer la) {
		if (la == null) {
			return null;
		}
		Announcer an = new Announcer();
		an.setAnnouncerId(la.getLiveAnnouncerId());
		an.setNickname(la.getNickName());
		an.setAvatarUrl(la.getAvatarUrl());
		return an;
	}

	public static Schedule radioToSchedule(Radio radio) {
		if (radio == null) {
			return null;
		}

		Schedule schedule = new Schedule();
		schedule.setKind(PlayableModel.KIND_RADIO);
		String todey = BaseUtil.getYDTDayNum()[1];
		schedule.setStartTime(todey + ":00:00");
		schedule.setEndTime(todey + ":23:59");

		Program program = new Program();
		program.setBackPicUrl(radio.getCoverUrlLarge());
		program.setRate24AacUrl(radio.getRate24AacUrl());
		program.setRate24TsUrl(radio.getRate24TsUrl());
		program.setRate64AacUrl(radio.getRate64AacUrl());
		program.setRate64TsUrl(radio.getRate64TsUrl());
		program.setProgramName(radio.getRadioName());

		schedule.setDataId(radio.getDataId());
		schedule.setRelatedProgram(program);
		schedule.setRadioId(radio.getDataId());
		schedule.setRadioName(radio.getRadioName());
		schedule.setRadioPlayCount(radio.getRadioPlayCount());
		schedule.setChannelId(radio.getChannelId());
		schedule.setChannelName(radio.getChannelName());
		schedule.setChannelPic(radio.getChannelPic());

		return schedule;
	}
}
