<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:skin="http://schemas.android.com/android/skin"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/host_rl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!--下拉二楼广告,不能放到HomePage中因为fragment_container above 了tabs-->
    <com.ximalaya.ting.android.host.view.ad.ShowPairImageView
        android:id="@+id/host_drop_down_two_style_ad"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/host_tab_bg_lay"
        android:layout_marginBottom="-3dp" />

    <FrameLayout
        android:id="@+id/host_tab_bg_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignTop="@id/rg_tabs">

        <View
            android:id="@+id/host_v_tabs_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/host_theme_bottom_tab_background"
            android:scaleType="centerCrop"
            skin:enable="true" />

        <ImageView
            android:id="@+id/host_iv_tabs_bg"
            android:layout_width="match_parent"
            android:layout_height="49dp"
            android:layout_gravity="bottom"
            android:scaleType="centerCrop"
            android:visibility="gone"
            tools:visibility="visible" />
    </FrameLayout>

    <ViewStub
        android:id="@+id/host_mine_tip_vs"
        android:layout_alignParentEnd="true"
        android:layout_marginBottom="-12dp"
        android:layout_above="@+id/rg_tabs"
        android:layout="@layout/host_mine_strong_tip_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <include
        android:id="@+id/host_dync_tip_vs"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/rg_tabs"
        android:layout_marginBottom="3dp"
        android:layout_alignParentEnd="true"
        layout="@layout/host_dync_tip_layout" />

    <!--    高度在MainActivity中会根据ab设置-->
    <RadioGroup
        android:id="@+id/rg_tabs"
        android:layout_width="match_parent"
        android:layout_height="63dp"
        android:layout_alignParentBottom="true"
        android:background="@color/host_transparent"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/tab_home_page"
            style="@style/host_main_tab_style"
            android:layout_marginTop="@dimen/host_tab_icon_margin_top"
            android:drawablePadding="@dimen/host_tab_icon_drawable_padding"
            android:importantForAccessibility="yes"
            android:drawableTop="@drawable/host_home_page_tab_01"
            android:text="@string/host_home_page"
            android:textColor="@color/host_theme_bottom_tab_text_selector" />

        <RadioButton
            android:id="@+id/tab_vip"
            style="@style/host_main_tab_style"
            android:layout_marginTop="@dimen/host_tab_icon_margin_top"
            android:importantForAccessibility="yes"
            android:drawablePadding="@dimen/host_tab_icon_drawable_padding"
            android:drawableTop="@drawable/host_vip_tab_01"
            android:visibility="gone"
            android:text="会员"
            android:textColor="@color/host_theme_bottom_tab_text_selector" />

        <RadioButton
            android:id="@+id/play"
            style="@style/host_main_tab_style" />

        <RadioButton
            android:id="@+id/tab_category"
            style="@style/host_main_tab_style"
            android:layout_marginTop="@dimen/host_tab_icon_margin_top"
            android:importantForAccessibility="yes"
            android:drawablePadding="@dimen/host_tab_icon_drawable_padding"
            android:drawableTop="@drawable/host_vip_tab_01"
            android:visibility="gone"
            android:text="分类"
            android:textColor="@color/host_theme_bottom_tab_text_selector" />

        <RadioButton
            android:id="@+id/tab_finding"
            style="@style/host_main_tab_style"
            android:layout_marginTop="@dimen/host_tab_icon_margin_top"
            android:importantForAccessibility="yes"
            android:drawablePadding="@dimen/host_tab_icon_drawable_padding"
            android:drawableTop="@drawable/host_find_tab_01"
            android:text="关注"
            android:visibility="gone"
            android:textColor="@color/host_theme_bottom_tab_text_selector" />

        <RadioButton
            android:id="@+id/tab_myspace_and_listen"
            style="@style/host_main_tab_style"
            android:layout_marginTop="@dimen/host_tab_icon_margin_top"
            android:drawablePadding="@dimen/host_tab_icon_drawable_padding"
            android:importantForAccessibility="yes"
            android:drawableTop="@drawable/host_mine_v9_tab_01"
            android:visibility="gone"
            android:text="我的"
            android:textColor="@color/host_theme_bottom_tab_text_selector" />

    </RadioGroup>

    <ViewStub
        android:id="@+id/host_elderly_radio_tabs"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout="@layout/host_view_elderly_radio_group" />

    <ViewStub
        android:id="@+id/host_accessibility_mode_radio_tabs"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout="@layout/host_view_accessibility_mode_radio_group" />

    <ViewStub
        android:id="@+id/host_bottom_tab_hot_stub"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@+id/rg_tabs"
        android:layout="@layout/host_main_bottom_hot_stub"
        android:layout_alignParentBottom="true"
        android:layout_alignTop="@+id/rg_tabs" />

    <ViewStub
        android:id="@+id/host_quick_listen_tip_vs"
        android:layout_alignParentStart="true"
        android:layout_above="@+id/rg_tabs"
        tools:visibility="visible"
        android:layout="@layout/host_quick_listen_tip_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <FrameLayout
        android:id="@+id/fragment_full"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/fragment_playbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:background="@null" />

    <FrameLayout
        android:id="@+id/fragment_playbar_new"
        android:visibility="gone"
        android:layout_above="@+id/rg_tabs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null" />

    <com.ximalaya.ting.android.host.quicklisten.view.CustomReactClipViewGroup
        android:id="@+id/fragment_quick_listen_xiaoya_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@null"
        android:visibility="gone" />

    <View
        android:id="@+id/host_safe_area"
        android:visibility="gone"
        android:background="@color/host_color_fafafafa_fa262626"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="12dp" />

    <FrameLayout
        android:id="@+id/fragment_play"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ViewStub
        android:id="@+id/host_vs_iting_return_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="120dp"
        android:layout="@layout/host_view_iting_return_btn" />

    <FrameLayout
        android:id="@+id/top_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</RelativeLayout>