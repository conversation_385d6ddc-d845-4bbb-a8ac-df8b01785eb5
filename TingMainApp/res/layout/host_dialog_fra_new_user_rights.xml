<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/host_iv_img"
        android:layout_width="match_parent"
        android:layout_height="326dp"
        app:layout_constraintTop_toTopOf="parent"
        android:scaleType="fitXY"
        android:contentDescription="登录领权益"
        />

    <TextView
        android:id="@+id/host_tv_countdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/host_iv_img"
        android:layout_marginBottom="16dp"
        android:textSize="12sp"
        android:textColor="#fff4df"
        tools:text="04:22:45"
        />
    
    <View
        android:id="@+id/host_v_click_area"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="275:99"
        app:layout_constraintBottom_toBottomOf="@id/host_iv_img"
        android:contentDescription="登录领取"
        />

    <ImageView
        android:id="@+id/host_iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/host_close"
        android:src="@drawable/host_ic_new_user_rights_dialog_close"
        app:layout_constraintTop_toBottomOf="@id/host_iv_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="20dp"
        />
    
</androidx.constraintlayout.widget.ConstraintLayout>