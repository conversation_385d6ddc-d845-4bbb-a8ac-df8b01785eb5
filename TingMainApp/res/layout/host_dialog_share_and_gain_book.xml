<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_share_book_dialog_shape"
    android:minWidth="280dp"
    android:paddingBottom="30dp">

    <ImageView
        android:id="@+id/host_iv_close"
        android:layout_width="wrap_content"
        android:padding="8dp"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:src="@drawable/host_ic_close"
        android:contentDescription="@string/host_close"/>

    <TextView
        android:id="@+id/host_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="40dp"
        android:text="会员资格已放入您的账户"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:textSize="16sp"
         />

    <TextView
        android:id="@+id/host_tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/host_tv_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:text="请在「首页 → 0元购 → 我参与的」查看"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/host_tv_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="10dp"
        android:layout_marginTop="40dp"
        android:layout_below="@+id/host_tv_content"
        android:background="@drawable/host_bg_share_book_selector"
        android:text="立即查看"
        android:layout_marginLeft="65dp"
        android:layout_marginRight="65dp"
        android:paddingBottom="10dp"
        android:textColor="@color/host_white"
        android:textSize="16sp" />

</RelativeLayout>