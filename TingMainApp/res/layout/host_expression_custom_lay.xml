<?xml version="1.0" encoding="utf-8"?>
<merge
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:parentTag="android.widget.FrameLayout"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/host_layout_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:paddingVertical="4dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/host_iv_like"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:scaleType="fitXY"
            android:src="@drawable/host_album_rate_like_selector_new"
            android:visibility="visible" />

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/host_lottie_like"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:scaleType="centerInside"
            app:lottie_autoPlay="false"
            app:lottie_fileName="lottie/host_lottie_for_like_action.json"
            android:visibility="gone" />

        <TextView
            android:id="@+id/host_tv_like_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2dp"
            android:paddingTop="3dp"
            android:textColor="@color/host_play_page_comment_like_count_text_color_selector"
            android:textSize="20sp"
            tools:text="99" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/host_layout_hate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="100dp"
        android:paddingVertical="4dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/host_iv_hate"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingLeft="2dp"
            android:paddingVertical="4dp"
            android:contentDescription="踩"
            android:src="@drawable/host_ic_standard_thumb_down" />

        <TextView
            android:id="@+id/host_tv_hate_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2dp"
            android:paddingTop="3dp"
            android:textColor="@color/host_play_page_comment_like_count_text_color_selector"
            android:textSize="20sp"
            tools:text="99" />
    </LinearLayout>

</merge>