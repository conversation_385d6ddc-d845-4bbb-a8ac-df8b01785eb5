<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:makeramen="http://schemas.android.com/apk/res-auto"
    xmlns:skin="http://schemas.android.com/android/skin"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ViewStub
        android:id="@+id/host_vs_continue_play_strong_tips"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/host_view_continue_play_strong_tips"/>

    <FrameLayout
        android:id="@+id/main_fragment_playbar"
        android:layout_width="114dp"
        android:layout_height="63dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        skin:enable="true">
        <ImageView
            android:id="@+id/main_play_bar_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/host_icon_play_bar_shadow"/>
        <FrameLayout
            android:id="@+id/main_fragment_playbar_without_shadow"
            android:layout_width="62dp"
            android:layout_height="59dp"
            android:paddingBottom="3dp"
            android:layout_gravity="center_horizontal|bottom">
            <ImageView
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:layout_gravity="center"
                android:src="@drawable/host_icon_play_bar_bg"/>
            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/main_sound_cover_img"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_gravity="center"
                android:background="@drawable/host_default_album"
                android:contentDescription="@string/host_enter_play_page"
                android:scaleType="centerCrop"
                makeramen:corner_radius="50dp"
                makeramen:pressdown_shade="true"
                makeramen:round_background="true" />

            <androidx.cardview.widget.CardView
                android:id="@+id/main_vg_chatxmly_anim"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_gravity="center"
                makeramen:cardCornerRadius="100dp"
                makeramen:cardElevation="0dp"
                android:visibility="invisible">

                <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                    android:id="@+id/main_lottie_chatxmly_anim"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_gravity="center"
                    makeramen:lottie_repeatCount="-1"
                    />

            </androidx.cardview.widget.CardView>

            <com.ximalaya.ting.android.host.view.bar.RoundProgressBar
                android:id="@+id/main_round_progressbar"
                makeramen:style="FILL"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_gravity="center"
                makeramen:roundWidth="0dip"
                makeramen:roundColor="#0000"
                makeramen:roundProgressColor="#5f000000" />

            <ImageView
                android:id="@+id/main_play_icon_img"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_gravity="center"
                android:scaleType="centerInside"
                android:contentDescription="@string/host_enter_play_page"
                skin:enable="true"
                android:src="@drawable/host_theme_global_play_level_list" />

            <com.ximalaya.ting.android.host.view.bar.RoundProgressBar
                android:id="@+id/host_round_progressbar_play_progress"
                makeramen:style="STROKE"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center"
                makeramen:textIsDisplayable="false"
                makeramen:roundWidth="2dp"
                makeramen:roundColor="@color/host_transparent"
                makeramen:roundProgressColor="@color/host_color_xmRed"
                makeramen:cap="ROUND"
                android:visibility="visible"/>

            <ImageView
                android:id="@+id/main_ad_mark"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:visibility="gone"
                android:scaleType="centerCrop"
                android:src="@drawable/host_icon_playbar_yaoyiyao" />
        </FrameLayout>

    </FrameLayout>

    <!--<com.airbnb.lottie.LottieAnimationView-->
    <!--android:id="@+id/host_sleep_mode_lottie_animation_view"-->
    <!--android:layout_width="46dp"-->
    <!--android:layout_height="60dp"-->
    <!--android:layout_alignLeft="@+id/main_fragment_playbar"-->
    <!--android:layout_alignBottom="@+id/main_fragment_playbar"-->
    <!--android:layout_marginLeft="80dp"-->
    <!--android:layout_marginBottom="20dp"-->
    <!--android:layout_gravity="center"-->
    <!--android:scaleType="centerCrop" />-->

    <FrameLayout
        android:id="@+id/main_fragment_quick_listen_tab"
        android:layout_width="114dp"
        android:layout_height="63dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:visibility="gone"
        tools:visibility="visible">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="59dp"
            android:layout_gravity="center_horizontal|bottom"
            android:paddingBottom="3dp">

            <com.ximalaya.ting.android.host.quicklisten.view.QuickListenPlayStatusIcon
                android:id="@+id/main_quick_listen_tab_play_btn"
                android:layout_width="51dp"
                android:layout_height="51dp"
                android:layout_gravity="center" />
        </FrameLayout>
    </FrameLayout>

    <ViewStub
        android:id="@+id/host_to_listen_animation_vs"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginBottom="32dp"
        android:layout_centerHorizontal="true"
        android:layout_above="@+id/main_fragment_playbar"
        android:layout="@layout/host_to_listen_animation_cover_layout" />

    <TextView
        android:id="@+id/tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/host_bg_point_toast"
        android:maxLines="1"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:layout_marginBottom="-5dp"
        android:paddingTop="5dp"
        android:layout_above="@id/main_fragment_playbar"
        android:layout_centerHorizontal="true"
        android:textColor="@color/host_color_ffffff_000000"
        android:visibility="gone"
        android:textSize="14sp" />

</RelativeLayout>