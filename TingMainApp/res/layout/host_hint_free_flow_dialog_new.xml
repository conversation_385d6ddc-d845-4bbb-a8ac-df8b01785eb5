<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="279dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center_horizontal">



    <LinearLayout
        android:id="@+id/host_bottom_lay"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="-30dp"
        android:gravity="center_horizontal"
        android:layout_below="@id/host_iv_top_img"
        android:background="@drawable/host_bg_rect_ffffff_1e1e1e_radius_8"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/host_tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:textSize="16sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/host_color_ac6034_ffffff"
            tools:text="@string/host_flow_tips"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            />

        <TextView
            android:id="@+id/host_tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="@string/host_flow_tips_content"
            android:layout_marginTop="8dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:gravity="center"
            android:textColor="@color/host_color_ccac6034_99ffffff"
            />

        <TextView
            android:id="@+id/host_tv_free_flow_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/host_bg_rect_ff4c2f_ff742e_radius_100"
            android:textColor="@color/host_color_white_ffffff"
            android:textSize="16sp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            tools:text="@string/host_open_free_flow_service"
            android:gravity="center"
            />

        <TextView
            android:id="@+id/host_tv_allow_always_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:background="@drawable/host_bg_rect_stroke_eeeeee_2c2c2c_radius_100"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:text="@string/host_allow_always"
            android:gravity="center"
            />

        <TextView
            android:id="@+id/host_tv_active_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:padding="8dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="8dp"
            android:textSize="12sp"
            android:textColor="@color/host_color_999999"
            android:text="@string/host_has_order_free_flow_service_active_right_now"
            android:gravity="center"
            android:includeFontPadding="false"
            android:drawableEnd="@drawable/host_arrow_gray_right_small"
            android:drawablePadding="8dp"
            />

    </LinearLayout>

    <ImageView
        android:id="@+id/host_iv_top_img"
        android:layout_alignLeft="@id/host_bottom_lay"
        android:layout_alignRight="@id/host_bottom_lay"
        android:layout_width="wrap_content"
        android:layout_height="88dp"
        android:src="@drawable/host_bg_free_flow_dialog_new"
        android:scaleType="fitXY"
        />

    <ImageView
        android:id="@+id/host_iv_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentRight="true"
        android:layout_marginTop="28dp"
        android:layout_marginRight="8dp"
        android:src="@drawable/host_ic_x_close_pop_fill_regular_24" />

</RelativeLayout>