<?xml version="1.0" encoding="utf-8"?>

<com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/host_ad_hybird_lay"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.ximalaya.ting.android.host.view.ad.advideo.AdVideoView
        android:id="@+id/host_video_ad_view"
        android:background="@color/host_black"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
    </com.ximalaya.ting.android.host.view.ad.advideo.AdVideoView>

    <RelativeLayout
        android:id="@+id/host_ad_video_top_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/host_ad_video_play_state"
            android:layout_centerInParent="true"
            android:src="@drawable/host_ad_video_play_statue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/host_ad_video_volumn"
            android:src="@drawable/host_ad_video_volumn_statue"
            android:layout_alignParentRight="true"
            android:layout_marginTop="12dp"
            android:layout_marginRight="16dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="6dp"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/host_ad_video_start_time"
                tools:text="00:02"
                android:layout_marginLeft="12dp"
                android:textSize="10dp"
                android:shadowColor="#f0000000"
                android:shadowDx="2"
                android:shadowDy="2"
                android:shadowRadius="3"
                android:layout_centerVertical="true"
                android:textColor="@color/host_color_ccffffff"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/host_ad_video_end_time"
                tools:text="00:02"
                android:layout_marginRight="12dp"
                android:textSize="10dp"
                android:shadowColor="#f0000000"
                android:shadowDx="2"
                android:shadowDy="2"
                android:shadowRadius="3"
                android:layout_centerVertical="true"
                android:layout_alignParentRight="true"
                android:textColor="@color/host_color_ccffffff"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <SeekBar
                android:id="@+id/host_ad_video_seek_bar"
                style="@style/host_ad_video_seek_bar_theme"
                android:layout_toLeftOf="@id/host_ad_video_end_time"
                android:layout_toRightOf="@id/host_ad_video_start_time"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:thumbOffset="6dp"
                tools:progress="50" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/host_ad_video_end_lay"
        android:background="@color/host_color_cc000000"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/host_ad_video_end_icon"
            android:layout_width="50dp"
            android:layout_marginTop="40dp"
            android:layout_centerHorizontal="true"
            app:corner_radius="10dp"
            android:layout_height="50dp" />

        <TextView
            android:id="@+id/host_ad_video_end_title"
            android:layout_below="@id/host_ad_video_end_icon"
            android:layout_width="wrap_content"
            tools:text="应用名字名字"
            android:layout_marginTop="10dp"
            android:layout_centerHorizontal="true"
            android:textSize="13sp"
            android:textColor="@color/host_color_ffffff"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/main_video_play_ad_click"
            android:textColor="@color/host_color_ffffff"
            android:layout_below="@id/host_ad_video_end_title"
            android:layout_marginTop="26dp"
            android:layout_centerHorizontal="true"
            android:paddingLeft="24dp"
            android:paddingRight="24dp"
            android:paddingBottom="9dp"
            android:paddingTop="9dp"
            android:minWidth="103dp"
            android:textSize="11sp"
            android:gravity="center"
            android:text="了解详情"
            android:background="@drawable/host_video_play_btn_big_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_margin="17dp"
            android:text="重播"
            android:textSize="13sp"
            android:padding="5dp"
            android:drawablePadding="5dp"
            android:textColor="@color/host_color_ffffff"
            android:drawableLeft="@drawable/host_ad_icon_to_replay"
            android:id="@+id/host_video_play_retry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </RelativeLayout>

    <FrameLayout
        android:id="@+id/host_ad_loading_lay"
        android:layout_centerInParent="true"
        android:background="@color/host_color_black"
        android:layout_width="match_parent"
        tools:visibility="invisible"
        android:layout_height="match_parent" >

        <ProgressBar
            android:id="@+id/host_ad_video_loading"
            android:layout_gravity="center"
            android:indeterminate="true"
            android:indeterminateDrawable="@drawable/host_video_ic_loading"
            android:layout_width="32dp"
            android:layout_height="32dp"/>
    </FrameLayout>

    <ImageView
        android:id="@+id/host_ad_back"
        android:src="@drawable/host_icon_back_white"
        android:padding="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
</com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout>