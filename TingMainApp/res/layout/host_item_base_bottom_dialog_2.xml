<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/host_bg_list_selector"
    android:padding="14dp"
    android:paddingLeft="20dp">

    <ImageView
        android:id="@+id/host_iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/host_tv_title"
        android:layout_centerVertical="true" />

    <ImageView
        android:id="@+id/host_red_dot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/host_iv_icon"
        android:layout_toRightOf="@id/host_iv_icon"
        android:src="@drawable/host_ic_red_dot"
        android:visibility="invisible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/host_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="26dp"
        android:layout_toRightOf="@+id/host_iv_icon"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:textSize="15sp"
        android:layout_centerVertical="true"
        tools:text="这是标题" />

    <TextView
        android:id="@+id/host_tv_title_extra"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@+id/host_tv_title"
        android:textColor="@color/host_color_f86442"
        android:textSize="15sp" />
</RelativeLayout>