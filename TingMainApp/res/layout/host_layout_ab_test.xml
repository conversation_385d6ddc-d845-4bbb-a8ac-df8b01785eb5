<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="100dp"
    android:orientation="vertical">


    <EditText
        android:id="@+id/config_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="实验name" />

    <Button
        android:id="@+id/query"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="查询" />

    <CheckBox
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="是否上报"
        android:id="@+id/upload_check"
        android:checked="true"
        />


    <RadioGroup
        android:id="@+id/check_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/check_int"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="int" />

        <RadioButton
            android:id="@+id/check_bool"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="bool" />

        <RadioButton
            android:id="@+id/check_string"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="string" />


    </RadioGroup>

    <TextView
        android:id="@+id/config_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="16sp"

        />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="当前xabtestIds为："
        android:textSize="16sp"

        />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/abtestids"
        android:textSize="16sp"
        />

</LinearLayout>
