<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ViewStub
        android:id="@+id/host_vs_share_vip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/host_layout_share_dialog_new_vip"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0" />

    <ViewStub
        android:id="@+id/host_vs_share_sale_track"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/host_layout_share_dialog_sale_trck"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0" />

</androidx.constraintlayout.widget.ConstraintLayout>