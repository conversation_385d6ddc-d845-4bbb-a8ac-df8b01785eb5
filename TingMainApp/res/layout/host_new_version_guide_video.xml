<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/host_new_version_gudie_root_lay"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ximalaya.ting.android.host.view.ad.PlayVideoUsSurfaceView
        android:id="@+id/host_ad_video"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/host_video_sound_control"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_margin="10dp"
        android:padding="10dp"
        android:src="@drawable/host_no_sound_for_wel"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/host_count_down_text"
        android:layout_width="wrap_content"
        android:minWidth="56dp"
        android:layout_height="29dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="15dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/host_welcome_skip_bg"
        android:gravity="center"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:text="跳过"
        android:textColor="@color/host_white"
        android:textSize="13dp"
        android:visibility="gone"
        tools:visibility="visible" />

</RelativeLayout>

