<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/host_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_share_pannel_dialog"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/host_fl_share"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical" />

    <View
        android:id="@+id/host_top_divider"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="12dp"
        android:background="@color/host_color_lineColor2_darkFill1" />

    <FrameLayout
        android:id="@+id/host_panel_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <Button
        android:id="@+id/host_dismiss"
        style="@style/host_style_text_t1"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:padding="0dp"
        android:text="取消"
        android:background="@color/host_transparent"
        android:textColor="@color/host_color_textColor" />

</LinearLayout>