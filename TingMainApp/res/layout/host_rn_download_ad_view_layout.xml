<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_2corner_ffffff_1f1f1f"
    android:orientation="vertical"
    android:paddingHorizontal="16dp"
    android:paddingTop="12dp"
    android:paddingBottom="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/main_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="安装热门应用赚金币"
            android:textColor="@color/host_color_666666_8d8d91"
            android:textFontWeight="500"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/main_change_task"
            android:drawablePadding="2dp"
            android:drawableLeft="@drawable/host_download_task_change"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="0"
            android:text="换一换"
            android:textColor="@color/host_color_C0C0C4_8D8D91"
            android:textFontWeight="500"
            android:textSize="12sp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="8.5dp"
        android:background="@color/host_color_f0f0f0_1f1f1f" />

    <RelativeLayout
        android:id="@+id/main_ad_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="11.5dp">


        <com.ximalaya.ting.android.framework.view.image.FlexibleRoundImageView
            android:id="@+id/main_ad_image"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginTop="2dp"
            app:flexible_round_corner="all"
            app:flexible_round_corner_radius="5dp"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            android:layout_toEndOf="@id/main_ad_image"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="72dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/main_ad_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:ellipsize="end"
                    android:maxEms="8"
                    android:maxLines="1"
                    android:textColor="@color/host_color_111111_ffffff"
                    android:textFontWeight="500"
                    android:textSize="14sp"
                    tools:text="应用名称啦啦啦啦啦啦啦啦啦啦啦啦啦啦" />

                <TextView
                    android:id="@+id/main_reward_coin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingVertical="3dp"
                    android:paddingHorizontal="4dp"
                    android:background="@drawable/host_bg_31corner_fafafa_33000000"
                    android:drawableLeft="@drawable/host_reward_coin_icon"
                    android:drawablePadding="2dp"
                    android:textColor="#ffE89821"
                    android:textSize="11sp"
                    tools:text="+1000" />

            </LinearLayout>

            <TextView
                android:id="@+id/main_app_developer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="72dp"
                android:layout_marginTop="2dp"
                android:visibility="gone"
                tools:visibility="visible"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/host_color_999999_acacaf"
                android:textSize="12sp"
                tools:text="行吟信息科技(上海)有限公司啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦啦" />

            <LinearLayout
                android:id="@+id/main_download_info_container"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <!-- 应用大小 -->
                <TextView
                    android:id="@+id/main_app_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/host_color_999999_acacaf"
                    android:textSize="10sp"
                    tools:text="123.94MB" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="8dp"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    android:background="@color/host_color_999999_acacaf" />

                <TextView
                    android:id="@+id/main_app_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/host_color_999999_acacaf"
                    android:textSize="10sp"
                    tools:text="8.98.0" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="8dp"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    android:background="@color/host_color_999999_acacaf" />

                <TextView
                    android:id="@+id/main_app_permission"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="权限"
                    android:textColor="#5A7EA7"
                    android:textSize="10sp" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="8dp"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    android:background="@color/host_color_999999_acacaf" />

                <TextView
                    android:id="@+id/main_app_privacy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="隐私"
                    android:textColor="#5A7EA7"
                    android:textSize="10sp" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="8dp"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    android:background="@color/host_color_999999_acacaf" />

                <TextView
                    android:id="@+id/main_app_introduction"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="介绍"
                    android:textColor="#5A7EA7"
                    android:textSize="10sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/main_ad_sub_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="72dp"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/host_color_999999_acacaf"
                android:textSize="12sp"
                tools:text="跳转应用市场安装APP"
                tools:visibility="visible"
                android:visibility="gone" />
        </LinearLayout>

        <TextView
            android:id="@+id/main_ad_button"
            android:layout_width="64dp"
            android:layout_height="28dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="5.5dp"
            android:background="@drawable/host_ad_action_btn_on_selected"
            android:gravity="center"
            android:text="去安装"
            android:textColor="#ffFFFFFF"
            android:textFontWeight="600"
            android:textSize="11sp" />

    </RelativeLayout>

</LinearLayout>
