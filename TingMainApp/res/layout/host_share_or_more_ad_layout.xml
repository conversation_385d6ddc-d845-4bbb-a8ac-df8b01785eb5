<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="match_parent"
    android:layout_height="45dp"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <ImageView
        android:id="@+id/host_share_ad_cover"
        android:layout_centerHorizontal="true"
        android:scaleType="fitXY"
        android:layout_width="307dp"
        android:layout_height="45dp" />

    <ImageView
        android:id="@+id/host_share_ad_sub_cover"
        android:layout_alignTop="@id/host_share_ad_cover"
        android:layout_alignLeft="@id/host_share_ad_cover"
        android:maxHeight="18dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/host_share_ad_mark"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:layout_marginRight="6dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignParentBottom="true"
        android:background="@color/host_color_e8e8e8_353535" />
</RelativeLayout>

