<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.adsdk.external.XmNativeAdContainer xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/read_ad_root_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#ffffff">

    <LinearLayout
        android:id="@+id/read_ad_click_root_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingVertical="12dp"
        android:paddingStart="16dp">

        <com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout
            android:layout_width="100dp"
            android:layout_height="56dp"
            app:corner="all"
            app:corner_radius="4dp">

            <com.ximalaya.ting.android.framework.view.image.FlexibleRoundImageView
                android:id="@+id/read_iv_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY"
                tools:background="@color/host_red" />

            <FrameLayout
                android:id="@+id/read_video_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ImageView
                android:id="@+id/read_iv_ad_tag"
                android:layout_width="wrap_content"
                android:layout_height="14dp"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/read_ting_read_ad_tag" />

        </com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginStart="12dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true">

                <TextView
                    android:id="@+id/read_tv_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="40dp"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#3d3d3d"
                    android:textSize="14dp"
                    tools:text="标题测试啦啦啦啦阿啦啦啦啦了标题测试啦啦啦啦阿啦啦啦啦了阿啦啦啦啦" />

                <TextView
                    android:id="@+id/read_ad_down_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/read_tv_title"
                    android:layout_marginTop="6dp"
                    android:includeFontPadding="false"
                    android:textColor="#307CF6"
                    android:textFontWeight="500"
                    android:textSize="12dp"
                    tools:text="立即查看" />

                <LinearLayout
                    android:id="@+id/read_ll_app_name_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:orientation="horizontal"
                    android:visibility="visible">

                    <TextView
                        android:id="@+id/read_tv_download_app_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:includeFontPadding="false"
                        android:maxEms="4"
                        android:maxLines="1"
                        android:textColor="#999999"
                        android:textSize="9dp"
                        tools:text="美团外卖" />

                    <View
                        android:layout_width="1dp"
                        android:layout_height="7dp"
                        android:layout_gravity="bottom"
                        android:layout_marginLeft="4dp"
                        android:layout_marginRight="4dp"
                        android:layout_marginBottom="2dp"
                        android:alpha="0.6"
                        android:background="#999999" />

                    <TextView
                        android:id="@+id/read_tv_download_dev_info"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:includeFontPadding="false"
                        android:maxEms="4"
                        android:maxLines="1"
                        android:textColor="#999999"
                        android:textSize="9dp"
                        tools:text="开发者啦阿拉蕾" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/read_download_developer_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_toEndOf="@+id/read_ll_app_name_container"
                    android:orientation="horizontal"
                    android:visibility="visible">

                    <View
                        android:layout_width="1dp"
                        android:layout_height="7dp"
                        android:layout_gravity="bottom"
                        android:layout_marginLeft="4dp"
                        android:layout_marginRight="4dp"
                        android:layout_marginBottom="2dp"
                        android:alpha="0.6"
                        android:background="#999999" />

                    <TextView
                        android:id="@+id/read_download_app_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:includeFontPadding="false"
                        android:maxWidth="30dp"
                        android:maxLines="1"
                        android:paddingTop="4dp"
                        android:textColor="#999999"
                        android:textSize="9dp"
                        tools:text="V9.0.34V9.0.34V9.0.34V9.0.34V9.0.34" />

                    <View
                        android:layout_width="1dp"
                        android:layout_height="7dp"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="4dp"
                        android:layout_marginBottom="2dp"
                        android:alpha="0.6"
                        android:background="#999999" />

                    <TextView
                        android:id="@+id/read_download_info_policy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:paddingHorizontal="4dp"
                        android:paddingTop="4dp"
                        android:text="隐私"
                        android:textColor="#5A7EA7"
                        android:textSize="9dp" />

                    <View
                        android:layout_width="1dp"
                        android:layout_height="7dp"
                        android:layout_gravity="bottom"
                        android:layout_marginBottom="2dp"
                        android:alpha="0.6"
                        android:background="#999999" />

                    <TextView
                        android:id="@+id/read_download_info_permission"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:paddingHorizontal="4dp"
                        android:paddingTop="4dp"
                        android:text="权限"
                        android:textColor="#5A7EA7"
                        android:textSize="9dp" />

                    <View
                        android:layout_width="1dp"
                        android:layout_height="7dp"
                        android:layout_gravity="bottom"
                        android:layout_marginBottom="2dp"
                        android:alpha="0.6"
                        android:background="#999999" />

                    <TextView
                        android:id="@+id/read_download_info_intro"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:paddingHorizontal="4dp"
                        android:paddingTop="4dp"
                        android:text="介绍"
                        android:textColor="#5A7EA7"
                        android:textSize="9dp" />
                </LinearLayout>
            </RelativeLayout>
        </RelativeLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/read_iv_close"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_gravity="end"
        android:paddingVertical="14dp"
        android:paddingStart="10dp"
        android:paddingEnd="18dp"
        android:scaleType="fitXY"
        android:src="@drawable/read_ting_read_ic_banner_close" />

</com.ximalaya.ting.android.adsdk.external.XmNativeAdContainer>