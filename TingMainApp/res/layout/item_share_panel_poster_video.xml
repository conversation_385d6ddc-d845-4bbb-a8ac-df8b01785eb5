<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    tools:ignore="ResourceName">

    <androidx.cardview.widget.CardView
        android:layout_width="120dp"
        android:layout_height="160dp"
        app:cardBackgroundColor="#537A9E"
        app:cardCornerRadius="6dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_share_video_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/host_iv_poster_cover_blur"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                app:corner_radius="4dp"
                android:alpha="0.1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/host_lottie_video_share"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@+id/host_iv_poster_cover"
                app:layout_constraintTop_toTopOf="@+id/host_iv_poster_cover"
                app:lottie_fileName="lottie/share/share_video_wave_anim.json"
                app:lottie_imageAssetsFolder="lottie/share"
                app:lottie_autoPlay="true"
                android:alpha="0.3"
                app:lottie_loop="true" />

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/host_iv_poster_cover"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginTop="33dp"
                android:scaleType="centerCrop"
                android:src="@drawable/host_ic_video_share_preview_cover"
                app:corner_radius="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/host_tv_album_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="6dp"
                android:layout_marginBottom="10dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:includeFontPadding="false"
                android:gravity="center"
                android:textColor="@color/host_color_white"
                android:textSize="11dp"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:text="《哈利·波特》第3部《哈利·波特与阿兹卡班囚徒》中文精..." />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.cardview.widget.CardView>


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:text="视频片段"
        android:textColor="@color/host_color_8f8f8f_80ffffff"
        android:textSize="12sp" />

</LinearLayout>