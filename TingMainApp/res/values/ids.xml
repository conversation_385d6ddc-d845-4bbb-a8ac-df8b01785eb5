<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item name="host_viewpager" type="id" />


    <item name="host_buried_points" type="id" />
    <item name="host_home_bottom_ad" type="id" />
    <item name="host_notity_pop_data" type="id" />

    <item name="host_id_live_stickynavlayout_topview" type="id" />
    <item name="host_id_live_stickynavlayout_indicator" type="id" />
    <item name="host_id_live_stickynavlayout_viewpager" type="id" />
    <item name="host_id_live_stickynavlayout_innerscrollview" type="id" />

    <item name="host_id_stickynavlayout_topview" type="id" />
    <item name="host_id_stickynavlayout_indicator" type="id" />
    <item name="host_id_stickynavlayout_content" type="id" />
    <item name="host_id_stickynavlayout_innerscrollview" type="id" />
    <item name="host_id_stickynavlayout_top_ad_content" type="id" />

    <item name="host_banner_img" type="id" />
    <item name="host_badge_view_layout" type="id" />
    <item name="host_red_dot_view_layout" type="id" />

    <item name="host_keyboard_layout_id" type="id" />
    <item name="host_video_item_view_layout" type="id" />

    <item name="host_btn_top" type="id" />
    <item name="host_lock_screen" type="id" />
    <item name="host_broadside_ad" type="id" />
    <item name="host_category_ad_index_flag" type="id" />

    <item name="host_firework_button_tag" type="id" />

    <item name="host_csj_ad_media_view" type="id" />

    <item name="host_gdt_ad_media_view" type="id" />
    <item name="host_xm_ad_media_view" type="id" />
    <item name="host_xm_ad_media_view_vertical" type="id" />
    <item name="host_baidu_ad_media_view" type="id" />
    <item name="host_home_login_hint_view" type="id" />
    <item name="host_sdk_skip_view" type="id" />
    <item name="host_action_btn_style" type="id"/>
    <item name="host_cur_resource_tag" type="id" />
    <item name="host_load_success" type="id" />
    <item name="host_refresh_loading" type="id" />
    <item name="host_click_down_up_xy" type="id" />
    <item name="host_ad_img_tag" type="id" />

    <item name="host_immersion_status_bar_view" type="id" />
    <item name="host_immersion_navigation_bar_view" type="id" />
    <item name="host_skip_layout" type="id" />

    <item name="host_video_item_layout" type="id"/>

    <item name="host_nine_grid_layout_time_id" type="id"/>
    <item name="host_nine_grid_gif_display_animation_end_id" type="id" />

    <item name="host_play_page_ad_unlock_vip" type="id" />

    <item name="host_iv_switch_sound_effect_status" type="id" />
    <item name="host_iv_switch_sound_effect_loading" type="id" />

    <!-- 涉及到mainbundle代码 改动较大 先不改成host前缀 -->
    <item name="main_tv_album_title" type="id" />
    <item name="main_tv_album_subtitle" type="id" />
    <item name="main_iv_album_cover" type="id" />
    <item name="main_album_border" type="id"/>
    <item name="main_tv_album_rank_num" type="id" />
    <item name="main_album_activity_123_2018" type="id" />
    <item name="main_banner_no_check_visable" type="id" />
    <item name="main_id_stickynavlayout_innerscrollview" type="id" />
    <item name="host_splash_ad_hint_goto_other_app_view" type="id"/>

    <item name="host_mine_list_item_tag" type="id" />
    <item name="host_recommend_focus_id_is_new_recommend" type="id" />

    <!--  BaseAdapter  -->
    <item name="BaseQuickAdapter_viewholder_support" type="id"/>
    <item name="BaseQuickAdapter_swiping_support" type="id"/>
    <item name="BaseQuickAdapter_dragging_support" type="id"/>
    <item name="BaseQuickAdapter_databinding_support" type="id"/>
    <item name="dynamic_tab_red_hint" type="id"/>
    <item name="host_tab_dynamic_drawable" type="id"/>
    <item name="host_tab_dynamic_red_hint_drawable" type="id"/>

    <item name="host_reward_video_ad_auto_click_tag" type="id"/>


    <item name="read_adapter_ex_header" type="id"/>
    <item name="read_adapter__viewholder_support" type="id"/>
    <item name="read_adapter__swiping_support" type="id"/>
    <item name="read_adapter__dragging_support" type="id"/>
    <item name="read_adapter__databinding_support" type="id"/>

    <item name="host_id_recommend_show_tag_tv" type="id"/>
    <item name="host_id_recommend_show_tag_list" type="id"/>
    <item name="host_id_recommend_show_tag_theme" type="id"/>
    <item name="host_id_recommend_show_tag_content" type="id"/>
    <item name="host_id_show_tag_cache_data" type="id"/>

    <item name="host_id_recommend_show_tag_cache" type="id"/>
    <item name="host_id_recommend_feed_track_cache" type="id"/>
    <item name="host_is_dark" type="id"/>

    <!--  brvh  -->
    <item name="BaseQuickAdapter_key_multi" type="id"/>
    <item name="BaseQuickAdapter_empty_view" type="id"/>

</resources>