package com.ximalaya.ting.android.activity.pay

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.WindowManager
import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.framework.BaseApplication.getMainActivity
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.host.data.model.pay.BocPayResultModel
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router.IBundleInstallCallback
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.pay.log.PayActionManager
import com.ximalaya.ting.android.pay.log.PayConstants
import com.ximalaya.ting.android.routeservice.RouterServiceManager
import com.ximalaya.ting.android.routeservice.service.bocpay.IBocPayManager
import com.ximalaya.ting.android.routeservice.service.pay.PayResult
import java.net.URLDecoder
import java.net.URLEncoder

/**
 * Create by {jian.kang} on 5/21/22
 * <AUTHOR>
 */
class BocPayResultActivity: FragmentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        saveWindow()
    }

    private fun saveWindow() {
        try {
            window.setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onResume() {
        super.onResume()
        intent?.data?.let {
            when (it.host) {
                "payresult" -> parsePayResult(intent)
                "bindSubWallet" -> parseBindSubWalletResult(intent)
            }
        }
        finish()
    }

    private fun parseBindSubWalletResult(intent: Intent) {
        if (intent?.data == null) {
            return
        }
        var url = intent!!.data!!.getQueryParameter("url")
        if (TextUtils.isEmpty(url)) {
            return
        }
        var uri = Uri.parse("iting://open?msg_type=14&url=${URLEncoder.encode(url)}")
        var toMainAppIntent = Intent(Intent.ACTION_VIEW)
        toMainAppIntent.data = uri
        startActivity(toMainAppIntent)
    }

    // iting://payresult/mobile?jsonData=123
    private fun parsePayResult(intent: Intent) {
        if (intent == null || intent.data == null || TextUtils.isEmpty(intent.data.toString())) {
            return
        }

        val uri = intent.data
        val resultModel = parseUri(uri) ?: return

        val mainActivity = getMainActivity()
        if (mainActivity != null) {
            // 直接回调前端支付成功页
            val service = RouterServiceManager.getInstance().getService(IBocPayManager::class.java)

            val result = PayResult()
            result.payType = PayConstants.TYPE_CCB_SDK_PAY.toString() + ""
            result.retCode = -1

            Router.getActionByCallback(Configure.BUNDLE_RN, object : IBundleInstallCallback {
                override fun onInstallSuccess(bundleModel: BundleModel?) {
                    if (service == null || service.callback == null) {
                        return
                    }

                    when (resultModel?.orderStatus) {
                        1 -> {
                            service?.callback?.onSuccess("${resultModel?.payAmount}")
                            result.retCode = PayResult.RESULT_SUCCESS
                            result.errorMsg = "支付成功"
                        }
                        else -> {
                            service?.callback?.onError("error", "支付失败")
                            result.errorMsg = "支付失败"
                        }
                    }

                    // 资源回收，防止内存泄漏
                    service?.unRegisterBocCallBack(service?.callback)
                    val log = PayActionManager.getInstance()?.curPayActionLog
                    if (result != null && log != null && log.type == PayConstants.TYPE_BOC_PAY) {
                        log.endPay(result)
                        PayActionManager.getInstance()?.curPayActionLog = null
                    }
                }

                override fun onLocalInstallError(t: Throwable?, bundleModel: BundleModel?) {
                }

                override fun onRemoteInstallError(t: Throwable?, bundleModel: BundleModel?) {
                }
            }, true, BundleModel.DOWNLOAD_SHOW_PROGRESS)
        } else {
            //走iting启动MainActivity
            Intent().let {
                it.data = Uri.parse("iting://open?msg_type=${AppConstants.PAGE_APP}")
                startActivity(it)
            }
        }
    }

    private fun parseUri(uri: Uri?): BocPayResultModel? {
        var model: BocPayResultModel? = null
        try {
            model = uri?.apply {
                Uri.parse(this.toString().trim())
            }.let {
                it?.getQueryParameter("jsonData").toString()
            }.apply {
                URLDecoder.decode(this, "utf-8")
            }.let {
                BocPayResultModel().apply {
                    this.parseJson(it)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return model
    }
}