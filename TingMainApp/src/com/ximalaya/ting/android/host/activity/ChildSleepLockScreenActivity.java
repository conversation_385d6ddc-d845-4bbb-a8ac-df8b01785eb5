package com.ximalaya.ting.android.host.activity;

import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;

/**
 * <AUTHOR>
 * @date 2020/01/20 19:52
 */
public class ChildSleepLockScreenActivity extends LockScreenActivity {
    private static final String TAG = "ChildSleepLockScreenActivity";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        mPlayPreIv.setVisibility(View.INVISIBLE);
        mPlayNextIv.setVisibility(View.INVISIBLE);
        mPlayBack15SecondIv.setVisibility(View.INVISIBLE);
        mPlayNext15SecondIv.setVisibility(View.INVISIBLE);
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
            String videoUrl = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.ITEM_SLEEP_AUTO_TIME_OFF_BACKGROUND, "");
            setShowVideoBg(R.drawable.main_pic_bg_sleep_mode, videoUrl);
        } else {
            setShowVideoBg(R.drawable.main_pic_bg_sleep_mode);
        }
        post(new Runnable() {
            @Override
            public void run() {
                if (!canUpdateUi()) {
                    return;
                }
                locationTip();
            }
        });
    }

    private void locationTip() {
//        if (!(mTimeTv.getLayoutParams() instanceof ViewGroup.MarginLayoutParams)) {
//            return;
//        } // 68
//        if (!(mSlideUnlockLottieView.getLayoutParams() instanceof ViewGroup.MarginLayoutParams)) {
//            return;
//        } // 36
//        if (!(mPlayActionContainerLl.getLayoutParams() instanceof ViewGroup.MarginLayoutParams)) {
//            return;
//        } // 36
//        int height = mChildSleepTipFl.getHeight();
//        int dp82 = BaseUtil.dp2px(ChildSleepLockScreenActivity.this, 82);
//        int dp40 = BaseUtil.dp2px(ChildSleepLockScreenActivity.this, 40);
//        if (height < dp82) {
//            int offset = dp82 - height;
//            int offset1 = offset - dp40;
//            ViewGroup.MarginLayoutParams timeLp = (ViewGroup.MarginLayoutParams) mTimeTv.getLayoutParams();
//            ViewGroup.MarginLayoutParams slideLp = (ViewGroup.MarginLayoutParams) mSlideUnlockLottieView.getLayoutParams();
//            ViewGroup.MarginLayoutParams playContainerLp = (ViewGroup.MarginLayoutParams) mPlayActionContainerLl.getLayoutParams();
//
//            if (offset1 > 0) {
//                timeLp.topMargin = timeLp.topMargin - dp40;
//                mTimeTv.setLayoutParams(timeLp);
//                slideLp.bottomMargin = slideLp.bottomMargin - offset1 / 2;
//                mSlideUnlockLottieView.setLayoutParams(slideLp);
//                playContainerLp.bottomMargin = playContainerLp.bottomMargin - offset1 / 2;
//                mPlayActionContainerLl.setLayoutParams(playContainerLp);
//            } else {
//                timeLp.topMargin = timeLp.topMargin - offset;
//                mTimeTv.setLayoutParams(timeLp);
//            }
//            if (mChildSleepTipFl.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
//                ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) mChildSleepTipFl.getLayoutParams();
//                lp.height = dp82;
//                mChildSleepTipFl.setLayoutParams(lp);
//            }
//        }
    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        // 复写 baseplayactivity的方法
        if (!canUpdateUi())
            return;
        if (curModel == null)
            return;
        // showPlayBtnLoadingStatus(true);
        showSoundLikeStatus();
        checkPreAndNextEnabled();
    }

    @Override
    protected void updateUiWhileResumeStopped() {
        updateUiForStopped();
    }

    @Override
    protected void onPlanFinished() {
        if (mPlanTerminateTipTv == null) {
            return;
        }
        HandlerManager.obtainMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                updateUiForStopped();
            }
        }, 1000);
    }

    private void updateUiForStopped() {
        if (canUpdateUi()) {
            if (!XmPlayerManager.getInstance(ChildSleepLockScreenActivity.this).isPlaying()) {
                if (mPlanTerminateTipTv != null) {
                    mPlanTerminateTipTv.setText("定时计划结束");
                    mPlanTerminateTipTv.setVisibility(View.VISIBLE);
                }
                disablePlayBtn();
            }
        }
    }

}
