/**
 * MySpaceActivity.java
 * com.ximalaya.ting.android.host.activity
 * <p/>
 * Function： TODO
 * <p/>
 * ver     date      		author
 * ──────────────────────────────────
 * 2015-11-25 		jack.qin
 * <p/>
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.host.activity.login;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.WindowManager;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.activity.web.WebActivityDuiBaMall;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.login.ILoginFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LoginActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.model.sso.SsoAuthInfo;
import com.ximalaya.ting.android.host.util.FullScreenUseNotchUtil;
import com.ximalaya.ting.android.host.util.LoginTraceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.loginservice.model.BindLoginInfoModel;
import com.ximalaya.ting.android.loginservice.verify.VerifyManager;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;


/**
 * <AUTHOR> on 2017/6/29.
 */
public class LoginActivity extends BaseFragmentActivity2 {

    public static final int REQUEST_OAUTH_SSO_AUTHORIZE = 0x100;
    public static final int REQUEST_OAUTH_SSO_FORGET_PASSWORD = 0x200;
    @Nullable
    private BaseFragment2 mGameOneKeyLoginFragment;

    /**
     * OAuth2SDK：登录是否来自OAuth2SDK
     */
    private boolean isFormOAuth2SDK;
    private boolean isFromXmAuth;
    private boolean isFromGuide;
    private boolean shouldBindPhone;
    private int loginBy;
    private boolean goToMySpacePage;
    private boolean gotoQuickLogin;
    private boolean gotoFreePasswordLogin;
    private boolean isFromXMAuthorizeActivity;

    @Override
    protected void onCreate(Bundle savedState) {
        Intent intent = getIntent();
        // 引导登录全屏展示
        if (intent != null && intent.getBooleanExtra(BundleKeyConstants.KEY_LOGIN_GOTO_GUIDE_LOGIN, false)) {
            FullScreenUseNotchUtil.setFullScreenWithSystemUi(getWindow(), true);
        }

        super.onCreate(savedState);
        if(!MmkvCommonUtil.getInstance(getContext()).getBoolean(PreferenceConstantsInHost.KEY_OPEN_SCREEN_SHOT_ENABLE)) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE);
        }


        if (intent != null) {
            shouldBindPhone = intent.getBooleanExtra("shouldBindPhone", false);
            isFormOAuth2SDK = intent.getBooleanExtra(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK, false);
            isFromXMAuthorizeActivity = intent.getBooleanExtra(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH_ACTIVITY, false);

            if(intent.hasExtra(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH)) {
                isFormOAuth2SDK = intent.getBooleanExtra(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH, false);
                isFromXmAuth = isFormOAuth2SDK;
            }

            isFromGuide = intent.getBooleanExtra(BundleKeyConstants.KEY_LOGIN_FROM_GUIDE, false);
            loginBy = intent.getIntExtra(BundleKeyConstants.KEY_LOGIN_BY, LoginByConstants.LOGIN_BY_FULL_SCREEN);
            goToMySpacePage = intent.getBooleanExtra(BundleKeyConstants.KEY_JUMP_TO_MYSPACE, false);
            gotoQuickLogin = intent.getBooleanExtra(BundleKeyConstants.KEY_USE_QUICK_LOGIN_KEY, false);
            gotoFreePasswordLogin = intent.getBooleanExtra(BundleKeyConstants.KEY_USE_FREE_PASSWORD_KEY, false);
        }

        try {
            Router.getActionByCallback(Configure.BUNDLE_LOGIN, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    

                    if (Configure.loginBundleModel.bundleName.equals(bundleModel.bundleName) &&
                            ToolUtil.activityIsValid(LoginActivity.this)) {
                        try {
                            onBundleInstallSuccess();
                        } catch (Exception e) {
                            onBundleError();
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    onBundleError();
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });


        } catch (Exception e) {
            onBundleError();
            e.printStackTrace();
        }

    }

    private void onBundleInstallSuccess() throws Exception {
        Intent intent = getIntent();
        if (intent != null) {
            boolean isFromGame = intent.getBooleanExtra(BundleKeyConstants.KEY_LOGIN_FROM_GAME_SDK, false);
            boolean isFromDuiBa = intent.getBooleanExtra(
                    WebActivityDuiBaMall.LOGIN_FROM_DUIBA, false);
            boolean isFromHotLine = intent.getBooleanExtra(AppConstants.LOGIN_FROM_HOTLINE, false);

            boolean needJumpToMainActivity = intent.getBooleanExtra(BundleKeyConstants.KEY_JUMP_MAIN, true);

            boolean gotoGuideLogin = intent.getBooleanExtra(BundleKeyConstants.KEY_LOGIN_GOTO_GUIDE_LOGIN, false);

            // 引导登录页
            if (gotoGuideLogin) {
                Bundle bundle = new Bundle();
                LoginTraceUtil.traceGotoLogin("new");
                Fragment mLoginFragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getGuideLoginFragment(bundle);
                addFragment(android.R.id.content, mLoginFragment);
                return;
            }

            // 一键登录
            if (gotoQuickLogin) {
                Bundle bundle = new Bundle();
                bundle.putBoolean(BundleKeyConstants.KEY_JUMP_MAIN, needJumpToMainActivity);
                bundle.putBoolean(AppConstants.LOGIN_FROM_HOTLINE, isFromHotLine);
                bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_GUIDE, isFromGuide);
                bundle.putString(BundleKeyConstants.KEY_OPEN_CHANNEL,
                        intent.getStringExtra(BundleKeyConstants.KEY_OPEN_CHANNEL));
                bundle.putBoolean(BundleKeyConstants.KEY_JUMP_TO_MYSPACE, goToMySpacePage);
                bundle.putInt(BundleKeyConstants.KEY_LOGIN_BY, loginBy);

                bundle.putParcelable(BundleKeyConstants.KEY_ONE_KEY_QUICK_PRE_VERIFY, intent.getParcelableExtra(BundleKeyConstants.KEY_ONE_KEY_QUICK_PRE_VERIFY));
                bundle.putParcelable(BundleKeyConstants.KEY_ONE_KEY_QUICK_LOGIN_INFO, intent.getParcelableExtra(BundleKeyConstants.KEY_ONE_KEY_QUICK_LOGIN_INFO));
                bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH_ACTIVITY, isFromXMAuthorizeActivity);
                Fragment mLoginFragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getOneKeyQuickLoginFragment(bundle);
                addFragment(android.R.id.content, mLoginFragment);
                return;
            }

            if (gotoFreePasswordLogin) {
                Bundle bundle = new Bundle();
                bundle.putBoolean(BundleKeyConstants.KEY_JUMP_MAIN, needJumpToMainActivity);
                bundle.putBoolean(AppConstants.LOGIN_FROM_HOTLINE, isFromHotLine);
                bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_GUIDE, isFromGuide);
                bundle.putString(BundleKeyConstants.KEY_OPEN_CHANNEL,
                        intent.getStringExtra(BundleKeyConstants.KEY_OPEN_CHANNEL));
                bundle.putBoolean(BundleKeyConstants.KEY_JUMP_TO_MYSPACE, goToMySpacePage);
                bundle.putInt(BundleKeyConstants.KEY_LOGIN_BY, loginBy);
                bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH_ACTIVITY, isFromXMAuthorizeActivity);
                Fragment mLoginFragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getFreePasswordLoginFragment(bundle);
                addFragment(android.R.id.content, mLoginFragment);
                return;
            }

            if (isFromDuiBa) {
                Bundle bundle = new Bundle();
                bundle.putBoolean(WebActivityDuiBaMall.LOGIN_FROM_DUIBA, true);
                bundle.putString("currentUrl",
                        intent.getStringExtra("currentUrl"));
                bundle.putBoolean(BundleKeyConstants.KEY_JUMP_MAIN, needJumpToMainActivity);
                Fragment mLoginFragment =
                        Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getLoginFragment(bundle);
                addFragment(android.R.id.content, mLoginFragment);
            } else if (isFromGame) { // 来自游戏SDK
                String packName = intent.getStringExtra("packId");
                if (packName != null) {
                    mGameOneKeyLoginFragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getGameOneKeyLoginFragment(packName);
                    addFragment(android.R.id.content, mGameOneKeyLoginFragment);
                }
            } else if (isFormOAuth2SDK) {
                // OAuth2SDK：检查主APP用户登录状态，分两种情况：
                // (1) 已登录，跳转至授权页面；
                // (2) 未登录，执行登录流程，登录成功后跳转至授权页面。
                if (UserInfoMannage.hasLogined()) {
                    Intent authorizeIntent = new Intent(LoginActivity.this, SsoAuthorizeActivity.class);
                    Bundle data = intent.getExtras();
                    SsoAuthInfo ssoAuthInfo;
                    if (data != null) {
                        ssoAuthInfo = SsoAuthInfo.parseBundleData(data);
                        authorizeIntent.putExtra(BundleKeyConstants.KEY_OAUTH_SDK_OAUTH_INFO, ssoAuthInfo);
                        startActivityForResult(authorizeIntent, REQUEST_OAUTH_SSO_AUTHORIZE);
                    } else {
                        Bundle error = new Bundle();
                        error.putString(BundleKeyConstants.KEY_OAUTH_SDK_ERROR, getResources().getString(R.string.host_sso_authorize_common_error_param_check_failed));
                        callAuthSDKError(error);
                    }
                } else {
                    boolean isQuickLogin = intent.getBooleanExtra("is_quick_login", false);
                    Bundle bundle = new Bundle();
                    bundle.putBoolean(BundleKeyConstants.KEY_JUMP_MAIN, needJumpToMainActivity);
                    bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK, isFormOAuth2SDK);
                    bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH, isFromXmAuth);
                    bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH_ACTIVITY, isFromXMAuthorizeActivity);
                    Bundle data = intent.getExtras();
                    SsoAuthInfo ssoAuthInfo;
                    if (data != null) {
                        ssoAuthInfo = SsoAuthInfo.parseBundleData(data);
                        bundle.putParcelable(BundleKeyConstants.KEY_OAUTH_SDK_OAUTH_INFO, ssoAuthInfo);
                        if (isQuickLogin) {
                            BaseFragment2 quickLoginFragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getSsoQuickLoginFragment(bundle);
                            addFragment(android.R.id.content, quickLoginFragment);
                        } else {
                            Fragment mLoginFragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getLoginFragment(bundle);
                            addFragment(android.R.id.content, mLoginFragment);
                        }
                    } else {
                        Bundle error = new Bundle();
                        error.putString(BundleKeyConstants.KEY_OAUTH_SDK_ERROR, getResources().getString(R.string.host_sso_authorize_common_error_param_check_failed));
                        callAuthSDKError(error);
                    }
                }
            } else if (shouldBindPhone) {
                Bundle bundle = intent.getBundleExtra("data");
                if (bundle != null) {
                    long uid = bundle.getLong("uid");
                    String bizKey = bundle.getString("bizKey");
                    boolean loginByEmail = bundle.getBoolean("loginByEmail");
                    BaseFragment2 fra = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().
                            getGetAndVerifySmsCodeFragment(uid, bizKey, true, loginByEmail, false);
                    addFragment(android.R.id.content, fra);
                }
            } else {
                Bundle bundle = new Bundle();
                bundle.putBoolean(BundleKeyConstants.KEY_JUMP_MAIN, needJumpToMainActivity);
                bundle.putBoolean(AppConstants.LOGIN_FROM_HOTLINE, isFromHotLine);
                bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_GUIDE, isFromGuide);
                bundle.putString(BundleKeyConstants.KEY_OPEN_CHANNEL,
                        intent.getStringExtra(BundleKeyConstants.KEY_OPEN_CHANNEL));
                bundle.putBoolean(BundleKeyConstants.KEY_JUMP_TO_MYSPACE, goToMySpacePage);
                bundle.putBoolean(BundleKeyConstants.KEY_LOGIN_FROM_XM_AUTH_ACTIVITY, isFromXMAuthorizeActivity);
                bundle.putInt(BundleKeyConstants.KEY_LOGIN_BY, loginBy);
                Fragment mLoginFragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getLoginFragment(bundle);
                addFragment(android.R.id.content, mLoginFragment);
            }
        } else {
            Bundle bundle = new Bundle();
            addFragment(android.R.id.content, Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getLoginFragment(bundle));
        }
    }

    private void onBundleError() {
        if (ConstantsOpenSdk.isDebug) {
            CustomToast.showFailToast("登录bundle安装失败");
        }

        if(!isFinishing()) {
            finish();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        /**
         * 微博登录必须回调的步骤
         */
        // OAuth2SDK：接收授权页面返回数据，分两种情况：
        // (1) 授权成功 resultCode == RESULT_OK，将access token等信息回传给OAuth2SDK
        // (2) 授权失败 resultCode == RESULT_CANCELED，将失败信息回传给OAuth2SDK
        if (requestCode == REQUEST_OAUTH_SSO_AUTHORIZE) {
            if (resultCode == RESULT_OK) {
                Intent intent = new Intent();
                Bundle bundle = data.getExtras();
                if (null == bundle) {
                    bundle = new Bundle();
                }
                bundle.putString(BundleKeyConstants.KEY_OAUTH_SDK_TING_PACKAGE_NAME, this.getPackageName());
                intent.putExtras(bundle);
                setResult(RESULT_OK, intent);
                finish();
            } else if (resultCode == RESULT_CANCELED) {  // 失败分两种情况：授权失败，取消授权
                if (data != null) { // 授权失败，
                    Bundle bundle = data.getExtras();
                    if (null == bundle) {
                        bundle = new Bundle();
                    }
                    bundle.putString(BundleKeyConstants.KEY_OAUTH_SDK_TING_PACKAGE_NAME, this.getPackageName());
                    callAuthSDKError(bundle);
                } else { // 取消授权
                    setResult(RESULT_CANCELED);
                    finish();
                }
            }
        } else if (requestCode == REQUEST_OAUTH_SSO_FORGET_PASSWORD) {  // OAuth2SDK：接收找回密码页面返回数据
            if (resultCode == RESULT_OK) {  //  找回密码成功
                if (data != null) {
                    isFormOAuth2SDK = data.getBooleanExtra(BundleKeyConstants.KEY_LOGIN_FROM_OAUTH_SDK, false);
                }
            }
            defaultOnActivityResult(requestCode, resultCode, data);
        } else {
            defaultOnActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void finish() {
        super.finish();
    }

    public void startFragment(Fragment fragment) {
        if (isFinishing() || isDestroyed())
            return;
        if (fragment == null) return;
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(android.R.id.content, fragment);
        ft.addToBackStack(null);
        ft.commitAllowingStateLoss();
    }


    @Override
    public void onBackPressed() {
        if (!onInterceptOnBackPressed()) {
            if (isFormOAuth2SDK) {
                setResult(RESULT_CANCELED);
                finish();
            } else if (isFromGuide) {
                int count = getSupportFragmentManager().getBackStackEntryCount();
                if (count == 0) {
                    toMainActivity();
                } else {
                    super.onBackPressed();
                }
            } else {
                super.onBackPressed();
            }
        }
    }

    private boolean onInterceptOnBackPressed() {
        BaseFragment2 curFragment = getCurFragment();
        if (curFragment != null) {
            if (TextUtils.equals(curFragment.getPageLogicNameForPublic() ,
                    ILoginFragmentAction.PAGE_LOGIC_GET_AND_VERIFY_SMS_CODE)
                    || TextUtils.equals(curFragment.getPageLogicNameForPublic() ,
                            ILoginFragmentAction.PAGE_LOGIC_CHOOSE_COUNTRY)
                    || TextUtils.equals(curFragment.getPageLogicNameForPublic() ,
                            ILoginFragmentAction.PAGE_LOGIC_SMS_VERIFICATION_CODE)
                    || TextUtils.equals(ILoginFragmentAction.PAGE_LOGIC_LOGIN,
                            curFragment.getPageLogicNameForPublic())
                    || TextUtils.equals(ILoginFragmentAction.PAGE_LOGIC_LOGIN_QUICK,
                            curFragment.getPageLogicNameForPublic())
                    || TextUtils.equals(ILoginFragmentAction.PAGE_LOGIC_LOGIN_GUIDE,
                            curFragment.getPageLogicNameForPublic())) {
                return curFragment.onBackPressed();
            }
        }
        return false;

    }


    private void defaultOnActivityResult(int requestCode, int resultCode, Intent data) {
        try {
            List<Fragment> fragments = getSupportFragmentManager().getFragments();
            if (!ToolUtil.isEmptyCollects(fragments)) {
                for (Fragment fragment : fragments) {
                    if (fragment.getUserVisibleHint() && fragment.isVisible() && !fragment.isHidden()) {
                        fragment.onActivityResult(requestCode, resultCode, data);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (mGameOneKeyLoginFragment != null) {
            mGameOneKeyLoginFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    private void callAuthSDKError(Bundle error) {
        Intent intent = new Intent();
        intent.putExtras(error);
        setResult(RESULT_CANCELED, intent);
        finish();
    }

    @Override
    public void onResume() {
        super.onResume();

        //如果当前登陆状态,自己关掉,防止登录页在最上面覆盖现场
        if (UserInfoMannage.hasLogined() && !isFormOAuth2SDK && !shouldBindPhone
                && loginBy != LoginByConstants.LOGIN_BY_CHANGE_USER) { // 授权登录需要入 Task 中
            finish();
            return;
        }
        checkBindInfo();

    }

    public void removeFragment(Fragment fragment) {
        FragmentManager manager = getSupportFragmentManager();
        FragmentTransaction ft = manager.beginTransaction();
        ft.remove(fragment);
        ((BaseFragment) fragment).setIsAdd(false);
        ft.commitAllowingStateLoss();
    }

    private void checkBindInfo() {
        BindLoginInfoModel model = ToolUtil.getLastBindPhoneInfo();
        if (model != null && model.getLoginInfoModel() != null) {
            try {
                BaseFragment2 curFragment = getCurFragment();
                if (curFragment != null && TextUtils.equals(curFragment.getPageLogicNameForPublic() ,
                        ILoginFragmentAction.PAGE_LOGIC_GET_AND_VERIFY_SMS_CODE) ) {
                    removeFragment(curFragment);
                }
                BaseFragment fragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getGetAndVerifySmsCodeFragment
                        (model.getLoginInfoModel().getUid(), model.getLoginInfoModel().getBizKey(), true, model.isLoginByEmail() ,isFormOAuth2SDK);
                if (fragment != null) {
                    startFragment(fragment);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Nullable
    private BaseFragment2 getCurFragment() {
        if (getSupportFragmentManager() == null)
            return null;
        if (getSupportFragmentManager().findFragmentById(android.R.id.content) != null && getSupportFragmentManager().findFragmentById(android.R.id.content) instanceof BaseFragment2) {
            return (BaseFragment2) getSupportFragmentManager().findFragmentById(android.R.id.content);
        }
        return null;
    }

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onSaveInstanceState(Bundle outState) {
//        super.onSaveInstanceState(outState);
//        //fix java.lang.IllegalStateException: Can not perform this action after onSaveInstanceState
//        invokeFragmentManagerNoteStateNotSaved();
    }

    //fix java.lang.IllegalStateException: Can not perform this action after onSaveInstanceState
    @Nullable
    private Method noteStateNotSavedMethod;
    @Nullable
    private Object fragmentMgr;
    private String[] activityClassName = {"Activity", "FragmentActivity"};

    private void invokeFragmentManagerNoteStateNotSaved() {
        //java.lang.IllegalStateException: Can not perform this action after onSaveInstanceState
        try {
            if (noteStateNotSavedMethod != null && fragmentMgr != null) {
                noteStateNotSavedMethod.invoke(fragmentMgr);
                return;
            }
            Class cls = getClass();
            do {
                cls = cls.getSuperclass();
                if (cls == null) {
                    break;
                }
            } while (!(activityClassName[0].equals(cls.getSimpleName())
                    || activityClassName[1].equals(cls.getSimpleName())));

            Field fragmentMgrField = prepareField(cls, "mFragments");
            if (fragmentMgrField != null) {
                fragmentMgr = fragmentMgrField.get(this);
                noteStateNotSavedMethod = getDeclaredMethod(fragmentMgr, "noteStateNotSaved");
                if (noteStateNotSavedMethod != null) {
                    noteStateNotSavedMethod.invoke(fragmentMgr);
                }
            }

        } catch (Exception ex) {
            Logger.e(ex);
            ex.printStackTrace();
        }
    }

    private Field prepareField(Class<?> c, String fieldName) throws NoSuchFieldException {
        while (c != null) {
            try {
                Field f = c.getDeclaredField(fieldName);
                f.setAccessible(true);
                return f;
            } finally {
                c = c.getSuperclass();
            }
        }
        throw new NoSuchFieldException();
    }

    @Nullable
    private Method getDeclaredMethod(Object object, String methodName, Class<?>... parameterTypes) {
        Method method = null;
        for (Class<?> clazz = object.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
            if (clazz == null) {
                return method;
            }
            try {
                method = clazz.getDeclaredMethod(methodName, parameterTypes);
                return method;
            } catch (Exception e) {
            }
        }
        return null;
    }
    //fix java.lang.IllegalStateException: Can not perform this action after onSaveInstanceState

    public void toMainActivity() {
        final Intent intent = MainActivity.getMainActivityIntent(this);
        if (getIntent() != null && getIntent().getData() != null) {
            intent.setData(getIntent().getData());
        }
        if (getIntent() != null && PlayTools.ACTION_LAUNCH_FROM_WIDGET.equals(getIntent().getAction())) {
            intent.putExtra("show_recommend_sound", true);
        }
        if (getIntent() != null && getIntent().getAction() != null && getIntent().getAction().contains(PlayerConstants.ACTION_NOTIFICATION_START_PLAY)) {
            intent.setAction(PlayerConstants.ACTION_NOTIFICATION_START_PLAY);
        }
        startActivity(intent);
        finish();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        try {
            VerifyManager.onConfigurationChanged(newConfig);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
