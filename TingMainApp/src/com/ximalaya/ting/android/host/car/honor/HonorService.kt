package com.ximalaya.ting.android.host.car.honor

import android.app.Service
import android.content.ComponentName
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.os.RemoteException
import android.util.Log
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.car.hicar.HiCarUtil
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.car.ICarFunctionAction
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.CarActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import java.util.Timer
import kotlin.concurrent.fixedRateTimer


class HonorService : Service() {
    private val TAG = "HonorService"
    private var UUID: String? = null
    private val START_BUNDLE = "start_hicar"
    private val STOP_BUNDLE = "stop_hicar"
    private var conditionTimer: Timer? = null
    private val cost = 5 *60 * 1000
    private var start = 0L
    override fun onCreate() {
        Log.i(TAG, "onCreate")
        HiCarUtil.isHicarInstall = false
        super.onCreate()
        checkDownLoad(true)
    }

    private fun isAgree(): Boolean {
        return MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
            .getBooleanCompat(PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PRIVACY_POLICY_AGREED_NEW)
    }
    private fun getStartBundle() {
        val result = Bundle()
        // 如果初始化音频服务成功则返回 0，如果因为权限未授予或者隐私声明未确认返回 100，其他错误返回 999
        // 如果初始化音频服务成功则返回 0，如果因为权限未授予或者隐私声明未确认返回 100，其他错误返回 999
        if (isAgree()) {
            sendMessageToClient(0)
            agreePrivacy()
        } else {
            sendMessageToClient(100)
            startTimer()
        }
        Log.i(TAG, "getStartBundle: result = $result")
    }

    private fun getNeedMessage() {
        val result = Bundle()
        // 如果初始化音频服务成功则返回 0，如果因为权限未授予或者隐私声明未确认返回 100，其他错误返回 999
        // 如果初始化音频服务成功则返回 0，如果因为权限未授予或者隐私声明未确认返回 100，其他错误返回 999
        if (isAgree()) {
            sendMessageToClient(0)
        } else {
            sendMessageToClient(100)
            startTimer()
        }
        Log.i(TAG, "getNeedMessage: result = $result")
    }

    private fun agreePrivacy() {
        XmPlayerManager.getInstance(applicationContext).playScene =
            XmPlayerService.PLAY_SCENE_TYPE_HICAR
        // hicar连接  其他事件
        // hicar连接  其他事件
        XMTraceApi.Trace()
            .setMetaId(54927)
            .setServiceId("others")
            .createTrace()
        try {
            startMediasession()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
    private fun startMediasession() {
        val serviceName = (Router.getActionRouter<CarActionRouter>(Configure.BUNDLE_CAR)
            ?.functionAction as? ICarFunctionAction)?.hiCarMediaSerVice
        Log.d(TAG, "startMediasession: serviceName = $serviceName")
        if (serviceName != null) {
            val intent = Intent(applicationContext,serviceName)
            applicationContext
                .startService(intent)
        }

    }

    private fun stopMediasession() {
        val serviceName = (Router.getActionRouter<CarActionRouter>(Configure.BUNDLE_CAR)
            ?.functionAction as? ICarFunctionAction)?.hiCarMediaSerVice
        Log.d(TAG, "stopService: serviceName = $serviceName")
        if (serviceName != null) {
            val intent = Intent(applicationContext,serviceName)
            applicationContext
                .stopService(intent)
        }
    }
    private fun startTimer() {
        if (conditionTimer != null) {
            return
        }
        start = System.currentTimeMillis()
        conditionTimer = fixedRateTimer(name = "condition-checker", initialDelay = 0, period = 1000) {
            if (System.currentTimeMillis() - start > cost) {
                conditionTimer?.cancel() // 停止定时器
                conditionTimer = null
                return@fixedRateTimer
            }
            if (isAgree()) {
                sendMessageToClient(301000)
                Log.i(TAG,"Condition met. Stopping the timer.")
                agreePrivacy()
                conditionTimer?.cancel() // 停止定时器
                conditionTimer = null
            } else {
                Log.i(TAG,"Condition not met. Continuing to check...")
            }
        }
    }

    private fun checkDownLoad(isCreate:Boolean = false): Boolean {
        try { //车载插件没有安装开始去静默安装
            if (!HiCarUtil.isInstalledCarBundle(this)) {
                Log.i(TAG, "need downLoad")
                Router.getActionByCallback(
                    Configure.BUNDLE_CAR,
                    object : Router.IBundleInstallCallback {
                        override fun onInstallSuccess(bundleModel: BundleModel) {
                            Log.i(TAG, "downLoad success")
                            if (!isCreate) {
                                getStartBundle()
                            }

                        }

                        override fun onLocalInstallError(t: Throwable, bundleModel: BundleModel) {
                            Log.i(TAG, "onLocalInstallError t=${t.message}")

                        }
                        override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {
                            Log.i(TAG, "onRemoteInstallError t=${t.message}")
                            if (!isCreate) {
                                getNeedMessage()
                            }
                        }
                    },
                    true,
                    BundleModel.DOWNLOAD_IN_BACKGROUND
                )
                return true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        Log.i(TAG, "not need downLoad")
        return false
    }

    private var clientMessenger: Messenger? = null

    // 处理来自客户端的消息
    private val handler: Handler = Handler(Looper.getMainLooper()) { msg ->
        Log.i(TAG, " entry handler callback msg = $msg")

        val clientMessage: Bundle? = msg.data
        Log.d(TAG, "Received message from client: $clientMessage")
        UUID = clientMessage?.getString("requestId")
        // 保存客户端的Messenger引用
        clientMessenger = msg.replyTo
        when (clientMessage?.getString("data")) {
            START_BUNDLE -> {
                if (!checkDownLoad()) {
                    getStartBundle()
                }
            }
            STOP_BUNDLE -> stopMediasession()
        }


        true
    }

    // 创建绑定的 Messenger
    private val messenger: Messenger = Messenger(handler)

    override fun onBind(intent: Intent?): IBinder? {
        val name = intent?.extras?.getString("packageName")
        Log.i(TAG, "onBind packagename = $name")
        if (name == "com.hihonor.auto") {
            return messenger.binder
        }
        return null
    }

    override fun onUnbind(intent: Intent?): Boolean {
        HiCarUtil.isHicarInstall = true
        clientMessenger = null
        stopMediasession()
        return super.onUnbind(intent)
    }

    // 服务端主动向客户端发送消息的方法
    private fun sendMessageToClient(message: Int) {
        clientMessenger?.let {
            val msg: Message = Message.obtain()
            val bundle = Bundle()
            bundle.putInt("result", message)
            bundle.putString("packageName", applicationContext.packageName)
            if (UUID != null) {
                bundle.putString("requestId", UUID)
            }
            msg.data = bundle
            Log.i(TAG, "sendMessageToClient: msg.data = ${msg.data}")
            try {
                it.send(msg)
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }
    }

}