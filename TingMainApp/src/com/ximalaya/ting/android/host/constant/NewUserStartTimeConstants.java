package com.ximalaya.ting.android.host.constant;

public class NewUserStartTimeConstants {
    public static boolean needTrack = false;
    /**
     * native   0;
     * h5       1;
     * flutter  2;
     * rn       3;
     * dsl      4;
     */
    public static int pageType = 0; //根据服务端控制使用native还是rn展示兴趣卡片
    public static long sAgreeTime = -1; //新用户点击同意协议的时间点
    public static long sAcOnCreateTime = -1; //启动activity-Mainactivity-onCreate方法入口时间
    public static long sAcCreateToInterestCardStartLogicCostTime = -1; //从oncCreate到开始判断新用户兴趣卡片的时间
    public static long sLotteryNetCostTime = -1; //请求抽奖用户网络数据的时间 - 如果是抽奖用户不需要弹兴趣卡片
    public static long sInterestCardNetCostTime = -1; //开始请求新用户兴趣卡片网络数据的时间
    public static long sInterestCardStartLoadTime = -1; //新用户兴趣卡片开始加载的时间
    public static long sEndCostTimeInFirstLoad = -1; //新用户且ab到优先加载兴趣卡片，兴趣卡片可见的时间点
    public static long sEndTimeInFirstLoad = -1; //新用户且ab到优先加载兴趣卡片，兴趣卡片可见的时间点
}
