package com.ximalaya.ting.android.host.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import com.ximalaya.ting.android.host.manager.QuickListenForceItem
import com.ximalaya.ting.android.host.model.album.AlbumVideoInfoModel
import com.ximalaya.ting.android.host.model.play.VideoBaseInfo

/**
 * Created by nali on 2025/6/25.
 * <AUTHOR>
 */
data class QuickListenResultModel(
    val data: List<QuickListenModel>? = emptyList(),
    val ret: Int? = 0,
    val showToast: Boolean? = false
)

enum class QuickElementType(val elementType: String, val desc: String, val viewType: Int) {
    XIMA_TEN_CARD("XimaTen", "每日十条", 1),
    AUDIO_CARD("Track", "声音卡片", 2),
    LOADING_CARD("loading", "加载中", 3),
    AGENT_GUIDE_CARD("agentGuide", "AI引导卡", 4),
    AGENT_RADIO_CARD("AgentRadio", "AI内容卡", 2), // 跟声音卡张的基本一样
    TRACK_COLLECTION_CARD("TrackCollection", "合集卡", 2), // 跟声音卡张的基本一样
    VIDEO("Video", "小说种草卡", 5)
}

val QuickElementTypeMap = hashSetOf<String>().apply {
    add(QuickElementType.XIMA_TEN_CARD.elementType)
    add(QuickElementType.AUDIO_CARD.elementType)
    add(QuickElementType.LOADING_CARD.elementType)
    add(QuickElementType.AGENT_GUIDE_CARD.elementType)
    add(QuickElementType.AGENT_RADIO_CARD.elementType)
    add(QuickElementType.TRACK_COLLECTION_CARD.elementType)
    add(QuickElementType.VIDEO.elementType)
}

data class QuickListenModel(
    val anchor: Anchor? = null,
    val auditStatus: Int? = 0,
    val bizType: String? = "",
    val contentType: String? = "",
    val count: Count? = null,
    val cover: String? = "",
    val createdAt: Long? = 0L,
    val digStatus: Int? = 0,
    val duration: Long? = 0,    // 单位s
    val elementType: String? = "",
    val endAt: Long? = 0,
    val extraInfo: ExtraInfo? = null,
    val id: Long? = 0,
    val interact: Interact? = null,
    val platform: String? = "",
    val recReason: String? = "",
    val refId: Long? = 0,
    val startAt: Long? = 0,
    val status: Status? = null,
    val subElements: List<QuickListenModel>? = emptyList(),
    val subTitle: String? = "",
    val summary: String? = "",
    val surElement: SurElement? = null,
    val title: String? = "",
    val ubtV2: Map<String, String>? = null,
    val updatedAt: Long? = 0L,
    var sug: String? = "",
    var metaDataTags: List<MetaDataTag>? = null,
    var sugAnimator: Boolean? = false,
    var totalDuration: Long? = 0L,  // 单位s
    var ximaTenAnimatoring: Boolean? = false,
    var localExt: LocalExt? = null,
    var pageState: Int? = 0, // 0 未设置过 1 没有在可视,并且已经设置过, 2 在可视,并且设置过
    var parentElementType: String? = "",
    var videoInfoRequesting: Boolean = false,
    var videoBaseInfo: VideoBaseInfo?,
    var albumVideoInfo: AlbumVideoInfoModel.AlbumVideoInfo?,
    var videoHintShow: Boolean = false,
    var showVideoOverHinting: Boolean = false,
    var showVideoOverHinted: Boolean = false,
    var tabId: Long? = 0, // 本地设置
    var videoScaleValue: Float
) {
    fun isTrackCollect(): Boolean {
        return elementType == QuickElementType.TRACK_COLLECTION_CARD.elementType
                && (subElements?.size ?: 0) > 0
    }

    fun isAgentRadio(): Boolean {
        return elementType == QuickElementType.AGENT_RADIO_CARD.elementType
    }

    fun isXimaTen(): Boolean {
        return elementType == QuickElementType.XIMA_TEN_CARD.elementType
    }

    fun isAgentGuide(): Boolean {
        return elementType == QuickElementType.AGENT_GUIDE_CARD.elementType
    }

    fun isVideo(): Boolean {
        return elementType == QuickElementType.VIDEO.elementType
    }

    fun isVideoCollect(): Boolean {
        return isVideo() && (subElements?.size ?: 0) > 0
    }
}

data class TrackCollectionInfo(
    val trackCollectionType: Int? = 0,
    val entranceContent: String? = "",
    val nextPlotOverview: String? = "",
    val fullLandingPage: String? = ""
)

data class MetaDataTag(
    val tag: String? = "",
    val trackingId: Long? = 0
)

data class Anchor(
    val following: Long? = 0,
    val level: Int? = 0,
    val logo: String? = "",
    @SerializedName("nickName", alternate = ["nickname"])
    val nickName: String? = "",
    val summary: String? = "",
    val uid: Long? = 0
)

data class Count(
    val like: Long? = 0,
    val play: Long? = 0,
    val trackCollectionSize: Long? = 0
)

data class ExtraInfo(
    val aigc: Boolean? = false,
    val createdAt: Long? = 0L,
    val forceInsertIndex: Int? = 0,
    val id: Long? = 0,
    var isFirstTrack: Boolean? = false,
    var isLike: Boolean? = false,
    var likeStatus: Int? = -1,  // 0 未点赞 1 点赞 2 点踩
    val trackCollectionType: Int? = 0,
    val updatedAt: Long? = 0L,
    val version: Int? = 0,
    val wraps: List<Wrap>? = emptyList(),
    var recReason: RecReason? = null,
    val startTime: Int? = 0,
    var isCollect: Boolean? = false,
    val isPureMusic: Boolean? = false,
    val trackCollectionInfo: TrackCollectionInfo? = null,
    val forceItem: QuickListenForceItem? = null,
    val nextLandingPage: String? = ""
)

data class RecReason(
    val text: String? = "",
    val suffix: String? = "",
    val refId: Long? = 0
)

data class Interact(
    var subscribed: Int? = 0
)

data class Status(
    val isFree: Int? = 0,
    val isPaid: Int? = 0,
    val decoupleStatus: Int? = 0,
    val isFinished: Int? = 0,
)

data class Wrap(
    val auditStatus: Int? = 0,
    val bu: String? = "",
    val channel: String? = "",
    val contentId: Long? = 0,
    val createdAt: Long? = 0L,
    val deleteStatus: String? = "",
    val id: Long? = 0,
    val platform: String? = "",
    val sourceApp: String? = "",
    val summary: String? = "",
    val updatedAt: Long? = 0L,
    val wrapLocationCode: String? = ""
)

data class SurElement(
    val anchor: Anchor? = null,
    val auditStatus: Int? = 0,
    val bizType: String? = "",
    val categoryId: Long? = 0,
    val contentType: String? = "",
    val cover: String? = "",
    val createdAt: Long? = 0L,
    val digStatus: Int? = 0,
    val extraInfo: ExtraInfo? = null,
    val id: Long? = 0,
    val interact: Interact? = null,
    val landingPage: String? = "",
    val refId: Long? = 0,
    val status: Status? = null,
    val summary: String? = "",
    val title: String? = "",
    val updatedAt: Long? = 0L,
    var metaDataTags: List<MetaDataTag>? = null,
)

@Keep
data class LocalExt(
    val tabName: String? = "",
    val positionNew: Int? = 0,
    val itemPosition: Int? = 0,
    val showFrom: String? = "",
)

data class Behavior(
    var refId: Long? = 0,                // 声音ID
    var bizType: String? = "",           // 声音类型
    var behaveType: String? = "",        // 行为类型, 播放/点赞/负反馈
    var duration: Long? = 0,             // 声音时长, 单位s
    var playDuration: Long? = 0,         // 播放时长, 单位s
    var ts: Long? = 0,                   // 时间戳

    var playStartTime: Long? = 0,        // 播放开始时间
    var playPauseTime: Long? = 0,        // 播放暂停时间
    var playBeforePauseTime: Long? = 0,  // 播放暂停前时长
    var playEndTime: Long? = 0,          // 播放结束时间
    var totalPauseTime: Long? = 0,       // 累计暂停时间
)