package com.ximalaya.ting.android.host.data.model.album

import java.io.Serializable

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2023/4/25
 */
data class FollowRecommendParam(
    val currPage: String,
    val anchorId: Long
) : Serializable {

    fun getPageType(): String {
        return when (currPage) {
            "newPlay" -> "播放页"
            "album" -> "售后"
            else -> "售前"
        }
    }

    fun isPlayPage(): Boolean {
        return currPage == "newPlay"
    }

    fun getPaidType(isPaid: Boolean): String {
        return if (isPaid) "付费" else "免费"
    }
}