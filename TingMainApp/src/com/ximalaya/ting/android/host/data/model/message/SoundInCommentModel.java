package com.ximalaya.ting.android.host.data.model.message;

import com.ximalaya.ting.android.host.model.base.BaseModel;

 /**
  * <AUTHOR>
  */
public class SoundInCommentModel extends BaseModel {
    private long trackId;// ":20951, //声音 trackId
    private long albumId;// ":20951, //声音 trackId
    private String trackTitle;// ":"啦啦啦啊", //声音的标题
    private String albumTitle;
    private long trackUid;// ":"", //声音拥有者
    private long albumUid;// ":"", //声音拥有者
    private String trackNickname;// ":"",
    private String trackCoverPath;// ":"", //声音图片地址
    private String albumCoverPath;// ":"", //声音图片地址
    private long commentId;// ":2046, //本条评论的ID，commentId
    private Double second;// ":77.53 //评论的时间点
    private long pCommentId;// ":3717, //被评论的评论的id
    private String pCommentContent;// ":"的打碟" //被评论的评论内容
    private long albumCommentId;// ":3717, //被评论的评论的id
    private long parentalbumCommentId;// ":3717, //被评论的评论的id
    private String parentalbumCommentContent;// 被评论的评论内容

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public long getAlbumUid() {
        return albumUid;
    }

    public void setAlbumUid(long albumUid) {
        this.albumUid = albumUid;
    }

    public String getAlbumCoverPath() {
        return albumCoverPath;
    }

    public void setAlbumCoverPath(String albumCoverPath) {
        this.albumCoverPath = albumCoverPath;
    }

    public long getpCommentId() {
        return pCommentId;
    }

    public void setpCommentId(long pCommentId) {
        this.pCommentId = pCommentId;
    }

    public String getpCommentContent() {
        return pCommentContent;
    }

    public void setpCommentContent(String pCommentContent) {
        this.pCommentContent = pCommentContent;
    }

    public long getAlbumCommentId() {
        return albumCommentId;
    }

    public void setAlbumCommentId(long albumCommentId) {
        this.albumCommentId = albumCommentId;
    }

    public long getParentalbumCommentId() {
        return parentalbumCommentId;
    }

    public void setParentalbumCommentId(long parentalbumCommentId) {
        this.parentalbumCommentId = parentalbumCommentId;
    }

    public long getCommentId() {
        return commentId;
    }

    public String getPcommentContent() {
        return pCommentContent;
    }

    public long getPcommentId() {
        return pCommentId;
    }

    public Double getSecond() {
        return second;
    }

    public String getTrackCoverPath() {
        return trackCoverPath;
    }

    public long getTrackId() {
        return trackId;
    }

    public String getTrackNickname() {
        return trackNickname;
    }

    public String getTrackTitle() {
        return trackTitle;
    }

    public long getTrackUid() {
        return trackUid;
    }

    public void setCommentId(long commentId) {
        this.commentId = commentId;
    }

    public void setPcommentContent(String pCommentContent) {
        this.pCommentContent = pCommentContent;
    }

    public void setPcommentId(long pcommentId) {
        this.pCommentId = pcommentId;
    }

    public void setSecond(Double second) {
        this.second = second;
    }

    public void setTrackCoverPath(String trackCoverPath) {
        this.trackCoverPath = trackCoverPath;
    }

    public void setTrackId(long trackId) {
        this.trackId = trackId;
    }

    public void setTrackNickname(String trackNickname) {
        this.trackNickname = trackNickname;
    }

    public void setTrackTitle(String trackTitle) {
        this.trackTitle = trackTitle;
    }

    public void setTrackUid(long trackUid) {
        this.trackUid = trackUid;
    }

    public String getAlbumTitle() {
        return albumTitle;
    }

    public void setAlbumTitle(String albumTitle) {
        this.albumTitle = albumTitle;
    }

    public String getParentalbumCommentContent() {
        return parentalbumCommentContent;
    }

    public void setParentalbumCommentContent(String parentalbumCommentContent) {
        this.parentalbumCommentContent = parentalbumCommentContent;
    }

}
