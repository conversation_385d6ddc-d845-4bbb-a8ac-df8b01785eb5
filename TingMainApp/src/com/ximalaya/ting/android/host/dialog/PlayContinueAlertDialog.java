package com.ximalaya.ting.android.host.dialog;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.ColorInt;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;

public class PlayContinueAlertDialog extends BaseDialogFragment {

    private static final String TAG_DIALOG = "community_alert_dialog";

    private TextView mTvTitle;

    private TextView mTvMessage;

    private TextView mBtnOk;

    private View vDivider;

    private TextView mBtnCancel;

    private String mTitle;

    private String mMessage;
    private int mFontSizeSp;

    private String mOkLabel;
    private int mOkTextColor;
    private IHandleOk mOnOkClick;

    private String mCancelLabel;
    private int mCancelTextColor;
    private IHandleOk mOnCancelClick;

    private int mShowBtnCount = 2;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {

        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);

        if (getDialog().getWindow() != null) {
            getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }

        View content = inflater.inflate(R.layout.main_play_continue_alert, container, false);

        mTvTitle = (TextView) content.findViewById(R.id.main_tv_alert_title);

        if (TextUtils.isEmpty(mTitle)) {
            mTvTitle.setVisibility(View.GONE);
        } else {
            mTvTitle.setVisibility(View.VISIBLE);
            mTvTitle.setText(mTitle);
        }

        mTvMessage = (TextView) content.findViewById(R.id.main_tv_alert_message);

        mTvMessage.setText(mMessage);

        if (mFontSizeSp > 0) {
            mTvMessage.setTextSize(TypedValue.COMPLEX_UNIT_SP, mFontSizeSp);
        }

        mBtnOk = (TextView) content.findViewById(R.id.main_alert_ok);

        mBtnOk.setText(mOkLabel);
        mBtnOk.setTextColor(mOkTextColor);
        mBtnOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PlayContinueAlertDialog.this.dismiss();

                if (mOnOkClick != null) {
                    mOnOkClick.onReady();
                }
            }
        });

        vDivider = content.findViewById(R.id.main_alert_btn_divider);

        mBtnCancel = (TextView) content.findViewById(R.id.main_alert_cancel);

        mBtnCancel.setText(mCancelLabel);
        mBtnCancel.setTextColor(mCancelTextColor);
        mBtnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PlayContinueAlertDialog.this.dismiss();

                if (mOnCancelClick != null) {
                    mOnCancelClick.onReady();
                }
            }
        });

        if (mShowBtnCount == 1) {
            vDivider.setVisibility(View.GONE);
            mBtnCancel.setVisibility(View.GONE);
        }

        return content;
    }

    public void showConfirm(FragmentManager fm) {
        mShowBtnCount = 1;

        show(fm, TAG_DIALOG);
    }

    public void showAlert(FragmentManager fm) {
        mShowBtnCount = 2;

        show(fm, TAG_DIALOG);
    }

    public void setTitle(String title) {
        mTitle = title;
    }

    public void setMessage(String message) {
        mMessage = message;
    }

    public void setMessage(String message, int fontSizeSp) {
        mMessage = message;

        mFontSizeSp = fontSizeSp;
    }

    public void setOkBtn(String label, IHandleOk onClick) {
        setOkBtn(label, Color.BLACK, onClick);
    }

    public void setOkBtn(String label, @ColorInt int textColor, final IHandleOk onClick) {
        if (BaseFragmentActivity.sIsDarkMode) {
            textColor = 0xffcfcfcf;
        }
        mOkLabel = label;
        mOkTextColor = textColor;
        mOnOkClick = onClick;
    }

    public void setCancelBtn(String label, IHandleOk onClick) {
        setCancelBtn(label, Color.BLACK, onClick);
    }

    public void setCancelBtn(String label, @ColorInt int textColor, final IHandleOk onClick) {
        if (BaseFragmentActivity.sIsDarkMode) {
            textColor = 0xffcfcfcf;
        }
        mCancelLabel = label;
        mCancelTextColor = textColor;
        mOnCancelClick = onClick;
    }
}
