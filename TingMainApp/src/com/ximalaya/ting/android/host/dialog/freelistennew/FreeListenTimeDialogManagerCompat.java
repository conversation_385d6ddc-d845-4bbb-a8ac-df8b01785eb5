package com.ximalaya.ting.android.host.dialog.freelistennew;

import android.text.TextUtils;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.dialog.PointCenterHomeDialogInterceptor;
import com.ximalaya.ting.android.host.dialog.freelisten.FreeListenDialogInterceptor;
import com.ximalaya.ting.android.host.dialog.freelisten.FreeListenDialogTrace;
import com.ximalaya.ting.android.host.dialog.freelisten.FreeListenOutsideStationDialogInterceptor;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.play.AddListenTimeBuilder;
import com.ximalaya.ting.android.host.manager.play.FreeListenTimeManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 会员主导的畅听首页弹窗管理类
 *
 */
public class FreeListenTimeDialogManagerCompat {

    public static final String TAG = "FreeListenTimeDialogManagerCompat";
    public static int limitFreeLogTime;

    private static final FreeListenDialogInterceptor[] interceptors = {
            // 积分首页引流弹窗
            new PointCenterHomeDialogInterceptor(),
            // 畅听站外承接弹窗
            new FreeListenOutsideStationDialogInterceptor(),
            //新老用户通用的唤端弹窗
            new FreeListenInspireAdDialogInterceptor(),
            //赠送时长的弹窗，包括全天的
            new FreeListenGuideWatchADDialogInterceptor(),
            //专辑信息弹窗
            new FreeListenAlbumInfoDialogInterceptor(),
            //获取1天vip弹窗
            new FreeListenGetVipDialogInterceptor(),
            // 新看视频获得全天免费时长
            new FreeListenAllDayDialogInterceptor(),
            // 新看视频获得全天免费时长唤端弹窗
            new FreeListenAllDayInspireAdDialogInterceptor()
    };

    /**
     * 检测是否要出畅听广告相关弹窗
     * @param source
     * @return
     */
    public static boolean checkFreeListenDialogEnable(String source) {

        boolean enable = false;
        for (FreeListenDialogInterceptor interceptor : interceptors) {
            if (interceptor.shouldInterceptShow()) {
                enable = true;
                break;
            }
        }
        if ("LimitFree".equals(source)) {
            // 这里防止限免弹窗无限上报
            if (limitFreeLogTime >= 3) {
                return enable;
            } else {
                limitFreeLogTime++;
            }
        }
        Logger.i(TAG, "checkFreeListenDialogEnable 是否有新畅听弹窗 " + enable + ", source = " + source);
        if (!enable) {
            FreeListenDialogTrace.traceEvent("checkFreeListenDialogEnable = false, source = " + source);
        }
        return enable;
    }

    public static void notifyFreeListenDialogAction() {

        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                for (FreeListenDialogInterceptor interceptor : interceptors) {
                    if (interceptor.shouldInterceptShow()) {

                        FreeListenHomeDialogManager.log("showDialog: " + interceptor.getClass().getName());

                        interceptor.showDialog();
                    }
                }
            }
        });
        FreeListenHomeDialogManager.log("notifyFreeListenDialogAction");
    }

    public static boolean hasDialogShowed() {
        for (FreeListenDialogInterceptor interceptor : interceptors) {
            if (interceptor.hasDialogShow()) {
                return true;
            }
        }
        return false;
    }

    public static void notifyFragmentResume() {
        for (FreeListenDialogInterceptor interceptor : interceptors) {
            if (interceptor instanceof FreeListenInspireAdDialogInterceptor) {
                ((FreeListenInspireAdDialogInterceptor) interceptor).onRecommendFragmentResume();
            }
            if (interceptor instanceof FreeListenAllDayInspireAdDialogInterceptor) {
                ((FreeListenAllDayInspireAdDialogInterceptor) interceptor).onRecommendFragmentResume();
            }
        }
    }

    public static void reportFreeListenDialogShow(int popupType, long responseId, int adId, long adResponseId) {
        reportFreeListenDialogShow(popupType, responseId, adId, adResponseId, -1);
    }

    public static void reportFreeListenDialogShow(int popupType, long responseId, int adId, long adResponseId, int actionType) {
        JSONObject params = new JSONObject();
        try {
            params.put("popupType", popupType);
            if (adId != 0) {
                params.put("adId", adId);
            }
            if (adResponseId != 0) {
                params.put("adResponseId", adResponseId);
            }
            params.put("responseId", responseId);
            params.put("sceneId", "");
            String sceneId = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_SCENE_ID, "");
            if (!TextUtils.isEmpty(sceneId)) {
                params.put("sceneId", Integer.parseInt(sceneId));
            }
            String expId = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXP_ID, "");
            if (!TextUtils.isEmpty(expId)) {
                params.put("groupId", Integer.parseInt(expId));
            }
            params.put("localDuration", FreeListenTimeManager.getListenTime(BaseApplication.getMyApplicationContext()));
            if (actionType >= 0) {
                params.put("actionType", actionType);
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        CommonRequestM.reportFreeListenHomeDialogShow(params, null);
    }

    public static void dailyRewardDurationWhenDialogShow(FreeListenTimeManager.PopUpData popUpData) {
        if (popUpData == null || popUpData.actionType != 1) return;
        AddListenTimeBuilder builder = new AddListenTimeBuilder();
        builder.type = AddListenTimeBuilder.IType.HOME_DIALOG_GIF;
        builder.rewardDurType = AddListenTimeBuilder.IRewardDurType.REWARD_FROM_GIFT_DIALOG;  // 弹窗赠送
        builder.rewardTime = popUpData.baseDuration;  // 弹窗赠送
        FreeListenTimeManager.addListenTime(builder);
    }
}
