package com.ximalaya.ting.android.host.drivemode.model;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by {jian.kang} on 2023/6/8
 *
 * <AUTHOR>
 */
public class DriveModeDailyNewModel extends DriveModeSceneModel {
    public List<DailyNewsSceneModel> modelList;
    public String requestId = "";

    public DriveModeDailyNewModel(JSONArray jsonArray, String requestId) {
        if (jsonArray == null || jsonArray.length() <= 0) {
            return;
        }

        try {
            this.requestId = requestId;
            for (int i = 0; i < jsonArray.length(); i++) {
                if (jsonArray.get(i) == null) {
                    continue;
                }
                if (modelList == null) {
                    modelList = new ArrayList<>();
                }
                String content = jsonArray.get(i).toString();
                JSONObject jsonObject = new JSONObject(content);
                modelList.add(new DailyNewsSceneModel(jsonObject, requestId));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static class DailyNewsSceneModel {
        public String bizType;
        public String cover;
        public long id;
        public String title;
        public String requestId;

        public DailyNewsSceneModel(JSONObject jsonObject, String requestId) {
            if (jsonObject == null) {
                return;
            }

            try {
                this.requestId = requestId;
                if (jsonObject.has("bizType")) {
                    bizType = jsonObject.optString("bizType");
                }
                if (jsonObject.has("cover")) {
                    cover = jsonObject.optString("cover");
                }
                if (jsonObject.has("id")) {
                    id = jsonObject.optLong("id");
                }
                if (jsonObject.has("title")) {
                    title = jsonObject.optString("title");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
