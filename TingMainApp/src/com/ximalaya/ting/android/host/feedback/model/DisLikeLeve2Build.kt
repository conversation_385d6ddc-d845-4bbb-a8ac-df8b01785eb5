package com.ximalaya.ting.android.host.feedback.model

import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog

class DisLikeLeve2Build {
    var isFromAd: Boolean = false // 是否来自广告  非广告有一个标题栏
    var isFromAdNature: Boolean = false // 是否来自广告听单的自然内容
    var anchorName: String? = ""    // 主播名称
    var requestMap: MutableMap<String, String>? = null //不喜欢画像接口上报参数 rn不传  自己处理上报
    var traceMap: Map<String, String>? = null   // 不喜欢埋点相关参数 rn不传  自己处理上报
    var onFeedBackListener: NewXmFeedBackPopDialog.IOnFeedBackListener? = null //不喜欢窗口相关回调
    var showToastInner: Boolean = true  // 是否显示不喜欢toast
    var vibratorEnable: Boolean = false  // 是否震动
    var showTitle: Boolean = true  // 是否展示标题栏
    var dialogType: String = NewXmFeedBackPopDialog.DIALOG_TYPE_NORMAL //弹窗类型
    var adPositionId: String = "" // 广告的positionId
    companion object {
        @JvmStatic
        fun createCommonBuild(
            isFromAd: Boolean,
            anchorName: String?,
            onFeedBackListener: NewXmFeedBackPopDialog.IOnFeedBackListener? = null
        ): DisLikeLeve2Build {
            val build = DisLikeLeve2Build()
            build.isFromAd = isFromAd
            build.anchorName = anchorName
            build.onFeedBackListener = onFeedBackListener
            return build
        }

        @JvmStatic
        fun createAdBuild(
            isFromAd: Boolean,
            anchorName: String?,
            onFeedBackListener: NewXmFeedBackPopDialog.IOnFeedBackListener? = null
        ): DisLikeLeve2Build {
            val build = DisLikeLeve2Build()
            build.isFromAd = isFromAd
            build.anchorName = anchorName
            build.onFeedBackListener = onFeedBackListener
            build.dialogType = NewXmFeedBackPopDialog.DIALOG_TYPE_ADVERTISE
            return build
        }
    }

}