package com.ximalaya.ting.android.host.fragment.dialog;

import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * created by chendekun 4/16/21
 *
 * @email <EMAIL>
 * @phoneNumber 13032178206
 * des:
 */
public class PermissionConfirmDialog extends BaseDialogFragment implements View.OnClickListener {
    public String des = "";

    public static PermissionConfirmDialog newInstance(String des) {
        PermissionConfirmDialog fragment = new PermissionConfirmDialog();
        fragment.des = des;
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        setCancelable(false);
        Window window = getDialog().getWindow();
        if (window == null) {
            return null;
        }
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        View view = inflater.inflate(R.layout.host_dialog_permission_confirm, (ViewGroup) window.findViewById(android.R.id.content), false);
        TextView des = view.findViewById(R.id.host_tv_dialog_des);
        if (!TextUtils.isEmpty(this.des)){
            des.setText(this.des);
        }
        TextView ok = view.findViewById(R.id.host_tv_dialog_ok);
        ok.setOnClickListener(this);
        AutoTraceHelper.bindData(ok, AutoTraceHelper.MODULE_DEFAULT, "");

        WindowManager.LayoutParams params = window.getAttributes();
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        params.width = BaseUtil.getScreenWidth(getContext()) - 2 * BaseUtil.dp2px(getContext(), 50);
        params.gravity = Gravity.CENTER;
        window.setAttributes(params);
        return view;
    }

    @Override
    public void onClick(View v) {
        dismiss();
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        if (mOnDismissListener != null) {
            mOnDismissListener.onDismiss(dialog);
        }
    }

    private DialogInterface.OnDismissListener mOnDismissListener;

    public void setOnDismissListener(DialogInterface.OnDismissListener mOnDismissListener) {
        this.mOnDismissListener = mOnDismissListener;
    }
}
