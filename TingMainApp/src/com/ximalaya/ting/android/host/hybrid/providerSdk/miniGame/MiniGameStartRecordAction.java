package com.ximalaya.ting.android.host.hybrid.providerSdk.miniGame;


import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.component.Component;

import org.json.JSONObject;


/**
 * Created by chengyun.wu on 2019/3/31.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17621691226
 */
public class MiniGameStartRecordAction extends BaseMiniGameAction {

    private static final int MAX_INTERNAL = 1000;//ms
    private static final int MIN_INTERNAL = 100;//ms


    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args, AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);
        long internal = args.optInt("interval");
        if(internal < MIN_INTERNAL){
            internal = MIN_INTERNAL;
        }
        if(internal > MAX_INTERNAL){
            internal = MAX_INTERNAL;
        }
        MiniGameAudioManager.getInstance().checkPermissionAndStartRecord(internal, hybridContainer, callback);
    }

}
