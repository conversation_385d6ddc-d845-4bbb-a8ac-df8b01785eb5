package com.ximalaya.ting.android.host.hybrid.providerSdk.payment;

import com.ximalaya.ting.android.host.hybrid.provider.payment.PaymentAction;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.component.Component;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by chengyun.wu on 2018/2/1.
 *
 * <AUTHOR>
 */

public class XMPayAction extends PaymentAction {

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args, AsyncCallback callback, Component comp, String scheme) {
        try {
            if(args != null) {
                args.put("type", "xmpay");
            }
            super.doAction(hybridContainer, args, callback, comp, scheme);
        } catch (JSONException e) {
            e.printStackTrace();
            callback.callback(NativeResponse.fail(-1, "json exception"));
        }
    }
}
