package com.ximalaya.ting.android.host.hybrid.providerSdk.pubsub.actions

import com.ximalaya.ting.android.hybridview.IHybridContainer
import com.ximalaya.ting.android.hybridview.NativeResponse
import com.ximalaya.ting.android.hybridview.component.Component
import org.json.JSONObject

class GetConnStatusAction : BasePubSubAction() {

    override fun doAction(
        hybridContainer: IHybridContainer?,
        args: JSONObject?,
        callback: AsyncCallback?,
        comp: Component?,
        scheme: String?
    ) {
        super.doAction(hybridContainer, args, callback, comp, scheme)

        val connStatus = pubSubService.getConnStatus()
        val result = JSONObject().apply {
            put("connectionStatus", connStatus)
        }
        callback?.callback(NativeResponse.success(result))
    }
} 