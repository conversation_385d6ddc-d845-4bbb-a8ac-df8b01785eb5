package com.ximalaya.ting.android.host.hybrid.providerSdk.util;

import android.text.TextUtils;

import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.component.Component;
import com.ximalaya.ting.android.hybridview.provider.BaseAction;

import org.json.JSONObject;

/**
 * Created by Travis on 2022/11/25 10:47.
 *
 * <AUTHOR>
 */

public class AddListenerAction extends BaseAction {
    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args, AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);
        Fragment fragment = hybridContainer.getAttachFragment();
        if (fragment == null || !NativeHybridFragment.class.isInstance(fragment)) {
            callback.callback(NativeResponse.fail());
            return;
        }
        String action = args.optString("action");
        if (TextUtils.isEmpty(action)) {
            callback.callback(NativeResponse.fail(-1, "action cannot be empty"));
            return;
        }
        Boolean repeat = args.optBoolean("repeat", false);
        NativeHybridFragment hybridFragment = (NativeHybridFragment) fragment;
        hybridFragment.addHybridActionListener(action, new IHybridActionListener() {
            @Override
            public boolean shouldRepeat() {
                return repeat;
            }

            @Override
            public void doAction(Object data) {
                callback.callback(NativeResponse.success(data));
            }

            @Override
            public String getAction() {
                return action;
            }
        });
    }

    @Override
    protected boolean needStatRunloop() {
        return false;
    }
}
