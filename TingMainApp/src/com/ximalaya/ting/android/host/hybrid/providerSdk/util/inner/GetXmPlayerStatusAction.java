package com.ximalaya.ting.android.host.hybrid.providerSdk.util.inner;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.component.Component;
import com.ximalaya.ting.android.hybridview.provider.BaseAction;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 当前喜马播放器状态
 */
public class GetXmPlayerStatusAction extends BaseAction {
    @Override
    public boolean needStatRunloop() {
        return false;
    }

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args,
                         AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);
        if (XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isConnected()) {
            try {
                JSONObject jsonObject = new JSONObject();
                PlayableModel track = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound();
                if (track != null) {
                    jsonObject.put("trackId", track.getDataId());
                }
                jsonObject.put("isPlaying", XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying());
                callback.callback(NativeResponse.success(jsonObject));
            } catch (JSONException e) {
                e.printStackTrace();
                callback.callback(NativeResponse.fail(-1, e.getMessage()));
            }
        } else {
            XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                @Override
                public void onConnected() {
                    try {
                        JSONObject jsonObject = new JSONObject();
                        PlayableModel track = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getCurrSound();
                        if (track != null) {
                            jsonObject.put("trackId", track.getDataId());
                        }
                        jsonObject.put("isPlaying", XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlaying());
                        callback.callback(NativeResponse.success(jsonObject));
                    } catch (JSONException e) {
                        e.printStackTrace();
                        callback.callback(NativeResponse.fail(-1, e.getMessage()));
                    }
                    XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).removeOnConnectedListerner(this);
                }
            });
        }
    }
}
