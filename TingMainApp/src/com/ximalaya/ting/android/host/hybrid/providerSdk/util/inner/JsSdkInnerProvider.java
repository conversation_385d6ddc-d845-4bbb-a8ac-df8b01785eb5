package com.ximalaya.ting.android.host.hybrid.providerSdk.util.inner;


import com.ximalaya.ting.android.host.fragment.web.prerequest.PreRequestAction;
import com.ximalaya.ting.android.hybridview.provider.BaseJsSdkProvider;

/**
 * 对内域名提供的对内api合集
 */
public class JsSdkInnerProvider extends BaseJsSdkProvider {

    public JsSdkInnerProvider() {
        // TODO 新增api记得完善api文档 http://thoughts.ximalaya.com/workspaces/5ec1f15c2d54160012c3fd3e/docs/5f990770f4c000000106a5b9
        addAction("adUnlock", AdUnlockAction.class);
        addAction("showAdBanner", AdWelfareShowBannerAction.class);  // 砸金蛋页面 底部广告banner
        addAction("eggResultDialog", AdWelfareEggResultAction.class);  // 砸金蛋弹结果弹窗
        addAction("playAdVideo", AdWelfarePlayVideoAction.class);  // 砸金蛋 看视频领取砸金蛋次数

        addAction("goGameCenter", AdLetoGameCenterAction.class);  // 跳到游戏中心action

        addAction("getLetoGameAdInfo", AdGetLetoGameInfoAction.class);  // 获取游戏 frameworkversion 的 action
        addAction("showFoatPlayer", AdShowPlayerFloatViewAction.class);  // h5页面展示播放器 的 action


        addAction("prerequest", PreRequestAction.class);  // h5页面展示播放器 的 action
        addAction("shopWindowAd", AdShopWindowAction.class);  // h5购物车页面展示橱窗广告
        addAction("hideWebView", HideWebViewAction.class);  // h5购物车页面展示橱窗广告
        addAction("getXmRequestId", GetXmRequestIdAction.class);  // 获取埋点用的xmRequestId
        addAction("showAdFeedBack", FeedBackAction.class);  // 弹出负反馈弹窗
        addAction("onAdShow", OnAdShowAction.class);  // 广告曝光
        addAction("onAdClick", OnAdClickAction.class);  // 广告点击
        addAction("onAdClose", OnAdCloseAction.class);  // 广告关闭
        addAction("playAdPreAnimator", AdPlayPreAnimatorAction.class);  //播放页前摇动画
        addAction("getPlayStatus", GetXmPlayerStatusAction.class);  //当前喜马播放器状态
    }

}
