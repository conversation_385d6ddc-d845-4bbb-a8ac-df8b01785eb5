package com.ximalaya.ting.android.host.imchat.database.constants;

import com.ximalaya.ting.android.im.xchat.db.constants.IMDBDataConstants;

/**
 * 订阅号数据表 表结构定义
 *
 * <AUTHOR>
 * @desc 文件描述
 * @email <EMAIL>
 * @wiki 说明文档的链接地址
 * @server 服务端开发人员放在这里
 * @since 2019-09-05 16:13
 */
public class SubsDbTableConstants {

    
    //*************************** 订阅号信息相关 start  ***********************************/

    public static final String TABLE_IM_SUBS_MSGS     = "table_im_subs_msgs";
    public static final String TABLE_IM_SUBS_SESSIONS = "table_im_subs_sessions";

    //订阅号消息数据表
    public static final String SUBS_IM_KEY                   = "_id";//主键，自增
    public static final String SUBS_IM_SENDER_ID             = "subs_sender_id";//发送方 id 列名
    public static final String SUBS_IM_SENDER_NAME           = "subs_sender_name";//发送方 昵称
    public static final String SUBS_IM_SENDER_AVATAR         = "subs_sender_avatar";//发送方 头像
    public static final String SUBS_IM_RECEIVER_ID           = "subs_receiver_id";//接收方 id 列名
    public static final String SUBS_IM_MESSAGE_TYPE          = "subs_message_type";//消息类型列名
    public static final String SUBS_IM_MSG_SUBTYPE           = "subs_msg_sub_type";//自定义消息内部类型
    public static final String SUBS_IM_CONTENT               = "subs_content";//消息内容列名
    public static final String SUBS_IM_UNIQUE_ID             = "subs_unique_id";//消息标识列名
    public static final String SUBS_IM_MESSAGE_ID            = "subs_message_id";//消息id
    public static final String SUBS_IM_TIME                  = "subs_time";//发送时间列名
    public static final String SUBS_IM_IS_READ               = "subs_is_read";//消息是否已读列名
    public static final String SUBS_IM_SENDER_TYPE           = "subs_sender_type";//用户关系列名
    public static final String SUBS_IM_IS_RETREAT            = "subs_is_retreat";//是否时被撤销的消息列名
    public static final String SUBS_IM_MESSAGE_DIRECTION     = "subs_message_direction";//消息发送方向列名
    public static final String SUBS_IM_SEND_STATUS           = "subs_send_status";//消息发送状态列名
    public static final String SUBS_IM_ATTACHMENT_STATUS     = "subs_attachment_status";//特殊类消息的处理状态，例如 语音消息是否已听，文件是否下载
    public static final String SUBS_IM_SESSION_ID            = "subs_session_id";//会话id
    public static final String SUBS_IM_SESSION_TYPE          = "subs_session_type";//会话类型
    public static final String SUBS_IM_MESSAGE_EXTENSIONS    = "subs_message_extensions";//消息的扩展属性，典型如@


    //订阅号会话数据表
    public static final String SUBS_SESSION_KEY              = "_id";//主键
    public static final String SUBS_SESSION_ID               = "subs_session_id";//会话 id
    public static final String SUBS_SESSION_TYPE             = "subs_session_type";//会话类型
    public static final String SUBS_SESSION_SENDER_ID        = "subs_sender_id";//发送方id
    public static final String SUBS_SESSION_NAME             = "subs_session_name";//会话名称
    public static final String SUBS_SESSION_AVATAR           = "subs_session_avatar";//会话头像
    public static final String SUBS_SESSION_RECEIVER_ID      = "subs_receiver_id";//接收方 id
    public static final String SUBS_SESSION_CONTENT          = "subs_content";//会话内容
    public static final String SUBS_SESSION_UNREAD_COUNT     = "subs_unread_count";//未读消息数
    public static final String SUBS_SESSION_UNIQUE_ID        = "subs_unique_id";//消息标识
    public static final String SUBS_SESSION_MESSAGE_ID       = "subs_message_id";//消息 id
    public static final String SUBS_SESSION_MESSAGE_TYPE     = "subs_message_type";//消息类型
    public static final String SUBS_SESSION_MSG_SUBTYPE      = "subs_msg_sub_type";//自定义消息类型
    public static final String SUBS_SESSION_SEND_STATUS      = "subs_send_status";//发送状态
    public static final String SUBS_SESSION_UPDATE_TIME      = "subs_update_time";//更新时间
    public static final String SUBS_SESSION_MAX_MESSAGE_ID   = "subs_max_message_id";//最大消息id
    public static final String SUBS_SESSION_MIN_MESSAGE_ID   = "subs_min_message_id";//最小消息id



    //*************************** 订阅号信息相关 end  *************************************/



    //插入新的订阅号消息，发现会话中没有该用户，增加新的会话记录
    public static final String TRIGGER_INSERT_SUBS_SESSION_AFTER_INSERT_MSG = "trigger_insert_subs_session_after_insert_msg";
    //插入订阅号消息，发现该会话已经存在，更新会话的信息
    public static final String TRIGGER_UPDATE_SUBS_SESSION_AFTER_INSERT_NEW_MSG = "trigger_update_subs_session_after_insert_new_msg";
    public static final String TRIGGER_UPDATE_SUBS_SESSION_AFTER_INSERT_OLD_MSG = "trigger_update_subs_session_after_insert_old_msg";

    //更新消息未读之后，更新会话中未读数量统计
    public static final String TRIGGER_UPDATE_SUBS_SESSION_UNREAD_AFTER_MSG_READ = "trigger_update_subs_session_unread_after_msg_read";
    //外部主体session表中 用户头像、昵称信息更新时，随之更新订阅号会话相关信息
    public static final String TRIGGER_UPDATE_SUBS_SESSION_AFTER_UPDATE_MAIN_USERINFO = "trigger_update_subs_userinfo_after_update_main_userinfo";



    //检查订阅号会话是否存在
    public static final String CHECK_SUBS_SESSION_EXIST_OR_NOT =
            "(SELECT COUNT(_id) FROM " + TABLE_IM_SUBS_SESSIONS
                    + " WHERE " + SUBS_SESSION_ID + " = new." + SUBS_SESSION_ID
                    + " AND " + SUBS_SESSION_TYPE + " = new." + SUBS_IM_SESSION_TYPE + ")";


    //检查是否新的订阅号消息
    public static final String CHECK_IS_NEW_SUBS_MSG =
            "(SELECT COUNT(_id) FROM " + TABLE_IM_SUBS_SESSIONS
                    + " WHERE " + SUBS_SESSION_MAX_MESSAGE_ID + " < new." + SUBS_IM_MESSAGE_ID
                    + " AND " + SUBS_SESSION_UPDATE_TIME + " <= new." + SUBS_IM_TIME + " AND "
                    + SUBS_SESSION_ID + " = new." + SUBS_IM_SESSION_ID
                    + " AND " + SUBS_SESSION_TYPE + " = new." + SUBS_IM_SESSION_TYPE + ")";


    //
    //操作：插入新的会话记录
    //触发条件：私信、群发表有新纪录
    public static final String INSERT_NEW_SUBS_SESSION_ON_INSERT_SUBS_MSG =
            "BEGIN" +
                    " INSERT INTO " + TABLE_IM_SUBS_SESSIONS +
                    " ("
                        + SUBS_SESSION_ID             + "," +
                    " " + SUBS_SESSION_TYPE           + "," +
                    " " + SUBS_SESSION_SENDER_ID      + "," +
                    " " + SUBS_SESSION_NAME           + "," +
                    " " + SUBS_SESSION_AVATAR         + "," +
                    " " + SUBS_SESSION_RECEIVER_ID    + "," +
                    " " + SUBS_SESSION_CONTENT        + "," +
                    " " + SUBS_SESSION_UNREAD_COUNT   + "," +
                    " " + SUBS_SESSION_UNIQUE_ID      + "," +
                    " " + SUBS_SESSION_MESSAGE_ID     + "," +
                    " " + SUBS_SESSION_MESSAGE_TYPE   + "," +
                    " " + SUBS_SESSION_MSG_SUBTYPE    + "," +
                    " " + SUBS_SESSION_SEND_STATUS    + "," +
                    " " + SUBS_SESSION_UPDATE_TIME    + "," +
                    " " + SUBS_SESSION_MAX_MESSAGE_ID + "," +
                    " " + SUBS_SESSION_MIN_MESSAGE_ID + ") " +

                    " VALUES " +
                    "(new." + SUBS_IM_SESSION_ID      + "," +
                    " new." + SUBS_IM_SESSION_TYPE    + "," +
                    " new." + SUBS_IM_SENDER_ID       + "," +
                    " new." + SUBS_IM_SENDER_NAME     + "," +
                    " new." + SUBS_IM_SENDER_AVATAR   + "," +
                    " new." + SUBS_IM_RECEIVER_ID     + "," +
                    " new." + SUBS_IM_CONTENT         + "," +
                    // 消息是否已读
                    " (CASE" +
                    " WHEN (new." + SUBS_IM_IS_READ + " = " + IMDBDataConstants.VALUE_MESSAGE_READ + ")" +
                    " THEN 0" +
                    " ELSE 1" +
                    " END), " +
                    " new." + SUBS_IM_UNIQUE_ID     + "," +
                    " new." + SUBS_IM_MESSAGE_ID    + "," +
                    " new." + SUBS_IM_MESSAGE_TYPE  + "," +
                    " new." + SUBS_IM_MSG_SUBTYPE   + "," +
                    " new." + SUBS_IM_SEND_STATUS   + "," +
                    " new." + SUBS_IM_TIME          + "," +
                    " new." + SUBS_IM_MESSAGE_ID    + "," +
                    " new." + SUBS_IM_MESSAGE_ID    + ");" +
                    "END;";



    //操作：更新订阅号会话记录
    public static final String UPDATE_SUBS_SESSION_ON_INSERT_NEW_SUBS_MSG =
            "BEGIN" +
                    " UPDATE " + TABLE_IM_SUBS_SESSIONS + " SET" +
                    " " + SUBS_SESSION_UPDATE_TIME + "= (new." + SUBS_IM_TIME + ")," +
                    " " + SUBS_SESSION_UNREAD_COUNT + " = "+
                    " (CASE" +
                    " WHEN (new." + SUBS_IM_IS_READ + " = "+ IMDBDataConstants.VALUE_MESSAGE_UNREAD + " )" +
                    " THEN ("+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_UNREAD_COUNT + "+1) " +
                    " ELSE "+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_UNREAD_COUNT +
                    " END), " +

                    " " + SUBS_SESSION_NAME + "= (new." + SUBS_IM_SENDER_NAME + ")," +
                    " " + SUBS_SESSION_AVATAR + "= (new." + SUBS_IM_SENDER_AVATAR + ")," +

                    " " + SUBS_SESSION_MAX_MESSAGE_ID + "=" +
                    " (CASE" +
                    " WHEN (new." + SUBS_IM_MESSAGE_ID + " > "+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_MAX_MESSAGE_ID + " )" +
                    " THEN new." + SUBS_IM_MESSAGE_ID +
                    " ELSE "+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_MAX_MESSAGE_ID +
                    " END), " +

                    " " + SUBS_SESSION_CONTENT + "= (new." + SUBS_IM_CONTENT + ")," +
                    " " + SUBS_SESSION_MESSAGE_TYPE + "= (new." + SUBS_IM_MESSAGE_TYPE + ")," +
                    " " + SUBS_SESSION_MSG_SUBTYPE + "= (new." + SUBS_IM_MSG_SUBTYPE + ")," +
                    " " + SUBS_SESSION_SENDER_ID  + "= new." + SUBS_IM_SENDER_ID +

                    " WHERE " + SUBS_SESSION_ID       + "= new." + SUBS_IM_SESSION_ID
                    + " AND " + SUBS_SESSION_TYPE + " = new." + SUBS_IM_SESSION_TYPE + ";" +
                    "END;";

    //
    //操作：更新会话记录
    //触发条件：私信、群发表插入历史记录
    public static final String UPDATE_SUBS_SESSION_ON_INSERT_OLD_SUBS_MSG =
            "BEGIN" +
                    " UPDATE " + TABLE_IM_SUBS_SESSIONS + " SET" +
                    " " + SUBS_SESSION_UNREAD_COUNT + "=" +
                    " (CASE" +
                    " WHEN (new." + SUBS_IM_IS_READ + " = "+ IMDBDataConstants.VALUE_MESSAGE_UNREAD + " )" +
                    " THEN ("+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_UNREAD_COUNT + "+1) " +
                    " ELSE "+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_UNREAD_COUNT +
                    " END), " +

                    " " + SUBS_SESSION_MAX_MESSAGE_ID + "=" +
                    " (CASE" +
                    " WHEN (new." + SUBS_IM_MESSAGE_ID + " > "+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_MAX_MESSAGE_ID + " )" +
                    " THEN new." + SUBS_IM_MESSAGE_ID +
                    " ELSE "+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_MAX_MESSAGE_ID +
                    " END), " +

                    " " + SUBS_SESSION_MIN_MESSAGE_ID + "=" +
                    " (CASE" +
                    " WHEN (new." + SUBS_IM_MESSAGE_ID + " < "+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_MIN_MESSAGE_ID + " )" +
                    " THEN new." + SUBS_IM_MESSAGE_ID +
                    " ELSE "+ TABLE_IM_SUBS_SESSIONS+"." + SUBS_SESSION_MIN_MESSAGE_ID +
                    " END) " +

                    " WHERE " + SUBS_SESSION_ID + "= new." + SUBS_IM_SESSION_ID
                    + " AND " + SUBS_SESSION_TYPE + " = new." + SUBS_IM_SESSION_TYPE + ";" +
                    "END;";


    //
    //更新会话中的未读数量
    public static final String UPDATE_SUBS_SESSION_UNREAD_MSG_NUM =
            "BEGIN " +
                    "UPDATE " + TABLE_IM_SUBS_SESSIONS + " SET " +
                    SUBS_SESSION_UNREAD_COUNT + " = " +
                    "(SELECT COUNT(" + TABLE_IM_SUBS_MSGS + "._id) FROM " + TABLE_IM_SUBS_MSGS + " " +
                    "WHERE " + SUBS_IM_SESSION_ID + " = new." + SUBS_IM_SESSION_ID +" " +
                    "AND " + SUBS_IM_IS_READ + " = " + IMDBDataConstants.VALUE_MESSAGE_UNREAD + ") " +
                    "WHERE " + SUBS_SESSION_ID + " = new." + SUBS_IM_SESSION_ID
                    + " AND " + SUBS_SESSION_TYPE + " = new." + SUBS_IM_SESSION_TYPE + ";" +
                    "END;";






}
