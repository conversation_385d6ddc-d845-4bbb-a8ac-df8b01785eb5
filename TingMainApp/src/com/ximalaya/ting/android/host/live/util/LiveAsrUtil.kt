package com.ximalaya.ting.android.host.live.util

import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.xmutil.Logger

/**
 *
 *
 * <AUTHOR> min<PERSON>chuan
 * @email <EMAIL>
 * @phoneNumber 17621066329
 * @wiki
 * @server
 * @since 2025/4/27
 */
object LiveAsrUtil {

    /**
     * 预安装 asr bundle
     */
    fun preLoadBundleToAsr() {
        Router.getActionByCallback(
            Configure.BUNDLE_LIVE_ASR,
            object : Router.IBundleInstallCallback {
                override fun onInstallSuccess(bundleModel: BundleModel) {
                    Logger.e("ymc_test", "LiveASR 安装成功")
                }

                override fun onLocalInstallError(
                    t: Throwable,
                    bundleModel: BundleModel
                ) {
                    Logger.e("ymc_test", "LiveASR 安装失败")
                }

                override fun onRemoteInstallError(
                    t: Throwable,
                    bundleModel: BundleModel
                ) {
                    Logger.e("ymc_test", "LiveASR 插件下载失败")

                }
            },
            true,
            BundleModel.DOWNLOAD_IN_BACKGROUND
        )
    }

}