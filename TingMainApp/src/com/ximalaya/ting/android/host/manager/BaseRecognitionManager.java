package com.ximalaya.ting.android.host.manager;

import android.content.Context;

public class BaseRecognitionManager {

    protected SpeechRecognitionManager.ISpeechRecognizeCallback mCallback;
    protected boolean mInterval; // 是否间断给出识别结果
    protected long mTimeout; //超时时间，单位是ms

    protected void setCallback(SpeechRecognitionManager.ISpeechRecognizeCallback callback) {
        this.mCallback = callback;
    }

    protected void setInterval(boolean interval) {
        this.mInterval = interval;
    }

    protected void setTimeout(long timeout) {
        this.mTimeout = timeout;
    }

    protected void initRecogineListener(Context context) {
    }

    protected void removeRecogineListener() {
    }

    protected void startRecognize(Context context) {
    }

    protected void stopRecognize() {
    }

}
