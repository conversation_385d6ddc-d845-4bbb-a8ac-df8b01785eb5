package com.ximalaya.ting.android.host.manager

import android.net.Uri
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.pageerrormonitor.PageErrorModel
import com.ximalaya.ting.android.framework.pageerrormonitor.XmPageErrorMonitor
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.server.NetworkUtils
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created by <PERSON><PERSON>u on 2023/5/22.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
object OuterItingManager {
    private const val VALID_ACTION_INTERVAL = 5000L
    private const val OUTER_ITING_PLAY_TRACE = "外部iting播放"
    private const val OUTER_ITING_PLAY_RESULT_OK = 0
    private const val OUTER_ITING_PLAY_RESULT_FAIL_NO_NETWORK = PageErrorModel.RESULT_NO_NETWORK
    private const val OUTER_ITING_PLAY_RESULT_FAIL = -2
    private var mOuterItingActionTime = 0L

    private val CAN_SKIP_HOME_PAGE_ITING_MSGTYPE = arrayListOf<Int>(AppConstants.PAGE_TO_RECOMMEND_ALBUM)

    private var mWaitToPlayIting: String? = null
    private var mHasPlay = false

    fun markOuterItingAction() {
        mOuterItingActionTime = System.currentTimeMillis()
    }

    fun isOuterIting(): Boolean {
        val actionInterval = System.currentTimeMillis() - mOuterItingActionTime
        mOuterItingActionTime = 0
        return actionInterval in 0 until VALID_ACTION_INTERVAL
    }

    fun canSkipHomePage(uri: Uri): Boolean {
        if (uri.scheme != "iting") {
            return false
        }
        try {
            val msgType = uri.getQueryParameter("msg_type")?.toInt()
            if (msgType != null) {
                return CAN_SKIP_HOME_PAGE_ITING_MSGTYPE.contains(msgType)
            }
        } catch (e: Exception) {
            Logger.e(e)
        }
        return false
    }

    fun waitToPlay(iting: String) {
        mWaitToPlayIting = iting
        HandlerManager.postOnUIThreadDelay4Kt(20000) {
            // 这里重点是收集没有播放的数据，已播放的没那么重要，所以不在启播的时候就直接上报，在这里统一上报
            if (mHasPlay) {
                XmPageErrorMonitor.post(OUTER_ITING_PLAY_TRACE, OUTER_ITING_PLAY_RESULT_OK)
            } else {
                val result = if (NetworkUtils.isNetworkAvaliable(BaseApplication.getMyApplicationContext())) {
                    OUTER_ITING_PLAY_RESULT_FAIL
                } else {
                    OUTER_ITING_PLAY_RESULT_FAIL_NO_NETWORK
                }
                XmPageErrorMonitor.post(OUTER_ITING_PLAY_TRACE, result)
            }
            mWaitToPlayIting = null
        }
    }

    fun notifyPlay() {
        mHasPlay = true
    }
}