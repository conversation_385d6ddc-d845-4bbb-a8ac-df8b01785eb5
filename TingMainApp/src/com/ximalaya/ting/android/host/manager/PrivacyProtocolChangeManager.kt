package com.ximalaya.ting.android.host.manager

import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.view.KeyEvent
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.PreInstallBaseModeActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.dialog.PrivacySecondLevelDialog
import com.ximalaya.ting.android.host.fragment.dialog.PrivacyPolicyChangeDialogFragment
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil

/**
 * Created by WolfXu on 2023/8/22.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
object PrivacyProtocolChangeManager {
    private var mNeedShowDialog: Boolean? = null

    fun isNeedShowPrivacyProtocolChangeDialog(): Boolean {
        if (ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext())) {
            return false
        }
        if (mNeedShowDialog == null) {
            mNeedShowDialog = checkNeedShowPrivacyProtocolChangeDialog()
        }
        return mNeedShowDialog ?: false
    }

    private fun checkNeedShowPrivacyProtocolChangeDialog(): Boolean {
        if (ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext())) {
            return false
        }
        val localVersion = MMKVUtil.getInstance().getInt(CConstants.Group_toc.KEY_POLICY_CHANGE_NOTICE_VERSION, -1)
        val remoteVersion = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getInt(
            PreferenceConstantsInHost.KEY_PRIVACY_PROTOCOL_REMOTE_VERSION, -1)
        if (localVersion == -1) {
            // 保存当前远端版本到本地
            if (remoteVersion > -1) {
                MMKVUtil.getInstance().saveInt(CConstants.Group_toc.KEY_POLICY_CHANGE_NOTICE_VERSION, remoteVersion)
            }
            // 第一次启动，展示的是最新的隐私协议，所以不需要再次弹窗
            return false
        }
        if (localVersion == 0 && remoteVersion == 1) {
            return false
        }
        // 当前版本小于远端版本, 需要展示隐私弹窗确认
        if (remoteVersion > localVersion) {
            return true
        }
        return false
    }

    fun onAgreePrivacyProtocol() {
        if (mNeedShowDialog != false) {
            mNeedShowDialog = false
            val remoteVersion = ConfigureCenter.getInstance()
                .getInt(
                    CConstants.Group_toc.GROUP_NAME,
                    CConstants.Group_toc.KEY_POLICY_CHANGE_NOTICE_VERSION, -1
                )
            MMKVUtil.getInstance()
                .saveInt(CConstants.Group_toc.KEY_POLICY_CHANGE_NOTICE_VERSION, remoteVersion)
        }
    }

    fun showDialog(activity: FragmentActivity) {
        val dialog = PrivacyPolicyChangeDialogFragment.newInstance()
        dialog.agreeCallback = {
            onAgreePrivacyProtocol()
            startMainActivity(activity)
        }
        dialog.disagreeCallback = {
            showPrivacySecondLevelDialog(activity)
        }
        dialog.show(activity.supportFragmentManager, "")
    }

    fun startMainActivity(activity: Activity) {
        val intent = MainActivity.getMainActivityIntent(activity)
        activity.startActivity(intent)
        activity.finish()
    }

    fun saveRemoteVersionCache() {
        ConfigureCenter.getInstance().registerConfigureCallback {
            val remoteVersion = ConfigureCenter.getInstance().getInt(CConstants.Group_toc.GROUP_NAME,
                CConstants.Group_toc.KEY_POLICY_CHANGE_NOTICE_VERSION, -1)
            MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).saveInt(
                PreferenceConstantsInHost.KEY_PRIVACY_PROTOCOL_REMOTE_VERSION, remoteVersion)
            val localVersion = MMKVUtil.getInstance().getInt(CConstants.Group_toc.KEY_POLICY_CHANGE_NOTICE_VERSION, -1)
            if (localVersion == -1) {
                MMKVUtil.getInstance().saveInt(CConstants.Group_toc.KEY_POLICY_CHANGE_NOTICE_VERSION, remoteVersion)
            }
        }
    }

    private fun showPrivacySecondLevelDialog(activity: Activity) {
        val secondLevelDialog = PrivacySecondLevelDialog(activity)
        secondLevelDialog.setPositiveButtonClickListener { v: View? ->
            //进入完整模式
            secondLevelDialog.dismiss()
            onAgreePrivacyProtocol()
            startMainActivity(activity)
        }
        secondLevelDialog.setNegativeButtonClickListener { v: View? ->
            //进入基础模式
            secondLevelDialog.dismiss()
            startToBaseModeActivity(activity)
        }
        secondLevelDialog.setFinishApp { v: View? ->
            secondLevelDialog.dismiss()
            activity.finish()
        }
        secondLevelDialog.setOnKeyListener { dialog: DialogInterface?, keyCode: Int, event: KeyEvent? ->
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                secondLevelDialog.dismiss()
                activity.finish()
            }
            false
        }
        secondLevelDialog.setCanceledOnTouchOutside(false)
        secondLevelDialog.show()
    }

    private fun startToBaseModeActivity(activity: Activity) {
        val intent = Intent(activity, PreInstallBaseModeActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        activity.startActivity(intent)
        activity.finish()
    }
}