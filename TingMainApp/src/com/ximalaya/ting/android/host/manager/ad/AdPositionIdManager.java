package com.ximalaya.ting.android.host.manager.ad;

import android.text.TextUtils;
import android.util.Log;

import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;

/**
 * Created by le.xin on 2020/8/26.
 * 根据positionName转positionId方法
 * <AUTHOR>
 * @email <EMAIL>
 */
public class AdPositionIdManager {

    public static String getPositionIdByPositionName(String name) {
        String adPositionId = getPositionIdByPositionNameNoDefault(name);
        if (adPositionId == null) {
            if (ConstantsOpenSdk.isDebug) {
                Log.e("RuntimeException","name = "+name);
//                throw new RuntimeException("这里应该不存在这种情况的" + name);
            }
            adPositionId = name;
        }

        return adPositionId;
    }

    public static String getPositionIdByPositionNameNoDefault(String name) {
        String adPositionId = null;

        if(TextUtils.equals(AppConstants.AD_POSITION_NAME_SOUND_PATCH, name)) {
            adPositionId = IAdConstants.IAdPositionId.TRACK_SOUND_PATCH;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_CATA_LIST, name)) {
            adPositionId = IAdConstants.IAdPositionId.CATE_LIST;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_LOADING, name)) {
            adPositionId = IAdConstants.IAdPositionId.LOADING;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_CATA_BANNER, name)) {
            adPositionId = IAdConstants.IAdPositionId.CATA_BANNER;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_CATA_INDEX_BANNER, name)) {
            adPositionId = IAdConstants.IAdPositionId.CATA_INDEX_BANNER;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_NATIVE, name)) {
            adPositionId = IAdConstants.IAdPositionId.NATIVE_PLAY;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_FIND_NATIVE, name)) {
            adPositionId = IAdConstants.IAdPositionId.FIND_NATIVE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_CITY_NATIVE, name)) {
            adPositionId = IAdConstants.IAdPositionId.LOCAL_LIST_NATIVE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_CITY_COLUMN, name)) {
            adPositionId = IAdConstants.IAdPositionId.LOCAL_BANNER;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_ALBUM_NOTICE, name)) {
            adPositionId = IAdConstants.IAdPositionId.ALBUM_NOTICE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_SKIN, name)) {
            adPositionId = IAdConstants.IAdPositionId.PLAY_SKIN;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_READ, name)) {
            adPositionId = IAdConstants.IAdPositionId.PLAY_READ;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_CHAT_ROOM, name)) {
            adPositionId = IAdConstants.IAdPositionId.LIVE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_CENTER, name)) {
            adPositionId = IAdConstants.IAdPositionId.PLAY_LARGE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_BROADCASTER_BANNER ,name)) {
            adPositionId = IAdConstants.IAdPositionId.BROADCASTER_BANNER;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_BROADCAST_NATIVE ,name)) {
            adPositionId = IAdConstants.IAdPositionId.BROADCAST_NATIVE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_SHARE_FLOAT ,name)) {
            adPositionId = IAdConstants.IAdPositionId.SHARE_FLOAT;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_MORE_OPERATE ,name)) {
            adPositionId = IAdConstants.IAdPositionId.OPERATE_FLOAT;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_TITLEBAR_MIDDLE_BOTTOM ,name)) {
            adPositionId = IAdConstants.IAdPositionId.RECOMMEND_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_XIAOYA_FLOAT ,name)) {
            adPositionId = IAdConstants.IAdPositionId.XIAOYA_FLOAT;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_FIND_FLOAT ,name)) {
            adPositionId = IAdConstants.IAdPositionId.FIND_FLOAT;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_WALLET_BANNER ,name)) {
            adPositionId = IAdConstants.IAdPositionId.WALLET_BANNER;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_YELLOW_BAR ,name)) {
            adPositionId = IAdConstants.IAdPositionId.PLAY_YELLOW_BAR;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_BRAND_FEATURE ,name)) {
            adPositionId = IAdConstants.IAdPositionId.BRAND_FEATURE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_WAISTBAND ,name)) {
            adPositionId = IAdConstants.IAdPositionId.WAISTBAND;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_LIVE_BANNER ,name)) {
            adPositionId = IAdConstants.IAdPositionId.LIVE_BANNER;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_GIANT_SCREEN ,name)) {
            adPositionId = IAdConstants.IAdPositionId.GIANT_SCREEN;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_ICON ,name)) {
            adPositionId = IAdConstants.IAdPositionId.ICON;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_SEARCH_EGGS ,name)) {
            adPositionId = IAdConstants.IAdPositionId.SEARCH_EGGS;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_COLUMN_SPONSORSHIP ,name)) {
            adPositionId = IAdConstants.IAdPositionId.COLUMN_SPONSORSHIP;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PURCHASE_MIDDLE_BOTTOM ,name)) {
            adPositionId = IAdConstants.IAdPositionId.PURCHASE_MIDDLE_BOTTOM;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_COMMENT ,name)) {
            adPositionId = IAdConstants.IAdPositionId.COMMENT;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_INCENTIVE, name)) {
            adPositionId = IAdConstants.IAdPositionId.UNLOCK_VIDEO;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION, name)) {
            adPositionId = IAdConstants.IAdPositionId.INCENTIVE_DURATION;
        } else if(TextUtils.equals(AppConstants.AD_POSITION_NAME_PURCHASE_BOTTOM, name)) {
            adPositionId = IAdConstants.IAdPositionId.PURCHASE_BOTTOM;
        } else if(TextUtils.equals(AppConstants.AD_POSITION_NAME_PURCHASE_MIDDLE, name)) {
            adPositionId = IAdConstants.IAdPositionId.PURCHASE_MIDDLE;
        } else if(TextUtils.equals(AppConstants.AD_POSITION_NAME_MAIN_SEARCH_BANNER, name)) {
            adPositionId = IAdConstants.IAdPositionId.SEARCH_MAIN_BANNER;
        } else if(TextUtils.equals(AppConstants.AD_POSITION_NAME_WELFARE_LAYER, name)) {
            adPositionId = IAdConstants.IAdPositionId.WELFARE_LAYER;
        } else if(TextUtils.equals(AppConstants.AD_POSITION_NAME_WELFARE_DIALOG, name)) {
            adPositionId = IAdConstants.IAdPositionId.WELFARE_DIALOG;
        } else if(TextUtils.equals(AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO, name)) {
            adPositionId = IAdConstants.IAdPositionId.WELFARE_INCENTIVE_VIDEO;
        } else if(TextUtils.equals(AppConstants.AD_POSITION_NAME_FORWARD_VIDEO, name)) {
            adPositionId = IAdConstants.IAdPositionId.FORWARD_VIDEO;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PAOPAO_ACTIVITY_ENTRY, name)) {
            adPositionId = IAdConstants.IAdPositionId.PAOPAO_ACTIVITY_ENTRY;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_IMMERSIVE_SKIN, name)) {
            adPositionId = IAdConstants.IAdPositionId.IMMERSIVE_NEW_SKIN;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_CHASE_RECOMMEND, name)) {
            adPositionId = IAdConstants.IAdPositionId.SOUND_PATCH_MORE_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_GAME_CENTER_VIDEO, name)) {
            adPositionId = IAdConstants.IAdPositionId.GAME_CENTER_REWARD_VIDEO;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_SEARCH_TOP_BRAND ,name)) {
            adPositionId = IAdConstants.IAdPositionId.SEARCH_TOP_BRAND_AD;
        }else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_LIVE_LITTLE_BANNER ,name)) {
            adPositionId = IAdConstants.IAdPositionId.LIVE_LITTLE_BANNER_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_NATIVE_CONTENT_AD, name)) {
            adPositionId = IAdConstants.IAdPositionId.NATIVE_CONTENT_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_RECOMMEND_PROMOTION, name)) {
            adPositionId = IAdConstants.IAdPositionId.RECOMMEND_PROMOTION_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_INSERT_SOUND_PATCH, name)) {
            adPositionId = IAdConstants.IAdPositionId.INSERT_SOUND_PATCH;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_WINDOW_SHOP, name)) {
            adPositionId = IAdConstants.IAdPositionId.WINDOW_SHOP_AD;
        } else if(TextUtils.equals(IAdConstants.IAlbumAdInfoPoisitionName.TRACK_DETAIL_RELATIVE_RECOMMEND, name)) {
            adPositionId = IAdConstants.IAdPositionId.TRACK_DETAIL_RELATIVE_RECOMMEND;
        } else if(TextUtils.equals(IAdConstants.IAlbumAdInfoPoisitionName.ALBUM_DETAIL_RELATIVE_RECOMMEND, name)) {
            adPositionId = IAdConstants.IAdPositionId.ALBUM_DETAIL_RELATIVE_RECOMMEND;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_HOME_BOTTOM, name)) {
            adPositionId = IAdConstants.IAdPositionId.HOME_BOTTOM;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_SOUND_PATCH_PLAY_BANNER, name)) {
            adPositionId = IAdConstants.IAdPositionId.SOUND_PATCH_PLAY_BANNER;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_SOUND_PATCH_BANNER, name)) {
            adPositionId = IAdConstants.IAdPositionId.SOUND_PATCH_BANNER;
        } else if(TextUtils.equals(AppConstants.AD_POSITION_NAME_FLOW_SEARCH_AD, name)){
            adPositionId = IAdConstants.IAdPositionId.SEARCH_FLOW_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_SEARCH_KEY_WORDS_FIRST, name)) {
            adPositionId = IAdConstants.IAdPositionId.SEARCH_KEY_WORDS_FIRST_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_SEARCH_KEYWORDS_RESULT, name)) {
            adPositionId = IAdConstants.IAdPositionId.POINTS_KEY_WORDS_RESULT_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_INCENTIVE_SINGLE, name)) {
            adPositionId = IAdConstants.IAdPositionId.SINGLE_TRACK_VIDEO_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_INCENTIVE_SKITS, name)) {
            adPositionId = IAdConstants.IAdPositionId.SKITS_VIDEO_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_INCENTIVE_MIX, name)) {
            adPositionId = IAdConstants.IAdPositionId.REWARD_VIDEO_MIX_AD;
        } else if (TextUtils.equals(AppConstants.AD_SEARCH_ASSOCIATE_WORD, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_SEARCH_SUGGEST;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_LISTEN_LIST;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_RECOMMEND_WHOLE_LISTEN_LIST, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_WHOLE_LISTEN_LIST;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_RECOMMEND_RANK_LIST, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_RANK_LIST;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_POINT_CASH, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_POINT_CASH;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_INCENTIVE_SPECIES, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_INCENTIVE_SPECIES;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_INCENTIVE_WELFARE, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_INCENTIVE_WELFARE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_IINTEGRAL_CENTER_INSPIRE_VIDEO, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_IINTEGRAL_CENTER_INSPIRE_VIDEO;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_WELFARE_CASH_RECEIVE, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_WELFARE_CASH_RECEIVE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_WELFARE_CASH_WITHDRAWAL;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_DOWNLOAD_REWARD_VIDEO, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_DOWNLOAD_REWARD_VIDEO;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_ISOLATE_LARGE, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_PLAY_ISOLATE_LARGE;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_WELFARE_PLAY_LET_TASK, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_WELFARE_PLAY_LET_TASK;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_WELFARE_MALL_TASK, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_WELFARE_MALL_TASK;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_PLAY_PAGE_LIVE_MC_AD, name)) {
            adPositionId = IAdConstants.IAdPositionId.AD_PLAY_PAGE_LIVE_MC_AD;
        } else if (TextUtils.equals(AppConstants.AD_POSITION_NAME_POINTS_CENTER_TASK_BANNER, name)) {
            adPositionId = IAdConstants.IAdPositionId.POINTS_CENTER_TASK_BANNER_AD;
        }

        return adPositionId;
    }

    public static String getPositionNameByPositionId(String positionId) {
        String positionName = null;

        if (TextUtils.equals(IAdConstants.IAdPositionId.TRACK_SOUND_PATCH, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SOUND_PATCH;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.CATE_LIST, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_CATA_LIST;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.LOADING, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_LOADING;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.CATA_BANNER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_CATA_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.CATA_INDEX_BANNER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_CATA_INDEX_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.NATIVE_PLAY, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_NATIVE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.FIND_NATIVE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_FIND_NATIVE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.LOCAL_LIST_NATIVE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_CITY_NATIVE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.LOCAL_BANNER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_CITY_COLUMN;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.ALBUM_NOTICE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_ALBUM_NOTICE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.PLAY_SKIN, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_SKIN;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.PLAY_READ, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_READ;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.LIVE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_CHAT_ROOM;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.PLAY_LARGE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_CENTER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.BROADCASTER_BANNER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_BROADCASTER_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.BROADCAST_NATIVE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_BROADCAST_NATIVE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.SHARE_FLOAT, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SHARE_FLOAT;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.OPERATE_FLOAT, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_MORE_OPERATE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.RECOMMEND_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_TITLEBAR_MIDDLE_BOTTOM;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.XIAOYA_FLOAT, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_XIAOYA_FLOAT;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.FIND_FLOAT, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_FIND_FLOAT;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.WALLET_BANNER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WALLET_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.PLAY_YELLOW_BAR, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_YELLOW_BAR;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.BRAND_FEATURE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_BRAND_FEATURE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.WAISTBAND, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WAISTBAND;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.LIVE_BANNER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_LIVE_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.GIANT_SCREEN, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_GIANT_SCREEN;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.ICON, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_ICON;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.SEARCH_EGGS, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SEARCH_EGGS;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.COLUMN_SPONSORSHIP, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_COLUMN_SPONSORSHIP;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.PURCHASE_MIDDLE_BOTTOM, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PURCHASE_MIDDLE_BOTTOM;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.COMMENT, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_COMMENT;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.UNLOCK_VIDEO, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_INCENTIVE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.INCENTIVE_DURATION, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_INCENTIVE_SPECIES, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_INCENTIVE_SPECIES;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_IINTEGRAL_CENTER_INSPIRE_VIDEO, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_IINTEGRAL_CENTER_INSPIRE_VIDEO;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_INCENTIVE_WELFARE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_INCENTIVE_WELFARE;
        } else if(TextUtils.equals(IAdConstants.IAdPositionId.PURCHASE_BOTTOM, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PURCHASE_BOTTOM;
        } else if(TextUtils.equals(IAdConstants.IAdPositionId.PURCHASE_MIDDLE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PURCHASE_MIDDLE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.SEARCH_MAIN_BANNER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_MAIN_SEARCH_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.WELFARE_LAYER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WELFARE_LAYER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.WELFARE_DIALOG, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WELFARE_DIALOG;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.WELFARE_INCENTIVE_VIDEO, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO;
        } else if(TextUtils.equals(IAdConstants.IAdPositionId.FORWARD_VIDEO, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_FORWARD_VIDEO;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.PAOPAO_ACTIVITY_ENTRY, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PAOPAO_ACTIVITY_ENTRY;
        } else if(TextUtils.equals(IAdConstants.IAdPositionId.IMMERSIVE_NEW_SKIN, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_IMMERSIVE_SKIN;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.SOUND_PATCH_MORE_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_CHASE_RECOMMEND;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.HOME_MIDDLE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_HOME_MIDDLE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.HOME_BOTTOM, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_HOME_BOTTOM;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.HOME_DROP_DOWN, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_HOME_DROP_DOWN;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.POPUP_NEW, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_FIREWORK_POPUP;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.SEARCH_TOP_BRAND_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SEARCH_TOP_BRAND;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.LIVE_LITTLE_BANNER_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_LIVE_LITTLE_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.NATIVE_CONTENT_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_NATIVE_CONTENT_AD;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.RECOMMEND_PROMOTION_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_RECOMMEND_PROMOTION;
        } else if (!TextUtils.isEmpty(positionId) && positionId.contains("66000")) {
            positionName = AppConstants.AD_POSITION_NAME_FOCUS;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.INSERT_SOUND_PATCH, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_INSERT_SOUND_PATCH;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.WINDOW_SHOP_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WINDOW_SHOP;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.RADIO_SOUND_PATCH, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SOUND_PATCH;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.SOUND_PATCH_PLAY_BANNER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SOUND_PATCH_PLAY_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.SOUND_PATCH_BANNER, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SOUND_PATCH_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.SEARCH_KEY_WORDS_FIRST_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SEARCH_KEY_WORDS_FIRST;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.POINTS_CENTER_TASK_BANNER_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_POINTS_CENTER_TASK_BANNER;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.POINTS_KEY_WORDS_RESULT_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SEARCH_KEYWORDS_RESULT;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.ALBUM_COMPONENT_1, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_ALBUM_COMPONENT_1;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.ALBUM_COMPONENT_2, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_ALBUM_COMPONENT_2;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.SOUND_AIGC_MIDDLE_INSERT_AD, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_AIGC_NATIVE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_LISTEN_LIST, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_RECOMMEND_LISTEN_LIST;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_WHOLE_LISTEN_LIST, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_RECOMMEND_WHOLE_LISTEN_LIST;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_RANK_LIST, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_RECOMMEND_RANK_LIST;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_SEARCH_BOTTOM_WORDS, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_SEARCH_BOTTOM_WORDS;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_HOT_SEARCH_LIST, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_HOT_SEARCH_LIST;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_POINT_CASH, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_POINT_CASH;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_WELFARE_CASH_RECEIVE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WELFARE_CASH_RECEIVE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_WELFARE_CASH_WITHDRAWAL, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_DOWNLOAD_REWARD_VIDEO, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_DOWNLOAD_REWARD_VIDEO;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_PLAY_ISOLATE_LARGE, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_PLAY_ISOLATE_LARGE;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_WELFARE_PLAY_LET_TASK, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WELFARE_PLAY_LET_TASK;
        } else if (TextUtils.equals(IAdConstants.IAdPositionId.AD_WELFARE_MALL_TASK, positionId)) {
            positionName = AppConstants.AD_POSITION_NAME_WELFARE_MALL_TASK;
        } else {
            if (ConstantsOpenSdk.isDebug) {
                Log.e("RuntimeException","positionId = "+positionId);
//                throw new RuntimeException("这里应该不存在这种情况的" + positionId);
            }
            positionName = positionId;
        }

        return positionName;
    }

    public static String getAnchorAlbumPositionId(String positionName) {
        String adPositionId = null;
        // 首页猜你喜欢
        if (IAdConstants.IAlbumAdInfoPoisitionName.HOME_GUESS_YOU_LIKE.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.HOME_GUESS_YOU_LIKE;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.HOME_RECOMMEND_FOR_YOU.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.HOME_RECOMMEND_FOR_YOU;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.GUESS_YOU_LIKE.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.GUESS_YOU_LIKE;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.ALBUM_DETAIL_RELATIVE_RECOMMEND.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.ALBUM_DETAIL_RELATIVE_RECOMMEND;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.TRACK_DETAIL_RELATIVE_RECOMMEND.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.TRACK_DETAIL_RELATIVE_RECOMMEND;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.SEARCH_RESULT_RELATIVE_RECOMMEND.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.SEARCH_RESULT_RELATIVE_RECOMMEND;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.HOME_CATEGORY_CARD.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.HOME_CATEGORY_CARD;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.CATEGORY_RECOMMEND.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.CATEGORY_RECOMMEND;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.PAYABLE_RECOMMEND.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.PAYABLE_RECOMMEND;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.CATEGORY_KEYWORD.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.CATEGORY_KEYWORD;
        } else if(IAdConstants.IAlbumAdInfoPoisitionName.PAYABLE_KEYWORD.equals(positionName)) {
            adPositionId = IAdConstants.IAdPositionId.PAYABLE_KEYWORD;
        }

        return adPositionId;

    }


}
