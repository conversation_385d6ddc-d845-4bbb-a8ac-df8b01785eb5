package com.ximalaya.ting.android.host.manager.ad;

import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_SOUND_PATCH;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.LINK_TYPE_WEB;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.adsdk.AdSDK;
import com.ximalaya.ting.android.adsdk.bridge.importsdk.ImportSDKHelper;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.AdSDKAdapterModel;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.AdSDKAdapterWebVideoModel;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.AdCookieParams;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.AnchorAlbumAd;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.IHybridFragment;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.IImageSource;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.IJumpStrategy;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.IMainAppSelfConfig;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.IRewardFragmentWrapper;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.SimpleAdModel;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.SimpleJumpModel;
import com.ximalaya.ting.android.adsdk.bridge.inner.selfconfig.SimpleShareData;
import com.ximalaya.ting.android.adsdk.constants.IXmAdConstants;
import com.ximalaya.ting.android.adsdk.external.IAppStatus;
import com.ximalaya.ting.android.adsdk.external.IBaseAd;
import com.ximalaya.ting.android.adsdk.external.IMediationType;
import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.adsdk.external.IRewardVideoAdListener;
import com.ximalaya.ting.android.adsdk.external.IXmAdSDKCustomController;
import com.ximalaya.ting.android.adsdk.external.IXmAdSDKCustomControllerForJadSDK;
import com.ximalaya.ting.android.adsdk.external.SDKConfig;
import com.ximalaya.ting.android.adsdk.external.XmRewardExtraParam;
import com.ximalaya.ting.android.adsdk.external.bean.XmAdLocation;
import com.ximalaya.ting.android.adsdk.external.mediation.IBaiduApiProvider;
import com.ximalaya.ting.android.adsdk.external.mediation.IBaiduInitParams;
import com.ximalaya.ting.android.adsdk.external.mediation.ICSJInitParams;
import com.ximalaya.ting.android.adsdk.external.mediation.IGDTInitParams;
import com.ximalaya.ting.android.adsdk.external.mediation.IGetBaiduApiCallBack;
import com.ximalaya.ting.android.adsdk.external.mediation.IInitConfig;
import com.ximalaya.ting.android.adsdk.external.mediation.IInitParams;
import com.ximalaya.ting.android.adsdk.external.mediation.IJADInitParams;
import com.ximalaya.ting.android.adsdk.external.mediation.InitParamsConfig;
import com.ximalaya.ting.android.adsdk.splash.util.UploadManager;
import com.ximalaya.ting.android.apm.startup.StartUpRecord;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.detect.PhoneGrade;
import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.data.request.AdUploadManager;
import com.ximalaya.ting.android.host.fragment.AdSdkRewardFragmentWrapper;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.ad.SplashAdRecord;
import com.ximalaya.ting.android.host.fragment.web.IWebFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.AdSDKHybridFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.AdSDKHybridFragmentNew;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.listener.ICollectStatusCallback;
import com.ximalaya.ting.android.host.manager.XuidManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.download.center.AdDownloadListFragment;
import com.ximalaya.ting.android.host.manager.ad.fullvideo.AdFullVideoFragment;
import com.ximalaya.ting.android.host.manager.ad.gamead.AdGameUtil;
import com.ximalaya.ting.android.host.manager.ad.model.DspInitConfig;
import com.ximalaya.ting.android.host.manager.ad.playweb.AdPlayNativeWebManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnlockUtil;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnlockVipTimeManager;
import com.ximalaya.ting.android.host.manager.ad.util.ADABTestUtil;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.ad.IAdFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.AdActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.trace.SoundPatchAdTrace;
import com.ximalaya.ting.android.host.util.PlayPageStyleUtil;
import com.ximalaya.ting.android.host.util.SubContractChannelUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.RemoteTypefaceManager;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConfigConstants;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.other.VideoPlayParamsBuildUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.view.ad.AdSourceFromView;
import com.ximalaya.ting.android.locationservice.LocationService;
import com.ximalaya.ting.android.locationservice.NoInitException;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.httputil.XimalayaException;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.NewPlayPageUtil;
import com.ximalaya.ting.android.template.TemplateManager;
import com.ximalaya.ting.android.template.model.TemplateDetail;
import com.ximalaya.ting.android.template.model.TemplateInfo;
import com.ximalaya.ting.android.xmlogmanager.uploadlog.Utils;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.BaseDeviceUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.Interceptor;
import okhttp3.OkHttpClient;

/**
 * Created by le.xin on 2021/7/8.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class AdSDKManager {
    private static boolean isInited = false;

    private static Boolean canUseSDKShowRecord;
    private static Boolean canUseSDKClickRecord;
    private static Boolean enableInitCsj = null;
    private static Boolean enableInitGdt = null;
    private static Boolean enableInitJad = null;
    private static Boolean enableInitBaidu = null;

    public static boolean canUseSDKShowRecord(Context context) {
        if (canUseSDKShowRecord == null) {
            canUseSDKShowRecord = MmkvCommonUtil.getInstance(context)
                    .getBoolean(PreferenceConstantsInOpenSdk.KEY_CAN_INIT_XM, true) && MmkvCommonUtil.getInstance(context).getBoolean(CConstants.Group_ad.SHOW_RECORD_USE_SDK, ConstantsOpenSdk.isDebug);
        }
        return canUseSDKShowRecord;
    }

    public static boolean canUseSDKClickRecord(Context context) {
        if (canUseSDKClickRecord == null) {
            canUseSDKClickRecord = MmkvCommonUtil.getInstance(context)
                    .getBoolean(PreferenceConstantsInOpenSdk.KEY_CAN_INIT_XM, true) && MmkvCommonUtil.getInstance(context).getBoolean(CConstants.Group_ad.CLICK_RECORD_USE_SDK, ConstantsOpenSdk.isDebug);
        }
        return canUseSDKClickRecord;
    }

//    /**
//     * app进入MainActivity onCreate延时5秒调用，每次冷启动只会调用1次
//     */
//    public static void appStartForMainActivityOnCreateDelay() {
//        AdSDK.getInstance().setApplicationReady();
//    }

    // 多进程调用
    public static void init(Context context) {
        if (isInited) {
            return;
        }

        isInited = true;

//        File file = new File(ToolUtil.getCtx().getFilesDir(), "monitor-" + System.currentTimeMillis() + ".trace");
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            Debug.startMethodTracing(file.getAbsolutePath(), 120 * 1024 * 1024);
//        }

        final String XmSDKInitStage = "AdResLoadThreadBackStage";

        StartUpRecord.getSingleInstance().onStageBegin(XmSDKInitStage);

        long curTime = System.currentTimeMillis();
        SDKConfig.environmentId = BaseConstants.environmentId;

        InitParamsConfig.getInstance().setIInitConfig(new IInitConfig() {
            @Override
            public ICSJInitParams getCSJInitParams() {
                return AdSDKManager.getCSJInitParams();
            }

            @Override
            public IGDTInitParams getGDTInitParams() {
                return AdSDKManager.getGdtInitParams();
            }

            @Override
            public IJADInitParams getJADInitParams() {
                return AdSDKManager.getJadInitParams();
            }

            @Override
            public IBaiduInitParams getBaiduInitParams() {
                return AdSDKManager.getBaiduInitParams();
            }

        });

        String templateId = "-100";
        try {
            TemplateInfo firstPage = TemplateManager.getInstance().getTemplateInfo("firstPage");
            if (firstPage != null) {
                TemplateDetail<? extends TemplateDetail.Layout> templateDetail = firstPage.getTemplateDetail();
                templateId = templateDetail.getTemplateId();
                if (TextUtils.isEmpty(templateId)) {
                    templateId = "-102";
                }
            }
        }catch (Throwable e){
            templateId = "-103";
        }

        Bundle bundle = new Bundle();
        bundle.putInt("splashShakeSpeed", MmkvCommonUtil.getInstance(context).getInt(CConstants.AdSplashShake.SHAKE_SPEED_ANDROID, 0));
        bundle.putString(Settings.Secure.ANDROID_ID,DeviceUtil.getAndroidId(context));
        bundle.putString("templateId",templateId);
        bundle.putInt("splash_async_adx_dsp", MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getInt(CConstants.Group_ad.ITEM_AD_SPLASH_ASYNC_ADX_DSP, 0));
        UploadManager.getInstance().init(new AdUploadManager());
        AdSDK.getInstance().init(context,
                new SDKConfig.Builder("0")
                        .isDebug(ConstantsOpenSdk.isDebug)
                        .wxAppId(AppConfigConstants.WEIXIN_APP_ID_FOR_SHARE)
                        .xmSelfConfig(getSelfConfig())
                        .customController(XM_AD_SDK_CUSTOM_CONTROLLER)
                        .extraBundle(bundle)
                        .build());

        StartUpRecord.getSingleInstance().onStageEnd(XmSDKInitStage);
        SplashAdRecord.reportEvent("SDK_INIT");
        Logger.log("AdSDKManager : initTime  " + (System.currentTimeMillis() - curTime) + "   bundle=" + bundle);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            Debug.stopMethodTracing();
//        }
    }

    public static void initOther(Context context) {

        // enableInitInstallRecordToOtherProcess
        try {
            boolean enableInit = MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInOpenSdk.ITEM_ENABLE_INIT_RECORD_TO_OTHER_PROCESS_V2, false);

            if (enableInit) {
                SDKConfig.environmentId = BaseConstants.environmentId;
                AdSDK.getInstance().initInstallRecordInOtherProcess(context, new SDKConfig.Builder("0")
                        .isDebug(ConstantsOpenSdk.isDebug)
                        .xmSelfConfig(getSelfConfig())
                        .customController(XM_AD_SDK_CUSTOM_CONTROLLER)
                        .build());
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }



    public static ICSJInitParams getCSJInitParams() {
        if (enableInitCsj == null) {
            enableInitCsj = MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_CAN_INIT_CSJ_DSP, true);
        }
        //当init开关打开时，还需要再校验下广告平台的开关是否打开
        if (enableInitCsj) {
            enableInitCsj = getCSJSDKInitEnable();
        }
        Logger.log("adsdk : enableInitCsj = "+enableInitCsj);
        return enableInitCsj?CSJ_INIT_PARAMS:null;
    }

    public static boolean getCSJSDKInitEnable() {
        DspInitConfig config = ADABTestUtil.getDspSDKInitEnable();
        if (config != null) {
            return config.csj;
        }
        return false;
    }


    public static IGDTInitParams getGdtInitParams() {
        if (enableInitGdt == null) {
            enableInitGdt = MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_CAN_INIT_GDT_DSP, true);
        }
        Logger.log("adsdk : 主站football 开关 ableInitGdt = "+enableInitGdt);

        //当init开关打开时，还需要再校验下广告平台的开关是否打开
        if (enableInitGdt) {
            enableInitGdt = getGdtSDKInitEnable();
        }
        Logger.log("adsdk : 广告 ab开关 enableInitGdt = "+enableInitGdt);
        return enableInitGdt?GDT_INIT_PARAMS:null;
    }

    public static boolean getGdtSDKInitEnable() {
        DspInitConfig config = ADABTestUtil.getDspSDKInitEnable();
        if (config != null) {
            return config.gdt;
        }
        return false;
    }

    public static IJADInitParams getJadInitParams() {
        if (enableInitJad == null) {
            enableInitJad = MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_CAN_INIT_JAD_DSP, true);
        }
        //当init开关打开时，还需要再校验下广告平台的开关是否打开
        if (enableInitJad) {
            enableInitJad = getJadSDKInitEnable();
        }
        Logger.log("adsdk : enableInitJad = " + enableInitJad);

        return enableInitJad?JAD_INIT_PARAMS:null;
    }

    public static boolean getJadSDKInitEnable() {
        DspInitConfig config = ADABTestUtil.getDspSDKInitEnable();
        if (config != null) {
            return config.jad;
        }
        return false;
    }

    public static IBaiduInitParams getBaiduInitParams() {
        if (enableInitBaidu == null) {
            enableInitBaidu = MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_CAN_INIT_BAIDU_DSP, true);
        }

        //当init开关打开时，还需要再校验下广告平台的开关是否打开
        if (enableInitBaidu) {
            enableInitBaidu = getBaiDuSDKInitEnable();
        }
        Logger.log("adsdk : enableInitBaidu = " + enableInitBaidu);

        return enableInitBaidu ? BAIDU_INIT_PARAMS : null;
    }

    public static boolean getBaiDuSDKInitEnable() {
        DspInitConfig config = ADABTestUtil.getDspSDKInitEnable();
        if (config != null) {
            return config.baidu;
        }
        return false;
    }

    private static final ICSJInitParams CSJ_INIT_PARAMS = new ICSJInitParams() {
        @Override
        public IXmAdSDKCustomController getCustomController() {
            return XM_AD_SDK_CUSTOM_CONTROLLER;
        }

        @Override
        public String appId() {
            return AdManager.CSJ_APPID;
        }

        @Override
        public String appName() {
            return AdManager.CJS_APP_NAME;
        }

        @Override
        public boolean isDebug() {
            return ConstantsOpenSdk.isDebug;
        }

        @Override
        public boolean isShakeEnable() {
            return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_ENABLE_SPLASH_SHAKE_STYLE, true);
        }
    };

    private static final IGDTInitParams GDT_INIT_PARAMS = new IGDTInitParams() {
        @Override
        public IXmAdSDKCustomController getCustomController() {
            return XM_AD_SDK_CUSTOM_CONTROLLER;
        }

        @Override
        public String appId() {
            return AdManager.GDT_APPID;
        }

        @Override
        public String preloadSplashIds() {
            return MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getString(CConstants.Group_ad.ITEM_AD_GDT_PRELOAD_CONFIG);
        }
    };

    private static IBaiduApiProvider baiduApiProvider;
    private static final IBaiduInitParams BAIDU_INIT_PARAMS = new IBaiduInitParams() {
        @Override
        public IXmAdSDKCustomController getCustomController() {
            return XM_AD_SDK_CUSTOM_CONTROLLER;
        }

        @Override
        public String appId() {
            return AdManager.BAIDU_APPID;
        }

        @Override
        public String appName() {
            return AdManager.BAIDU_APP_NAME;
        }

        @Override
        public boolean isDebug() {
            return ConstantsOpenSdk.isDebug;
        }

        @Override
        public void getApiProvider(IGetBaiduApiCallBack iGetBaiduApiCallBack) {
            Log.d("adBundle", "getApiProvider baiduApiProvider = " + baiduApiProvider);
            if (baiduApiProvider == null) {
                Router.getActionByCallback(Configure.BUNDLE_AD, new Router.IBundleInstallCallback() {
                    @Override
                    public void onInstallSuccess(BundleModel bundleModel) {
                        Log.d("adBundle", "onInstallSuccess");
                        try {
                            IAdFunctionAction functionAction = Router.<AdActionRouter>getActionRouter(Configure.BUNDLE_AD).getFunctionAction();
                            baiduApiProvider = functionAction.getBaiduApiProvider();
                            iGetBaiduApiCallBack.onGetSdkApi(baiduApiProvider);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }

                    @Override
                    public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                        Log.d("adBundle", "onLocalInstallError");
                        iGetBaiduApiCallBack.onGetSdkApiFail();
                    }

                    @Override
                    public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                        Log.d("adBundle", "onRemoteInstallError");
                        iGetBaiduApiCallBack.onGetSdkApiFail();
                    }
                }, true, BundleModel.DOWNLOAD_IN_BACKGROUND);
            } else {
                iGetBaiduApiCallBack.onGetSdkApi(baiduApiProvider);
            }
        }
    };

    private static final IJADInitParams JAD_INIT_PARAMS = new IJADInitParams() {
        @Override
        public String appId() {
            return "189312";

        }

        @Override
        public boolean isDebug() {
            return true;
        }

        @Override
        public IXmAdSDKCustomController getCustomController() {
            return XM_AD_JAD_CUSTOM_CONTROLLER;
        }
    };

    private static final IXmAdSDKCustomController XM_AD_SDK_CUSTOM_CONTROLLER = new IXmAdSDKCustomController() {
        @Override
        public boolean isCanUseLocation() {
            return false;
        }

        @Override
        public XmAdLocation getXmLocation() {
            try {
                return new XmAdLocation(LocationService.getInstance().getLatitude(), LocationService.getInstance().getLongitude());
            } catch (NoInitException e) {
                e.printStackTrace();
            }

            return null;
        }

        @Override
        public String getDevImei() {
            return BaseDeviceUtil.getIMEI(ToolUtil.getCtx());
        }

        @Override
        public String getDevOaid() {
            return DeviceUtil.getOAID();
        }

        @Override
        public String getDevEncryptOaid() {
            return DeviceUtil.getEncryptOAID();
        }
    };

    private static IXmAdSDKCustomControllerForJadSDK XM_AD_JAD_CUSTOM_CONTROLLER =  new IXmAdSDKCustomControllerForJadSDK() {
        @Override
        public boolean isCanUseLocation() {
            return false;
        }

        @Override
        public XmAdLocation getXmLocation() {
            try {
                return new XmAdLocation(LocationService.getInstance().getLatitude(), LocationService.getInstance().getLongitude());
            } catch (NoInitException e) {
                e.printStackTrace();
            }

            return null;
        }

        @Override
        public String getDevImei() {
            return BaseDeviceUtil.getIMEI(ToolUtil.getCtx());
        }

        @Override
        public String getDevOaid() {
            return DeviceUtil.getOAID();
        }

        @Override
        public boolean isCanUseIP() {
            return false;
        }

        @Override
        public String getIp() {
            return DeviceUtil.getFormatMacAddress(ToolUtil.getCtx());
        }
    };


    public static IMainAppSelfConfig getSelfConfig() {
        return new IMainAppSelfConfig() {
            @Override
            public String uid() {
                try {
                    return UserInfoMannage.getUid() + "";
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
                return "";
            }

            @Override
            public String userToken() {
                try {
                    return UserInfoMannage.getToken();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
                return "";
            }

            @Override
            public IJumpStrategy getJumpStrategy() {
                try {
                    return createJumpStrategy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
                return null;
            }

            @Override
            public IImageSource getImageSource() {
                try {
                    return createImageSource();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                }
              return null;
            }

            @Override
            public String getCookieForMainApp() {
                try {
                    return CommonRequestM.getInstanse().getCommonCookie(CommonRequestM.NetRequestType.TYPY_XM_ALL_AD, null);
                } catch (Throwable e) {
                    e.printStackTrace();
                }
                return null;
            }

            @Override
            public AdCookieParams getCookieParams() {

                try {
                    String locationResult =
                            LocationService.getInstance().getLocationResult(ToolUtil.getCtx());
                    return new AdCookieParams(locationResult,
                            CommonRequestM.getDInfo(ToolUtil.getCtx()),
                            CommonRequestM.getUMID(ToolUtil.getCtx()),
                            PhoneGrade.getInstance().getDeviceType());
                } catch (Throwable e) {
                    e.printStackTrace();
                }
                return null;
            }

            @Override
            public Map<String,String> getXuid() {
                return XuidManager.INSTANCE.getHttpHeader();
            }

            @Nullable
            @Override
            public Object getOkHttpClient() {
                OkHttpClient okHttpClient = BaseCall.getInstanse().getOkHttpClient();
                if (okHttpClient == null) {
                    return null;
                }
                boolean hasAdRequestZipInterceptor = false;
                boolean hasAdPIInterceptor = false;
                List<Interceptor> networkInterceptors = okHttpClient.interceptors();
                if (networkInterceptors != null){
                    for (Interceptor interceptor : networkInterceptors) {
                        if (interceptor instanceof AdRequestZipInterceptor) {
                            hasAdRequestZipInterceptor = true;
                        }
                        if (interceptor instanceof AdPIInterceptor) {
                            hasAdPIInterceptor = true;
                        }
                        if (hasAdRequestZipInterceptor && hasAdPIInterceptor){
                            break;
                        }
                    }
                }
                if (!hasAdRequestZipInterceptor || !hasAdPIInterceptor){
                    OkHttpClient.Builder builder = okHttpClient.newBuilder();
                    if (!hasAdPIInterceptor) {
                        builder.addInterceptor(new AdPIInterceptor());
                    }
                    if (!hasAdRequestZipInterceptor) {
                        builder.addInterceptor(new AdRequestZipInterceptor());
                    }
                    okHttpClient = builder.build();
                }
                return okHttpClient;
            }

            @Override
            public boolean interceptorJump(String realUrl, SimpleAdModel simpleAdModel) {
                return AdManager.checkGotoGame(realUrl, simpleAdModel.getAdid(), simpleAdModel.getAdType(), simpleAdModel.getPositionName(), simpleAdModel.getRewardActivity());
            }

            @Override
            public boolean jump(IHybridFragment iHybridFragment) {
                if (MainApplication.getMainActivity() instanceof MainActivity && iHybridFragment instanceof BaseFragment2) {
                    if (iHybridFragment.isNeedDoAnimation()) {
                        ((MainActivity) MainApplication.getMainActivity()).startFragment((Fragment) iHybridFragment);
                    } else {
                        ((MainActivity) MainApplication.getMainActivity()).startFragment((Fragment) iHybridFragment, -1, -1);
                    }
                    return true;
                }

                return false;
            }

            @Override
            public long playingTrackId() {
                if (BaseUtil.isMainProcess(ToolUtil.getCtx())) {
                    return PlayTools.getCurTrackId(ToolUtil.getCtx());
                } else {
                    XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
                    if (playerSrvice != null) {
                        PlayableModel currPlayModel = playerSrvice.getCurrPlayModel();
                        if (currPlayModel instanceof Track) {
                            return currPlayModel.getDataId();
                        }
                    }
                    return 0;
                }
            }

            @Override
            public boolean isCurrentTrackPlaying(String trackId) {
                if (TextUtils.isEmpty(trackId)) {
                    return false;
                }
                String playTrackId = playingTrackId() + "";
                return trackId.equals(playTrackId) && XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying();
            }

            @Override
            public void onAdShowRecord() {
                HandlerManager.postOnBackgroundThread(new Runnable() {
                    @Override
                    public void run() {
                        SubContractChannelUtil.getSubContractChannelId(MainApplication.getMyApplicationContext());
                    }
                });
//                Log.i("AdSDKManager","onAdShowRecord回调");
            }

            @Override
            public long playingAlbumId() {
                if (BaseUtil.isMainProcess(ToolUtil.getCtx())) {
                    Track curTrack = PlayTools.getCurTrack(ToolUtil.getCtx());

                    if (curTrack != null) {
                        SubordinatedAlbum album = curTrack.getAlbum();
                        if(album != null) {
                            return album.getAlbumId();
                        } else if (PlayableModel.KIND_RADIO.equals(curTrack.getKind()) || PlayableModel.KIND_SCHEDULE.equals(curTrack.getKind())) {
                            return curTrack.getRadioId();
                        }
                    }
                } else {
                    XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
                    if (playerSrvice != null) {
                        PlayableModel currPlayModel = playerSrvice.getCurrPlayModel();
                        if (currPlayModel instanceof Track) {
                            SubordinatedAlbum album = ((Track) currPlayModel).getAlbum();
                            if (album != null) {
                                return album.getAlbumId();
                            } else if (PlayableModel.KIND_RADIO.equals(((Track) currPlayModel).getKind()) || PlayableModel.KIND_SCHEDULE.equals(((Track) currPlayModel).getKind())) {
                                return ((Track) currPlayModel).getRadioId();
                            }
                        }
                    }
                }

                return 0;
            }

            @Override
            public String adPlayVersion() {
                return AdManager.getAdPlayVersion();
            }

            @Override
            public Activity getTopActivity() {
                return MainApplication.getTopActivity();
            }

            @Override
            public IHybridFragment getHybridFragment(SimpleJumpModel jumpModel) {
                if (jumpModel == null) {
                    return null;
                }
                IHybridFragment nativeHybridFragment;
                if (jumpModel.isHasVideoJoining() && jumpModel.getWebVideoType() == IXmAdConstants.IWebVideoType.VIDEO_VIEW_NEW) {
                    nativeHybridFragment = new AdSDKHybridFragmentNew();
                } else {
                    nativeHybridFragment = new AdSDKHybridFragment();
                }
                nativeHybridFragment.setArguments(createStartNativeHybridFragmentBundle(jumpModel));
                return nativeHybridFragment;
            }

            @Override
            public String getAppChannel() {
                try {
                    return CommonRequestM.getInstanse().getUmengChannel();
                } catch (XimalayaException e) {
                    e.printStackTrace();
                }

                return null;
            }

            @Nullable
            @Override
            public String getUAByWebView() {
                return DeviceUtil.getUserAgentByWebView(ToolUtil.getCtx());
            }

            @Override
            public boolean isUseSdkClickOrShowRecord() {
                return AdSDKManager.canUseSDKShowRecord(MainApplication.getMyApplicationContext())
                        || AdSDKManager.canUseSDKClickRecord(MainApplication.getMyApplicationContext());
            }

            @Override
            public boolean recordMonitor() {
                boolean sdkMonitorUpload = MMKVUtil.getInstance().getBoolean(CConstants.Client.ADSDK_MONITOR_UPLOAD, false);
                Logger.log("AdSDKManager : recordMonitor  " + sdkMonitorUpload);
                return sdkMonitorUpload;
            }

            @Override
            public boolean isEnableXMGzip() {
                return Utils.enableXMGzip;
            }

            @Override
            public void setSourceHanTypeFace(boolean isHeavy, TextView textView) {
                if (textView == null) {
                    return;
                }
                RemoteTypefaceManager.INSTANCE.setRemoteTypeface(isHeavy ? RemoteTypefaceManager.RemoteTypeface.SourceHanSansCNHeavy : RemoteTypefaceManager.RemoteTypeface.SourceHanSerifBold, textView);
            }

            @Override
            public String getPlaySignature(Context context, Map<String, String> specificParams) {
                return EncryptUtil.getInstance(context).getPlaySignature(context, specificParams);
            }

            @Override
            public int getCurScreenHeight() {
                return com.ximalaya.ting.android.framework.util.BaseUtil.getScreenHeight(ToolUtil.getCtx());
            }

            @Override
            public int getCurScreenWidth() {
                return com.ximalaya.ting.android.framework.util.BaseUtil.getScreenWidth(ToolUtil.getCtx());
            }

            @Override
            public String getTicket(String business, String scene) {
                return AdManager.getTicket(business, scene);
            }
        };
    }


    public static Bundle createStartNativeHybridFragmentBundle(SimpleJumpModel jumpModel) {
        if (jumpModel == null) {
            return null;
        }
        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, jumpModel.getUrl());
        bundle.putBoolean(BundleKeyConstants.KEY_IS_LANDSCAPE, jumpModel.isLandScape());
        bundle.putBoolean(NativeHybridFragment.IS_EXTERNAL_URL, jumpModel.isExternal());
        SimpleShareData shareData = jumpModel.getAdShareData();
        if (shareData != null) {
            setBundleShare(bundle, shareData);
        }
        bundle.putBoolean(BundleKeyConstants.KEY_FIT_SOFT_KEYBOARD, true);
        bundle.putString(BundleKeyConstants.KEY_AD_POSITION_NAME, jumpModel.getPositionName());
        AdManager.addResPositionTag(bundle, jumpModel.getPositionName());
        bundle.putBoolean(BundleKeyConstants.KEY_USE_LOTTIE, false);
        bundle.putBoolean(BundleKeyConstants.KEY_SHOW_TITLE, jumpModel.isShowTitle());
        bundle.putBoolean(BundleKeyConstants.KEY_IS_FROM_AD_LANDING_PAGE, true);
        if (jumpModel.isHideInfoForWebUa()) {
            bundle.putBoolean(BundleKeyConstants.KEY_HIDE_UA_INFO, true);
        }
        if (jumpModel.isHasVideoJoining()) {
            bundle.putBoolean("fullScreenWithStatusBar", true);
            bundle.putBoolean(BundleKeyConstants.KEY_SHOW_STATUS_BAR, false);
            bundle.putInt(BundleKeyConstants.KEY_VIDEO_TYPE_HYBRID, jumpModel.getWebVideoType());
        } else {
            bundle.putInt(BundleKeyConstants.KEY_VIDEO_TYPE_HYBRID, 0); // 无有效的视频地址的话，默认走半屏落地页布局，最后不会添加顶部视频
        }
        if (jumpModel.getBusinessExtraSDKInfo() != null) {
            bundle.putInt(BundleKeyConstants.KEY_POP_REMINDER_STYLE, jumpModel.getBusinessExtraSDKInfo().getPopReminderStyle());
            bundle.putString(BundleKeyConstants.KEY_POP_REMINDER_TEXT, jumpModel.getBusinessExtraSDKInfo().getPopReminderText());
        }
        AdSDKAdapterModel sdkAdapterModel = jumpModel.getAdModel();
        if (sdkAdapterModel != null) {
            bundle.putInt(BundleKeyConstants.KEY_OPEN_BOUNCE, sdkAdapterModel.getOpenBounce());
            bundle.putInt(BundleKeyConstants.KEY_BOUNCE_DELAY, sdkAdapterModel.getBounceDelay());
            bundle.putInt(BundleKeyConstants.KEY_AD_ID, sdkAdapterModel.getAdid());
            bundle.putLong(BundleKeyConstants.KEY_AD_RESPONSE_ID, sdkAdapterModel.getResponseId());
            bundle.putBoolean(BundleKeyConstants.KEY_AD_ONE_KEY_LEAVE, sdkAdapterModel.isOneClickLeaveCash());
            if (sdkAdapterModel.getUnlockType() == AdUnlockUtil.UNLOCK_TYPE_TRY_PLAY) {
                Advertis advertis = AdConversionUtil.translateSDKModelToAdvertis(sdkAdapterModel);
                if (advertis != null) {
                    bundle.putParcelable(BundleKeyConstants.KEY_GOTO_HYBRID_VIEW_AD, advertis);
                }
            }
            AdSDKAdapterWebVideoModel webVideoModel = sdkAdapterModel.getWebVideoModel();
            if (webVideoModel != null) {
                bundle.putString(BundleKeyConstants.KEY_WEB_VIEW_TITLE, webVideoModel.getTitle());
                bundle.putString(AdSDKHybridFragmentNew.KEY_WEB_VIEW_BANNER_IMAGE, webVideoModel.getImageUrl());
                bundle.putString(AdSDKHybridFragmentNew.KEY_WEB_VIEW_BANNER_TITLE, webVideoModel.getTitle());
                bundle.putString(AdSDKHybridFragmentNew.KEY_WEB_VIEW_BANNER_DESCRIPTION, webVideoModel.getDesc());
                bundle.putInt(AdSDKHybridFragmentNew.KEY_WEB_VIEW_AUTO_SHOW_TIME, sdkAdapterModel.getAutoShowWebTime());
            }
        }
        return bundle;
    }



    public static Bundle setBundleShare(Bundle bundle, SimpleShareData model) {
        if (bundle == null || model == null) {
            return null;
        }

        bundle.putBoolean(IWebFragment.SHOW_SHARE_BTN, true);
        bundle.putString(IWebFragment.SHARE_COVER_PATH, model.getLinkCoverPath());
        bundle.putString(IWebFragment.SHARE_CONTENT, model.getLinkContent());
        bundle.putString(IWebFragment.SHARE_TITLE, model.getLinkTitle());
        bundle.putString(IWebFragment.SHARE_URL, model.getLinkUrl());
        if (!bundle.containsKey(IWebFragment.IS_EXTERNAL_URL)) {
            bundle.putBoolean(IWebFragment.IS_EXTERNAL_URL, model.isExternalUrl());
        }
        return bundle;
    }

    public static boolean isSDKAd(IBaseAd baseAd) {
        if(baseAd == null) {
            return false;
        }

        return baseAd.getMediationType() != IMediationType.XM;
    }

    public static boolean isGDTAd(IBaseAd baseAd) {
        if (baseAd == null) {
            return false;
        }

        return baseAd.getMediationType() == IMediationType.GDT;
    }

    public static boolean isBaiDuAd(IBaseAd baseAd) {
        if (baseAd == null) {
            return false;
        }

        return baseAd.getMediationType() == IMediationType.BAIDU;
    }

    public static boolean isCSJAd(IBaseAd baseAd) {
        if (baseAd == null) {
            return false;
        }

        return baseAd.getMediationType() == IMediationType.CSJ;
    }
    public static boolean isJADAd(IBaseAd baseAd) {
        if (baseAd == null) {
            return false;
        }

        return baseAd.getMediationType() == IMediationType.JAD;
    }

    public static void bindSourceFromAndAdTag(INativeAd nativeAd, ImageView adTag, int defaultImg,
                                              AdSourceFromView sourceFromView, String positionName) {
        nativeAd.setAdMark(adTag, defaultImg);

        if (sourceFromView != null) {
            if (AdSDKManager.isSDKAd(nativeAd)) {
                sourceFromView.setVisibility(View.GONE);
            } else {

                Object otherInfo = getOtherInfo(nativeAd,
                        INativeAd.OtherInfoKey.SOURCE_FROM_CONFIG_IN_SCREEN_SOURCE);

                if (otherInfo instanceof Integer) {
                    int inScreenSource = (int) otherInfo;

                    Object materialProvideSourceObj = getOtherInfo(nativeAd,
                            INativeAd.OtherInfoKey.SOURCE_FROM_CONFIG_MATERIAL_PROVIDE_SOURCE);
                    if (materialProvideSourceObj instanceof String) {
                        String materialProvideSource = (String) materialProvideSourceObj;
                        if (sourceFromView.setAdvertis(materialProvideSource, inScreenSource,
                                positionName)) {
                            if (adTag != null) {
                                adTag.setVisibility(View.GONE);
                            }
                        }
                    }
                }
            }
        }
    }

    @Nullable
    public static Object getOtherInfo(INativeAd nativeAd, String key) {
        if(nativeAd == null || TextUtils.isEmpty(key)) {
            return null;
        }


        Map<String, Object> otherInfo = nativeAd.getOtherInfo();
        if(otherInfo != null) {
            return otherInfo.get(key);
        }

        return null;
    }

    public static String getProgressText(INativeAd mAdItem, String defaultContent) {
        String progressTitle = defaultContent;

        if (mAdItem != null && AdCommonUtils.checkAppInstalled(mAdItem.getPackageName())) {
            return "立即打开";
        }
        if (!(mAdItem == null || !mAdItem.isAppAd())) {
            switch (mAdItem.getAPPStatus()) {
                case IAppStatus.APP_STATUS_NO_DOWNLOADED:
                case IAppStatus.APP_STATUS_DOWNLOAD_REMOVED:
                    // fix: ZK-13473: 行动按钮文案应显示服务端返回的buttonText字段值
                    if (TextUtils.isEmpty(defaultContent)) {
                        progressTitle = "立即下载";
                    } else {
                        progressTitle = defaultContent;
                    }
                    break;
                case IAppStatus.APP_STATUS_DOWNLOAD_PAUSED:
                    progressTitle = "继续下载";
                    break;
                case IAppStatus.APP_STATUS_INSTALLED:
                    progressTitle = "立即启动";
                    break;
                case IAppStatus.APP_STATUS_UPDATE:
                    progressTitle = "点击更新";
                    break;
                case IAppStatus.APP_STATUS_DOWNLOADING:
                    progressTitle = mAdItem.getProgress() >= 0 ?  mAdItem.getProgress() + "%" : "下载中"; // 特别注意：当进度小于0时，不要使用进度来渲染界面
                    break;
                case IAppStatus.APP_STATUS_DOWNLOADED:
                    progressTitle = "立即安装";
                    break;
                case IAppStatus.APP_STATUS_DOWNLOAD_FAIL:
//                    progressTitle = "下载失败";
                    /**
                     * fix： tb-ZK-13335: 下载失败要展示继续下载
                     */
                    progressTitle = "继续下载";
                    break;
                default:
                    progressTitle = defaultContent;
            }
        }

        if (TextUtils.isEmpty(progressTitle)) {
            progressTitle = "查看详情";
        }

        return progressTitle;
    }

    private static IImageSource createImageSource() {
        return new IImageSource() {
            @Override
            public void downloadImage(String imageUrl, @Nullable ISourceDisplayCallBack callBack, boolean isSavePermanent) {
                ImageManager.from(ToolUtil.getCtx()).downloadBitmap(imageUrl, new ImageManager.DisplayCallback() {
                    @Override
                    public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                        if(callBack != null) {
                            callBack.onResponse(lastUrl, bitmap != null ? new BitmapDrawable(ToolUtil.getCtx().getResources(), bitmap) : null);
                        }
                    }
                }, isSavePermanent);
            }

            @Override
            public void deleteDownloadImage(String imageUrl) {
                ImageManager.from(ToolUtil.getCtx()).deleteBitmapFromDownloadCache(imageUrl);
            }

            @Override
            public void displayImage(String imageUrl, ImageView imageView, Options options,
                                     ISourceDisplayCallBack callBack) {

                int defaultImg = -1;
                int errorImg = -1;
                int imageWidth = imageView != null ? imageView.getWidth() : 0;
                int imageHeight = imageView != null ? imageView.getHeight() : 0;

                if (options != null) {
                    defaultImg = options.defaultImageSource;
                    errorImg = options.errorResId;
                    if (options.targetWidth != 0) {
                        imageWidth = options.targetWidth;
                    }
                    if (options.targetHeight != 0) {
                        imageHeight = options.targetHeight;
                    }
                }

                ImageManager.from(ToolUtil.getCtx()).displayImage(null, imageView, imageUrl, defaultImg, errorImg, imageWidth, imageHeight, new ImageManager.DisplayCallback() {
                    @Override
                    public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                        if (callBack != null) {
                            callBack.onResponse(lastUrl, bitmap != null ? new BitmapDrawable(ToolUtil.getCtx().getResources(), bitmap) : null);
                        }
                    }
                }, null, true, false, true, options == null || options.autoPlay, null);
            }

            @Override
            public boolean hasDownload(String url) {
                return ImageManager.from(ToolUtil.getCtx()).hasDownloaded(url);
            }

            @Override
            public Drawable downloadImageSync(String url) {
                if (ImageManager.isGifUrl(url)) {
                    String filePath = ImageManager.getPicassoCachePath(ToolUtil.getCtx(), url);
                    if (TextUtils.isEmpty(filePath) || !(new File(filePath).exists())) {
                        filePath = ImageManager.from(ToolUtil.getCtx()).getPathAfterDnsCache(url);
                    }

                    if (!TextUtils.isEmpty(filePath) && new File(filePath).exists()) {
                        try {
                            return ImportSDKHelper.getAdFrameSequenceDrawable(filePath);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

                Bitmap bitmap =
                        ImageManager.from(ToolUtil.getCtx()).downloadBitmapSync(url, null, false);
                return bitmap != null ? new BitmapDrawable(ToolUtil.getCtx().getResources(), bitmap) : null;
            }
        };
    }

    private static IJumpStrategy createJumpStrategy() {
        return new IJumpStrategy() {
            @Override
            public void jumpToGameBundle(String realUrl, SimpleAdModel simpleAdModel) {
                if(simpleAdModel != null) {
                    AdGameUtil.jumpToGameBundle(realUrl, simpleAdModel.getAdid(),
                            simpleAdModel.getAdType(), simpleAdModel.getPositionName(), true);
                } else {
                    AdGameUtil.jumpToGameBundle(realUrl, 0, 0, null, true);
                }
            }

            @Override
            public void jumpToSearchBrandAd(String s) {
                if (android.text.TextUtils.isEmpty(s)) {
                    return;
                }
                BaseFragment fragment = null;
                Activity mainActivity = BaseApplication.getMainActivity();
                if (mainActivity == null || !(mainActivity instanceof MainActivity)) {
                    return;
                }
                try {
                    fragment =
                            Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction() != null ?
                                    Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction().loadSearchFragmentByWord((MainActivity) mainActivity, s) :
                                    null;
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (fragment != null) {
                    ((MainActivity) mainActivity).startFragment(fragment);
                }
            }

            @Override
            public void doAlbumCollect(Fragment fragment, String albumId, CollectSuccessCallBack collectSuccessCallBack) {
                AlbumM albumM = new AlbumM();
                albumM.setId(Long.parseLong(albumId));
                albumM.setFavorite(false);
                AlbumEventManage.doCollectActionV2(albumM, fragment, new ICollectStatusCallback() {
                            @Override
                            public void onCollectSuccess(int code, final boolean isCollected) {
                                if (isCollected && collectSuccessCallBack != null) {
                                    collectSuccessCallBack.onCollectSuccess();
                                }
                            }

                    @Override
                    public void onError() {

                    }
                });
            }

            @Override
            public boolean jumpToDownloadListPage() {
                // 使用sdk的点击， 则跳到sdk下载中心,return false 交由sdk处理
                if (AdSDKManager.canUseSDKShowRecord(MainApplication.getMyApplicationContext()) ||
                        AdSDKManager.canUseSDKClickRecord(MainApplication.getMyApplicationContext())) {
                    return false;
                }
                if (MainApplication.getMainActivity() instanceof MainActivity) {
                    ((MainActivity) MainApplication.getMainActivity()).startFragment(AdDownloadListFragment.newInstance());
                    return true;
                }
                return false;
            }

            @Override
            public boolean jumpNativeWebViewClick(AdSDKAdapterModel sdkAdapterModel,
                                                  AnchorAlbumAd anchorAlbumAd) {
                try {
                    Bundle bundle = new Bundle();
                    if (sdkAdapterModel.getClickJumpType() != 0) {
                        // 默认跳到视频页，
                        bundle.putBoolean(VideoPlayParamsBuildUtil.KEY_FOCUS_VIDEO_PLAY_TAB, true);
                    }
                    bundle.putString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_LINK, sdkAdapterModel.getRealLink());
                    bundle.putString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_TITLE, sdkAdapterModel.getName());
                    bundle.putBoolean(VideoPlayParamsBuildUtil.KEY_IS_OPEN_NATIVE_WEBVIEW, true);
                    bundle.putParcelable(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_ADVERITS, sdkAdapterModel);
                    bundle.putString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_AD_POSITION_NAME, anchorAlbumAd.getPositionName());

                    bundle.putString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_AD_TRACK_ID, anchorAlbumAd.getPromoteTrackId()+"");

                    // bundle 信息里包含原生落地页信息，先存一下
                    MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext())
                            .saveString(PreferenceConstantsInOpenSdk.ITEM_PLAY_PAGE_AD_VIDEO_INFO_SET, anchorAlbumAd.getPromoteTrackId()+"true");
                    AdPlayTools.goVideoPlayByTrackId(anchorAlbumAd.getPromoteTrackId(), bundle);

                    return true;
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return false;
            }

            @Override
            public void iVClick(SimpleJumpModel simpleJumpModel) {
                Bundle bundle = createStartNativeHybridFragmentBundle(simpleJumpModel);
                bundle.putBoolean(BundleKeyConstants.KEY_IS_FROM_AD_VI_CLICK, true);
                if (MainApplication.getMainActivity() instanceof MainActivity) {
                    ((MainActivity) MainApplication.getMainActivity()).startAdFragment(bundle);
                }
            }

            @Override
            public void jumpRewardVideo(AdSDKAdapterModel adSDKAdapterModel) {
                AdUnlockVipTimeManager.unlockVipTime(adSDKAdapterModel.getPositionName());
            }

            @Override
            public void jumpFullVideoPage(AdSDKAdapterModel adSDKAdapterModel) {
                Activity mainActivity = MainApplication.getMainActivity();
                if (mainActivity instanceof MainActivity){
                    Advertis advertis = AdConversionUtil.translateSDKModelToAdvertis(adSDKAdapterModel);
                    ((MainActivity) mainActivity).startFragment(AdFullVideoFragment.newInstance(advertis));
                }
            }

            @Override
            public void jumpRewardVideoForSDK(AdSDKAdapterModel adSDKAdapterModel) {
                loadRewardAdFromSDK(adSDKAdapterModel);
            }

            private void loadRewardAdFromSDK(AdSDKAdapterModel adSDKAdapterModel) {
                if (MainApplication.getTopActivity() == null || adSDKAdapterModel == null) {
                    return;
                }

                int watchVideoTime = RewardExtraParams.DEFAULT_CLOSE_TIME;

                XmRewardExtraParam xmRewardExtraParam = new XmRewardExtraParam();
                xmRewardExtraParam.setCanCloseTime(watchVideoTime);//关闭按钮展示时间,单位s
                Bundle bundle = new Bundle();
                bundle.putInt("linkType", 105);
                xmRewardExtraParam.setExtraParams(bundle);

                LoadingDialog loadingDialog = new LoadingDialog(MainApplication.getTopActivity());
                loadingDialog.setTitle("正在努力加载中");
                loadingDialog.showIcon(true);
                loadingDialog.show();

                final String positionId = IAdConstants.IAdPositionId.REWARD_VIDEO_MIX_AD;

                RewardVideoAdManager.getInstance().loadRewardAdFromSDK(MainApplication.getTopActivity(), positionId, xmRewardExtraParam, adSDKAdapterModel,
                        new IRewardVideoAdListener() {
                            boolean isPlaying;

                            @Override
                            public void onAdLoad(HashMap hashMap) {
                                Logger.d("msg_video_forward", "  onAdLoad ---- -  ");
                                dismissLoadingDialog();
                                isPlaying = XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).isPlaying();
                                if (isPlaying) {
                                    XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).pause(PauseReason.Business.AdSdkJumpStrategy);
                                }
                            }

                            @Override
                            public void onAdClose() {
                                rePlay();
                                Logger.d("msg_video_forward", "  onAdClose ---- -  ");
                            }

                            @Override
                            public void onLoadError(int i, String s) {
                                fallBackJump(adSDKAdapterModel);
                                dismissLoadingDialog();
                                rePlay();
                                Logger.w("msg_video_forward", "  --- ---- -onLoadError ---- -  i = " + i + " , msg = " + s);
                            }

                            @Override
                            public void onAdPlayError(int i, String s) {
                                dismissLoadingDialog();
                                rePlay();
                                Logger.d("msg_video_forward", "  --- ---- -onAdPlayError ---- -  i = " + i + " , msg = " + s);
                            }

                            @Override
                            public void onAdPlayStart() {
                                Logger.w("msg_video_forward", "  --- ---- -onAdPlayStart ---- -  i = ");
                            }

                            @Override
                            public void onAdClick() {
                                Logger.w("msg_video_forward", "  --- ---- -onAdClick ---- -  i = ");
                            }

                            @Override
                            public void onReward(boolean realFinishTask) {
                                Logger.w("msg_video_forward", "  --- ---- -onReward ---- -  i = ");
                            }

                            @Override
                            public void onVideoComplete() {
                                Logger.w("msg_video_forward", "  --- ---- -onVideoComplete ---- -  i = ");
                            }

                            private void dismissLoadingDialog() {
                                if (loadingDialog != null && loadingDialog.isShowing()) {
                                    try {
                                        loadingDialog.dismiss();
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            }

                            private void rePlay() {
                                if (isPlaying) {
                                    XmPlayerManager.getInstance(ToolUtil.getCtx()).play();
                                    isPlaying = false;
                                }
                            }

                            private long mLastFallBackJumpTime = 0;

                            private void fallBackJump(AdSDKAdapterModel adSDKAdapterModel) {
                                if (adSDKAdapterModel == null) {
                                    return;
                                }
                                //开关，默认true.
                                boolean enable = ConfigureCenter.getInstance().getBool("ad", "rewardAdFallBackJumpEnable", true);
                                if (!enable) {
                                    return;
                                }
                                //1s钟内有过兜底行为，就不再兜底，防止重复或者循环
                                if (System.currentTimeMillis() - mLastFallBackJumpTime < 1000) {
                                    Log.d("loadRewardAdFromSDK", "fallBackJump: 短时间内发生兜底跳转");
                                    return;
                                }
                                mLastFallBackJumpTime = System.currentTimeMillis();
                                AdReportModel.Builder builder = AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK, adSDKAdapterModel.getPositionName())
                                        .showType(Advertis.SHOW_TYPE_VIDEO)
                                        .adPlayVersion(AdManager.getAdPlayVersion());

                                builder.playPageRevision(PlayPageStyleUtil.getNewPageStyle() + "");
                                builder.newPlayPageVersion(NewPlayPageUtil.getNewPageParams());
                                builder.onlyGotoClickNoRecord(true);
                                Advertis advertis = AdConversionUtil.translateSDKModelToAdvertis(adSDKAdapterModel);
                                //兜底当作linkType 为1来跳转
                                advertis.setLinkType(LINK_TYPE_WEB);
                                AdManager.hanlderSoundAdClick(MainApplication.getMyApplicationContext(), advertis,
                                        builder.build());
                                SoundPatchAdTrace.reportIncentiveJumpFallBack(advertis);
                            }
                        });

            }

            @Override
            public boolean jumpPlayWebViewClick(AdSDKAdapterModel adSDKAdapterModel) {
                if (adSDKAdapterModel == null) {
                    return false;
                }
                try {
                    if (adSDKAdapterModel.getJumpTrackId() <= 0
                            && adSDKAdapterModel.getBusinessExtraInfo() != null && adSDKAdapterModel.getBusinessExtraInfo().getJumpTrackId() > 0) {
                        adSDKAdapterModel.setJumpTrackId(adSDKAdapterModel.getBusinessExtraInfo().getJumpTrackId());
                        adSDKAdapterModel.setAutoJumpTime(adSDKAdapterModel.getBusinessExtraInfo().getAutoJumpTime());
                    }
                    long trackId = adSDKAdapterModel.getJumpTrackId();
                    if (trackId > 0) {
                        Bundle bundle = new Bundle();
                        bundle.putParcelable(AdPlayNativeWebManager.KEY_NATIVE_PLAY_ADVERTIS, adSDKAdapterModel);
                        bundle.putLong(AdPlayNativeWebManager.KEY_NATIVE_PLAY_TRACK_ID, trackId);
                        AdPlayTools.goPlayByTrackId(trackId, bundle);
                        return true;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return false;
            }

            @Override
            public IRewardFragmentWrapper addRewardFragmentToMainActivity(Fragment sdkChildFragment) {
                AdSdkRewardFragmentWrapper rewardFragmentWrapper = new AdSdkRewardFragmentWrapper(sdkChildFragment);
                Activity mainActivity = MainApplication.getMainActivity();
                if(mainActivity instanceof MainActivity) {
                    ((MainActivity) mainActivity).startFragment(rewardFragmentWrapper);
                }
                return rewardFragmentWrapper;
            }
        };
    }

    public static boolean hasAdBundleInit() {
        return !(ConstantsOpenSdk.isBundleFrameWork &&
                Configure.adBundleModel.isDl &&
                !Configure.adBundleModel.hasGenerateBundleFile &&
                !Configure.adBundleModel.hasInitApplication);
    }

//    public static Advertis adModelToAdvertis(AdModel adModel) {
//        if(adModel == null) {
//            return null;
//        }
//
//        Advertis advertis = new Advertis();
//        advertis.setAdid(adModel.getAdid());
//        advertis.setName(adModel.getName());
//        advertis.setClickType(adModel.getClickType());
//        advertis.setLinkUrl(adModel.getLinkUrl());
//        advertis.setImageUrl(adModel.getCover());
//        advertis.setLogoUrl(adModel.getLogo());
//        advertis.setSoundUrl(adModel.getSoundUrl());
//        advertis.setThirdStatUrl(adModel.getThirdStatUrl());
//        advertis.setVolume(adModel.getVolume());
//        advertis.setInteractiveType(adModel.getInteractiveType());
//        advertis.setSoundType(adModel.getSoundType());
//        advertis.setIsAutoNotifyInstall(adModel.isAutoNotifyInstall());
//        advertis.setShareFlag(adModel.isShareFlag());
//
//        AdShareData shareData = adModel.getShareData();
//        if (shareData != null) {
//            AdShareDataForOpenSDK shareDataForOpenSDK = new AdShareDataForOpenSDK();
//            shareDataForOpenSDK.setLinkUrl(shareData.getLinkUrl());
//            shareDataForOpenSDK.setExternalUrl(shareData.isExternalUrl());
//            shareDataForOpenSDK.setLinkTitle(shareData.getLinkTitle());
//            shareDataForOpenSDK.setLinkCoverPath(shareData.getLinkCoverPath());
//            shareDataForOpenSDK.setLinkContent(shareData.getLinkContent());
//            advertis.setShareData(shareDataForOpenSDK);
//        }
//
//        advertis.setDuringPlay(adModel.isDuringPlay());
//        advertis.setAdtype(adModel.getAdtype());
//        advertis.setClickable(adModel.isClickable());
//        advertis.setClickTokens(adModel.getClickTokens());
//        advertis.setShowTokens(adModel.getShowTokens());
//        advertis.setRealLink(adModel.getRealLink());
//        advertis.setRecSrc(adModel.getRecSrc());
//        advertis.setRecTrack(adModel.getRecTrack());
//        advertis.setDescription(adModel.getDescription());
//        advertis.setLandScape(adModel.isLandScape());
//        advertis.setSubCover(adModel.getSubCover());
//        advertis.setSubName(adModel.getSubName());
//        advertis.setPositionId(adModel.getPositionId());
//        advertis.setIsInternal(adModel.getIsInternal());
//        advertis.setWordOfMouth(adModel.isWordOfMouth());
//        advertis.setDynamicImage(adModel.getDynamicImage());
//        advertis.setAdpr(adModel.getAdpr());
//        advertis.setBucketIds(adModel.getBucketIds());
//        advertis.setPlanId(adModel.getPlanId());
//        advertis.setClickTitle(adModel.getClickTitle());
//
//        AnchorTimeRange anchorTimeRange = adModel.getAnchorTimeRange();
//
//        if (anchorTimeRange != null) {
//            com.ximalaya.ting.android.opensdk.model.advertis.AnchorTimeRange anchorTimeRangeAdvertis = new com.ximalaya.ting.android.opensdk.model.advertis.AnchorTimeRange();
//            anchorTimeRangeAdvertis.setFrom(anchorTimeRange.getFrom());
//            anchorTimeRangeAdvertis.setTo(anchorTimeRange.getTo());
//            anchorTimeRangeAdvertis.setShowed(anchorTimeRange.isShowed());
//            anchorTimeRangeAdvertis.setClosed(anchorTimeRange.isClosed());
//            anchorTimeRangeAdvertis.setPlaying(anchorTimeRange.isPlaying());
//            advertis.setAnchorTimeRange(anchorTimeRangeAdvertis);
//        }
//
//        CouponInfo couponInfo = adModel.getCouponInfo();
//
//        if(couponInfo != null) {
//            com.ximalaya.ting.android.opensdk.model.advertis.CouponInfo couponInfoAd = new com.ximalaya.ting.android.opensdk.model.advertis.CouponInfo();
//            couponInfoAd.setAccept(couponInfo.isAccept());
//            couponInfoAd.setDescription(couponInfo.getDescription());
//            couponInfoAd.setStartTime(couponInfo.getStartTime());
//            couponInfoAd.setEndTime(couponInfo.getEndTime());
//            couponInfoAd.setAccept(couponInfo.isAccept());
//            advertis.setCouponInfo(couponInfoAd);
//        }
//
//        advertis.setAdProvider(adModel.getAdpr());
//        advertis.setDpRealLink(adModel.getDpRealLink());
//        advertis.setTrueExposure(adModel.isTrueExposure());
//        advertis.setAdBucketIds(adModel.getAdBucketIds());
//        advertis.setPopupId(adModel.getPopupId());
//        advertis.setPausedRequestAd(adModel.isPausedRequestAd());
//        advertis.setButtonText(adModel.getButtonText());
//        List<BootUp> bootUps = adModel.getBootUps();
//        if(bootUps != null) {
//            List<com.ximalaya.ting.android.opensdk.model.advertis.BootUp> bootUpAds = new ArrayList<>();
//            for (BootUp tempBootUp : bootUps) {
//                com.ximalaya.ting.android.opensdk.model.advertis.BootUp bootUp =
//                        new com.ximalaya.ting.android.opensdk.model.advertis.BootUp();
//
//                bootUp.setOrder(tempBootUp.getOrder());
//                bootUp.setType(tempBootUp.getType());
//                bootUp.setCover(tempBootUp.getCover());
//                bootUp.setVideoCover(tempBootUp.getVideoCover());
//                bootUp.setCarouselCovers(tempBootUp.getCarouselCovers());
//
//                bootUpAds.add(bootUp);
//                advertis.setBootUps(bootUpAds);
//            }
//        }
//
//        advertis.setBrandName(adModel.getBrandName());
//        advertis.setPreviewAd(adModel.isPreviewAd());
//        advertis.setCopywriting(adModel.getCopywriting());
//        advertis.setVipPaymentLink(adModel.getVipPaymentLink());
//        advertis.setCache(adModel.isCache());
//        advertis.setAdShowTime(adModel.getAdShowTime());
//        advertis.setPlayMode(adModel.getPlayMode());
//        advertis.setFrameCover(adModel.getFrameCover());
//        advertis.setGiantVideoCover(adModel.getGiantVideoCover());
//        advertis.setGiantCover(adModel.getGiantCover());
//        advertis.setShowstyle(adModel.getShowstyle());
//        advertis.setVideoCover(adModel.getVideoCover());
//        advertis.setGiantVolume(adModel.getGiantVolume());
//        advertis.setDspPositionId(adModel.getDspPositionId());
//        advertis.setCloseStyle(adModel.getCloseStyle());
//        advertis.setActionButtonStyle(adModel.getActionButtonStyle());
//        advertis.setSkipTipStyle(adModel.getSkipTipStyle());
//        advertis.setColumnSequence(adModel.getColumnSequence());
//        advertis.setSponsorColor(adModel.getSponsorColor());
//        advertis.setSponsorCover(adModel.getSponsorCover());
//        advertis.setBannerCover(adModel.getBannerCover());
//        advertis.setTouchCover(adModel.getTouchCover());
//        advertis.setClientIp(adModel.getClientIp());
//        advertis.setGestureEnabled(adModel.isGestureEnabled());
//        advertis.setGestureCode(adModel.getGestureCode());
//        advertis.setGestureColor(adModel.getGestureColor());
//        advertis.setGestureCopywriting(adModel.getGestureCopywriting());
//        advertis.setGestureStartMs(adModel.getGestureStartMs());
//        advertis.setGestureInstructions(adModel.getGestureInstructions());
//        advertis.setShowDelayMs(adModel.getShowDelayMs());
//        advertis.setProviderName(adModel.getProviderName());
//        advertis.setInScreenSource(adModel.getInScreenSource());
//        advertis.setMaterialProvideSource(adModel.getMaterialProvideSource());
//        advertis.setSoundEnabled(adModel.isSoundEnabled());
//        advertis.setPlayFollowHeaderHint(adModel.isPlayFollowHeaderHint());
//        advertis.setChargeTime(adModel.getChargeTime());
//        advertis.setEffectiveExposure(adModel.isEffectiveExposure());
//        advertis.setLinkType(adModel.getLinkType());
//        advertis.setWxMiniProgramId(adModel.getWxMiniProgramId());
//        advertis.setShowTokenEnable(adModel.isShowTokenEnable());
//        advertis.setClickTokenEnable(adModel.isClickTokenEnable());
//        advertis.setStrongReminder(adModel.isStrongReminder());
//        advertis.setFreeTime(adModel.getFreeTime());
//        advertis.setDisplayAnimation(adModel.getDisplayAnimation());
//        advertis.setNeedPreRequest(adModel.isNeedRender());
//        advertis.setButtonIconUrl(adModel.getButtonIconUrl());
//        advertis.setIconAnimation(adModel.getIconAnimation());
//        advertis.setTags(adModel.getTags());
//        advertis.setRecordedVirtual(adModel.isRecordedVirtual());
//        advertis.setHightingPicUrl(adModel.getHightingPicUrl());
//        advertis.setHightingCover(adModel.getHightingCover());
//        advertis.setExternalType(adModel.getExternalType());
//        advertis.setNeedRender(adModel.isNeedRender());
//        advertis.setRenderWidth(adModel.getRenderWidth());
//        advertis.setRenderHeight(adModel.getRenderHeight());
//        advertis.setSoundTipsUrl(adModel.getSoundTipsUrl());
//        advertis.setExemptionUserType(adModel.getExemptionUserType());
//        advertis.setStrongType(adModel.getStrongType());
//        advertis.setIncreaseFreeTime(adModel.getIncreaseFreeTime());
//        advertis.setForwardVideoTime(adModel.getForwardVideoTime());
//
//        advertis.setCommonReportMap(adModel.getCommonReportMap());
//        advertis.setClientPageMode(adModel.getClientPageMode());
//        advertis.setDownloadAppLogo(adModel.getDownloadAppLogo());
//        advertis.setDownloadAppName(adModel.getDownloadAppName());
//        advertis.setNeedDownloadProgressBar(adModel.isNeedDownloadProgressBar());
//
//        advertis.setDazzleType(adModel.getDazzleType());
//        advertis.setDazzleStartTime((float) adModel.getDazzleStartTime());
//        advertis.setDazzleCover1(adModel.getDazzleCover1());
//        advertis.setDazzleCover2(adModel.getDazzleCover2());
//        advertis.setDazzleCover3(adModel.getDazzleCover3());
//        advertis.setButtonCover(adModel.getButtonCover());
//        advertis.setBox(adModel.isBox());
//        advertis.setBoxCover(adModel.getBoxCover());
//
//        advertis.setNeedShowJumpText(adModel.getNeedShowJumpText());
//        advertis.setClickableAreaType(adModel.getClickableAreaType());
//        advertis.setAdJumpText(adModel.getAdJumpText());
//        advertis.setJumpModeType(adModel.getJumpModeType());
//        advertis.setJumpLightColor(adModel.getJumpLightColor());
//        advertis.setSmallImageUrl(adModel.getSmallImageUrl());
//        advertis.setScheme(adModel.getScheme());
//        advertis.setChainTouchId(adModel.getChainTouchId());
//        advertis.setEnableShowProcessButton(adModel.isEnableShowProcessButton());
//        advertis.setEnableContinuePlay(adModel.isEnableContinuePlay());
//
//        AdWebVideoModel webVideoModel = adModel.getWebVideoModel();
//        if (webVideoModel != null) {
//            com.ximalaya.ting.android.opensdk.model.advertis.AdWebVideoModel adWebVideoModel = new com.ximalaya.ting.android.opensdk.model.advertis.AdWebVideoModel();
//            adWebVideoModel.setWebVideoUrl(webVideoModel.getWebVideoUrl());
//            adWebVideoModel.setLastVideoPlayPosition(webVideoModel.getLastVideoPlayPosition());
//            adWebVideoModel.setPlayMute(webVideoModel.isPlayMute());
//            advertis.setWebVideoModel(adWebVideoModel);
//        }
//
//        advertis.setDownloadPopupStyle(adModel.getDownloadPopupStyle());
//        advertis.setDownloadAppDesc(adModel.getDownloadAppDesc());
//        advertis.setVideoFirstFrame(adModel.getVideoFirstFrame());
//        advertis.setAdDownloaderType(adModel.getAdDownloaderType());
//
//        SplashViewSize skipSize = adModel.getSkipSize();
//        if(skipSize != null) {
//            com.ximalaya.ting.android.opensdk.model.advertis.splash.SplashViewSize splashViewSize = new com.ximalaya.ting.android.opensdk.model.advertis.splash.SplashViewSize();
//            splashViewSize.setHorizontal(skipSize.getHorizontal());
//            splashViewSize.setVertical(skipSize.getVertical());
//            advertis.setSkipSize(splashViewSize);
//        }
//
//        SplashViewSize aroundTouchSize = adModel.getAroundTouchSize();
//        if(aroundTouchSize != null) {
//            com.ximalaya.ting.android.opensdk.model.advertis.splash.SplashViewSize splashViewSize = new com.ximalaya.ting.android.opensdk.model.advertis.splash.SplashViewSize();
//            splashViewSize.setHorizontal(aroundTouchSize.getHorizontal());
//            splashViewSize.setVertical(aroundTouchSize.getVertical());
//            advertis.setSkipSize(splashViewSize);
//        }
//
//        advertis.setAroundSkipTouchType(adModel.getAroundSkipTouchType());
//        advertis.setTouchText(adModel.getTouchText());
//        advertis.setDownloadMonitorMoment(adModel.getDownloadMonitorMoment());
//        advertis.setLandingPageResId(adModel.getLandingPageResId());
//        advertis.setDownloadProgressBarClickType(adModel.getDownloadProgressBarClickType());
//
//        advertis.setInteractAdType(adModel.getInteractiveType());
//        advertis.setInteractAdNeedPopup(adModel.getInteractAdNeedPopup());
//        advertis.setInteractAdPopupDpText(adModel.getInteractAdPopupDpText());
//        advertis.setInteractAdPopupText(adModel.getInteractAdPopupText());
//
//        advertis.setEnableDownloadPopUp(adModel.isEnableDownloadPopUp());
//        advertis.setDownloadPopUpClickArea(adModel.getDownloadPopUpClickArea());
//        advertis.setAppPackageName(adModel.getAppPackageName());
//        advertis.setResponseId(adModel.getResponseId());
//
//        return advertis;
//    }
}
