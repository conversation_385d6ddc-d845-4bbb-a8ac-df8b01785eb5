package com.ximalaya.ting.android.host.manager.ad;


import com.ximalaya.ting.android.adsdk.aggregationsdk.record.adtrace.HeadSoundAdEventRecord;
import com.ximalaya.ting.android.adsdk.aggregationsdk.record.adtrace.RequestTimeRecord;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;

import java.util.HashMap;
import java.util.Map;

public class HeadSoundAdRecordUtil {

    public static final String PATH_SOUND_PATCH = "soundPatch";
    public static final String PATH_XM_AD = "xm_ad";
    public static final String PATH_BASE_INFO = "baseInfo";

    private static final String EVENT_HEAD_SOUND_AD_LOAD = "load";
    private static final String EVENT_HEAD_SOUND_AD_PLAY = "play";
    private static final String EVENT_HEAD_SOUND_AD_SKIP = "skip";

    private static void reportRequestTime(String requestPath,long millis){
        if (millis <= 0 || millis > 20_000) return;
        if (!ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_AD_REQUEST_TIME_RECORD_SWTCH, false)){
            return;
        }
        RequestTimeRecord.reportRequestTime(requestPath, (int)millis);
    }

    public static void reportSoundPatchTime(long millis){
        reportRequestTime(PATH_SOUND_PATCH, millis);
    }

    public static void reportXmAdTime(long millis){
        reportRequestTime(PATH_XM_AD, millis);
    }

    public static void reportBaseInfoTime(long millis){
        reportRequestTime(PATH_BASE_INFO, millis);
    }

    public static void reportHeadSoundAdEvent(String event, Map<String, String> params){
        if (params == null) {
            return;
        }
        boolean frontInsertAdReportAdTrace = ConfigureCenter.getInstance().getBool(
                CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_AD_FRONT_INSERT_AD_REPORT_ADTRACE,
                false);
        if (!frontInsertAdReportAdTrace){
            return;
        }
        Map<String, String> optData = new HashMap<>();
        if (EVENT_HEAD_SOUND_AD_SKIP.equals(event)){
            optData.put("requestTime", params.get("requestTime") + "");
            optData.put("soundUrl", params.get("soundUrl") + "");
            optData.put("reason", params.get("reason") + "");
        }
        optData.put("isPreRequest", params.get("isPreRequest") + "");
        String trackId = params.get("trackId") + "";
        String responseId = params.get("responseId") + "";
        HeadSoundAdEventRecord.reportSoundAdEvent(event, trackId, responseId, optData);
    }
}
