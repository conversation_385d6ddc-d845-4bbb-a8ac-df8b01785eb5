package com.ximalaya.ting.android.host.manager.ad;

import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

public class MiniProgramTaskManager {

    private static final String TAG = "MiniProgramTaskManager";
    private static final String KEY_AD_ID = "key_ad_id";
    private static final String KEY_REMAIN_DURATION = "key_remain_duration";
    private static boolean taskClicked = false;
    private static long jumpMillis;
    private static long remainDuration;
    private static Advertis sAdvertis;
    private static PullAliveTaskManager.OnTaskExecuteCallback mCallback;


    public static void clickTask(Advertis advertis, final PullAliveTaskManager.OnTaskExecuteCallback callback) {
        if (advertis == null || callback == null) return;
        resetData();
        taskClicked = true;
        sAdvertis = advertis;
        mCallback = callback;
        remainDuration = advertis.getActualStayTime() * 1000L;
        int lastAdId = MMKVUtil.getInstance().getInt(KEY_AD_ID);
        if (lastAdId == advertis.getAdid()) {
            remainDuration = MMKVUtil.getInstance().getLong(KEY_REMAIN_DURATION, remainDuration);
        }
        Logger.d(TAG, "小程序任务需要跳转时长 = " + remainDuration + "ms");
    }


    public static void onPageResume() {
        if (taskClicked && jumpMillis > 0){
            long currentTimeMillis = System.currentTimeMillis();
            if (ConstantsOpenSdk.isDebug) {
                Logger.d(TAG, "回到福利页 ： " + DateTimeUtil.long2String(currentTimeMillis, "MM-dd HH:mm:ss"));
            }
            remainDuration = remainDuration - (currentTimeMillis - jumpMillis);
            if (remainDuration <= 0){
                // 任务完成
                Logger.d(TAG, "任务完成");
                onRewardFinish(PullAliveTaskManager.CODE_REWARD_SUCCESS, "小程序任务完成");
                MMKVUtil.getInstance().removeByKey(KEY_AD_ID);
                MMKVUtil.getInstance().removeByKey(KEY_REMAIN_DURATION);
            } else {
                // 任务失败
                Logger.d(TAG, "未满足任务时长，跳转时长 = " + (currentTimeMillis - jumpMillis) + "ms");
                onRewardFinish(PullAliveTaskManager.CODE_TASK_FAILED_SHORT_OF_TIME, "小程序任务未满足时长");
                MMKVUtil.getInstance().saveInt(KEY_AD_ID, sAdvertis.getAdid());
                MMKVUtil.getInstance().saveLong(KEY_REMAIN_DURATION, remainDuration);
            }
            resetData();
        }
    }

    public static void onPagePause() {
        if (taskClicked){
            jumpMillis = System.currentTimeMillis();
            if (ConstantsOpenSdk.isDebug) {
                Logger.d(TAG, "跳转离开福利页 ： " + DateTimeUtil.long2String(jumpMillis, "MM-dd HH:mm:ss"));
            }
        }
    }

    private static void onRewardFinish(int code, String message ) {
        if (sAdvertis == null || mCallback == null) return;
        mCallback.onSuccess(code, message);
    }

    private static void resetData(){
        sAdvertis = null;
        mCallback = null;
        taskClicked = false;
        jumpMillis = -1;
        remainDuration = -1;
    }
}
