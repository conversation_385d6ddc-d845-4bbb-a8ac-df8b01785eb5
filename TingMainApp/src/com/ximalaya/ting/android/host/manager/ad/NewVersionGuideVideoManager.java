package com.ximalaya.ting.android.host.manager.ad;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;

/**
 * Created by le.xin on 2021/4/23.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class NewVersionGuideVideoManager {

    // 要展示的版本名称
    private static final String SHOW_NEW_VIDEO_VERSION = "8.0.1";
    // 视频尺寸比例
    private static float HINT_VIDEO_HEIGHT_WIDTH_RATIO = 1754 / 810f;

    private static int HINT_VIDEO_ID = 0;

    // 是否可能显示新版引导视频
    public static boolean willShowNewVideoVersion(Context context) {
        if (HINT_VIDEO_ID <= 0) {
            return false;
        }

        String versionName = getCurVersionName(context);

        if (SHOW_NEW_VIDEO_VERSION.equals(versionName)) {
            String string =
                    MmkvCommonUtil.getInstance(context).getString(PreferenceConstantsInHost.KEY_HAS_SHOW_HINT_VIDEO);
            if (!TextUtils.isEmpty(string) && (SHOW_NEW_VIDEO_VERSION + true).equals(string)) {
                return false;
            }
            return true;
        }

        MmkvCommonUtil.getInstance(context).saveString(PreferenceConstantsInHost.KEY_HAS_SHOW_HINT_VIDEO, versionName + false);
        return false;
    }

    public static void showNewHintVideo(Context context) {
        MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).saveString(
                PreferenceConstantsInHost.KEY_HAS_SHOW_HINT_VIDEO, getCurVersionName(context) + true);
    }

    private static String getCurVersionName(Context context) {
        String versionName = "";
        try {
            versionName = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0).versionName;
            if (!TextUtils.isEmpty(versionName)) {
                String[] str = versionName.split("\\.");
                if (str.length > 3) {
                    StringBuilder sb = null;
                    for (int i = 0; i < 3; i++) {
                        if (sb == null) {
                            sb = new StringBuilder();
                            sb.append(str[i]);
                        } else {
                            sb.append(".");
                            sb.append(str[i]);
                        }
                    }
                    versionName = sb.toString();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return versionName;
    }

    public static void clipViewToShow(View view, int screenWidth, int screenHeight) {
        if(view == null) {
            return;
        }

        // 保持比例并铺满屏幕，多的裁掉，尝试了VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING，没有效果，通过-margin来实现
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            if (HINT_VIDEO_HEIGHT_WIDTH_RATIO > 0) {
                // 在异形屏上没有很好的办法取到正确的屏幕高度，所以尽量取控件本身的高度来代替
                float screenHeightWidthRatio = screenHeight * 1f / screenWidth;
                if (screenHeightWidthRatio > HINT_VIDEO_HEIGHT_WIDTH_RATIO) {
                    // 裁宽度
                    int videoWidth = (int) (screenHeight / HINT_VIDEO_HEIGHT_WIDTH_RATIO);
                    int horizontalMargin = -(videoWidth - screenWidth) / 2;
                    ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin = horizontalMargin;
                    ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin = horizontalMargin;
                    layoutParams.width = videoWidth;
                } else if (screenHeightWidthRatio < HINT_VIDEO_HEIGHT_WIDTH_RATIO) {
                    // 裁高度
                    int videoHeight = (int) (screenWidth * HINT_VIDEO_HEIGHT_WIDTH_RATIO);
                    int verticalMargin = -(videoHeight- screenHeight) / 2;
                    ((ViewGroup.MarginLayoutParams) layoutParams).topMargin = verticalMargin;
                    ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin = verticalMargin;
                    layoutParams.height = videoHeight;
                }
                view.setLayoutParams(layoutParams);
            }
        }
    }

    public static Uri getPlayUri(Context context) {
        return Uri.parse("android.resource://" + context.getPackageName() + "/" + HINT_VIDEO_ID);
    }
}
