package com.ximalaya.ting.android.host.manager.ad;

import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnlockUtil;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.UUID;

public class PullAliveTaskManager {

    public static final int CODE_TASK_FAILED_SHORT_OF_TIME = 0;
    public static final int CODE_REWARD_SUCCESS = 1;
    public static final int CODE_TASK_FAILED = 3;
    public static int DP_WATCH_MAX_TIME = 3;

    private static final Handler handler = new Handler(Looper.getMainLooper());

    private static int retryCount = 0;
    private static int watchTime = 0;
    private static Advertis mAdvertis;
    private static OnTaskExecuteCallback mCallback;
    private static int lastAdId;
    private static long lastStayTime; // 上次累计停留的时长，单位ms
    private static CountDownTimer jumpTimer; // 跳转计时器
    private static boolean isJumping; // 点击后发生跳转
    private static boolean isJumpCountdownFinish; //点击后发生跳转 倒计时是否结束
    private static  long mJumpMillis = 0;

    public static void clickOver(boolean isDownload, boolean openOtherApp, Advertis advertis, final OnTaskExecuteCallback callback) {
        if (advertis == null) {
            callback.onError("广告物料为空");
            return;
        }
        retryCount = 0;
        try {
            final String requestId = UUID.randomUUID().toString();
            if (advertis.getBusinessExtraInfo() != null && advertis.getBusinessExtraInfo().isBannerLiveBroadcast()) {
                // 直播广告走新的停留时长校验
                startJumpCountdown(callback, advertis, requestId);
                return;
            }
            if (!openOtherApp && TextUtils.isEmpty(advertis.getDpRealLink()) && TextUtils.isEmpty(advertis.getDpMarketUrl()) && !TextUtils.isEmpty(advertis.getRealLink())) {
                // 非dp吊起，非下载，站内打开
                Logger.i("PullAliveTaskManager", "jumpToH5");
                onRewardFinish(advertis, callback, CODE_REWARD_SUCCESS, "站内跳转成功");
                return;
            }
            if (openOtherApp) {
                resetDpWatchData();
                watchDeepLink(isDownload, callback, advertis, requestId);
            } else {
                onRewardFail(callback, CODE_TASK_FAILED, "dp吊起失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void resetDpWatchData() {
        DP_WATCH_MAX_TIME = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_POINT_CENTER_DP_WATCH_MAX_TIME, 3);
        if (ConstantsOpenSdk.isDebug) {
            try {
                String property = ToolUtil.getSystemProperty("debug.ad.dp_watch", "3");
                DP_WATCH_MAX_TIME = Integer.parseInt(property);
            } catch (Exception e){
                e.printStackTrace();
            }
        }
        watchTime = 0;
        handler.removeCallbacksAndMessages(null);
    }

    private static void startJumpCountdown(final OnTaskExecuteCallback callback, final Advertis advertis, final String requestId) {
        if (advertis == null) {
            return;
        }
        mCallback = callback;
        mAdvertis = advertis;
        int currentAdId = advertis.getAdid();
        long checkStayTime = advertis.getActualStayTime() * 1000L;
        if (lastAdId == currentAdId) {
            checkStayTime = checkStayTime - lastStayTime;
        } else {
            lastStayTime = 0;
        }
        Logger.d("JumpCountdown", "start lastStayTime = " + lastStayTime + " checkStayTime = " + checkStayTime);
        if (checkStayTime <= 0) {
            // 停留时长已经满足，直接回调成功
            onRewardFinish(advertis, callback, CODE_REWARD_SUCCESS, "dp吊起成功");
            return;
        }
        isJumping = true;
        mJumpMillis = System.currentTimeMillis();
        isJumpCountdownFinish = false;
        jumpTimer = new CountDownTimer(checkStayTime, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                Logger.d("JumpCountdown", "finish");
                isJumpCountdownFinish = true;
                onRewardFinish(advertis, callback, CODE_REWARD_SUCCESS, "dp吊起成功");
            }
        };
        jumpTimer.start();
        lastAdId = currentAdId;
    }

    private static void cancelJumpCountdown() {
        if (jumpTimer != null) {
            jumpTimer.cancel();
        }
    }

    public static void onPageResume() {
        if (isJumping && !AdUnlockUtil.isFixLaunchModeActivityShowed()) {
            isJumping = false;
            cancelJumpCountdown();
            Logger.d("JumpCountdown", "onPageResume isJumpCountdownFinish = " + isJumpCountdownFinish);
            if (!isJumpCountdownFinish) {
                lastStayTime = lastStayTime + (System.currentTimeMillis() - mJumpMillis);
                onRewardFail(mCallback, CODE_TASK_FAILED_SHORT_OF_TIME, "dp吊起时长不足");
                mCallback = null;
            } else {
                lastStayTime = 0;
            }
            Logger.d("JumpCountdown", "onPageResume stayTime = " + lastStayTime);
        }
    }

    public static void onPageDestroy() {
        lastAdId = 0;
        lastStayTime = 0;
        mCallback = null;
    }

    private static void watchDeepLink(boolean isDownload, final OnTaskExecuteCallback callback, final Advertis advertis, final String requestId) {
        Logger.i("PullAliveTaskManager", "watchDeepLink");
        if (advertis == null) {
            errorCallBack(callback, "广告物料为空");
            return;
        }
        watchTime ++;
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    final JSONObject result = new JSONObject();
                    if (BaseUtil.isForegroundIsMyApplication(ToolUtil.getCtx())) {
                        // 这里循环检测，为了防止用户dp太慢，3秒还不能拉起的case
                        if (!isDownload && watchTime < DP_WATCH_MAX_TIME){
                            Logger.i("PullAliveTaskManager", "jumpToDp_watchAgain");
                            watchDeepLink(isDownload, callback, advertis, requestId);
                        } else {
                            onRewardFail(callback, CODE_TASK_FAILED_SHORT_OF_TIME, "dp吊起时长不足");
                            Logger.i("PullAliveTaskManager", "jumpToDp_less3s");
                        }
                    } else {
                        Logger.i("PullAliveTaskManager", "jumpToDp_over3s");
                        onRewardFinish(advertis, callback, CODE_REWARD_SUCCESS, "dp吊起成功");
                        watchTime = 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, 3000);
    }

    private static void onRewardFinish(Advertis advertis, OnTaskExecuteCallback callback, int code, String message) {
        if (callback == null) {
            return;
        }
        callback.onSuccess(code, message);
    }

    private static void onRewardFail(OnTaskExecuteCallback callback, int code, String message) {
        if (callback == null) {
            return;
        }
        callback.onSuccess(code, message);
    }

    private static void errorCallBack(OnTaskExecuteCallback callback, String msg) {
        if (callback == null) {
            return;
        }
        callback.onError(msg);
    }

    /**
     * 任务执行回调
     */
    public interface OnTaskExecuteCallback {
        void onSuccess(int clientCode, String msg);
        void onError(String error);
    }

}
