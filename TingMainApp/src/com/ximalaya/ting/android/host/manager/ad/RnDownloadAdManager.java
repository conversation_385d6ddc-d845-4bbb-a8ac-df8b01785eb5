package com.ximalaya.ting.android.host.manager.ad;

import android.content.Context;
import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.adsdk.external.feedad.IAdModel;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.ad.RewardCoinDialogFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.PackageUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.view.ad.RnDownloadAdView;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * rn 下载广告管理器
 */
public class RnDownloadAdManager {
    private static final String TAG = "RnDownloadAdManager";
    private static Advertis currentAd;
    private static boolean isWaitingDownloadFinish;
    private static boolean isExpectingDpJump = false; // 是否期望dp跳转
    private static boolean hasRealJumpedToOtherApp = false; // 是否真正跳转到外部App
    private static int currentRewardCoin;
    private static String currentSourceName;
    private static String currentPositionName;
    private static int currentPositionId;
    private static ILoadDownloadAdCallback currentLoadCallback;
    private static RnDownloadAdManager.IRewardDownloadAdCallback currentRewardCallback;
    private static RnDownloadAdView currentAdView;

    public static void loadRnDownloadAd(RnDownloadAdView adView, int positionId, String positionName, String sourceName, ILoadDownloadAdCallback callback) {
        currentAdView = adView;
        currentPositionId = positionId;
        currentPositionName = positionName;
        currentSourceName = sourceName;
        currentLoadCallback = callback;
        requestTaskAd(false);
    }

    private static void requestTaskAd(boolean isFromChange) {
        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("positionId", currentPositionId);
        params.put("name" , currentPositionName);
        params.put("version", DeviceUtil.getVersion(MainApplication.getMyApplicationContext()));
        params.put("xt", String.valueOf(System.currentTimeMillis()));
        params.put("appid", "0");
        params.put("device", "android");
        params.put("uid", UserInfoMannage.getUid() + "");
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("sourceName", currentSourceName);
        params.put("extInfo", extInfo);

        // 请求广告数据
        AdRequest.getDownloadTask(currentPositionId, currentPositionName, params, new IDataCallBack<List<Advertis>>() {
            @Override
            public void onSuccess(List<Advertis> data) {
                if (data != null && data.size() != 0 && data.get(0) != null && currentAdView != null) {
                    currentAd = data.get(0);
                    resetData();
                    currentAdView.bindData(currentAd, new RnDownloadAdView.IDownloadTaskCallback() {
                        @Override
                        public void onRefresh(RnDownloadAdView view) {
                            requestTaskAd(true);
                        }

                        @Override
                        public void onClick(Context context, Advertis advertis) {
                            clickDownloadAd(context, advertis);
                        }

                        @Override
                        public void onShowSuccess(int height) {
                            if (currentLoadCallback != null) {
                                currentLoadCallback.onLoadSuccess(height);
                            }
                        }
                    });
                } else {
                    if (isFromChange) {
                        CustomToast.showFailToast("没有更多任务啦，请稍后重试");
                    } else {
                        currentAd = null;
                        resetData();
                        if (currentLoadCallback != null) {
                            currentLoadCallback.onLoadFail();
                        }
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                if (isFromChange) {
                    CustomToast.showFailToast("没有更多任务啦，请稍后重试");
                } else {
                    currentAd = null;
                    resetData();
                    if (currentLoadCallback != null) {
                        currentLoadCallback.onLoadFail();
                    }
                }
            }
        });
    }

    private static void resetData() {
        isWaitingDownloadFinish = false;
        isExpectingDpJump = false;
        hasRealJumpedToOtherApp = false;
    }

    public static void setCurrentRewardCallback(IRewardDownloadAdCallback callback) {
        currentRewardCallback = callback;
    }

    private static void clickDownloadAd(Context context, Advertis advertis) {
        if (advertis == null) {
            return;
        }
        if (!PackageUtil.isAppInstalled(context, advertis.getAppPackageName()) && advertis.getTaskStatus() == 0) {
            // 未安装且之前未下载过，才会发放下载奖励
            isWaitingDownloadFinish = true;
        } else {
            isWaitingDownloadFinish = false;
            if (PackageUtil.isAppInstalled(context, advertis.getAppPackageName()) && advertis.getTaskStatus() != 2) {
                // 已安装且之前未打开过，才会发放下载奖励
                isExpectingDpJump = true;
                hasRealJumpedToOtherApp = false;
            } else {
                isExpectingDpJump = false;
                hasRealJumpedToOtherApp = false;
            }
        }
        AdManager.handlerAdClick(context, advertis, new AdManager.IHasOpenOtherApp() {
                    @Override
                    public void clickOver(boolean openOtherApp) {
                    }
                },
                AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId()))
                        .build());
    }


    public static void onPageResume() {
        boolean isAppInstalled = false;
        if (currentAd != null) {
           isAppInstalled = PackageUtil.isAppInstalled(ToolUtil.getCtx(), currentAd.getAppPackageName());
        }
        if (isWaitingDownloadFinish && isAppInstalled) {
            // 安装任务完成
            // todo 已安装完成， 通知rn发放奖励，展示弹窗
            if (currentRewardCallback != null) {
                currentRewardCallback.onInstallSuccess(currentAd);
            }
            showRewardDialog(currentAd.getPriceSecondBanner());
            // todo 调用adx状态更新接口，避免重复安装重复奖励
            isWaitingDownloadFinish = false;
            if (currentAd != null){
                currentAd.setTaskStatus(1);
            }
        }
        if (isExpectingDpJump) {
            if (hasRealJumpedToOtherApp) {
                // 真正跳转外部App后回到页面，发放奖励
                // todo 已打开完成， 通知rn发放奖励，展示toast
                if (currentRewardCallback != null) {
                    currentRewardCallback.onOpenSuccess(currentAd);
                }
                ToastManager.showToast("恭喜获得金币奖励");
                // todo 调用adx状态更新接口，避免重复打开重复奖励
                if (currentAd != null) {
                    currentAd.setTaskStatus(2);
                }
                // todo 更新任务需要改成rn调用
                requestTaskAd(false);
            }
            isExpectingDpJump = false;
            hasRealJumpedToOtherApp = false;
        }
        if (currentAdView != null && currentAd != null) {
            currentAdView.updateButtonState(isAppInstalled);
        }
    }

    public static void onPageStop() {
        if (isExpectingDpJump) {
            hasRealJumpedToOtherApp = true;
        }
    }

    private static void showRewardDialog(int rewardCoins) {
        FragmentActivity activity = null;
        if (MainApplication.getTopActivity() instanceof MainActivity) {
            activity = (FragmentActivity) MainApplication.getMainActivity();
        }
        if (activity == null) {
            return;
        }
        
        RewardCoinDialogFragment dialogFragment = RewardCoinDialogFragment.getInstance(rewardCoins, false,
                new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        // 打开已安装应用
                        clickDownloadAd(ToolUtil.getCtx(), currentAd);
                    }
                }, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                    }
                });
        dialogFragment.show(activity.getSupportFragmentManager(), "RewardCoinDialogFragment");
    }

    public static void onRnDialogClick() {
        clickDownloadAd(ToolUtil.getCtx(), currentAd);
    }

    public interface ILoadDownloadAdCallback {
        void onLoadSuccess(int adHeight);
        void onLoadFail();
    }

    public interface IRewardDownloadAdCallback {
        void onInstallSuccess(Advertis advertis);
        void onOpenSuccess(Advertis advertis);
    }
}
