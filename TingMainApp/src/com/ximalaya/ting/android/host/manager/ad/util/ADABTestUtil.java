package com.ximalaya.ting.android.host.manager.ad.util;

import com.google.gson.Gson;
import com.ximalaya.ting.android.adsdk.util.config.ABConfig;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.manager.ad.model.AdxRequestTimeoutConfig;
import com.ximalaya.ting.android.host.manager.ad.model.DspInitConfig;
import com.ximalaya.ting.android.host.manager.ad.model.SoundPatchRemoveDspConfig;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenLogManager;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @Time 2023/3/3
 * @Description
 */
public class ADABTestUtil {

    public static final String KEY_SOUND_PATCH_AD_TRACE = "soundPatchAdTrace";
    public static final String KEY_SOUND_PATCH_AD_TRACE_OPTIMIZE = "soundpatchAdTraceOptimize";
    public static final String KEY_SOUND_PATCH_CLOSE_TRACE = "soundPatchCloseTrace";
    public static final String KEY_HIGH_LIGHT_AD_CAN_CLOSE = "highLightAdCloseEnable";

    private static Boolean mIsScatterMutexPlayControl;

    public interface IADABAsyncConfigCallback<T> {
        void onDataBack(String itemName, T value);
    }

    public static boolean isScatterMutexPlayControlAbTest() {
        return true;
//        try {
//            if (mIsScatterMutexPlayControl == null) {
//                mIsScatterMutexPlayControl = ABTest.getBoolean(CConstants.Group_ad.ITEM_SCATTER_MUTEX_PLAY_CONTROL, false);
//            }
//            return mIsScatterMutexPlayControl.booleanValue();
//        } catch (Throwable throwable) {
//            throwable.printStackTrace();
//        }
//        return false;
    }


    private static JSONObject adClosePaddingJson = null;

    public static JSONObject getCloseAdPaddingJson() {

        //从广告AB平台获取数据 （实验已结束）
        try {
            if (adClosePaddingJson == null) {
                adClosePaddingJson = ABConfig.getClosePaddingJson();
                if (adClosePaddingJson != null) {
                    return adClosePaddingJson;
                }
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            Logger.e("--------msg_close", " ab test throwable = " + throwable.toString());
        }

        //从主站新AB接口获取数据
        try {
            if (adClosePaddingJson == null) {
                String closeAdPaddingStr = ABTest.getString(CConstants.Group_ad.ITEM_CLOSE_AD_PADDING, null);
                if (!TextUtils.isEmpty(closeAdPaddingStr)) {
                    adClosePaddingJson = new JSONObject(closeAdPaddingStr);
                    return adClosePaddingJson;
                }
            }

        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }



        //从旧版本AB接口获取数据
        try {
            if (adClosePaddingJson == null) {
                if (adClosePaddingJson == null) {
                    adClosePaddingJson = ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME,
                            CConstants.Group_ad.ITEM_CLOSE_AD_PADDING);
                    return adClosePaddingJson;
                }
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            Logger.i("--------msg_close", " football  error = " + adClosePaddingJson);
        }
        return adClosePaddingJson;
    }

    private static int adSceneIntervalAb = -1;
    public static Integer getAdSceneIntervalAbTest() {
        try {
            if (adSceneIntervalAb == -1) {
                adSceneIntervalAb = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_AD_SCENE_INTERVAL_ENABLE, 0);
            }
            return adSceneIntervalAb;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return adSceneIntervalAb;
    }

    private static int adExposureIntervalAb = -1;
    public static Integer getAdExposureIntervalAbTest() {
        try {
            if (adExposureIntervalAb == -1) {
                adExposureIntervalAb = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_AD_EXPOSURE_INTERVAL_TIME, 0);
            }
            Logger.i("msg_vertical_sdk", " -- ab 实验配置的值 ： -> adExposureIntervalAb = " + adExposureIntervalAb);
            return adExposureIntervalAb;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return adExposureIntervalAb;
    }

    private static int adExposureClearedEnable = -1;

    public static Boolean getAdExposureClearedEnable() {
        try {
            if (adExposureClearedEnable == -1) {
                adExposureClearedEnable = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_AD_EXPOSURE_CLEARED_ENABLE, 2);
            }
            return adExposureClearedEnable == 2;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return adExposureClearedEnable == 2;
    }

    private static String adClientFrequencyJson = null;

    public static String getAdClientFrequencyConfig() {
        try {
            if (adClientFrequencyJson == null) {
                ABConfig.getClientFrequencyConfig((itemName, value) -> {
                    adClientFrequencyJson = value;
                    MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveString(CConstants.Group_ad.ITEM_AD_CLIENT_FREQUENCY_CONFIG, value);
                });
            }
            return adClientFrequencyJson;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            Logger.i("--------msg_close", " football  error = " + adClientFrequencyJson);
        }
        return adClientFrequencyJson;
    }


    private static int removeSoundPatchEnable = -1;
    public static Boolean getAdRemoveSoundPatchEnable() {
        try {
            if (removeSoundPatchEnable == -1) {
                removeSoundPatchEnable = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_AD_REMOVE_SOUND_PATCH_ENABLE, 1);
            }
            return removeSoundPatchEnable == 1;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return removeSoundPatchEnable == 1;
    }

    public static Boolean isCenterBigAdNeedMutex;

    public static boolean isCenterBigAdNeedMutex() {
        try {
            if (isCenterBigAdNeedMutex == null) {
                isCenterBigAdNeedMutex = "true".equals(ABConfig.getABConfigString(CConstants.Group_ad.ITEM_CENTER_BIG_AD_MUTEX, "true"));
            }
            Logger.i("CenterBigMutex", "isCenterBigAdNeedMutex = " + isCenterBigAdNeedMutex.booleanValue());
            return isCenterBigAdNeedMutex.booleanValue();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        Logger.i("CenterBigMutex", "isCenterBigAdNeedMutex = true");
        return true;
    }

    private static String mSoundPatchRemoveDspConfigStr;
    private static SoundPatchRemoveDspConfig mSoundPatchRemoveDspConfig;
    public static SoundPatchRemoveDspConfig getSoundPatchRemoveDspConfig() {
        try {
            if (mSoundPatchRemoveDspConfigStr == null) {
                mSoundPatchRemoveDspConfigStr = ABConfig.getSoundPatchRemoveDspConfig();
                mSoundPatchRemoveDspConfig = new Gson().fromJson(mSoundPatchRemoveDspConfigStr, SoundPatchRemoveDspConfig.class);
                Logger.log("removeDspConfig : " + mSoundPatchRemoveDspConfigStr);
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return mSoundPatchRemoveDspConfig;
    }

    private static Boolean mIsMineAdOpen;
    public static boolean isMineAdOpenAbTest() {
        try {
            if (mIsMineAdOpen == null) {
                mIsMineAdOpen = "true".equals(ABConfig.getABConfigString(CConstants.Group_ad.ITEM_AD_MINE_AD_OPEN, "true"));
            }
            return mIsMineAdOpen.booleanValue();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return false;
    }

    private static String mAdxRequestTimeoutConfigStr;
    private static AdxRequestTimeoutConfig mAdxRequestTimeoutConfig;

    public static AdxRequestTimeoutConfig getAdxRequestTimeoutConfig() {
        try {
            if (mAdxRequestTimeoutConfigStr == null) {
                mAdxRequestTimeoutConfigStr = ABConfig.getABConfigString(CConstants.Group_ad.ITEM_SOUND_PATCH_ADXREQUESTTIMEOUTMS,"");
                mAdxRequestTimeoutConfig = new Gson().fromJson(mAdxRequestTimeoutConfigStr, AdxRequestTimeoutConfig.class);
                Logger.log("AdxRequestTimeoutConfigStr : " + mAdxRequestTimeoutConfigStr);
            }
            return mAdxRequestTimeoutConfig;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return mAdxRequestTimeoutConfig;
    }

    private static int soundPatchAdxCacheEnable = -1;
    public static Boolean getSoundPatchAdxCacheEnable() {
        try {
            if (soundPatchAdxCacheEnable == -1) {
                soundPatchAdxCacheEnable = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_AD_SOUND_PATCH_CACHE_ENABLE, 1);
            }
            return soundPatchAdxCacheEnable == 1;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return soundPatchAdxCacheEnable == 1;
    }

    private static Boolean mIsFreeListenV2Open;
    public static boolean isFreeListenV2AbTest() {
        try {
            if (mIsFreeListenV2Open == null) {
                mIsFreeListenV2Open = "true".equals(ABConfig.getABConfigString(CConstants.Group_ad.ITEM_FREE_LISTEN_V2_AB_SWITCH, "true"));
            }
            FreeListenLogManager.writeLog("ADABTestUtil isFreeListenV2AbTest isOpen = "+ mIsFreeListenV2Open.booleanValue());
            return mIsFreeListenV2Open.booleanValue();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return false;
    }
    private static Boolean SoundPatchAdTraceEnable;
    public static boolean getSoundPatchAdTraceEnable() {
        try {
            if (SoundPatchAdTraceEnable == null) {
                SoundPatchAdTraceEnable = "1".equals(ABConfig.getABConfigString(KEY_SOUND_PATCH_AD_TRACE, "0"));
                Logger.log("SoundPatchAdTraceEnable : " + ABConfig.getABConfigString(KEY_SOUND_PATCH_AD_TRACE, "-1"));
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return SoundPatchAdTraceEnable;
    }

    private static String dspConfigStr;
    private static DspInitConfig dspInitConfig;

    public static DspInitConfig getDspSDKInitEnable() {
        try {
            if (dspConfigStr == null) {
                dspConfigStr = ABConfig.getABConfigString(CConstants.Group_ad.ITEM_DSP_SDK_INIT_CONFIG, "");
                if (TextUtils.isEmpty(dspConfigStr)) {
                    dspInitConfig = new DspInitConfig();
                } else {
                    dspInitConfig = new Gson().fromJson(dspConfigStr, DspInitConfig.class);
                }
                Logger.log("dspInitConfig : " + dspConfigStr);
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return dspInitConfig;
    }

    private static Boolean SoundPatchAdTraceOptimizeEnable;
    public static boolean getSoundPatchAdTraceOptimizeEnable() {
        try {
            if (SoundPatchAdTraceOptimizeEnable == null) {
                SoundPatchAdTraceOptimizeEnable = "true".equals(ABConfig.getABConfigString(KEY_SOUND_PATCH_AD_TRACE_OPTIMIZE, "false"));
                Logger.log("SoundPatchAdTraceEnable : " + ABConfig.getABConfigString(KEY_SOUND_PATCH_AD_TRACE_OPTIMIZE, "-1"));
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return SoundPatchAdTraceOptimizeEnable;
    }

    private static Boolean SoundPatchCloseTraceEnable;
    public static boolean getSoundPatchAdCloseTraceEnable() {
        try {
            if (SoundPatchCloseTraceEnable == null) {
                SoundPatchCloseTraceEnable = "true".equals(ABConfig.getABConfigString(KEY_SOUND_PATCH_CLOSE_TRACE, "false"));
                Logger.log("SoundPatchCloseTraceEnable : " + ABConfig.getABConfigString(KEY_SOUND_PATCH_CLOSE_TRACE, "-1"));
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return SoundPatchCloseTraceEnable;
    }

    // 高光touch广告是否支持关闭
    private static Boolean highLightAdsorbAdCanClose;

    public static boolean getHighLightAdCanClose() {
        try {
            if (highLightAdsorbAdCanClose == null) {
                highLightAdsorbAdCanClose = "true".equals(ABConfig.getABConfigString(KEY_HIGH_LIGHT_AD_CAN_CLOSE, "false"));
            }
            return highLightAdsorbAdCanClose;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return false;
    }

    private static Boolean mUseUserInfoAllow;
    public static boolean getUserInfoAllowEnable() {
        try {
            if (mUseUserInfoAllow == null) {
                mUseUserInfoAllow = ABConfig.getUserInfoAllowEnable();
                Logger.log(" oneClickLeaveCash --- === :"+ mUseUserInfoAllow);
            }
            return mUseUserInfoAllow;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return false;
    }

    private static Integer mCenterBigPicAdShowInterval;
    public static int getCenterBigPicAdShowInterval() {
        try {
            if (mCenterBigPicAdShowInterval == null) {
                mCenterBigPicAdShowInterval = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_CENTER_BIG_PIC_AD_SHOW_INTERVAL, -1);
                Logger.log("mCenterBigPicAdShowInterval --- === :"+ mCenterBigPicAdShowInterval);
            }
            return mCenterBigPicAdShowInterval;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return -1;
    }

    public static void getBooleanAsync(String key, boolean defaultValue, IADABAsyncConfigCallback<Boolean> asyncConfigCallback){
        com.ximalaya.ting.android.adsdk.util.config.ConfigureCenter.getInstance().getBoolAsync(key, defaultValue, (s, aBoolean) -> {
            if (asyncConfigCallback != null) {
                asyncConfigCallback.onDataBack(s, aBoolean);
            }
        });
    }

    private static Integer mRewardAdBtnBgAb;
    public static int getRewardAdBtnBgAb() {
        try {
            if (mRewardAdBtnBgAb == null) {
                mRewardAdBtnBgAb = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_REWARD_AD_BTN_BG_AB, -1);
                Logger.log(" mRewardAdBtnBgAb --- === :"+ mRewardAdBtnBgAb);
            }
            return mRewardAdBtnBgAb;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return -1;
    }

    private static String mInterceptFastAppEnable;
    public static boolean isInterceptFastAppEnable() {
        try {
            if (mInterceptFastAppEnable == null) {
                mInterceptFastAppEnable = ABConfig.getABConfigString("interceptFastAppEnable", "1");
                Logger.log(" mInterceptFastAppEnable --- === :"+ mInterceptFastAppEnable);
            }
            return "1".equals(mInterceptFastAppEnable);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return true;
    }

    private static String adAIgcFrequencyConfig = null;

    public static String getAIgcFrequencyConfig() {
        try {
            if (adAIgcFrequencyConfig == null) {
                adAIgcFrequencyConfig = ABConfig.getAIgcFrequencyConfig();
            }
            Logger.i("--------getAIgcFrequencyConfig", " football  = " + adAIgcFrequencyConfig);
            return adAIgcFrequencyConfig;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return adAIgcFrequencyConfig;
    }

    private static String touchCloseFrequencyConfig = null;

    public static String getTouchCloseFrequencyConfig() {
        try {
            if (touchCloseFrequencyConfig == null) {
                touchCloseFrequencyConfig = ABConfig.getABConfigString("touchCloseFrequencyConfig", "");
            }
            Logger.i("--------getTouchCloseFrequencyConfig", " football  = " + touchCloseFrequencyConfig);
            return touchCloseFrequencyConfig;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return touchCloseFrequencyConfig;
    }

    private static Integer playCoverAdTimeout1 = null;

    public static int getPlayCoverAdTimeout1() {
        try {
            if (playCoverAdTimeout1 == null) {
                playCoverAdTimeout1 = ABConfig.getABConfigInt("ad_api_timeout_in_play_one_second", 0);
            }
            Logger.logToFile("--------playCoverAdTimeout1", " adAbTest  = " + playCoverAdTimeout1);
            return playCoverAdTimeout1;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return playCoverAdTimeout1;
    }

    private static Integer playCoverAdTimeout2 = null;

    public static int getPlayCoverAdTimeout2() {
        try {
            if (playCoverAdTimeout2 == null) {
                playCoverAdTimeout2 = ABConfig.getABConfigInt("ad_api_timeout_in_play_two_second", 0);
            }
            Logger.logToFile("--------playCoverAdTimeout2", " adAbTest  = " + playCoverAdTimeout2);
            return playCoverAdTimeout2;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return playCoverAdTimeout2;
    }

    private static String startPlayWhileAdRequest = null;

    public static boolean getStartPlayWhileAdRequest() {
        try {
            if (startPlayWhileAdRequest == null) {
                startPlayWhileAdRequest = ABConfig.getABConfigString("start_play_while_ad_request_optmize", "0");
            }
            Logger.i("--------startPlayWhileAdRequest", " adAbTest  = " + startPlayWhileAdRequest);
            return "1".equals(startPlayWhileAdRequest);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return false;
    }

    private static int soundPatchAdCloseEnlargeEnable = -1;
    public static Boolean getSoundPatchAdCloseEnlargeEnable() {
        try {
            if (soundPatchAdCloseEnlargeEnable == -1) {
                soundPatchAdCloseEnlargeEnable = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_AD_SOUND_PATCH_CLOSE_ENLARGE_ENABLE, 0);
            }
            return soundPatchAdCloseEnlargeEnable == 1;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return soundPatchAdCloseEnlargeEnable == 1;
    }

    private static int soundPatchInterruptReportEnable = -1;
    public static Boolean getSoundPatchInterruptReportEnable() {
        try {
            if (soundPatchInterruptReportEnable == -1) {
                soundPatchInterruptReportEnable = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_AD_SOUND_PATCH_INTERRUPT_REPORT_ENABLE, 0);
            }
            return soundPatchInterruptReportEnable == 1;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return soundPatchInterruptReportEnable == 1;
    }

    private static int needUpdateCurrentAdComponentEnable = -1;
    public static Boolean getNeedUpdateCurrentAdComponentEnable() {
        try {
            if (needUpdateCurrentAdComponentEnable == -1) {
                needUpdateCurrentAdComponentEnable = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_AD_SOUND_PATCH_UPDATE_CUR_COMPONENT_ENABLE, 0);
            }
            return needUpdateCurrentAdComponentEnable == 1;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return needUpdateCurrentAdComponentEnable == 1;
    }

    private static String webViewPreloadConfig = null;

    public static String getWebViewPreloadConfig() {
        try {
            if (webViewPreloadConfig == null) {
                webViewPreloadConfig = ABConfig.getABConfigString(CConstants.Group_ad.ITEM_AD_SOUND_WEB_VIEW_PRE_LOAD_CONFIG, "");
            }
            Logger.i("--------webViewPreloadConfig", " ab   = " + webViewPreloadConfig);
            return webViewPreloadConfig;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return webViewPreloadConfig;
    }

    private static int centerBigPicAdReloadInterval = -1;
    public static Integer getCenterBigPicAdReloadInterval() {
        try {
            if (centerBigPicAdReloadInterval == -1) {
                centerBigPicAdReloadInterval = ABConfig.getABConfigInt(CConstants.Group_ad.ITEM_CENTER_BIG_PIC_AD_RELOAD_INTERVAL, -1);
            }
            return centerBigPicAdReloadInterval;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return centerBigPicAdReloadInterval;
    }

    private static String centerBigPicAdReloadIntervalOptimize = null;
    public static String getCenterBigPicAdReloadIntervalOptimize() {
        try {
            if (centerBigPicAdReloadIntervalOptimize == null) {
                centerBigPicAdReloadIntervalOptimize = ABConfig.getABConfigString(CConstants.Group_ad.ITEM_CENTER_BIG_PIC_AD_RELOAD_INTERVAL_OPTIMIZE, "");
            }
            return centerBigPicAdReloadIntervalOptimize;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return centerBigPicAdReloadIntervalOptimize;
    }

    private static String adFeedbackConfig = null;
    public static boolean adFeedbackOptimizeEnable() {
        try {
            if (adFeedbackConfig == null) {
                adFeedbackConfig = ABConfig.getABConfigString(CConstants.Group_ad.ITEM_AD_FEEDBACK, "0");
            }
            return "1".equals(adFeedbackConfig);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return false;
    }

}
