package com.ximalaya.ting.android.host.manager.ai.radio

import com.ximalaya.aiagentconnect.sdk.logger.XYLogger
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.player.service.IXmSimplePlayerStatusListener
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException
import kotlin.random.Random

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2025/4/28
 */
object AiRadioOfflinePlayUtil : IAiRadioPlay {

    var radioPlayFinish: Boolean? = null
    private var isPlay = false
    private val playerListeners: MutableSet<IRadioPlayerListener> = mutableSetOf()

    // 电台播放列表
    private val radioParts: MutableList<RadioPart> = mutableListOf()

    private val xmPlayer: XmPlayerManager by lazy {
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
    }

    private val listener = object : IXmSimplePlayerStatusListener() {
        override fun onPlayStart() {
            super.onPlayStart()
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            radioPlayFinish = false
            playerListeners.forEach {
                it.onPlayStart()
            }
        }

        override fun onPlayPause() {
            super.onPlayPause()
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            playerListeners.forEach {
                it.onPlayPause()
            }
        }

        override fun onPlayStop() {
            super.onPlayStop()
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            playerListeners.forEach {
                it.onPlayStop()
            }
        }

        override fun onPlayProgress(currPos: Int, duration: Int) {
            super.onPlayProgress(currPos, duration)
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            playerListeners.forEach {
                it.onPlayProgress(currPos, duration)
            }
        }

//        override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
//            super.onSoundSwitch(lastModel, curModel)
//            if (!AiRadioPlayHelper.isRadioSound(lastModel)) {
//                return
//            }
//            playerListeners.forEach {
//                it.onPlayStop()
//            }
//        }

        override fun onSoundPlayComplete() {
            super.onSoundPlayComplete()
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            radioPlayFinish = radioParts.isEmpty()
            playerListeners.forEach {
                if (radioPlayFinish == true) {
                    it.onPlayFinish()
                } else {
                    it.onPlayEnd()
                }
            }
            if (radioParts.isNotEmpty()) {
                playAiRadioInner(radioParts.removeAt(0))
            }
            if (radioPlayFinish == true) {
                dlog("onRadioPlayFinish offline >>> ${AiRadioPlayHelper.getCurPart()?.radioId}")
                AiRadioPlayHelper.checkDisconnectAgent()
            }
        }

        override fun onError(exception: XmPlayerException?): Boolean {
            if (!AiRadioPlayHelper.isRadioSound()) {
                return super.onError(exception)
            }
            playerListeners.forEach {
                it.onPlayError(exception?.message ?: "")
            }
            return super.onError(exception)
        }
    }

    override fun addPlayListener(listener: IRadioPlayerListener) {
        playerListeners.add(listener)
    }

    override fun removePlayListener(listener: IRadioPlayerListener) {
        playerListeners.remove(listener)
    }

    private fun playAiRadioInner(radioPart: RadioPart) {
        AiRadioPlayHelper.playAiRadio(radioPart, true)
    }

    override fun isPlay(): Boolean {
        return isPlay
    }

    override fun playAiRadio(radioPart: RadioPart) {
        isPlay = true
        xmPlayer.addPlayerStatusListener(listener)
        val url = if (radioPart.trackId > 0) {
            ""
        } else {
            radioPart.url
        }
        val track = TrackM().apply {
            kind = PlayableModel.KIND_AI_AGENT_RADIO
            this.dataId = if (radioPart.trackId > 0) radioPart.trackId else -1
            aiRadioId = radioPart.radioId
            aiRadioEpisodeId = radioPart.episodeId
            trackTitle = radioPart.title
            playUrl64 = url
            coverUrlLarge = radioPart.coverUrl
            coverUrlMiddle = radioPart.coverUrl
            coverUrlSmall = radioPart.coverUrl
        }
        dlog("playRadio offline >>> isTrack = ${radioPart.trackId}  , curPart = $radioPart  , playUrl = $url ,")
        PlayTools.playTrack(BaseApplication.getMyApplicationContext(), track, false, null)
    }

    override fun play(): Boolean {
        xmPlayer.play()
        return true
    }

    fun playAiRadio(list: List<RadioPart>) {
        radioParts.clear()
        radioParts.addAll(list)
        val part = radioParts.removeFirstOrNull() ?: return
        playAiRadioInner(part)
    }

    override fun clear() {
        dlog("clear radio offline >>> ")
        isPlay = false
        xmPlayer.removePlayerStatusListener(listener)
        radioParts.clear()
        playerListeners.clear()
    }

    override fun release() {
        if (xmPlayer.isPlaying && xmPlayer.currSound?.isKindOfAiAgentRadio == true) {
            xmPlayer.stop(PauseReason.Business.AI_RADIO)
        }
        clear()
    }

    override fun addPlayPart(parts: List<RadioPart>) {
        radioParts.addAll(parts)
    }

    private fun dlog(msg: String) {
        XYLogger.log(AiRadioOfflinePlayUtil::class.java.simpleName, msg)
    }
}