package com.ximalaya.ting.android.host.manager.ai.radio

import android.content.Context
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Handler
import android.os.Looper
import com.google.gson.GsonBuilder
import com.ximalaya.aiagentconnect.sdk.connection.XiaoYaConnectionSDK
import com.ximalaya.aiagentconnect.sdk.connection.protocol.Directive
import com.ximalaya.aiagentconnect.sdk.connection.protocol.directive.speech.SpeechttsFinishDirective
import com.ximalaya.aiagentconnect.sdk.logger.XYLogger
import com.ximalaya.ting.android.aiagent.bean.directive.InvokeAppPayload
import com.ximalaya.ting.android.aiagent.bean.directive.SpeechTtsPayload
import com.ximalaya.ting.android.aiagent.bean.vui.InvokeUrlPlayAction
import com.ximalaya.ting.android.aiagent.message.IMessageDirectiveListener
import com.ximalaya.ting.android.aiagent.message.MessageDispatchHelper
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.player.service.IXmSimplePlayerStatusListener
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import java.io.File
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.random.Random
import androidx.core.net.toUri

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2025/4/8
 */
object AiRadioOnlinePlayUtil : IAiRadioPlay {

    private val xmPlayer: XmPlayerManager by lazy {
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
    }
    private const val KEY_AI_RADIO_PART_WRITE_FINISH_ID = "key_ai_radio_part_write_finish_id"

    private var cachePath = ""
    private var curMessageId: String? = null
    private var partList = mutableListOf<RadioPart>()
    private val mHandler = Handler(Looper.getMainLooper())
    private var bufferManager: AiRadioPlayerBufferManager? = null

    private var curPlayPart: RadioPart? = null
    private var service: ExecutorService = Executors.newSingleThreadExecutor()
    private var isRadioData = false
    private var autoPlay = false
    private val gson = GsonBuilder().create()
    private var isPlay = false

    var radioSaveFinish: Boolean = true
    private var radioPlayFinish: Boolean? = null

    private val playerListeners: MutableSet<IRadioPlayerListener> = mutableSetOf()

    private val directiveListener = object : IMessageDirectiveListener() {
        override fun onBinaryMessage(data: ByteArray) {
            if (isRadioData) {
                onReceiveAiData(data)
            }
        }

        override fun onInvokeApp(directive: Directive<InvokeAppPayload>) {
            super.onInvokeApp(directive)
            val uri = (directive.payload?.uri)?.toUri() ?: return
            val subType = uri.getQueryParameter("sub_type")?.toIntOrNull() ?: return
            if (subType != 37) {
                return
            }
            val value = Uri.decode(uri.getQueryParameter("value"))
            dlog("onInvokeAppRadio >>> $value")
            when (subType) {
                37 -> {
                    val action = gson.fromJson(value, InvokeUrlPlayAction::class.java)
                    if (action.trackId > 0) {
                        addPart(
                            RadioPart(
                                partId = action.partId,
                                trackId = action.trackId,
                                isOnline = true
                            )
                        )
                    }
                }
            }
        }

        override fun onMsgTTsStart(directive: Directive<SpeechTtsPayload>) {
            super.onMsgTTsStart(directive)
            isRadioData = directive.payload?.moduleType == "list"
            if (isRadioData) {
                onLoadAiStart(directive)
            }
        }

        override fun onMsgTTsEnd(directive: Directive<SpeechTtsPayload>) {
            super.onMsgTTsEnd(directive)
            if (isRadioData) {
                onLoadAiEnd(directive)
            }
            isRadioData = false
        }

        override fun onMsgTTsFinish(directive: SpeechttsFinishDirective) {
            super.onMsgTTsFinish(directive)
            if (directive.payload?.contentType == "aiRadio" || directive.payload?.moduleType == "list") {
                onLoadAiFinish(directive)
            }
        }
    }

    val listener = object : IXmSimplePlayerStatusListener() {
        override fun onPlayStart() {
            super.onPlayStart()
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            autoPlay = true
            playerListeners.forEach {
                it.onPlayStart()
            }
        }

        override fun onPlayPause() {
            super.onPlayPause()
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            autoPlay = false
            playerListeners.forEach {
                it.onPlayPause()
            }
        }

        override fun onPlayStop() {
            super.onPlayStop()
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            autoPlay = false
            playerListeners.forEach {
                it.onPlayStop()
            }
        }

        override fun onPlayProgress(currPos: Int, duration: Int) {
            super.onPlayProgress(currPos, duration)
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            playerListeners.forEach {
                it.onPlayProgress(currPos, duration)
            }
        }

//        override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
//            super.onSoundSwitch(lastModel, curModel)
//            if (!AiRadioPlayHelper.isRadioSound(lastModel)) {
//                return
//            }
//            playerListeners.forEach {
//                it.onPlayStop()
//            }
//        }

        override fun onSoundPlayComplete() {
            super.onSoundPlayComplete()
            if (!AiRadioPlayHelper.isRadioSound()) {
                return
            }
            dlog("onSoundPlayComplete online >>>")
            val finished = partList.isEmpty() && radioSaveFinish
            playerListeners.forEach {
                if (finished) {
                    it.onPlayFinish()
                } else {
                    it.onPlayEnd()
                }
            }
            if (finished) {
                dlog("onRadioPlayFinish online >>> $curMessageId")
                radioPlayFinish = true
                AiRadioPlayHelper.checkDisconnectAgent()
                return
            }
            autoPlay = true
            checkAndPlayNextPart()
        }

        override fun onError(exception: XmPlayerException?): Boolean {
            if (!AiRadioPlayHelper.isRadioSound()) {
                return super.onError(exception)
            }
            autoPlay = false
            playerListeners.forEach {
                it.onPlayError(exception?.message ?: "")
            }
            return super.onError(exception)
        }
    }

    init {
        val context = BaseApplication.getMyApplicationContext()
        cachePath = File(
            context.externalCacheDir?.absolutePath ?: context.cacheDir.absolutePath,
            "aiRadio"
        ).absolutePath
        if (bufferManager == null) {
            bufferManager = AiRadioPlayerBufferManager(cachePath)
        }
    }

    private fun playAiRadioInner(radioPart: RadioPart) {
        AiRadioPlayHelper.playAiRadio(radioPart, false)
    }

    override fun isPlay(): Boolean {
        return isPlay
    }

    override fun playAiRadio(radioPart: RadioPart) {
        isPlay = true
        xmPlayer.addPlayerStatusListener(listener)
        if (!File(radioPart.getFilePath(cachePath)).exists() && radioPart.trackId <= 0) {
            dlog("本地文件不存在 >>> radioId=${radioPart.radioId} , messageId=${radioPart.messageId} , partId=${radioPart.partId}")
            return
        }
        val url = if (radioPart.trackId > 0) {
            ""
        } else {
            "pcm://url=${radioPart.getFilePath(cachePath)}&partId=${radioPart.partId}&messageId=${radioPart.messageId}"
        }
        val track = TrackM().apply {
            kind = PlayableModel.KIND_AI_AGENT_RADIO
            this.dataId = if (radioPart.trackId > 0) radioPart.trackId else -1
            aiRadioId = radioPart.radioId
            aiRadioEpisodeId = radioPart.episodeId
            trackTitle = radioPart.title
            playUrl64 = url
            coverUrlLarge = radioPart.coverUrl
            coverUrlMiddle = radioPart.coverUrl
            coverUrlSmall = radioPart.coverUrl
        }
        dlog("playRadio online >>> isTrack = ${radioPart.trackId}  , curPart = $radioPart  , playUrl = $url")
        PlayTools.playTrack(BaseApplication.getMyApplicationContext(), track, false, null)
    }

    override fun play(): Boolean {
        if (curPlayPart == null || File(curPlayPart!!.getFilePath(cachePath)).exists().not()) {
            return false
        }
        xmPlayer.play()
        return true
    }

    fun startListen() {
        MessageDispatchHelper.addListener(directiveListener)
    }

    fun removeListener() {
        MessageDispatchHelper.removeListener(directiveListener)
    }

    private fun checkAndPlayNextPart() {
        dlog("checkAndPlayNextPart  , list size=${partList.size}")
        if (partList.isEmpty()) {
            return
        }
        if (xmPlayer.isPlaying && xmPlayer.currSound?.isKindOfAiAgentRadio == true) {
            dlog("radio is playing, ignore >>>")
            return
        }
        if (!autoPlay) {
            dlog("radio can not auto play , ignore >>>")
            return
        }
        autoPlay = false
        curPlayPart = partList.removeFirstOrNull()
        curPlayPart?.let {
            playAiRadioInner(it)
        }
    }

//    fun addPart(part: RadioPart) {
//        partList.add(part)
//    }

    private fun onLoadAiStart(directive: Directive<SpeechTtsPayload>) {
        dlog("onLoadAiStart >>>")
        if (directive.messageId != XiaoYaConnectionSDK.getCurMessageId()) {
            dlog("ttsStart >>> ignore , ${directive.messageId}")
            return
        }
        getService().execute {
            synchronized(this) {
                val radioPart = RadioPart(
                    messageId = directive.messageId ?: "",
                    partId = directive.payload?.contentId ?: "",
                    business = directive.payload?.business ?: "",
                    isOnline = true
                )
                if (curMessageId != radioPart.messageId) {
                    curMessageId = radioPart.messageId
                    bufferManager?.clear()
                    partList.clear()
                    radioSaveFinish = false
                    autoPlay = true
                    radioPlayFinish = false
                    MMKVUtil.getInstance().removeByKey(KEY_AI_RADIO_PART_WRITE_FINISH_ID)
                }
                bufferManager?.onStart(radioPart)
                addPart(radioPart)
            }
        }
    }

    private fun addPart(radioPart: RadioPart) {
        dlog("try add part >>> partId=${radioPart.partId} trackId=${radioPart.trackId}")
        val hasAdd =
            partList.firstOrNull { it.partId == radioPart.partId && it.trackId == radioPart.trackId } != null
                    || (curPlayPart?.partId == radioPart.partId && curPlayPart?.trackId == radioPart.trackId)
        if (hasAdd) {
            return
        }
        dlog("add part >>> ${radioPart.partId} $radioPart")
        partList.add(radioPart)
        checkAndPlayNextPart()
    }

    private fun onReceiveAiData(data: ByteArray) {
        getService().execute {
            bufferManager?.writeData(data, 0, data.size)
        }
    }

    private fun onLoadAiEnd(directive: Directive<SpeechTtsPayload>) {
        dlog("onLoadEnd >>> ")
        getService().execute {
            bufferManager?.onEnd(directive.payload?.contentId ?: "")
        }
    }


    /**
     * 一段part结束,使用ttsFinish的streaming字段来判断，暂时不用
     * */
    private fun onLoadAiFinish(directive: SpeechttsFinishDirective) {
        dlog("onLoadFinish >>> ")
        if (directive.messageId != XiaoYaConnectionSDK.getCurMessageId()) {
            dlog("ttsFinish >>> ignore , ${directive.messageId}")
            return
        }
        getService().execute {
            bufferManager?.stop()
            if (directive.payload?.streaming == false) {
                radioSaveFinish = true
            }
            val messageId = directive.messageId ?: ""
            val partId = directive.payload?.contentId ?: ""
            val streaming = directive.payload?.streaming ?: false
            if (directive.messageId.isNullOrEmpty().not()) {
                val finishPart =
                    (partList + curPlayPart).firstOrNull { it?.messageId == messageId && it.partId == partId }
                finishPart?.apply {
                    duration = getMp3Duration(
                        BaseApplication.getMyApplicationContext(),
                        Uri.parse(getFilePath(cachePath))
                    ).toInt()
                }
                dlog("save write finish id >>> streaming=${streaming} , partId=$partId , duration=${finishPart?.duration} , messageId=$messageId")
                MMKVUtil.getInstance()
                    .saveString(
                        KEY_AI_RADIO_PART_WRITE_FINISH_ID,
                        "${messageId}&${if (streaming) partId else -1}"
                    )
            }
            if (!streaming) {
                radioSaveFinish = true
            }
        }
    }

    override fun clear() {
        dlog("clear radio online >>> ")
        isPlay = false
        xmPlayer.removePlayerStatusListener(listener)
        MMKVUtil.getInstance().removeByKey(KEY_AI_RADIO_PART_WRITE_FINISH_ID)
        playerListeners.clear()
        radioSaveFinish = true
        radioPlayFinish = null
        curPlayPart = null
        autoPlay = false
//        curMessageId = null
        partList.clear()
        service.shutdownNow()
        bufferManager?.clear()
    }

    override fun release() {
        dlog("release >>>")
        if (xmPlayer.isPlaying && xmPlayer.currSound?.isKindOfAiAgentRadio == true) {
            xmPlayer.stop(PauseReason.Business.AI_RADIO)
        }
        clear()
        bufferManager = null
    }

    override fun addPlayPart(parts: List<RadioPart>) {
        partList.addAll(parts)
    }

    override fun addPlayListener(listener: IRadioPlayerListener) {
        playerListeners.add(listener)
    }

    override fun removePlayListener(listener: IRadioPlayerListener) {
        playerListeners.remove(listener)
    }

    private fun getService(): ExecutorService {
        if (service.isShutdown) {
            service = Executors.newSingleThreadExecutor()
        }
        return service
    }

    private fun getMp3Duration(context: Context, fileUri: Uri): Long {
        val retriever = MediaMetadataRetriever()
        return try {
            // 设置数据源
            if (fileUri.scheme?.startsWith("http") == true) {
                retriever.setDataSource(fileUri.toString(), HashMap())
            } else {
                retriever.setDataSource(context, fileUri)
            }
            // 获取时长（毫秒）
            val durationStr =
                retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            dlog("duration get success >>> $durationStr")
            durationStr?.toLongOrNull() ?: -1
        } catch (e: Exception) {
            e.printStackTrace()
            dlog("duration get error >>> ${e.message}")
            -1 // 返回-1表示出错
        } finally {
            retriever.release() // 释放资源
        }
    }

    private fun dlog(msg: String) {
        XYLogger.log(AiRadioOnlinePlayUtil::class.java.simpleName, msg)
    }
}