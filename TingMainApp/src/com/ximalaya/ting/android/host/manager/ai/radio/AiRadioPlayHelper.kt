package com.ximalaya.ting.android.host.manager.ai.radio

import com.ximalaya.aiagentconnect.sdk.connection.XiaoYaConnectionSDK
import com.ximalaya.aiagentconnect.sdk.logger.XYLogger
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.async.AsyncRequest
import com.ximalaya.ting.android.host.chatxmly2.manager.ChatXmlyDataManager
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.manager.ai.AiForceLoginManager
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.routeservice.RouterServiceManager
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2025/4/15
 */
object AiRadioPlayHelper {

    private var hasInited = false

    private val xmPlayer: XmPlayerManager by lazy {
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
    }

    // 基础数据
    private var rnPart: RadioPart? = null

    // 在播电台
    private var curPart: RadioPart? = null

    val listeners = mutableSetOf<IRadioPlayerListener>()

    private var playStartTime = 0L

    var agentPageState = ""

    // 埋点数据
    var rnAgent = ""
    var rnScene = ""
    var rnintent = ""

    private val skipUrlTrack: Boolean by lazy {
        ABTest.getString("agent_radio_skip_guid_tts", "0") == "1"
    }

    fun addPlayListener(rnListener: IRadioPlayerListener) {
        listeners.add(rnListener)
    }

    fun removePlayListener(rnListener: IRadioPlayerListener) {
        listeners.remove(rnListener)
    }

    private val playerListener = object : IRadioPlayerListener {
        override fun onPlayStart() {
            super.onPlayStart()
            playStartTime = System.currentTimeMillis()
            RadioPlayHistoryUtil.savePlayHistory(curPart, curPart?.seekTime ?: 0)
            if (curPart?.isOnline != true || (curPart?.trackId ?: 0) > 0) {
                dlog("set seekTime >>> ${curPart?.seekTime}")
                // 先重置为开头播放
                xmPlayer.seekTo(curPart?.seekTime ?: 0)
            }
            listeners.forEach {
                it.onPlayStart()
            }
        }

        override fun onPlayFinish() {
            super.onPlayFinish()
            RadioPlayHistoryUtil.savePlayHistory(curPart, 0)
            getRadioEpisodeInfoList(curPart, {
                listeners.forEach {
                    it.onPlayFinish()
                }
            })
        }

        override fun onPlayEnd() {
            super.onPlayEnd()
            tracePlayDuration()
            RadioPlayHistoryUtil.savePlayHistory(curPart, 0)
            listeners.forEach {
                it.onPlayEnd()
            }
        }

        override fun onPlayStop() {
            super.onPlayStop()
            tracePlayDuration()
//                curPart = null
            listeners.forEach {
                it.onPlayStop()
            }
        }

        override fun onPlayPause() {
            super.onPlayPause()
            tracePlayDuration()
            listeners.forEach {
                it.onPlayPause()
            }
        }

        override fun onPlayProgress(currPos: Int, duration: Int) {
            super.onPlayProgress(currPos, duration)
            if (currPos == 0) {
                // 忽略无意义的进度回调，在onPlayStart回调之前这里可能回调currPos==0的场景
                return
            }
//            dlog("onPlayProgress >>> currPos = $currPos , duration = $duration")
            curPart?.seekTime = currPos
            RadioPlayHistoryUtil.savePlayHistory(curPart, currPos)
            listeners.forEach {
                it.onPlayProgress(currPos, duration)
            }
        }

        override fun onPlayError(msg: String) {
            super.onPlayError(msg)
            dlog(msg)
            tracePlayDuration()
            listeners.forEach {
                it.onPlayError(msg)
            }
        }
    }

    fun initAiRadioPlayer() {
        if (hasInited) {
            return
        }
        hasInited = true
        curPart = RadioPlayHistoryUtil.getCurPart()
    }

    /**
     * 组合rn给的数据，清除在线/离线数据后播放
     * */
    fun playAiRadio(radioPart: RadioPart, offline: Boolean) {
        if (offline) {
            AiRadioOnlinePlayUtil.clear()
            curPart = radioPart.apply {
                coverUrl = coverUrl.ifEmpty { ChatXmlyDataManager.COVER_URL }
            }
            AiRadioOfflinePlayUtil.addPlayListener(playerListener)
            AiRadioOfflinePlayUtil.playAiRadio(curPart!!)
        } else {
            AiRadioOfflinePlayUtil.clear()
            curPart = radioPart.apply {
                radioId = rnPart?.radioId ?: ""
                episodeId = rnPart?.episodeId ?: ""
                title = rnPart?.title ?: ""
                coverUrl = rnPart?.coverUrl ?: ChatXmlyDataManager.COVER_URL
            }
            AiRadioOnlinePlayUtil.addPlayListener(playerListener)
            AiRadioOnlinePlayUtil.playAiRadio(curPart!!)
        }
    }

    /**
     * 播放rn离线列表数据
     * */
    fun playOfflineAiRadioList(list: MutableList<RadioPart>) {
        if (skipUrlTrack) {
            list.removeAll {
                it.trackId == 0L
            }
        }
        AiRadioOfflinePlayUtil.playAiRadio(list)
    }

    fun getAiRadioInfo(radioId: String, episodeId: String): RadioPart? {
        var fromHis = true
        val part =
            if (xmPlayer.currSound?.isKindOfAiAgentRadio == true && (radioId.isEmpty() || episodeId.isEmpty())) {
                fromHis = false
                curPart
            } else {
                RadioPlayHistoryUtil.getPlayHistory(radioId, episodeId)
            }
        val isPlaying =
            xmPlayer.currSound?.isKindOfAiAgentRadio == true && xmPlayer.isPlaying() && (xmPlayer.currSound as? Track)?.aiRadioId == part?.radioId
                    && (xmPlayer.currSound as? Track)?.aiRadioEpisodeId == part?.episodeId
        part?.isPlaying = isPlaying
        dlog("find part >>> fromHis=$fromHis , $part")
        return part
    }

    fun updateAiRadioInfo(radioPart: RadioPart) {
        rnPart = radioPart
    }

    fun isRadioSound(sound:PlayableModel? = null): Boolean {
        val currSound =
            sound
                ?: XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).currSound
                ?: return false
        return currSound.isKindOfAiAgentRadio
    }

    fun release() {
//        radioSaveFinish = true
//        radioPlayFinish = null
        curPart = null
        rnPart = null
        listeners.clear()
//        radioParts.clear()
        agentPageState = ""
        AiRadioOnlinePlayUtil.release()
        AiRadioOfflinePlayUtil.release()
        hasInited = false
    }

    fun checkDisconnectAgent() {
        // 页面未销毁
        if (agentPageState.isNotEmpty()) {
            return
        }
        dlog("checkDisconnectAgent 111 >>> $agentPageState")
        // 电台存储未完成
        if (!AiRadioOnlinePlayUtil.radioSaveFinish) {
            return
        }
        dlog("disconnect >>> 222")
        XiaoYaConnectionSDK.disconnect()
    }

    fun getCurPart(): RadioPart? {
        return curPart
    }

    /**
     * 检查清除aiRadio
     * */
    fun checkClearAiRadio() {
        val context = BaseApplication.getMyApplicationContext()
        val playManager = XmPlayerManager.getInstance(context)
        if (!playManager.isConnected) {
            xLog("clear aiRadio cache 222 >>>")
            return
        }
        if (playManager?.currSound?.isKindOfAiAgentRadio != true) {
            xLog("clear aiRadio cache 333 >>>")
            return
        }
        if (AiForceLoginManager.isAiRadioEnable()) {
            xLog("clear aiRadio cache 444 >>>")
            return
        }
        xLog("clear aiRadio >>>")
        playManager.stop()
        playManager.resetPlayList()
        val historyManager = RouterServiceManager.getInstance().getService(
            IHistoryManagerForMain::class.java
        )
        historyManager?.clearPlayList()
        MmkvCommonUtil.getInstance(context)
            .saveString(PreferenceConstantsInHost.KEY_PLAY_BAR_COVER, "")
        BaseApplication.getMainActivity()?.let {
            if (it is MainActivity) {
                it.refreshPlayBar()
                it.resetPlayBarCover()
            }
        }
    }

    fun isPlaying(): Boolean {
        return xmPlayer.isPlaying()
    }

    fun play(): Boolean {
        if (curPart == null) {
            return false
        }
        val curTrack = PlayTools.getCurTrack(BaseApplication.getMyApplicationContext())
        if (curTrack?.isKindOfAiAgentRadio != true) {
            return false
        }
        return if (curPart?.isOnline == true) {
            AiRadioOnlinePlayUtil.play()
        } else {
            AiRadioOfflinePlayUtil.play()
        }
    }

    fun pause() {
        xmPlayer.pause(PauseReason.Business.AI_RADIO)
    }

    fun stop() {
        xmPlayer.stop()
        clear()
    }

    fun clear() {
        curPart = null
        AiRadioOnlinePlayUtil.clear()
        AiRadioOfflinePlayUtil.clear()
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun getRadioEpisodeInfoList(part: RadioPart?, finishCallback: () -> Unit) {
        part?:return
        GlobalScope.launch {
            runCatching {
                AsyncRequest.getRaw<String>(
                    UrlConstants.getInstanse().getRadioEpisodeVoiceList(part.episodeId)
                )
            }.onSuccess {
                dlog("getRadioEpisodeVoiceList $it")
                val jsonObject = JSONObject(it)
                val listStr = jsonObject.optJSONObject("data")?.optString("voiceList") ?: ""
                if (listStr.isEmpty()) {
                    finishCallback.invoke()
                    dlog("getRadioEpisodeVoiceList listStr is empty")
                    return@launch
                }
                val infoData = GsonUtils.parseJson(listStr, EpisodeInfoData::class.java)
                val parts = mutableListOf<RadioPart>()
                infoData.partList.filter { it.partId.toInt() > part.partId.toInt() }.onEach {
                    // url
                    if (it.playUrl.isNotEmpty()) {
                        parts.add(
                            RadioPart(
                                radioId = part.radioId,
                                episodeId = part.episodeId,
                                title = it.title,
                                partId = it.partId,
                                business = it.business,
                                url = it.playUrl,
                                coverUrl = it.trackCover,
                                aigc = it.aigc
                            )
                        )
                    }
                    // track
                    if (it.trackId > 0L) {
                        parts.add(
                            RadioPart(
                                radioId = part.radioId,
                                episodeId = part.episodeId,
                                title = it.title,
                                partId = it.partId,
                                business = it.business,
                                trackId = it.trackId,
                                coverUrl = it.trackCover,
                                aigc = it.aigc
                            )
                        )
                    }
                }
                if (parts.isEmpty()) {
                    finishCallback.invoke()
                    dlog("getRadioEpisodeVoiceList parts is empty")
                    return@launch
                }
                dlog("有新的aigc数据，num==${parts.size} 执行播放 >>>")
                playOfflineAiRadioList(parts)
            }.onFailure {
                dlog("getRadioEpisodeVoiceList error $it")
                finishCallback.invoke()
                XDCSCollectUtil.statErrorToXDCS(
                    "radio",
                    "getRadioEpisodeVoiceList cause ${it.cause.toString()}"
                )
            }
        }
    }

    private fun dlog(msg: String) {
        XYLogger.log("AiRadioPlayHelper", msg)
    }

    private fun xLog(msg: String) {
        Logger.i("AiRadioPlayHelper", msg)
    }

    private fun tracePlayDuration() {
        var duration = 0L
        if (playStartTime > 0L) {
            duration = (System.currentTimeMillis() - playStartTime) / 1000
            playStartTime = 0L
        }
        // agent长音频-播放时长  其他事件
        XMTraceApi.Trace()
            .setMetaId(68062)
            .setServiceId("others")
            .put("agent", rnAgent)
            .put("sceneName", rnScene)
            .put("productId", "S_PROD16_1549")
            .put("id", curPart?.radioId ?: "")
            .put("title", curPart?.title ?: "")
            .put("url", curPart?.url ?: "")
            .put("trackId", curPart?.trackId?.toString() ?: "")
            .put("chatXmlyIntent", rnintent)
            .put("duration", duration.toString())
            .put("abtestGroupId", if (skipUrlTrack) "1" else "0")
            .createTrace()
    }
}