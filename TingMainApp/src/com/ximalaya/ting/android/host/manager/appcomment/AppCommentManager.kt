package com.ximalaya.ting.android.host.manager.appcomment

import android.text.TextUtils
import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import org.json.JSONObject
import java.lang.ref.WeakReference

/**
 * Author: <PERSON> Shi
 * Email: <EMAIL>
 * Date: 2023/4/7
 * Description：
 */
object AppCommentManager {
    const val BUS_KEY_BASE = "基础好评弹窗"

    private const val KEY_APP_COMMENT_CLICK_TIME = "app_comment_click_time"
    private const val KEY_APP_COMMENT_LAST_TIME = "app_comment_last_time"
    private var fragmentReference: WeakReference<BaseFragment2>? = null

    private val mAppCommentRunnable = Runnable {
        showAppCommendDialogInner()
    }

    fun showDialog(delayLong: Long, show: Boolean = true) {
        HandlerManager.removeCallbacks(mAppCommentRunnable)
        if (show) {
            HandlerManager.postOnUIThreadDelay(mAppCommentRunnable, delayLong)
        }
    }

    fun init(baseFragment2: BaseFragment2?) {
        if (baseFragment2 != null) {
            fragmentReference = WeakReference(baseFragment2)
        }
    }

    fun release() {
        HandlerManager.removeCallbacks(mAppCommentRunnable)
        fragmentReference = null
    }

    /**
     * 1. 用户点击过"给好评"按钮后，除非卸载重装，否则180天内不出弹窗
     * 2. 30天内最多出两次弹窗（可配置）
     * 3. 每次弹窗间隔时间不低于15天（可配置）
     *
     * 播放页：点赞或评论成功
     * 我的、关注页：进入页面
     * */
    private fun shouldShowAppCommendDialog(): Boolean {
        val clickTime = MMKVUtil.getInstance().getLong(KEY_APP_COMMENT_CLICK_TIME, 0)
        val lastShowTime = MMKVUtil.getInstance().getLong(KEY_APP_COMMENT_LAST_TIME, 0)

        if (clickTime > 0 && (System.currentTimeMillis() - clickTime < getTwiceTopRateIntervalDay() * 24 * 60 * 60 * 1000L)) {
            return false
        }
        val lastConfig = getTwiceIntervalDay()

        if (lastShowTime > 0 && (System.currentTimeMillis() - lastShowTime < lastConfig * 24 * 60 * 60 * 1000L)) {
            return false
        }

        val twiceConfig = getMaxCountOneMonth()
        // 30天内只出2次
        val time30DaysAgo = System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000L
        val removeList = mutableListOf<String>()
        MMKVUtil.getInstance("AppCommentManager").allKeys?.forEach {
            val time = it?.toLong()
            if (time == null || time < time30DaysAgo) {
                removeList.add(it)
            }
        }
        removeList.forEach {
            MMKVUtil.getInstance("AppCommentManager").removeByKey(it)
        }
        val allKeySize = MMKVUtil.getInstance("AppCommentManager").allKeys?.size ?: 0
        if (allKeySize >= twiceConfig) {
            return false
        }

        return true
    }

    private fun showAppCommendDialogInner() {
        if (!shouldShowAppCommendDialog()) {
            return
        }
        if (ViewUtil.haveDialogIsShowing(BaseApplication.getMainActivity() as? FragmentActivity?)) {
            return
        }
        if (fragmentReference != null && fragmentReference!!.get() != null) {
            MMKVUtil.getInstance().saveLong(KEY_APP_COMMENT_LAST_TIME, System.currentTimeMillis())
            MMKVUtil.getInstance("AppCommentManager").saveLong(System.currentTimeMillis().toString(), System.currentTimeMillis())
            ViewUtil.setHasDialogShow(false)
            showAppCommentDialogForGlobal(fragmentReference!!.get(), null, null, null, BUS_KEY_BASE, "newPlay", null, null)
        }
    }

    private fun getTwiceIntervalDay(): Long {
        val json = getDataConfigCenter()
        if (json != null) {
            return json.optLong("twiceIntervalDay", 15)
        }
        return 15
    }

    private fun getMaxCountOneMonth(): Long {
        val json = getDataConfigCenter()
        if (json != null) {
            return json.optLong("maxCountOneMonth", 2)
        }
        return 2
    }

    private fun getTwiceTopRateIntervalDay(): Int {
        val json = getDataConfigCenter()
        if (json != null) {
            return json.optInt("twiceTopRateIntervalDay", 180)
        }
        return 180
    }

    fun getDialogTitle(defaultValue: String?): String? {
        val json = getDataConfigCenter()
        if (json != null) {
            return json.optString("dialogTitle", defaultValue)
        }
        return defaultValue
    }

    fun getConfigString(key: String?, defaultValue: String?): String? {
        var defaultCallback = ""
        defaultCallback = defaultValue ?: ""
        if (TextUtils.isEmpty(key)) {
            return defaultCallback
        }
        val json = getDataConfigCenter()
        if (json != null) {
            return json.optString(key, defaultCallback)
        }
        return defaultCallback
    }

    /**
     * 各业务请使用这个入口，busType找甘梦琪定义，currPage是曝光的页面的埋点
     * */
    fun showAppCommentDialogForGlobal(baseFragment2: BaseFragment2?,
                                      dialogTitle: String?,
                                      praiseTitle: String?,
                                      feedbackTitle: String?,
                                      busType: String?,
                                      currPage: String?,
                                      goodClickCallBack: AppCommentBottomDialog.IOnBtnClick?,
                                      badClickCallBack: AppCommentBottomDialog.IOnBtnClick?) {
        if (ViewUtil.haveDialogIsShowing(BaseApplication.getMainActivity() as? FragmentActivity?)) {
            return
        }
        if (baseFragment2 != null) {
            AppCommentBottomDialog.show(baseFragment2, dialogTitle, praiseTitle, feedbackTitle, busType, currPage, goodClickCallBack, badClickCallBack)
        }
    }

    private fun getDataConfigCenter(): JSONObject? {
        val configStr = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME,
            CConstants.Group_toc.KEY_APP_COMMENT_NEW_CONFIG, "")
        if (!TextUtils.isEmpty(configStr)) {
            return JSONObject(configStr)
        }
        return null
    }

    fun onAppCommendDialogActiveClick() {
        MMKVUtil.getInstance().saveLong(KEY_APP_COMMENT_CLICK_TIME, System.currentTimeMillis())
    }
}