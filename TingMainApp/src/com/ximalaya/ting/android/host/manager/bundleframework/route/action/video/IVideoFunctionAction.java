package com.ximalaya.ting.android.host.manager.bundleframework.route.action.video;

import android.content.Context;

import com.ximalaya.ting.android.host.manager.bundleframework.route.action.base.IAction;
import com.ximalaya.ting.android.host.model.play.DubMixSubtitleParams;
import com.ximalaya.ting.android.player.video.listener.IVideoPreLoadManager;
import com.ximalaya.ting.android.player.video.listener.IXmVideoView;
import com.ximalaya.ting.android.xmplaysdk.IMediaPlayerControl;

import java.util.List;

/**
 * Created by easoll on 18-1-29.
 * <AUTHOR>
 */

public interface IVideoFunctionAction extends IAction {
    interface IVideoMediaStreamClipperListener{
        void onSuccess();
        void onError(int what,int extra);
        void onProgress(int progress);
    }

    interface IGlobalVideoPlayStatusListener {
        void onStart(String videoSourceUrl);

        void onPause(String videoSourceUrl, long playedTime, long duration);

        void onStop(String videoSourceUrl, long playedTime, long duration);

        void onComplete(String videoSourceUrl, long duration);

        void onError(String videoSourceUrl, long playedTime, long duration);

        void onProgress(String videoSourceUrl, long curPosition, long duration);

        void onRenderingStart(String videoSourceUrl, long renderingSpentMilliSec);

        void onBlockingStart(String videoSourceUrl);

        void onBlockingEnd(String videoSourceUrl);
    }

    interface IDubStreamMuxer{
        int IMAGE_DUB = 0;
        int VIDEO_DUB = 1;
        int VIDEO_KACHA = 2;
        //paths = [背景声，人声，视频] 或者 [背景声，人声]
        void muxStream(String[] paths, String outPutPath);
        void setMuxMode(int dubType);
        void setMuxFadeOutMode(boolean shouldFadeOut);
        void setRecordVolume(float volume);
        void setBgVolume(float volume);
        void setMuxerListener(IVideoFunctionAction.IDubStreamMuxerListener muxerListener);
    }

    interface IDubStreamMuxerListener{
        void onMuxStart();
        void onMuxProgress(int progress);
        void onMuxFinish();
        void onError(int errorCode, int extra);
    }

    interface IDubWithCameraMixer {
        void addMixListener(IDubWithCameraMixerListener dubWithCameraMixerListener);
        void removeMixListener(IDubWithCameraMixerListener dubWithCameraMixerListener);
        void mixCameraInVideo(int cameraType, String videoPath, String cameraRecordPath, String waterPicturePath,
            String outputPath);
        void stopMix();
        void releaseMix();
        boolean audioConcat(List<String> inputAudioList, String outputPath);
        void generateSilentAACFile(String outputPath, long durationMS);
        void burnSubtitle(String rawVideoPath, String outputPath, DubMixSubtitleParams params);
        void pipMergeVideoWithSubtitle(int cameraType, String videoPath, String cameraRecordPath,
            String waterPicturePath, String outputPathWithSubtitle, String outputPathWithPipSubtitle,
            DubMixSubtitleParams params);
    }
    interface IDubWithCameraMixerListener{
        void onStarted();
        void onStopped();
        void onProgress(int progress);
        void onCompleted();
        void onError();
    }

    interface IVideoCacheReuseManager {

        /**
         * 复用缓存的视频。如果本地有url 对应的缓存视频则直接使用，减少下载
         *
         * @param url 视频播放地址
         * @param callback 复用结果回调
         */
        void reuseCacheVideo(String url, VideoCacheReuseCallback callback);
    }

    /**
     * 复用视频缓存回调
     */
    interface VideoCacheReuseCallback {

        /**
         * 缓存存在，能够复用
         *
         * @param localCachePath 缓存的本地地址
         */
        void onCacheReused(String localCachePath);

        /**
         * 缓存不存在
         */
        void onCacheNoneExist();
    }

    IVideoPlayer getVideoPlayer(Context context);

    IVideoPlayer getVideoPlayer(Context context, boolean useIjk);
    /**
     * 有声满视频播放页的播放器，和普通视频专辑播放器的区别集中在Controller的样式
     **/
    IVideoPlayer getCartoonVideoPlayer(Context context);
    IVideoPlayer getDynamicDetailVideoPlayer(Context context);
    /**
     * 获取新版播放页视频tab播放器
     */
    IVideoPlayer getPlayVideoTabVideoPlayer(Context context);
    IVideoSource getVideoSource(String title, String url);
    IVideoSource getVideoSource(String title, String url, String coverUrl);
    IMediaMetadataRetriever getMediaMetaRetriever();
    IMediaStreamClipper getMediaStreamClipper(IVideoMediaStreamClipperListener listener);

    IMediaStreamClipper getMediaStreamClipper(IVideoMediaStreamClipperListener listener,String sourceFrom);
    IVideoPlayerAndClipper getPlayAndClipper(Context context);

    IMediaPlayerControl newXmVideoView(Context context);

    IMediaPlayerControl newXmVideoView(Context context, boolean forceUseIjk);

    void setAllowUseMobileNetwork(boolean allow);

    void addOnRequestAllowMobileNetworkListener(IOnRequestAllowMobileNetworkListener listener);

    void removeOnRequestAllowMobileNetworkListener(IOnRequestAllowMobileNetworkListener listener);
    IVideoPlayer getVideoPlayerForDub(Context context);

    IVideoPlayer getVideoPlayerForDubRecord(Context context);

    IDubStreamMuxer getDubStreamMuxer();

    IDubCameraView getDubCameraView(Context context);

    IDubWithCameraMixer getDubWithCameraMixer();

    IVideoPreLoadManager getVideoPreLoadManager(Context context);

    IVideoCacheReuseManager getVideoCacheReuseManager();

    void addGlobalVideoPlayStatusListener(IGlobalVideoPlayStatusListener listener);
    void removeGlobalVideoPlayStatusListener(IGlobalVideoPlayStatusListener listener);

    IVideoTouchEventHandler newVideoTouchEventHandler(Context context, IVideoTouchEventHandler.IVideoTouchHelper helper);
}
