package com.ximalaya.ting.android.host.manager.firework;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import com.ximalaya.ting.android.firework.dialog.monitor.IDialogMonitor;
import com.ximalaya.ting.android.firework.model.AdModel;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.R;

/**
 * Created by luhang on 2019-06-21.
 *
 * <AUTHOR>
 * Email: <EMAIL>
 * Tel:15918812121
 */
public class FireworkForAdVideoFragment extends FireworkForVideoFragment {

    private static final String KEY_AD_DATA = "ad_data";

    @Nullable
    private AdModel adModel;
    private String mJumpUrl;
    private ImageView mAdMark;

    public static FireworkForAdVideoFragment newVideoInstance(AdModel adModel) {
        FireworkForAdVideoFragment fireworkForAdVideoFragment = new FireworkForAdVideoFragment();
        Bundle bundle = new Bundle();

        if (adModel != null) {
            bundle.putParcelable(KEY_AD_DATA, adModel);
        }

        fireworkForAdVideoFragment.setArguments(bundle);
        return fireworkForAdVideoFragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() == null) {
            return;
        }
        adModel = getArguments().getParcelable(KEY_AD_DATA);

        if (adModel == null) {
            return;
        }
        mVideoUrl = adModel.videoUrl;//bundle.getString(BUNDLE_EXTRA_VIDEO_URL);
        mJumpUrl = adModel.realLink;//bundle.getString(BUNDLE_EXTRA_VIDEO_JUMP_URL);
        mCoverUrl = adModel.cover;//bundle.getString(BUNDLE_EXTRA_VIDEO_COVER_URL);
        mBackgroundUrl = adModel.bgCover;//bundle.getString(BUNDLE_EXTRA_BACKGROUND_IMG_URL);
        IDialogMonitor.DEFAULT.onFragmentShow(this, mVideoUrl);
    }

    @Override
    protected void clickToJump() {
        if (adModel != null && !TextUtils.isEmpty(adModel.realLink)) {
            findViewById(R.id.host_firework_total_layout).setBackgroundResource(R.color.host_transparent);
        }
        onJump(this, null);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mAdMark = findViewById(R.id.host_firework_ad_tag);
        if (mAdMark != null && adModel != null) {
            mAdMark.setVisibility(View.VISIBLE);
            ImageManager.from(getContext()).displayImage(mAdMark, adModel.adMark,
                    R.drawable.host_ad_tag_style_2);
        }
    }
}
