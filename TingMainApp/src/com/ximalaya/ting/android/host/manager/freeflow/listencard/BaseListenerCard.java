package com.ximalaya.ting.android.host.manager.freeflow.listencard;

import android.annotation.SuppressLint;
import android.content.Context;
import android.telephony.TelephonyManager;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.manager.freeflow.FreeFlowDataUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

import androidx.annotation.NonNull;

/**
 * Created by nali on 2018/4/26.
 *
 * <AUTHOR>
 */
public abstract class BaseListenerCard {

    public abstract void checkIsListenCard(Context context, final IDataCallBack<Boolean> callBack);

    class ListenCardCheckInfo {
        int code;
        String checkKey;
        String localIp;

        public ListenCardCheckInfo(int code, String checkKey, String localIp) {
            this.code = code;
            this.checkKey = checkKey;
            this.localIp = localIp;
        }
    }

    /**
     * 是否需要请求
     * @param context
     * @return 0表示通过回调返回false ,1 表示通过回调返回true ,2 表示需要通过请求确定是否是联名卡
     */
    @SuppressLint("MissingPermission")
    @NonNull
    ListenCardCheckInfo needRequestIsListenerCard(Context context ,int netWorkType) {
        final String localIpAddress = getLocalIpAddress();
        if(TextUtils.isEmpty(localIpAddress)) {
            return new ListenCardCheckInfo(0 ,null ,null);
        }

        String savedIMSI = FreeFlowDataUtil.getInstance(context).getSaveListenCardImsiAndIp();
        String checkKey = null;
        try{
            TelephonyManager telephonyManager = SystemServiceManager.getTelephonyManager(context);

            String subscriberId = FreeFlowDataUtil.getSubscriberId(telephonyManager);

            checkKey = subscriberId + localIpAddress;
            if(checkKey.equals(savedIMSI))	{
                int listenCard = FreeFlowDataUtil.getInstance(context).isListenCard(netWorkType, checkKey);
                if(listenCard >= 0) {
                    return new ListenCardCheckInfo(listenCard ,checkKey ,localIpAddress) ;
                }
            } else {
                FreeFlowDataUtil.getInstance(context).saveListenCardImsi(checkKey);
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        return new ListenCardCheckInfo(2 ,checkKey ,localIpAddress);
    }

    public static String getLocalIpAddress() {
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface
                    .getNetworkInterfaces(); en.hasMoreElements();) {
                NetworkInterface intf = en.nextElement();
                for (Enumeration<InetAddress> enumIpAddr = intf
                        .getInetAddresses(); enumIpAddr.hasMoreElements();) {
                    InetAddress inetAddress = enumIpAddr.nextElement();
                    if (!inetAddress.isLoopbackAddress() && !inetAddress.isLinkLocalAddress()
                            && inetAddress instanceof Inet4Address
                            && inetAddress.getHostAddress() != null
                            && !inetAddress.getHostAddress().startsWith("192.")) {
                        return inetAddress.getHostAddress();
                    }
                }
            }
        } catch (SocketException ex) {
        }
        return null;
    }
}
