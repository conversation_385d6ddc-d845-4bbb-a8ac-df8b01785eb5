package com.ximalaya.ting.android.host.manager.play;

import android.os.Bundle;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;

/**
 * Created by chengyun.wu on 17/8/15.
 *
 * <AUTHOR>
 *         主app 播放Fragment 生成器
 */

public class MainPlayFragmentFactory implements IPlayFragmentFactory {
    @Override
    public Class getPlayFragmentClass() {
        try {
            return Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().getPlayFragmentClass();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public BaseFragment generatePlayFragment(PlayableModel playableModel, Bundle bundle) {
        try {
            Class playFragmentClass = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().getPlayFragmentClass();
            BaseFragment fragment = (BaseFragment) playFragmentClass.newInstance();
            fragment.fid = Configure.MainFragmentFid.PLAY_FRAGMENT;
            fragment.setArguments(bundle);
            return fragment;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public boolean canShowCurrent(BaseFragment fragment, PlayableModel playableModel, Bundle bundle) {
        return fragment != null && fragment.getClass() == getPlayFragmentClass();
    }
}
