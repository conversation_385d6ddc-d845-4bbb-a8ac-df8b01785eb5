package com.ximalaya.ting.android.host.manager.play;

import android.os.Bundle;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.host.manager.AccessibilityModeManager;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RadioActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;

/**
 * Created by chengyun.wu on 17/8/15.
 *
 * <AUTHOR>
 */

public class RadioPlayFragmentFactory implements IPlayFragmentFactory {

    @Override
    public Class getPlayFragmentClass() {
        try {
            if (ElderlyModeManager.getInstance().isElderlyMode() && ElderlyModeManager.getInstance().hasElderlyBundleInit()) {
                return Router.<RadioActionRouter>getActionRouter(Configure.BUNDLE_RADIO).getFragmentAction()
                        .findRadioBundleFragmentClassByFid(Configure.ElderlyFragmentFid.RADIO_FRAGMENT);
            } else if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
                return Router.<RadioActionRouter>getActionRouter(Configure.BUNDLE_RADIO).getFragmentAction()
                        .findRadioBundleFragmentClassByFid(Configure.ElderlyFragmentFid.RADIO_FRAGMENT);
            } else {
                return Router.<RadioActionRouter>getActionRouter(Configure.BUNDLE_RADIO).getFragmentAction()
                        .findRadioBundleFragmentClassByFid(Configure.RadioFragmentFid.RADIO_FRAGMENT);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public BaseFragment generatePlayFragment(PlayableModel playableModel, Bundle bundle) {
        try {
            return Router.<RadioActionRouter>getActionRouter(Configure.BUNDLE_RADIO).getFunctionAction().generatePlayFragment(playableModel, bundle);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public boolean canShowCurrent(BaseFragment fragment, PlayableModel playableModel, Bundle bundle) {
        return fragment != null && fragment.getClass() == getPlayFragmentClass();
    }
}
