package com.ximalaya.ting.android.host.manager.request;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.firework.FireworkApi;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * Created by luh<PERSON> on 2019-08-07.
 *
 * <AUTHOR>
 * Email: <EMAIL>
 * Tel:15918812121
 */
public class DelayRequestManager {
    /**
     * abtest pickoff 接口
     */
//    public static void abTestPickOf() {
//        ConfigureCenter.getInstance().abTestPickOff(BaseApplication.getMyApplicationContext());
//    }
//    public static void abTestPickOf() {
//        ConfigureCenter.getInstance().abTestPickOff(BaseApplication.getMyApplicationContext());
//    }

    /**
     * 弹屏同步本地dialog 配置
     */
    public static void fireworkNativeDialog() {
        FireworkApi.getInstance().syncNativeDialog();
    }

    public static void uploadTraceData() {
        XMTraceApi.getInstance().uploadTraceDate();
    }
}
