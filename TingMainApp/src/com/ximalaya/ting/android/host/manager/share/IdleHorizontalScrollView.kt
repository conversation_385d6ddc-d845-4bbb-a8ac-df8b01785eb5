package com.ximalaya.ting.android.host.manager.share

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.util.Log
import android.widget.HorizontalScrollView
import android.view.MotionEvent

open class IdleHorizontalScrollView : HorizontalScrollView {

    private var canScroll = true

    constructor(
        context: Context?,
        attrs: AttributeSet?, defStyle: Int
    ) : super(context, attrs, defStyle)

    constructor(
        context: Context?,
        attrs: AttributeSet?
    ) : super(context, attrs)

    constructor(context: Context?) : super(context)

    interface ScrollViewListener {
        fun onScrollChanged(scrollType: ScrollType?)
    }

    private var mHandler: Handler? = Handler(Looper.getMainLooper())
    private var scrollViewListener: ScrollViewListener? = null

    /**
     * 滚动状态   IDLE 滚动停止  TOUCH_SCROLL 手指拖动滚动         FLING滚动
     */
    enum class ScrollType {
        IDLE, TOUCH_SCROLL, FLING
    }

    /**
     * 记录当前滚动的距离
     */
    private var currentX = -9999999

    /**
     * 当前滚动状态
     */
    private var scrollType = ScrollType.IDLE

    /**
     * 滚动监听间隔
     */
    private val scrollDealy = 50

    /**
     * 滚动监听runnable
     */
    private val scrollRunnable: Runnable = object : Runnable {
        override fun run() {
            if (scrollX == currentX) {
                //滚动停止  取消监听线程  
                Log.d("", "停止滚动")
                scrollType = ScrollType.IDLE
                if (scrollViewListener != null) {
                    scrollViewListener!!.onScrollChanged(scrollType)
                }
                mHandler!!.removeCallbacks(this)
                return
            } else {
                //手指离开屏幕    view还在滚动的时候  
                Log.d("", "Fling。。。。。")
                scrollType = ScrollType.FLING
                if (scrollViewListener != null) {
                    scrollViewListener!!.onScrollChanged(scrollType)
                }
            }
            currentX = scrollX
            mHandler!!.postDelayed(this, scrollDealy.toLong())
        }
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        if (!canScroll) {
            return false
        }
        when (ev.action) {
            MotionEvent.ACTION_MOVE -> {

                scrollType = ScrollType.TOUCH_SCROLL
                scrollViewListener!!.onScrollChanged(scrollType)
                //手指在上面移动的时候   取消滚动监听线程  
                mHandler!!.removeCallbacks(scrollRunnable)
            }
            MotionEvent.ACTION_UP ->                 //手指移动的时候
            {
                mHandler!!.post(scrollRunnable)
            }
        }
        return super.onTouchEvent(ev)
    }

    fun setHandler(handler: Handler?) {
        mHandler = handler
    }

    fun setOnScrollStateChangedListener(listener: ScrollViewListener?) {
        scrollViewListener = listener
    }

    fun setCanScroll(canScroll: Boolean = true) {
        this.canScroll = canScroll
    }
}