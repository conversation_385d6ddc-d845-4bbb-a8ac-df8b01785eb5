package com.ximalaya.ting.android.host.manager.statistic;

import android.content.Context;
import android.util.Log;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.util.EncryptProxy;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

import static com.ximalaya.ting.android.host.manager.statistic.ListenTaskUtil.UID_PREFIX_FOR_LISTEN_TASK;


/**
 * 替代之前的 UserOneDataOperatingSPContentProvider 类
 * <p>
 * 可以存取的数据类型包括：int、long、boolean、String
 *
 * <AUTHOR>
 * On 2019-07-25
 */
public class UserOneDateMMKV {

    private static final String TAG = "UserOneDateMMKV";
    private static final String FILENAME_USER_ONEDATE_DATA = "user_one_date_data";
    private static final String KEY_IS_MIGRATIONED = "key_is_migrationed";

    private static boolean isMigrationed = false;

    public UserOneDateMMKV(Context context) {
        if (null != context) {
            MMKVUtil.initialize(context);
            MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA);
            migrationData(context);
        } else {
            Log.e(TAG, "Method: UserOneDateMMKV, Parameter is null");
        }
    }

    private static synchronized void migrationData(Context context) {
        if (isMigrationed) {
            return;
        }

        isMigrationed = true;


        try {

            boolean isMigrationed = MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).getBoolean(KEY_IS_MIGRATIONED);
            if (isMigrationed) {
                return;
            }

            String[] keys = MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).getAllKeys();
            MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).saveBoolean(KEY_IS_MIGRATIONED, true);
            if (keys != null && keys.length > 0) {
                for (String key : keys) {
                    if (!TextUtils.isEmpty(key) && key.startsWith(UID_PREFIX_FOR_LISTEN_TASK)) {
                        String replace = key.replace(UID_PREFIX_FOR_LISTEN_TASK, "");
                        long uid;
                        try {
                            uid = Long.parseLong(replace);
                        } catch (NumberFormatException e) {
                            e.printStackTrace();
                            continue;
                        }

                        MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).saveString(ListenTaskUtil.DISPOSED_UID_PREFIX_FOR_LISTEN_TASK + uid,
                                EncryptProxy.encryptByPublicKeyNative(context,
                                        MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).getString(key)));
                        MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).removeByKey(key);
                    }
                }
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
    }

    public static <T> T getOneDateValue(Context context, String name, T defaultValue) {
        if (null != context) {
            MMKVUtil.initialize(context);
            migrationData(context);
            if (MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).containsKey(name)) {
                if (defaultValue instanceof Boolean) {
                    Boolean def = (Boolean) defaultValue;
                    return (T) Boolean.valueOf(MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).getBoolean(name, def));
                } else if (defaultValue instanceof String) {
                    return (T) MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).getString(name, (String) defaultValue);
                } else if (defaultValue instanceof Integer) {
                    Integer def = (Integer) defaultValue;
                    return (T) Integer.valueOf(MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).getInt(name, def));
                } else if (defaultValue instanceof Long) {
                    Long def = (Long) defaultValue;
                    return (T) Long.valueOf(MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).getLong(name, def));
                }
            }
        }
        return defaultValue;
    }

    public static String getListenerTime(Context context, String key, String defaultStr) {
        String data = (String) UserOneDateMMKV.getOneDateValue(context , key ,"");
        try {
            if (!TextUtils.isEmpty(data)) {
                return EncryptProxy.decryptByPrivateKeyNative(context, data);
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }

        return defaultStr;
    }

    public static void saveOneDateValue(Context context, String name, Object value) {
        if (null != context) {
            MMKVUtil.initialize(context);
            migrationData(context);
            if (value instanceof Boolean) {
                MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).saveBoolean(name, (boolean) value);
            } else if (value instanceof String) {
                MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).saveString(name,
                        String.valueOf(value));
            } else if (value instanceof Integer) {
                MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).saveInt(name, (int) value);
            } else if (value instanceof Long) {
                MMKVUtil.getInstance(FILENAME_USER_ONEDATE_DATA).saveLong(name, (long) value);
            }
        }
    }
}
