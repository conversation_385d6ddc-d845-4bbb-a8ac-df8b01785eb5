package com.ximalaya.ting.android.host.model;

import android.graphics.Color;
import android.os.SystemClock;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.model.play.YellowZoneModel;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2024/10/24 16:27
 * @description: 加高播放页mot下挂条-uve小黄条新数据模型，具体type类型参考{@link YellowZoneModel}
 **/
public abstract class BaseMotBarModel implements Serializable {

    public static <T extends BaseMotBarModel> T parse(JSONObject object, Class<T> clazz) {
        if (null == object) {
            return null;
        }
        try {
            return new Gson().fromJson(object.toString(), clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static <T extends BaseMotBarModel> T parse(String object, Class<T> clazz) {
        if (null == object || object.isEmpty()) {
            return null;
        }
        try {
            return new Gson().fromJson(object, clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @SerializedName("configId")
    public int configId;                        // mot 配置id
    @SerializedName("configType")
    public int configType;                      // mot 配置类型
    @SerializedName("templateId")
    public int templateId;                      // 模板id
    @SerializedName("businessType")
    public int businessType;                    // 业务类型
    @SerializedName("type")
    public int type;                            // 小黄条类型
    @SerializedName("text")
    public String text;                         // 文案
    @SerializedName("subText")
    public String subText;                      // 副文案
    @SerializedName("logo")
    public Logo logo;                           // 业务logo
    @SerializedName("backgroundImage")
    public String backgroundImage;              // IP背景图
    @SerializedName(value = "buttonList", alternate = "buttons")
    public List<ButtonInfo> buttonList;         // 按钮列表
    @SerializedName("style")
    public Style style;                         // 文案样式资源
    @SerializedName("extraInfo")
    public JsonElement extraInfo;               // 额外的信息，内容会根据不同的type发生变化
    @SerializedName("expireTime")
    public long expireTime;                     // 到期时间单位s
    @SerializedName("showCloseIcon")
    public boolean showCloseIcon;               // 是否显示关闭按钮
    @SerializedName("sceneId")
    public int sceneId = -1;                         // 场景id
    @SerializedName("displaySceneId")
    public int displaySceneId = -1;                  // 曝光场景id

    protected boolean localIsForToInvalid = false;
    public long localBaseTimeStamp = SystemClock.elapsedRealtime();
    protected boolean localHasInitMarkPointExtraInfo = false;
    protected Map<String, String> localMarkPointExtraInfo;
    protected boolean localHasInitJsonExtraInfo = false;
    protected JsonObject localJsonExtraInfo;

    public long getExpireTimeInMill() {
        return 1000 * expireTime;
    }


    public abstract boolean isValid();

    public abstract boolean isButtonValid(ButtonInfo buttonInfo);

    public ButtonInfo getFirstButton() {
        if (buttonList != null && buttonList.size() > 0) {
            ButtonInfo firstButton = buttonList.get(0);
            if (firstButton != null) {
                firstButton.localFieldShowAdFreeListen = getStringValueFromExtraInfo("showAdFreeListen");
            }
            return buttonList.get(0);
        }
        return null;
    }

    public boolean isCloseAble() {
        return showCloseIcon;
    }

    public Map<String, String> getMarkPointExtraInfo() {
        if (!localHasInitMarkPointExtraInfo) {
            try {
                if (extraInfo == null) {
                    return null;
                }
                if (extraInfo.isJsonObject()) {
                    JsonObject jsonObject = extraInfo.getAsJsonObject();
                    if (null == localMarkPointExtraInfo) {
                        localMarkPointExtraInfo = new HashMap<>();
                    }
                    Set<String> keys = jsonObject.keySet();
                    if (keys != null && keys.size() > 0) {
                        for (String key : keys) {
                            JsonElement element = jsonObject.get(key);
                            localMarkPointExtraInfo.put(key, element.toString());
                        }
                    }
                } else if (extraInfo.isJsonPrimitive() && ((JsonPrimitive) extraInfo).isString()) {
                    JSONObject jsonObject = null;
                    try {
                        jsonObject = new JSONObject(((JsonPrimitive) extraInfo).getAsString());
                        if (null == localMarkPointExtraInfo) {
                            localMarkPointExtraInfo = new HashMap<>();
                        }
                        if (jsonObject.has("categoryId")) {
                            localMarkPointExtraInfo.put("categoryId", jsonObject.optString("categoryId"));
                        }
                        if (jsonObject.has("materialId")) {
                            localMarkPointExtraInfo.put("materialId", jsonObject.optString("materialId"));
                        }
                        if (jsonObject.has("adid")) {
                            localMarkPointExtraInfo.put("adid", jsonObject.optString("adid"));
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        localHasInitMarkPointExtraInfo = true;
        return localMarkPointExtraInfo;
    }


    public static class ButtonInfo {
        public static final int ACTION_ID_PURCHASE = 1;
        public static final int ACTION_ID_JUMP_URL = 2;
        public static final int ACTION_ID_UNIVERSAL_PAYMENT = 3;
        public static final int ACTION_ID_AD_FLOATING_LAYER = 7; // 本地定义打开畅听广告领时长浮层
        public static final int ACTION_ID_BATCH_TRACK_BUY = 14;
        public static final int ACTION_ID_BATCH_TRACK_DOWNLOAD = 15;
        @SerializedName("text")
        public String text;                 // 按钮文案
        @SerializedName("url")
        public String url;                  // 按钮链接
        @SerializedName("buttonBackgroundImage")
        public String buttonBackgroundImage;                  // 按钮bg链接
        @SerializedName("actionId")
        public int actionId;             // 按钮行为类型，1：购买 2：跳转链接 3:拉起半浮层 7：开畅听广告领时长 14：声音购买浮层（选集弹窗）
        @SerializedName("floatLayerSource")
        public String floatLayerSource;
        @SerializedName("autoAllocateCoupons")
        public List<Long> autoAllocateCoupons; //自动领券id列表

        public String localFieldShowAdFreeListen; // 本地字段
    }

    public static class Logo implements Serializable {
        @SerializedName("logoUrl")
        public String logoUrl; // 业务logo链接
        @SerializedName("size")
        public String size;
    }

    public static class Style implements Serializable {
        @SerializedName("textColor")
        private String textColor;
        @SerializedName("backgroundColor")
        private String backgroundColor;
        @SerializedName("buttonTextColor")
        private String buttonTextColor;
        @SerializedName("buttonBackgroundColor")
        private String buttonBackgroundColor;
        @SerializedName("lightModeTextColor")
        private String lightModeTextColor;
        @SerializedName("lightModeBackgroundColor")
        private String lightModeBackgroundColor;
        @SerializedName("lightModeButtonTextColor")
        private String lightModeButtonTextColor;
        @SerializedName("lightModeButtonBackgroundColor")
        private String lightModeButtonBackgroundColor;


        private int localTextColor = 0;
        private int localBackgroundColor = 0;
        private int localButtonTextColor = 0;
        private int localButtonBackgroundColor = 0;
        private int localLightModeTextColor = 0;
        private int localLightModeBackgroundColor = 0;
        private int localLightModeButtonTextColor = 0;
        private int localLightModeButtonBackgroundColor = 0;

        public int getLocalTextColor() {
            if (localTextColor != 0) {
                return localTextColor;
            }
            try {
                if (textColor != null) {
                    if (textColor.startsWith("#")) {
                        localTextColor = Color.parseColor(textColor);
                    } else {
                        localTextColor = Color.parseColor("#" + textColor);
                    }
                } else {
                    localTextColor = Color.parseColor("#FFFFEBC5");
                }
            } catch (Exception e) {
                localTextColor = Color.parseColor("#FFFFEBC5");
                e.printStackTrace();
            }
            return localTextColor;
        }

        public int getLocalBackgroundColor() {
            if (localBackgroundColor != 0) {
                return localBackgroundColor;
            }
            try {
                if (backgroundColor != null) {
                    if (backgroundColor.startsWith("#")) {
                        localBackgroundColor = Color.parseColor(backgroundColor);
                    } else {
                        localBackgroundColor = Color.parseColor("#" + backgroundColor);
                    }
                } else {
                    localBackgroundColor = Color.parseColor("#1FFFEBC5");
                }
            } catch (Exception e) {
                localBackgroundColor = Color.parseColor("#1FFFEBC5");
                e.printStackTrace();
            }
            return localBackgroundColor;
        }

        public int getLocalButtonTextColor() {
            if (localButtonTextColor != 0) {
                return localButtonTextColor;
            }
            try {
                if (buttonTextColor != null) {
                    if (buttonTextColor.startsWith("#")) {
                        localButtonTextColor = Color.parseColor(buttonTextColor);
                    } else {
                        localButtonTextColor = Color.parseColor("#" + buttonTextColor);
                    }
                } else {
                    localButtonTextColor = Color.parseColor("#FFFFEBC5");
                }
            } catch (Exception e) {
                localButtonTextColor = Color.parseColor("#FFFFEBC5");
            }
            return localButtonTextColor;
        }

        public int getLocalButtonBackgroundColor() {
            if (localButtonBackgroundColor != 0) {
                return localButtonBackgroundColor;
            }
            try {
                if (buttonBackgroundColor != null) {
                    if (buttonBackgroundColor.startsWith("#")) {
                        localButtonBackgroundColor = Color.parseColor(buttonBackgroundColor);
                    } else {
                        localButtonBackgroundColor = Color.parseColor("#" + buttonBackgroundColor);
                    }
                } else {
                    localButtonBackgroundColor = Color.parseColor("#1FFFEBC5");
                }

            } catch (Exception e) {
                localButtonBackgroundColor = Color.parseColor("#1FFFEBC5");
            }
            return localButtonBackgroundColor;
        }

        public int getLocalLightModeTextColor() {
            if (localLightModeTextColor != 0) {
                return localLightModeTextColor;
            }
            try {
                if (lightModeTextColor != null) {
                    if (lightModeTextColor.startsWith("#")) {
                        localLightModeTextColor = Color.parseColor(lightModeTextColor);
                    } else {
                        localLightModeTextColor = Color.parseColor("#" + lightModeTextColor);
                    }
                } else {
                    localLightModeTextColor = Color.parseColor("#FF794A16");
                }
            } catch (Exception e) {
                localLightModeTextColor = Color.parseColor("#FF794A16");
                e.printStackTrace();
            }
            return localLightModeTextColor;
        }

        public int getLocalLightModeBackgroundColor() {
            if (localLightModeBackgroundColor != 0) {
                return localLightModeBackgroundColor;
            }
            try {
                if (lightModeBackgroundColor != null) {
                    if (lightModeBackgroundColor.startsWith("#")) {
                        localLightModeBackgroundColor = Color.parseColor(lightModeBackgroundColor);
                    } else {
                        localLightModeBackgroundColor = Color.parseColor("#" + lightModeBackgroundColor);
                    }
                } else {
                    localLightModeBackgroundColor = Color.parseColor("#FFFDF7EB");
                }
            } catch (Exception e) {
                localLightModeBackgroundColor = Color.parseColor("#FFFDF7EB");
                e.printStackTrace();
            }
            return localLightModeBackgroundColor;
        }

        public int getLocalLightModeButtonTextColor() {
            if (localLightModeButtonTextColor != 0) {
                return localLightModeButtonTextColor;
            }
            try {
                if (lightModeButtonTextColor != null) {
                    if (lightModeButtonTextColor.startsWith("#")) {
                        localLightModeButtonTextColor = Color.parseColor(lightModeButtonTextColor);
                    } else {
                        localLightModeButtonTextColor = Color.parseColor("#" + lightModeButtonTextColor);
                    }
                } else {
                    localLightModeButtonTextColor = Color.parseColor("#FF794A16");
                }
            } catch (Exception e) {
                localLightModeButtonTextColor = Color.parseColor("#FF794A16");
            }
            return localLightModeButtonTextColor;
        }

        public int getLocalLightModeButtonBackgroundColor() {
            if (localLightModeButtonBackgroundColor != 0) {
                return localLightModeButtonBackgroundColor;
            }
            try {
                if (lightModeButtonBackgroundColor != null) {
                    if (lightModeButtonBackgroundColor.startsWith("#")) {
                        localLightModeButtonBackgroundColor = Color.parseColor(lightModeButtonBackgroundColor);
                    } else {
                        localLightModeButtonBackgroundColor = Color.parseColor("#" + lightModeButtonBackgroundColor);
                    }
                } else {
                    localLightModeButtonBackgroundColor = Color.parseColor("#FFFFE5B2");
                }

            } catch (Exception e) {
                localLightModeButtonBackgroundColor = Color.parseColor("#FFFFE5B2");
            }
            return localLightModeButtonBackgroundColor;
        }

        public static Style EMPTY_STYLE = new Style();
    }

    public String getStringValueFromExtraInfo(String key) {
        initLocalExtraInfoJsonObject();
        if (localJsonExtraInfo != null) {
            JsonElement valueElement = localJsonExtraInfo.get(key);
            try {
                if (valueElement != null) {
                    return valueElement.getAsString();
                }
            }catch (Exception e){

            }
        }
        return null;
    }

    private void initLocalExtraInfoJsonObject(){
        if (!localHasInitJsonExtraInfo) {
            try {
                if (extraInfo == null) {
                    return;
                }
                if (extraInfo.isJsonObject()) {
                    localJsonExtraInfo = extraInfo.getAsJsonObject();
                } else if (extraInfo.isJsonPrimitive() && ((JsonPrimitive) extraInfo).isString()) {
                    localJsonExtraInfo = JsonParser.parseString(extraInfo.getAsString()).getAsJsonObject();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            localHasInitJsonExtraInfo = true;
        }
    }

    public Boolean getBooleanValueFromExtraInfo(String key) {
        initLocalExtraInfoJsonObject();
        if (localJsonExtraInfo != null) {
            JsonElement valueElement = localJsonExtraInfo.get(key);
            try {
                if (valueElement != null) {
                    return valueElement.getAsBoolean();
                }
            }catch (Exception e){

            }
        }
        return false;
    }

}