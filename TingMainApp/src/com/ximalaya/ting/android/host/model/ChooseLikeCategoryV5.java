package com.ximalaya.ting.android.host.model;

import androidx.annotation.Keep;

import java.util.ArrayList;
import java.util.List;

@Keep
public class ChooseLikeCategoryV5 implements Cloneable {

    protected String categoryName;
    protected String url;
    protected String code;
    protected String parentCode; // 子选项才有
    protected boolean selected;
    protected String color;
    protected boolean isSubCategory; // 子选项 true
    protected String tagIds; // 兴趣id
    private List<ChooseLikeCategoryV5> subCategories;  //父选项才有

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public boolean isSubCategory() {
        return isSubCategory;
    }

    public void setSubCategory(boolean subCategory) {
        isSubCategory = subCategory;
    }

    public List<ChooseLikeCategoryV5> getSubCategories() {
        return subCategories;
    }

    public void setSubCategories(List<ChooseLikeCategoryV5> subCategories) {
        this.subCategories = subCategories;
    }

    @Override
    public ChooseLikeCategoryV5 clone() {
        try {
            ChooseLikeCategoryV5 clone = (ChooseLikeCategoryV5) super.clone();
            if (clone.subCategories != null) {
                clone.subCategories = new ArrayList<>(clone.subCategories);
            }
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
