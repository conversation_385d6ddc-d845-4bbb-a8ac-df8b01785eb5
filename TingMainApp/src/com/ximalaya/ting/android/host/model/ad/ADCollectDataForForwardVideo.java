package com.ximalaya.ting.android.host.model.ad;

/**
 * Created by le.xin on 2020/5/10.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class ADCollectDataForForwardVideo extends AdCollectData {
    private String prompt;         // 强提醒
    private String promptTip;      // 弱提醒
    private String promptPopup;    // 弱提醒弹窗
    private String skipAd;         // 任务离开
    private String skipTime;       // 上报已观看进度（毫秒）
    private String promptSuc;      // 观看视频任务完成
    private String promptPlay;     // 进入激励视频播放页
    private String showSource;      // 1:前插强提醒 2:播放页免广告常驻入口
    private String promptObType;    // 看视频免广告入口类型
    private String promptShowType;  // 驻入口tip展示的类型的集合

    // 免广告4期 上报字段
    private String increaseFreeTime; // 免广告时间
    private String forwardVideoTime; // 看视频时间

    public String getShowSource() {
        return showSource;
    }

    public void setShowSource(String showSource) {
        this.showSource = showSource;
    }

    public String getPromptObType() {
        return promptObType;
    }

    public void setPromptObType(String promptObType) {
        this.promptObType = promptObType;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getPromptTip() {
        return promptTip;
    }

    public void setPromptTip(String promptTip) {
        this.promptTip = promptTip;
    }

    public String getPromptPopup() {
        return promptPopup;
    }

    public void setPromptPopup(String promptPopup) {
        this.promptPopup = promptPopup;
    }

    public String getSkipAd() {
        return skipAd;
    }

    public void setSkipAd(String skipAd) {
        this.skipAd = skipAd;
    }

    public String getSkipTime() {
        return skipTime;
    }

    public void setSkipTime(String skipTime) {
        this.skipTime = skipTime;
    }

    public String getPromptSuc() {
        return promptSuc;
    }

    public void setPromptSuc(String promptSuc) {
        this.promptSuc = promptSuc;
    }

    public String getPromptPlay() {
        return promptPlay;
    }

    public void setPromptPlay(String promptPlay) {
        this.promptPlay = promptPlay;
    }

    public String getPromptShowType() {
        return promptShowType;
    }

    public void setPromptShowType(String promptShowType) {
        this.promptShowType = promptShowType;
    }

    public String getIncreaseFreeTime() {
        return increaseFreeTime;
    }

    public void setIncreaseFreeTime(String increaseFreeTime) {
        this.increaseFreeTime = increaseFreeTime;
    }

    public String getForwardVideoTime() {
        return forwardVideoTime;
    }

    public void setForwardVideoTime(String forwardVideoTime) {
        this.forwardVideoTime = forwardVideoTime;
    }
}
