package com.ximalaya.ting.android.host.model.album

import org.json.JSONObject

/**
 * date : 2024/7/21
 * 新专辑tab model
 */
data class Album3AiOverviewModel(
    val id: Long? = 0,
    val title: String?,
    val summary: String?,
    val landingPage: String?,
    val playTimes: Long? = 0,
) {
    companion object {
        fun parseData(json: JSONObject): Album3AiOverviewModel {
            var count = 0L
            if (json.has("count")) {
                val countJson = json.optJSONObject("count")
                if (countJson != null && countJson.has("play")) {
                    count = countJson.optLong("play")
                }
            }
            return Album3AiOverviewModel(
                json.optLong("id"),
                json.optString("title"),
                json.optString("summary"),
                json.optString("landingPage"),
                count
            )
        }
    }
}