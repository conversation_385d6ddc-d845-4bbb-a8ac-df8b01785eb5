package com.ximalaya.ting.android.host.model.album;

import org.json.JSONObject;

public class Album3LiveModel {
    public static int LIVING_NOW = 9;
    public static int LIVING_WILL = 5;
    public static int LIVING_STOPPING = 1;

    public static int LIVE_TYPE_FIRST_LIVE = 2;

    public String header;
    public String url;
    public String liveRoomName;
    public String smallLogo;
    public String liveAnchorId;
    public String liveRoomId; // 主播直播房间号，在没有直播和直播中点击跳转
    public String liveId; // 直播场次id
    public String liveStatus; // 主播直播标签 9:直播中 5:将要直播,预约中 1:直播结束 0:没有直播
    public String subBizType;
    public String bizType; // liveRoomType
    public String reserveStatus; //预约状态 true已预约 false未预约
    public String startTime; //预约中直播开播时间
    public String liveType; //直播类型 1是非直播首映 2是直播首映 (liveType=2时 只要读取content字段，不需要拼接header)
    public String icon;
    public String liveRecSrc;
    public String liveRecTrack;
    public String desc;

    public static Album3LiveModel parse(JSONObject jsonObject) {
        Album3LiveModel result = null;
        if (jsonObject != null) {
            result = new Album3LiveModel();
            result.header = jsonObject.optString("header");
            result.url = jsonObject.optString("url");
            result.smallLogo = jsonObject.optString("smallLogo");
            result.liveRecSrc = jsonObject.optString("liveRecSrc");
            result.liveRecTrack = jsonObject.optString("liveRecTrack");
            result.liveRoomName = jsonObject.optString("liveRoomName");
            result.liveAnchorId = jsonObject.optString("liveAnchorId");
            result.liveRoomId = jsonObject.optString("liveRoomId");
            result.liveId = jsonObject.optString("liveId");
            result.liveStatus = jsonObject.optString("liveStatus");
            result.bizType = jsonObject.optString("bizType");
            result.reserveStatus = jsonObject.optString("reserveStatus");
            result.startTime = jsonObject.optString("startTime");
            result.liveType = jsonObject.optString("liveType");
            result.icon = jsonObject.optString("icon");
            result.desc = jsonObject.optString("desc");
        }
        return result;
    }
}