package com.ximalaya.ting.android.host.model.album;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.data.model.feed.AttentionModel;
import com.ximalaya.ting.android.host.model.base.BaseModel;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.model.album.Announcer;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 2017/7/28.
 *
 * <AUTHOR>
 */

public class WTAlbumModel extends BaseModel {
    public static final String CORNERMARK = "cornerMark";
    public static final String ALBUMLABEL = "albumLabel";
    // 大图入口（类似广告）
    public static final String PIC_ENTRY = "picEntry";

    @SerializedName("hasMore")
    private boolean hasMore;
    @SerializedName("totalSize")
    private int totalSize;
    @SerializedName("albums")
    private List<AlbumsItem> albums;
    @SerializedName("hasSubscribe")
    private boolean hasSubscribe;
    /**
     * 首页推荐订阅弹窗，客户端根据是否VIP采用不同文案
     */
    private boolean isVip;
    /**
     * 弹窗样式：0，订阅弹窗，1，浏览弹窗
     */
    private int popupStyle;
    /**
     * 首页推荐订阅弹窗，客户端根据是否新用户采用不同标题
     */
    private boolean isNewUser;

    public boolean isHasMore() {
        return hasMore;
    }

    public void setHasMore(boolean hasMore) {
        this.hasMore = hasMore;
    }

    public int getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(int totalSize) {
        this.totalSize = totalSize;
    }

    public boolean isHasSubscribe() {
        return hasSubscribe;
    }

    public void setHasSubscribe(boolean hasSubscribe) {
        this.hasSubscribe = hasSubscribe;
    }

    public boolean isVip() {
        return isVip;
    }

    public void setVip(boolean vip) {
        isVip = vip;
    }

    public int getPopupStyle() {
        return popupStyle;
    }

    public void setPopupStyle(int popupStyle) {
        this.popupStyle = popupStyle;
    }

    public boolean isNewUser() {
        return isNewUser;
    }

    public void setNewUser(boolean newUser) {
        isNewUser = newUser;
    }

    public List<AlbumsItem> getAlbums() {
        return albums;
    }

    public void setAlbums(List<AlbumsItem> albums) {
        this.albums = albums;
    }

    public List<Album> createAlbumItems() {
        List<Album> list = null;
        if (albums != null && albums.size() != 0) {
            list = new ArrayList<>(albums.size());
            for (AlbumsItem item : albums) {
                AlbumM mm = new AlbumM();
                mm.setId(item.getAlbumId());
                mm.setOfficialPublish(item.getContractStatus() == 1);
                mm.setCoverUrlMiddle(item.getCoverMiddle());
                mm.setCoverUrlSmall(item.getCoverSmall());
                mm.setDraft(item.isDraft());
                mm.setIsFinished(item.getIsFinished());
                mm.setIsPaid(item.isIsPaid());
                mm.setPlayCount(item.getPlaysCounts());
                mm.setSubscribeCount(item.getSubscribeCount());
                mm.setRecReason(item.getRecReason());
                mm.setRecommentSrc(item.getRecSrc());
                mm.setRecTrack(item.getRecTrack());
                mm.setRefundSupportType(item.refundSupportType);
                mm.setAlbumTitle(item.getTitle());
                mm.setIncludeTrackCount(item.getTracks());
                mm.setPrice(!TextUtils.isEmpty(item.getPrice()) ? Double.parseDouble(item.getPrice()) : 0);
                mm.setDiscountedPrice(!TextUtils.isEmpty(item.getPrice()) ? Double.parseDouble(item.getPrice()) : 0);
                mm.setDisplayPrice(item.getDisplayPrice());
                mm.setDisplayDiscountedPrice(item.getDisplayDiscountedPrice());
                mm.setStatus(item.getStatus());
                mm.setUpdatedAt(item.getLastUpdateAt());
                mm.setPriceTypeEnum(item.getPriceTypeEnum());
                mm.setRecReason(item.recReason);
                mm.setRecommentSrc(item.recSrc);
                mm.setRecTrack(item.recTrack);
                mm.getExtras().put(CORNERMARK, item.getCornerMark());
                mm.getExtras().put(ALBUMLABEL, item.getAlbumLabel());
                mm.setActivityTag(item.getActivityTag());
                mm.setAlbumSubscript(new AlbumSubscript(item.getAlbumSubscript()));

                AttentionModel attentionModel = new AttentionModel();
                attentionModel.setLastUpdateAt(item.getLastUpdateAt());
                attentionModel.setNickname(item.getNickname());
                mm.setAttentionModel(attentionModel);

                Announcer annoucer = new Announcer();
                annoucer.setNickname(item.getNickname());
                mm.setAnnouncer(annoucer);

                list.add(mm);
            }
        }
        return list;
    }

    public static class AlbumsItem {

        @SerializedName("albumId")
        private int albumId;
        @SerializedName("contractStatus")
        private int contractStatus;
        @SerializedName("coverMiddle")
        private String coverMiddle;
        @SerializedName("coverSmall")
        private String coverSmall;
        @SerializedName("isDraft")
        private boolean isDraft;
        @SerializedName("isPaid")
        private boolean isPaid;
        @SerializedName("lastUpdateAt")
        private long lastUpdateAt;
        @SerializedName("nickname")
        private String nickname;
        @SerializedName("playsCounts")
        private long playsCounts;
        @SerializedName("subscribeCount")
        private long subscribeCount;
        @SerializedName("recReason")
        private String recReason;
        @SerializedName("recSrc")
        private String recSrc;
        @SerializedName("recTrack")
        private String recTrack;
        @SerializedName("refundSupportType")
        private int refundSupportType;
        @SerializedName("title")
        private String title;
        @SerializedName("tracks")
        private int tracks;
        @SerializedName("isFinished")
        private int isFinished;
        @SerializedName("discountedPrice")
        private String discountedPrice;
        @SerializedName("displayDiscountedPrice")
        private String displayDiscountedPrice;
        @SerializedName("displayPrice")
        private String displayPrice;
        @SerializedName("price")
        private String price;
        @SerializedName("priceTypeEnum")
        private int priceTypeEnum;
        @SerializedName("status")
        private int status;// 下架状态 2:下架
        @SerializedName("cornerMark")
        private String cornerMark;//角标 首页推荐弹窗
        @SerializedName("albumLabel")
        private String albumLabel;//专辑标签 首页推荐弹窗
        @SerializedName("activityTag")
        private String activityTag;
        private String albumSubscript;

        public int getAlbumId() {
            return albumId;
        }

        public void setAlbumId(int albumId) {
            this.albumId = albumId;
        }

        public int getContractStatus() {
            return contractStatus;
        }

        public void setContractStatus(int contractStatus) {
            this.contractStatus = contractStatus;
        }

        public String getCoverMiddle() {
            return coverMiddle;
        }

        public void setCoverMiddle(String coverMiddle) {
            this.coverMiddle = coverMiddle;
        }

        public String getCoverSmall() {
            return coverSmall;
        }

        public void setCoverSmall(String coverSmall) {
            this.coverSmall = coverSmall;
        }

        public boolean isIsPaid() {
            return isPaid;
        }

        public void setIsPaid(boolean isPaid) {
            this.isPaid = isPaid;
        }

        public long getLastUpdateAt() {
            return lastUpdateAt;
        }

        public void setLastUpdateAt(long lastUpdateAt) {
            this.lastUpdateAt = lastUpdateAt;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public long getPlaysCounts() {
            return playsCounts;
        }

        public void setPlaysCounts(int playsCounts) {
            this.playsCounts = playsCounts;
        }

        public String getRecReason() {
            return recReason;
        }

        public void setRecReason(String recReason) {
            this.recReason = recReason;
        }

        public String getRecSrc() {
            return recSrc;
        }

        public void setRecSrc(String recSrc) {
            this.recSrc = recSrc;
        }

        public String getRecTrack() {
            return recTrack;
        }

        public void setRecTrack(String recTrack) {
            this.recTrack = recTrack;
        }

        public int getRefundSupportType() {
            return refundSupportType;
        }

        public void setRefundSupportType(int refundSupportType) {
            this.refundSupportType = refundSupportType;
        }

        public String getActivityTag() {
            return activityTag;
        }

        public void setActivityTag(String activityTag) {
            this.activityTag = activityTag;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public int getTracks() {
            return tracks;
        }

        public void setTracks(int tracks) {
            this.tracks = tracks;
        }

        public int getIsFinished() {
            return isFinished;
        }

        public void setIsFinished(int isFinished) {
            this.isFinished = isFinished;
        }

        public String getDiscountedPrice() {
            return discountedPrice;
        }

        public void setDiscountedPrice(String discountedPrice) {
            this.discountedPrice = discountedPrice;
        }

        public String getDisplayDiscountedPrice() {
            return displayDiscountedPrice;
        }

        public void setDisplayDiscountedPrice(String displayDiscountedPrice) {
            this.displayDiscountedPrice = displayDiscountedPrice;
        }

        public String getDisplayPrice() {
            return displayPrice;
        }

        public void setDisplayPrice(String displayPrice) {
            this.displayPrice = displayPrice;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public int getPriceTypeEnum() {
            return priceTypeEnum;
        }

        public void setPriceTypeEnum(int priceTypeEnum) {
            this.priceTypeEnum = priceTypeEnum;
        }

        public boolean isDraft() {
            return isDraft;
        }

        public void setDraft(boolean draft) {
            isDraft = draft;
        }

        public boolean isPaid() {
            return isPaid;
        }

        public void setPaid(boolean paid) {
            isPaid = paid;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getCornerMark() {
            return cornerMark;
        }

        public String getAlbumLabel() {
            return albumLabel;
        }

        public String getAlbumSubscript() {
            return albumSubscript;
        }

        public void setAlbumSubscript(String albumSubscript) {
            this.albumSubscript = albumSubscript;
        }

        public long getSubscribeCount() {
            return subscribeCount;
        }

        public void setSubscribeCount(long subscribeCount) {
            this.subscribeCount = subscribeCount;
        }
    }
}
