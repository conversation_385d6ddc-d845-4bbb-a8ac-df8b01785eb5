package com.ximalaya.ting.android.host.model.album.middlebar;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.model.BaseMotBarModel;
import com.ximalaya.ting.android.host.model.play.YellowZoneModel;

/**
 * <AUTHOR>
 * @time 2024/10/24 16:27
 * @description: 加高播放页mot下挂条-uve小黄条新数据模型，具体type类型参考{@link YellowZoneModel}
 **/
public class AlbumMotBarModel extends BaseMotBarModel {


    @Override
    public boolean isValid() {
        if (templateId == 2 || templateId == 4 || templateId == 5 || templateId == 7) {
            boolean flag = !TextUtils.isEmpty(text);
            if (flag && buttonList != null && buttonList.size() > 0) {
                ButtonInfo button = buttonList.get(0);
                flag = isButtonValid(button);
            }
            return flag;
        }
        return false;
    }

    @Override
    public boolean isButtonValid(BaseMotBarModel.ButtonInfo buttonInfo) {
        if (buttonInfo == null) {
            return false;
        }
        if (TextUtils.isEmpty(buttonInfo.text)) {
            return false;
        }
        return buttonInfo.actionId == -1
                || buttonInfo.actionId == BaseMotBarModel.ButtonInfo.ACTION_ID_JUMP_URL
                || buttonInfo.actionId == BaseMotBarModel.ButtonInfo.ACTION_ID_UNIVERSAL_PAYMENT
                || buttonInfo.actionId == BaseMotBarModel.ButtonInfo.ACTION_ID_BATCH_TRACK_DOWNLOAD
                || buttonInfo.actionId == BaseMotBarModel.ButtonInfo.ACTION_ID_BATCH_TRACK_BUY;
    }


    public int getTextColor() {
        Style temp = style != null ? style : Style.EMPTY_STYLE;
        if (BaseFragmentActivity2.sIsDarkMode) {
            return temp.getLocalTextColor();
        } else {
            return temp.getLocalLightModeTextColor();
        }
    }

    public int getBackgroundColor() {
        Style temp = style != null ? style : Style.EMPTY_STYLE;
        if (BaseFragmentActivity2.sIsDarkMode) {
            return temp.getLocalBackgroundColor();
        } else {
            return temp.getLocalLightModeBackgroundColor();
        }
    }

    public int getButtonTextColor() {
        Style temp = style != null ? style : Style.EMPTY_STYLE;
        if (BaseFragmentActivity2.sIsDarkMode) {
            return temp.getLocalButtonTextColor();
        } else {
            return temp.getLocalLightModeButtonTextColor();
        }
    }

    public int getButtonBackgroundColor() {
        Style temp = style != null ? style : Style.EMPTY_STYLE;
        if (BaseFragmentActivity2.sIsDarkMode) {
            return temp.getLocalButtonBackgroundColor();
        } else {
            return temp.getLocalLightModeButtonBackgroundColor();
        }
    }
}