package com.ximalaya.ting.android.host.model.cloudhistory;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.history.CloudHistoryForPlay;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.Announcer;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by zale on 16/11/18.
 *
 * <AUTHOR>
 */

public class CloudHistroyListenModel {

    private String childTitle;
    private String itemCoverUrl;
    private String itemTitle;
    private long uid;
    private int deviceType; //设备类型 0 未知 1 网页 2 IOS 3 Android 4 WP 5 pad 6 智能硬件
    private String deviceName;
    private String deviceId;
    private int platform = 0; // 默认0 主站 1 开放平台 2 主站儿童模式  3 老年模式(只是获取历史时过滤未购买的付费内容)
    private int length;
    private int direction = 0;//0正序 1倒序
    private long deleteTime;

    private long startedAt;
    private long endedAt;
    private int type;//类型 1:声音 2:广播 3:活动直播 4:主播直播
    private long itemId;//专辑id(type==1)
    private long childId;//声音id(type==1)
    private int breakSecond;//跳出点
    private boolean isSubscribe;//是否订阅
    private long campGroupId;//训练营群组id
    private boolean isAlbumTimeLimited;//是否是限时免费专辑
    private long expireTime;//限免专辑过期时间

    private int preferredType;  // 0表示不是优先；1表示优先
    private int vipFreeType;//0:非免费  1：会员免费
    private boolean isVipFree;
    private boolean isPaid;
    private String albumSubscript;
    private int channelType;    // 今日热点的频道类型

    private int bizSubType; // 业务子类型。比如对于type是8的直播类型，这个值是直播的一级类型，相当于直播里面用的bizType
    private int liveSubType; // 直播二级类型
    private String anchorNickName;
    private String anchorAvatar;
    private int trackType;
    private int decoupleStatus;
    private int modelId;  //标志该历史模型是否要更新
    public int albumAttribute; //  1有声书（非播客）、2播客
    private String quickForceItems;

    @SerializedName(value = "activityTag",alternate = {"activityLabel"})
    private String activityTag;


    //   针对专辑（type=1）1正常，2下架（已购下架专辑返回1）
    public int itemStatus = 1;

    private CloudHistroyListenModel() {
    }

    public CloudHistroyListenModel(long startedAt, long endedAt, int type, int platform,
                                   long itemId, long childId, int breakSecond, long deleteTime) {
        this.startedAt = startedAt;
        this.endedAt = endedAt;
        this.type = type;
        this.platform = platform;
        this.itemId = itemId;
        this.childId = childId;
        this.breakSecond = breakSecond;
        this.deleteTime = deleteTime;
    }

    public JSONObject getMergeHistoryJsonObject() {
        JSONObject jsonObject = new JSONObject();
        try {
            if (startedAt == endedAt) {
                jsonObject.put("endedAt", String.valueOf(System.currentTimeMillis()));
            } else {
                jsonObject.put("endedAt", String.valueOf(this.endedAt));
            }
            jsonObject.put("startedAt", String.valueOf(this.startedAt));
            jsonObject.put("type", String.valueOf(this.type));
            jsonObject.put("itemId", String.valueOf(this.itemId));
            jsonObject.put("childId", String.valueOf(this.childId));
            jsonObject.put("breakSecond", String.valueOf(this.breakSecond));
            jsonObject.put("platform", String.valueOf(this.platform));
            jsonObject.put("bizSubType", String.valueOf(this.bizSubType));
            jsonObject.put("trackType", String.valueOf(this.trackType));
            jsonObject.put("decoupleStatus", String.valueOf(this.decoupleStatus));
        } catch (JSONException e) {
            e.printStackTrace();
            jsonObject = null;
        }
        return jsonObject;
    }

    public JSONObject getBatchDeleteHistoryJsonObject() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("type", String.valueOf(this.type));
            jsonObject.put("itemId", String.valueOf(this.itemId));
            jsonObject.put("childId", String.valueOf(this.childId));
            jsonObject.put("deleteTime", String.valueOf(this.deleteTime));
        } catch (JSONException e) {
            e.printStackTrace();
            jsonObject = null;
        }
        return jsonObject;
    }

    public Track getCloudTrack() {
        Track cloudTrack = new Track();
        cloudTrack.setKind(PlayableModel.KIND_TRACK);
        SubordinatedAlbum album = new SubordinatedAlbum();
        album.setAlbumTitle(itemTitle);
        album.setAlbumId(itemId);
        album.setCoverUrlSmall(itemCoverUrl);
        album.setPreferredType(preferredType);
        album.setVipFreeType(vipFreeType);
        album.setVipFree(isVipFree);
        album.setPaid(isPaid);
        album.setAlbumSubscript(albumSubscript);
        cloudTrack.setCoverUrlLarge(itemCoverUrl);
        cloudTrack.setAlbum(album);
        cloudTrack.setDataId(childId);
        cloudTrack.setTrackTitle(childTitle);
        cloudTrack.setUid(uid);
        cloudTrack.setPaid(isPaid);
        cloudTrack.setDuration(length);
        cloudTrack.setType(trackType);
        cloudTrack.setDecoupleStatus(decoupleStatus);
        Announcer announcer = new Announcer();
        announcer.setNickname(anchorNickName);
        announcer.setAvatarUrl(anchorAvatar);
        announcer.setAnnouncerId(uid);
        cloudTrack.setAnnouncer(announcer);
        if (deviceType != AppConstants.DEVICE_TYPE_XIAOYA) {
            cloudTrack.setLastPlayedMills(breakSecond * 1000);
        }
        if (type == CloudHistoryForPlay.TYPE_TRACK_SINGLE) {
            cloudTrack.setDecoupleStatus(Track.TYPE_SINGLE);
        }
        if (type == CloudHistoryForPlay.RECORD_QUICK_LISTEN) {
            cloudTrack.setQuickForceItems(quickForceItems);
            try {
                long currentTrackId = new JSONArray(quickForceItems).optJSONObject(0).optLong("currentTrackId");
                if (currentTrackId > 0) {
                    cloudTrack.setDataId(currentTrackId);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            cloudTrack.setTabId(childId);
        }
        return cloudTrack;
    }

    public Track getMyClubTrack() {
        Track clubTrack = new Track();
        clubTrack.setKind(PlayableModel.KIND_MYCLUB_REPLAY_TRACK);
        clubTrack.setDataId(itemId);
        clubTrack.setLiveRoomId(itemId);
        clubTrack.setTrackTitle(itemTitle);
        clubTrack.setTrackIntro(childTitle);
        clubTrack.setCoverUrlLarge(itemCoverUrl);
        clubTrack.setCreatedAt(startedAt);
        clubTrack.setDuration(length);
        Announcer announcer = new Announcer();
        announcer.setNickname(anchorNickName);
        announcer.setAvatarUrl(anchorAvatar);
        announcer.setAnnouncerId(uid);
        clubTrack.setAnnouncer(announcer);
        return clubTrack;
    }


    public Track getLocalReadTrack() {
        Track track = new Track();
        track.setKind(PlayableModel.KIND_LOCAL_EBOOK);
        track.setDataId(itemId);
        track.setChannelId(childId);
        track.setTrackTitle(itemTitle);
        track.setCoverUrlLarge(itemCoverUrl);
        track.setCreatedAt(startedAt);
        track.setDuration(length);
        Announcer announcer = new Announcer();
        announcer.setNickname(anchorNickName);
        announcer.setAvatarUrl(anchorAvatar);
        announcer.setAnnouncerId(uid);
        track.setAnnouncer(announcer);
        return track;
    }

    public Track getLiveTrack() {
        Track liveTrack = new Track();
        switch (bizSubType) {
        case BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO:
        case BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO:
            liveTrack.setKind(PlayableModel.KIND_LIVE_FLV);
            break;
        case BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC:
            liveTrack.setKind(PlayableModel.KIND_ENT_FLY);
            break;
        case BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE:
            liveTrack.setKind(PlayableModel.KIND_LIVE_COURSE);
            break;
        }
        liveTrack.setLiveType(bizSubType);
        liveTrack.setKind(PlayableModel.KIND_LIVE_FLV);
        liveTrack.setDataId(itemId);
        liveTrack.setLiveRoomId(itemId);
        liveTrack.setTrackTitle(itemTitle);
        liveTrack.setTrackIntro(childTitle);
        liveTrack.setCoverUrlSmall(itemCoverUrl);
        liveTrack.setCreatedAt(startedAt);
        liveTrack.setLiveId(childId);
        liveTrack.setBizSubType(liveSubType);
        Announcer announcer = new Announcer();
        announcer.setNickname(anchorNickName);
        announcer.setAvatarUrl(anchorAvatar);
        announcer.setAnnouncerId(uid);
        liveTrack.setAnnouncer(announcer);
        return liveTrack;
    }

    public Radio getCloudRadio() {
        Radio cloudRadio = new Radio();
        cloudRadio.setKind(PlayableModel.KIND_SCHEDULE);
        cloudRadio.setCoverUrlSmall(itemCoverUrl);
        cloudRadio.setDataId(itemId);
        cloudRadio.setProgramId(childId);
        cloudRadio.setRadioName(itemTitle);
        cloudRadio.setProgramName(childTitle);
        cloudRadio.setUpdateAt(endedAt);
        if (endedAt == 0) {
            cloudRadio.setUpdateAt(startedAt);
        }
        return cloudRadio;
    }

    public Track getOneKeyTrack() {
        Track onekeyTrack = new Track();
        onekeyTrack.setKind(PlayableModel.KIND_TRACK);
        onekeyTrack.setType(Track.TYPE_ONE_KEY_LISTEN);
        onekeyTrack.setPlaySource(ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY);
        SubordinatedAlbum album = new SubordinatedAlbum();
        album.setAlbumTitle(itemTitle);
        album.setAlbumId(itemId);
        album.setCoverUrlSmall(itemCoverUrl);
        onekeyTrack.setCoverUrlLarge(itemCoverUrl);
        onekeyTrack.setAlbum(album);
        onekeyTrack.setDataId(childId);
        onekeyTrack.setChannelId(childId);
        onekeyTrack.setChannelName(childTitle);
        onekeyTrack.setChannelPic(itemCoverUrl);
        onekeyTrack.setTrackTitle(childTitle);
        return onekeyTrack;
    }

    public int getChannelType() {
        return channelType;
    }
    @Override
    public String toString(){
        return String.valueOf(itemId)+String.valueOf(childId)+String.valueOf(type);
    }

    public void setChannelType(int channelType) {
        this.channelType = channelType;
    }

    public String getChildTitle() {
        return childTitle;
    }

    public void setChildTitle(String childTitle) {
        this.childTitle = childTitle;
    }

    public String getItemCoverUrl() {
        return itemCoverUrl;
    }

    public void setItemCoverUrl(String itemCoverUrl) {
        this.itemCoverUrl = itemCoverUrl;
    }

    public String getItemTitle() {
        return itemTitle;
    }

    public void setItemTitle(String itemTitle) {
        this.itemTitle = itemTitle;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public boolean isSubscribe() {
        return isSubscribe;
    }

    public void setSubscribe(boolean subscribe) {
        isSubscribe = subscribe;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public int getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(int deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public int getPlatform() {
        return platform;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public int getDirection() {
        return direction;
    }

    public void setDirection(int direction) {
        this.direction = direction;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }

    public long getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(long startedAt) {
        this.startedAt = startedAt;
    }

    public long getEndedAt() {
        return endedAt;
    }

    public void setEndedAt(long endedAt) {
        this.endedAt = endedAt;
    }

    public String getQuickForceItems() {
        return quickForceItems;
    }

    public void setQuickForceItems(String quickForceItems) {
        this.quickForceItems = quickForceItems;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getItemId() {
        return itemId;
    }

    public void setItemId(long itemId) {
        this.itemId = itemId;
    }

    public long getChildId() {
        return childId;
    }

    public void setChildId(long childId) {
        this.childId = childId;
    }

    public int getBreakSecond() {
        return breakSecond;
    }

    public void setBreakSecond(int breakSecond) {
        this.breakSecond = breakSecond;
    }

    public boolean isPaid() {
        return isPaid;
    }

    public void setPaid(boolean paid) {
        isPaid = paid;
    }

    public String getActivityTag() {
        return activityTag;
    }

    public long getCampGroupId() {
        return campGroupId;
    }

    public int getVipFreeType() {
        return vipFreeType;
    }

    public boolean isVipFree() {
        return isVipFree;
    }

    public boolean isAlbumTimeLimited() {
        return isAlbumTimeLimited;
    }

    public long getExpireTime() {
        return expireTime;
    }

    public int getPreferredType() {
        return preferredType;
    }

    public void setPreferredType(int preferredType) {
        this.preferredType = preferredType;
    }

    public void setVipFreeType(int vipFreeType) {
        this.vipFreeType = vipFreeType;
    }

    public void setVipFree(boolean vipFree) {
        isVipFree = vipFree;
    }

    public String getAlbumSubscript() {
        return albumSubscript;
    }

    public void setAlbumSubscript(String albumSubscript) {
        this.albumSubscript = albumSubscript;
    }

    public int getBizSubType() {
        return bizSubType;
    }

    public void setBizSubType(int bizSubType) {
        this.bizSubType = bizSubType;
    }

    public String getAnchorNickName() {
        return anchorNickName;
    }

    public void setAnchorNickName(String anchorNickName) {
        this.anchorNickName = anchorNickName;
    }

    public String getAnchorAvatar() {
        return anchorAvatar;
    }

    public void setAnchorAvatar(String anchorAvatar) {
        this.anchorAvatar = anchorAvatar;
    }

    public int getLiveSubType() {
        return liveSubType;
    }

    public void setLiveSubType(int liveSubType) {
        this.liveSubType = liveSubType;
    }

    public int getTrackType() {
        return trackType;
    }

    public void setTrackType(int trackType) {
        this.trackType = trackType;
    }

    public int getDecoupleStatus() {
        return decoupleStatus;
    }

    public void setDecoupleStatus(int decoupleStatus) {
        this.decoupleStatus = decoupleStatus;
    }

    public int getModelId() {
        return modelId;
    }

    public void setModelId(int modelId) {
        this.modelId = modelId;
    }

    public void setActivityTag(String activityTag) {
        this.activityTag = activityTag;
    }

}
