package com.ximalaya.ting.android.host.model.payment;

import android.app.Activity;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenHostManager;
import com.ximalaya.ting.android.host.manager.freeunlock.PlayAutoUnlockTrackActionManager;
import com.ximalaya.ting.android.host.model.payment.behavior.BehaviorAction;
import com.ximalaya.ting.android.host.model.payment.behavior.CommonBehavior;
import com.ximalaya.ting.android.host.model.payment.commerial.FreeListenADBehaviorAction;
import com.ximalaya.ting.android.host.model.payment.commerial.PurchaseBehaviorAction;
import com.ximalaya.ting.android.host.model.payment.commerial.VipBehaviorAction;
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuItem;
import com.ximalaya.ting.android.host.model.payment.commerial.vipSku.VipSkuShelfInfo;
import com.ximalaya.ting.android.host.util.commercial.CommercialHostUtil;
import com.ximalaya.ting.android.host.util.vip.VipHostUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.track.Track;

import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

public class UniversalPayment implements Serializable {
    public static final String SOURCE_PRODUCT = "product";                              // 自制页专辑购买
    public static final String SOURCE_PRODUCT_TRACK_LIST = "productTrackList";          // 自制页声音列表
    public static final String SOURCE_PRODUCT_MIDDLE_BAR = "productMiddleBar";          // 自制页中插条
    public static final String SOURCE_ALBUM_TRACK_LIST = "albumTrackList";              // 专辑页声音列表
    public static final String SOURCE_NEW_PLAY_AFTER_SAMPLE = "newPlayAfterSample";     // 新播放页试听结束
    public static final String SOURCE_NEW_PLAY_AFTER_SAMPLE_V2 = "newPlayAfterSampleV2";// 半屏播放页试听结束
    public static final String SOURCE_PLAY_AFTER_SAMPLE = "playAfterSampleV2";          // 老播放页试听结束
    public static final String SOURCE_VIDEO_PLAY_AFTER_SAMPLE = "videoPlayAfterSample"; // 老视频播放页试听结束
    public static final String SOURCE_AFTER_SAMPLE = "afterSample";                     // 全局试听结束
    public static final String SOURCE_PLAY_BEFORE_SAMPLE = "playBeforeSample";          // 老播放页小黄条
    public static final String SOURCE_NEW_PLAY_BEFORE_SAMPLE = "newPlayBeforeSample";   // 新播放页小黄条
    public static final String SOURCE_AD_LOCKING = "adLocking";                         // 广告解锁中
    public static final String SOURCE_LIVE = "live";
    public static final String SOURCE_VIPITING = "vipiting";                            //大会员购买半浮层
    // 直播首映室
    public static final String SOURCE_WHOLE_ALBUM_PLAY_PAGE = "wholeAlbumPlayPage";                                    // 直播首映室
    public static final String SOURCE_PLAY_UNLOCK_POPUP = "playUnlockPopup"; // 畅听
    public static final String SOURCE_PRODUCT_DETAIL_VIP_HIDDEN_GUIDE_PURCHASE = "productDetailVipHiddenGuidePurchase";
    public static final String SOURCE_PLAY_PAGE_VIP_HIDDEN_GUIDE_PURCHASE = "toastPlayPageVipHiddenGuidePurchase";
    public static final String SOURCE_PLAY_PAGE_BAR = "toastPlayPageBar";
    public static final String SOURCE_PLAY_PAGE_SAMPLE = "toastPlayPageSample";

    public static final String SOURCE_LOCAL_DEFINE_TYPE = "local_defineType";           // 本地定义，不被服务端使用

    public static boolean isValidSource(String source) {
        if (VipHostUtil.enableUniversalCombineVipDialog()) {
            return true;
        }
        if (null == source) {
            return false;
        }
        if (ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.ITEM_IGNORE_COMMERCIAL_BEHAVIOR_SOURCE, true)) {
            // 配置 忽略source 是否有效匹配 开关，默认开
            return true;
        }
        return SOURCE_PRODUCT.equals(source)
                || SOURCE_PRODUCT_TRACK_LIST.equals(source)
                || SOURCE_PRODUCT_MIDDLE_BAR.equals(source)
                || SOURCE_ALBUM_TRACK_LIST.equals(source)
                || SOURCE_PLAY_AFTER_SAMPLE.equals(source)
                || SOURCE_NEW_PLAY_AFTER_SAMPLE.equals(source)
                || SOURCE_NEW_PLAY_AFTER_SAMPLE_V2.equals(source)
                || SOURCE_VIDEO_PLAY_AFTER_SAMPLE.equals(source)
                || SOURCE_AFTER_SAMPLE.equals(source)
                || SOURCE_PLAY_BEFORE_SAMPLE.equals(source)
                || SOURCE_NEW_PLAY_BEFORE_SAMPLE.equals(source)
                || SOURCE_AD_LOCKING.equals(source)
                || SOURCE_LIVE.equals(source)
                || SOURCE_WHOLE_ALBUM_PLAY_PAGE.equals(source)
                || SOURCE_VIPITING.equals(source)
                || SOURCE_LOCAL_DEFINE_TYPE.equals(source)
                || SOURCE_LOCAL_DEFINE_TYPE.equals(source)
                || SOURCE_PLAY_PAGE_VIP_HIDDEN_GUIDE_PURCHASE.equals(source)
                || SOURCE_PLAY_PAGE_BAR.equals(source)
                || SOURCE_PLAY_PAGE_SAMPLE.equals(source)
                || SOURCE_PRODUCT_DETAIL_VIP_HIDDEN_GUIDE_PURCHASE.equals(source);
    }

    @NonNull
    public static String findCurrPage(String source) {
        if (null == source) {
            return "";
        }
        switch (source) {
            case SOURCE_PRODUCT:
            case SOURCE_PRODUCT_TRACK_LIST:
            case SOURCE_PRODUCT_MIDDLE_BAR:
                return "payAlbumDetail(New)";
            case SOURCE_ALBUM_TRACK_LIST:
                return "album";
            case SOURCE_AD_LOCKING:
                return SOURCE_AD_LOCKING;
            case SOURCE_PLAY_AFTER_SAMPLE:
            case SOURCE_PLAY_BEFORE_SAMPLE:
            case SOURCE_NEW_PLAY_BEFORE_SAMPLE:
            case SOURCE_NEW_PLAY_AFTER_SAMPLE:
            case SOURCE_NEW_PLAY_AFTER_SAMPLE_V2:
            case SOURCE_VIDEO_PLAY_AFTER_SAMPLE:
            case SOURCE_AFTER_SAMPLE:
            case SOURCE_VIPITING:
                return "newPlay";
            default:
                return "newPlay";
        }
    }

    @SerializedName("title")
    public String title;
    @SerializedName("subTitle")
    public String subTitle;
    @SerializedName("afterSampleTitle")
    public String afterSampleTitle;                 // 半屏播放器试听结束标题
    @SerializedName("afterSampleSubTitle")
    public String afterSampleSubTitle;              // 半屏播放器试听结束副标题
    @SerializedName("coverPath")
    public String coverPath;
    @SerializedName("ruleLink")
    public String ruleLink;                         // 解锁规则链接
    @SerializedName("behaviors")
    public JsonArray behaviorsJson;
    @SerializedName("unlockExt")
    public String unlockExtJson;
    @SerializedName("unLockMinutes")
    public int unLockMinutes;
    @SerializedName("unLockTotalMinutes")
    public int unLockTotalMinutes;
    @SerializedName("unLockTime")
    public int unLockTime;

    @SerializedName("originalAmount")
    public String originalAmount;

    @SerializedName("purchaseAmount")
    public String purchaseAmount;

    @SerializedName("priceUnit")
    public String priceUnit;

    @SerializedName("unLockTotal")
    public int unLockTotal;

    @SerializedName("multiAccount")
    public JsonObject multiAccountJson;
    @SerializedName("isPlatinumVipStyle")
    public boolean isPlatinumVipStyle;

    @SerializedName("enhanceIpVisionInfo")
    public EnhanceIpVisionInfo enhanceIpVisionInfo; // 播放页封面强化样式信息

    @SerializedName("updateNote")
    public PlatinumUpdateNote updateNote; // 白金会员超前听更新说明

    public int localRet = 0;
    public String localMsg = null;
    public String localDialogSource;
    public boolean localIsExperimentalGroup = false;
    public final List<CommonBehavior> localBehaviors = new ArrayList<>();

    public CommonBehavior getFirstBehavior() {
        if (0 < localBehaviors.size()) {
            return localBehaviors.get(0);
        }
        return null;
    }

    public void prepare(boolean isFromDialog) {
        localIsExperimentalGroup = isExperimentalGroup();
        if (null == behaviorsJson) {
            return;
        }
        localBehaviors.clear();
        int index = 1;
        for (int i = 0; i < behaviorsJson.size(); i++) {
            JsonObject tempObject = behaviorsJson.get(i).getAsJsonObject();
            CommonBehavior behavior = CommonBehavior.parseBehavior(tempObject);
            if (null != behavior) {
                behavior.localDialogSource = localDialogSource;
                behavior.localIndexInDialog = index++;
                behavior.localDialogMainTitle = title;
                behavior.localIsExperimentalGroup = localIsExperimentalGroup;
                behavior.prepare(tempObject, this);
                localBehaviors.add(behavior);
            }
        }

        UniversalPaymentHelper.prepareBySource(isFromDialog, this);
        CommercialHostUtil.loadRnBundle();
    }

    /**
     * 查找会员强化sku的数据
     */
    @Nullable
    public VipSkuShelfInfo findVipSkuShelfInfo() {
        for (CommonBehavior commonBehavior : localBehaviors) {
            if (null == commonBehavior) {
                continue;
            }
            if (commonBehavior instanceof VipBehaviorAction) {
                VipBehaviorAction temp = (VipBehaviorAction) commonBehavior;
                if (temp.isUseSkuShelf()) {
                    return temp.shelfInfo;
                }
            }
        }
        return null;
    }

    /**
     * 查找新畅听的广告入口的数据
     *
     * @return
     */
    @Nullable
    public FreeListenADBehaviorAction findFreeListenADBehaviorAction() {
        for (CommonBehavior commonBehavior : localBehaviors) {
            if (null == commonBehavior) {
                continue;
            }
            if (commonBehavior instanceof FreeListenADBehaviorAction) {
                return (FreeListenADBehaviorAction) commonBehavior;
            }
        }
        return null;
    }

    /**
     * 查找「统一付费半浮层」的 vip sku 的数据
     * https://alidocs.dingtalk.com/i/nodes/gwva2dxOW4KpxlN0sQ6okLmj8bkz3BRL
     */
    @Nullable
    public VipSkuShelfInfo findNewStyleVipSkuShelfInfo() {
        for (CommonBehavior commonBehavior : localBehaviors) {
            if (null == commonBehavior) {
                continue;
            }
            if (commonBehavior instanceof VipBehaviorAction) {
                VipBehaviorAction temp = (VipBehaviorAction) commonBehavior;
                if (temp.isUseNewVipSkuShelf()) {
                    temp.shelfInfo.vipCategoryId = temp.vipCategoryId;
                    temp.shelfInfo.dataAnalysis = temp.dataAnalysis;
                    temp.shelfInfo.multiAccount = multiAccountJson;
                    try {
                        temp.shelfInfo.vipSpuId = Long.parseLong(temp.spuId);
                    } catch (Exception e) {
                        if (ConstantsOpenSdk.isDebug) {
                            ToastManager.showToast("spuId is not long, " + e.getMessage());
                        }
                    }
                    return temp.shelfInfo;
                }
            }
        }
        return null;
    }

    @Nullable
    public VipSkuShelfInfo findPVipSkuShelfInfo() {
        for (CommonBehavior commonBehavior : localBehaviors) {
            if (null == commonBehavior) {
                continue;
            }
            if (commonBehavior instanceof VipBehaviorAction) {
                VipBehaviorAction temp = (VipBehaviorAction) commonBehavior;
                if (temp.platinumVipShelfInfo != null && temp.platinumVipShelfInfo.isValid()) {
                    temp.platinumVipShelfInfo.vipCategoryId = temp.vipCategoryId;
                    temp.platinumVipShelfInfo.dataAnalysis = temp.dataAnalysis;
                    temp.platinumVipShelfInfo.multiAccount = multiAccountJson;
                    try {
                        temp.platinumVipShelfInfo.vipSpuId = Long.parseLong(temp.spuId);
                    } catch (Exception e) {
                        if (ConstantsOpenSdk.isDebug) {
                            ToastManager.showToast("spuId is not long, " + e.getMessage());
                        }
                    }
                    return temp.platinumVipShelfInfo;
                }
            }
        }
        return null;
    }

    /**
     * 畅听 实验组
     */
    private boolean isExperimentalGroup() {
        if (null == unlockExtJson) {
            return false;
        }
        try {
            JSONObject jsonObject = new JSONObject(unlockExtJson);
            return jsonObject.optBoolean("isExpV3", false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isEmptyAction() {
        return 0 >= localBehaviors.size();
    }

    /**
     * 在 专辑页 和 自制页 时，如果只返回了一直商业化（commercial）的按钮，则直接执行，不显示弹窗
     */
    public CommonBehavior isDirectProcessMode(String source) {
        if (SOURCE_PRODUCT.equals(source)
                || SOURCE_PRODUCT_TRACK_LIST.equals(source)
                || SOURCE_ALBUM_TRACK_LIST.equals(source)
                || SOURCE_PRODUCT_MIDDLE_BAR.equals(source)
                || SOURCE_PLAY_BEFORE_SAMPLE.equals(source)
                || SOURCE_WHOLE_ALBUM_PLAY_PAGE.equals(source)
                || SOURCE_NEW_PLAY_BEFORE_SAMPLE.equals(source)
                || SOURCE_PRODUCT_DETAIL_VIP_HIDDEN_GUIDE_PURCHASE.equals(source)
        ) {
            if (1 == localBehaviors.size()) {
                CommonBehavior commonBehavior = localBehaviors.get(0);
                if (null == commonBehavior) {
                    return null;
                }
                if (commonBehavior instanceof VipBehaviorAction) {
                    if (null == commonBehavior.action || (((VipBehaviorAction) commonBehavior).isNewVipStyle && ((VipBehaviorAction) commonBehavior).isSkuSimplify)) {
                        return null;
                    }
                    return commonBehavior;
                }
                if (commonBehavior instanceof PurchaseBehaviorAction) {
                    if (null == commonBehavior.action) {
                        return null;
                    }
                    return commonBehavior;
                }
            }
        }
        return null;
    }

    /**
     * 可能 自动执行 的行为
     */
    public CommonBehavior isPossibleAutoEngage(String source, @Nullable Track track) {
        PlayAutoUnlockTrackActionManager.debugLog("isPossibleAutoEngage source: " + source);
        if (!localIsExperimentalGroup
                && FreeListenHostManager.getInstance().canUnlockWithTrack()) {
            return null;
        }
        CommonBehavior behavior = getFirstBehavior();
        if (null == behavior || null == track) {
            return null;
        }
        return UniversalPaymentHelper.isPossibleAutoEngage(source, track, behavior);
    }

    public boolean isEnhanceIpVisionInfoValid() {
        return enhanceIpVisionInfo != null && enhanceIpVisionInfo.isValid();
    }

    public static class EnhanceIpVisionInfo {
        @SerializedName("dynamicImage")
        public String dynamicImage;
        @SerializedName("staticImage")
        public String staticImage;
        @SerializedName("scheme")
        public String scheme;

        public boolean isValid() {
            return !TextUtils.isEmpty(staticImage) && !TextUtils.isEmpty(scheme);
        }
    }

    public static class PaymentMaterial {
        private final BehaviorAction.IActionOuterImp actionOuterImp;
        public Track track;
        public long albumId;
        public HashMap<String, String> transmissionParams;
        public VipSkuItem vipSkuItem;

        public PaymentMaterial(BehaviorAction.IActionOuterImp actionOuterImp) {
            this.actionOuterImp = actionOuterImp;
        }

        public BehaviorAction.IActionOuterImp getActionOuterImp() {
            return actionOuterImp;
        }
    }

    public static class Util {
        /**
         * @return 返回进入Rn页面需要的Bundle
         */
        private static Bundle isEnterRnPayment(String iTingString) {
            if (null == iTingString) {
                return null;
            }
            Uri uri = Uri.parse(iTingString);
            if (null == uri) {
                return null;
            }
            try {
                if (!"iting".equalsIgnoreCase(uri.getScheme())) {
                    return null;
                }
                String msgType = uri.getQueryParameter("msg_type");
                if (94 != Integer.parseInt(msgType)) {
                    return null;
                }
                String bundleName = uri.getQueryParameter("bundle");
                if (!"commonpayment".equals(bundleName)) {
                    return null;
                }
                Bundle bundle = new Bundle();
                Set<String> keys = uri.getQueryParameterNames();
                for (String key : keys) {
                    if (null == key) {
                        continue;
                    }
                    String value = uri.getQueryParameter(key);
                    if (null == value) {
                        continue;
                    }
                    bundle.putString(key, value);
                }
                return bundle;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }

        public static boolean enterRnWithCallBack(BaseFragment2 fragment, String iting, IFragmentFinish fragmentFinishCallBack) {
            Bundle args = null;
            if (null == (args = isEnterRnPayment(iting))) {
                return false;
            }

            CommercialHostUtil.SettlementDialogMaterial material = new CommercialHostUtil.SettlementDialogMaterial(fragment, args);
            material.fragmentFinishCallBack = fragmentFinishCallBack;
            if (CommercialHostUtil.openSettlementFloatingDialog(material)) {
                // 被半浮层形式的Rn页面消费，不再使用全屏展示
                return true;
            }

            try {
                BaseFragment cashierFragment = Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).getFragmentAction()
                        .newRNFragment("rn", args);
                if (cashierFragment instanceof BaseFragment2) {
                    ((BaseFragment2) cashierFragment).setCallbackFinish(fragmentFinishCallBack);
                }
                if (null != cashierFragment) {
                    if (null != fragment) {
                        fragment.startFragment(cashierFragment);
                        return true;
                    } else {
                        Activity activity = BaseApplication.getMainActivity();
                        if (activity instanceof MainActivity) {
                            ((MainActivity) activity).startFragment(cashierFragment);
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            return false;
        }
    }

    public static class PlatinumUpdateNote {
        @SerializedName("text")
        public String text;
        @SerializedName("url")
        public String url;

        public boolean isValid() {
            return !TextUtils.isEmpty(text) && !TextUtils.isEmpty(url);
        }
    }
}
