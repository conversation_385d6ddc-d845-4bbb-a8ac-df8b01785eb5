package com.ximalaya.ting.android.host.model.play;

import com.google.gson.annotations.SerializedName;

public class PlayTimeLimitFreeInfo {
    public static final String TRACK_TAG_TIME_LIMIT_FREE_FINISH = "TRACK_TAG_TIME_LIMIT_FREE_FINISH";
    @SerializedName("showPrice")
    public boolean showPrice;//是否显示价格
    @SerializedName("guidance")
    public String guidance;//引导文案
    @SerializedName("description")
    public String description;//说明文案
    @SerializedName("buttonText")
    public String buttonText;
    @SerializedName("buttonActionId")
    public int buttonActionId;//1：购买专辑；2：跳转链接
    @SerializedName("buttonActionUrl")
    public String buttonActionUrl;
    @SerializedName("playFinishedVoiceUrl")
    public String playFinishedVoiceUrl;//播放结束声音url
    @SerializedName("playFinishedTip")
    public String playFinishedTip;//播放结束声音提示文案
    @SerializedName("remainTime")
    public long remainTime;//剩余时间(毫秒)
}
