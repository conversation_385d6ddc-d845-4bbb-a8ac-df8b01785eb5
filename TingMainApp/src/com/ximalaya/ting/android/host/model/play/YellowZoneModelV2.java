package com.ximalaya.ting.android.host.model.play;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.model.BaseMotBarModel;

/**
 * <AUTHOR>
 * @time 2024/10/24 16:27
 * @description: 加高播放页mot下挂条-uve小黄条新数据模型，具体type类型参考{@link YellowZoneModel}
 **/
public class YellowZoneModelV2 extends BaseMotBarModel {

   @Override
    public boolean isValid() {
        if(templateId == 1 || templateId == 3 || templateId == 6) {
            boolean flag = !TextUtils.isEmpty(text);
            if (flag && buttonList != null && buttonList.size() > 0) {
                ButtonInfo button = buttonList.get(0);
                flag = isButtonValid(button);
            }
            return flag;
        }
        return false;
    }

    @Override
    public boolean isButtonValid(BaseMotBarModel.ButtonInfo buttonInfo) {
        if(buttonInfo == null){
            return false;
        }
        if (TextUtils.isEmpty(buttonInfo.text)) {
            return false;
        }
        return buttonInfo.actionId == -1
                || buttonInfo.actionId == BaseMotBarModel.ButtonInfo.ACTION_ID_PURCHASE
                || buttonInfo.actionId == BaseMotBarModel.ButtonInfo.ACTION_ID_JUMP_URL
                || buttonInfo.actionId == BaseMotBarModel.ButtonInfo.ACTION_ID_UNIVERSAL_PAYMENT
                || buttonInfo.actionId == BaseMotBarModel.ButtonInfo.ACTION_ID_BATCH_TRACK_DOWNLOAD
                || buttonInfo.actionId == BaseMotBarModel.ButtonInfo.ACTION_ID_BATCH_TRACK_BUY;
    }
    public int getTextColor() {
        Style temp = style != null ? style : Style.EMPTY_STYLE;
        return temp.getLocalTextColor();
    }

    public int getBackgroundColor() {
        Style temp = style != null ? style : Style.EMPTY_STYLE;
        return temp.getLocalBackgroundColor();
    }

    public int getButtonTextColor() {
        Style temp = style != null ? style : Style.EMPTY_STYLE;
        return temp.getLocalButtonTextColor();
    }

    public int getButtonBackgroundColor() {
        Style temp = style != null ? style : Style.EMPTY_STYLE;
        return temp.getLocalButtonBackgroundColor();
    }
}