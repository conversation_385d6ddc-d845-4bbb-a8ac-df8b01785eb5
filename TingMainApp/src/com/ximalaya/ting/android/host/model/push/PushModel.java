/**
 * PushModel.java
 * com.ximalaya.ting.android.host.model.push
 * <p>
 * Function： TODO
 * <p>
 * ver     date      		author
 * ──────────────────────────────────
 * 2016-1-5 		jack.qin
 * <p>
 * Copyright (c) 2016, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.host.model.push;

import androidx.annotation.Nullable;

import java.util.HashMap;

/**
 * ClassName:PushModel Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 *
 * <AUTHOR>
 * @Date 2016-1-5 上午11:04:46
 * @see
 * @since Ver 1.1
 */
public class PushModel {

    public static final String TYPE_GROUP_RANK = "cluster";
    public static final String PAGE_STACK_TAG = "pageStackTag";
    /**
     * 消息ID
     */
    public long bid;
    public int nType = 0;// 推送类型，1=广播，2=单播，3=组播
    /**
     * 16 = 新增粉丝 15 = 收到评论 10 = 收到私信 17 = 收到新鲜事
     */
    public int messageType;// ":15
    public String title;// 标题
    public String intro; // 简介
    public String message;// 消息内容
    public String url;// ":"http://m.ximalaya.com",
    public long trackId;// 声音的id，messgeType=11
    public long albumId;// 专辑的id,messageType=13
    public long uid;// 用户id,messageType=12
    public boolean videoPrior;
    public long activityId;
    public long zoneId;// 圈子id
    public String activityName;
    public int expirySenconds;//过期时长，秒
    public String msgId;
    public String key;//榜单关键字
    public int rankingListId;//榜单id
    public long clusterId;//聚合排行榜id
    public long scene_id;//排行榜场景id
    public String screenShotPath;  //截图图片路径
    public long pushId;
    public boolean keepPage;//是否禁用跳转时切换Tab
    /**
     * For message type 32,搜索词
     */
    public String term;
    public String keyword;

    public String listenTab;
    public boolean updatedRec;
    public String listenSecTab;

    public String mRecSrc;
    public String mRecTrack;
    public String mRecContent;
    public String ubtTraceId;
    public String source;
    public String type;

    public String originalSource;

    /**
     * 直播页面跳转时区分来源，参见 IBusinessIdConstants
     */
    public int appId;

    //分类详情页使用的
    public int categoryId;
    public String categoryTitle;
    public String tagName;
    public boolean preview;

    public long tingListId;
    public int contentType;
    public long liveId;
    public int liveType;
    public int bizType;
    //跳转到直播间后是否禁止左右滑
    public boolean disableSlide;
    //跳转到直播间后是否展示返回上一直播间按钮
    public boolean showBack;
    //跳转到直播间后延迟打开其他页面
    public long delay;

    public String liveStreamUrl;//直播流地址
    public int liveMediaType;//1 音频直播 2 视频直播
    public String paidLiveStreamUrl; //加密的直播拉流地址

    public String screenSize; //当前直播分配率


    // 本地听直达城市
    public String cityCode;
    public String cityName;

    //for message 26 打开客服页面
    public String toUid;

    //是否来自通知栏推送
    public boolean isPush;
    public boolean keepWeb;
    public double amount;
    public long sendTime;//发送推送的时间，毫秒
    public String pushUrl;

    public int startTime;

    //全面朗读
    public String actionId;
    public String classId;
    public String from_page;
    public String srcUrl; // 来源
    public String tags;
    public double duration; // 时长,s
    // 房间id（个播直播，娱乐派对，UGC聊天室）
    public long liveRoomId;
    //官方直播间场次
    public long officialUid;
    //是否是官方直播间来源
    public boolean isOfficialSource;
    public boolean autoChange;
    public int liveStatus;
    public int open_type;
    public int adPosition = -1; //服务端配置从0开始
    //群组使用 群组群主的uid
    public long groupMasterUid;
    public long groupId; //群id

    public long groupInviteId;//群组的邀请id

    //圈子
    public long communityId;
    public long sectionId;
    public long articleId;
    public int auto_join;
    public long popId;
    public String tabId;
    public String tab_id;// 录音页默认加载标签页id复用该字段
    public long idolId;
    public String selTabId; //糖葫芦跳转落地页用
    public String hotTopIds;//圈子热门有单圈卡片时需要的参数
    public String userType;  // userType=anchor，跳转到创作侧；否则就是消费侧
    public String pageType; // pageType=new，跳转新页面，否则老页面

    //圈子话题日历
    public String date;
    //提问
    public long questionId;

    public String redeemCode;//站内兑换码
    public HashMap<String, String> xdcsParams;
    public String schema;

    public int vipTabType;   // 0 有大会员，1 有小会员，2 大小会员都有

    public boolean autoRead;

    public String prefID;
    public String status;
    public int productType;//充值：productType==1为喜钻充值，默认为喜点充值

    public int radioId;
    public boolean openDoc;

    public String recSrc;  //推荐资源
    public String recTrack;  //推荐算法

    public String deliveryChannelid; // 投放渠道的部分场景的归因参数

    public String tag;  //子频道的 url 编码
    public boolean openRich;
    public long channelGroupId;   //父频道 id
    public long channelId;   //子频道 id
    public long channelDirectLandId; // 一键听直接进卡片页的标识
    public long metadataValueId;   //标签 id
    public String liveCommonId;   //复用子频道要传的 id
    public long templateCategoryId;   //支持新版频道分类筛选用

    public String api; //广播列表请求url
    public int radioCategoryId;//广播分类iD
    public long adAlbumId; // 广告跳转声音的时候传递的album 字段

    public long sessionId;
    public String opName;
    public String opGroup;
    public long created;
    public long processTime;

    public String locatedTab;
    public String deviceName;

    /**
     * 一键听
     */
    public long listenCategoryId; // 一键听频道id
    public long listenChannelId;

    /**
     * 听头条
     */
    public long headLineTabId; // 听头条指定tabId
    public long headLineTrackId; // 听头条指定声音id

    //微课课程ID
    public long weikeCourseId;
    public int weikeCourseType;
    public String weikeEntertrance;
    public int weikeCategoryId;
    public String weikeCategoryName;
    public double weikeCouponPrice;
    public long weikeCouponId;
    public long weikeHostId;
    public long weikeOpenDiscussId;

    //配音秀图片选择
    public int dubTopicId;
    public String dubTopicName;
    public String dubTypeFrom;
    // 有声漫画
    public String chapterName;
    public long templateId;
    public int sourceType;
    public int pageId;
    public String trackList;
    public int limit;
    public int topicId;
    public String topicName;
    public String moduleId;
    public int reqType;

    public boolean hideRelated;

    //合作配音秀
    public int videoId;

    //主播列表页，复用type，title字段，category可解析为category_id或category_name
    public String category;
    public String channel;

    public int keywordId;

    public boolean isDisplay = true;    // 是否去欢迎页
    public boolean channelJumpOver = false;    // 不请求首次播放时的广告
    public boolean inWebActivity; //在WebActivity中启动

    //打开有特定内容的趣配音素材列表
    public int order_by;
    public String tag_id;
    public String type_id;
    public String metadataStr;

    //动态-》话题id (topicId)
    public long feedTopicId;

    // 新的挑战话题id
    public long pkTopicId;

    // scene 场景一键听
    public int scene;

    // 一键听频道id
    public long onekeyListenChannelId;
    // 今日热点频道id
    public long toNewsId;
    // 一键听 播放的trackId
    public long onekeyPlayTrackId;
    // 新闻频道推送支持多条声音
    public String onekeyPlayTrackIds;
    // 今日热点是否跳转到文稿页
    public boolean toDetailInfo;
    // 一键听 是否重置听头条数据
    public boolean resetHeadlineTracks;
    // 是否重置一键听频道
    public boolean resetOneKeyTracks;
    public boolean fromDesktop;
    public boolean fromWeather;
    public int oneKeyNormalExtra; // 1:强制自动开播热点 2:强制不开播热点
    public int channelType; // 用于区分今日热点和私人FM页面
    public String dailyNewsContentId; // 专题id

    // 播客排行榜topId，默认跳转到指定page
    public int topId;

    //评论详情
    public long feedId;
    public long rootCommentId;

    // src 素材广场来源；录音页来源标识复用
    public String src;

    public String metadatas;
    public String calcDimension;

    //录音承接话题/活动
    public String topic;
    public String activity;

    /**
     * 创建动态时默认透传内容
     */
    public String text;
    public String localPicPath;

    // 登录时直接选择登录哪个渠道(qq,微信)  qq,weixin,weibo,xiaomi,meizu,huawei
    public String openChannel;

    public int fold; // 折叠或展开

    //未成年人保护模式增加来源字段
    public int from;

    // 播放来源
    public int playSource;

    public int squareOperationId; // 方块运营位id

    public int voice_tab_id;
    public String albumActivityParams;//专辑活动标识
    public String fromLiveParams;     // 从直播过来的source相关信息

    public boolean autoPlay;
    public boolean isAutoPlayForce; // 开播专辑 可以不传trackId 直接开播历史或列表最上面的声音
    public boolean showSharePopup;
    public String gift_key; // 礼包key
    public String self_share_from; // 买书客户标识
    public String book_price; // 书本价格
    public String bookAlbumCover;

    public boolean playFirst;
    public boolean openGlobal;

    public long commentid;

    public int opType;

    public long refundId;

    /**
     * 直播首页分类id
     */
    public int segmentId;

    public boolean onlyShowPlay;

    public int dailyId;

    public boolean toHome;

    public long pkId;
    public int pkMode;
    //匿名直播间允许通过主播榜打开主播资料卡，值取 1
    public int forceShowUserCard;
    public long anchorId;

    public String uploadId;
    public long shootVideoId;

    public long bookId;
    public boolean isLatestChapter;
    public String showSwitch;
    public long chapterId;
    public long textLoc;
    public long textLen;
    public int code;
    public String chartId;
    public String genderId;
    public String genderName;
    public String moduleTitle;
    public String kw;
    public String paramsFilePath;

    // 听更新-推送声音（1-3条，以逗号分割）
    public String trackIds;
    // 听更新-时间戳
    public long timeline;


    //直播语音房自定义弹窗-------start-------
    public String liveCustomerPosition;
    public String buttonAnim;
    public boolean noClear;
    public int liveCustomerWidth;
    public int liveCustomerHeigh;
    public String liveCustomerAnimationFrom;
    public int liveCustomerCorner;
    public int liveCustomerTransparent;
    public int liveCustomerRealTransparent;
    public String liveCustomerExtraUrl;
    public int liveCustomerShowClose;
    public int liveCustomerHideGiftShow;
    public int bottomHeightPercent;
    public int liveCustomerRestoreAfterJump;
    public String liveCustomerShowOnBelow;

    //直播语音房自定义弹窗-------end-------

    //消息中心评论id
    public String commentId;

    //是否直接定位到全部tab  1：需要 其他为不需要
    public int needToAll;

    /**
     * 跳转到直播礼物面板所需参数
     */
    public long anchorUid;
    public long chatId;

    public long giftId;

    public int showPopup;

    //背包物品id
    public long packageId;
    //背包物品过期时间
    public long expireAt;

    public long userId;

    // 专辑页标签
    public String tabName;

    public int selectSortRuleId;
    public String sortRuleGroup;
    public boolean notOpenIfFragmentExist;

    public int selectedTabId;
    // Pia 戏剧本 id
    public int dramaId;

    // 专辑评价
    public boolean isPaid;

    // 跳转首页，切换指定tab
    public String tab;

    // 亲子平台绑定时需要家长的信息
    public String deviceId;
    public String name;
    public long timestamp;

    // pageStackTag
    public String pageStackTag;

    public long videoLiveId;//视频直播的场次id
    public int videoBuzId;//视频直播的业务id
    public long videoLiveAlbumId;//专辑/训练营ID

    public int liveCourseCategoryId;//当前课程直播配置在所属分类下的分类id
    public int liveCourseLiveId;//直播分类id列表对应的配置id

    /**
     * 广告点击标识 clickId
     */
    public String cid = "";
    /**
     * 广告投放透传给直播间落地页数据
     */
    public String adPayload;
    /**
     * 直播分类类型(包括一级分类和聚合分类)
     */
    public int liveCategoryType;

    public int audioType;

    // iting ab 独有字段， defaultUrl 如果网络请求失败，则走默认的url

    public String defaultUrl;

    //驾驶模式 来源
    public String driveModeFrom;

    //直播娱乐厅-》播客中挂件弹窗点击弹窗的特殊弹窗
    public String extraUrl;
    public int podcastDialogWidth;
    public int podcastDialogHeigh;

    // 跳转新版播放页面指定tab（如时刻文稿）
    public int focusPlayTabType;
    public String targetAlbums; // 站外分享的专辑id，例如: 1228872,1228787,1228784
    public long targetUid; // 站外分享的用户uid
    public boolean isOpenComment;//打开评论框
    public long taskId;//积分任务id

    // 要跳转诸如会员福利页的url
    public String vipExtraUrl;

    // 控制H5弹窗
    public String operation;
    public String targetDialog;
    //目标地址  target
    public String target;
    //Nano类型 0普通版  1便捷版
    public int nanoType;
    // tws 耳机
    public String twsId;
    //---------------一起听相关start------------------
    public int themeId;// 一起听场馆id
    public int hotwordsId;
    public int subThemeId;
    public String animated;
    public long sourceUid;
    public boolean isMixTrack; // 是否是白噪音
    public boolean hasCollect; // 是否有收藏的声音
    public String trackName; // 白噪音名称
    public String trackGroupName; // 白噪音组合名称
    public String trackVolumes;//白噪音声音
    public int sourceFrom;
    public long masteruid;
    public long defaultRoomId;
    //---------------一起听相关end------------------
    // 文稿点击时间戳链接带的时间格式 例：[00:00:00]
    public String seekTimeStr;

    // 评论活动类型盖楼、抽奖、中奖公告
    public int commentActivityId;
    // 评论活动id
    public int commentActivityType;

    // 游戏中心相关上报数据
    public String positionName;
    public String gameId;
    public String gameResource;
    public String adType;
    public String gameTip;
    public String gameSign;

    //语音唤醒指令解析
    public int subType;
    public String value;
    public boolean isTTSPlaying;
    public String localPeriph;
    public String localDialogId;

    // 豹趣小游戏token
    public String gameToken;
    public String gameUid;

    public String fromPage;

    /**
     *
     **/
    public int roomMode;

    /**
     * 广告透传字段, responseid 和 物料ID
     */
    public long responseId;
    public long materialId;

    public boolean toSpacePoint;

    public int sharePoint;
    public String pShareC;
    public String id;
    //msg_type=62跳至我的作品页
    public int recordTab;
    public int recordStatusFilter;
    public String moduleIds;

    public String myListenFrom;
    //音频云剪辑导入的project
    public String prj_id;

    //打赏（喜点）
    public int srcType;
    public long targetId;
    public String data;
    public int badge;  // 桌面小红点个数
    public int directLanding;  // 0:否   1:是 表示是冷启动推送优化方案ab标志

    //通联
    public String mgm_callback_url;

    public boolean isHybridFull;


    /**
     * iting://open?msg_type=330&type=2&seqId=XXX（type=1按热度排序，type=2 去后台运营配置的广告池子，seqId随序号增大而增大）
     */
    public String seqId;

    //Myclub
    public long scheduleId; //日程id
    public int roleType;
    public String shareUrl;
    public boolean darkMode;
    // 录音上传后显示h5活动
    public String activity_code;

    //首页已显示的糖葫芦id
    public String calabashId;

    public long moreIndexId;

    // 装扮中心类型
    public int decorateType;

    public String resPosition; //核心资源位

    public boolean highlightedComment;   // 高亮显示评论
    //听单聚合页
    public int scenceId;

    //听单聚合页
    public long tingListGroupId = -1;

    // 填写地址后返回的id
    public long userContactId;

    // 动态抽奖id
    public long lotteryId;
    // ai文稿主播侧专辑
    public String albumName;

    public int payResult;

    public double payAmount;
    // 新用户榜单ab
    public int newUserOperationAb;

    public String liveRadio;

    public String content;
    public String receive;
    public long thirdPartyId;
    public long toThirdpartyId;
    public int trackDuration;
    // 直播-背包物品id
    public int itemId;

    public long talkId;
    public long voteId;
    public boolean showKeyboard;
    public boolean singleRank;
    public long recordId;
    public long startPlayTrackId;
    public boolean purchased;
    public int ximi;

    // 分享解锁声音
    public String share_unlock_nickname;
    public String share_unlock_avatar;
    public String share_unlock_activityId;
    public String share_unlock_key;

    // 互动卡片
    @Nullable public String cardType;
    public long cardId;

    // alarm
    public int alarmFrom;

    public long toAlbumId;

    public String requestId;

    public boolean openMonthlyTicketDialog;
    public int initialMonthlyTicketDialogTab;
    public int channel_key;
    public String channel_title;
    // 是否展示儿童退出引导，1=展示 0=不展示
    public int isShowExitAlert;

    // 业务类型
    public long businessType;
    public String bundle;
    public boolean openPlayList;
    public long sceneId;
    public String sourceGuidance;
    public int uiType;
    public boolean fromToggle;
    public String growthContentId; // 增长投放的内容类型及id

    // 角色
    public long characterId;
    public long inviteUid;

    //召回用户相关
    public String playCardPageStyle;    //"new"
    public int firstTrackPlayPoint;

    public boolean isFromNewIntent;

    //大师课会员购买相关
    public String utmsource;
    public String orderUpType;
    public String orderUpValue;
    public String orderSubType;
    public String orderSubValue;
    public String pcreditNumInterest;
    public String sceneType;

    public String speechXmChannel;

    public int jumpType;
    public int isVideoPage;
    public String coverUrl;
    public String ids;

    public boolean isOuterIting; // 本地设置的，不是从iting参数里来

    public long contentId;

    public String medalId; // 勋章id
    public String medalFrom; // 勋章来源
    public String medalTheme;

    public String userName;

    public String marks; // 剪辑声音标记 例4.0,7.0
    public String fromSource;

    public long poolId; // 指定内容池 id
    public boolean poolRandom; // 内容池取数是否随机

    public int fromPool;
    public String hint;
    public String albumCover;
    public String albumTitle;

    public long toTrackId;
    public long toChannelId;
    public int openStatus;
    public String loginCode;
    public String loginScheme;
    public String prefixContentIds;
    public int paidPageCode;
    public String pgcAlbumPageRouteResult;
    public boolean notShowPlayPage;
    public String playListMode;

    public String secondaryType;
    public int emotionType;

    public String clickPlayIting;
    public boolean openComment;
    public boolean recDefaultFlag;

    public int sync;
    // ai经纪人阴阳标题
    public String titleToServer;
    public String inputTxt;
    public String focusTab;

    public long previewId;
    public long showStartTime;
    public int pageFromSource;

    public long metaValueId;
    public String metaTitle;
    public int sortType;
    public String albumIds;
    public boolean scrollToCategory;
    public boolean openElderlyMode;
    public String elderlyAction;
    public int specialPosition;
    public String outsideSourceParam;
    public int rewardTime; // 奖励时长，单位：分钟
    // 录音页参数
    public long ai_content_id;
    public String ai_content_title;
    public String sign;
    public long purchaseUserId;
    public String tradeOrderNo;
    public boolean keepPlayFra;
    public boolean noShowRepeat;

    // directExit=1时，首页点击热词搜索后，搜索结果页点取消直接返回到首页
    public int directExit;

    public int reservationId;

    public boolean isIgnoreShowPlayPage;

    public boolean autoReserve;
    public boolean toReserve;
    public long productId;
    public String currPage;
    public String newLanding;
    public String forceItems;
    public String jump;
}
