package com.ximalaya.ting.android.host.model.quicklisten

import android.text.TextUtils
import androidx.core.util.Pair
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2025/7/4 14:59
 */
const val ELEMENT_TYPE_RECOMMEND = "Recommend"
const val ELEMENT_TYPE_NEW_USER = "Video"

data class QuickListenTab(val title: String, val id: Long, val elementType: String? = null)

fun parseTabs(content: String): Pair<List<QuickListenTab>, String>? {
    runCatching {
        val jsonObject = JSONObject(content)
        if (jsonObject.optInt("ret", -1) == 0) {
            val jsonArr = jsonObject.optJSONArray("data")
            if (jsonArr != null && jsonArr.length() > 0) {
                val list: MutableList<QuickListenTab> = ArrayList()
                for (i in 0 until jsonArr.length()) {
                    val item = jsonArr.optJSONObject(i)
                    if (item != null) {
                        val id = item.optLong("id")
                        val title = item.optString("title")
                        val elementType = item.optString("elementType")
                        if (id > 0 && !TextUtils.isEmpty(title)) {
                            list.add(QuickListenTab(title, id, elementType))
                        }
                    }
                }
                return Pair<List<QuickListenTab>, String>(list, content)
            } else {
                return null
            }
        } else {
            return null
        }
    }.onFailure { it.printStackTrace() }
    return null
}