package com.ximalaya.ting.android.host.model.recommend

import androidx.annotation.Keep
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2

@Keep
data class Ext(
    val searchCover: String?,
    val whiteCover: String?,
    val style: String?,
    var textColor: String? = null, // 服务端返回的字体颜色
    var darkTextColor: String? = null, // 服务端返回的夜间字体颜色
) {
    fun isBlueStyle(): Boolean {
        return style == "blue"
    }

    fun isCustomStyle(): Boolean {
        return style == "custom"
    }

    fun getRealTextColor(): String? {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            darkTextColor
        } else {
            textColor
        }
    }

}
