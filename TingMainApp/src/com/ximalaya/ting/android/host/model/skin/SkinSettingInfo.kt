package com.ximalaya.ting.android.host.model.skin

import com.google.gson.annotations.SerializedName
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.host.manager.NovelTabAbManager
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager
import com.ximalaya.ting.android.main.util.MyListenAbUtil

/**
 * Created by <PERSON><PERSON><PERSON> on 2020-08-18.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
class SkinSettingInfo {

    @SerializedName("appBottomIconInfos")
    var bottomIconInfos : List<SkinBottomIconInfo?>? = null

    @SerializedName("appBackgroundInfo")
    var backgroundInfo : SkinAllBackgroundInfo? = null

    @SerializedName("atmosphereInfo")
    var atmosphereInfo: AtmosphereInfo? = null

    @SerializedName("bottomAtmospheres")
    var bottomAtmospheres: List<SkinBottomIconInfo?>? = null

    @SerializedName("exist")
    var exist : Boolean = false

    var json : String? = null
}

class AtmosphereInfo {
    var configName: String = ""
    var configType: Int = 0
    var isExist: Boolean = false
    var isSetTopBackground: Boolean = false
    var topBackgroundUrl: String = ""
    var materialPicUrl: String = ""
    var darkTopBackgroundUrl: String = ""
    var darkMaterialPicUrl: String = ""
    var topTabTitleColor: Int = 0 // 顶tab标题字色 1 - 恒定黑色； 2 - 恒定白色；
    var xmRequestId : String? = null

    @SerializedName("deviceConfigs")
    var deviceConfigs: List<DeviceConfigs?>? = null

    override fun equals(other: Any?): Boolean {
        if (other !is AtmosphereInfo) {
            return false
        }
        return this.configType == other.configType
                && (this.isExist && other.isExist)
                && (this.isSetTopBackground && other.isSetTopBackground )
                && this.topBackgroundUrl?.equals(other.topBackgroundUrl) == true
                && this.materialPicUrl?.equals(other.materialPicUrl) == true
                && this.darkTopBackgroundUrl?.equals(other.darkTopBackgroundUrl) == true
                && this.darkMaterialPicUrl?.equals(other.darkMaterialPicUrl) == true
                && this.topTabTitleColor?.equals(other.topTabTitleColor) == true
    }
}

class DeviceConfigs(
    var appType: Int, // 1: android,  2: ios
    var searchBoxColor: String? = null,
    var darkSearchBoxColor: String? = null,
    var transparency: String? = null,
    var darkTransparency: String? = null
)

enum class AtmosphereType(var num: Int, name: String) {
    normalSacrifice(1, "常规祭奠"),
    advertise(2, "宣传氛围"),
    festival(3, "节日氛围"),
    greatSacrifice(4, "重大祭奠");
}

class SkinBottomIconInfo {
    @SerializedName(value = "appBottomIcon", alternate = ["appBottomAtmosphere"])
    var bottomIcon : SkinBottomIcon? = null

    @SerializedName("type")
    var type : Long = 0

    val tabType : Int
        get() {
            if (NovelTabAbManager.showCategoryV2Tab()) {
                return when (type) {
                    11L -> TabFragmentManager.TAB_HOME_PAGE
//                13L -> TabFragmentManager.TAB_FINDING // 实验已全量
                    16L -> TabFragmentManager.TAB_MY_AND_LISTEN
                    // 20 分类
                    20L -> TabFragmentManager.TAB_VIP
                    // 19 订阅
                    19L -> TabFragmentManager.TAB_FINDING
                    else -> 0
                }
            } else if (NovelTabAbManager.showNovelTab()) {
                return when (type) {
                    11L -> TabFragmentManager.TAB_HOME_PAGE
//                13L -> TabFragmentManager.TAB_FINDING // 实验已全量
                    16L -> TabFragmentManager.TAB_MY_AND_LISTEN
                    // 18 免费听
                    18L -> TabFragmentManager.TAB_VIP
                    // 19 订阅
                    19L -> TabFragmentManager.TAB_FINDING
                    20L -> TabFragmentManager.TAB_CATEGORY
                    else -> 0
                }
            } else {
                return when (type) {
                    11L -> TabFragmentManager.TAB_HOME_PAGE
//                13L -> TabFragmentManager.TAB_FINDING // 实验已全量
                    // 15 vip
                    15L -> TabFragmentManager.TAB_VIP // vip底tab原来就不在这里处理
                    16L -> TabFragmentManager.TAB_MY_AND_LISTEN
                    // 19 订阅
                    19L -> TabFragmentManager.TAB_FINDING
                    20L -> TabFragmentManager.TAB_CATEGORY
                    else -> 0
                }
            }
        }
}

class SkinAllBackgroundInfo {
    @SerializedName("appHeadBackgroundInfo")
    var headBackgroundInfo : SkinBackgroundInfo?= null
    @SerializedName("appBottomBackgroundInfo")
    var bottomBackgroundInfo : SkinBackgroundInfo?= null
}

class SkinBackgroundInfo {
    var hasColor : Boolean = false
    var hasBackgroundPic : Boolean = false
    var color : String? = null
    var backgroundPic : String? = null
}

class SkinBottomIcon {
    @SerializedName("checkedUrl")
    var checkedUrl : String? = null
    get() {
        return if (BaseFragmentActivity.sIsDarkMode && !darkCheckedUrl.isNullOrBlank()) {
            darkCheckedUrl
        } else {
            field
        }
    }

    fun getCheckUrlForce(forceDark: Boolean): String? {
        if (forceDark) {
            return darkCheckedUrl
        }
        return checkedUrl
    }

    @SerializedName("unCheckedUrl")
    var unCheckedUrl: String? = null
        get() {
            return if (BaseFragmentActivity.sIsDarkMode && !darkUnCheckedUrl.isNullOrBlank()) {
                darkUnCheckedUrl
            } else {
                field
            }
        }

    fun getUnCheckUrlForce(forceDark: Boolean): String? {
        if (forceDark) {
            return darkUnCheckedUrl
        }
        return unCheckedUrl
    }

    @SerializedName("darkCheckedUrl")
    var darkCheckedUrl : String? = null

    @SerializedName("darkUnCheckedUrl")
    var darkUnCheckedUrl: String? = null

    @SerializedName("charactersCheckedColor")
    var charactersCheckedColor : String? = null

    @SerializedName("charactersUnCheckedColor")
    var charactersUnCheckedColor: String? = null

    @SerializedName("darkCharactersCheckedColor")
    var darkCharactersCheckedColor: String? = null
    @SerializedName("darkCharactersUnCheckedColor")
    var darkCharactersUnCheckedColor:String? = null

    override fun equals(other: Any?): Boolean {
        if (other !is SkinBottomIcon) {
            return false
        }
        return this.checkedUrl.equals(other.checkedUrl) && this.unCheckedUrl.equals(other.unCheckedUrl)
    }

    override fun hashCode(): Int {
        var result = checkedUrl?.hashCode() ?: 0
        result = 31 * result + (unCheckedUrl?.hashCode() ?: 0)
        return result
    }

    fun isValid() : Boolean {
        return !checkedUrl.isNullOrBlank() && !unCheckedUrl.isNullOrBlank()
    }

    fun hasValidTextColor() : Boolean {
        return !charactersCheckedColor.isNullOrBlank() && !charactersUnCheckedColor.isNullOrBlank()
                && (charactersUnCheckedColor?.startsWith("#") ?: false) && (charactersCheckedColor?.startsWith("#") ?: false)
    }

    fun hasValidTextColorAndDark() : Boolean {
        return !charactersCheckedColor.isNullOrBlank() && !charactersUnCheckedColor.isNullOrBlank()
                && (charactersUnCheckedColor?.startsWith("#") ?: false) && (charactersCheckedColor?.startsWith("#") ?: false) && !darkCharactersCheckedColor.isNullOrBlank() && !darkCharactersUnCheckedColor.isNullOrBlank()
        && (darkCharactersCheckedColor?.startsWith("#") ?: false) && (darkCharactersUnCheckedColor?.startsWith("#") ?: false)
    }
}
data class VipBottomTabIconInfo(
    val materialId: String? = "",
    val inactiveImage: String? = "",
    val activeImage: String? = "",
    val darkInactiveImage: String? = "",
    val darkActiveImage: String? = "",
    val token: String? = ""
) {
    var dataJson:String? = null

    fun isValid() =
        !materialId.isNullOrEmpty() && !token.isNullOrEmpty() && !activeImage.isNullOrEmpty() && !inactiveImage.isNullOrEmpty()

}