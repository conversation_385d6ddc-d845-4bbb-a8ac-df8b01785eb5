package com.ximalaya.ting.android.host.monitor;

import android.os.Environment;
import android.util.Log;
import android.view.Choreographer;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * Created on 2023/6/26.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class BlockFpsMonitor implements Choreographer.FrameCallback{

    private static final long FRAME_INTERVAL_THRESHOLD = 16; // 设置卡顿判断的阈值，这里使用 16ms
    private static final String TAG = "FrameCallbackMonitor";

    private boolean isMonitoring = false;
    private long lastFrameTime = 0;
    long lastFrameTimeNanos = 0;
    long currentFrameTimeNanos = 0;

    @Override
    public void doFrame(long frameTimeNanos) {
        if (!isMonitoring) {
            return;
        }
        if(lastFrameTimeNanos == 0){
            lastFrameTimeNanos = frameTimeNanos;
        }
        currentFrameTimeNanos = frameTimeNanos;
        long diffMs = TimeUnit.MILLISECONDS.convert(currentFrameTimeNanos-lastFrameTimeNanos, TimeUnit.NANOSECONDS);
        if (diffMs > 100f) {
            Log.d(TAG, "-- doFrame: diffMs " +diffMs);
            StackTraceMonitor.getInstance().startMonitor();
            //long droppedCount = (int) diffMs / 16.6f;
        }
        lastFrameTimeNanos = frameTimeNanos;
        Choreographer.getInstance().postFrameCallback(this);
    }

    private void saveStackTrace() {
        try {
            // 创建文件
            File logFile = createLogFile();
            if (logFile == null) {
                Log.e(TAG, "Failed to create log file");
                return;
            }

            // 获取堆栈信息
            Throwable throwable = new Throwable();
            StackTraceElement[] stackTraceElements = throwable.getStackTrace();

            // 将堆栈信息写入文件
            PrintWriter pw = new PrintWriter(new FileWriter(logFile));
            for (StackTraceElement element : stackTraceElements) {
                pw.println(element.toString());
            }
            pw.close();

            Log.d(TAG, "Stack trace saved to file: " + logFile.getAbsolutePath());
        } catch (Exception e) {
            Log.e(TAG, "Error saving stack trace", e);
        }
    }

    private File createLogFile() {
        try {
            // 创建日志目录
            String logFolderPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/fps_log";
            File logFolder = new File(logFolderPath);
            if (!logFolder.exists()) {
                logFolder.mkdirs();
            }

            // 生成日志文件名
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
            String logFileName = "Stacktrace_" + dateFormat.format(new Date()) + ".txt";

            // 创建日志文件
            File logFile = new File(logFolderPath, logFileName);
            logFile.createNewFile();

            return logFile;
        } catch (Exception e) {
            Log.e(TAG, "Error creating log file", e);
            return null;
        }
    }
    public void startMonitoring() {
        if (!isMonitoring) {
            isMonitoring = true;
            lastFrameTime = System.currentTimeMillis();
            Choreographer.getInstance().postFrameCallback(this);
        }
    }

    public void stopMonitoring() {
        if (isMonitoring) {
            isMonitoring = false;
            Choreographer.getInstance().removeFrameCallback(this);
        }
    }
}
