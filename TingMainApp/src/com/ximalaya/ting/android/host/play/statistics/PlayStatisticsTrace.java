package com.ximalaya.ting.android.host.play.statistics;

import android.app.Application;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.AccessibilityModeManager;
import com.ximalaya.ting.android.host.manager.XuidManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.xmlog.XmLogManager;
import com.ximalaya.ting.android.host.play.util.SystemUtil;
import com.ximalaya.ting.android.host.play.util.TraceUtil;
import com.ximalaya.ting.android.host.util.BluetoothUtil;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.host.util.VersionUtil;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.TicketConstantsKt;
import com.ximalaya.ting.android.opensdk.model.BluetoothStateModel;
import com.ximalaya.ting.android.opensdk.model.soundpatch.AutoPlayTraceModel;
import com.ximalaya.ting.android.opensdk.player.receive.FilterCarBluetoothDevice;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.statistics.model.PlayStatisticsRecord;
import com.ximalaya.ting.android.opensdk.player.statistics.util.PlayStatisticsUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;
import java.util.Random;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/8 20:34
 */
public class PlayStatisticsTrace implements PlayStatisticsUtil.IStatisticsCallback {
    private static final String TAG = "PlayStatisticsTrace";

    private static final String KEY_UPLOAD_TRACK_ID = "key_upload_track_id_for_play_statistics";

    private static final String KEY_LAST_DAY_UPLOAD_SYSTEM_TIME = "key_last_day_upload_system_time";
    private static final String KEY_LAST_DAY_UPLOAD_DURATION = "key_last_day_upload_duration";
    private static final String KEY_LAST_DAY_UPLOAD_LISTENED_DURATION = "key_last_day_upload_listened_duration";

    private static final String KEY_CUR_DAY_UPLOAD_SYSTEM_TIME = "key_cur_day_upload_system_time";
    private static final String KEY_CUR_DAY_UPLOAD_DURATION = "key_cur_day_upload_duration";
    private static final String KEY_CUR_DAY_UPLOAD_LISTENED_DURATION = "key_cur_day_upload_listened_duration";

    private static final String KEY_LAST_DAY_AD_UPLOAD_DURATION = "key_last_day_ad_upload_duration";

    private static final String KEY_LAST_DAY_AD_UPLOAD_LISTENED_DURATION = "key_last_day_ad_upload_listened_duration";

    private static final String KEY_CUR_DAY_AD_UPLOAD_DURATION = "key_cur_day_ad_upload_duration";

    private static final String KEY_CUR_DAY_AD_UPLOAD_LISTENED_DURATION = "key_cur_day_ad_upload_listened_duration";

    private static final String CONTENT_TYPE = "track";
    private static final String CONTENT_TYPE_AI_RADIO = "aiRadio";
    private boolean mEqualToXdcs = false;
    public static int sProcessUploadCount = 0;

    public PlayStatisticsTrace(Application realApplication) {
        mEqualToXdcs = MmkvCommonUtil.getInstance(realApplication).getBoolean(PreferenceConstantsInOpenSdk.KEY_EQUAL_TO_XDCS_FOR_PLAY_DURATION, false);
    }

    @Override
    public void startPlay(String event, PlayStatisticsRecord record) {
        if (record == null) {
            return;
        }
        Log.i(TAG, "event=startPlay " + event + ", " + record);
        // 开始播放  播放
        String contentType = CONTENT_TYPE;
        if (record.getAiSectionId() > 0) {
            contentType = CONTENT_TYPE_AI_RADIO;
        }
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52255)
                .setServiceId("play") // 带上事件类型（启播、暂停->播放、杀进程->续播播放）以及常规属性
                .put("event", event)
                .put("uuid", record.getUuid())
                .put("trackId", String.valueOf(record.getTrackId()))
                .put("albumId", String.valueOf(record.getAlbumId()))
                .put("xuid", XuidManager.INSTANCE.getXuid())
                .put("impl", TraceUtil.IMPL) // 播放渠道来源（ximalaya.itingpad IpadHD，ximalaya.ting.lite 极速版，gemd.iting 主app ios，ximalaya.ting.android 主app android
                .put("xmGrade", TraceUtil.getXmGrade()) // 0不是模拟器，1可能是模拟器或平板，2是模拟器
                .put("contentType", contentType)
                .put("startTime", String.valueOf(record.getStartSysTime()))
                .put("isDownload", String.valueOf(record.getDownloadType()))
                .put("anchorId", "0")
                .put("isBluetooth", String.valueOf(BluetoothUtil.isConnectedToBluetoothDevice()));
        if (!TextUtils.isEmpty(record.getSearchId())) {
            trace.put("searchId", record.getSearchId());
        }
        if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().isElderlyMode()) {
            // specialModeStatus, 0 - 关闭， 1- 老年模式开启， 2 -可以代表别的模式开启
            trace.put("specialModeStatus", "1");
        } else if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
            trace.put("specialModeStatus", "3");
        }
        if (record.getAddSource() > 0) {
            trace.put("addSource", String.valueOf(record.getAddSource()));
        }
        trace.put("ubtSource", record.getUbtSource());

        BluetoothStateModel bluetoothModel = FilterCarBluetoothDevice.getBluetoothStateModel();
        String bluetoothDeviceName = "";
        if (bluetoothModel != null && bluetoothModel.type == BluetoothStateModel.CONNECTED) {
            bluetoothDeviceName = bluetoothModel.deviceName;
        }
        Logger.d(TAG, "SystemInfoUtil-bluetoothDeviceName=" + bluetoothDeviceName);

        trace.put("bsic", SystemUtil.getBsic())
                .put("wifiName", SystemUtil.getWifiInfo())
                .put("bluetoothName", bluetoothDeviceName)
                .put("isPaidAlbum", String.valueOf(record.isPaidAlbum()))
                .put("permissionTime", String.valueOf(record.getPermissionExpireTime()))
                .put("trackListenStatus", String.valueOf(record.getOfflineVisibleType())) // 透传，0 -完全下架，1-下架仅自己可见，2-整张售卖下架仅购买者可见，3-单条售卖下架仅购买者可见
                .put("albumListenStatus", String.valueOf(record.getAlbumOfflineType())) // 透传，0-完全下架任何人不可见，1-仅自己可见，2-已购用户可见
                .put("isPaidTrack", String.valueOf(record.isPaidTrack()))
                .put("isfree", String.valueOf(record.isFree())) // true:免费试听 false:付费声音
                .put("albumAuditStatus", String.valueOf(record.getAlbumStatus())) // 透传，0 审核中，1 表示上架，2 表示下架
                .put("trackAuditStatus", String.valueOf(record.getTrackStatus())) // 透传，0 审核中，1 表示上架，2 表示下架
                .put("permissionSource", record.getPermissionSource())
                .put("vipFirstStatus", String.valueOf(record.getVipFirstStatus()))
                .put("ximiFirstStatus", String.valueOf(record.getXimiFirstStatus()))
                .put("aiSectionID", String.valueOf(record.getAiSectionId()))
                .put("programId", record.getAiRadioEpisodeId())
                .put("playUrl", record.getAiRadioPlayUrl())
                .put("quality", String.valueOf(record.getQuality()));

        String middleAdDurationSecond = timeToStr(record.getMiddleAdDuration());
        trace.put("adInTrackListenedDuration", timeToStr(0)) // 声音中插广告自然播放时长
                .put("adInTrackPlayDuration", timeToStr(0)) // 声音中插广告播放时长
                .put("adInTrackDuration", middleAdDurationSecond) // 声音中插广告时长
                .put("isAdInTrack", String.valueOf(record.isMiddleAdUrl())); // 声音是否中插广告
        Logger.d(TAG, event + " trackAd 广告时长=" + middleAdDurationSecond + "s,isAdInTrack=" + record.isMiddleAdUrl());

        // add ticket
        String ticket = getTicket();
        trace.put("ticket", ticket);

        String bitrateKbps = String.valueOf(record.getBitrate());
        trace.put("framerate", bitrateKbps);

        trace.put("fileSize", String.valueOf(record.getFileSize()))
                .put("fileType", record.getFileType())
                .put("host", record.getHost());

        if (record.getSceneId() > 0) {
            trace.put("sceneId", String.valueOf(record.getSceneId()));
        }

        if (ConstantsOpenSdk.isDebug) {
            Logger.d(TAG, "PlayStatisticsTrace startPlay downloadType=" + record.getDownloadType()
                    + ",bitrateKbps=" + bitrateKbps
                    + ",fileSize=" + record.getFileSize()
                    + ",fileType=" + record.getFileType()
                    + ",host=" + record.getHost()
                    + ",event=" + event);
        }
        Logger.d(TAG, "PlayStatisticsTrace startPlay startTime=" + record.getStartSysTime() + ", ubtSource=" + record.getUbtSource());
        addUbtV2Data(trace, record.getUbtV2());

        XmLogManager.sendToXlogSync(trace);
    }

    private String getTicket() {
        long time = System.currentTimeMillis();
        String ticket = null;
        try {
            XmPlayerService playService = XmPlayerService.getPlayerSrvice();
            if (playService != null) {
                ticket = playService.getTicket(TicketConstantsKt.TYPE_UBT_PLAY);
            } else {
                Logger.d(TAG, "playService null");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(ticket)) {
            ticket = TicketConstantsKt.DEFAULT_TICKET;
        }

        long diff = Math.abs(System.currentTimeMillis() - time);
        if (diff > 3_000) {
            XDCSCollectUtil.statErrorToXDCS("PlayStatTrace", "PlayStatisticsTrace ticket time out " + diff);
        }
        return ticket;
    }

    @Override
    public void stopPlay(String event, PlayStatisticsRecord record, long playedDuration) {
        if (record == null) {
            return;
        }
        if ("杀进程".equals(event)) {
            sProcessUploadCount++;
        }
        Logger.i(TAG, "event=stopPlay " + event + ",pauseReason=" + record.getPauseReason() + ", playedDuration=" + playedDuration + ", " + record);
        // 结束播放  播放
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52256)
                .setServiceId("play") // 需要事件类型（暂停、切歌、播放完）、累积播放时长（开始播放_启播事件到当前事件的播放时长，注意不是进度条时长）、当前事件播放时长（上一个开始播放事件到当前事件的播放时长）
                .put("event", event)
                .put("uuid", record.getUuid());
        Map<String, String> params = getPlayParams(event, record, false, playedDuration);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                trace.put(entry.getKey(), entry.getValue());
            }
        }
        if (!TextUtils.isEmpty(record.getSearchId())) {
            trace.put("searchId", record.getSearchId());
        }
        trace.put("pauseReason", record.getPauseReason());
        addUbtV2Data(trace, record.getUbtV2());
        XmLogManager.sendToXlogSync(trace);
    }

    @Override
    public void processStarted(String event, PlayStatisticsRecord record, long playedDuration) {
        if (record == null) {
            return;
        }
        sProcessUploadCount++;
        Logger.i(TAG, "event=processStarted " + event + ", " + record);
        // 杀进程 启动
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52861)
                .setServiceId("play") // 需要事件类型（暂停、切歌、播放完）、累积播放时长（开始播放_启播事件到当前事件的播放时长，注意不是进度条时长）、当前事件播放时长（上一个开始播放事件到当前事件的播放时长）
                .put("event", event)
                .put("uuid", record.getUuid());
        Map<String, String> params = getPlayParams(event, record, false, playedDuration);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                trace.put(entry.getKey(), entry.getValue());
            }
        }
        if (!TextUtils.isEmpty(record.getSearchId())) {
            trace.put("searchId", record.getSearchId());
        }
        XmLogManager.sendToXlogSync(trace);
    }

    @Override
    public void validPlay(PlayStatisticsRecord record) {
        if (record == null) {
            return;
        }
        Logger.i(TAG, "event=validPlay " + record);
        // 有效播放  其他事件
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52257)
                .setServiceId("play") // 有效播放定义：一次播放记录中，声音播放时长 >240s ；或者  播放时长/声音时长大于40%
                .put("uuid", record.getUuid());
        Map<String, String> params = getPlayParams("有效播放", record);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                trace.put(entry.getKey(), entry.getValue());
            }
        }
        if (!TextUtils.isEmpty(record.getSearchId())) {
            trace.put("searchId", record.getSearchId());
        }
        addUbtV2Data(trace, record.getUbtV2());

        XmLogManager.sendToXlogSync(trace);
    }

    @Override
    public void completePlay(PlayStatisticsRecord record) {
        if (record == null) {
            return;
        }
        Logger.i(TAG, "event=completePlay " + record);
        // 完播  其他事件
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52260)
                .setServiceId("play") //     ○ 当前完播定义：一次播放记录中，如果声音本身<=600s,播放时长/声音时长大于90%；或者声音本身>600s,播放时长超过540s
                .put("uuid", record.getUuid());
        Map<String, String> params = getPlayParams("完播", record);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                trace.put(entry.getKey(), entry.getValue());
            }
        }
        if (!TextUtils.isEmpty(record.getSearchId())) {
            trace.put("searchId", record.getSearchId());
        }

        addUbtV2Data(trace, record.getUbtV2());

        XmLogManager.sendToXlogSync(trace);
    }

    @Override
    public void heartBeat(String event, PlayStatisticsRecord record, long intervalTime) {
        if (record == null) {
            return;
        }
        Logger.i(TAG, "event=heartBeat " + event + " , record: " + record);

        Map<String, String> params = getPlayParams(event, record, true, 0);

        // 心跳播放  播放
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52379)
                .setServiceId("play")
                .put("uuid", record.getUuid())
                .put("IntervalTime", String.valueOf(intervalTime)); // 上传配置的心跳时长
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                trace.put(entry.getKey(), entry.getValue());
            }
        }
        addUbtV2Data(trace, record.getUbtV2());

        XmLogManager.sendToXlogSync(trace);
        sendNewTrace(record, intervalTime);
    }

    private void sendNewTrace(PlayStatisticsRecord record, long intervalTime) {
        int metaId = getMetaId();
        float tempo = 1.0f;
        if (XmPlayerService.getPlayerSrvice() != null && XmPlayerService.getPlayerSrvice().getPlayControl() != null) {
            tempo = XmPlayerService.getPlayerSrvice().getPlayControl().getTempo();
        }
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(metaId)
                .setServiceId("play")
                .put("trackId", String.valueOf(record.getTrackId()))
                .put("albumId", String.valueOf(record.getAlbumId()))
                .put("uuid", record.getUuid())
                .put("playType", String.valueOf(record.getPlayType()))
                .put("psource", record.getPermissionSource())
                .put("tempo", String.valueOf(tempo))
                .put("intervalTime", String.valueOf(intervalTime/1000)); // 上传配置的心跳时长
        Logger.i(TAG, "event=heartBeat sendNewTrace metaId=" + metaId);
        XmLogManager.sendToXlogSync(trace);
    }

    private int[] Ids = {64988, 64982, 64980, 64977, 64973};

    private int getMetaId() {
        Random random = new Random();
        int randomIndex = random.nextInt(Ids.length);
        int randomValue = Ids[randomIndex];
        return randomValue;
    }

    // 有效播放、完播
    private Map<String, String> getPlayParams(String event, PlayStatisticsRecord record) {
        return getPlayParams(event, record, false, 0);
    }

    private static final int MAX_DURATION_RATIO = 10;
    // 最大时间为10h
    private static final long MAX_DURATION_MS = 3600_000 * 10L;

    private Map<String, String> getPlayParams(String event, PlayStatisticsRecord record, boolean heartBeat, long playedDuration) {
        String playDuration = timeToStr(record.getPlayedDuration() / 1000.0f);
        long totalPlayDurationMs = record.getTotalPlayedDuration() + record.getPlayedDuration() + record.getLastPlayedDuration();
        // 这里限制下最大时长是播放时长的10倍
        int duration = record.getDuration();
        if (duration > 0) {
            totalPlayDurationMs = Math.min(totalPlayDurationMs, duration * 1000L * MAX_DURATION_RATIO);
        }
        totalPlayDurationMs = Math.min(totalPlayDurationMs, MAX_DURATION_MS);
        if (totalPlayDurationMs <= 0) {
            totalPlayDurationMs = 0;
        }

        Context context = BaseApplication.getMyApplicationContext();
        long savedTrackId = MmkvCommonUtil.getInstance(context).getLong(KEY_UPLOAD_TRACK_ID, 0);
        if (savedTrackId > 0 && savedTrackId != record.getTrackId()) {
            clearUploadTime();
        }

        long last_sys_time = MmkvCommonUtil.getInstance(context).getLong(KEY_CUR_DAY_UPLOAD_SYSTEM_TIME, 0);
        long cur_sys_time = System.currentTimeMillis();

        if (ConstantsOpenSdk.isDebug) {
            Logger.d(TAG, "PlayStatisticsTrace getPlayParams cur_sys_time=" + DateTimeUtil.getDateStr(cur_sys_time) + ", last_sys_time=" + DateTimeUtil.getDateStr(last_sys_time) + ",permissionSource=" + record.getPermissionSource());
        }

        try {
            AutoPlayTraceModel model = GsonUtils.parseJson(MMKVUtil.getInstance().getString("KEY_IS_FROM_AUTO_PLAY_" + record.getTrackId(), ""),
                    AutoPlayTraceModel.class);
            if (model != null && model.getTrackId() == record.getTrackId()) {
                record.setNextType(5);
                record.setRec_track(model.getRecTrack() == null ? "" : model.getRecTrack());
                record.setRec_src(model.getRecSrc() == null ? "" : model.getRecSrc());
                record.setUbtTraceId(model.getTraceId() == null ? "" : model.getTraceId());
            }
        } catch (Exception ignored) {
        }

        long lastDayPlayDuration = 0;
        float lastDayListenedDuration = 0;
        float lastDayAdPlayDuration = 0;
        float lastDayAdListenedDuration = 0;
        if (DateTimeUtil.isAnotherDay(last_sys_time)) {
            // 如果上一次的上报时间不是同一天，需要跨天了
            lastDayPlayDuration = MmkvCommonUtil.getInstance(context).getLong(KEY_CUR_DAY_UPLOAD_DURATION);
            lastDayListenedDuration = MmkvCommonUtil.getInstance(context).getFloat(KEY_CUR_DAY_UPLOAD_LISTENED_DURATION);
            lastDayAdPlayDuration = MmkvCommonUtil.getInstance(context).getFloat(KEY_CUR_DAY_AD_UPLOAD_DURATION);
            lastDayAdListenedDuration = MmkvCommonUtil.getInstance(context).getFloat(KEY_CUR_DAY_AD_UPLOAD_LISTENED_DURATION);
            MmkvCommonUtil.getInstance(context).saveLong(KEY_LAST_DAY_UPLOAD_SYSTEM_TIME, cur_sys_time);
            MmkvCommonUtil.getInstance(context).saveFloat(KEY_LAST_DAY_UPLOAD_LISTENED_DURATION, lastDayListenedDuration);
            MmkvCommonUtil.getInstance(context).saveLong(KEY_LAST_DAY_UPLOAD_DURATION, lastDayPlayDuration);
            MmkvCommonUtil.getInstance(context).saveFloat(KEY_LAST_DAY_AD_UPLOAD_DURATION, lastDayAdPlayDuration);
            MmkvCommonUtil.getInstance(context).saveFloat(KEY_LAST_DAY_AD_UPLOAD_LISTENED_DURATION, lastDayAdListenedDuration);
        } else {
            // 当前仍然是同一天
            lastDayPlayDuration = MmkvCommonUtil.getInstance(context).getLong(KEY_LAST_DAY_UPLOAD_DURATION);
            lastDayListenedDuration = MmkvCommonUtil.getInstance(context).getFloat(KEY_LAST_DAY_UPLOAD_LISTENED_DURATION);
            lastDayAdPlayDuration = MmkvCommonUtil.getInstance(context).getFloat(KEY_LAST_DAY_AD_UPLOAD_DURATION);
            lastDayAdListenedDuration = MmkvCommonUtil.getInstance(context).getFloat(KEY_LAST_DAY_AD_UPLOAD_LISTENED_DURATION);
        }

        // 每个结束埋点都会带上这个数据，同时计算当天的播放时长
        long curTotalPlayDuration = totalPlayDurationMs;
        String totalPlayDuration = timeToStr(totalPlayDurationMs / 1000.0f);
        float totalPlayedTimeMs = totalPlayDurationMs;
        if (mEqualToXdcs && playedDuration > 0) {
            // 这里和totalPlayDurationMs比较下，取一个最大值
            if (duration > 0) {
                playedDuration = Math.min(playedDuration, duration * 1000L * MAX_DURATION_RATIO);
            }
            playedDuration = Math.min(playedDuration, MAX_DURATION_MS);
            if (playedDuration > totalPlayDurationMs) {
                long diff = playedDuration - totalPlayDurationMs;
                curTotalPlayDuration = playedDuration;
                totalPlayedTimeMs = playedDuration;
                totalPlayDuration = timeToStr(playedDuration / 1000.0f);
                playDuration = timeToStr((record.getPlayedDuration() + diff) / 1000.0f);
            }
        }

        // totalListenedDurationMs 总计自然收听时长
        float totalListenedDurationMs = record.getTotalListenedDuration() + record.getListenedDuration() + record.getLastListenedDuration();
        if (duration > 0) {
            totalListenedDurationMs = Math.min(totalListenedDurationMs, duration * 1000L * MAX_DURATION_RATIO);
        }
        totalListenedDurationMs = Math.min(totalListenedDurationMs, MAX_DURATION_MS);
        if (totalListenedDurationMs <= 0) {
            totalListenedDurationMs = 0;
        }

        // 规范下数据，永远小于playedDuration
        float adPlayDurationMs = record.getAdPlayedDuration();
        float adListenedDurationMs = record.getAdListenedDuration();
        if (adListenedDurationMs > totalListenedDurationMs) {
            adListenedDurationMs = totalListenedDurationMs;
        }
        if (adPlayDurationMs > totalPlayedTimeMs) {
            adPlayDurationMs = totalPlayedTimeMs;
        }

        float curTotalListenedDuration = totalListenedDurationMs;

        MmkvCommonUtil.getInstance(context).saveLong(KEY_CUR_DAY_UPLOAD_DURATION, curTotalPlayDuration);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_CUR_DAY_UPLOAD_LISTENED_DURATION, curTotalListenedDuration);
        MmkvCommonUtil.getInstance(context).saveLong(KEY_CUR_DAY_UPLOAD_SYSTEM_TIME, cur_sys_time);
        MmkvCommonUtil.getInstance(context).saveLong(KEY_UPLOAD_TRACK_ID, record.getTrackId());
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_CUR_DAY_AD_UPLOAD_DURATION, adPlayDurationMs);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_CUR_DAY_AD_UPLOAD_LISTENED_DURATION, adListenedDurationMs);
//        Logger.d(TAG, "PlayStatisticsTrace getPlayParams lastDayAdListenedDuration=" + lastDayAdListenedDuration
//                + ",adListenedDurationMs=" + adListenedDurationMs
//                + ",adPlayDurationMs=" + adPlayDurationMs + ",lastDayAdPlayDuration=" + lastDayAdPlayDuration);

        // 这里添加时长的比较
        if (curTotalListenedDuration < lastDayListenedDuration) {
            lastDayListenedDuration = 0;
        }
        if (curTotalPlayDuration < lastDayPlayDuration) {
            lastDayPlayDuration = 0;
        }
        if (adListenedDurationMs < lastDayAdListenedDuration) {
            lastDayAdListenedDuration = 0;
        }
        if (adPlayDurationMs < lastDayAdPlayDuration) {
            lastDayAdPlayDuration = 0;
        }

        String totalListenedDuration = timeToStr(totalListenedDurationMs / 1000.0f);
        String listenedDuration = timeToStr(record.getListenedDuration() / 1000.0f);

        int durationVal = record.getDuration();
        if (durationVal < 0) {
            durationVal = 0;
        }
        String durationStr = String.valueOf(durationVal);

        float playedSecsVal = record.getEndTime() / 1000.0f;
        if (playedSecsVal < 0) {
            playedSecsVal = 0f;
        }
        String playedSecsStr = timeToStr(playedSecsVal);

        Logger.d(TAG, event + " track getPlayParams 声音时长=" + durationStr + "总计播放时长=" + totalPlayDuration
                + "s, 总计自然收听时长=" + totalListenedDuration
                + "s, 当前播放时长=" + playDuration + "s, 当前自然收听时长=" + listenedDuration + "s, 当前时间点=" + playedSecsStr + "s");

        int connectType = record.getConnectType();
        int connectDevice = record.getConnectDevice();
        String connectDeviceName = record.getConnectDeviceName();
        if (connectType == 1) {
            try {
                BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
                if (mBluetoothAdapter.isEnabled() && mBluetoothAdapter.getProfileConnectionState(BluetoothProfile.A2DP) == BluetoothProfile.STATE_CONNECTED) {
                } else {
                    connectType = 0;
                    connectDevice = 0;
                    connectDeviceName = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        int vipType = -1;
        if (UserInfoMannage.isVipUser() && UserInfoMannage.getInstance().getUser() != null) {
            if (UserInfoMannage.getInstance().getUser().getVipLevel() > 0) {
                vipType = 1;
            } else {
                vipType = 2;
            }
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(HttpParamsConstants.PARAM_USER_VIP_TYPE, String.valueOf(vipType));
            jsonObject.put(HttpParamsConstants.PARAM_USER_CHILD_VIP_TYPE, String.valueOf(UserInfoMannage.isChildVipUser() ? 1 : -1));
            jsonObject.put(HttpParamsConstants.PARAM_USER_XIMI_VIP_TYPE, String.valueOf(UserInfoMannage.isXimiVipUser() ? 1 : -1));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String vipTypeStr = jsonObject.toString();

        String playSceneStr = "";
        int specialModeStatus = 0; // specialModeStatus, 0 - 关闭， 1- 老年模式开启， 2 -可以代表别的模式开启
        if (XmPlayerService.getPlayerSrvice() != null) {
            int playScene = XmPlayerService.getPlayerSrvice().getPlayScene();
            if (playScene == XmPlayerService.PLAY_SCENE_TYPE_DRIVE_MODE) {
                playSceneStr = String.valueOf(playScene);
            }
            if (XmPlayerService.getPlayerSrvice().isElderlyMode()) {
                specialModeStatus = 1;
            } else if (AccessibilityModeManager.INSTANCE.isAccessibilityMode()) {
                specialModeStatus = 3;
            }
        }

        String command = MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).
                getString(PreferenceConstantsInOpenSdk.KEY_XM_CLIP_FOR_PLAY_1);
        MmkvCommonUtil.getInstance(MainApplication.getMyApplicationContext()).
                removeKey(PreferenceConstantsInOpenSdk.KEY_XM_CLIP_FOR_PLAY_1);

//        Map<String, String> ubtTraceMap = null;
//        try {
//            ubtTraceMap = TrackUbtSourceManager.parseUbtSource(TrackUbtSourceManager.getUbtSourceString(String.valueOf(record.getTrackId())));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        boolean skipHead = record.getHeadSkip();
        int head = skipHead ? record.getHead() / 1000 : 0;
        boolean tailSkip = record.getTailSkip();
        int tail = tailSkip ? record.getTail() / 1000 : 0;
        if (!heartBeat) {
            // 这里会重置设置的值为false
            if (skipHead) {
                record.setHeadSkip(false);
            }
            if (tailSkip) {
                record.setTailSkip(false);
            }
        }

        String adListenedDurationMsStr = timeToStr(adListenedDurationMs / 1000f);
        String adPlayDurationMsStr = timeToStr(adPlayDurationMs / 1000f);
        String middleAdDurationSecond = timeToStr(record.getMiddleAdDuration());
        Logger.d(TAG, event + " trackAd getPlayParams 广告时长=" + middleAdDurationSecond
                + "s,isAdInTrack=" + record.isMiddleAdUrl()
                + ",广告自然收听时长=" + adListenedDurationMsStr + "s,广告收听时长=" + adPlayDurationMsStr + "s");

        TraceUtil.ParamsBuilder builder = new TraceUtil.ParamsBuilder();

        builder.put("adInTrackListenedDuration", adListenedDurationMsStr) // 声音中插广告自然播放时长
                .put("adInTrackPlayDuration", adPlayDurationMsStr) // 声音中插广告播放时长
                .put("adInTrackDuration", middleAdDurationSecond) // 声音中插广告时长
                .put("isAdInTrack", String.valueOf(record.isMiddleAdUrl())); // 声音是否中插广告

        if (!heartBeat) {
            builder.put("listenedDuration", listenedDuration) // 单位为秒。当次播放开始到播放结束的用户花费时长；倍数情况下，比如用户11:00:00开始两倍数播放声音，11:05:00结束播放，自然播放时长为5min。
                    .put("playDuration", playDuration); // 单位为秒。当次播放开始到播放结束时播放的声音时长；倍数情况下，比如用户11:00:00开始两倍数播放声音，11:05:00结束播放，播放时长为10min
        }

        if (specialModeStatus > 0) {
            // 大字模式标识
            builder.put("specialModeStatus", String.valueOf(specialModeStatus));
        }
        if (record.getAddSource() > 0) {
            builder.put("addSource", String.valueOf(record.getAddSource()));
        }

        String lastDayPlayDurationStr = timeToStr(lastDayPlayDuration / 1000f);
        String lastDayListenedDurationStr = timeToStr(lastDayListenedDuration / 1000f);
        Logger.d(TAG, "PlayStatisticsTrace getPlayParams 跨天播放时长=" + lastDayPlayDurationStr
                + "s, 跨天自然播放时长=" + lastDayListenedDurationStr + "s, 当天播放时长=" + (curTotalPlayDuration / 1000f)
                + "s, 当天自然收听时长=" + (curTotalListenedDuration / 1000f) + "s");

        builder.put("beforeTodayTotalPlayDuration", lastDayPlayDurationStr)
                .put("beforeTodayTotalListenedDuration", lastDayListenedDurationStr);

        String lastDayAdPlayDurationStr = timeToStr(lastDayAdPlayDuration / 1000f);
        String lastDayAdListenedDurationStr = timeToStr(lastDayAdListenedDuration / 1000f);
        Logger.d(TAG, "PlayStatisticsTrace getPlayParams ad跨天自然播放时长=" + lastDayAdListenedDurationStr + "s,ad跨天播放时长=" + lastDayAdPlayDurationStr);

        builder.put("adInTrackBeforeTodayTotalListenedDuration", lastDayAdListenedDurationStr)
                        .put("adInTrackBeforeTodayTotalPlayDuration", lastDayAdPlayDurationStr);

        String contentType = CONTENT_TYPE;
        if (record.getAiSectionId() > 0) {
            contentType = CONTENT_TYPE_AI_RADIO;
        }
        builder.put("totalListenedDuration", totalListenedDuration) // 单位为秒。一次播放中，用户累计的花费时长，中间有暂停、杀进程操作需累加；倍数情况下，比如用户11:00:00开始两倍数播放声音，11:05:00结束播放，自然播放时长为5min。
                .put("totalPlayDuration", totalPlayDuration) // 单位为秒。一次播放中，用户累计的声音播放时长，中间有暂停、杀进程操作需累加；倍数情况下，比如用户11:00:00开始两倍数播放声音，11:05:00结束播放，播放时长为10min。
                .put("endSkipDuration", String.valueOf(tail)) // 单位为秒。一次播放中，用户跳过了多长时间的片尾，如果没有跳过片尾，赋值0；
                .put("isSkipEnd", tailSkip ? "true" : "false") // 一次播放中，用户收听过程中是否跳过了片尾，跳过取值“true”，没有跳过取值“false”；
                .put("introSkipDuration", String.valueOf(head)) // 单位为秒。一次播放中，用户跳过了多长时间的片头，如果没有跳过片头，赋值0；
                .put("isSkipIntro", skipHead ? "true" : "false") // 一次播放中，用户收听过程中是否跳过了片头，跳过取值“true”，没有跳过取值“false”
                .put("trackId", String.valueOf(record.getTrackId()))
                .put("albumId", String.valueOf(record.getAlbumId()))
                .put("anchorId", "0")
                .put("rec_src", record.getRec_src())
                .put("rec_track", record.getRec_track())
                .put("trackDuration", durationStr)
                .put("playSource", record.getPlaySource())
                .put("quality", String.valueOf(record.getQuality())) // 音质分布采集，Android+iOS，0:标准 1:高清 2:超清
                .put("isAutoNext", String.valueOf(record.getNextType())) // 1 自动播放; 2 点击上一首跳转；3 点击下一首跳转；4 其他跳转 5 完播推荐专辑自动播放
                .put("connectType", String.valueOf(connectType))
                .put("connectDeviceName", connectDeviceName)
                .put("connectDevice", String.valueOf(connectDevice)) // 比如蓝牙
                .put("endTime", String.valueOf(record.getEndSysTime()))
                .put("url", record.getPlayUrl()) // 记录收听链接
                .put("startTime", String.valueOf(record.getStartSysTime()))
                .put("sceneId", record.getSceneId() > 0 ? String.valueOf(record.getSceneId()) : "")
                .put("channelId", record.getChannelId() > 0 ? String.valueOf(record.getChannelId()) : "")
                .put("permissionSource", record.getPermissionSource())
                .put("systemUserAgent", DeviceUtil.getSystemUserAgent())
                .put("playScene", playSceneStr)
                .put("vipType", vipTypeStr)
                .put("contentType", contentType)
                .put("playedSecs", playedSecsStr)
                .put("sdkClientType", "-99")
                .put("clientOsType", "-99")
                .put("ubtPrevTraceId", record.getUbtPrevTraceId())
                .put("umid", CommonRequestM.getUMID(BaseApplication.getMyApplicationContext()))
                .put("playType", String.valueOf(record.getPlayType()))
                .put("openPlatformAppKey", "-99") // 开放平台注册的应用的APPKEY
                .put("platformType", "-99") // 1-开放平台，2-podcast
//                .put("playId", XX)
                .put("ubtTraceId", record.getUbtTraceId())
                .put("ubtSource", record.getUbtSource())
                .put("impl", TraceUtil.IMPL) // 播放渠道来源（ximalaya.itingpad IpadHD，ximalaya.ting.lite 极速版，gemd.iting 主app ios，ximalaya.ting.android 主app android
                .put("command", command)
                .put("isDownload", String.valueOf(record.getDownloadType()))
                .put("uploadId", String.valueOf(record.getUploadId())) // 音频对应的一种或多种音色
                .put("showType", "0");
        if (ConstantsOpenSdk.isDebug) {
            Logger.d(TAG, "PlayStatisticsTrace getPlayParams downloadType=" + record.getDownloadType() + ", event=" + event);
        }

        BluetoothStateModel bluetoothModel = FilterCarBluetoothDevice.getBluetoothStateModel();
        String bluetoothDeviceName = "";
        if (bluetoothModel != null && bluetoothModel.type == BluetoothStateModel.CONNECTED) {
            bluetoothDeviceName = bluetoothModel.deviceName;
        }
        Logger.d(TAG, "SystemInfoUtil-bluetoothDeviceName=" + bluetoothDeviceName);

        builder.put("bsic", SystemUtil.getBsic())
                .put("wifiName", SystemUtil.getWifiInfo())
                .put("bluetoothName", bluetoothDeviceName)
                .put("isPaidAlbum", String.valueOf(record.isPaidAlbum()))
                .put("permissionTime", String.valueOf(record.getPermissionExpireTime()))
                .put("trackListenStatus", String.valueOf(record.getOfflineVisibleType())) // 透传，0 -完全下架，1-下架仅自己可见，2-整张售卖下架仅购买者可见，3-单条售卖下架仅购买者可见
                .put("albumListenStatus", String.valueOf(record.getAlbumOfflineType())) // 透传，0-完全下架任何人不可见，1-仅自己可见，2-已购用户可见
                .put("isPaidTrack", String.valueOf(record.isPaidTrack()))
                .put("isfree", String.valueOf(record.isFree())) // true:免费试听 false:付费声音
                .put("albumAuditStatus", String.valueOf(record.getAlbumStatus())) // 透传，0 审核中，1 表示上架，2 表示下架
                .put("trackAuditStatus", String.valueOf(record.getTrackStatus())) // 透传，0 审核中，1 表示上架，2 表示下架
                .put("vipFirstStatus", String.valueOf(record.getVipFirstStatus()))
                .put("ximiFirstStatus", String.valueOf(record.getXimiFirstStatus()))
                .put("programId", record.getAiRadioEpisodeId())
                .put("playUrl", record.getAiRadioPlayUrl())
                .put("aiSectionID", String.valueOf(record.getAiSectionId()));

        String ticket = getTicket();
        builder.put("ticket", ticket);
        Logger.d(TAG, "ticket=" + ticket);
        Logger.d(TAG, "PlayStatisticsTrace getPlayParams: " + event + " ubtTraceId=" + record.getUbtTraceId() + ", ubtPrevTraceId=" + record.getUbtPrevTraceId() + ", ubtSource=" + record.getUbtSource() + ", searchId=" + record.getSearchId() + ", isAutoNext=" + record.getNextType() + ", aiSectionID=" + record.getAiSectionId());
        return builder.getParams();
    }

    // 单位是s
    private static String timeToStr(float time) {
        return String.format("%.3f", time);
    }

    @Override
    public boolean isBetaVersion() {
        return !VersionUtil.isReleaseVersion();
    }

    @Override
    public void clearUploadTime() {
        Context context = BaseApplication.getMyApplicationContext();
        MmkvCommonUtil.getInstance(context).saveLong(KEY_LAST_DAY_UPLOAD_SYSTEM_TIME, 0);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_LAST_DAY_UPLOAD_LISTENED_DURATION, 0);
        MmkvCommonUtil.getInstance(context).saveLong(KEY_LAST_DAY_UPLOAD_DURATION, 0);

        MmkvCommonUtil.getInstance(context).saveLong(KEY_CUR_DAY_UPLOAD_DURATION, 0);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_CUR_DAY_UPLOAD_LISTENED_DURATION, 0);
        MmkvCommonUtil.getInstance(context).saveLong(KEY_CUR_DAY_UPLOAD_SYSTEM_TIME, 0);

        MmkvCommonUtil.getInstance(context).saveFloat(KEY_LAST_DAY_AD_UPLOAD_LISTENED_DURATION, 0);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_LAST_DAY_AD_UPLOAD_DURATION, 0);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_CUR_DAY_AD_UPLOAD_DURATION, 0);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_CUR_DAY_AD_UPLOAD_LISTENED_DURATION, 0);

        MmkvCommonUtil.getInstance(context).saveLong(KEY_UPLOAD_TRACK_ID, 0);
    }

    private static void addUbtV2Data(XMTraceApi.Trace trace, Map<String, String> ubtV2) {
        if (ubtV2 == null || ubtV2.isEmpty() || trace == null) {
            return;
        }

        Set<Map.Entry<String, String>> entries = ubtV2.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            trace.put(entry.getKey(), entry.getValue());
        }
    }
}
