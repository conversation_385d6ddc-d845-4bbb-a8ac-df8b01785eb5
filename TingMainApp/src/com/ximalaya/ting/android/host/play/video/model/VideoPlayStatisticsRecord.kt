package com.ximalaya.ting.android.host.play.video.model

import androidx.annotation.Keep
import com.google.gson.Gson
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.model.album.AlbumVideoInfoModel
import com.ximalaya.ting.android.host.model.play.ExtraInfo
import com.ximalaya.ting.android.host.model.play.VideoBaseInfo
import com.ximalaya.ting.android.host.play.video.trace.VideoPlayStatisticsTrace
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.statistic.PlayIdManager
import com.ximalaya.ting.android.opensdk.player.statistics.event.STATUS_NONE
import com.ximalaya.ting.android.opensdk.util.Constants
import com.ximalaya.ting.android.opensdk.util.DeviceUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.model.UbtSourceInfo
import com.ximalaya.ting.android.xmutil.Logger

/**
 * <AUTHOR>
 * @date 2023/3/9 17:46
 */
const val TAG_UBT = "VideoPlayStatisticsUbt"

@Keep
class VideoPlayStatisticsRecord {
    companion object {
        fun convertToVideoInfo(track: Track?, extraInfo: ExtraInfo? = null): AlbumVideoInfoModel.AlbumVideoInfo {
            return AlbumVideoInfoModel.AlbumVideoInfo().apply {
                trackId = track?.dataId ?: 0
                albumId = track?.album?.albumId ?: 0
                title = track?.trackTitle ?: ""
                duration = (track?.duration ?: 0).toDouble()
                this.track = track
                this.extraInfo = extraInfo
            }
        }
    }

    var trackId = 0L
    var albumId = 0L
    var title = ""
    var playUrl = ""
    var rec_src = ""
    var rec_track = ""
    var playSource = ""
    var quality = 0
    var connectType = 0
    var connectDevice = 0
    var connectDeviceName = ""
    var permissionSource = ""
    var sceneId = 0L
    var channelId = 0L
    var playType = 0

    var sourceType = ""
    var contentId = 0L
    var contentRepoId: String? = null
    var contentType = "video"

    var ubtSource = ""
    var ubtTraceId = ""
    var ubtPrevTraceId = ""

    // 自定义字段
    var validPlay = false
    var completePlay = false
    var playedDuration = 0L
    var listenedDuration = 0F

    // 在整个进程生命周期的播放时长
    var totalPlayedDuration = 0L
    var totalListenedDuration = 0F

    var lastStatus = STATUS_NONE

    var startTime = 0
        private set
    var endTime = 0L
        private set
    var duration = 0L

    var startSysTime = 0L
    var endSysTime = 0L

    var uuid: String = ""

    var downloadType = 0

    var isPaidTrack = false // 是否付费声音
    var isPaidAlbum = false // 是否付费专辑
    var offlineVisibleType = -1 // 可见类型（下架之后才有的）visiableType = 0 完全下架
    var permissionExpireTime: Long = 0 // 权益过期时间戳
    var albumStatus = 0 // 专辑审核状态
    var albumOfflineType = -1 //专辑下架类型
    var trackStatus = 0 // 审核状态
    var isFree = false // 是否免费听
    var vipFirstStatus = 0
    var ximiFirstStatus = -1

    var ubtV2: Map<String, String>? = null

    constructor()
    constructor(videoInfo: AlbumVideoInfoModel.AlbumVideoInfo, playUrl: String?, playType: Int) {
        lastStatus = STATUS_NONE

        trackId = videoInfo.trackId ?: 0L
        albumId = videoInfo.albumId ?: 0L
        val xuid = DeviceUtil.deviceCallback?.getXuid() ?: ""
        uuid = "$xuid-${PlayIdManager.generatePlayId()}"
        title = videoInfo.title ?: ""
        this.playUrl = playUrl ?: ""
        this.downloadType = if (playUrl == null) 0 else if (playUrl.startsWith("http://") || playUrl.startsWith("https://")) 0 else 1
        this.playType = playType
        this.duration = (videoInfo.duration * 1000).toLong()
        if (this.duration < 0) {
            this.duration = 0
        }
        this.sourceType = videoInfo.sourceType ?: ""
        this.contentId = videoInfo.contentId
        this.contentRepoId = videoInfo.contentRepoId
        if (videoInfo.contentType != null && videoInfo.contentType.isNotEmpty()) {
            this.contentType = videoInfo.contentType
        } else {
            this.contentType = VideoPlayStatisticsTrace.CONTENT_TYPE
        }

        val model = videoInfo.track
        if (model != null) {
            rec_src = model.recSrc ?: ""
            rec_track = model.recTrack ?: ""

            // 一键听上传播放统计时 playSource变为5001 废弃掉之前的31
            var playSource = model.playSource
            if (playSource == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY || playSource == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4) {
                playSource = ConstantsOpenSdk.PLAY_FROM_ONE_KEY_STATISTICS
            }
            this.playSource = "$playSource"

            this.quality = 0

            val recordModel = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).curRecordModel
            connectType = recordModel?.type ?: 0
            connectDevice = recordModel?.device ?: 0
            connectDeviceName = recordModel?.deviceName ?: ""

            this.permissionSource = model.permissionSource ?: ""

            sceneId = model.channelGroupId
            channelId = model.channelId

            isPaidTrack = videoInfo.isPaid
            isFree = videoInfo.isFree
            vipFirstStatus = videoInfo.vipFirstStatus
            ximiFirstStatus = videoInfo.ximiFirstStatus
        }

        updateUbtSource()
        updateExtraInfo(videoInfo.extraInfo)
        if (model != null && model.ubtV2 != null && model.ubtV2.size > 0) {
            ubtV2 = model.ubtV2
        }
    }

    fun updateExtraInfo(extraInfo: ExtraInfo?) {
        extraInfo ?: return
        isPaidAlbum = extraInfo.isPaidAlbum
        albumStatus = extraInfo.albumStatus
        offlineVisibleType = extraInfo.offlineVisibleType
        permissionExpireTime = extraInfo.permissionExpireTime
        albumOfflineType = extraInfo.albumOfflineType
        trackStatus = extraInfo.trackStatus
    }

    private fun updateUbtSource() {
        try {
            val ubtSourceInfo: UbtSourceInfo = XMTraceApi.getInstance().getPlayingSource(Constants.KEY_PLAY) ?: return
            ubtTraceId = ubtSourceInfo.currTraceId ?: ""
            ubtPrevTraceId = ubtSourceInfo.prevTraceId ?: ""
            ubtSource = Gson().toJson(ubtSourceInfo.ubtSource)
            Logger.d(TAG_UBT, "video record $ubtTraceId, $ubtPrevTraceId $ubtSource")
        } catch (e: Exception) {
            e.printStackTrace()
            Logger.d(TAG_UBT, "video error, $ubtTraceId, $ubtPrevTraceId $ubtSource")
        }
    }

    fun updateStartTime(startTime: Int) {
        this.startTime = startTime
        this.startSysTime = System.currentTimeMillis()
    }

    fun updateEndTime(endTime: Long) {
        this.endTime = endTime
        this.endSysTime = System.currentTimeMillis()
    }

    override fun toString(): String {
        return "VideoPlayStatisticsRecord(trackId=$trackId, albumId=$albumId, title='$title', playUrl='$playUrl', rec_src='$rec_src', rec_track='$rec_track', playSource='$playSource', quality=$quality, connectType=$connectType, connectDevice=$connectDevice, connectDeviceName='$connectDeviceName', permissionSource='$permissionSource', sceneId=$sceneId, channelId=$channelId, playType=$playType, sourceType='$sourceType', contentId=$contentId, contentType='$contentType', ubtSource='$ubtSource', ubtTraceId='$ubtTraceId', ubtPrevTraceId='$ubtPrevTraceId', validPlay=$validPlay, completePlay=$completePlay, playedDuration=$playedDuration, listenedDuration=$listenedDuration, totalPlayedDuration=$totalPlayedDuration, totalListenedDuration=$totalListenedDuration, lastStatus=$lastStatus, startTime=$startTime, endTime=$endTime, duration=$duration, startSysTime=$startSysTime, endSysTime=$endSysTime, uuid='$uuid', downloadType=$downloadType, isPaidTrack=$isPaidTrack, isPaidAlbum=$isPaidAlbum, offlineVisibleType=$offlineVisibleType, permissionExpireTime=$permissionExpireTime, albumStatus=$albumStatus, albumOfflineType=$albumOfflineType, trackStatus=$trackStatus, isFree=$isFree, vipFirstStatus=$vipFirstStatus, ximiFirstStatus=$ximiFirstStatus)"
    }
}