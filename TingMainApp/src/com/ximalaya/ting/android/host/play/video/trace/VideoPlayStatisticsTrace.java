package com.ximalaya.ting.android.host.play.video.trace;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.text.TextUtils;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.manager.XuidManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.play.util.SystemUtil;
import com.ximalaya.ting.android.host.play.util.TraceUtil;
import com.ximalaya.ting.android.host.play.video.model.VideoPlayStatisticsRecord;
import com.ximalaya.ting.android.host.receiver.BluetoothStateBroadcastReceiver;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.TicketConstantsKt;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/8 20:34
 */
public class VideoPlayStatisticsTrace {
    private static final String TAG = "VideoPlayStatisticsTrace";
    public static final String CONTENT_TYPE = "video";

    private static final String KEY_UPLOAD_TRACK_ID = "key_video_upload_track_id_for_play_statistics";

    private static final String KEY_LAST_DAY_UPLOAD_SYSTEM_TIME = "key_video_last_day_upload_system_time";
    private static final String KEY_LAST_DAY_UPLOAD_DURATION = "key_video_last_day_upload_duration";
    private static final String KEY_LAST_DAY_UPLOAD_LISTENED_DURATION = "key_video_last_day_upload_listened_duration";

    private static final String KEY_CUR_DAY_UPLOAD_SYSTEM_TIME = "key_video_cur_day_upload_system_time";
    private static final String KEY_CUR_DAY_UPLOAD_DURATION = "key_video_cur_day_upload_duration";
    private static final String KEY_CUR_DAY_UPLOAD_LISTENED_DURATION = "key_video_cur_day_upload_listened_duration";

    public static void startPlay(String event, VideoPlayStatisticsRecord record) {
        if (record == null) {
            return;
        }
        Logger.i(TAG, "startPlay " + event + ", " + record);

        String ticket = getTicket();
        Logger.d(TAG, "ticket=" + ticket);

        String bluetoothDeviceName = BluetoothStateBroadcastReceiver.sBluetoothName;
        Logger.d(TAG, "SystemInfoUtil-bluetoothDeviceName=" + bluetoothDeviceName);

        // 开始播放  播放
        XMTraceApi.Trace trace = new XMTraceApi.Trace();
        trace
            .setMetaId(52255)
            .setServiceId("play") // 带上事件类型（启播、暂停->播放、杀进程->续播播放）以及常规属性
            .put("event", event);
        if (record.getSceneId() > 0) {
            trace.put("sceneId", String.valueOf(record.getSceneId()));
        }
        addUbtV2Data(trace, record.getUbtV2());

        trace.put("uuid", record.getUuid())
                .put("trackId", String.valueOf(record.getTrackId()))
                .put("albumId", String.valueOf(record.getAlbumId()))
                .put("startTime", String.valueOf(record.getStartSysTime()))
                .put("xuid", XuidManager.INSTANCE.getXuid())
                .put("impl", TraceUtil.IMPL) // 播放渠道来源（ximalaya.itingpad IpadHD，ximalaya.ting.lite 极速版，gemd.iting 主app ios，ximalaya.ting.android 主app android
                .put("xmGrade", TraceUtil.getXmGrade()) // 0不是模拟器，1可能是模拟器或平板，2是模拟器
                .put("contentType", record.getContentType() != null ? record.getContentType() : CONTENT_TYPE)
                .put("anchorId", "0")
                .put("ubtSource", record.getUbtSource())
                .put("isDownload", String.valueOf(record.getDownloadType()))
                .put("ticket", ticket)
                .put("sourceType", record.getSourceType())
                .put("contentId", String.valueOf(record.getContentId()))
                .put("contentRepoId", record.getContentRepoId())
                .put("bsic", SystemUtil.getBsic())
                .put("wifiName", SystemUtil.getWifiInfo())
                .put("bluetoothName", bluetoothDeviceName)
                .put("isPaidAlbum", String.valueOf(record.isPaidAlbum()))
                .put("permissionTime", String.valueOf(record.getPermissionExpireTime()))
                .put("trackListenStatus", String.valueOf(record.getOfflineVisibleType())) // 透传，0 -完全下架，1-下架仅自己可见，2-整张售卖下架仅购买者可见，3-单条售卖下架仅购买者可见
                .put("albumListenStatus", String.valueOf(record.getAlbumOfflineType())) // 透传，0-完全下架任何人不可见，1-仅自己可见，2-已购用户可见
                .put("isPaidTrack", String.valueOf(record.isPaidTrack()))
                .put("isfree", String.valueOf(record.isFree())) // true:免费试听 false:付费声音
                .put("albumAuditStatus", String.valueOf(record.getAlbumStatus())) // 透传，0 审核中，1 表示上架，2 表示下架
                .put("trackAuditStatus", String.valueOf(record.getTrackStatus())) // 透传，0 审核中，1 表示上架，2 表示下架
                .put("permissionSource", record.getPermissionSource())
                .put("vipFirstStatus", String.valueOf(record.getVipFirstStatus()))
                .put("ximiFirstStatus", String.valueOf(record.getXimiFirstStatus()))
                .createTrace();
        if (ConstantsOpenSdk.isDebug) {
            Logger.d(TAG, "VideoPlayStatisticsTrace startPlay downloadType=" + record.getDownloadType() + ", event=" + event);
        }
        Logger.d(TAG, "VideoPlayStatisticsTrace startPlay ubtSource=" + record.getUbtSource());
    }

    private static String getTicket() {
        String ticket = XuidManager.INSTANCE.getTicket(TicketConstantsKt.TYPE_UBT_PLAY);
        if (TextUtils.isEmpty(ticket)) {
            ticket = TicketConstantsKt.DEFAULT_TICKET;
        }
        return ticket;
    }

    public static void stopPlay(String event, VideoPlayStatisticsRecord record) {
        if (record == null) {
            return;
        }
        Logger.i(TAG, "stopPlay " + event + ", " + record);

        // 结束播放  播放
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52256)
                .setServiceId("play") // 需要事件类型（暂停、切歌、播放完）、累积播放时长（开始播放_启播事件到当前事件的播放时长，注意不是进度条时长）、当前事件播放时长（上一个开始播放事件到当前事件的播放时长）
                .put("event", event)
                .put("uuid", record.getUuid());
        Map<String, String> params = getPlayParams(event, record);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                trace.put(entry.getKey(), entry.getValue());
            }
        }
        trace.createTrace();
    }

    public static void validPlay(VideoPlayStatisticsRecord record) {
        if (record == null) {
            return;
        }
        Logger.i(TAG, "validPlay " + record);
        // 有效播放  其他事件
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52257)
                .setServiceId("play") // 有效播放定义：一次播放记录中，声音播放时长 >240s ；或者  播放时长/声音时长大于40%
                .put("uuid", record.getUuid());
        Map<String, String> params = getPlayParams("有效播放", record);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                trace.put(entry.getKey(), entry.getValue());
            }
        }
        trace.createTrace();
    }

    public static void completePlay(VideoPlayStatisticsRecord record) {
        if (record == null) {
            return;
        }
        Logger.i(TAG, "completePlay " + record);
        // 完播  其他事件
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52260)
                .setServiceId("play") //     ○ 当前完播定义：一次播放记录中，如果声音本身<=600s,播放时长/声音时长大于90%；或者声音本身>600s,播放时长超过540s
                .put("uuid", record.getUuid());
        Map<String, String> params = getPlayParams("完播", record);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                trace.put(entry.getKey(), entry.getValue());
            }
        }
        trace.createTrace();
    }

    public static void heartBeat(String event, VideoPlayStatisticsRecord record, long intervalTime) {
        if (record == null) {
            return;
        }
        Logger.i(TAG, "heartBeat " + event + " , record: " + record);

        Map<String, String> params = getPlayParams(event, record, true);

        // 心跳播放  播放
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(52379)
                .setServiceId("play")
                .put("uuid", record.getUuid())
                .put("IntervalTime", String.valueOf(intervalTime)); // 上传配置的心跳时长
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                trace.put(entry.getKey(), entry.getValue());
            }
        }
        trace.createTrace();
    }

    private static Map<String, String> getPlayParams(String event, VideoPlayStatisticsRecord record) {
        return getPlayParams(event, record, false);
    }

    private static Map<String, String> getPlayParams(String event, VideoPlayStatisticsRecord record, boolean heartBeat) {
        String playDuration = timeToStr(record.getPlayedDuration() / 1000.0f);
        String listenedDuration = timeToStr(record.getListenedDuration() / 1000.0f);

        long curTotalPlayDuration = record.getPlayedDuration() + record.getTotalPlayedDuration();
        float curTotalListenedDuration = record.getListenedDuration() + record.getTotalListenedDuration();
        String totalPlayDuration = timeToStr((record.getPlayedDuration() + record.getTotalPlayedDuration()) / 1000.0f);
        String totalListenedDuration = timeToStr((record.getListenedDuration() + record.getTotalListenedDuration()) / 1000.0f);

        Context context = BaseApplication.getMyApplicationContext();
        long savedTrackId = MmkvCommonUtil.getInstance(context).getLong(KEY_UPLOAD_TRACK_ID, 0);
        if (savedTrackId > 0 && savedTrackId != record.getTrackId()) {
            clearUploadTime();
        }

        long last_sys_time = MmkvCommonUtil.getInstance(context).getLong(KEY_CUR_DAY_UPLOAD_SYSTEM_TIME, 0);
        long cur_sys_time = System.currentTimeMillis();

        if (ConstantsOpenSdk.isDebug) {
            Logger.d(TAG, "VideoPlayStatisticsTrace getPlayParams cur_sys_time="
                    + DateTimeUtil.getDateStr(cur_sys_time) + ", last_sys_time=" + DateTimeUtil.getDateStr(last_sys_time));
        }

        long lastDayPlayDuration = 0;
        float lastDayListenedDuration = 0;
        if (DateTimeUtil.isAnotherDay(last_sys_time)) {
            // 如果上一次的上报时间不是同一天，需要跨天了
            lastDayPlayDuration = MmkvCommonUtil.getInstance(context).getLong(KEY_CUR_DAY_UPLOAD_DURATION);
            lastDayListenedDuration = MmkvCommonUtil.getInstance(context).getFloat(KEY_CUR_DAY_UPLOAD_LISTENED_DURATION);
            MmkvCommonUtil.getInstance(context).saveLong(KEY_LAST_DAY_UPLOAD_SYSTEM_TIME, cur_sys_time);
            MmkvCommonUtil.getInstance(context).saveFloat(KEY_LAST_DAY_UPLOAD_LISTENED_DURATION, lastDayListenedDuration);
            MmkvCommonUtil.getInstance(context).saveLong(KEY_LAST_DAY_UPLOAD_DURATION, lastDayPlayDuration);
        } else {
            // 当前仍然是同一天
            lastDayPlayDuration = MmkvCommonUtil.getInstance(context).getLong(KEY_LAST_DAY_UPLOAD_DURATION);
            lastDayListenedDuration = MmkvCommonUtil.getInstance(context).getFloat(KEY_LAST_DAY_UPLOAD_LISTENED_DURATION);
        }

        MmkvCommonUtil.getInstance(context).saveLong(KEY_CUR_DAY_UPLOAD_DURATION, curTotalPlayDuration);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_CUR_DAY_UPLOAD_LISTENED_DURATION, curTotalListenedDuration);
        MmkvCommonUtil.getInstance(context).saveLong(KEY_CUR_DAY_UPLOAD_SYSTEM_TIME, cur_sys_time);
        MmkvCommonUtil.getInstance(context).saveLong(KEY_UPLOAD_TRACK_ID, record.getTrackId());

        // 这里添加时长的比较
        if (curTotalListenedDuration < lastDayListenedDuration) {
            lastDayListenedDuration = 0;
        }
        if (curTotalPlayDuration < lastDayPlayDuration) {
            lastDayPlayDuration = 0;
        }

        float durationVal = record.getDuration() / 1000.0f;
        if (durationVal < 0) {
            durationVal = 0;
        }
        String durationStr = timeToStr(durationVal);

        float playedSecsVal = record.getEndTime() / 1000.0f;
        if (playedSecsVal < 0) {
            playedSecsVal = 0;
        }
        String playedSecsStr = timeToStr(playedSecsVal);

        Logger.d(TAG, event + " video getPlayParams 声音时长=" + durationStr + "s, 总计播放时长=" + totalPlayDuration
                + "s, 总计自然收听时长=" + totalListenedDuration
                + "s, 当前播放时长=" + playDuration + "s, 当前自然收听时长=" + listenedDuration + "s, 当前时间点=" + playedSecsStr + "s");

        int connectType = record.getConnectType();
        int connectDevice = record.getConnectDevice();
        String connectDeviceName = record.getConnectDeviceName();
        if (connectType == 1) {
            try {
                BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
                if (mBluetoothAdapter.isEnabled() && mBluetoothAdapter.getProfileConnectionState(BluetoothProfile.A2DP) == BluetoothProfile.STATE_CONNECTED) {
                } else {
                    connectType = 0;
                    connectDevice = 0;
                    connectDeviceName = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        int vipType = -1;
        if (UserInfoMannage.isVipUser() && UserInfoMannage.getInstance().getUser() != null) {
            if (UserInfoMannage.getInstance().getUser().getVipLevel() > 0) {
                vipType = 1;
            } else {
                vipType = 2;
            }
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(HttpParamsConstants.PARAM_USER_VIP_TYPE, String.valueOf(vipType));
            jsonObject.put(HttpParamsConstants.PARAM_USER_CHILD_VIP_TYPE, String.valueOf(UserInfoMannage.isChildVipUser() ? 1 : -1));
            jsonObject.put(HttpParamsConstants.PARAM_USER_XIMI_VIP_TYPE, String.valueOf(UserInfoMannage.isXimiVipUser() ? 1 : -1));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String vipTypeStr = jsonObject.toString();

        String playSceneStr = "";
        if (XmPlayerService.getPlayerSrvice() != null) {
            int playScene = XmPlayerService.getPlayerSrvice().getPlayScene();
            if (playScene == XmPlayerService.PLAY_SCENE_TYPE_DRIVE_MODE) {
                playSceneStr = String.valueOf(playScene);
            }
        }

        TraceUtil.ParamsBuilder builder = new TraceUtil.ParamsBuilder();

        String lastDayPlayDurationStr = timeToStr(lastDayPlayDuration / 1000f);
        String lastDayListenedDurationStr = timeToStr(lastDayListenedDuration / 1000f);
        Logger.d(TAG, "VideoPlayStatisticsTrace getPlayParams 跨天播放时长=" + lastDayPlayDurationStr
                + "s, 跨天自然播放时长=" + lastDayListenedDurationStr + "s, 当天播放时长=" + (curTotalPlayDuration / 1000f)
                + "s, 当天自然收听时长=" + (curTotalListenedDuration / 1000f) + "s");

        builder.put("beforeTodayTotalPlayDuration", lastDayPlayDurationStr)
                .put("beforeTodayTotalListenedDuration", lastDayListenedDurationStr);

        if (!heartBeat) {
            builder.put("playDuration", playDuration) // 单位为秒。当次播放开始到播放结束时播放的声音时长；倍数情况下，比如用户11:00:00开始两倍数播放声音，11:05:00结束播放，播放时长为10min
                    .put("listenedDuration", listenedDuration); // 单位为秒。当次播放开始到播放结束的用户花费时长；倍数情况下，比如用户11:00:00开始两倍数播放声音，11:05:00结束播放，自然播放时长为5min。
        }

        builder.put("totalListenedDuration", totalListenedDuration) // 单位为秒。一次播放中，用户累计的花费时长，中间有暂停、杀进程操作需累加；倍数情况下，比如用户11:00:00开始两倍数播放声音，11:05:00结束播放，自然播放时长为5min。
                .put("totalPlayDuration", totalPlayDuration) // 单位为秒。一次播放中，用户累计的声音播放时长，中间有暂停、杀进程操作需累加；倍数情况下，比如用户11:00:00开始两倍数播放声音，11:05:00结束播放，播放时长为10min。
                .put("endSkipDuration", "0") // 单位为秒。一次播放中，用户跳过了多长时间的片尾，如果没有跳过片尾，赋值0；
                .put("isSkipEnd", "false") // 一次播放中，用户收听过程中是否跳过了片尾，跳过取值“true”，没有跳过取值“false”；
                .put("introSkipDuration", "0") // 单位为秒。一次播放中，用户跳过了多长时间的片头，如果没有跳过片头，赋值0；
                .put("isSkipIntro", "false") // 一次播放中，用户收听过程中是否跳过了片头，跳过取值“true”，没有跳过取值“false”
                .put("trackId", String.valueOf(record.getTrackId()))
                .put("albumId", String.valueOf(record.getAlbumId()))
                .put("anchorId", "0")
                .put("rec_src", record.getRec_src())
                .put("rec_track", record.getRec_track())
                .put("trackDuration", durationStr)
                .put("playSource", record.getPlaySource())
                .put("quality", "0") // 音质分布采集，Android+iOS，0:标准 1:高清 2:超清
                .put("isAutoNext", "0") // 1 自动播放; 2 点击上一首跳转；3 点击下一首跳转；4 其他跳转
                .put("connectType", String.valueOf(connectType))
                .put("connectDeviceName", connectDeviceName)
                .put("connectDevice", String.valueOf(connectDevice)) // 比如蓝牙
                .put("endTime", timeToStr(record.getEndSysTime() / 1000.0f))
                .put("url", record.getPlayUrl()) // 记录收听链接
                .put("startTime", timeToStr(record.getStartSysTime() / 1000.0f))
                .put("sceneId", record.getSceneId() > 0 ? String.valueOf(record.getSceneId()) : "")
                .put("channelId", record.getChannelId() > 0 ? String.valueOf(record.getChannelId()) : "")
                .put("permissionSource", record.getPermissionSource())
                .put("systemUserAgent", DeviceUtil.getSystemUserAgent())
                .put("playScene", playSceneStr)
                .put("vipType", vipTypeStr)
                .put("contentType", record.getContentType() != null ? record.getContentType() : CONTENT_TYPE)
                .put("playedSecs", playedSecsStr)
                .put("sdkClientType", "-99")
                .put("clientOsType", "-99")
//                .put("ubtPrevTraceId", XX)
                .put("umid", CommonRequestM.getUMID(BaseApplication.getMyApplicationContext()))
                .put("playType", String.valueOf(record.getPlayType()))
                .put("openPlatformAppKey", "-99") // 开放平台注册的应用的APPKEY
                .put("platformType", "-99") // 1-开放平台，2-podcast
                .put("ubtPrevTraceId", record.getUbtPrevTraceId())
                .put("ubtTraceId", record.getUbtTraceId())
                .put("ubtSource", record.getUbtSource())
                .put("impl", TraceUtil.IMPL) // 播放渠道来源（ximalaya.itingpad IpadHD，ximalaya.ting.lite 极速版，gemd.iting 主app ios，ximalaya.ting.android 主app android
                .put("command", null)
                .put("isDownload", String.valueOf(record.getDownloadType()))
                .put("uploadId", null) // 音频对应的一种或多种音色
                .put("showType", "0");

        if (ConstantsOpenSdk.isDebug) {
            Logger.d(TAG, "VideoPlayStatisticsTrace getPlayParams downloadType=" + record.getDownloadType() + ", event=" + event + ", title=" + record.getTitle());
        }
        String ticket = getTicket();
        if (TextUtils.isEmpty(ticket)) {
            ticket = TicketConstantsKt.DEFAULT_TICKET;
        }
        builder.put("ticket", ticket);
        Logger.d(TAG, "ticket=" + ticket);

        String bluetoothDeviceName = BluetoothStateBroadcastReceiver.sBluetoothName;
        Logger.d(TAG, "SystemInfoUtil-bluetoothDeviceName=" + bluetoothDeviceName);
        builder.put("bsic", SystemUtil.getBsic())
                .put("wifiName", SystemUtil.getWifiInfo())
                .put("bluetoothName", bluetoothDeviceName);

        builder.put("sourceType", record.getSourceType())
                .put("contentId", String.valueOf(record.getContentId()))
                .put("contentRepoId", record.getContentRepoId())
                .put("isPaidAlbum", String.valueOf(record.isPaidAlbum()))
                .put("permissionTime", String.valueOf(record.getPermissionExpireTime()))
                .put("trackListenStatus", String.valueOf(record.getOfflineVisibleType())) // 透传，0 -完全下架，1-下架仅自己可见，2-整张售卖下架仅购买者可见，3-单条售卖下架仅购买者可见
                .put("albumListenStatus", String.valueOf(record.getAlbumOfflineType())) // 透传，0-完全下架任何人不可见，1-仅自己可见，2-已购用户可见
                .put("isPaidTrack", String.valueOf(record.isPaidTrack()))
                .put("isfree", String.valueOf(record.isFree())) // true:免费试听 false:付费声音
                .put("albumAuditStatus", String.valueOf(record.getAlbumStatus())) // 透传，0 审核中，1 表示上架，2 表示下架
                .put("trackAuditStatus", String.valueOf(record.getTrackStatus())) // 透传，0 审核中，1 表示上架，2 表示下架
                .put("vipFirstStatus", String.valueOf(record.getVipFirstStatus()))
                .put("ximiFirstStatus", String.valueOf(record.getXimiFirstStatus()));

        Logger.d(TAG, "VideoPlayStatisticsTrace getPlayParams ubtTraceId=" + record.getUbtTraceId() + ", preId=" + record.getUbtPrevTraceId() + ", source=" + record.getUbtSource());
        return builder.getParams();
    }

    // 单位是s
    private static String timeToStr(float time) {
        return String.format("%.3f", time);
    }

    public static void clearUploadTime() {
        Context context = BaseApplication.getMyApplicationContext();
        MmkvCommonUtil.getInstance(context).saveLong(KEY_LAST_DAY_UPLOAD_SYSTEM_TIME, 0);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_LAST_DAY_UPLOAD_LISTENED_DURATION, 0);
        MmkvCommonUtil.getInstance(context).saveLong(KEY_LAST_DAY_UPLOAD_DURATION, 0);

        MmkvCommonUtil.getInstance(context).saveLong(KEY_CUR_DAY_UPLOAD_DURATION, 0);
        MmkvCommonUtil.getInstance(context).saveFloat(KEY_CUR_DAY_UPLOAD_LISTENED_DURATION, 0);
        MmkvCommonUtil.getInstance(context).saveLong(KEY_CUR_DAY_UPLOAD_SYSTEM_TIME, 0);

        MmkvCommonUtil.getInstance(context).saveLong(KEY_UPLOAD_TRACK_ID, 0);
    }


    private static void addUbtV2Data(XMTraceApi.Trace trace, Map<String, String> ubtV2) {
        if (ubtV2 == null || ubtV2.isEmpty() || trace == null) {
            return;
        }

        Set<Map.Entry<String, String>> entries = ubtV2.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            trace.put(entry.getKey(), entry.getValue());
        }
    }
}
