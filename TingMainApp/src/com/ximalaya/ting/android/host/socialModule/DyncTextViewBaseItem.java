package com.ximalaya.ting.android.host.socialModule;

import com.ximalaya.ting.android.host.model.community.DyncInteractiveSpan;

import java.util.List;

/**
 * Created by zhangqianghua on 2022/1/24
 *
 * @phoneNumber 17621920801
 * @Description 这是描述
 * @wiki Wiki网址放在这里
 */
public abstract class DyncTextViewBaseItem extends BaseDyncItemView {
    public static final String PARAMS_TOPIC_ITING = "params_topic_iting";

    public static final String PARAMS_SEARCH_KEYWORD = "params_search_keyword";

    public static final String PARAMS_TOPIC_RECOMMEND_OR_NEW = "params_topic_recommend_or_new";

    public static final String PARAMS_TOPIC_CIRCLE_ID = "params_topic_circle_id";

    public static final String PARAMS_TOPIC_FEED_ID = "params_topic_feed_id";

    public static class TextItemData {
        public int font;//字体大小
        public String color;//字体颜色
        public int row;//最大行数
        public String content;
        public List<DyncInteractiveSpan> interactiveSpans;
    }
}
