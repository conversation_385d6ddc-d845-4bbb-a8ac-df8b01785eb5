package com.ximalaya.ting.android.host.socialModule.imageviewer.util;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.fixtoast.ToastCompat;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.ImageExt;
import com.ximalaya.ting.android.host.socialModule.imageviewer.ViewerContextProvider;
import com.ximalaya.ting.android.host.socialModule.imageviewer.transaction.TransitionParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.UUID;

import androidx.core.util.Pair;

/**
 * Created by qianmenchao on 2018/12/27.
 *
 * <AUTHOR>
 */
public class FileUtil {

    public static final String TAG = "xm_viewer";
    private static final String JPG = ".jpg";
    private static final String JPEG = ".jpeg";
    private static final String PNG = ".png";
    private static final String BMP = ".bmp";
    private static final String WEBP = ".webp";

    private static File fileIsExistCreate(String Path) {
        File file = new File(Path);
        if (!file.exists()) {
            try {
                File parentFile = file.getParentFile();
                if (parentFile != null && !parentFile.exists()) {
                    boolean res = parentFile.mkdirs();
                    if (!res) {
                        return null;
                    }
                }
                boolean res = file.createNewFile();
                if (!res) {
                    return null;
                }
            } catch (Exception var3) {
                var3.printStackTrace();
            }
        }

        return file;
    }

    private static String checkPicName(String url) {
        if (TextUtils.isEmpty(url)) {
            return Utils.md5(String.valueOf(System.currentTimeMillis()));
        }
        String endFix = JPG;

        int index = url.lastIndexOf(".");
        if (index > 0) {
            String temEndFix = url.substring(index);
            if (JPG.equalsIgnoreCase(temEndFix)) {
                endFix = JPG;
            } else if (JPEG.equalsIgnoreCase(temEndFix)) {
                endFix = JPEG;
            } else if (PNG.equalsIgnoreCase(temEndFix)) {
                endFix = PNG;
            } else if (BMP.equalsIgnoreCase(temEndFix)) {
                endFix = BMP;
            } else if (WEBP.equalsIgnoreCase(temEndFix)) {
                endFix = WEBP;
            }
        }

        return Utils.md5(url) + endFix;
    }

    private static void saveToContextProvider(Context context, String path, Bitmap bitmap) {
        if (context == null || TextUtils.isEmpty(path)) {
            return;
        }

        Logger.d(TAG, "writeImageToContextProvider path = " + path);

        ContentValues values = new ContentValues(4);
        values.put(MediaStore.Images.Media.DATA, path);
        values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
        values.put(MediaStore.Images.Media.WIDTH, bitmap != null ? bitmap.getWidth() : 0);
        values.put(MediaStore.Images.Media.HEIGHT, bitmap != null ? bitmap.getHeight() : 0);

        ContentResolver content = context.getContentResolver();

        String state = Environment.getExternalStorageState();
        Uri uri;
        if (state.equals(Environment.MEDIA_MOUNTED)) {
            Logger.d(TAG, "writeImageToContextProvider EXTERNAL_CONTENT_URI path = " + path);
            uri = content.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
            if (uri != null) {
                Logger.d(TAG, "writeImageToContextProvider EXTERNAL_CONTENT_URI uri = " + uri.toString());
            }
        } else {
            Logger.d(TAG, "writeImageToContextProvider INTERNAL_CONTENT_URI path = " + path);
            uri = content.insert(MediaStore.Images.Media.INTERNAL_CONTENT_URI, values);
            if (uri != null) {
                Logger.d(TAG, "writeImageToContextProvider INTERNAL_CONTENT_URI uri = " + uri.toString());
            }
        }
        context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, ProviderUtil.fromFile(new File(path))));
    }

    public static void download(Bitmap bitmap, String url, boolean isGif) {
        if (bitmap == null || bitmap.getWidth() == 0 || bitmap.getHeight() == 0 || TextUtils.isEmpty(url)) {
            return;
        }
        new SaveAsyncTask(bitmap, url, isGif).myexec();
    }

    public static boolean isImageSaved(String url, boolean isGif) {
        String fileName = checkPicName(url);
        if (isGif) {
            if (fileName == null) {
                fileName = UUID.randomUUID().toString();
            }
            int index = fileName.lastIndexOf('.');
            if (index == -1) {
                fileName = fileName + ".gif";
            } else {
                fileName = fileName.substring(0, index) + ".gif";
            }
            String path = getPicturePath() + fileName;

            File gif = new File(path);
            return gif.exists();
        } else {
            String path = getPicturePath() + fileName;
            return new File(path).exists();
        }
    }

    /**
     * 需图片保存的地址
     */
    private static String getPicturePath() {
        Activity activity = BaseApplication.getTopActivity();
        String appName;
        try {
            PackageManager packageManager = activity.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(activity.getPackageName(), 0);
            int labelRes = packageInfo.applicationInfo.labelRes;
            appName = activity.getResources().getString(labelRes);
        } catch (Exception e) {
            e.printStackTrace();
            appName = "喜马拉雅";
        }
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES) + "/" + appName + "/";
    }

    private static void saveGif(String url) {
        String fileName = checkPicName(url);
        if (fileName == null) {
            fileName = UUID.randomUUID().toString();
        }
        int index = fileName.lastIndexOf('.');
        if (index == -1) {
            fileName = fileName + ".gif";
        } else {
            fileName = fileName.substring(0, index) + ".gif";
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ImageExt.saveGifAndroidQ(MainApplication.getMyApplicationContext().getContentResolver(), url, fileName);
        } else {
            FileInputStream fis = null;
            FileOutputStream fos = null;

            try {
                String path = getPicturePath() + fileName;
                File gif = new File(url);
                if (gif.exists()) {
                    File mFile;
                    mFile = fileIsExistCreate(path);
                    if (mFile == null) {
                        return;
                    }
                    fis = new FileInputStream(gif);
                    fos = new FileOutputStream(mFile);

                    int len;
                    byte[] buffer = new byte[512];
                    while ((len = fis.read(buffer)) > 0) {
                        fos.write(buffer, 0, len);
                    }
                    Activity activity = BaseApplication.getTopActivity();
                    if (activity != null) {
                        saveToContextProvider(activity.getApplicationContext(), path, null);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (fis != null) {
                        fis.close();
                    }
                    if (fos != null) {
                        fos.close();
                    }
                } catch (Exception ignored) {
                }
            }
        }

    }

    public static boolean isImageFileExistInCache(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        if (!url.startsWith("http")) {
            File file = new File(url);
            return file.exists();
        }
        String localPath = ViewerContextProvider.getPicassoCache().findImageSavePathFromImageLoader(url);
        if (TextUtils.isEmpty(localPath)) {
            return false;
        }
        File file = new File(localPath);
        return file.exists();
    }

    private static void writeBitmapToFile(Bitmap bitmap, String file, String key) {
        OutputStream out;
        Bitmap.CompressFormat mCompressFormatJPG = Bitmap.CompressFormat.JPEG;
        Bitmap.CompressFormat mCompressFormatPNG = Bitmap.CompressFormat.PNG;
        int mCompressQuality = 70;
        int IO_BUFFER_SIZE = 4 * 1024;
        try {
            out = new BufferedOutputStream(new FileOutputStream(file), IO_BUFFER_SIZE);
            if (bitmap == null || TextUtils.isEmpty(file)) {
                return;
            }
            if (key.contains(".png")) {
                bitmap.compress(mCompressFormatPNG, mCompressQuality, out);
            } else {
                bitmap.compress(mCompressFormatJPG, mCompressQuality, out);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean findPreViewUrlCacheFromOkHttpCache(String url) {
        if (url == null || !url.startsWith("http") || !url.startsWith("https")) {
            return false;
        }
        Activity activity = BaseApplication.getTopActivity();
        if (activity == null || ViewerContextProvider.getPicassoCache() == null) {
            return false;
        }
        return ViewerContextProvider.getPicassoCache().findImageFileStreamFromOkHttpCache(url) != null;
    }

    public static void findPreViewDrawableCache(TransitionParams params) {
        if (params == null || TextUtils.isEmpty(params.mPreViewUrl)) {
            return;
        }
        getPreViewDrawableRealSize(params);
    }

    private static void getPreViewDrawableRealSize(TransitionParams params) {
        Pair<Integer, Integer> out = ViewerContextProvider.getPicassoCache().decodePicOutWidthAndHeight(params.mPreViewUrl);
        if (out == null || out.first == null || out.second == null) {
            return;
        }
        params.newDrawableWidth = out.first;
        params.newDrawableHeight = out.second;
    }

    public static void getThumbDrawableRealSize(TransitionParams params) {
        if (params == null || TextUtils.isEmpty(params.mSourceUrl)) {
            return;
        }
        Activity activity = BaseApplication.getTopActivity();
        if (activity == null) {
            return;
        }
        int screenWidth = BaseUtil.getScreenWidth(activity);
        int screenHeight = BaseUtil.getScreenHeight(activity);
        if (!params.mSourceUrl.startsWith("http")) {
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(params.mSourceUrl, options);
            params.sourceDrawableWidth = Math.min(options.outWidth, screenWidth);
            params.sourceDrawableHeight = Math.min(options.outHeight, screenHeight);
            if (params.sourceDrawableWidth > 0 && params.sourceDrawableHeight > 0) {
                return;
            }
        }

        Pair<Integer, Integer> out = ViewerContextProvider.getPicassoCache().decodePicOutWidthAndHeight(params.mSourceUrl);
        if (out == null || out.first == null || out.second == null) {
            return;
        }
        params.sourceDrawableWidth = out.first;
        params.sourceDrawableHeight = out.second;
    }

    private static class SaveAsyncTask extends MyAsyncTask<Void, Void, Boolean> {

        private final Bitmap bitmap;
        private final String url;
        private final boolean isGif;

        private SaveAsyncTask(Bitmap bitmap, String url, boolean isGif) {
            this.bitmap = bitmap;
            this.url = url;
            this.isGif = isGif;
        }

        @Override
        protected void onPostExecute(Boolean aBoolean) {
            String toast;
            if (aBoolean != null && aBoolean) {
                toast = "图片已保存";
            } else {
                toast = "图片保存失败";
            }

            ToastCompat.makeText(BaseApplication.getMyApplicationContext(), toast, Toast.LENGTH_SHORT).show();
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            String fileName = checkPicName(url);
            Log.d(TAG, "doInBackground: url " + url);
            if (isGif) {
                saveGif(url);
            } else {
                String path = getPicturePath() + fileName;
                path = path.replace("\\u0000", "");
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    ImageExt.saveToAlbum(bitmap, ToolUtil.getCtx(), fileName, path, 70);
                } else {
                    fileIsExistCreate(path);
                    writeBitmapToFile(bitmap, path, fileName);
                    Activity activity = BaseApplication.getTopActivity();
                    if (activity != null) {
                        saveToContextProvider(activity.getApplicationContext(), path, bitmap);
                    }
                }
            }
            return true;
        }
    }

    private static class ProviderUtil {

        private static final String FILE_PROVIDER_HOST =
            MainApplication.getMyApplicationContext().getPackageName() + ".fileprovider";

        private static Uri fromFile(File file) {
            Context context = MainApplication.getMyApplicationContext();
            Uri fileUri;
            if (Build.VERSION.SDK_INT >= 24) {
                fileUri = fromFileFor24(context, file);
            } else {
                fileUri = Uri.fromFile(file);
            }
            return fileUri;
        }

        private static Uri fromFileFor24(Context context, File file) {
            return androidx.core.content.FileProvider.getUriForFile(context, FILE_PROVIDER_HOST, file);
        }
    }

}
