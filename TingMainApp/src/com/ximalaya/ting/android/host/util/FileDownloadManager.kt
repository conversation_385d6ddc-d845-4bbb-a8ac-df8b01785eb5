package com.ximalaya.ting.android.host.util;

import android.text.TextUtils;
import com.ximalaya.ting.android.framework.service.DownloadService;
import com.ximalaya.ting.android.framework.util.FileUtil;
import com.ximalaya.ting.android.host.manager.record.BaseDownloadTask;
import com.ximalaya.ting.android.host.manager.record.DownloadManager;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import java.io.File;

/**
 * 通用文件下载管理器
 * 支持下载任意类型的文件，包括视频、图片、音频等
 */
object FileDownloadManager {

    private const val TAG = "FileDownloadManager"
    private const val DEFAULT_DIR = "download"
    private const val MIN_REQUIRED_SPACE = 10 * 1024 * 1024L // 10MB

    /**
     * 下载文件
     *
     * @param fileUrl 文件URL
     * @param callBack 下载结果回调
     * @param dirName 下载目录名称，默认为"download"
     * @param fileName 文件名，默认根据URL生成
     */
    @JvmStatic
    @JvmOverloads
    fun downloadFile(
        fileUrl: String,
        callBack: IDataCallBack<String>?,
        dirName: String = DEFAULT_DIR,
        fileName: String? = null
    ) {
        // 检查URL是否为空
        if (TextUtils.isEmpty(fileUrl)) {
            callBack?.onError(-1, "文件URL为空")
            return
        }

        // 检查上下文是否有效
        val ctx = ToolUtil.getCtx() ?: run {
            callBack?.onError(-3, "获取上下文失败")
            return
        }

        // 如果未指定文件名，则根据URL生成
        val finalFileName = fileName ?: generateFileName(fileUrl)
        if (TextUtils.isEmpty(finalFileName)) {
            callBack?.onError(-4, "生成文件名失败")
            return
        }

        // 获取并检查目录路径
        val basePath = DownloadService.getDiskCachePath(ctx)
        if (TextUtils.isEmpty(basePath)) {
            callBack?.onError(-5, "获取存储路径失败")
            return
        }
        val dirPath = File(basePath, dirName).absolutePath

        // 检查文件是否已存在
        val file = File(dirPath, finalFileName)
        if (file.exists()) {
            callBack?.onSuccess(file.absolutePath)
            return
        }

        // 检查存储空间是否足够
        try {
            val availableSpace = FileUtil.getAvailableExternalMemorySize()
            if (availableSpace < 0 || availableSpace < MIN_REQUIRED_SPACE) {
                callBack?.onError(-2, "存储空间不足，需要至少10MB")
                return
            }
        } catch (e: Exception) {
            callBack?.onError(-6, "检查存储空间失败")
            return
        }

        // 创建目录
        val dir = File(dirPath)
        if (!dir.exists() && !dir.mkdirs()) {
            callBack?.onError(-7, "无法创建下载目录")
            return
        }
        if (!dir.isDirectory) {
            callBack?.onError(-8, "下载路径不是有效的目录")
            return
        }

        // 检查下载管理器是否可用
        val downloadManager = DownloadManager.getInstance() ?: run {
            callBack?.onError(-9, "下载管理器未初始化")
            return
        }

        // 创建下载任务
        val task = object : BaseDownloadTask() {
            override fun getDownloadUrl(): String = fileUrl
            override fun getLocalPath(): String = dirPath
            override fun getLocalName(): String = finalFileName
            override fun isRefresh(): Boolean = false

            override fun handleStartDownload() {}
            override fun handleStopDownload() {}
            override fun handleUpdateDownload(curr: Long, total: Long) {}

            override fun handleCompleteDownload() {
                if (file.exists() && file.length() > 0) {
                    callBack?.onSuccess(file.absolutePath)
                } else {
                    callBack?.onError(-10, "下载完成但文件无效")
                }
            }

            override fun handleDownloadError(e: Exception?, what: Int, extra: Int) {
                callBack?.onError(-11, "下载失败: ${e?.message ?: "未知错误"}")
            }
        }

        try {
            downloadManager.download(task, true)
        } catch (e: Exception) {
            callBack?.onError(-12, "提交下载任务失败")
        }
    }

    /**
     * 简化的文件名生成逻辑
     */
    private fun generateFileName(fileUrl: String): String {
        try {
            // 从URL提取文件名和扩展名
            var fileName = fileUrl.substringAfterLast('/').substringBefore('?')

            // 如果提取的文件名过于简单，使用哈希值确保唯一性
            if (fileName.length < 3 || !fileName.contains(".")) {
                val hash = fileUrl.hashCode().toString().replace("-", "")
                val ext = when {
                    fileUrl.contains(".mp4", true) -> ".mp4"
                    fileUrl.contains(".webp", true) -> ".webp"
                    fileUrl.contains(".jp", true) -> ".jpg"
                    fileUrl.contains(".png", true) -> ".png"
                    fileUrl.contains(".mp3", true) -> ".mp3"
                    else -> ".bin"
                }
                return hash + ext
            }

            // 确保文件名安全（移除特殊字符）
            return fileName.replace(Regex("[^a-zA-Z0-9_.-]"), "_")
        } catch (e: Exception) {
            // 异常情况下使用时间戳确保唯一性
            return System.currentTimeMillis().toString() + ".bin"
        }
    }
}
