package com.ximalaya.ting.android.host.util

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.animation.ValueAnimator
import android.content.ComponentCallbacks
import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Typeface
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.Keep
import androidx.cardview.widget.CardView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.easyfloat.utils.DisplayUtils
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.fragment.ManageFragment
import com.ximalaya.ting.android.framework.service.DownloadService
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.framework.util.ViewUtil.IOnDialogShowStateChange
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.fragment.IQuickListenTabFragment
import com.ximalaya.ting.android.host.listener.IMainTabClickCallback
import com.ximalaya.ting.android.host.manager.QuickListenFrom
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.record.BaseDownloadTask
import com.ximalaya.ting.android.host.manager.record.DownloadManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager
import com.ximalaya.ting.android.host.util.HomePageQuickListenGuideUtil.OPEN_QUICK_LISTEN_ITING
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.kt.isAvailable
import com.ximalaya.ting.android.host.view.XmLottieAnimationView
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.AsyncGson
import com.ximalaya.ting.android.opensdk.util.AsyncGson.IResult
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.read.utils.LogUtils
import com.ximalaya.ting.android.read.utils.checkActivity
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONObject
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.lang.ref.SoftReference
import java.lang.ref.WeakReference
import kotlin.math.max
import androidx.core.net.toUri

object HomePageQuickListenGuideUtil {

    public const val TAG = "HomePageQuickListenGuideUtil"
    var OPEN_QUICK_LISTEN_ITING = "iting://open?msg_type=616&source=${QuickListenFrom.BOTTOM_TAB.from}"

    private var curViewRefresh: SoftReference<View>? = null
    private var isShowed = false
    private const val KEY_SHOW_DATE_RECORD = "key_show_quick_listen_guide_record"
    private var mData:JSONObject? = null
    private val FILE_DIR_PATH: String =
        DownloadService.getDiskCachePath(MainApplication.getMyApplicationContext()) + File.separator + "ql_guide"

    //    private const val autoDismissInterval = 5 * 1000L
    private var xmRequestId = ""

    private var fileInputStream:FileInputStream? = null

    private var mQuickListenTipsView: WeakReference<View>? = null

    private const val NEW_COMER_TYPE = "newComer"

    private var isRequested = false

    private fun requestShowGuideData(callback: IDataCallBack<JSONObject?>) {
        val map = mutableMapOf<String, String>()
        val recordDay = MMKVUtil.getInstance().getString(
            PreferenceConstantsInHost.KEY_OPEN_QUICK_LISTEN_DAY)
        map["isEnter"] = (if (recordDay == DateTimeUtil.getNowDateStr()) 1 else 0).toString()
        CommonRequestM.baseGetRequest(
            UrlConstants.getInstanse().quickListenGuideUrl, map, callback
        ) {
            try {
                JSONObject(it)
            } catch (e: Exception) {
                null
            }
        }
    }
    private var mComponentCallbacksRef: WeakReference<ComponentCallbacks?>? = null

    private fun reportGuideShow(jsonObject: JSONObject?) {
        jsonObject?:return
        val postStr = JSONObject()

        postStr.put("bizType", jsonObject.optString("bizType"))
        postStr.put("uiType", jsonObject.optString("uiType"))
        postStr.put("contentType", jsonObject.optString("contentType"))
        postStr.put("event", "expose")
        CommonRequestM.basePostRequestJsonStr(UrlConstants.getInstanse()
            .reportQuickListenGuideShowUrl(),
            postStr.toString(),
            object : IDataCallBack<String> {
                override fun onSuccess(data: String?) {
                }

                override fun onError(code: Int, message: String?) {
                }

            }) {
            it
        }
    }

    private fun reportGuideShow(bizType: String?, uiType: String?, contentType: String?) {
        val postStr = JSONObject()

        postStr.put("bizType", bizType)
        postStr.put("uiType", uiType)
        postStr.put("contentType", contentType)
        postStr.put("event", "expose")
        CommonRequestM.basePostRequestJsonStr(UrlConstants.getInstanse()
            .reportQuickListenGuideShowUrl(),
            postStr.toString(),
            object : IDataCallBack<String> {
                override fun onSuccess(data: String?) {
                }

                override fun onError(code: Int, message: String?) {
                }

            }) {
            it
        }
    }


    fun checkShowGuide(
        activity: FragmentActivity?,
        itemView: View?,
    ) {
        if(ConfigureCenter.getInstance().getInt(CConstants.Group_android.GROUP_NAME, "show_quick_listen_guide_switch", 0) == 2){
            // 控制中心控制关闭展示弹窗
            return
        }
        if (!activity.checkActivity() || itemView == null) {
            Logger.d(TAG, "itemView:$itemView")
            return
        }

        if (ToolUtil.getMainActivity()?.quickListenRadioButton?.isChecked == true) {
            return
        }

        if (isRequested) {
            return
        }

        isRequested = true
        val context = BaseApplication.getMyApplicationContext()

        Logger.d(TAG, "requestShowGuideData")
        requestShowGuideData(object : IDataCallBack<JSONObject?> {
            override fun onSuccess(data: JSONObject?) {
                Logger.d(TAG, "requestShowGuideData:$data")
                xmRequestId = XmRequestIdManager.getInstance(context).requestId

                if (ToolUtil.getMainActivity()?.quickListenRadioButton?.isChecked == true) {
                    return
                }

                data?.optJSONObject("data")?.let {
                    if (NEW_COMER_TYPE == it.optString("bizType")
                        && UiType.FLOAT_LAYER.value == it.optString("uiType")
                    ) {
                        val url = it.optJSONObject("guideInfo")?.optString("url")
                        if (!url.isNullOrEmpty()) {
                            mData = it
                            preLoadDialogJsonFile(
                                activity, itemView, url
                            )
                        }
                    } else {
                        showOtherTips(it, itemView)
                    }
                }
            }

            override fun onError(code: Int, message: String?) {
                Logger.d(TAG, "requestShowGuideData:code$code, message$message")
            }

        })
    }

    private fun showOtherTips(jsonObject: JSONObject?, itemView: View) {
        jsonObject?.let {
            AsyncGson<GuideData>().fromJson(jsonObject.toString(),
                GuideData::class.java,
                object : IResult<GuideData> {
                override fun postResult(result: GuideData?) {
                    result?.let {
                        if (it.uiType == UiType.BUBBLE.value
                            || it.uiType == UiType.BUBBLE_WITH_RED_DOT.value) {
                            showBubble(result, itemView)
                        } else if (it.uiType == UiType.RED_DOT.value) {
                            showRod(result, false)
                        }
                    }
                }

                override fun postException(e: java.lang.Exception?) {
                }
            })
        }
    }

    private var onDialogShowStateChange: IOnDialogShowStateChange? = null
    private fun onDialogChange(dialogHide: Boolean, guideData: GuideData, itemView: View): Boolean {
        if (!dialogHide) {
            if (onDialogShowStateChange != null) {
                ViewUtil.removeDialogShowStateChange(onDialogShowStateChange)
                onDialogShowStateChange = null
            }
            showBubble(guideData, itemView)
            return true
        } else {
            return false
        }
    }

    private fun showBubble(guideData: GuideData, itemView: View) {
        val rect = Rect()
        itemView.getGlobalVisibleRect(rect)

        if (rect.isEmpty || rect.top == 0) {
            Logger.log("HomePageQuickListenGuideUtil : showBubble 未获取到菜单位置")
            return
        }

        val activity = MainApplication.getMainActivity() as? FragmentActivity ?: return

        // 如果快听已经在展示了不显示,这个tips
        if ((activity as? MainActivity)?.manageFragment != null) {
            val stacks = activity.manageFragment!!.mStacks
            if (stacks != null) {
                for (i in stacks.indices) {
                    val softReference = stacks[i]
                    if (softReference != null) {
                        val fragment = softReference.get()
                        if (fragment is IQuickListenTabFragment) {
                            return
                        }
                    }
                }
            }
        }

        if (ToolUtil.getMainActivity()?.quickListenRadioButton?.isChecked == true) {
            return
        }

        if (ViewUtil.haveDialogIsShowing(activity)) {
            Logger.d(TAG, "haveDialogIsShowing:true")

            onDialogShowStateChange = IOnDialogShowStateChange {
                onDialogChange(it, guideData, itemView)
            }
            ViewUtil.addDialogShowStateChange(onDialogShowStateChange)
            return
        }

        ViewUtil.setHasDialogShow(true)

        val centerX = (rect.right + rect.left) / 2

        val viewStub = activity.findViewById<ViewStub>(R.id.host_quick_listen_tip_vs)
        if (viewStub != null) {
            mQuickListenTipsView = WeakReference(viewStub.inflate())
        }

        if (mQuickListenTipsView == null) {
            return
        }

        Logger.log("HomePageQuickListenGuideUtil : showBubble")

        // 如果已经跳转到其他页面, 或者不在首页tab,直接显示 pop
        if (((activity as? MainActivity)?.manageFragment?.mStacks?.size ?: 0) > 0
            || (activity as? MainActivity)?.isHomePageFragmentOnTop != true) {
            hideBubble()

            if (guideData.uiType == UiType.BUBBLE_WITH_RED_DOT.value) {
                showRod(guideData, false)
            }
        }

        mQuickListenTipsView?.get()?.let {
            it.setTag(R.id.host_id_recommend_show_tag_cache, guideData)
            mQuickListenTipsView = WeakReference(it)
            val arrowView = it.findViewById<View>(R.id.host_quick_listen_arrow)
            val close = it.findViewById<View>(R.id.host_quick_listen_close)
            val tipTitle = it.findViewById<TextView>(R.id.host_quick_listen_tip_title)

            val arrowViewMarginStart = centerX - 22.dp
            arrowView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                marginStart = arrowViewMarginStart
            }

            close.setOnClickListener { closeView ->
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                hideBubble()
            }

            val paint = Paint()
            paint.textSize = 12f * ToolUtil.getCtx().resources.displayMetrics.density // 设置字体大小，单位是sp
            paint.typeface = Typeface.DEFAULT // 设置字体

            val text = if (guideData.guideInfo?.title.isAvailable()) guideData.guideInfo?.title else "点击收听今日内容速递"
            val textWidth = paint.measureText(text) + 32.dp + 12.dp

            val tipViewMarginStart = max(0f, centerX - 16.dp - textWidth / 2)
            tipTitle.updateLayoutParams<ConstraintLayout.LayoutParams> {
                marginStart = tipViewMarginStart.toInt()
            }
            tipTitle.text = text

            it.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                hideBubble()
                tracePopClick(xmRequestId, guideData.bizType, guideData.uiType, text ?: "")

                var landingPage = guideData.guideInfo?.getAvailableLandingPage()
                if (QuickListenTabAbManager.isNewUserForQuickListen) {
                    runCatching {
                        landingPage?.let {
                            landingPage = it.toUri().buildUpon().appendQueryParameter("focusTab", "true").toString()
                        }
                    }
                }

                ToolUtil.clickUrlAction(activity as? MainActivity, landingPage, null)
            }

            listenStackChangeOrTabClick()

            HandlerManager.removeCallbacks(mHideBubbleRunnable)
            mHideBubbleRunnable = Runnable {
                mHideBubbleRunnable = null
                removeListenStackChangeOrTabClick()
                hideBubble()
                if (guideData.uiType == UiType.BUBBLE_WITH_RED_DOT.value) {
                    showRod(guideData, true)
                }
            }
            HandlerManager.postOnUIThreadDelay4Kt(guideData.guideInfo?.showTime ?: 5000, mHideBubbleRunnable)

            tracePopShow(xmRequestId, guideData.bizType, guideData.uiType, guideData.guideInfo?.title ?: "")
            reportGuideShow(guideData.bizType, guideData.uiType, guideData.contentType)
        }
    }

    private fun listenStackChangeOrTabClick() {
        val activity = MainApplication.getMainActivity() as? MainActivity ?: return
        activity.manageFragment?.addStackChangeListener(mFragmentStackChange)
        activity.addMainTabClickCallback(mainTabClick)

    }

    private fun removeListenStackChangeOrTabClick() {
        val activity = MainApplication.getMainActivity() as? MainActivity ?: return
        activity.manageFragment?.removeStackChangeListener(mFragmentStackChange)
        activity.removeMainTabClickCallback(mainTabClick)
    }

    private val mFragmentStackChange = object : ManageFragment.StackChangeListener {
        override fun onEntryAdd(fragment: Fragment?) {
            val activity = MainApplication.getMainActivity() as? MainActivity ?: return
            if ((activity.manageFragment?.mStacks?.size ?: 0) > 0) {
                if (fragment !is IQuickListenTabFragment) {
                    mHideBubbleRunnable?.run()
                }
            }
        }

        override fun onEntryRemove(fragment: Fragment?) {
        }
    }

    private val mainTabClick = object : IMainTabClickCallback {
        override fun onCheckedChanged(checkedId: Int) {
            val activity = MainApplication.getMainActivity() as? MainActivity ?: return
            if (checkedId != activity.quickListenRadioButton?.id) {
                mHideBubbleRunnable?.run()
            }
        }
    }

    private var mHideBubbleRunnable: Runnable? = null

    fun hasBubbleShow(): Boolean {
        return mQuickListenTipsView?.get()?.visibility == View.VISIBLE
    }

    fun openBubbleLandingPage(): String? {
        hideBubble()
        hideRod()
        mHideBubbleRunnable = null

        (mQuickListenTipsView?.get()?.getTag(R.id.host_id_recommend_show_tag_cache) as? GuideData)?.let {
            mQuickListenTipsView?.get()?.setTag(R.id.host_id_recommend_show_tag_cache, null)
            tracePopClick(xmRequestId, it.bizType, it.uiType, it.guideInfo?.title ?: "", false)
            val availableLandingPage = it.guideInfo?.getAvailableLandingPage()
            return availableLandingPage
        }
        return null
    }

    fun openRodLandingPage(): String? {
        hideBubble()
        hideRod()
        mHideBubbleRunnable = null

        (mQuickListenTipsView?.get()?.getTag(R.id.host_id_recommend_show_tag_cache) as? GuideData)?.let {
            mQuickListenTipsView?.get()?.setTag(R.id.host_id_recommend_show_tag_cache, null)
            tracePopClick(xmRequestId, it.bizType, it.uiType, it.redDot?.title ?: "", false)
            val availableLandingPage = it.redDot?.getAvailableLandingPage()
            return availableLandingPage
        }
        return null
    }

    fun hideBubble() {
        HandlerManager.removeCallbacks(mHideBubbleRunnable)
        mHideBubbleRunnable = null
        mQuickListenTipsView?.get()?.visibility = View.GONE
        ViewUtil.setHasDialogShow(false)
    }

    private fun showRod(guideData: GuideData, isFromPop: Boolean) {
        val activity = MainApplication.getMainActivity() as? FragmentActivity ?: return
        if (ToolUtil.getMainActivity()?.quickListenRadioButton?.isChecked == true) {
            return
        }

        (activity as? MainActivity)?.setQuickListenRedContent(guideData.redDot?.title)
        tracePopShow(xmRequestId, guideData.bizType, guideData.uiType, guideData.redDot?.title ?: "", isFromPop)
        if (!isFromPop) {
            reportGuideShow(guideData.bizType, guideData.uiType, guideData.contentType)
        }
        HandlerManager.removeCallbacks(mHideRod)
        HandlerManager.postOnUIThreadDelay4Kt(guideData.redDot?.showTime ?: 60_000, mHideRod)
    }

    private val mHideRod = Runnable { hideRod() }

    fun hideRod() {
        HandlerManager.removeCallbacks(mHideRod)
        val activity = MainApplication.getMainActivity() as? FragmentActivity ?: return
        (activity as? MainActivity)?.setQuickListenRedContent(null)
    }

    private fun getCompatScreenWidth(context: Context): Int {
        return if (BaseUtil.isFoldScreen(context) && BaseUtil.dp2px(
                context,
                600f
            ) <= BaseUtil.getScreenWidth(context)
        ) {
            BaseUtil.dp2px(context, 375f)
        } else {
            BaseUtil.getScreenWidth(context)
        }
    }

    private fun checkShowGuideInner(
        activity: FragmentActivity?,
        itemView: View,
        filePath: String,
    ): Boolean {
        val context = BaseApplication.getMyApplicationContext()
        val rect = Rect()
        itemView.getGlobalVisibleRect(rect)
        if (rect.isEmpty || rect.top == 0) {
            LogUtils.d(TAG, "未获取到菜单位置 $rect")
            return false
        }
        val jsonFile = File(filePath)
        if (!jsonFile.exists() || !jsonFile.isFile) {
          return false
        }

        if (activity is MainActivity) {
            if (!activity.isManagerEmpty) {
                Logger.d(
                    TAG, "有别的界面 不显示引导:${activity.manageFragment?.currentFragment}"
                )
                return false
            }

            if (ToolUtil.getMainActivity()?.quickListenRadioButton?.isChecked == true) {
                return false
            }

            val tabFragmentManager = activity.tabFragmentManager
            if (tabFragmentManager != null) {
                if (tabFragmentManager.currentTab != TabFragmentManager.TAB_HOME_PAGE) {
                    Logger.d(
                        TAG,
                        "不在我页界面 不显示引导:${tabFragmentManager.currFragment}"
                    )
                    return false
                }
            }
        }

        if (ViewUtil.haveDialogIsShowing(activity)) {
            Logger.d(TAG, "haveDialogIsShowing:true")
            return false
        }

        val currentDate = DateTimeUtil.getToday()
        val lastShowDate = MmkvCommonUtil.getInstance(context).getString(KEY_SHOW_DATE_RECORD, "")
        val debugMode = ToolUtil.getDebugSystemProperty("debug.mark.showGuide", "0") == "2"
        if (!debugMode && currentDate == lastShowDate && !lastShowDate.isNullOrEmpty()) {
            Logger.d(TAG, "一个自然日最多展示一次")
            return false
        }

        try {
            ViewUtil.setHasDialogShow(true)
            val rootView = activity!!.window.decorView as ViewGroup
            val childView = LayoutInflater.from(context)
                .inflate(R.layout.host_layout_quick_listen_guide, rootView, false)
            curViewRefresh = SoftReference(childView)
            val maskView = childView.findViewById<View>(R.id.host_mask_view)
            val bottomTab = childView.findViewById<ImageFilterView>(R.id.host_iv_bottom_tab)
            val ivArrowTop = childView.findViewById<ImageView>(R.id.host_iv_arrow_top)
            val cardView = childView.findViewById<CardView>(R.id.host_card_lottie)
            val bottomTabLeftSpace = childView.findViewById<View>(R.id.host_view_bottom_left_space)
            val ivBgView = childView.findViewById<XmLottieAnimationView>(R.id.host_iv_lottie)
            val ivCloseView = childView.findViewById<View>(R.id.host_iv_close)
            try {
                fileInputStream = FileInputStream(jsonFile)
                ivBgView?.setAnimation(fileInputStream, "quick_listen_guide")
            } catch (e: IOException) {
                e.printStackTrace()
            }
            val updateLayoutParamsRunnable = Runnable {
                itemView.getGlobalVisibleRect(rect)
                val halfRound = BaseUtil.dp2px(context, 26f)
                val centerY = (rect.bottom + rect.top) / 2
                val centerX = (rect.right + rect.left) / 2
                rect.top = centerY - halfRound
                rect.bottom = centerY + halfRound
                rect.left = centerX - halfRound
                rect.right = centerX + halfRound
                Logger.d(
                    TAG, "itemView.rect:$rect"
                )
                cardView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    width = getCompatScreenWidth(context) - BaseUtil.dp2px(context, 32f)
                }
                bottomTabLeftSpace.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    if (BaseUtil.isFoldScreen(context) && BaseUtil.dp2px(
                            context,
                            600f
                        ) <= BaseUtil.getScreenWidth(context)
                    ) {
                        width = BaseUtil.dp2px(context, 70f)
                    } else {
                        width = centerX - BaseUtil.dp2px(context, 31f) - BaseUtil.dp2px(
                            context,
                            16f
                        )
                    }
                }
                val bottomTabParams = bottomTab.layoutParams as ConstraintLayout.LayoutParams
                bottomTabParams.leftMargin = centerX - BaseUtil.dp2px(context, 31f)
                Logger.d(TAG, "screenHeight:${BaseUtil.getScreenHeight(context)} ,rect:$rect")
                bottomTabParams.bottomMargin = DisplayUtils.getNavigationBarCurrentHeight(context)
                bottomTab.layoutParams = bottomTabParams
            }
            updateLayoutParamsRunnable.run()
            val callbacks = object : ComponentCallbacks {
                override fun onConfigurationChanged(newConfig: Configuration) {
                    Logger.d(
                        TAG, "onScreenWidthChanged"
                    )
                    childView?.postDelayed(updateLayoutParamsRunnable, 16)
                }

                override fun onLowMemory() {
                    removeGuide()
                }

            }
            mComponentCallbacksRef = WeakReference(callbacks)
            ToolUtil.getCtx()?.registerComponentCallbacks(callbacks)
            maskView.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                removeGuide()
            }
            bottomTab.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                tracePopClick(xmRequestId, NEW_COMER_TYPE, UiType.FLOAT_LAYER.value)
                removeGuide()
                QuickListenTabAbManager.go2QuickListenMainPage(
                    activity as? MainActivity, QuickListenFrom.BOTTOM_TAB.from, null
                )
            }
            cardView.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                tracePopClick(xmRequestId, NEW_COMER_TYPE, UiType.FLOAT_LAYER.value)
                removeGuide()
                QuickListenTabAbManager.go2QuickListenMainPage(
                    activity as? MainActivity, QuickListenFrom.BOTTOM_TAB.from, null
                )

            }
            ivCloseView.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                removeGuide()
            }


            rootView.addView(childView)
            ValueAnimator.ofFloat(0f, 1f)?.let {
                it.duration = 250
                it.addUpdateListener { value ->
                    if (value?.animatedValue is Float) {
                        ivArrowTop?.alpha = value.animatedValue as Float
                        cardView?.alpha = value.animatedValue as Float
                    }

                }
                it.addListener(object:AnimatorListener{
                    override fun onAnimationStart(animation: Animator?) {
                    }

                    override fun onAnimationEnd(animation: Animator?) {
                        ivBgView.visibility = View.VISIBLE
                        ivBgView.playAnimation()

                    }

                    override fun onAnimationCancel(animation: Animator?) {
                    }

                    override fun onAnimationRepeat(animation: Animator?) {
                    }

                })
                it.start()
            }
            var hasChanged1 = false
            var hasChanged2 = false
            ivBgView?.addAnimatorUpdateListener {
                if( it.animatedValue as Float  >= 0.001 && !hasChanged1){
                    hasChanged1 = true
                    ivArrowTop.imageTintList = ColorStateList.valueOf(Color.parseColor("#C0DFFE"))
                }
                if (it.animatedValue as Float >= 0.2983 && !hasChanged2) {
                    hasChanged2 = true
                    ivArrowTop.imageTintList =
                        ColorStateList.valueOf(Color.parseColor("#F3F4FC"))
                }
            }
            ivBgView?.addAnimatorListener(object : AnimatorListener {
                override fun onAnimationStart(animation: Animator?) {
                }

                override fun onAnimationEnd(animation: Animator?) {
                    removeGuide()
                }

                override fun onAnimationCancel(animation: Animator?) {
                }

                override fun onAnimationRepeat(animation: Animator?) {
                }

            })
            isShowed = true
            MmkvCommonUtil.getInstance(context).saveString(KEY_SHOW_DATE_RECORD, currentDate)
            tracePopShow(xmRequestId, NEW_COMER_TYPE, UiType.FLOAT_LAYER.value)
            reportGuideShow(mData)
            return true
        } catch (e: Exception) {
            ViewUtil.setHasDialogShow(false)
            e.printStackTrace()
            Logger.d(TAG, "显示异常:${e.message}")
        }
        return false
    }

    private fun preLoadDialogJsonFile(
        activity: FragmentActivity?,
        itemView: View,
        url: String,
    ): Boolean {
        val lottieUrl = if (url.isNullOrEmpty()) {
            "https://audiotest.cos.tx.xmcdn.com/storages/0b54-audiotest/50/16/GAqStYUMOlVJAAo8NAAB4dK-.json"
        } else {
            url
        }
        val fileName = "${lottieUrl.hashCode()}.json"
        val file = File(FILE_DIR_PATH, fileName);
        Logger.d(TAG, "preLoadDialogJsonFile:url$lottieUrl,file.name:$fileName ,file:${file.exists()}")
        if (file.exists()) {
            checkShowGuideInner(activity, itemView, file.absolutePath)
            return true
        }
        val downloadTask = QuickListenGuideDownloadTask(
            lottieUrl,
            FILE_DIR_PATH,
            fileName,
            object : QuickListenGuideDownloadTask.DownloadCallback {
                override fun onSuccess(filePath: String) {
                    checkShowGuideInner(activity, itemView, file.absolutePath)
                }

                override fun onError(e: java.lang.Exception?) {
                }

            }
        )
        DownloadManager.getInstance().download(downloadTask, true)
        return false
    }

    fun removeGuide() {
        Logger.d(
            TAG, "removeGuide"
        )
        mComponentCallbacksRef?.get()?.let {
            ToolUtil.getCtx()?.unregisterComponentCallbacks(it)
        }
        ViewUtil.setHasDialogShow(false)
        try {
            fileInputStream?.close();
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val childView = curViewRefresh?.get()
        if (childView?.parent is ViewGroup) {
            try {
                (childView.parent as ViewGroup).removeView(childView)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        curViewRefresh = null
    }

//    private val autoDismissRunnable = Runnable {
//        removeGuide()
//    }

    private fun tracePopShow(
        xmRequestId: String,
        bizType: String?,
        uiType: String?,
        guideText: String? = "",
        isAuto: Boolean? = false,
    ) {
        // 快听底Tab气泡引导  控件曝光
        XMTraceApi.Trace()
            .setMetaId(69108)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "forAll")
            .put("xmRequestId", xmRequestId)
            .put(
                "isDuplicateView",
                "0"
            ) // 0，1，2 字段文档见：https://alidocs.dingtalk.com/i/nodes/jb9Y4gmKWr7lEp50HZ55dyaZVGXn6lpz?utm_scene=team_space&utm_source=dingdoc_doc&utm_medium=dingdoc_doc_plugin_card
            .put("guideText", guideText)
            .put("bizType", bizType)
            .put("uiType", uiType) // 传对应的引导文案
            .put("isAuto", isAuto.toString())
            .createTrace()
    }

    private fun tracePopClick(
        xmRequestId: String,
        bizType: String?,
        uiType: String?,
        guideText: String? = "",
        isAuto: Boolean? = false,
    ) {
        Logger.d(TAG, "tracePopClick:$xmRequestId")
        // 全局页-底Tab-快听  点击事件
        XMTraceApi.Trace()
            .click(68007) // 用户点击时上报
            .put("currPage", "forAll")
            .put("xmRequestId", xmRequestId)
            .put("guideText", guideText)
            .put("bizType", bizType)
            .put("uiType", uiType) // 传对应的引导文案
            .put("isAuto", isAuto.toString())
            .createTrace();
    }

    fun showGuidePop() {
        hideBubble()
        hideRod()

        val activity = MainApplication.getMainActivity() as? MainActivity ?: return
        val itemView = activity.quickListenRadioButton

        if (itemView == null) {
            return
        }

        val rect = Rect()
        itemView.getGlobalVisibleRect(rect)

        if (rect.isEmpty || rect.top == 0) {
            Logger.log("HomePageQuickListenGuideUtil : showBubble 未获取到菜单位置")
            return
        }

        if (ViewUtil.haveDialogIsShowing(activity)) {
            return
        }

        ViewUtil.setHasDialogShow(true)

        val centerX = (rect.right + rect.left) / 2

        val viewStub = activity.findViewById<ViewStub>(R.id.host_quick_listen_tip_vs)
        if (viewStub != null) {
            mQuickListenTipsView = WeakReference(viewStub.inflate())
        }

        if (mQuickListenTipsView == null) {
            return
        }

        mQuickListenTipsView?.get()?.let {
            val arrowView = it.findViewById<View>(R.id.host_quick_listen_arrow)
            val close = it.findViewById<View>(R.id.host_quick_listen_close)
            val tipTitle = it.findViewById<TextView>(R.id.host_quick_listen_tip_title)

            val arrowViewMarginStart = centerX - 22.dp
            arrowView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                marginStart = arrowViewMarginStart
            }

            close.setOnClickListener { closeView ->
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                hideBubble()
            }

            val paint = Paint()
            paint.textSize = 12f * ToolUtil.getCtx().resources.displayMetrics.density // 设置字体大小，单位是sp
            paint.typeface = Typeface.DEFAULT // 设置字体

            val text = "下次点这里可以一键开听"
            val textWidth = paint.measureText(text) + 32.dp + 12.dp

            val tipViewMarginStart = max(0f, centerX - 16.dp - textWidth / 2)
            tipTitle.updateLayoutParams<ConstraintLayout.LayoutParams> {
                marginStart = tipViewMarginStart.toInt()
            }
            tipTitle.text = text

            it.setOnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@setOnClickListener
                }
                tracePopClick(xmRequestId, "退出引导", UiType.BUBBLE.value, text)
                hideBubble()

                ToolUtil.clickUrlAction(activity as? MainActivity, OPEN_QUICK_LISTEN_ITING, null)
            }

            listenStackChangeOrTabClick()
            it.visibility = View.VISIBLE
            HandlerManager.removeCallbacks(mHideBubbleRunnable)
            mHideBubbleRunnable = Runnable {
                mHideBubbleRunnable = null
                removeListenStackChangeOrTabClick()
                hideBubble()
            }
            HandlerManager.postOnUIThreadDelay4Kt(5000, mHideBubbleRunnable)

            tracePopShow(xmRequestId, "退出引导", UiType.BUBBLE.value, text)
            reportGuideShow("morningScene", "bubble", "Text")
        }
    }
}

class QuickListenGuideDownloadTask(
    private val mDownloadUrl: String,
    private val mDirPath: String,
    private val mFileName: String,
    private val mDownloadCallback: DownloadCallback? = null,
) : BaseDownloadTask() {

    override fun getDownloadUrl(): String {
        return mDownloadUrl
    }

    override fun getLocalPath(): String {
        return mDirPath
    }

    override fun getLocalName(): String {
        return mFileName
    }

    override fun isRefresh(): Boolean {
        return false
    }

    override fun handleStartDownload() {
        // 更新为下载中
        Logger.d(HomePageQuickListenGuideUtil.TAG, "开始下载json:$mFileName")
    }

    override fun handleStopDownload() {
        Logger.d(HomePageQuickListenGuideUtil.TAG, "停止下载json:$mFileName")
        HandlerManager.postOnUIThread {
            mDownloadCallback?.onError(null)
        }
    }

    override fun handleUpdateDownload(curr: Long, total: Long) {}

    override fun handleCompleteDownload() {
        Logger.d(HomePageQuickListenGuideUtil.TAG, "json:$mFileName 下载完成")
        HandlerManager.postOnUIThread {
            mDownloadCallback?.onSuccess(mDirPath + File.separator + mFileName)
        }
    }

    override fun handleDownloadError(e: java.lang.Exception, what: Int, extra: Int) {
        Logger.d(HomePageQuickListenGuideUtil.TAG, "json:$mFileName 下载失败 ${e.message} what=${what} extra=${extra}")
        HandlerManager.postOnUIThread {
            mDownloadCallback?.onError(e)
        }
    }

    interface DownloadCallback {
        fun onSuccess( filePath: String)
        fun onError(e:java.lang.Exception?)
    }
}


@Keep
data class GuideData(
    val bizType: String? = "",
    val uiType: String? = "",
    val contentType: String? = "",
    val guideInfo: GuideInfo? = null,
    val redDot: RedDot? = null,
)

@Keep
data class GuideInfo(
    val url: String? = "",
    val landingPage: String? = "",
    val title: String? = "",
    val showTime: Long? = 5000,
) {
    fun getAvailableLandingPage(): String {
        return if (landingPage.isAvailable()) landingPage!! else OPEN_QUICK_LISTEN_ITING
    }
}

@Keep
data class RedDot(
    val title: String? = "",
    val showTime: Long? = 0,
    val url: String? = "",
) {
    fun getAvailableLandingPage(): String {
        return if (url.isAvailable()) url!! else OPEN_QUICK_LISTEN_ITING
    }
}

enum class UiType(val value: String) {
    /** 半浮层 */
    FLOAT_LAYER("floatLayer"),
    /** 气泡 */
    BUBBLE("bubble"),
    /** 红点 */
    RED_DOT("redDot"),
    /** 气泡+红点 */
    BUBBLE_WITH_RED_DOT("bubbleWithRedDot");
}