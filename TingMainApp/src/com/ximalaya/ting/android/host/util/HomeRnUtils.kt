package com.ximalaya.ting.android.host.util

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import com.ximalaya.ting.android.adsdk.util.config.ABConfig
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.rn.RnLocalBundleCheckUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager
import com.ximalaya.ting.android.opensdk.util.EasyConfigure
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil

object HomeRnUtils {

    private val useHomeRN by lazy {
        if (MMKVUtil.getInstance().getBoolean(CConstants.Group_toc.HOME_RN_USE_CLIENT_AB, false)) {
            println("HomeRnUtils useClientAb")
            ABTest.getString("homepagern", "0") == "1"
        } else {
            ABConfig.getABConfigString("new_homepage_rn_server", "0") == "1"
        }
    }

    private val localHasRnBundle by lazy {
        RnLocalBundleCheckUtil.hasBundle(getRNHomeBundleName())
    }

    @JvmStatic
    private fun useRNRecommend(): Boolean {
        val debugValue = ToolUtil.getDebugSystemProperty("debug.home.rn_tab", "-1")
        if (debugValue == "0") {
            return false
        } else if (debugValue == "1") {
            return true
        }

        val switchValue = EasyConfigure.getBoolean("home_recommend_use_rn", true)
        if (!switchValue) {
            return false
        }

        if (MMKVUtil.getInstance().containsKey("recommend_use_rn_toggle")) {
            return MMKVUtil.getInstance().getBoolean("recommend_use_rn_toggle", false)
        }

        println("HomeRnUtils  hasInited= ${ABTest.isInit()} useHomeRN=${useHomeRN}   localHasRnBundle=${localHasRnBundle}")

        if (!localHasRnBundle) {
            return false
        }

        if (useHomeRN) {
            // 预加载下RN
            Router.getActionByCallback(Configure.BUNDLE_RN, object : Router.IBundleInstallCallback {
                override fun onInstallSuccess(bundleModel: BundleModel?) {
                    Router.getActionRouter<RNActionRouter>(Configure.BUNDLE_RN)
                }

                override fun onLocalInstallError(t: Throwable?, bundleModel: BundleModel?) {
                }

                override fun onRemoteInstallError(t: Throwable?, bundleModel: BundleModel?) {
                }
            })
        }

        return useHomeRN
    }

    @JvmStatic
    fun isShowRnFragment(): Boolean {
        return RecommendFragmentAbManager.is2024NewRecommendFragment() && useRNRecommend()
    }

    @JvmStatic
    fun getRecommendRnFragment(callBack: IHomeRnCallBack?) {
        try {
            val sendSuccess = {
                val rnActionRouter = Router.getActionRouter<RNActionRouter>(Configure.BUNDLE_RN)
                val clazz = if (ToolUtil.getSystemProperty("debug.xima.home_rn", "-1") == "1") {
                    println("homeRN 使用的是RNTestFragment")
                    rnActionRouter?.functionAction?.rnTestFragmentClazz
                } else {
                    println("homeRN 使用的是RNFragment")
                    rnActionRouter?.functionAction?.rnFragmentClazz
                }

                if (clazz != null) {
                    callBack?.onSuccess(clazz)
                } else {
                    callBack?.onError("获取class失败")
                }
            }

            Router.getActionByCallback(Configure.BUNDLE_RN, object : Router.IBundleInstallCallback {
                override fun onInstallSuccess(bundleModel: BundleModel?) {
                    sendSuccess()
                }

                override fun onLocalInstallError(t: Throwable?, bundleModel: BundleModel?) {
                    callBack?.onError(t?.message)
                }

                override fun onRemoteInstallError(t: Throwable?, bundleModel: BundleModel?) {
                    callBack?.onError(t?.message)
                }
            })
        } catch (e: Exception) {
            e.printStackTrace()
            callBack?.onError(e.message)
        }
    }

    @JvmStatic
    fun putArguments(bundle: Bundle) {
        if (!isShowRnFragment()) {
            return
        }
        bundle.putString("bundle", getRNHomeBundleName())
        bundle.putBoolean("inTab", true)
    }

    @JvmStatic
    fun getRNHomeBundleName(): String {
        try {
            val bundleName = ConfigureCenter.getInstance()
                .getString(CConstants.Group_android.GROUP_NAME, "home_recommend_rn_bundle_name")
            Log.d("home_rn", "getRNHomeBundleName: $bundleName")
            if (!TextUtils.isEmpty(bundleName)) {
                return bundleName
            }
        } catch (e: Exception) {
//            e.printStackTrace()
        }
        return "rn_home_recommend"
    }

    interface IHomeRnCallBack {
        fun onSuccess(clazz: Class<out Fragment>?)
        fun onError(message: String?)
    }
}