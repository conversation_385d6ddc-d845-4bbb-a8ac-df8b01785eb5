package com.ximalaya.ting.android.host.util;

import android.os.Looper;
import android.util.Log;

import com.ximalaya.ting.android.lockmonitor.LockCheckUtil;

import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR>
 * @date 2022/4/15 13:02
 */
public class MainThreadLockMonitor {
    private static final String TAG = "MainThreadLockMonitor";

    private static MainThreadLockMonitor INSTANCE;
    private static final long INTERVAL = 100L;
    private static final Timer TIMER;
    private boolean mHasScheduled = false;
    private boolean mLastLoopThreadBlocked = false;

    static {
        TIMER = new Timer("main_thread_lock_check");
    }

    private MainThreadLockMonitor() {
    }

    public static MainThreadLockMonitor getInstance() {
        if (INSTANCE == null) {
            synchronized (MainThreadLockMonitor.class) {
                if (INSTANCE == null) {
                    INSTANCE = new MainThreadLockMonitor();
                }
            }
        }
        return INSTANCE;
    }

    public void checkLock() {
        if (mHasScheduled) {
            return;
        }
        mHasScheduled = true;
        init();
        TIMER.scheduleAtFixedRate(mCheckLockRunnable, 0L, INTERVAL);
    }

    private void init() {
        if (LockCheckUtil.isInit()) {
            return;
        }
        LockCheckUtil.init();
    }

    private TimerTask mCheckLockRunnable = new TimerTask() {
        @Override
        public void run() {
            Thread mainThread = Looper.getMainLooper().getThread();
            if (mainThread == null) {
                return;
            }
            if (mainThread.getState() != Thread.State.BLOCKED) {
                return;
            }
            if (!mLastLoopThreadBlocked) {
                mLastLoopThreadBlocked = true;
                return;
            } else {
                mLastLoopThreadBlocked = false;
                StringBuilder sb = new StringBuilder();
                sb.append("主线程被卡住超过" + INTERVAL + "ms，主线程堆栈为：\n");
                StackTraceElement[] traceElements = mainThread.getStackTrace();
                if (traceElements != null) {
                    if (traceElements.length >= 1) {
                        String traceStr = traceElements[0].toString();
                        if (traceStr != null && (traceStr.startsWith("android.") || traceStr.startsWith("androidx."))) {
                            return;
                        }
                    }
                    for (StackTraceElement element : traceElements) {
                        sb.append(element.toString()).append("\n");
                    }
                }

                sb.append("主线程正在等待线程的日志： ");
                String blockThreadLog = LockCheckUtil.checkLockThread(mainThread);
                sb.append(blockThreadLog).append("\n");

                Log.e(TAG, sb.toString());
            }
        }
    };
}
