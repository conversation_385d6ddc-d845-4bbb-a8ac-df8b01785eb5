package com.ximalaya.ting.android.host.util


import android.graphics.PorterDuff
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.model.recommend.ShowTag
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.host.util.common.StringUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import kotlin.math.max
import kotlin.math.min

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2024/3/20
 * Description：首页统一标签样式，传入LinearLayout
 */
object RecommendShowTagsUtilNew {
    const val TYPE_ALBUM_TITLE = "albumTitle"
    const val TYPE_COUNT_PLAY = "count/play"
    const val TYPE_OTHER = "other"
    const val TYPE_DURATION = "duration"
    const val TYPE_JIAOBIAO = "jiaobiao"

    const val THEME_DEFAULT = 0
    const val THEME_WHITE = 1
    const val THEME_IP = 2
    const val THEME_SHOWTAG2 = 3
    const val THEME_PURE_WHITE = 4
    const val THEME_DARK_MODE = 5

    // 同时触发多个会导致颜色错乱 所以针对不用的颜色缓存不同的drawable
    private var mAnchorNameDrawable: MutableMap<Int, Drawable> = mutableMapOf()
    private var mDurationDrawable: MutableMap<Int, Drawable> = mutableMapOf()
    private var mPlayDrawable: MutableMap<Int, Drawable> = mutableMapOf()

    fun bindTagsViewWhiteTheme(warpLayout: LinearLayout?, tags: List<ShowTag>?, containerWidthInPx: Int): Boolean {
        return bindTagsView(warpLayout, tags, containerWidthInPx, "", "", THEME_WHITE)
    }

    fun bindTagsView(
        warpLayout: LinearLayout?,
        tags: List<ShowTag>?,
        containerWidthInPx: Int,
        subtitle1: String? = "",
        subtitle2: String? = ""
    ): Boolean {
        return bindTagsView(warpLayout, tags, containerWidthInPx, subtitle1, subtitle2, THEME_DEFAULT)
    }

    fun bindTagsView(warpLayout: LinearLayout?, tags: List<ShowTag>?, containerWidthInPx: Int,
                     subtitle1: String?, subtitle2: String?,
                     themeEnum: Int
    ): Boolean {
        if (warpLayout == null) {
            return false
        }
        if (tags.isNullOrEmpty()) {
            ViewStatusUtil.setVisible(View.GONE, warpLayout)
            if (!TextUtils.isEmpty(subtitle1) || !TextUtils.isEmpty(subtitle2)) {
                val newList = mutableListOf<ShowTag>()
                if (!TextUtils.isEmpty(subtitle1)) {
                    val tag1 = ShowTag()
                    tag1.tag = subtitle1
                    tag1.type = TYPE_OTHER
                    newList.add(tag1)
                }
                if (!TextUtils.isEmpty(subtitle2)) {
                    val tag2 = ShowTag()
                    tag2.tag = subtitle2
                    tag2.type = TYPE_OTHER
                    newList.add(tag2)
                }
                if (newList.size > 0) {
                    return bindTagsView(warpLayout, newList, containerWidthInPx, "", "", themeEnum)
                }
            }
            return false
        }
        warpLayout.gravity = Gravity.CENTER_VERTICAL
        warpLayout.orientation = LinearLayout.HORIZONTAL
        warpLayout.removeAllViews()
        ViewStatusUtil.setVisible(View.VISIBLE, warpLayout)

        var isLastTag = false
        var totalTvWidth = 0f
        var hasEllipsizeTag = false
        val tvList = mutableListOf<View>()
        tags.forEachIndexed { tagIndex, it ->
            if (it.type != null && it.tag != null && !TextUtils.isEmpty(it.tag)) {
                val childView: View?
                if (it.type == TYPE_JIAOBIAO) {
                    childView = ImageView(warpLayout.context)
                    runCatching {
                        if (themeEnum == THEME_WHITE) {
                            val colorStateList = ContextCompat.getColorStateList(warpLayout.context, R.color.host_color_99ffffff)
                            childView.setImageTintList(colorStateList)
                        } else if (themeEnum == THEME_PURE_WHITE) {
                            val colorStateList = ContextCompat.getColorStateList(warpLayout.context, R.color.host_color_white)
                            childView.setImageTintList(colorStateList)
                        }
                    }.onFailure { it.printStackTrace() }
                    childView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
                } else {
                    childView = TextView(warpLayout.context)
                    childView.gravity = Gravity.CENTER_VERTICAL or Gravity.START
                    childView.maxLines = 1
                    childView.textSize = 12f
                    childView.includeFontPadding = false
                    childView.contentDescription = it.tag
                }
                val layoutParams = LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                if (tagIndex == tags.size - 1) {
                    isLastTag = true
                }
                if (!isLastTag) {
                    // 设计说  再也不改了
                    totalTvWidth += 8.dp
                    layoutParams.marginEnd = 8.dp
                }
                warpLayout.addView(childView, layoutParams)
                tvList.add(childView)
                var spanUtils: SpanUtils? = null
                if (childView is TextView) {
                    spanUtils = SpanUtils.with(childView)
                }
                val defaultColor = when (themeEnum) {
                    THEME_PURE_WHITE -> {
                        R.color.host_color_white
                    }
                    THEME_WHITE -> {
                        R.color.host_color_99ffffff
                    }
                    THEME_DARK_MODE->{
                        R.color.host_color_99ffffff
                    }
                    THEME_IP -> {
                        R.color.host_color_8f8f8f_66666b
                    }
                    THEME_SHOWTAG2 -> {
                        R.color.host_color_662c2c3c_8d8d91
                    }
                    else -> {
                        R.color.host_color_662c2c3c_cc8d8d91
                    }
                }

                val blueTag = {
                    childView.minimumHeight = 16.dp
                    childView.setPadding(6.dp, 2.dp, 6.dp, 1.5.toFloat().dp)
                    totalTvWidth += 12.dp // padding
                    (childView as? TextView)?.textSize = 10f
                    var textColor = R.color.host_color_637eb6
                    when (themeEnum) {
                        THEME_WHITE -> {
                            textColor = R.color.host_color_99ffffff
                            childView.setBackgroundResource(R.drawable.host_4corner_0bffffff)
                        }
                        THEME_IP -> {
                            textColor = R.color.host_color_8f8f8f_66666b
                            childView.setBackgroundResource(R.drawable.host_4corner_0bffffff)
                        }
                        else -> {
                            childView.setBackgroundResource(R.drawable.host_2corner_f4f5f6_272c31)
                        }
                    }
                    spanUtils!!
                        .append(it.tag!!)
                        .setForegroundColor(childView.resources.getColor(textColor))
                        .setTypeface(Typeface.create("sans-serif-light", Typeface.BOLD))
                }

                if (it.ext?.isBlueStyle() == true) {
                    blueTag()
                } else {
                    when (it.type) {
                        TYPE_JIAOBIAO -> {
                            childView.layoutParams.height = 16.dp
                            childView.layoutParams.width = (16.dp * (it.value?.toFloat() ?: 0f)).toInt()
                            totalTvWidth += childView.layoutParams.width
                            (childView as? ImageView)?.scaleType = ImageView.ScaleType.FIT_XY
                            val widDp = (16 * (it.value?.toFloat() ?: 0f)).toInt()
                            ImageManager.from(childView.context).displayImageNotIncludeDownloadCacheSizeInDp(childView as? ImageView, it.tag, -1,
                                widDp, 16
                            ) { _, bitmap ->
                                if (bitmap == null) {
                                    childView.visibility = View.GONE
                                }
                            }
                        }
                        "rankNo", "count/subscribe" -> {
                            blueTag()
                        }
                        "socialTag", "customTitle", "summary", TYPE_ALBUM_TITLE -> {
                            hasEllipsizeTag = true
                            it.canZip = true
                            spanUtils!!
                                .append(it.tag!!)
                                .setForegroundColor(
                                    childView.resources.getColor(defaultColor)
                                )
                        }
                        "scoreTag", "award" -> {
                            childView.setPadding(0, 2.toFloat().dp, 0, 1.5.toFloat().dp)
                            (childView as? TextView)?.textSize = 11.5f
                            val scoreDouble = it.value?.toDoubleOrNull() ?: 0.toDouble()
                            val recScoreInt = (scoreDouble * 100).toInt() // 推荐值百分数

                            val labelColor: Int = if (themeEnum == THEME_WHITE) {
                                childView.context.resources.getColor(R.color.host_color_99ffffff)
                            } else if (it.type == "award") {
                                childView.context.resources.getColor(R.color.host_color_b08f68)
                            } else {
                                AlbumRateColorUtil.getScoreLabelColor(recScoreInt)
                            }
                            val mScoreTagLeftDrawable = childView.resources.getDrawable(R.drawable.host_recommend_tags_score_left)?.mutate()
                            mScoreTagLeftDrawable?.setBounds(0, 0, 8.dp, 14.dp)
                            val mScoreTagRightDrawable = childView.resources.getDrawable(R.drawable.host_recommend_tags_score_right)?.mutate()
                            mScoreTagRightDrawable?.setBounds(0, 0, 8.dp, 14.dp)
                            mScoreTagLeftDrawable?.setColorFilter(labelColor, PorterDuff.Mode.SRC_IN)
                            mScoreTagRightDrawable?.setColorFilter(labelColor, PorterDuff.Mode.SRC_IN)
                            if (mScoreTagLeftDrawable != null) {
                                totalTvWidth += 8.dp // image width
                                totalTvWidth += 1 // space px
                                spanUtils!!
                                    .appendVerticalCenterImage(mScoreTagLeftDrawable, true)
                                    .appendSpace(1)
                            }
                            spanUtils!!
                                .append(it.tag!!)
                                .setForegroundColor(labelColor)
                                .setTypeface(Typeface.DEFAULT_BOLD)
                            if (mScoreTagRightDrawable != null) {
                                totalTvWidth += 8.dp // image width
                                totalTvWidth += 1 // space px
                                spanUtils
                                    .appendSpace(1)
                                    .appendVerticalCenterImage(mScoreTagRightDrawable, true)
                            }
                        }
                        "anchorName" -> {
                            totalTvWidth += 11.dp // 10 + 1
                            // 一个界面同时多个样式触发绑定  就会导致按钮颜色异常
                            var drawable = mAnchorNameDrawable[defaultColor]
                            if (drawable == null) {
                                drawable = childView.resources.getDrawable(R.drawable.host_recommend_author_name_tag_icon).mutate()
                                drawable.setBounds(0, 0, 10.dp, 10.dp)
                                mAnchorNameDrawable[defaultColor] = drawable
                            }
                            drawable.setColorFilter(childView.resources.getColor(defaultColor), PorterDuff.Mode.SRC_IN)
                            spanUtils!!
                                .appendVerticalCenterImage(drawable, true)
                                .appendSpace(1.dp)
                            spanUtils
                                .append(it.tag!!)
                                .setForegroundColor(childView.resources.getColor(defaultColor))
                        }
                        TYPE_DURATION -> {
                            totalTvWidth += 11.dp // 10 + 1
                            var drawable = mDurationDrawable[defaultColor]
                            if (drawable == null) {
                                drawable = childView.resources.getDrawable(R.drawable.host_recommend_author_duration_tag_icon).mutate()
                                drawable.setBounds(0, 0, 10.dp, 10.dp)
                                mDurationDrawable[defaultColor] = drawable
                            }
                            drawable.setColorFilter(childView.resources.getColor(defaultColor), PorterDuff.Mode.SRC_IN)
                            spanUtils!!
                                .appendVerticalCenterImage(drawable, true)
                                .appendSpace(1.dp)
                            spanUtils
                                .append(it.tag!!)
                                .setForegroundColor(childView.resources.getColor(defaultColor))
                        }
                        TYPE_COUNT_PLAY, "countPlayLimit" -> {
                            totalTvWidth += 10.dp // 10 + 2
                            var drawable = mPlayDrawable[defaultColor]
                            if (drawable == null) {
                                drawable = childView.resources.getDrawable(R.drawable.host_recommend_play_count_tag_icon).mutate()
                                drawable.setBounds(0, 0, 10.dp, 10.dp)
                                mPlayDrawable[defaultColor] = drawable
                            }
                            drawable.setColorFilter(childView.resources.getColor(defaultColor), PorterDuff.Mode.SRC_IN)
                            try {
                                val playCount = it.value?.toLong() ?: 0L
                                if (playCount > 0) {
                                    it.tag = if (isUseNewPlayCount()) {
                                        StringUtil.getPlayCountStrFromShowTag(playCount)
                                    } else {
                                        StringUtil.getFriendlyNumStrNew(playCount)
                                    }
                                }
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                            spanUtils!!
                                .appendVerticalCenterImage(drawable, true)
                            spanUtils
                                .append(it.tag!!)
                                .setForegroundColor(childView.resources.getColor(defaultColor))
                            childView.contentDescription = "播放量${it.tag}"
                        }
                        else -> {
                            spanUtils!!.append(it.tag!!)
                                .setForegroundColor(childView.resources.getColor(defaultColor))
                        }
                    }
                }

                if (childView is TextView) {
                    totalTvWidth += getRealWidth(childView, it.tag)
                }
                spanUtils?.create()
                childView.setTag(R.id.host_id_recommend_show_tag_tv, it)
            }
        }

        if (tvList.size == 1) {
            tvList.forEach {
                (it as? TextView)?.ellipsize = TextUtils.TruncateAt.END
            }
            return true
        }
        val canShowOneLine = containerWidthInPx - totalTvWidth > 0
        if (canShowOneLine) {
            return true
        }
        if (tags.size >= 3) {
            // 当发生截断时，从后往前舍弃标签 播放量标签除外
            val newList = mutableListOf<ShowTag>()
            newList.addAll(tags)

            val lastTag = tags.last()
            if (lastTag.type == TYPE_COUNT_PLAY) {
                newList.removeAt(tags.lastIndex - 1)
            } else {
                newList.removeLast()
            }

            bindTagsView(warpLayout, newList, containerWidthInPx, subtitle1, subtitle2, themeEnum)
            return false
        }

        if (tags.size == 2 && hasEllipsizeTag) {
            // 看能不能略缩
            if (tvList[0].getTag(R.id.host_id_recommend_show_tag_tv) is ShowTag && tvList[1].getTag(R.id.host_id_recommend_show_tag_tv) is ShowTag) {
                val tag0 = tvList[0].getTag(R.id.host_id_recommend_show_tag_tv) as ShowTag
                if (tag0.canZip == true) {
                    (tvList[0] as? TextView)?.ellipsize = TextUtils.TruncateAt.END
                    (tvList[0].layoutParams as? LinearLayout.LayoutParams)?.weight = 1f
                    (tvList[0] as? TextView)?.setSingleLine()
                    tvList[1].post {
                        if ((tvList[1].measuredWidth + 8.dp) > 0.85 * containerWidthInPx || tvList[0].measuredWidth == 0) {
                            // 第二个tv超过整行的80%
                            val newList = mutableListOf<ShowTag>()
                            newList.addAll(tags)
                            newList.removeFirst()
                            if (newList.size > 0) {
                                bindTagsView(warpLayout, newList, containerWidthInPx, subtitle1, subtitle2, themeEnum)
                            }
                        }
                    }
                } else {
                    (tvList[1] as? TextView)?.ellipsize = TextUtils.TruncateAt.END
                    tvList[0].post {
                        if ((tvList[0].measuredWidth + 8.dp) > 0.85 * containerWidthInPx || tvList[1].measuredWidth == 0) {
                            val newList = mutableListOf<ShowTag>()
                            newList.addAll(tags)
                            newList.removeLast()
                            if (newList.size > 0) {
                                bindTagsView(warpLayout, newList, containerWidthInPx, subtitle1, subtitle2, themeEnum)
                            }
                        }
                    }
                }
            }
        } else {
            val newList = mutableListOf<ShowTag>()
            newList.addAll(tags)
            if (newList.size > 1) {
                try {
                    val lastTag = newList.last()
                    if (lastTag.type == TYPE_COUNT_PLAY) {
                        newList.removeAt(newList.lastIndex - 1)
                    } else {
                        newList.removeLast()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    newList.removeLast()
                }
            } else {
                newList.removeLast()
            }

            if (newList.size > 0) {
                bindTagsView(warpLayout, newList, containerWidthInPx, subtitle1, subtitle2, themeEnum)
            }
        }
        return true
    }

    private fun getRealWidth(textView: TextView, textString: String?): Float {
        if (TextUtils.isEmpty(textString)) {
            return 0f
        }
        val paint = textView.paint
        return paint.measureText(textString)
    }

    fun clearDrawableCache() {
        mAnchorNameDrawable.clear()
        mDurationDrawable.clear()
        mPlayDrawable.clear()
    }

    private fun getColorWithAlpha(alpha: Float, baseColor: Int): Int {
        val a = min(255, max(0, (alpha * 255).toInt())) shl 24
        val rgb = 0x00ffffff and baseColor
        return a + rgb
    }

    fun canShowOneLine(textView: TextView?, textString: String?, containerWidthInPx: Int): Boolean {
        if (TextUtils.isEmpty(textString) || textView == null) {
            return true
        }
        val paint = textView.paint
        return paint.measureText(textString) <= containerWidthInPx
    }

    fun canShowOneLine(
        textView: TextView?,
        textString: CharSequence?,
        containerWidthInPx: Int
    ): Boolean {
        if (TextUtils.isEmpty(textString) || textView == null) {
            return true
        }
        val paint = textView.paint
        return paint.measureText(textString, 0, textString!!.length) <= containerWidthInPx
    }

    private var isUseNewPlayCount: Boolean? = null

    fun isUseNewPlayCount(): Boolean {
        if (isUseNewPlayCount != null) {
            return isUseNewPlayCount!!
        }
        val context = BaseApplication.getMyApplicationContext()
        isUseNewPlayCount =
            MmkvCommonUtil.getInstance(context).getBoolean(KEY_SHOW_TAG_USE_NEW_PLAY_COUNT, true)
        return isUseNewPlayCount!!
    }

    private const val KEY_SHOW_TAG_USE_NEW_PLAY_COUNT = "key_show_tag_use_new_play_count"

    @JvmStatic
    fun cacheConfig() {
        try {
            val config = ConfigureCenter.getInstance()
                .getBool("toc", KEY_SHOW_TAG_USE_NEW_PLAY_COUNT)
            val context = BaseApplication.getMyApplicationContext()
            MmkvCommonUtil.getInstance(context).saveBoolean(KEY_SHOW_TAG_USE_NEW_PLAY_COUNT, config)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}