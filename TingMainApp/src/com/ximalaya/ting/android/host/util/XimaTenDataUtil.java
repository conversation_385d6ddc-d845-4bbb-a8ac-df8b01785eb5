package com.ximalaya.ting.android.host.util;

import com.google.gson.Gson;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.data.model.QuickElementType;
import com.ximalaya.ting.android.host.data.model.QuickListenModel;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManager;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenManager;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.model.album.Announcer;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.xmutil.Logger;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/21 21:18
 */
public class XimaTenDataUtil {
    private static final String ELEMENT_TYPE_VIDEO = "Video";
    private static final String ELEMENT_TYPE_TRACK = "Track";
    private static final String KEY_ELEMENT_TYPE = "elementType";
    private static final String KEY_SUB_ELEMENTS = "subElements";
    private static final String KEY_BIZ_TYPE = "bizType";

    private static final Gson sGson = new Gson();

    public static final String COVER_URL_XIMA_TEN = "https://imagev2.xmcdn.com/storages/a10f-audiofreehighqps/47/36/GKwRIasL56KfAACSDwOjqjUy.png";

    public static boolean isXimaTenPlayList() {
        XmPlayerManager playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext());
        return playerManager.isQuickListen();
    }

    public static long getQuickListenTabId() {
        XmPlayerManager playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext());
        return playerManager.getQuickListenTabId();
    }

    public static boolean isXimaTenTrack(Track track) {
        if (track != null) {
            return track.getType() == Track.TYPE_XIMA_TEN_DATA;
        }
        return false;
    }

    public static List<QuickListenModel> convertDataToQuickListenModel(long tabId, List<JSONObject> list) {
        if (list == null || list.size() <= 0) {
            return new ArrayList<>();
        }
        try {
            List<QuickListenModel> result = new LinkedList<>();
            for (JSONObject item : list) {
                if (item == null) {
                    continue;
                }
                QuickListenModel model = sGson.fromJson(item.toString(), QuickListenModel.class);
                if (model != null) {
                    model.setTabId(tabId);
                    result.add(model);
                }
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Deprecated
    public static List<Track> convertDataToTrack(List<JSONObject> list) {
        return convertDataToTrack(0, list);
    }

    public static List<Track> convertDataToTrack(long tabId, List<JSONObject> list) {
        if (list == null || list.size() <= 0) {
            return new ArrayList<>();
        }
        int size = list.size();
        List<Track> result = new LinkedList<>();
        for (int i = 0; i < size; i++) {
            JSONObject item = list.get(i);
            if (item == null) {
                continue;
            }
            String elementType = item.optString(KEY_ELEMENT_TYPE);
            String refId = "";
            if (QuickElementType.XIMA_TEN_CARD.getElementType().equals(elementType)
                    || QuickElementType.TRACK_COLLECTION_CARD.getElementType().equals(elementType)
                    || QuickListenManager.isVideoCollect(item)) {
                JSONArray arr = item.optJSONArray(KEY_SUB_ELEMENTS);
                if (arr != null && arr.length() > 0) {
                    int len = arr.length();
                    for (int j = 0; j < len; j++) {
                        JSONObject itemObject = arr.optJSONObject(j);
                        if (j == 0) {
                            refId = itemObject.optString("refId");
                        }
                        TrackM track = parseTrackM(itemObject, elementType, refId, true, tabId, item);
                        if (track != null) {
                            result.add(track);
                        }
                    }
                } else if (ELEMENT_TYPE_VIDEO.equals(elementType)) {
                    TrackM track = parseTrackM(item, elementType, refId, false, tabId, item);
                    if (track != null) {
                        result.add(track);
                    }
                }
            } else {
                TrackM track = parseTrackM(item, elementType, refId, false, tabId, item);
                if (track != null) {
                    result.add(track);
                }
            }
        }
        return result;
    }

    private static TrackM parseTrackM(JSONObject item, String elementType, String refId, boolean isCollect, long tabId, JSONObject originalJsonObject) {
        if (item.has(KEY_BIZ_TYPE) && ELEMENT_TYPE_TRACK.equals(item.optString(KEY_BIZ_TYPE))) {
            TrackM tempTrack = new TrackM();
            tempTrack.setDataId(item.optLong("refId"));
            tempTrack.setTrackTitle(item.optString("title"));
            tempTrack.setCoverUrlLarge(item.optString("cover"));
            if (QuickElementType.XIMA_TEN_CARD.getElementType().equals(elementType)) {
                tempTrack.setTrackFrom(1);
            } else if (QuickElementType.TRACK_COLLECTION_CARD.getElementType().equals(elementType) || (ELEMENT_TYPE_VIDEO.equals(elementType) && isCollect)) {
                tempTrack.setTrackFrom(4);
                tempTrack.setCollectionId(refId);
            } else {
                tempTrack.setTrackFrom(2);
            }
            tempTrack.setQuickForceItems(QuickListenManager.createForceItemsRn(originalJsonObject, tempTrack.getDataId()));
            tempTrack.setTabId(tabId);
            tempTrack.setVideo(QuickElementType.VIDEO.getElementType().equals(elementType));
            if ((QuickElementType.AGENT_RADIO_CARD.getElementType().equals(elementType)
                    && originalJsonObject.optJSONObject("extraInfo") != null
                    && originalJsonObject.optJSONObject("extraInfo").optBoolean("aigc"))
                    || QuickElementType.AGENT_GUIDE_CARD.getElementType().equals(elementType)
                    || originalJsonObject.optJSONObject("extraInfo") == null
                    || !originalJsonObject.optJSONObject("extraInfo").has("forceItem") ) {
                tempTrack.setNoAddToHistory(true);
            }

            JSONObject uidJson = item.optJSONObject("anchor");
            if (uidJson != null) {
                tempTrack.setUid(uidJson.optInt("uid"));
                Announcer announcer = new Announcer();
                announcer.setAnnouncerId(uidJson.optLong("uid"));
                if (uidJson.has("nickname")) {
                    announcer.setNickname(uidJson.optString("nickname"));
                } else if (uidJson.has("nickName")) {
                    announcer.setNickname(uidJson.optString("nickName"));
                }
                if (uidJson.has("logo")) {
                    announcer.setAvatarUrl(uidJson.optString("logo"));
                }
                tempTrack.setAnnouncer(announcer);
            }

            JSONObject extraInfo = item.optJSONObject("extraInfo");
            if (extraInfo != null) {
                if (extraInfo.has("startTime")) {
                    tempTrack.setQuickListenHeadSkip(extraInfo.optInt("startTime"));
                }

                if (extraInfo.has("isFirstTrack")) {
                    tempTrack.setFirstTrack(extraInfo.optBoolean("isFirstTrack", false));
                }

                if (extraInfo.has("aigc")) {
                    tempTrack.setAigc(extraInfo.optBoolean("aigc", false));
                }

                if (extraInfo.has("isCollect")) {
                    tempTrack.setCollect(extraInfo.optBoolean("isCollect", false));
                }
            }

            JSONObject surElement = item.optJSONObject("surElement");
            if (surElement != null) {
                SubordinatedAlbum anAlbum = new SubordinatedAlbum();
                tempTrack.setAlbum(anAlbum);
                if (surElement.has("refId")) {
                    anAlbum.setAlbumId(surElement.optLong("refId"));
                }
                anAlbum.setAlbumTitle(surElement.optString("title"));
                anAlbum.setCoverUrlLarge(surElement.optString("cover"));
            }

            try {
                JSONObject ubtV2 = item.optJSONObject("ubtV2");
                if (ubtV2 != null) {
                    String rec_track = ubtV2.optString("rec_track");
                    String rec_src = ubtV2.optString("rec_src");
                    String source = ubtV2.optString("source");
                    String ubtTraceId = ubtV2.optString("ubtTraceId");

                    tempTrack.setUbtTraceId(ubtTraceId);
                    tempTrack.setRecSource(rec_src);
                    tempTrack.setRecTrack(rec_track);

                    Map<String, String> ubtV2Map = new HashMap<>();
                    Iterator<String> keys = ubtV2.keys();

                    while (keys.hasNext()) {
                        String key = keys.next();
                        ubtV2Map.put(key, ubtV2.optString(key));
                    }

                    tempTrack.setUbtV2(ubtV2Map);
                }
            } catch (Exception e) {}

            return tempTrack;
        }
        return null;
    }

    public static int trackIndexInList(List<Track> tracks, long trackId) {
        if (!ToolUtil.isEmptyCollects(tracks)) {
            for (int i = 0; i < tracks.size(); i++) {
                Track item = tracks.get(i);
                if (item != null && item.getDataId() == trackId) {
                    return i;
                }
            }
        }
        return -1;
    }

    public static void printList(String message, List<? extends Object> list) {
//        if (list == null) {
//            return;
//        }
//        StringBuilder sb = new StringBuilder(message).append("\n");
//        for (int i = 0; i < list.size(); i++) {
//            Object item = list.get(i);
//            if (item instanceof Track) {
//                sb.append(((Track) item).getTrackTitle()).append("\n");
//            } else if (item instanceof JSONObject) {
//                sb.append(((JSONObject) item).optString("title", "none")).append("\n");
//            }
//        }
//        Logger.d("XimaTenData", sb.toString());
    }

}
