package com.ximalaya.ting.android.host.util.common;

import androidx.annotation.Nullable;

import java.io.IOException;
import java.nio.charset.Charset;
import okhttp3.RequestBody;
import okhttp3.MediaType;
import okio.BufferedSink;

public class JsonRequestBody extends RequestBody {
    private String json;

    public JsonRequestBody(String json) {
        this.json = json;
    }

    @Nullable
    public MediaType contentType() {
        return MediaType.parse("application/json");
    }

    public void writeTo(BufferedSink sink) throws IOException {
        sink.writeString(this.json, Charset.defaultCharset());
    }
}
