package com.ximalaya.ting.android.host.util.common;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build;
import android.text.TextUtils;

import com.umeng.commonsdk.debug.E;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.IOException;
import java.util.List;

/**
  * <AUTHOR>
  */
public final class PackageUtil {

	private static final String KEY_MIUI_VERSION_CODE = "ro.miui.ui.version.code";
	private static final String KEY_MIUI_VERSION_NAME = "ro.miui.ui.version.name";
	private static final String KEY_MIUI_INTERNAL_STORAGE = "ro.miui.internal.storage";

	public static final String PACKAGE_360_WEISHI = "com.qihoo360.mobilesafe";
	public static final String PACKAGE_TENCENT_GUANJIA = "com.tencent.qqpimsecure";
	public static final String PACKAGE_LBE_ANQUAN = "com.lbe.security";
	public static final String PACKAGE_BAIDU_WEISHI = "cn.opda.a.phonoalbumshoushou";
	public static final String PACKAGE_LIEBAO_DASHI = "com.cleanmaster.mguard_cn";

	public static final String PACKAGE_MOBILE_QQ="com.tencent.mobileqq";
	public static final String PACKAGE_WECHAT="com.tencent.mm";
	public static final String PACKAGE_UNION_PAY = "com.unionpay";
	public static final String PACKAGE_SINA_WB = "com.sina.weibo";

	public static boolean isAppInstalled(Context context, String appPackageName) {
		PackageInfo packageInfo;

		try {
			packageInfo = context.getPackageManager().getPackageInfo(appPackageName, 0);

		} catch (NameNotFoundException e) {
			return false;
		} catch (Exception e) {
			Logger.e("-------msg Exception ", "err: " + e);
			return false;
		}
		return packageInfo != null;
	}

	/**
	 * 增强版应用安装检测方法
	 * 使用多种策略检测应用是否已安装，提高检测准确性
	 */
	public static boolean isAppInstalledEnhanced(Context context, String appPackageName) {
		if (context == null || TextUtils.isEmpty(appPackageName)) {
			return false;
		}

		// 策略1: 使用PackageManager.getPackageInfo检测
		if (isAppInstalled(context, appPackageName)) {
			return true;
		}

		// 策略2: 尝试获取启动Intent检测
		try {
			PackageManager pm = context.getPackageManager();
			Intent launchIntent = pm.getLaunchIntentForPackage(appPackageName);
			if (launchIntent != null) {
				return true;
			}
		} catch (Exception e) {
			Logger.e("-------msg Exception ", "err: " + e);
		}

		// 策略3: 对于某些特殊系统，尝试查询已安装应用列表
		try {
			PackageManager pm = context.getPackageManager();
			List<PackageInfo> packages = pm.getInstalledPackages(0);
			for (PackageInfo packageInfo : packages) {
				if (appPackageName.equals(packageInfo.packageName)) {
					return true;
				}
			}
		} catch (Exception e) {
			Logger.e("PackageUtil", "install error: " + e.getMessage());
		}

		return false;
	}

	public static boolean isMIUI() {
		try {
			final BuildProperties prop = BuildProperties.newInstance();
			return prop.getProperty(KEY_MIUI_VERSION_CODE, null) != null
					|| prop.getProperty(KEY_MIUI_VERSION_NAME, null) != null
					|| prop.getProperty(KEY_MIUI_INTERNAL_STORAGE, null) != null;
		} catch (final IOException e) {
			return false;
		}
	}
	
	public static boolean isPostHB() {
		return Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB;
	}

	public static boolean isPostICS() {
		return android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.ICE_CREAM_SANDWICH;
	}
	
	public static void launchApk(Activity activity, String packageName)
	{
		if (activity == null || TextUtils.isEmpty(packageName)) return;
		PackageManager packageManager = activity.getApplicationContext()
				.getPackageManager();
		Intent intent = new Intent();
		intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		intent = packageManager.getLaunchIntentForPackage(packageName);
		if (intent != null)
		{
			activity.startActivity(intent);
		}
	}
	
	private PackageUtil() {
	}

	/**
	 * @param flags Additional option flags. Use any combination of
	 *            {@link #GET_ACTIVITIES}, {@link #GET_GIDS},
	 *            {@link #GET_CONFIGURATIONS}, {@link #GET_INSTRUMENTATION},
	 *            {@link #GET_PERMISSIONS}, {@link #GET_PROVIDERS},
	 *            {@link #GET_RECEIVERS}, {@link #GET_SERVICES},
	 *            {@link #GET_SIGNATURES}, {@link #GET_UNINSTALLED_PACKAGES} to
	 *            modify the data returned.
     */
	public static PackageInfo getPackageInfo(Context context, int flags) {
		if (context == null) {
			return null;
		}
		PackageManager packageManager = context.getPackageManager();
		if (packageManager != null) {
			try {
				return packageManager.getPackageInfo(context.getPackageName(), flags);
			} catch (NameNotFoundException e) {
				return null;
			}
		}
		return null;
	}

	public static PackageInfo getPackageInfoFromPackageName(Context context, String packageName) {
		PackageManager pm = context.getPackageManager();
		try {
			PackageInfo packageInfo = pm.getPackageInfo(packageName, 0);
			return packageInfo;
		} catch (NameNotFoundException e) {
			e.printStackTrace();
			return null;
		} catch (Exception e) {
			return null;
		}
	}

	 /**
	  * 通过apk 文件路径 获取packageinfo
	  */
	public static PackageInfo getPackageInfoFromApkPath(Context context, String apkPath) {
		try {
			PackageManager pm = context.getPackageManager();
			PackageInfo info = pm.getPackageArchiveInfo(apkPath, PackageManager.GET_ACTIVITIES);
			return info;
		}catch (Exception e) {
			e.printStackTrace();
			Logger.e("--------msg", " ------- e : " + e.toString());
		}
		return null;
	}


}
