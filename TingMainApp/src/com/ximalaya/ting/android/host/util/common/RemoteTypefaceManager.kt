package com.ximalaya.ting.android.host.util.common

import android.graphics.Typeface
import android.os.Environment
import android.widget.TextView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.service.DownloadService
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.record.BaseDownloadTask
import com.ximalaya.ting.android.host.manager.record.DownloadManager
import com.ximalaya.ting.android.host.util.common.RemoteTypefaceManager.TAG
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.player.MD5
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONArray
import java.io.File
import java.lang.ref.WeakReference
import java.util.*

object RemoteTypefaceManager {

    enum class RemoteTypeface(val fontName: String, val chineseName: String) {
        SourceHanSerifBold("SourceHanSerifCN-Bold", "思源宋体"),
        SourceHanSansCNHeavy("SourceHanSansCN-Heavy", "思源黑体")
    }

    const val TAG = "RemoteTypefaceManager"
    private const val MAX_VIEW_CACHE_SIZE = 50
    private val mDownloadingTasks = hashMapOf<RemoteTypeface, FontDownloadTask>()
    private val mWaitingTextViews = hashMapOf<RemoteTypeface, LinkedList<WeakReference<TextView?>>>()
    private val mExistedTypefaces = hashMapOf<RemoteTypeface, Typeface>()
    val mLocalDir: String

    // 新增：字体加载回调接口，用于返回下载/加载结果
    interface OnTypefaceLoadedListener {
        /**
         * 字体加载成功
         * @param typeface 目标字体
         * @param remoteTypeface 字体枚举
         */
        fun onSuccess(typeface: Typeface, remoteTypeface: RemoteTypeface)

        /**
         * 字体加载失败
         * @param remoteTypeface 目标字体枚举
         */
        fun onFailure(remoteTypeface: RemoteTypeface)
    }

    private val mDownloadCallback = object : FontDownloadTask.DownloadCallback {
        override fun onSuccess(typeface: RemoteTypeface, filePath: String) {
            val downloadedFile = File(filePath)
            if (!downloadedFile.exists()) {
                onError(typeface)
                return
            }
            try {
                val newTypeface = Typeface.createFromFile(downloadedFile)
                mExistedTypefaces[typeface] = newTypeface
                Logger.d(TAG, "使用刚下载的字体: ${typeface.fontName}")
                mWaitingTextViews[typeface]?.forEach {
                    it.get()?.includeFontPadding = false
                    it.get()?.typeface = newTypeface
                }
                mWaitingTextViews.remove(typeface)
                mDownloadingTasks.remove(typeface)
            } catch (e: Throwable) {
                Logger.e(TAG, "加载下载的字体失败", e)
                onError(typeface)
            }
        }

        override fun onError(typeface: RemoteTypeface) {
            mWaitingTextViews.remove(typeface)
            mDownloadingTasks.remove(typeface)
        }
    }

    init {
        mLocalDir = initLocalFontCachePath()
        kotlin.runCatching {
            val fontDir = File(mLocalDir)
            if (!fontDir.exists()) {
                fontDir.mkdirs() // 确保目录存在
            }
            fontDir.listFiles()?.forEach {
                if (it.name.endsWith(".ttf.temp") || it.name.endsWith(".otf.temp")) {
                    it.delete() // 清理临时文件
                }
            }
        }.onFailure {
            Logger.e(TAG, "初始化字体目录失败", it)
        }
    }

    private fun initLocalFontCachePath(): String {
        val localDir: String
        var appCacheDirPath = ""
        val cachePath = DownloadService.getDiskCachePath(ToolUtil.getCtx())
        if (StringUtil.isEmpty(cachePath)) {
            val externalFilesDir = ToolUtil.getCtx().getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)
            appCacheDirPath = externalFilesDir?.absolutePath ?: ""
            localDir = "$appCacheDirPath${File.separator}ting${File.separator}host${File.separator}remote_fonts"
        } else {
            localDir = "$cachePath${File.separator}remote_fonts"
        }
        return localDir
    }

    fun getTypefaceOrDownload(target: RemoteTypeface): Typeface? {
        return try {
            getTypefaceOrDownloadInternal(target)
        } catch (e: Exception) {
            if (ConstantsOpenSdk.isDebug) {
                throw e
            }
            null
        }
    }

    private fun getTypefaceOrDownloadInternal(target: RemoteTypeface): Typeface? {
        // 内存中存在直接返回
        val existTypeface = mExistedTypefaces[target]
        if (existTypeface != null) {
            return existTypeface
        }

        // 正在下载中不处理
        if (mDownloadingTasks[target] != null) {
            return null
        }

        val downloadedFiles = File(mLocalDir).listFiles { _, name ->
            (name.endsWith(".ttf") || name.endsWith(".otf")) && name.substringBeforeLast('(') == target.fontName
        }
        val fontUrl: String? = getLatestDownloadUrl(target)
        val urlValid = fontUrl != null && (fontUrl.endsWith(".ttf") || fontUrl.endsWith(".otf"))
        if (!urlValid) { // 配置中心没有有效下载地址，直接使用已下载的字体文件
            if (downloadedFiles != null && downloadedFiles.isNotEmpty()) {
                return try {
                    val newTypeface = Typeface.createFromFile(downloadedFiles[0])
                    mExistedTypefaces[target] = newTypeface
                    newTypeface
                } catch (e: Exception) {
                    Logger.e(TAG, "加载本地字体失败", e)
                    null
                }
            } else {
                return null
            }
        }
        // 检查已下载的字体是否使用的最新下载地址
        val md5Url = MD5.md5(fontUrl)
        val existFile = downloadedFiles?.find { file ->
            val startIndex = file.name.indexOfLast { it == '(' }
            val endIndex = file.name.indexOfLast { it == ')' }
            startIndex >= 0 && endIndex > startIndex && file.name.substring(startIndex + 1, endIndex) == md5Url
        }
        if (existFile != null) {
            return try {
                val newTypeface = Typeface.createFromFile(existFile)
                mExistedTypefaces[target] = newTypeface
                Logger.d(TAG, "使用磁盘字体: ${target.fontName}")
                newTypeface
            } catch (e: Exception) {
                Logger.e(TAG, "加载本地字体失败", e)
                null
            }
        }

        // 删除已下载的url不符的字体文件，并重新下载
        Logger.d(TAG, "删除旧文件，个数：${downloadedFiles?.size ?: 0}")
        downloadedFiles?.forEach { it.delete() }
        val suffix = fontUrl!!.substringAfterLast('.')
        val downloadTask = FontDownloadTask(target, fontUrl, mLocalDir, "${target.fontName}($md5Url).$suffix")
        downloadTask.setDownloadListener(mDownloadCallback)
        mDownloadingTasks[target] = downloadTask
        DownloadManager.getInstance().download(downloadTask, true)
        return null
    }

    fun setRemoteTypeface(target: RemoteTypeface, textView: TextView?) {
        try {
            setRemoteTypefaceInternal(target, textView)
        } catch (e: Exception) {
            if (ConstantsOpenSdk.isDebug) {
                throw e
            }
        }
    }

    fun hasDownload(target: RemoteTypeface): Boolean {
        val existTypeface = mExistedTypefaces[target]
        return existTypeface != null
    }

    private fun setRemoteTypefaceInternal(target: RemoteTypeface, textView: TextView?) {
        if (textView == null) return

        // 1.尝试使用已加载到内存的字体对象
        val existTypeface = mExistedTypefaces[target]
        if (existTypeface != null) {
            textView.includeFontPadding = false
            textView.typeface = existTypeface
            Logger.d(TAG, "直接使用内存字体: ${target.fontName}")
            return
        }

        // 2.判断是否正在下载，如果是，直接加入等待队列
        if (mDownloadingTasks[target] != null) {
            val targetTvList = mWaitingTextViews[target]
            if (targetTvList != null) {
                if (targetTvList.find { it.get() == textView } == null) {
                    if (targetTvList.size >= MAX_VIEW_CACHE_SIZE) {
                        targetTvList.removeAll { it.get() == null }
                        if (targetTvList.size >= MAX_VIEW_CACHE_SIZE) {
                            targetTvList.removeFirst()
                        }
                    }
                    mWaitingTextViews[target]?.addLast(WeakReference(textView))
                    Logger.d(TAG, "textView加入等待队列: ${target.fontName}")
                }
            } else {
                mWaitingTextViews[target] = LinkedList()
                mWaitingTextViews[target]?.add(WeakReference(textView))
            }
            return
        }

        // 3.尝试从磁盘加载已下载的字体
        val downloadedFiles = File(mLocalDir).listFiles { _, name ->
            (name.endsWith(".ttf") || name.endsWith(".otf")) && name.substringBeforeLast('(') == target.fontName
        }
        val fontUrl: String? = getLatestDownloadUrl(target)
        val urlValid = fontUrl != null && (fontUrl.endsWith(".ttf") || fontUrl.endsWith(".otf"))
        if (!urlValid) { // 配置的下载地址无效，直接使用已下载的文件
            if (downloadedFiles != null && downloadedFiles.isNotEmpty()) {
                try {
                    val newTypeface = Typeface.createFromFile(downloadedFiles[0])
                    mExistedTypefaces[target] = newTypeface
                    textView.includeFontPadding = false
                    textView.typeface = newTypeface
                } catch (e: Exception) {
                    Logger.e(TAG, "加载本地字体失败", e)
                }
            }
            return
        }
        val md5Url = MD5.md5(fontUrl)
        val existFile = downloadedFiles?.find { file ->
            val startIndex = file.name.indexOfLast { it == '(' }
            val endIndex = file.name.indexOfLast { it == ')' }
            startIndex >= 0 && endIndex > startIndex && file.name.substring(startIndex + 1, endIndex) == md5Url
        }
        if (existFile != null) { // 字体名和下载地址都符合的文件
            try {
                val newTypeface = Typeface.createFromFile(existFile)
                mExistedTypefaces[target] = newTypeface
                textView.includeFontPadding = false
                textView.typeface = newTypeface
                Logger.d(TAG, "使用磁盘字体: ${target.fontName}")
            } catch (e: Exception) {
                Logger.e(TAG, "加载本地字体失败", e)
            }
            return
        }

        // 4.删除已下载的url不符的字体文件，并重新下载
        Logger.d(TAG, "删除旧文件，个数：${downloadedFiles?.size ?: 0}")
        downloadedFiles?.forEach { it.delete() }
        mWaitingTextViews[target] = LinkedList()
        mWaitingTextViews[target]?.add(WeakReference(textView))
        val suffix = fontUrl!!.substringAfterLast('.')
        val downloadTask = FontDownloadTask(target, fontUrl, mLocalDir, "${target.fontName}($md5Url).$suffix")
        downloadTask.setDownloadListener(mDownloadCallback)
        mDownloadingTasks[target] = downloadTask
        DownloadManager.getInstance().download(downloadTask, true)
    }

    /**
     * 新增：下载并获取字体（通过回调返回结果）
     * @param target 目标字体枚举
     * @param listener 加载结果回调（UI线程执行）
     */
    fun downloadAndGetTypeface(target: RemoteTypeface, listener: OnTypefaceLoadedListener) {
        // 1. 若内存中已存在，直接通过回调返回
        val existTypeface = mExistedTypefaces[target]
        if (existTypeface != null) {
            HandlerManager.postOnUIThread {
                listener.onSuccess(existTypeface, target)
            }
            return
        }

        // 2. 若正在下载中，添加回调到等待队列（复用现有逻辑，避免重复下载）
        if (mDownloadingTasks.containsKey(target)) {
            // 包装回调，确保在UI线程执行
            val callbackWrapper = object : FontDownloadTask.DownloadCallback {
                override fun onSuccess(typeface: RemoteTypeface, filePath: String) {
                    val typefaceObj = mExistedTypefaces[typeface]
                    HandlerManager.postOnUIThread {
                        if (typefaceObj != null) {
                            listener.onSuccess(typefaceObj, typeface)
                        } else {
                            listener.onFailure(typeface)
                        }
                    }
                }

                override fun onError(typeface: RemoteTypeface) {
                    HandlerManager.postOnUIThread {
                        listener.onFailure(typeface)
                    }
                }
            }

            // 给当前下载任务添加额外回调
            mDownloadingTasks[target]?.addExtraCallback(callbackWrapper)
            return
        }

        // 3. 尝试从本地加载，若存在则返回；否则触发下载
        val downloadedFiles = File(mLocalDir).listFiles { _, name ->
            (name.endsWith(".ttf") || name.endsWith(".otf")) && name.substringBeforeLast('(') == target.fontName
        }
        val fontUrl = getLatestDownloadUrl(target)
        val urlValid = fontUrl != null && (fontUrl.endsWith(".ttf") || fontUrl.endsWith(".otf"))

        if (!urlValid) {
            // 配置地址无效，尝试使用本地文件
            if (downloadedFiles != null && downloadedFiles.isNotEmpty()) {
                try {
                    val typeface = Typeface.createFromFile(downloadedFiles[0])
                    mExistedTypefaces[target] = typeface
                    HandlerManager.postOnUIThread {
                        listener.onSuccess(typeface, target)
                    }
                } catch (e: Throwable) {
                    Logger.e(TAG, "加载本地字体失败", e)
                    HandlerManager.postOnUIThread {
                        listener.onFailure(target)
                    }
                }
            } else {
                HandlerManager.postOnUIThread {
                    listener.onFailure(target)
                }
            }
            return
        }

        // 4. 检查本地是否有匹配最新URL的文件
        val md5Url = MD5.md5(fontUrl)
        val existFile = downloadedFiles?.find { file ->
            val startIndex = file.name.indexOfLast { it == '(' }
            val endIndex = file.name.indexOfLast { it == ')' }
            startIndex >= 0 && endIndex > startIndex && file.name.substring(startIndex + 1, endIndex) == md5Url
        }
        if (existFile != null) {
            try {
                val typeface = Typeface.createFromFile(existFile)
                mExistedTypefaces[target] = typeface
                HandlerManager.postOnUIThread {
                    listener.onSuccess(typeface, target)
                }
                return
            } catch (e: Throwable) {
                Logger.e(TAG, "加载本地字体失败", e)
                // 本地文件损坏，删除并重新下载
                downloadedFiles.forEach { it.delete() }
            }
        } else {
            // 删除旧文件
            downloadedFiles?.forEach { it.delete() }
        }

        // 5. 启动下载，并通过回调返回结果
        val suffix = fontUrl?.substringAfterLast('.')
        val downloadTask = FontDownloadTask(
            target,
            fontUrl ?: "",
            mLocalDir,
            "${target.fontName}($md5Url).$suffix"
        )

        // 包装回调，确保下载完成后通知listener，同时保留原有全局回调
        val downloadCallback = object : FontDownloadTask.DownloadCallback {
            override fun onSuccess(typeface: RemoteTypeface, filePath: String) {
                val typefaceObj = mExistedTypefaces[typeface]
                HandlerManager.postOnUIThread {
                    if (typefaceObj != null) {
                        listener.onSuccess(typefaceObj, typeface)
                    } else {
                        listener.onFailure(typeface)
                    }
                }
            }

            override fun onError(typeface: RemoteTypeface) {
                HandlerManager.postOnUIThread {
                    listener.onFailure(typeface)
                }
            }
        }

        // 设置任务回调（同时触发原有全局回调和新增回调）
        downloadTask.setDownloadListener(object : FontDownloadTask.DownloadCallback {
            override fun onSuccess(typeface: RemoteTypeface, filePath: String) {
                mDownloadCallback.onSuccess(typeface, filePath) // 原有回调
                downloadCallback.onSuccess(typeface, filePath) // 新增回调
            }

            override fun onError(typeface: RemoteTypeface) {
                mDownloadCallback.onError(typeface) // 原有回调
                downloadCallback.onError(typeface) // 新增回调
            }
        })

        // 加入下载队列
        mDownloadingTasks[target] = downloadTask
        DownloadManager.getInstance().download(downloadTask, true)
    }

    private fun getLatestDownloadUrl(target: RemoteTypeface): String? {
        var fontUrl: String? = null
        val configStr = ConfigureCenter.getInstance().getString(CConstants.Group_toc.GROUP_NAME, "ext_font_list", "[]")
        try {
            val jsonArray = JSONArray(configStr)
            for (i in 0 until jsonArray.length()) {
                val jsonObj = jsonArray.optJSONObject(i)
                if (jsonObj.optString("fontName") == target.fontName) {
                    fontUrl = jsonObj.optString("fontUrl")
                    break
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "解析字体配置失败", e)
        }
        return fontUrl
    }
}

class FontDownloadTask(
    private val mTypeface: RemoteTypefaceManager.RemoteTypeface,
    private val mDownloadUrl: String,
    private val mDirPath: String,
    private val mFileName: String
) : BaseDownloadTask() {
    private var mDownloadCallback: DownloadCallback? = null
    // 新增：额外回调列表（支持多个监听）
    private val mExtraCallbacks = mutableListOf<DownloadCallback>()

    // 新增：添加额外回调
    fun addExtraCallback(callback: DownloadCallback) {
        mExtraCallbacks.add(callback)
    }

    override fun getDownloadUrl(): String {
        return mDownloadUrl
    }

    override fun getLocalPath(): String {
        return mDirPath
    }

    override fun getLocalName(): String {
        return mFileName
    }

    override fun isRefresh(): Boolean {
        return false
    }

    override fun handleStartDownload() {
        // 更新为下载中
        Logger.d(RemoteTypefaceManager.TAG, "开始下载字体: ${mTypeface.fontName}")
    }

    override fun handleStopDownload() {
        Logger.d(RemoteTypefaceManager.TAG, "停止下载字体: ${mTypeface.fontName}")
        HandlerManager.postOnUIThread {
            mDownloadCallback?.onError(mTypeface)
            mExtraCallbacks.forEach { it.onError(mTypeface) } // 通知额外回调
        }
    }

    override fun handleUpdateDownload(curr: Long, total: Long) {}

    override fun handleCompleteDownload() {
        Logger.d(TAG, "字体下载完成: ${mTypeface.fontName}")
        val filePath = "$mDirPath${File.separator}$mFileName"
        HandlerManager.postOnUIThread {
            mDownloadCallback?.onSuccess(mTypeface, filePath)
            mExtraCallbacks.forEach { it.onSuccess(mTypeface, filePath) } // 通知额外回调
        }
    }

    override fun handleDownloadError(e: Exception, what: Int, extra: Int) {
        Logger.e(TAG, "字体下载失败 ${mTypeface.fontName}: ${e.message}", e)
        HandlerManager.postOnUIThread {
            mDownloadCallback?.onError(mTypeface)
            mExtraCallbacks.forEach { it.onError(mTypeface) } // 通知额外回调
        }
    }

    fun setDownloadListener(callback: FontDownloadTask.DownloadCallback?) {
        mDownloadCallback = callback
    }

    interface DownloadCallback {
        fun onSuccess(typeface: RemoteTypefaceManager.RemoteTypeface, filePath: String)
        fun onError(typeface: RemoteTypefaceManager.RemoteTypeface)
    }
}