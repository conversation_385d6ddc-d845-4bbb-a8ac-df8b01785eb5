package com.ximalaya.ting.android.host.util.common;

import static com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost.TINGMAIN_KEY_TODAY_FIRST_OPEN_APP_DATE;

import android.app.Activity;
import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.database.Cursor;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Parcelable;
import android.provider.ContactsContract.CommonDataKinds.Phone;
import android.telephony.TelephonyManager;
import android.text.Html;
import android.text.Html.ImageGetter;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.SpannedString;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.ImageSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.StyleSpan;
import android.util.DisplayMetrics;
import android.view.ContextThemeWrapper;
import android.view.Display;
import android.view.Surface;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.base.IConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.FileUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.web.WebActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.PhoneContactsManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.albumpage.AlbumPageSizeManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.freeflow.FreeFlowDataUtil;
import com.ximalaya.ting.android.host.manager.freeflow.FreeFlowService;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.router.XmUriRouter;
import com.ximalaya.ting.android.host.model.account.LoginInfoModel;
import com.ximalaya.ting.android.host.model.base.BaseModel;
import com.ximalaya.ting.android.host.model.feed.VideoInfoBean;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.model.user.ThirdPartyUserInfo;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.view.CenterAlignImageSpan;
import com.ximalaya.ting.android.host.util.view.CenterAlignImageSpanNew;
import com.ximalaya.ting.android.host.util.view.MarginImageSpan;
import com.ximalaya.ting.android.host.view.other.RichWebView;
import com.ximalaya.ting.android.host.view.richtext.OnURLClickListener;
import com.ximalaya.ting.android.host.view.richtext.RichText;
import com.ximalaya.ting.android.loginservice.model.BindLoginInfoModel;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationChannelUtils;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.util.AsyncGson;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.player.MD5;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmsysteminvoke.XmSystemInvokeManager;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.Scanner;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ToolUtil {
    public static final String TAG = ToolUtil.class.getSimpleName();
    /**
     * 获取库Phon表字段
     **/
    private static final String[] PHONES_PROJECTION = new String[]{
            Phone.DISPLAY_NAME, Phone.NUMBER};
    /**
     * 联系人显示名称
     **/
    private static final int PHONES_DISPLAY_NAME_INDEX = 0;
    /**
     * 电话号码
     **/
    private static final int PHONES_NUMBER_INDEX = 1;

    /**
     * fix一些机型退出应用后还会被android.text.TextLine的sCached
     */
    public static void clearTextLineCache() {
        try {
            Field textLineCached = Class.forName("android.text.TextLine").getDeclaredField("sCached");
            textLineCached.setAccessible(true);

            if (textLineCached == null) return;
            Object cached = cached = textLineCached.get(null);
            if (cached != null) {
                // Clear the array.
                for (int i = 0, size = Array.getLength(cached); i < size; i++) {
                    Array.set(cached, i, null);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * fix一些手机退出应用MainActivity还被InputMethodManager的mservedview mnextservedview引用问题
     *
     * @param destContext
     */
    public static void fixInputMethodManagerLeak(Context destContext) {
        if (destContext == null) {
            return;
        }

        InputMethodManager imm = SystemServiceManager.getInputMethodManager(destContext);
        if (imm == null) {
            return;
        }

        String[] arr = new String[]{"mCurRootView", "mServedView", "mNextServedView", "mLastSrvView"};
        Field f = null;
        Object obj_get = null;
        try {
            for (int i = 0; i < arr.length; i++) {
                String param = arr[i];

                f = imm.getClass().getDeclaredField(param);
                if (f.isAccessible() == false) {
                    f.setAccessible(true);
                } // author: sodino mail:<EMAIL>
                obj_get = f.get(imm);
                if (obj_get != null && obj_get instanceof View) {
                    View v_get = (View) obj_get;
                    if (v_get.getContext() == destContext ||
                            (v_get.getContext() instanceof ContextThemeWrapper && ((ContextThemeWrapper) v_get.getContext()).getBaseContext() == destContext)) { // 被InputMethodManager持有引用的context是想要目标销毁的
                        f.set(imm, null); // 置空，破坏掉path to gc节点
                    } else {
                        // 不是想要目标销毁的，即为又进了另一层界面了，不要处理，避免影响原逻辑,也就不用继续for循环了
                        break;
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    /**
//     * 刷新loooer中的message, 用以解决Message持有context对象造成内存泄漏,比如dialog在onCancel时的内存泄漏
//     */
//    public static void flushStackLocalLeaks() {
//        HandlerManager.postOnMainAuto(new Runnable() {
//            @Override
//            public void run() {
//                Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
//                    @Override
//                    public boolean queueIdle() {
//                        HandlerManager.obtainMainHandler().sendMessage(new Message());
//                        return false;
//                    }
//                });
//            }
//        });
//    }

    /**
     * 读取预装应用默认渠道
     *
     * @return
     */
    public static String getChannelFromEtc() {
        String channel = getChannelForEtcStep1();
        if(!TextUtils.isEmpty(channel)) {
            return channel;
        }

        channel = getChannelForEtcStep2();
        return channel;
    }

    private static String getChannelForEtcStep1() {
        Scanner scanner = null;
        try {
            scanner = new Scanner(new File("/system/etc/xmlyconfig.ini"));
            while (scanner.hasNext()) {
                return scanner.next();
            }

        } catch (Exception e) {
            return null;
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                }
            }
        }
        return null;
    }

    private static String getChannelForEtcStep2() {
        Scanner scanner = null;
        try {
            scanner = new Scanner(new File("/product/etc/xmlyconfig.ini"));
            while (scanner.hasNext()) {
                return scanner.next();
            }

        } catch (Exception e) {
            return null;
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                }
            }
        }
        return null;
    }

    private static String channelHuawei;

    /**
     * 读取华为预装应用默认渠道
     *
     * @return
     */
    public static String getChannelForHuaweiPreinstall() {
        if (channelHuawei != null) {
            return channelHuawei;
        }
        try {
            Class<?> clazz = Class.forName("android.os.SystemProperties");
            Method method = clazz.getMethod("get", String.class);
            String path = (String) method.invoke(clazz, "ro.com.ximalaya.ting.android.yz-huawei");
            if (!TextUtils.isEmpty(path)) {
                channelHuawei = "yz-huawei";
                if (!TextUtils.isEmpty(channelHuawei)) {
                    return channelHuawei;
                }
            }
        } catch (Exception e) {
            if (ConstantsOpenSdk.isDebug) {
                e.printStackTrace();
            }
        }

        try {
            Class<?> clazz = Class.forName("android.os.SystemProperties");
            Method method = clazz.getMethod("get", String.class);
            String path = (String) method.invoke(clazz, "ro.ximalaya.ting.yz-huawei");
            if (!TextUtils.isEmpty(path)) {
                channelHuawei = "yz-huawei";
                if (!TextUtils.isEmpty(channelHuawei)) {
                    return channelHuawei;
                }
            }
        } catch (Exception e) {
            if (ConstantsOpenSdk.isDebug) {
                e.printStackTrace();
            }
        }
        channelHuawei = "";
        return channelHuawei;
    }


    private static String channelRongyao;
    /**
     * 读取华为预装应用默认渠道
     *
     * @return
     */
    public static String getChannelForRongyaoPreinstall() {
        if(channelRongyao != null) {
            return channelRongyao;
        }
        try {
            Class<?> clazz = Class.forName("android.os.SystemProperties");
            Method method = clazz.getMethod("get", String.class);
            String path = (String) method.invoke(clazz, "ro.com.ximalaya.ting.android.yz-rongyao");
            if (!TextUtils.isEmpty(path)) {
                channelRongyao = "yz-rongyao";
                if(!TextUtils.isEmpty(channelRongyao)) {
                    return channelRongyao;
                }
            }
        } catch (Exception e) {
            if (ConstantsOpenSdk.isDebug) {
                e.printStackTrace();
            }
        }

        try {
            Class<?> clazz = Class.forName("android.os.SystemProperties");
            Method method = clazz.getMethod("get", String.class);
            String path = (String) method.invoke(clazz, "ro.ximalaya.ting.yz-rongyao");
            if (!TextUtils.isEmpty(path)) {
                channelRongyao = "yz-rongyao";
                if(!TextUtils.isEmpty(channelRongyao)) {
                    return channelRongyao;
                }
            }
        } catch (Exception e) {
            if (ConstantsOpenSdk.isDebug) {
                e.printStackTrace();
            }
        }
        channelRongyao = "";
        return channelRongyao;
    }


    /**
     * 读取预装应用默认渠道
     *
     * @return
     */
    public static String getChannelFromEtcForOppo() {
        String channelForOppo = getChannelForOppoPlan2();

        System.out.println("ToolUtil.getChannelFromEtcForOppo 2 = " + channelForOppo);

        if(TextUtils.isEmpty(channelForOppo)) {
            channelForOppo = getChannelForOppoPlan3();
            System.out.println("ToolUtil.getChannelFromEtcForOppo 3 = " + channelForOppo);
//            if(TextUtils.isEmpty(channelForOppo)) {
//                channelForOppo = getChannelForOppoPlan1();
//                System.out.println("ToolUtil.getChannelFromEtcForOppo 1 = " + channelForOppo);
//            }
        }

        return channelForOppo;
    }


    private static String getChannelForOppoPlan1() {
        Scanner scanner = null;
        try {
            Class<?> clazz = Class.forName("android.os.SystemProperties");
            Method method = clazz.getMethod("get", String.class);
            String path = (String) method.invoke(clazz, "ro.preinstall.ximalaya_path");

            if (TextUtils.isEmpty(path)) {
                return null;
            }

            String fileName = "xmlyconfig.ini";
            File f = new File(path, fileName);
            if (!f.exists() || !f.isFile()) {
                return null;
            }

            scanner = new Scanner(f);

            while (scanner.hasNext()) {
                return scanner.next();
            }
        } catch (Exception e) {
            return null;
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }

        return null;
    }

    private static String getChannelForOppoPlan2() {
        Scanner scanner = null;
        try {
            Class<?> clazz = Class.forName("android.os.SystemProperties");
            Method method = clazz.getMethod("get", String.class);
            String path = (String) method.invoke(clazz, "ro.preinstall.path");

            if (TextUtils.isEmpty(path)) {
                return null;
            }

            String fileName = "xmlyconfig.ini";
            File f = new File(path, fileName);
            if (!f.exists() || !f.isFile()) {
                return null;
            }

            scanner = new Scanner(f);

            while (scanner.hasNext()) {
                return scanner.next();
            }
        } catch (Exception e) {
           return null;
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception ex) {
                    if (ConstantsOpenSdk.isDebug) {
                        ex.printStackTrace();
                    }
                }
            }
        }

        return null;
    }

    private static String getChannelForOppoPlan3() {
        Scanner scanner = null;
        String fileName = "xmlyconfig.ini";
        try {
            File f = new File("/data/etc/appchannel/" + fileName);
            if (!f.exists() || !f.isFile()) {
                f = new File("/system/etc/appchannel/" + fileName);
            }

            scanner = new Scanner(f);

            while (scanner.hasNext()) {
                return scanner.next();
            }

        } catch (Exception e) {
            return null;
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                }
            }
        }
        return null;
    }

    public static String getChannelForVivoPreInstall() {
        Scanner scanner = null;
        try {
            Class<?> clazz = Class.forName("android.os.SystemProperties");
            Method method = clazz.getMethod("get", String.class);
            String path = (String) method.invoke(clazz, "ro.preinstall.path");

            if (TextUtils.isEmpty(path)) {
                return null;
            }

            Logger.i(TAG, "vivo preinstall channel path: " + path);
            String fileName = "xmlyconfig.ini";
            File channelFile = new File(path + fileName);

            scanner = new Scanner(channelFile);

            return scanner.next();
        } catch (Exception e) {
            if (ConstantsOpenSdk.isDebug) {
                e.printStackTrace();
            }
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return null;
    }

    /**
     * 判断该应用是否来自小米预装渠道
     * NOTICE: 该方法在2018年之后预装的机器上也会返回true
     *
     * @param pkg
     * @return
     */
    public static boolean checkPreinstallAppOld(String pkg) {
        try {
            // 通过反射获取接口：miui.os.MiuiInit.isPreinstalledPackage， 小米旧的防覆盖方案中的方法
            Class<?> miui = Class.forName("miui.os.MiuiInit");
            Method method = miui.getMethod("isPreinstalledPackage",
                    String.class);
            // 调用接口
            Boolean result = (Boolean) method.invoke(null, pkg);
            return result;
        } catch (Exception e) {
            // 当前机型还没有这个接口，无法判断pkg是否为预装的APP
            // TO-DO
        }

        return false;
    }

    private static int mXiaomiPreInstallApp = -1;

    /**
     * 判断该应用是否来自小米预装渠道，2018年使用的新方法
     *
     * @param pkg
     * @return
     */
    public static boolean checkPreinstallApp2018(String pkg) {
        if (mXiaomiPreInstallApp == -1) {
            try {
                // 通过反射获取接口：miui.os.MiuiInit.getMiuiChannelPath， 小米新的防覆盖方案中的方法
                Class<?> miui = Class.forName("miui.os.MiuiInit");
                Method method = miui.getMethod("getMiuiChannelPath",
                        String.class);
                // 调用接口
                String result = (String) method.invoke(null, pkg);
                mXiaomiPreInstallApp = !TextUtils.isEmpty(result) ? 1 : 0;
            } catch (Exception e) {
                // 当前机型还没有这个接口，无法判断pkg是否为预装的APP
                // TO-DO
            }
        }

        return mXiaomiPreInstallApp > 0;
    }


    public static Map<String, String> getQueryMap(String query) {
        if (TextUtils.isEmpty(query))
            return null;
        String[] params = query.split("&");
        Map<String, String> map = new HashMap<>();
        for (String param : params) {
            String[] arrayStr = param.split("=");
            String name = (arrayStr.length > 0) ? arrayStr[0] : "";
            String value = (arrayStr.length > 1) ? arrayStr[1] : "";
            if (!TextUtils.isEmpty(name)) {
                map.put(name, value);
            }
        }
        return map;
    }

    public static String getFirstTag(String tag) {
        if (TextUtils.isEmpty(tag)) {
            return "";
        }
        String[] strList = tag.split(",");
        if (strList.length > 0) {
            return strList[0];
        } else {
            return "";
        }
    }

    public static String getCloudUUID() {
        return getSystemProperty("ro.aliyun.clouduuid", "false");
    }

    public static String getDebugSystemProperty(String key, String defaultValue) {
        if (ConstantsOpenSdk.isDebug) {
            return getSystemProperty(key, defaultValue);
        }
        return defaultValue;
    }

    public static String getSystemProperty(String key, String defaultValue) {
        try {
            Class<?> SystemProperties = Class
                    .forName("android.os.SystemProperties");
            Method m = SystemProperties.getMethod("get", String.class,
                    String.class);
            String result = (String) m.invoke(null, key, defaultValue);
            return result;
        } catch (Exception e) {
            Logger.e(e);
        }
        return defaultValue;
    }

    public static boolean setSystemProperty(String key, String defaultValue) {
        try {
            Class<?> SystemProperties = Class
                    .forName("android.os.SystemProperties");
            Method m = SystemProperties.getMethod("set", String.class,
                    String.class);
            String result = (String) m.invoke(null, key, defaultValue);
            return true;
        } catch (Exception e) {
            Logger.e(e);
        }
        return false;
    }

    public static String addFilePrefix(String path) {
        if (path != null && path.length() > 0 && !path.startsWith("http") && !path.startsWith("file://")) {
            return "file://" + path;
        }
        return path;
    }

    public static @Nullable
    Notification createNotification(Context context,
                                    String title, String ticker, String msg, PendingIntent pi) {
        Notification notification;
        if (TextUtils.isEmpty(title)) {
            title = context.getResources().getString(R.string.host_app_name);
        }
        NotificationCompat.Builder builder = null;

        builder = NotificationChannelUtils.newNotificationBuilder(context)
                .setWhen(System.currentTimeMillis())
                .setSmallIcon(R.drawable.notification_icon).setTicker(ticker)
                .setContentTitle(title).setContentText(msg)
                .setContentIntent(pi)
                .setAutoCancel(true).setDefaults(Notification.DEFAULT_SOUND)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(msg));

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN) {
            builder.setPriority(Notification.PRIORITY_HIGH);
        }

        try {
            notification = builder.build();
            notification.when = System.currentTimeMillis();
            return notification;
        } catch (NullPointerException e) {  //系统调用会产生空指针，无法修复，只能在此处catch
            e.printStackTrace();
        }

        return null;
    }

    public static void gotoFlowActivityPage(Context context) {
        gotoFlowActivityPage(context, false);
    }
    /**
     * 跳转到激活页面
     * @param context
     * @param isGroup 是否是免流聚合页
     */
    public static void gotoFlowActivityPage(Context context, boolean isGroup) {
        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
        if (freeFlowService == null) {
            return;
        }
        String url = freeFlowService.getH5Url(AppConstants.environmentId != AppConstants.ENVIRONMENT_ON_LINE, isGroup);
        gotoFlowPage(context, url);
    }

    public static void gotoFlowPage(Context context, String url) {
        Activity activity = MainApplication.getTopActivity();
        if (activity == null || activity.isFinishing()) {
            return;
        }

        if (activity instanceof MainActivity) {
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
            bundle.putBoolean(BundleKeyConstants.KEY_SHOW_TITLE, true);
            bundle.putBoolean(BundleKeyConstants.KEY_FIT_SOFT_KEYBOARD, true);
            bundle.putBoolean(NativeHybridFragment.IS_EXTERNAL_URL, false);
            ((MainActivity) activity).startFragment(NativeHybridFragment.newInstance(bundle));
        } else {
            Intent intent = new Intent(activity, WebActivity.class);
            intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, url);
            intent.putExtra(BundleKeyConstants.KEY_FIT_SOFT_KEYBOARD, true);
            intent.putExtra(NativeHybridFragment.IS_EXTERNAL_URL, false);
            activity.startActivityForResult(intent, FreeFlowService.ORDER_PAGE);
        }

        FreeFlowDataUtil.getInstance(context).removeUpdateOrderStatusDate();
    }

    /**
     * 跳转到免流激活页面
     */
    public static void gotoFlowActivePage() {
        Activity activity = MainApplication.getTopActivity();
        if (activity == null || activity.isFinishing()) {
            return;
        }

        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
        if (freeFlowService == null) {
            return;
        }

        String url = freeFlowService.getActiveUrl();
        if(activity instanceof MainActivity) {
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
            bundle.putBoolean(BundleKeyConstants.KEY_SHOW_TITLE, true);
            bundle.putBoolean(BundleKeyConstants.KEY_FIT_SOFT_KEYBOARD, true);
            ((MainActivity) activity).startFragment(NativeHybridFragment.newInstance(bundle));
        } else {
            Intent intent = new Intent(activity, WebActivity.class);
            intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, url);
            intent.putExtra(BundleKeyConstants.KEY_FIT_SOFT_KEYBOARD, true);
            activity.startActivity(intent);
        }
    }

    public static String getSimpleSystemVersion() {
        return "Android" + android.os.Build.VERSION.SDK_INT;
    }

    public static List<ThirdPartyUserInfo> getSimContacts(Context contenxt) {
        List<ThirdPartyUserInfo> contacts = new ArrayList<>();
        Cursor phoneCursor = null;
        Cursor simCursor = null;
        try {
            ContentResolver resolver = contenxt.getContentResolver();

            // 得到手机通讯录联系人信息
            phoneCursor = resolver.query(Phone.CONTENT_URI, PHONES_PROJECTION,
                    null, null, null);

            if (phoneCursor != null) {
                while (phoneCursor.moveToNext()) {

                    // 得到手机号码
                    String phoneNumber = phoneCursor
                            .getString(PHONES_NUMBER_INDEX);
                    // 当手机号码为空的或者为空字段 跳过当前循环
                    if (StringUtil.isEmpty(phoneNumber))
                        continue;
                    phoneNumber = phoneNumber.replaceAll("[-——_ \\*\\+]", "");// 过滤特殊字符
                    // 得到联系人名称
                    String contactName = phoneCursor
                            .getString(PHONES_DISPLAY_NAME_INDEX);

                    ThirdPartyUserInfo contact = new ThirdPartyUserInfo();

                    contact.setNickname(contactName);
                    contact.setIdentity(phoneNumber);
                    contacts.add(contact);

                }

            }
            String str = ToolUtil.isSimExist(contenxt);
            if (!"1".equals(str)) {// SIM卡状态不正常
                return contacts;
            }
            // 获取Sims卡联系人
            Uri uri = Uri.parse("content://icc/adn");
            simCursor = resolver
                    .query(uri, PHONES_PROJECTION, null, null, null);

            if (simCursor != null) {
                while (simCursor.moveToNext()) {

                    // 得到手机号码
                    String phoneNumber = simCursor
                            .getString(PHONES_NUMBER_INDEX);
                    // 当手机号码为空的或者为空字段 跳过当前循环
                    if (StringUtil.isEmpty(phoneNumber))
                        continue;
                    phoneNumber = phoneNumber.replaceAll("[-——_ \\*\\+]", "");// 过滤特殊字符
                    // 得到联系人名称
                    String contactName = simCursor
                            .getString(PHONES_DISPLAY_NAME_INDEX);

                    // Sim卡中没有联系人头像
                    ThirdPartyUserInfo contact = new ThirdPartyUserInfo("");

                    contact.setNickname(contactName);
                    contact.setIdentity(phoneNumber);
                    contacts.add(contact);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (phoneCursor != null)
                    phoneCursor.close();
                if (simCursor != null)
                    simCursor.close();
            } catch (Exception e) {
            }
        }
        return contacts;
    }

    public static String isSimExist(Context context) {
        String mString = "";
        int simState = TelephonyManager.SIM_STATE_UNKNOWN;
        try {
            TelephonyManager mTelephonyManager = SystemServiceManager.getTelephonyManager(context);
            simState = mTelephonyManager.getSimState();
        } catch (Exception e) {
        }

        switch (simState) {

            case TelephonyManager.SIM_STATE_ABSENT:

                mString = "检测不到SIM卡";

                // do something

                break;

            case TelephonyManager.SIM_STATE_NETWORK_LOCKED:

                mString = "需要NetworkPIN解锁";

                // do something

                break;

            case TelephonyManager.SIM_STATE_PIN_REQUIRED:

                mString = "需要PIN解锁";

                // do something

                break;

            case TelephonyManager.SIM_STATE_PUK_REQUIRED:

                mString = "需要PUN解锁";

                // do something

                break;

            case TelephonyManager.SIM_STATE_READY:

                mString = "1";
                break;

            case TelephonyManager.SIM_STATE_UNKNOWN:

                mString = "未知状态";

                // do something

                break;

        }
        return mString;
    }

    public static void showUploadPhoneNumDialog(final Activity context) {
        new DialogBuilder(context).setTitle("提示")
                .setOkBtn("确定", new DialogBuilder.DialogCallback() {

                    @Override
                    public void onExecute() {
                        uploadAllPhoneNum(context, false);
                    }
                }).setCancelBtn("取消").setMessage("看看手机通讯录里谁在使用喜马拉雅？")
                .showConfirm();
    }

    public static void uploadAllPhoneNum(final Activity context, boolean force) {
        if (!force) {
            String lastUploadPhoneNumTime = SharedPreferencesUtil.getInstance(
                    context).getString("last_upload_phone_num_time");
            if (!TextUtils.isEmpty(lastUploadPhoneNumTime)) {
                long days = (System.currentTimeMillis() - Long
                        .valueOf(lastUploadPhoneNumTime)) / (1000 * 60 * 60 * 24);
                if (days < 15) {
                    return;
                }
            }
            SharedPreferencesUtil.getInstance(context).saveString(
                    "last_upload_phone_num_time", System.currentTimeMillis() + "");
        }

        new AsyncTask<Object, Void, List<ThirdPartyUserInfo>>() {
            Context mCt;

            @Override
            protected List<ThirdPartyUserInfo> doInBackground(Object... params) {
                mCt = (Context) params[0];
                return getSimContacts(mCt);
            }

            protected void onPostExecute(final List<ThirdPartyUserInfo> result) {

                if (result != null && !result.isEmpty()) {
                    if (AppConstants.IS_NOTIFY_UPLOAD_CONTACTS) {
                        if (context.isFinishing()) {
                            return;
                        }
                        new DialogBuilder(context)
                                .setOkBtn("允许",
                                        new DialogBuilder.DialogCallback() {

                                            @Override
                                            public void onExecute() {
                                                doUploadContacts(result, "");
                                            }
                                        }).setCancelBtn("拒绝")
                                .setMessage("是否允许开启找听友功能并且同步通讯录数据？")
                                .showConfirm();
                    } else {
                        doUploadContacts(result, "");
                    }
                }
            }

        }.execute(context);
    }

    public static void doUploadContacts(final List<ThirdPartyUserInfo> list, String hash) {
        LoginInfoModelNew loginInfoModel = UserInfoMannage.getInstance().getUser();
        if (loginInfoModel == null)
            return;

        JSONArray array = new JSONArray();
        try {
            for (ThirdPartyUserInfo info : list) {
                JSONObject object = new JSONObject();
                object.put("mobileHash", info.getPhoneHash());
                array.put(object);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (array.length() == 0) {
            return;
        }

        CommonRequestM.getInstanse().uploadContacts(array, new IDataCallBack<String>() {
            @Override
            public void onSuccess(String object) {
                SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).saveString(PreferenceConstantsInHost.KEY_CONTACT_HASH, hash);
                PhoneContactsManager.getInstance().afterUploadContacts();
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    /**
     * 网络请求是否成功
     */
    public static JSONObject requestIsSuccess(String responseContent) {

        if (TextUtils.isEmpty(responseContent))
            return null;
        try {
            JSONObject jsonObject = new JSONObject(responseContent);
            if (jsonObject.optInt("ret", -1) == BaseModel.SUCCESS) {
                return jsonObject;
            }
        } catch (Exception e) {
        }
        return null;
    }

    /**
     * 取消通知
     *
     * @param context
     * @param id
     */
    public static void cancelNotification(Context context, int id) {
        NotificationManager nm = SystemServiceManager.getNotificationManager(context);
        if (nm != null) {
            try {
                nm.cancel(id);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 为当前应用添加桌面快捷方式
     *
     * @param context
     */
    public static void addShortcut(Activity context) {
        SharedPreferencesUtil shared = SharedPreferencesUtil.getInstance(context);
        boolean isCreatedShortcut = shared.getBoolean("isCreatedShortcut", false);
        if (isCreatedShortcut)
            return;

        Intent shortcut = new Intent("com.android.launcher.action.INSTALL_SHORTCUT");
        Intent shortcutIntent = new Intent(Intent.ACTION_MAIN);
        shortcutIntent.setComponent(new ComponentName(context.getPackageName(), context.getPackageName() + "."
                + context.getClass().getSimpleName()));
        shortcutIntent.setClass(context.getApplicationContext(), context.getClass());

        shortcutIntent.setAction(Intent.ACTION_MAIN);
        shortcutIntent.addCategory(Intent.CATEGORY_LAUNCHER);
        shortcutIntent.setFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
        shortcut.putExtra(Intent.EXTRA_SHORTCUT_INTENT, shortcutIntent);

        // 快捷方式名称
        shortcut.putExtra(Intent.EXTRA_SHORTCUT_NAME, context.getString(R.string.host_app_name));
        // 不允许重复创建（不一定有效）
        shortcut.putExtra("duplicate", false);
        // 快捷方式的图标
        Parcelable iconResource = Intent.ShortcutIconResource.fromContext(context.getApplicationContext(), com.ximalaya.ting.android.host.R.mipmap.ic_launcher_ting);
        shortcut.putExtra(Intent.EXTRA_SHORTCUT_ICON_RESOURCE, iconResource);

        context.sendBroadcast(shortcut);

        shared.saveBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_IS_CREATED_SHORTCUT, true);
    }


    private static ApplicationInfo mApplicationInfo;

    public static String getMetaData(Context context, String key) {
        try {
            if (mApplicationInfo == null) {
                mApplicationInfo = context.getPackageManager()
                        .getApplicationInfo(context.getPackageName(),
                                PackageManager.GET_META_DATA);
            }
            String value = mApplicationInfo.metaData.getString(key);

            return value;
        } catch (NameNotFoundException e) {
            e.printStackTrace();
            return null;
        } catch (ClassCastException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static boolean getMetaData(Context context, String key, boolean defaultValue) {
        try {
            if (mApplicationInfo == null) {
                mApplicationInfo = context.getPackageManager()
                        .getApplicationInfo(context.getPackageName(),
                                PackageManager.GET_META_DATA);
            }
            boolean value = mApplicationInfo.metaData.getBoolean(key);

            return value;
        } catch (NameNotFoundException e) {
            e.printStackTrace();
            return defaultValue;
        } catch (ClassCastException e) {
            e.printStackTrace();
            return defaultValue;
        }
    }

    public static String getMetaDataCheckQuot(Context context, String key) {
        try {
            if (mApplicationInfo == null) {
                mApplicationInfo = context.getPackageManager()
                        .getApplicationInfo(context.getPackageName(),
                                PackageManager.GET_META_DATA);
            }

            String value = mApplicationInfo.metaData.getString(key);
            if (value != null && value.contains("\"")) {
                return value.replace("\"", "");
            }
            return value;
        } catch (NameNotFoundException e) {
            e.printStackTrace();
            return null;
        } catch (ClassCastException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static MyProgressDialog createProgressDialog(Context context, String content) {
        MyProgressDialog pd = new MyProgressDialog(context);
        pd.setCanceledOnTouchOutside(false);
        pd.setMessage(content);
        return pd;
    }

    public static MyProgressDialog createProgressDialog(Context context, String title, String content) {
        MyProgressDialog pd = new MyProgressDialog(context);
        pd.setCanceledOnTouchOutside(false);
        pd.setTitle(title);
        pd.setMessage(content);
        return pd;
    }

    //	public static String getplayScehdule(String curPosStr, long duration)
//	{
//		long curPos = -1L;
//		try
//		{
//			curPos = Long.valueOf(curPosStr.trim());
//
//		} catch (NumberFormatException e)
//		{
//		}
//		return getPlaySchedule(curPos, duration);
//
//	}
    public static String getPlaySchedule(long curPos, long duration) {

        if (curPos == PlayerConstants.PLAY_COMPLETE) {
            return "已播完";
        } else if (curPos == PlayerConstants.PLAY_NO_HISTORY) {
            return null;
        } else {

            if (duration <= 0 || curPos < 0)
                return null;

            double time = curPos / (duration * 1000d) * 100;
            int percent = (int) time;
            if (percent >= 97) {
                return "已播完";
            } else if (percent >= 1) {
                return "已播" + percent + "%";
            } else {
                return "已播" + "1%";
            }
        }
    }

    public static String getPlayScheduleNew(long curPos, long duration) {

        if (curPos == PlayerConstants.PLAY_COMPLETE) {
            return "已播完";
        } else if (curPos == PlayerConstants.PLAY_NO_HISTORY) {
            return null;
        } else {

            if (duration <= 0 || curPos < 0)
                return null;

            double time = curPos / (duration * 1000d) * 100;
            int percent = (int) time;
            if (percent >= 97) {
                return "已播完";
            } else if (percent >= 1) {
                return percent + "%";
            } else {
                return "1%";
            }
        }
    }

    public static boolean isTrackPlayComplete(long curPos, long duration) {
        if (curPos == PlayerConstants.PLAY_COMPLETE) {
            return true;
        } else if (curPos == PlayerConstants.PLAY_NO_HISTORY) {
            return false;
        } else {
            if (duration <= 0 || curPos < 0) {
                return false;
            }
            double time = curPos / (duration * 1000d) * 100;
            int percent = (int) time;
            if (percent >= 97) {
                return true;
            } else {
                return false;
            }
        }
    }


    public static boolean isApkDebugable(Context context) {
        try {
            ApplicationInfo info = context.getApplicationInfo();
            return (info.flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
        } catch (Exception e) {

        }
        return false;
    }

    /**
     * 给声音列表搭上new标志图
     *
     * @param totalNewCount   服务端给定的新声音总数
     * @param curPage         当前页
     * @param totalTrackCount 声音总数
     * @param isRecDesc       排序顺序
     * @param maxPageId       最大页数
     * @param trackList       当前页声音
     */
    public static void stampNewFlag(int totalNewCount,
                                    int curPage, int totalTrackCount, boolean isRecDesc,
                                    int maxPageId, List<TrackM> trackList) {
        if (trackList == null || trackList.isEmpty() || totalNewCount <= 0 || totalNewCount > totalTrackCount)
            return;

        int curPageNewNum = 0;//当前页面标ＮＥＷ声音数
        int newTrackPage = 0;// 标NEW的页数
        int pageSize = DTransferConstants.DEFAULT_PAGE_SIZE;
        if (AlbumPageSizeManager.getInstance().isEnable()) {
            pageSize = AlbumPageSizeManager.getInstance().getPageSize();
        }

        //正序,从第一页开始
        if (isRecDesc) {
            //所有的新声音数按默认每页个数分页。
            if (totalNewCount % pageSize != 0) {
                newTrackPage = totalNewCount
                        / pageSize + 1;
            } else {
                newTrackPage = totalNewCount
                        / pageSize;
            }

            //计算当前页的新声音数
            if (curPage < newTrackPage) {
                curPageNewNum = pageSize;
            } else if (curPage == newTrackPage) {
                curPageNewNum = totalNewCount
                        % pageSize;
            }

            if (curPageNewNum > 0 && trackList.size() >= curPageNewNum) {
                for (int i = 0; i < curPageNewNum; i++) {
                    trackList.get(i).setNewTrack(true);//从第一条开始打标志,一共newTrackNum条
                }
            }
        } else {//倒序,从最后一页开始
            int lastPageTrackNum = totalTrackCount
                    % pageSize;//最后一页声音数

            //计算新声音数要显示的页数
            if (lastPageTrackNum >= totalNewCount) {
                newTrackPage = 1;
            } else {
                if ((totalNewCount - lastPageTrackNum)
                        % pageSize != 0) {
                    newTrackPage = (totalNewCount - lastPageTrackNum)
                            / pageSize + 2;
                } else {
                    newTrackPage = (totalNewCount - lastPageTrackNum)
                            / pageSize + 1;
                }
            }

            if (newTrackPage == 1) {
                if (curPage == maxPageId) {//当前页即最后页
                    curPageNewNum = totalNewCount;
                    //从最后一条往前标记
                    for (int i = lastPageTrackNum - 1; i >= (lastPageTrackNum - curPageNewNum); i--) {
                        if (i >= 0 && i < trackList.size()) {
                            trackList.get(i).setNewTrack(true);
                        }
                    }
                }
            } else {
                if (curPage == maxPageId - newTrackPage + 1) {
                    curPageNewNum = (totalNewCount - lastPageTrackNum)
                            % pageSize;
                    for (int i = pageSize - 1; i >= (pageSize - curPageNewNum); i--) {
                        if (i >= 0 && i < trackList.size()) {
                            trackList.get(i).setNewTrack(true);
                        }
                    }

                } else if (curPage == maxPageId) {
                    curPageNewNum = lastPageTrackNum;
                    for (int i = 0; i < curPageNewNum; i++) {
                        if (i < trackList.size()) {
                            trackList.get(i).setNewTrack(true);
                        }
                    }
                } else if (curPage > maxPageId - newTrackPage + 1) {
                    curPageNewNum = pageSize;
                    for (int i = 0; i < curPageNewNum; i++) {
                        if (i >= 0 && i < trackList.size()) {
                            trackList.get(i).setNewTrack(true);
                        }
                    }
                }
            }
        }
    }

    public static String getPhoneManufacturer() {
        String manufacturer = android.os.Build.MANUFACTURER;
        if (TextUtils.isEmpty(manufacturer))
            return "";
        else
            return manufacturer;
    }

    @Nullable
    public static String getXiaomiVersion() {
        try {
            return com.ximalaya.ting.android.xmutil.BuildProperties.getSystemProperty("ro.miui.ui.version.name", null);
        } catch (Exception e) {
            //
        }
        return null;
    }

    public static String getDownLoadLocation(String location) {
        String string = null;
        if (!TextUtils.isEmpty(location)) {
            String[] str = location.split("\\/");
            if (str != null && str.length > 3) {
                StringBuilder sb = null;
                for (int i = 0; i < 3; i++) {
                    if (sb == null) {
                        sb = new StringBuilder();
                        sb.append(str[i]);
                    } else {
                        sb.append("/");
                        sb.append(str[i]);
                    }
                }
                if (sb != null)
                    string = sb.toString();
            }
        }
        return string;
    }

    public static String getDownloadLocation() {
        ArrayList<String> voldFilePaths = RouteServiceUtil.getStoragePathManager().getVoldFilePaths();
        if (voldFilePaths != null && voldFilePaths.size() > 0) {
            if (voldFilePaths.size() == 1) {
                return "存储位置1";
            } else {
                if (RouteServiceUtil.getStoragePathManager().getCurSavePath() != null
                        && RouteServiceUtil.getStoragePathManager().getCurSavePath().contains(getDownLoadLocation(voldFilePaths.get(1)))) {
                    return "存储位置2";
                } else {
                    return "存储位置1";
                }
            }
        }
        return "";
    }

    /**
     * 转换评论数方法
     *
     * @param number
     * @return
     */
    public static String getCommentNumber(int number) {
        if (number <= 0) {
            return "0";
        } else if (number >= 1000) {
            return "999+";
        } else {
            return "" + number;
        }
    }

    public static SpannableString getMiddleLineStr(String str) {
        if (TextUtils.isEmpty(str)) return null;
        SpannableString sp = new SpannableString(str.trim());
        sp.setSpan(new StrikethroughSpan(), 0, str.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        return sp;
    }

    /**
     * 获取discountPrice带中划线
     *
     * @param number   discountPrice
     * @param specific 保留几位小数
     * @return
     */
    public static SpannableString getMiddleLineStr(double number, int specific) {
        String resultNum = StringUtil.subZeroAndDot(number, specific);
        if (resultNum == null || resultNum.equals("0.00")) return null;
        String result = resultNum + "喜点";
        SpannableString sp = new SpannableString(result.trim());
        sp.setSpan(new StrikethroughSpan(), 0, result.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        return sp;
    }

    public static SpannableString getMiddleLineStrWithoutUnit(double number, int specific) {
        String resultNum = StringUtil.subZeroAndDot(number, specific);
        if (resultNum == null || resultNum.equals("0.00")) return null;
        String result = resultNum;
        SpannableString sp = new SpannableString(result.trim());
        sp.setSpan(new StrikethroughSpan(), 0, result.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        return sp;
    }

    public static float convertRatingScore(float ratingScore) {
        float number = 0.0f;
        if (ratingScore > 0.0f && ratingScore < 1.0f) {
            number = 0.5f;
        } else if (ratingScore <= 5.0f) {
            number = ratingScore;
        } else {
            number = 5.0f;
        }

        return number;
    }

    public static float convertRatingScore10(float score) {
        if (score < 0)
            return 0;
        if (score > 10)
            return 10;
        return score / 2;
    }


    public static ImageGetter getImageGetter(final Context context) {

        ImageGetter imageGetter = new ImageGetter() {
            @Override
            public Drawable getDrawable(String source) {
                int id = Integer.parseInt(source);
                //根据id从资源文件中获取图片对象
                Drawable d = ContextCompat.getDrawable(BaseApplication.getMainActivity() != null ?
                                BaseApplication.getMainActivity() : context
                        , id);
                d.setBounds(0, 0, d.getIntrinsicWidth(), d.getIntrinsicHeight());
                return d;
            }
        };
        return imageGetter;
    }

    public static Spanned getAlbumTitleWithPicAhead(Context context, String intro, int resId_1) {
        if (TextUtils.isEmpty(intro) || context == null) return null;

        if (resId_1 > 0) {
            StringBuffer result = new StringBuffer();
            result.append("<img src=\"" + resId_1 + "\">  ");
            result.append(intro);
            return Html.fromHtml(result.toString(), getImageGetter(context), null);
        } else {
            return new SpannedString(intro);
        }
    }

    public static CharSequence getAlbumTitleWithTextAhead(Context context, CharSequence title, String text) {
        if (TextUtils.isEmpty(title) || context == null) return null;
        text = TextUtils.isEmpty(text) ? "完｜" : text;
        String result = text + title;
        SpannableString spannableString = new SpannableString(result);
        spannableString.setSpan(new AbsoluteSizeSpan(14, true), 0, text.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), 0, text.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#F59C20")), 0, text.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        return spannableString;
    }

    public static Spannable getSpannedText(String text) {
        text = TextUtils.isEmpty(text) ? "完｜" : text;
        SpannableString spannableString = new SpannableString(text);
        spannableString.setSpan(new AbsoluteSizeSpan(14, true), 0, text.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), 0, text.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#F59C20")), 0, text.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        return spannableString;
    }


    public static Spannable getAlbumFinishedSpannedText() {
        return getSpannedText(null);
    }

    public static Spanned getTrackTitleWithPicAhead(Context context, String intro, List<Integer> resIds, int maxCount) {
        if (TextUtils.isEmpty(intro) || context == null) return null;

        if (!ToolUtil.isEmptyCollects(resIds)) {
            StringBuffer result = new StringBuffer();

            int count = 0;

            for (int i = 0; i < resIds.size() && count < maxCount; i++) {
                int resId = resIds.get(i) == null ? 0 : resIds.get(i);

                if (resId > 0) {
                    count++;
                    result.append("<img src=\"").append(resId).append("\">  ");
                }
            }
            result.append(intro);

            return Html.fromHtml(result.toString(), getImageGetter(context), null);
        } else {
            return new SpannedString(intro);
        }
    }

    /**
     * @param spaceCount 用于给一些图片添加margin
     * @return
     */
    public static SpannableString getTrackTitleWithPicAheadCenterAlign(Context context, String intro, List<Integer> resIds, int maxCount, int spaceCount) {
        if (TextUtils.isEmpty(intro) || context == null) return null;

        StringBuilder spanSpaceStr = new StringBuilder();
        if (spaceCount > 0) {
            for (int i = 0; i < spaceCount; i++) {
                spanSpaceStr.append(" ");
            }
        }

        StringBuilder sb = new StringBuilder();
        if (ToolUtil.isEmptyCollects(resIds)) {
            return new SpannableString(intro);
        }

        for (int i = 0; i < Math.min(maxCount, resIds.size()); i++) {
            sb.append(" ");
            if (spaceCount > 0) {
                sb.append(spanSpaceStr);
            }
        }
        sb.append(intro);
        intro = sb.toString();

        SpannableString title = new SpannableString(intro);

        int count = 0;

        for (int i = 0; i < resIds.size() && count < maxCount; i++) {
            int resId = resIds.get(i) == null ? 0 : resIds.get(i);

            //根据id从资源文件中获取图片对象
            Drawable d = ContextCompat.getDrawable(context, resId);
            d.setBounds(0, 0, d.getIntrinsicWidth(), d.getIntrinsicHeight());
            CenterAlignImageSpan imageSpan = new CenterAlignImageSpan(d);
            int index = count;
            if (spaceCount > 0) {
                index += spaceCount * i;
            }
            title.setSpan(imageSpan, index, index + 1, ImageSpan.ALIGN_BASELINE);
            //fix:多个标签只显示一个标签 modified by zhangzhifu
            count++;
        }

        return title;
    }

    public static SpannableString getTrackTitleWithPicAheadCenterAlign(Context context, String intro, List<Integer> resIds, int maxCount) {
        return getTrackTitleWithPicAheadCenterAlign(context, intro, resIds, maxCount, 0);
    }

    public static SpannableString getTitleWithPicAheadCenterAlign(Context context, String intro, @DrawableRes int resId) {
        if (TextUtils.isEmpty(intro) || context == null) return null;
        StringBuilder sb = new StringBuilder();
        //根据id从资源文件中获取图片对象
        Drawable d = ContextCompat.getDrawable(context, resId);
        if (d != null) {
            sb.append(" ");
        }
        sb.append(intro);
        SpannableString title = new SpannableString(sb.toString());
        if (d != null) {
            d.setBounds(0, 0, d.getIntrinsicWidth(), d.getIntrinsicHeight());
            CenterAlignImageSpan imageSpan = new CenterAlignImageSpan(d);
            title.setSpan(imageSpan, 0, 1, ImageSpan.ALIGN_BASELINE);
        }
        return title;
    }

    public static SpannableString getTitleWithPicAheadCenterAlignMargin(Context context, String intro, @DrawableRes int resId,int marginBottom) {
        if (TextUtils.isEmpty(intro) || context == null) return null;
        StringBuilder sb = new StringBuilder();
        //根据id从资源文件中获取图片对象
        Drawable d = ContextCompat.getDrawable(context, resId);
        if (d != null) {
            sb.append(" ");
        }
        sb.append(intro);
        SpannableString title = new SpannableString(sb.toString());
        if (d != null) {
            d.setBounds(0, 0, d.getIntrinsicWidth(), d.getIntrinsicHeight());
            MarginImageSpan imageSpan = new MarginImageSpan(d, marginBottom);
            title.setSpan(imageSpan, 0, 1, ImageSpan.ALIGN_BASELINE);
        }
        return title;
    }

    public static SpannableString getTitleWithPicAheadCenterAlignWithWidthAndHeight(Context context, String intro,
                                                                                    @DrawableRes int resId, int width, int height) {
        if (android.text.TextUtils.isEmpty(intro) || context == null) return null;
        StringBuilder sb = new StringBuilder();
        //根据id从资源文件中获取图片对象
        Drawable d = ContextCompat.getDrawable(context, resId);
        if (d != null) {
            sb.append(" ");
        }
        sb.append(intro);
        SpannableString title = new SpannableString(sb.toString());
        if (d != null && width > 0 && height > 0) {
            d.setBounds(0, 0, width, height);
            CenterAlignImageSpan imageSpan = new CenterAlignImageSpan(d);
            title.setSpan(imageSpan, 0, 1, ImageSpan.ALIGN_BASELINE);
        }
        return title;
    }

    public static SpannableString getTitleWithPicAheadCenterAlignAndFitHeight(Context context, String intro
            , @DrawableRes int resIds, int maxHeight) {
        List<Integer> resList = new ArrayList<>();
        resList.add(resIds);
        return getTitleWithPicAheadCenterAlignAndFitHeight(context, intro, resList, 1, maxHeight, 1);
    }

    public static SpannableString getTitleWithPicAheadCenterAlignAndFitHeight(Context context, String intro
            , @DrawableRes int resIds, int maxHeight, int filterColor) {
        List<Integer> resList = new ArrayList<>();
        resList.add(resIds);
        return getTitleWithPicAheadCenterAlignAndFitHeight(context, intro, resList, 1, maxHeight, filterColor);
    }

    public static SpannableString getTitleWithPicAheadCenterAlignAndFitHeight(Context context, String intro
            , List<Integer> resIds, int maxCount, int maxHeight, int filterColor) {
        if (TextUtils.isEmpty(intro) || context == null) return null;

        StringBuilder sb = new StringBuilder();
        if (ToolUtil.isEmptyCollects(resIds)) {
            return new SpannableString(intro);
        }

        for (int i = 0; i < Math.min(maxCount, resIds.size()); i++) {
            if (i == 0) {
                sb.append(" ");
            } else {
                sb.append("  ");
            }
        }
        sb.append(intro);
        intro = sb.toString();

        SpannableString title = new SpannableString(intro);

        int count = 0;

        for (int i = 0; i < resIds.size() && count < maxCount; i++) {
            int resId = resIds.get(i) == null ? 0 : resIds.get(i);

            //根据id从资源文件中获取图片对象
            Drawable d = ContextCompat.getDrawable(context, resId);
            if (d != null) {
                int width = d.getIntrinsicWidth();
                int height = d.getIntrinsicHeight();
                if (height > maxHeight) {
                    width = width * maxHeight / height;
                    height = maxHeight;
                }
                d.setBounds(0, 0, width, height);
                if (filterColor != 1) {
                    d.mutate().setColorFilter(filterColor, PorterDuff.Mode.SRC_IN);
                }
                CenterAlignImageSpan imageSpan = new CenterAlignImageSpan(d);
                title.setSpan(imageSpan, count * 2, count * 2 + 1, ImageSpan.ALIGN_BASELINE);
                count++;
            }
        }

        return title;
    }


    public static SpannableString getTitleWithPicAheadCenterAlignSingle(Context context, String intro
            ,  @DrawableRes int resId, int maxHeight, int filterColor) {
        if (TextUtils.isEmpty(intro) || context == null) return null;

        StringBuilder sb = new StringBuilder();
        sb.append(" ");
        sb.append(intro);
        sb.insert(0, "\u200B"); // 在图片前添加零宽度空格
        sb.append("\u200B"); // 在图片后添加零宽度空格
        intro = sb.toString();

        SpannableString title = new SpannableString(intro);

        int count = 0;
        //根据id从资源文件中获取图片对象
        Drawable d = ContextCompat.getDrawable(context, resId);
        if (d != null) {
            int width = d.getIntrinsicWidth();
            int height = d.getIntrinsicHeight();
            if (height > maxHeight) {
                width = width * maxHeight / height;
                height = maxHeight;
            }
            d.setBounds(0, 0, width, height);
            if (filterColor != 1) {
                d.mutate().setColorFilter(filterColor, PorterDuff.Mode.SRC_IN);
            }
            CenterAlignImageSpanNew imageSpan = new CenterAlignImageSpanNew(d);
            title.setSpan(imageSpan, count * 2, count * 2 + 1, ImageSpan.ALIGN_BASELINE);
        }
        return title;
    }

    public static SpannableString getTitleWithPicAheadCenterAlign(Context context, String intro
            , @DrawableRes int id, int maxCount, int maxHeight, int filterColor) {
        if (TextUtils.isEmpty(intro) || context == null) return null;
        List<Integer> resIds = new ArrayList<>();
        resIds.add(id);

        SpannableStringBuilder ssb = new SpannableStringBuilder();
        if (ToolUtil.isEmptyCollects(resIds)) {
            return new SpannableString(intro);
        }
        for (int i = 0; i < Math.min(maxCount, resIds.size()); i++) {
            int resId = resIds.get(i) == null ? 0 : resIds.get(i);
            Drawable d = ContextCompat.getDrawable(context, resId);
            if (d != null) {
                int width = d.getIntrinsicWidth();
                int height = d.getIntrinsicHeight();
                if (height > maxHeight) {
                    width = width * maxHeight / height;
                    height = maxHeight;
                }
                d.setBounds(0, 0, width, height);
                if (filterColor != 1) {
                    d.mutate().setColorFilter(filterColor, PorterDuff.Mode.SRC_IN);
                }
                CenterAlignImageSpanNew imageSpan = new CenterAlignImageSpanNew(d);
                ssb.append(" ");
                ssb.setSpan(imageSpan, ssb.length() - 1, ssb.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }

        ssb.append(intro);
        return new SpannableString(ssb);
    }

    public static SpannableString getTitleWithPicAheadCenterAlignAndFitHeight(Context context, CharSequence intro
            , @DrawableRes int resIds, int maxHeight) {
        List<Integer> resList = new ArrayList<>();
        resList.add(resIds);
        return getTitleWithPicAheadCenterAlignAndFitHeight(context, intro, resList, 1, maxHeight, 1);
    }

    public static SpannableString getTitleWithPicAheadCenterAlignAndFitHeight(Context context, CharSequence intro
            , List<Integer> resIds, int maxCount, int maxHeight, int filterColor) {
        if (TextUtils.isEmpty(intro) || context == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        if (ToolUtil.isEmptyCollects(resIds)) {
            return new SpannableString(intro);
        }
        int num = Math.min(maxCount, resIds.size());
        for (int i = 0; i < num; i++) {
            if (i == 0) {
                sb.append(" ");
            } else {
                sb.append("  ");
            }
        }
        sb.append("  ").append(intro);
        num = 2 * num + 1;

        SpannableString title = new SpannableString(sb.toString());

        int count = 0;

        for (int i = 0; i < resIds.size() && count < maxCount; i++) {
            int resId = resIds.get(i) == null ? 0 : resIds.get(i);

            //根据id从资源文件中获取图片对象
            Drawable d = ContextCompat.getDrawable(context, resId);
            if (d != null) {
                int width = d.getIntrinsicWidth();
                int height = d.getIntrinsicHeight();
                if (height > maxHeight) {
                    width = width * maxHeight / height;
                    height = maxHeight;
                }
                d.setBounds(0, 0, width, height);
                if (filterColor != 1) {
                    d.mutate().setColorFilter(filterColor, PorterDuff.Mode.SRC_IN);
                }
                CenterAlignImageSpan imageSpan = new CenterAlignImageSpan(d);
                title.setSpan(imageSpan, count * 2, count * 2 + 1, ImageSpan.ALIGN_BASELINE);
                count++;
            }
        }
        if (intro instanceof Spanned) {
            Spanned text = (Spanned) intro;
            Object[] spans = text.getSpans(0, text.length(), Object.class);
            for (Object span : spans) {
                title.setSpan(span, num + text.getSpanStart(span), num + text.getSpanEnd(span), text.getSpanFlags(span));
            }
        }

        return title;
    }

    public static SpannableString getTitleWithPicTailCenterAlignAndFitHeight(Context context, String intro,
                                                                             List<Integer> resIds, int maxCount, int maxHeight, int filterColor) {
        if (TextUtils.isEmpty(intro) || context == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        if (ToolUtil.isEmptyCollects(resIds)) {
            return new SpannableString(intro);
        }

        int titleLen = intro.length();
        sb.append(intro);
        for (int i = 0; i < Math.min(maxCount, resIds.size()); i++) {
            if (i == 0) {
                sb.append(" ");
            } else {
                sb.append("  ");
            }
        }
        intro = sb.toString();

        SpannableString title = new SpannableString(intro);

        int count = 0;
        for (int i = 0; i < resIds.size() && count < maxCount; i++) {
            int resId = resIds.get(i) == null ? 0 : resIds.get(i);

            //根据id从资源文件中获取图片对象
            Drawable d = ContextCompat.getDrawable(context, resId);
            if (d != null) {
                int width = d.getIntrinsicWidth();
                int height = d.getIntrinsicHeight();
                if (height > maxHeight) {
                    width = width * maxHeight / height;
                    height = maxHeight;
                }
                d.setBounds(0, 0, width, height);
                if (filterColor != 1) {
                    d.mutate().setColorFilter(filterColor, PorterDuff.Mode.SRC_IN);
                }
                CenterAlignImageSpan imageSpan = new CenterAlignImageSpan(d);
                title.setSpan(imageSpan, titleLen + count * 2, titleLen + count * 2 + 1, ImageSpan.ALIGN_BASELINE);
                count++;
            }
        }

        return title;
    }

    public static void removeGlobalOnLayoutListener(ViewTreeObserver view, ViewTreeObserver.OnGlobalLayoutListener victim) {
        if (view != null) {
            view.removeOnGlobalLayoutListener(victim);
        }
    }

    public static LoginInfoModel parseLoginfo(String resultJson) {
        try {
            JSONObject json = new JSONObject(resultJson);
            String resultCode = json.get("ret").toString();
            if ("0".equals(resultCode)) {
                return new Gson().fromJson(resultJson, LoginInfoModel.class);
            }
        } catch (Exception e) {
            Logger.log("解析json异常：" + Logger.getLineInfo());
            CrashReport.postCatchedException(e);
            return null;
        }
        return null;
    }

    public static BindLoginInfoModel getLastBindPhoneInfo() {
        if (MainApplication.getMyApplicationContext() == null) return null;
        String result = SharedPreferencesUtil.getInstance(MainApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.TINGMAIN_KEY_BINDPHONE_JSON_NEW);
        if (TextUtils.isEmpty(result)) return null;
        BindLoginInfoModel loginInfoModel = new Gson().fromJson(result, BindLoginInfoModel.class);
        if (loginInfoModel != null && (System.currentTimeMillis() - loginInfoModel.getCurTimeStamp() > 1000 * 60 * 3)) {
            //登录时间超过3分钟
            return null;
        }
        return loginInfoModel;

    }

    public static void getLastBindPhoneInfo(IDataCallBack<BindLoginInfoModel> callBack) {
        String result = SharedPreferencesUtil.getInstance(MainApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.TINGMAIN_KEY_BINDPHONE_JSON_NEW);
        if (TextUtils.isEmpty(result)) {
            callBack.onSuccess(null);
            return;
        }

        new AsyncGson<BindLoginInfoModel>().fromJson(result, BindLoginInfoModel.class, new AsyncGson.IResult<BindLoginInfoModel>() {
            @Override
            public void postResult(BindLoginInfoModel loginInfoModel) {
                if (loginInfoModel != null && (System.currentTimeMillis() - loginInfoModel.getCurTimeStamp() < 1000 * 60 * 3)) {
                    //登录时间超过3分钟
                    callBack.onSuccess(loginInfoModel);
                    return;
                }
                callBack.onSuccess(null);
            }

            @Override
            public void postException(Exception e) {
                callBack.onError(-1, "" + e);
            }
        });
    }

    public static void removeLastBindPhoneInfo() {
        SharedPreferencesUtil.getInstance(MainApplication.getMyApplicationContext()).removeByKey(PreferenceConstantsInHost.TINGMAIN_KEY_BINDPHONE_JSON_NEW);
    }


    /**
     * 用做缓存页面数据,在请求数据调用
     *
     * @param content
     * @param address
     */
    public static void saveDataToCacheDir(String content, String address) {
        if (TextUtils.isEmpty(content) || TextUtils.isEmpty(address) || MainApplication.getTopActivity() == null || !(MainApplication.getTopActivity() instanceof MainActivity))
            return;
        saveDataToCacheDir(MainApplication.getTopActivity(), content, address);
    }

    /**
     * 用做缓存页面数据,在请求数据调用
     *
     * @param context
     * @param content
     * @param address
     */
    public static void saveDataToCacheDir(Context context, String content, String address) {
        if (TextUtils.isEmpty(content) || TextUtils.isEmpty(address) || context == null || !(context instanceof MainActivity))
            return;
        FileUtil.writeStr2File(content, new File(context.getCacheDir(),
                MD5.md5(address))
                .getAbsolutePath());
    }

    public static String getRandomUUID() {
        return UUID.randomUUID().toString();
    }

    public static void clickUrlAction(@NonNull BaseFragment2 baseFragment2, @NonNull String url, @Nullable View fromView) {
        clickUrlAction(baseFragment2, url, null, fromView);
    }

    public static void clickUrlAction(@NonNull BaseFragment2 baseFragment2, @NonNull String url, Bundle hybridParams, @Nullable View fromView) {
        if (baseFragment2 == null || TextUtils.isEmpty(url)) {
            return;
        }

        // 处理拦截判断是小雅设备的时候，下载smartdevicebundle
        if (url.contains(AppConstants.SMART_DEVISES_LIST_MANAGE_URL)) {
//            Log.e("xxxv","开始smart预加载--------------");
            Router.getActionByCallback(Configure.BUNDLE_SMARTDEVICE, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {

                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            }, true, BundleModel.DOWNLOAD_IN_BACKGROUND);
        }

        if (url.startsWith("iting://")) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(baseFragment2.getActivity(), Uri.parse
                        (url));
            } catch (Exception e) {
                e.printStackTrace();
                if (ConstantsOpenSdk.isDebug) {
                    throw new RuntimeException(e);
                }
            }
        } else if (url.startsWith("http") || url.startsWith("https")) {
            Bundle bundle = new Bundle();
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
            if (hybridParams != null) {
                bundle.putAll(hybridParams);
            }
            baseFragment2.startFragment(NativeHybridFragment.newInstance(bundle), fromView, 0, 0);
        } else if (url.startsWith("xmly://")) {
            if (baseFragment2.getActivity() != null) {
                XmUriRouter.route(baseFragment2.getActivity(), url);
            }
        } else {
            try {
                Intent intent = new Intent(Intent.ACTION_VIEW);
                intent.setData(Uri.parse(url));
                baseFragment2.startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void clickUrlAction(MainActivity activity, String url, View fromView) {
        if (activity == null || TextUtils.isEmpty(url)) {
            return;
        }

        if (url.startsWith("iting://")) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(activity, Uri.parse(url));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }

        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, url);
        activity.startFragment(NativeHybridFragment.newInstance(bundle), fromView);
    }

    public static Uri getImageUri(String imageName) {
        String filePath = null;
        if (android.os.Environment.getExternalStorageState().equals(
                android.os.Environment.MEDIA_MOUNTED)) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                filePath = MainApplication.getMyApplicationContext().getExternalFilesDir("") + "/ting/images";
            } else {
                filePath = Environment.getExternalStorageDirectory() + "/ting/images";
            }
            try {
                //生成uri的时候，判断当前路径是否存在，不存在创建
                //sd卡不存在文件路径，导致上传图片裁剪完获取到的图片为空，造成上传失败
                File file = new File(filePath);
                if (!file.exists()) {
                    file.mkdirs();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            if (MainApplication.getTopActivity() != null && MainApplication.getTopActivity().getCacheDir() != null) {
                filePath = MainApplication.getTopActivity().getCacheDir().getAbsolutePath();
            }
        }
        if (!TextUtils.isEmpty(filePath)) {

            return Uri.parse("file://" + filePath + "/" + imageName);
        } else {
            return null;
        }
    }

    public static File getTempImageFile(String fileName) {
        File f = null;
        File head = null;
        if (TextUtils.isEmpty(fileName)) return null;
        String fileDir = MainApplication.getMyApplicationContext().getExternalFilesDir("") + "/ting/images";
        f = new File(fileDir);
        if (f == null) {
            return null;
        }
        if (!f.exists())
            f.mkdirs();
        else {
            if (f.isFile()) {
                f.deleteOnExit();
                f.mkdirs();
            }
        }
        head = new File(f, fileName);
        if (!head.exists()) {
            try {
                head.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        f = null;
        return head;
    }

    public static void deleteTempImageFile(String imageName) {
        if (getTempImageFile(imageName) != null && getTempImageFile(imageName).isFile()) {
            getTempImageFile(imageName).delete();
        }
    }

    /**
     * 是否是第一次安装软件
     *
     * @param context
     * @return
     */
    public static boolean isFirstInstallApp(Context context) {
        return BaseUtil.isFirstInstallApp(context);
    }

    /**
     * 是否是当天新安装的用户
     *
     * @param context
     * @return
     */
    public static boolean isTodayFirstInstallApp(Context context) {
        if (ConstantsOpenSdk.isDebug) {
            return false;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy:MM:dd", Locale.getDefault());
        String curTime = sdf.format(System.currentTimeMillis());
        SharedPreferencesUtil sharedPreferencesUtil = SharedPreferencesUtil.getInstance(context);
        if (isFirstInstallApp(context)) {
            sharedPreferencesUtil.saveString(PreferenceConstantsInHost.TINGMAIN_KEY_NEW_APP_INSTALL_DATE, curTime);
            return true;
        }
        return curTime.equals(sharedPreferencesUtil.getString(PreferenceConstantsInHost.TINGMAIN_KEY_NEW_APP_INSTALL_DATE));
    }

    /**
     * 是否是当天第一次启动App
     */
    public static boolean isTodayFirstOpenApp() {
//        if (ConstantsOpenSdk.isDebug) {
//            return true;
//        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy:MM:dd", Locale.getDefault());
        String curTime = sdf.format(System.currentTimeMillis());
        String lastTime = MMKVUtil.getInstance().getString(TINGMAIN_KEY_TODAY_FIRST_OPEN_APP_DATE);
        if (!curTime.equals(lastTime)) {
            MMKVUtil.getInstance().saveString(TINGMAIN_KEY_TODAY_FIRST_OPEN_APP_DATE, curTime);
            return true;
        }
        return false;
    }

    public static <T> boolean isEmptyCollects(Collection<T> collection) {
        return collection == null || collection.isEmpty();
    }

    public static <T> boolean isEmptyMap(Map map) {
        return map == null || map.isEmpty();
    }


    public static boolean checkIntentAndStartActivity(Context context, Intent intent, boolean anim) {
        if (context == null || intent == null) {
            Logger.e("Warning", "Class: TooUtil.checkIntentAndStartActivity() context not null and intent not null");
            return false;
        }

        if (!(context instanceof Activity)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        } else if (((Activity) context).isFinishing()) {
            return false;
        }

        try {
            if (intent.resolveActivity(context.getPackageManager()) != null) {
                context.startActivity(intent);
                if (context instanceof Activity && anim) {
                    ((Activity) context).overridePendingTransition(R.anim.host_slide_in_bottom, R.anim.host_slide_out_bottom);
                }
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean checkIntentAndStartActivity(Context context, Intent intent) {
        return checkIntentAndStartActivity(context, intent, false);
    }

    /**
     * 需要上报dp 调起失败信息，所以单独写个throws Exception的方法
     */
    public static boolean checkAdDpIntentAndStartActivity(Context context, Intent intent) throws Exception {
        return checkAdApIntentAndStartActivity(context, intent, false);
    }

    /**
     * 需要上报dp 调起失败信息，所以单独写个throws Exception的方法
     */
    public static boolean checkAdApIntentAndStartActivity(Context context, Intent intent, boolean anim) throws Exception{
        if (context == null || intent == null) {
            Logger.e("Warning", "Class: TooUtil.checkIntentAndStartActivity() context not null and intent not null");
            // 手动抛异常
            throw new Exception("ERROR: TooUtil.checkIntentAndStartActivity() context not null and intent not null");
        }

        if (!(context instanceof Activity)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        } else if (((Activity) context).isFinishing()) {
            // 手动抛异常
            throw new Exception("ERROR: Activity isFinishing");
        }

        if (intent.resolveActivity(context.getPackageManager()) != null) {
            context.startActivity(intent);
            if (context instanceof Activity && anim) {
                ((Activity) context).overridePendingTransition(R.anim.host_slide_in_bottom, R.anim.host_slide_out_bottom);
            }
            return true;
        } else {
            // 手动抛异常
            throw new Exception("ERROR: intent.resolveActivity(context.getPackageManager()) == null");
        }
    }

    /**
     * 设置加载图片的textView,能够重复加载多张图片
     *
     * @param tv
     * @param context
     * @param richContent
     */
    public static void setImageWithHtmlTextView(final TextView tv, final Context context, String richContent) {
        if (tv == null) return;
        if (context == null || richContent == null) {
            tv.setText("暂无内容");
            return;
        }

        RichText.from(context, richContent)
                .async(true)
                .urlClick(new OnURLClickListener() {
                    @Override
                    public boolean urlClicked(String url) {
                        return true;
                    }
                })
                .into(tv);
    }

    /**
     * 设置富文本给RichWebView
     *
     * @param tv
     * @param context
     * @param richContent
     */
    public static void setRichContentToWebView(final RichWebView tv, final Context context, String richContent) {
        RichWebView.RichWebViewAttr attr = new RichWebView.RichWebViewAttr();
        attr.marginLeft = 0;
        attr.marginRight = 0;
        attr.color = "#666666";
        setRichContentToWebView(tv, context, richContent, attr);
    }

    public static void setRichContentToWebView(final RichWebView tv, final Context context, String richContent, RichWebView.RichWebViewAttr attr) {
        if (tv == null) return;
        if (context == null || richContent == null) {
            tv.setData("暂无内容", attr);
            return;
        }
        tv.setHorizontalScrollBarEnabled(false);
        tv.setVerticalScrollBarEnabled(false);

        tv.setData(richContent, attr);
    }

    public static <T> boolean isEqualList(List<T> list1, List<T> list2) {
        if (list1 == list2) {
            return true;
        }
        if (list1 == null || list2 == null) {
            return false;
        }
        if (list1.size() != list2.size()) {
            return false;
        }
        return list1.containsAll(list2);
    }

    public static void showPopWindow(PopupWindow popupWindow, View parent, int gravity, int x, int y) {
        try {
            popupWindow.showAtLocation(parent, gravity, x, y);
        } catch (WindowManager.BadTokenException exception) {
        } catch (NullPointerException e) {
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * 懒人必备之自动化生成测试数据
     *
     * @param cls
     * @return
     */
    public static Object generateFakeData(Class cls) {
        Object object = null;
        try {
            object = cls.newInstance();
        } catch (InstantiationException e) {
            e.printStackTrace();
            return null;
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            return null;
        }
        Field[] fields = cls.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            String fieldName = field.getName();
            boolean isPublic = field.getModifiers() == Modifier.PUBLIC;
            boolean isPrivate = field.getModifiers() == Modifier.PRIVATE;
            if (!isPrivate && !isPublic)
                continue;

            String type = field.getGenericType().toString();    //获取属性的类型
            Object parameter = null;
            Random random = new Random();
            if (type.equals("class java.lang.String")) {   //如果type是类类型，则前面包含"class "，后面跟类名
                parameter = "测试数据" + fieldName + random.nextInt();
            } else if (type.equals("class java.lang.Integer") || type.equals("int")) {
                parameter = random.nextInt();
            } else if (type.equals("class java.lang.Double") || type.equals("double")) {
                parameter = random.nextDouble();
            } else if (type.equals("class java.lang.Boolean") || type.equals("boolean")) {
                parameter = random.nextBoolean();
            } else if (type.equals("class java.lang.Long") || type.equals("long")) {
                parameter = random.nextLong();
            } else if (type.contains("[L")) {//表示是数组
                parameter = Array.newInstance(field.getType().getComponentType(), 1);
                Array.set(parameter, 0, generateFakeData(field.getType().getComponentType()));
            } else if (type.contains("java.util.List<")) {//表示是List
                if (field.getGenericType() instanceof ParameterizedType) {
                    List<Object> list = new ArrayList<>();
                    Type parameterizedType = ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0];
                    list.add(generateFakeData((Class) parameterizedType));
                    parameter = list;
                }
            } else if (field.getType().isAssignableFrom(Map.class)) {
                //暂不处理
            } else {
                parameter = generateFakeData(field.getType());
            }

            //如果是公共变量，直接赋值，否则调用set方法。
            if (isPrivate) {
                fieldName = fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1); //将属性的首字符大写，方便构造get，set方法
                try {
                    Method m = cls.getMethod("set" + fieldName, field.getType());
                    m.invoke(object, parameter);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            } else if (isPublic) {
                try {
                    field.set(object, parameter);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        return object;
    }

    // 添加时间戳到Url
    public static String addTsToUrl(String url) {
        if (!TextUtils.isEmpty(url)) {
            if (url.endsWith("/")) {
                url = url + "ts-" + System.currentTimeMillis();
            } else {
                url = url + "/ts-" + System.currentTimeMillis();
            }
        }
        return url;
    }

    public static final String AD_XM_TIMELINE = "xt";

    public static Map<String, String> setAdXmTimeline(Map<String, String> param) {
        if (param == null) {
            param = new HashMap<>();
        }
        param.put(AD_XM_TIMELINE, System.currentTimeMillis() + "");
        return param;
    }

    /**
     * 重新识别和映射某些http为itingurl
     *
     * @param baseFragment2
     * @param url
     */
    public static void recognizeItingUrl(@NonNull final BaseFragment2 baseFragment2, @NonNull final String url) {
        if (TextUtils.isEmpty(url) || baseFragment2 == null) {
            return;
        }

        if (url.startsWith("iting://")) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(baseFragment2.getActivity(), Uri.parse(url));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }

        Uri uri = Uri.parse(url);
        String host = uri.getHost() == null ? null : uri.getHost().trim();
        String path = uri.getPath() == null ? null : uri.getPath().trim();

        List<String> segments = uri.getPathSegments();
        String protocal = uri.getScheme() == null ? null : uri.getScheme().trim();
        if (!TextUtils.isEmpty(host) && ("http".equals(protocal) || "https".equals(protocal))) {
            String iting = null;
            if (host.equals("m.ximalaya.com")) {
                if (!TextUtils.isEmpty(path)) {
                    if (path.startsWith("/zhubo")) {
                        // http://m.ximalaya.com/zhubo/71241360/  ->  iting://open?msg_type=12&uid=xx
                        if (segments != null && segments.size() == 2) {
                            iting = "iting://open?msg_type=12&uid=" + segments.get(1);
                        }
                    } else if (path.equals("/explore/subject_detail")) {
                        // http://m.ximalaya.com/explore/subject_detail?id=760 --> iting://open?msg_type=36
                        String id = uri.getQueryParameter("id");
                        String content_type = uri.getQueryParameter("content_type");
                        if (!TextUtils.isEmpty(id) && !TextUtils.isEmpty(content_type)) {
                            iting = "iting://open?msg_type=36&id=" + id + "&content_type=" + content_type;
                        }
                    } else if (path.equals("/sharechart_v2")) {
                        String args = uri.getQueryParameter("subCategoryId");
                        if (TextUtils.isEmpty(args)) {
                            // http://m.ximalaya.com/sharechart_v2?contentType=album&rankingListId=21&target=main&from=singlemessage&isappinstalled=1
                            // -->
                            // iting://open?msg_type=38&type=track&key=rediskey&rankingListId=xx&title="排行title"    (type [track,album,anchor]),旧版客户端用key，新版客户端用ranklistid，兼容
                            String id = uri.getQueryParameter("rankingListId");
                            String type = uri.getQueryParameter("contentType");
                            iting = "iting://open?msg_type=38&type=" + type + "&rankingListId=" + id;
                        }
                    } else if (segments != null && segments.size() == 3) {
                        String type = segments.get(1);
                        if ("album".equals(type)) {
                            // http://m.ximalaya.com/24704450/album/3903830 --> iting://open?msg_type=13&album_id=5213
                            iting = "iting://open?msg_type=13&album_id=" + segments.get(2);
                        } else if ("sound".equals(type)) {
                            // http://m.ximalaya.com/12495477/sound/30106651 --> iting://open?msg_type=11&track_id=xx
                            iting = "iting://open?msg_type=11&track_id=" + segments.get(2);
                        }
                    }
                }
            } else if (host.equals("liveroom.ximalaya.com")) {
                if (!TextUtils.isEmpty(path)) {
                    if (path.startsWith("/xlive")) {
//                                http://liveroom.ximalaya.com/xlive/272 --> iting://open?msg_type=40&live_type=1&live_id=xx
                        if (segments != null && segments.size() == 2) {
                            String id = segments.get(1);
                            iting = "iting://open?msg_type=40&live_type=1&live_id=" + id;
                        }
                    } else if (path.startsWith("/live")) {
//                                http://liveroom.ximalaya.com/live/4419?from=singlemessage&isappinstalled=1 --> iting://open?msg_type=40&live_type=0&live_id=xx
                        if (segments != null && segments.size() == 2) {
                            String id = segments.get(1);
                            iting = "iting://open?msg_type=40&live_type=0&live_id=" + id;
                        }
                    }
                }
            }

            if (TextUtils.isEmpty(iting)) {
                HashMap<String, String> hashMap = new HashMap();
                hashMap.put("tinyUrl", url);
                CommonRequestM.getTransferQRCode(hashMap, new IDataCallBack<String>() {
                    @Override
                    public void onSuccess(String object) {
                        clickUrlAction(baseFragment2, object, null);
                    }

                    @Override
                    public void onError(int code, String message) {
                        clickUrlAction(baseFragment2, url, null);
                    }
                });
            } else {
                clickUrlAction(baseFragment2, iting, null);
            }
        }
    }

    public static Object map2Bean(Map<String, String> map, Class<?> beanClass) throws Exception {
        if (map == null) {
            return null;
        }
        Object obj = beanClass.newInstance();//新实例
        Field[] fields = obj.getClass().getDeclaredFields(); //获取所有的属性
        for (Field field : fields) {
            int mod = field.getModifiers();//返回此类或接口以整数编码的 Java 语言修饰符
            if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
                continue;
            }
            field.setAccessible(true);//打破封装
            field.set(obj, map.get(field.getName()));//给obj对象的id属性赋值
        }

        return obj;
    }

    public static String collectionToString(String splite, Collection collection) {
        if (collection == null) {
            return null;
        }
        Iterator it = collection.iterator();
        if (!it.hasNext())
            return "";

        StringBuilder sb = new StringBuilder();
        while (true) {
            sb.append(it.next());
            if (!it.hasNext())
                return sb.toString();
            sb.append(splite);
        }
    }

    public static boolean isLandscape(Activity activity) {
        int orientation = getScreenOrientation(activity);

        return orientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE ||
                orientation == ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
    }

    public static int getScreenOrientation(Activity activity) {
        Display display = activity.getWindowManager().getDefaultDisplay();
        int rotation = display.getRotation();
        DisplayMetrics dm = new DisplayMetrics();
        display.getMetrics(dm);
        int width = dm.widthPixels;
        int height = dm.heightPixels;
        int orientation;
        if ((rotation == Surface.ROTATION_0
                || rotation == Surface.ROTATION_180) && height > width || (rotation == Surface.ROTATION_90
                || rotation == Surface.ROTATION_270) && width > height) {
            switch (rotation) {
                case Surface.ROTATION_0:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
                    break;
                case Surface.ROTATION_90:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
                    break;
                case Surface.ROTATION_180:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT;
                    break;
                case Surface.ROTATION_270:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
                    break;
                default:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
                    break;
            }
        } else {
            switch (rotation) {
                case Surface.ROTATION_0:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
                    break;
                case Surface.ROTATION_90:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
                    break;
                case Surface.ROTATION_180:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
                    break;
                case Surface.ROTATION_270:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT;
                    break;
                default:
                    orientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
                    break;
            }
        }

        return orientation;
    }

    public static String getVideoRealUrl(VideoInfoBean videoInfoBean) {
        Map<String, String> map = new HashMap<>();
        map.put(DTransferConstants.FILE_ID, videoInfoBean.getFileId());
        map.put(DTransferConstants.EP, videoInfoBean.getEp());
        map.put(DTransferConstants.DURATION, videoInfoBean.getDuration() + "");
        map.put(DTransferConstants.API_VERSION, videoInfoBean.getApiVersion());
        map.put(DTransferConstants.PAY_DOMAIN, videoInfoBean.getDomain());

        return CommonRequestM.getAntiLeechUrl(map);
    }

    public static boolean activityIsValid(WeakReference<Activity> weakReference) {
        if (weakReference == null || weakReference.get() == null) {
            return false;
        }

        Activity activity = weakReference.get();

        if (activity == null || activity.isFinishing()) {
            return false;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            if (activity.isDestroyed()) {
                return false;
            }
        }
        return true;
    }

    public static boolean activityIsValid(Activity activity) {
        if (activity == null || activity.isFinishing()) {
            return false;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            if (activity.isDestroyed()) {
                return false;
            }
        }
        return true;
    }

    // 判断是否包含SIM卡
    public static boolean hasSimCard(Context context) {
        if (context == null) {
            return false;
        }
        TelephonyManager telMgr = SystemServiceManager.getTelephonyManager(context);
        if (telMgr == null) {
            return false;
        }

        int simState = telMgr.getSimState();
        boolean result = true;
        switch (simState) {
            case TelephonyManager.SIM_STATE_ABSENT:
                result = false; // 没有SIM卡
                break;
            case TelephonyManager.SIM_STATE_UNKNOWN:
                result = false;
                break;
        }
        return result;
    }

    public static boolean isInstalledByPackageName(Context context, String packageName) {
        if (context == null || TextUtils.isEmpty(packageName)) {
            return false;
        }

        return XmSystemInvokeManager.isAppInstalled(context, packageName);
    }

    public static void throwIllegalNoLogicException() {
        if (ConstantsOpenSdk.isDebug) {
//            throw new RuntimeException("throwIllegalNoLogicException");
            Logger.e(new RuntimeException("throwIllegalNoLogicException"));
        }
    }

    public static void configCenterToggleCheck(String groupName, String itemName, boolean defaultValue, boolean keepListen, IConfigureCenterResult<Boolean> result) {
        ConfigureCenter.getInstance().registerConfigFetchCallback(new IConfigureCenter.ConfigFetchCallback() {
            @Override
            public void onUpdateSuccess() {
                try {
                    if (!keepListen) {
                        ConfigureCenter.getInstance().unRegisterConfigFetchCallback(this);
                    }
                    boolean configureEnable = ConfigureCenter.getInstance().getBool(groupName, itemName, defaultValue);
                    if (result != null && defaultValue != configureEnable) {
                        result.onChange(configureEnable);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onRequestError() {
                if (!keepListen) {
                    ConfigureCenter.getInstance().unRegisterConfigFetchCallback(this);
                }
            }
        });
    }


    public static void configCenterIntCheck(String groupName, String itemName, int defaultValue, boolean keepListen, IConfigureCenterResult<Integer> result) {
        ConfigureCenter.getInstance().registerConfigFetchCallback(new IConfigureCenter.ConfigFetchCallback() {
            @Override
            public void onUpdateSuccess() {
                try {
                    if (!keepListen) {
                        ConfigureCenter.getInstance().unRegisterConfigFetchCallback(this);
                    }
                    int configureEnable = ConfigureCenter.getInstance().getInt(groupName, itemName, defaultValue);
                    if (result != null && defaultValue != configureEnable) {
                        result.onChange(configureEnable);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onRequestError() {
                if (!keepListen) {
                    ConfigureCenter.getInstance().unRegisterConfigFetchCallback(this);
                }
            }
        });
    }

    public interface IConfigureCenterResult<T> {
        void onChange(T t);
    }

    public static Context getCtx() {
        return MainApplication.getMyApplicationContext();
    }

    @Nullable
    public static MainActivity getMainActivity() {
        if (MainApplication.getMainActivity() instanceof MainActivity) {
            return (MainActivity) MainApplication.getMainActivity();
        }
        return null;
    }
}
