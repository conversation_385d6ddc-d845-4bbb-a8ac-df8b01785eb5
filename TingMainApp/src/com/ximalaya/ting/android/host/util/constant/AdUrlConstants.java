package com.ximalaya.ting.android.host.util.constant;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;

/**
 * Created by xmly on 2019-08-24.
 *
 * 广告的请求连接
 *
 * <AUTHOR>
 */
public class AdUrlConstants extends UrlConstants {

    private AdUrlConstants() {}
    private static final class Holder {
        static final AdUrlConstants instance = new AdUrlConstants();
    }

    public static AdUrlConstants getInstanse() {
        return Holder.instance;
    }

    public String getShareAd() {
        return getSERVER_XIMALAYA_AD() + "ting/share";
    }

    public String getCategoryCardAd() {
        return getSERVER_XIMALAYA_AD() + "ting/feature";
    }

    // 主播页面增加投放广告
    public String anchorCooperation() {
        return getSERVER_XIMALAYA_AD() + "ting/broadcaster_cooperation";
    }

    /**
     * 会员腰封广告
     *
     * @return
     */
    public String getWaistbandAd() {
        return getSERVER_XIMALAYA_AD() + "ting/waistband";
    }

    public String getBigScreenAd() {
        return getSERVER_XIMALAYA_AD() + "ting/screen";
    }

    public String getXimalayaADs() {
        return getSERVER_XIMALAYA_AD() + "ting";
    }

    public String getXimalayaCateADs() {
        return getSERVER_XIMALAYA_AD() + "ting/category";
    }


    /**
     * 获得广告的状态
     */
    public String getAdStat() {
        return getSERVER_XIMALAYA_AD() + "ting/implant";
    }

    /**
     * 收集离线的广告
     */
    public String getRecordOffAd() {
        return getSERVER_XIMALAYA_AD() + "adrecord/offlineAdRecord";
    }

    /**
     * 播放页的广告
     */
    public String getAdUrlPlayView() {
        return getSERVER_XIMALAYA_AD() + "ting/direct";
    }

    /**
     * 欢迎页广告
     */
    public String getWelcomeAd() {
        return getSERVER_XIMALAYA_AD() + "ting/loading";
    }

    /**
     * mad欢迎页广告
     */
    public String getWelcomeMadAd() {
        return "http://ad.madserving.com/adcall/bidrequest";
    }

    /**
     * 定制听广告
     */
    public String getFeedAd() {
        return getSERVER_XIMALAYA_AD() + "ting/feed";
    }

    /**
     * 获得声音贴片广告
     */
    public String getAdsData() {
        return getSERVER_XIMALAYA_AD() + "soundPatch";
    }

    /**
     * 获得预请求dsp 贴片广告
     */
    public String getPreSoundPatch() {
        return getSERVER_XIMALAYA_AD() + "ting/preSoundPatch";
    }

    /**
     * 获得声音贴片广告
     */
    public String getIconsAdsData() {
        return getSERVER_XIMALAYA_AD() + "ting/icon";
    }

    /**
     * 获得专辑页通知
     */
    public String getAlbumNotice() {
        return getSERVER_XIMALAYA_AD() + "ting/album";
    }

    /**
     * 首页广告
     */
    public String getHomeAd() {
        return getSERVER_XIMALAYA_AD() + "ting/home";
    }

    /**
     * 播放页皮肤广告
     *
     * @return
     */
    public String getPlayFragmentDirectAd() {
        return getSERVER_XIMALAYA_AD() + "ting/direct";
    }

    // 播放页中部广告
    public String getPlayCenterAd() {
        return getSERVER_XIMALAYA_AD() + "ting/direct_v2";
    }

    /**
     * 获得主播页广告
     *
     * @return
     */
    public String getAnchorBannerAd() {
        return getSERVER_XIMALAYA_AD() + "ting/broadcaster";
    }

    // 焦点图广告
    public String getFocusAd() {
        return getSERVER_XIMALAYA_AD() + "focusPicture";
    }

    /**
     * 付费专辑页广告地址
     *
     * @return
     */
    public String getPurchaseAdUrl() {
        return getSERVER_XIMALAYA_AD() + "ting/purchase";
    }

    /**
     * 预加载广告
     * @return
     */
    public String preloadMaterial() {
        return getSERVER_XIMALAYA_AD() + "ting/preload";
    }

    /**
     * 搜索彩蛋
     * @return
     */
    public String searchAdBonus() {
        return  getSearchHost() + "adBonus";
    }

    /**
     * 频道页添加冠名广告
     * @return
     */
    public String columnSponsorship() {
        return getSERVER_XIMALAYA_AD() + "ting/column_sponsorship";
    }

    /**
     * 播放页评论广告
     * @return
     */
    public String getPlayCommend() {
        return getSERVER_XIMALAYA_AD() + "ting/comment";
    }

    /**
     * 上报客户下载详情
     * @return
     */
    public String getAdDownloadRecord() {
        if (MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_AD_USE_HTTPS, false)) {
            return BaseUtil.chooseEnvironmentUrl(
                    "https://ad.ximalaya.com/", "https://ops.test.ximalaya.com/", "https://ops.test.ximalaya.com/") +
                    "ad-action/download";
        } else {
            return BaseUtil.chooseEnvironmentUrl(
                    "http://ad.ximalaya.com/", "http://ops.test.ximalaya.com/", "http://ops.test.ximalaya.com/") +
                    "ad-action/download";
        }
    }

    /**
     * 直播间广告
     */
    public String getChatRoomAd() {
        return getSERVER_XIMALAYA_AD() + "ting/broadcaster";
    }

    /**
     * 直播场次中间穿插的广告焦点图
     */
    public String getAdInLiveHomeRecordList() {
        return getSERVER_XIMALAYA_AD() + "ting";
    }

    /**
     * 获取广告nonce
     * @return
     */
    public String getAdNonce() {
        return getSERVER_XIMALAYA_AD() + "nonce";
    }

    /**
     * 前插视频完播回调接口
     * @return
     */
    public String hookForwardVideo() {
        return getSERVER_XIMALAYA_AD() + "hook/forwardVideo";
    }

    /**
     * 付费解锁小黄条提示接口
     * @return
     */
    public String unLockPaidYellowBar() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/preSale";
    }

    /**
     * 付费弹窗入口提示
     * @return
     */
    public String payDialogHintUnLock() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/buyPop";
    }

    /**
     * 解锁广告
     * @return
     */
    public String lookUnLockPaid() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/unLock";
    }

    /**x
     * 解锁广告 购买提示页面
     * @return
     */
    public String lookUnLockPlayPage() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/playPage";
    }

    /**
     * 播放页小黄条 提示解锁
     * @return
     */
    public String playBarUnLockHint() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/playBar";
    }

    /**
     * 具体的解锁信息的接口
     * @return
     */
    public String incentiveIncentive() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/incentive";
    }

    /**
     * 搜索页banner 广告
     * @return
     */
    public String searchPageAdBanner() {
        return getSERVER_XIMALAYA_AD() + "ting/common";
    }

    /**
     * 声音流 + 更多
     */
    public String getSoundPatchMore() {
        return getSERVER_XIMALAYA_AD() + "chase_recommend";
    }

    /**
     * 全站畅听，解锁vip专辑广告
     * @return
     */
    public String getVipTrackFree() {
        return getSERVER_XIMALAYA_AD() + "ting/incentive";
    }


    /**
     * 当有安装完成时，请求物料接口（接口-开机安装），获取物料信息。
     * @return
     */
    public String getAdRecordShadow() {
        return getSERVER_XIMALAYA_ADSE() + "ad-se-brand/ting/material/shadow";
    }


    /**
     * 上报广告关闭负反馈原因
     * @return
     */
    public String getDislikeReport() {
        if (MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_AD_USE_HTTPS, false)) {
            if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
                return "https://ad.ximalaya.com/ad-action/feedback";
            } else {
                return "https://ops.test.ximalaya.com/ad-action/feedback";
            }
        } else {
            if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
                return "http://ad.ximalaya.com/ad-action/feedback";
            } else {
                return "http://ops.test.ximalaya.com/ad-action/feedback";
            }
        }
    }

    /**
     * 听单负反馈补充单个内容
     * @return
     */
    public String getSingleSocialListenListItemUrl() {
        return getSERVER_XIMALAYA_AD() + "ting/feedback/recommend/card";
    }

    // 广告点赞
    public String getLikeReportUrl() {
        return getSERVER_XIMALAYA_AD() + "soundPatch/add/like";
    }

    /**
     * 原生落地页下载类型， app信息接口
     * @return
     */
    public String getNativeDownloadInfo() {
        return getSERVER_XIMALAYA_ADSE() + "ad-se-brand/ting/apk/info";
    }

    /**
     * 流量404接口
     * @return
     */
//    public String getIvRecordUrl() {
//        return BaseUtil.chooseEnvironmentUrl("https://crown.ximalaya.com/") + "visible/four04/ts-" + System.currentTimeMillis();
//    }

    public String getNativeContentAd() {
        return getSERVER_XIMALAYA_AD() + "xm/ad";
    }

    public String recordSoundAdShow() {
        return getSERVER_XIMALAYA_AD() + "reallog/chase";
    }

    public String getAdWelfare() {
        return getAdWelfAreHost() + "ad/api/xmg";
    }

    /**
     * 自动拉起激励视频上报回调
     * @return
     */
    public String hookReportPullForwardVideo() {
        return getSERVER_XIMALAYA_AD() + "soundPatch/report/pullForwardVideo";
    }

    public String rewardPointsCenter() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardPoints";
    }

    public String freeListenRewardPoints() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/taskRewardPoints";
    }

    public String freeListenRewardPointCash() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardTaskAward";
    }

    public String freeListenRewardPointDoubling() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/reDoubleTaskAward";
    }

    public String singleTrackUnlock() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardTrackDuration";
    }

    public String getAdUseInfoAuth() {
        if (BaseConstants.ENVIRONMENT_TEST == BaseConstants.environmentId){
            return "https://ops.test.ximalaya.com/ad-action/auth";
        }
        return getSERVER_XIMALAYA_ACT() + "ad-action/auth";
    }

    /**
     * uve 接口
     *
     * @return
     */
    public String getUveInfoRequestUrl() {
        return getSERVER_XIMALAYA_AD() + "adx/uve";
    }

    /**
     * 激励视频提现接口
     * @return
     */
    public String incentiveRewardCash() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardCash";
    }

    public String freeListenPointCash() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardPointsCash";
    }

    public String incentiveRewardWelfareAward() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardWelfareAward";
    }

    public String incentiveRewardClickAward() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardClickAward";
    }

    public String queryVipPopupForPlay() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/queryVipPopupForPlay";
    }

    /**
     * 福利页领金币接口
     * @return
     */
    public String incentiveRewardGoldCoin() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/welfare/rewardGoldCoin";
    }

    /**
     * 惊喜任务点击上报接口
     * @return
     */
    public String getHighPriceTaskClickReport() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/highPriceTaskClickReport/ts-" + System.currentTimeMillis();
    }
}
