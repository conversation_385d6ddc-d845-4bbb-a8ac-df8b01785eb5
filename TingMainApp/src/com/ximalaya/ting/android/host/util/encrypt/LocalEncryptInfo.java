package com.ximalaya.ting.android.host.util.encrypt;

import com.ximalaya.ting.android.opensdk.constants.BaseConstants;

import java.util.HashMap;
import java.util.Map;

public class LocalEncryptInfo {
    public static class Config {
        public String securityConfigId;
        public String salt;
        public String cert;

        public Config(String securityConfigId, String salt, String cert) {
            this.securityConfigId = securityConfigId;
            this.salt = salt;
            this.cert = cert;
        }
    }

    private final static Config CREDIT_CENTER_DEBUG = new Config("9888001", "y0hjQHsSvFKbzenM", "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4o412k6jTiIaivgPl5tztnk2ysUS+dGY40/oFMLWwqbYWvW3CUmF23FsgMGUHLrvf66eKBiIttalwVsf6y1Ul5od9mYKTRletDnBUpeH/lUK0iWs6pBXpKo+OPrfk0QrP2QIMMz7JhJTLTbgScgL8vdZw7e8GTI7vBbdgT/VHn5X3xj33Inqe4eWHIONC0moZ6wkH/TVBkqaLShHMjIx/6G7yVxN5HiF4KVAbf4y8WhGtsTbon6oLDTFNu9GXRAuwFEXzb5i2XqsJRb3si4LYCnnWcID5aGx7tKJHuj4lTiMF68vTmrz7E1S9jSN3VLKRJsDROUP0T2+vRdg8VFNowIDAQAB");
    private final static Config CREDIT_CENTER_RELEASE = new Config("2888001", "7UlJQvyrOitbIcD4", "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmrleE5oeaIJRs4GDRr/cAWPK/Vp3yV5XnDxJ+Jgb4YQUB5b/FvIFsP0137/bTSPdKLZXrZ238Lzd33/ymZN8yIUFTMpkgvu6aDPeRqW2Mhw8MYyuVN3K5wVP5cpuumxivEq6ogxe+9FHgmwPqqDUkgPw84gyhL4FBdaS1vxgyF9G5ShwfT4GC9pNvtUnS6aeaomnCGr6VlLZHBMr8KwPJVk2NtWXSn+tofw1Sq/itQs/WhIL/8w7mbiyG9j+uD4mLRUJocY/HJ0cR1ttBT3hBslKYlm/JNHcrHyseygZhzSUtQujmdNIqTZSXZ8Iq9FlrBAhpQa91kVBeKAJKu1ABQIDAQAB");

    private final static Map<String, Config> sDebugSceneEncryptConfigMap = new HashMap<>();

    private final static Map<String, Config> sReleaseSceneEncryptConfigMap = new HashMap<>();

    static {
        sDebugSceneEncryptConfigMap.put("credit_center", CREDIT_CENTER_DEBUG);

        sReleaseSceneEncryptConfigMap.put("credit_center", CREDIT_CENTER_RELEASE);
    }

    public static Config getEncryptInfo(String scene) {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return sReleaseSceneEncryptConfigMap.get(scene);
        }

        return sDebugSceneEncryptConfigMap.get(scene);
    }
}
