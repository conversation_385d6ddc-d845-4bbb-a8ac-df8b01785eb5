package com.ximalaya.ting.android.host.util.fixWebview;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;

import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import java.io.File;
import static android.os.Build.VERSION.SDK_INT;
import static android.os.Build.VERSION_CODES.O_MR1;

public class FixWebviewErrorFixer {
    //参见源码： com.android.webview.chromium.WebViewChromiumFactoryProvider
    private static final String CHROMIUM_PREFS_NAME = "WebViewChromiumPrefs";

    private static final String APP_WEB_VIEW_DIR_NAME = "app_webview";

    private static final String GPU_CACHE_DIR_NAME = "GPUCache";

    public static void fix64ByteWebviewError(Context context) {
        if (SDK_INT != O_MR1) {
            return;
        }
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.N) {
            return;
        }
        if (DeviceUtil.isHuaWei()){
            return;
        }
        try {
            final Context appContext = context;

            //移除：shared_prefs/WebViewChromiumPrefs.xml
            final SharedPreferences chromiumPrefs = appContext.getSharedPreferences(
                    CHROMIUM_PREFS_NAME,
                    Context.MODE_PRIVATE
            );
            chromiumPrefs.edit().clear().apply();

            //移除：app_webview 目录
            final File appWebViewDir;

            appWebViewDir = new File(
                    appContext.getDataDir() + File.separator
                            + APP_WEB_VIEW_DIR_NAME + File.separator
                            + GPU_CACHE_DIR_NAME
            );
            deleteRecursive(appWebViewDir);
        } catch (Exception e) {
            printInfo(e.getMessage());
        }
    }

    private static void deleteRecursive(@NonNull File fileOrDirectory) {
        if (fileOrDirectory == null){
            return;
        }
        if (fileOrDirectory.isDirectory()) {
            File[] files = fileOrDirectory.listFiles();
            if (files == null || files.length <= 0 ){
                return;
            }
            for (File child : files) {
                if (child == null){
                    return;
                }
                deleteRecursive(child);
            }
        }
        boolean isSuccessDelete = fileOrDirectory.delete();
        printInfo("delete isSuccessDelete: " + isSuccessDelete + " fileName: " + fileOrDirectory);
    }

    private static void printInfo(@NonNull String message) {
        Logger.i("Abi64WebViewCompat", message);
    }
}
