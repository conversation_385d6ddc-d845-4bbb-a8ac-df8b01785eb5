package com.ximalaya.ting.android.host.util.server;

import static com.ximalaya.ting.android.host.util.server.NetworkUtils.getPlayType;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.earn.statistics.PushArrivedTraceManager;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.fixtoast.ToastCompat;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constant.SharedConstant;
import com.ximalaya.ting.android.host.manager.LastAudioPlayListCache;
import com.ximalaya.ting.android.host.manager.PlayCompleteRecommendManager;
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager;
import com.ximalaya.ting.android.host.manager.XimaTenDataManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILivePlaySource;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.LiveActionCallbackSafe;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.StartRoomIntent;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MiniDramaActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MyclubActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.play.LivePlayControlUtil;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.playlist.PlayListUtilForMainKt;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManager;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenDataManagerKt;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenManager;
import com.ximalaya.ting.android.host.manager.quicklisten.QuickListenPlayUtil;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.service.TingLocalMediaService;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.XimaTenDataUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.ActionConstants;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.onekey.DailyNewsLogicManager;
import com.ximalaya.ting.android.host.xdcs.model.UserTrackCookie;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.opensdk.model.live.program.Program;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.live.schedule.Schedule;
import com.ximalaya.ting.android.opensdk.model.statistic.RecordModel;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.statistics.manager.UserInteractivePlayStatistics;
import com.ximalaya.ting.android.opensdk.util.ModelUtil;
import com.ximalaya.ting.android.opensdk.util.PlayListMMKVUtil;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.opensdk.util.TrackUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;


/**
 * <AUTHOR>
 */
public class PlayTools {

    public interface IDataCallBackWithIndex<T> extends IDataCallBack<T> {
        void onSuccess(@Nullable T data, int index, Track t);
    }

    public static final String ACTION_LAUNCH_FROM_WIDGET = "com.ximalaya.ting.android.launch_from_widget";
    public static boolean isRequestRadioInfo = false;
    public static long noShowPlayFragmentTrackId = 0;

    public static boolean showPlayFragmentReq = false;

    public static void playTrack(final Context ctx, final Track track,
                                 final boolean startPlayer, final View fromView) {
        playTrack(ctx, track, startPlayer, fromView, true);
    }

    public static void playTrack(final Context ctx, final Track track,
                                 final boolean isNeedShowPlayFragment, final View fromView, boolean willPlay) {
        Logger.logToFile("XiaoaiControl == playTrackHistoy 6 ");
        if (track == null) {
            return;
        }
        Logger.logToFile("XiaoaiControl == playTrackHistoy 7 ");
        //未成年人保护拦截
        if (ChildProtectManager.checkCurrentSoundAndStartChildProtect(ctx, track)) {
            Logger.logToFile("XiaoaiControl == playTrackHistoy 8 ");
            return;
        }

        LiveActionCallbackSafe actionCallback = new LiveActionCallbackSafe() {
            @Override
            public void actionImpl() {
                boolean isDownloadedTrack = false;
                if (!TextUtils.isEmpty(track.getDownloadedSaveFilePath())) {
                    isDownloadedTrack = true;
                }

                noShowPlayFragmentTrackId = 0;

                if (isPlayModelLive(track)) {
                    NetworkUtils.confirmNetworkForLivePlay(new NetworkUtils.ConfirmNetWorkClickCallBack() {
                        @Override
                        public void onOkCallBack() {
                            XmPlayerManager.getInstance(ctx.getApplicationContext())
                                    .playList(Arrays.asList(track), 0);
                            Bundle bundle = new Bundle();
                            bundle.putLong("roomId", track.getLiveRoomId());
                            bundle.putLong("liveId", track.getDataId());
                            checkToLiveAudioPlayFragment(ctx, bundle, false, fromView);
                        }

                        @Override
                        public void onCancleCallBack() {
                            XmPlayerManager.getInstance(ctx.getApplicationContext())
                                    .setPlayList(Arrays.asList(track), 0);
                        }
                    }, isDownloadedTrack);

                } else {
                    Logger.logToFile("XiaoaiControl == playTrackHistoy 9 ");
                    boolean finalWillPlay = willPlay;
                    NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
                        @Override
                        public void onOkCallBack() {
                            Logger.logToFile("XiaoaiControl == playTrackHistoy 10 ");
                            if (!isNeedShowPlayFragment) {
                                noShowPlayFragmentTrackId = track.getDataId();
                            } else {
                                XmPlayerManager.getInstance(ctx.getApplicationContext()).setPlayFragmentIsShowing(true);
                            }

                            PlayCompleteRecommendManager.getInstance().finishRecommend();
                            if (finalWillPlay) {
                                XmPlayerManager.getInstance(ctx.getApplicationContext()).playList(Arrays.asList(track), 0);
                            } else {
                                XmPlayerManager.getInstance(ctx.getApplicationContext()).setPlayList(Arrays.asList(track), 0);
                            }
                            if (track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY) {
                                checkToOnePlayFrament(ctx, isNeedShowPlayFragment, fromView);

                            } else if (track.isWeikeTrack) {

                                if (!track.isWeikeSimplePlay) {//直播间

                                    Bundle bundle = new Bundle();
                                    bundle.putLong("key_live_roomid", track.getWeikeRoomId());
                                    bundle.putLong("key_live_lessonid", track.getWeikeLessonId());
                                    checkToWeikeLiveFragment(ctx, bundle, isNeedShowPlayFragment, fromView);

                                } else {//极简播放
                                    Bundle bundle = new Bundle();
                                    bundle.putLong("key_live_roomid", track.getWeikeRoomId());
                                    bundle.putLong("key_live_lessonid", track.getWeikeLessonId());
                                    bundle.putBoolean("key_is_weike_simpleplay", true);

                                    checkToWeikeSimplePlayFragment(ctx, bundle, isNeedShowPlayFragment, fromView);
                                }


                            } else if (track.getType() == Track.TYPE_DUBSHOW) {
                                Bundle bundle = new Bundle();
                                bundle.putLong(BundleKeyConstants.KEY_TRACK_ID, track.getDataId());
                                checkToDubShowPPTPlayFragment(ctx, bundle, isNeedShowPlayFragment, fromView);
                            } else if (Track.TYPE_MARK_TRACK == track.getType()) {
                                if (track.getStartPlayPos() >= 0) {
                                    int playPos = track.getStartPlayPos() * 1000 - 500;
                                    if (playPos < 0) {
                                        playPos = 0;
                                    }
                                    PlayableModel playableModel =
                                            XmPlayerManager.getInstance(ctx.getApplicationContext()).getCurrSound();
                                    if (playableModel != null && playableModel.getDataId() == track.getDataId()) {
                                        XmPlayerManager.getInstance(ctx.getApplicationContext()).seekTo(playPos);
                                    } else {
                                        XmPlayerManager.getInstance(ctx.getApplicationContext()).setHistoryPos(track.getDataId(), playPos);
                                    }
                                }
                                checkToPlayFragment(ctx, isNeedShowPlayFragment, fromView);
                            } else {
                                checkToPlayFragment(ctx, isNeedShowPlayFragment, fromView);
                            }
                        }

                        @Override
                        public void onCancleCallBack() {
                            Logger.logToFile("XiaoaiControl == playTrackHistoy 11 ");
                            XmPlayerManager.getInstance(ctx.getApplicationContext()).setPlayList(Arrays.asList(track), 0);
                        }
                    }, isDownloadedTrack, getPlayType(track));
                }
            }
        };

        //需要播放的声音为非直播最小化场景，需要检查直播连麦状态
        boolean isSupportMinimizeType = PlayableModel.KIND_LIVE_FLV.equals(track.getKind()) || PlayableModel.KIND_ENT_FLY.equals(track.getKind()) || PlayableModel.KIND_LIVE_COURSE.equals(track.getKind());
        if (!isSupportMinimizeType && checkHasLiveOnline(ctx, actionCallback)) {
            return;
        } else {
            actionCallback.action();
        }
//
//        if (willPlay && AdMakeVipLocalManager.getInstance().canShowUnlockAd(track)) {
//            willPlay = false;
//        }
    }

    public static void playTrackWithoutWifi(final Context ctx, final Track track,
                                            final boolean isNeedShowPlayFragment, final View fromView) {
        if (track == null) {
            return;
        }
        //未成年人保护拦截
        if (ChildProtectManager.checkCurrentSoundAndStartChildProtect(ctx, track)) {
            return;
        }

        LiveActionCallbackSafe actionCallback = new LiveActionCallbackSafe() {
            @Override
            public void actionImpl() {
                PlayCompleteRecommendManager.getInstance().finishRecommend();
                XmPlayerManager.getInstance(ctx.getApplicationContext())
                        .playList(Arrays.asList(track), 0);
                if (track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY) {
                    checkToOnePlayFrament(ctx, isNeedShowPlayFragment, fromView);
                } else {
                    checkToPlayFragment(ctx, isNeedShowPlayFragment, fromView);
                }
            }
        };

        if (checkHasLiveOnline(ctx, actionCallback)) {
            return;
        } else {
            actionCallback.action();
        }
    }

    private static void checkToPlayFragment(Context ctx, boolean isNeedShowPlayFragment, View fromView) {
        checkToPlayFragment(ctx, isNeedShowPlayFragment, fromView, SharedConstant.CHANNEL_UNKNOWN);
    }

    private static void checkToPlayFragment(Context ctx, boolean isNeedShowPlayFragment, View fromView, int channel) {
        checkToPlayFragmentAndSetPlayerType(ctx, isNeedShowPlayFragment, fromView, PlayerManager.PLAY_TAG, channel);
    }

    private static void checkToOnePlayFrament(Context ctx, boolean isNeedShowPlayFragment, View fromView) {
        checkToPlayFragmentAndSetPlayerType(ctx, isNeedShowPlayFragment, fromView, PlayerManager.PLAY_TAG);
    }

    private static void checkToLiveAudioPlayFragment(Context ctx, Bundle bundle, boolean isNeedShowPlayFragment, View fromView) {
        if (isNeedShowPlayFragment) {
            if (ctx instanceof MainActivity) {
                ((MainActivity) ctx).showLiveAudioPlayFragment(fromView, bundle);
            } else {
                Activity activity = MainApplication.getTopActivity();
                if (activity instanceof MainActivity) {
                    ((MainActivity) activity).showLiveAudioPlayFragment(fromView, bundle);
                }
            }
        } else {
            PlayerManager.getInstanse().setPlayWithNoFragment(PlayerManager.ACTIVITY_LIVE_TAG);
        }
    }

    private static void checkToPlayFragmentAndSetPlayerType(Context ctx, boolean isNeedShowPlayFragment, View fromView, int playerType) {
        checkToPlayFragmentAndSetPlayerType(ctx, isNeedShowPlayFragment, fromView, playerType, SharedConstant.CHANNEL_UNKNOWN);
    }

    private static void checkToPlayFragmentAndSetPlayerType(Context ctx, boolean isNeedShowPlayFragment, View fromView, int playerType, int channel) {
        checkToPlayFragmentAndSetPlayerType(ctx, isNeedShowPlayFragment, fromView, playerType, channel, null);
    }

    private static void checkToPlayFragmentAndSetPlayerType(Context ctx, boolean isNeedShowPlayFragment, View fromView, int playerType, int channel, Bundle bundle) {
        if (isNeedShowPlayFragment) {
            showPlayFragmentReq = true;
            if (ctx instanceof MainActivity) {
                ((MainActivity) ctx).showPlayFragment(fromView, channel, bundle, playerType);
            } else {
                Activity activity = MainApplication.getTopActivity();
                if (activity instanceof MainActivity) {
                    ((MainActivity) activity).showPlayFragment(fromView, channel, bundle, playerType);
                }
            }
        } else {
            PlayerManager.getInstanse().setPlayWithNoFragment(playerType);
        }
    }


    public static void playList(final Context ctx, List<Track> tracks,
                                int index, View fromView) {
        playList(ctx, tracks, index, true, fromView);
    }

    public static void playList(final Context ctx, final List<Track> tracks,
                                final int index, final boolean isNeedShowPlayFragment, final View fromView) {
        playList(ctx, tracks, index, isNeedShowPlayFragment, false, fromView);
    }

    public static void playList(final Context ctx, final List<Track> tracks,
                                final int index, final boolean isNeedShowPlayFragment, final View fromView, boolean shouldFinishRecommend) {
        playList(ctx, tracks, index, isNeedShowPlayFragment, false, fromView, shouldFinishRecommend);
    }

    public static void playList(final Context ctx, final List<Track> tracks,
                                final int index, final boolean isNeedShowPlayFragment,
                                final boolean isContinuePlayWhenAuditionTrackPlayComplete, final View fromView) {
        playList(ctx, tracks, index, isNeedShowPlayFragment, isContinuePlayWhenAuditionTrackPlayComplete, fromView, true);
    }

    public static void playList(final Context ctx, final List<Track> tracks,
                                final int index, final boolean isNeedShowPlayFragment,
                                final boolean isContinuePlayWhenAuditionTrackPlayComplete, final View fromView,
                                final boolean shouldFinishRecommend) {
        if (tracks == null || tracks.isEmpty() || index < 0 || index >= tracks.size()) {
            return;
        }

        //未成年人保护拦截
        if (ChildProtectManager.checkCurrentSoundAndStartChildProtect(ctx, tracks, index)) {
            return;
        }

        LiveActionCallbackSafe actionCallback = new LiveActionCallbackSafe() {
            @Override
            public void actionImpl() {
                UserTrackCookie.getInstance().setXmPlayResource();
                Track track = tracks.get(index);
                boolean isDownloadedTrack = false;
                if (track == null) {
                    return;
                }
                if (!TextUtils.isEmpty(track.getDownloadedSaveFilePath())) {
                    isDownloadedTrack = true;
                }

                noShowPlayFragmentTrackId = 0;
                NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
                    @Override
                    public void onOkCallBack() {
                        if (shouldFinishRecommend) {
                            PlayCompleteRecommendManager.getInstance().finishRecommend();
                        }
                        XmPlayerManager.getInstance(ctx).setContinuePlayWhileAuditionTrackPlayComplete(isContinuePlayWhenAuditionTrackPlayComplete);
                        XmPlayerManager.getInstance(ctx).playList(tracks, index);

                        if (!isNeedShowPlayFragment && !ToolUtil.isEmptyCollects(tracks)
                                && index >= 0 && index < tracks.size()) {
                            noShowPlayFragmentTrackId = tracks.get(index).getDataId();
                        }

                        if (tracks.get(0).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY) {
                            checkToOnePlayFrament(ctx, isNeedShowPlayFragment, fromView);

                        } else if (tracks.get(0).isWeikeTrack) {//TODO 微课 直播间


                            if (!tracks.get(0).isWeikeSimplePlay) {//直播间

                                Bundle bundle = new Bundle();
                                bundle.putLong("key_live_roomid", tracks.get(0).getWeikeRoomId());
                                bundle.putLong("key_live_lessonid", tracks.get(0).getWeikeLessonId());
                                checkToWeikeLiveFragment(ctx, bundle, isNeedShowPlayFragment, fromView);

                            } else {//极简播放
                                Bundle bundle = new Bundle();
                                bundle.putLong("key_live_roomid", tracks.get(0).getWeikeRoomId());
                                bundle.putLong("key_live_lessonid", tracks.get(0).getWeikeLessonId());
                                bundle.putBoolean("key_is_weike_simpleplay", true);

                                checkToWeikeSimplePlayFragment(ctx, bundle, isNeedShowPlayFragment, fromView);
                            }


                        } else {
                            checkToPlayFragment(ctx, isNeedShowPlayFragment, fromView);
                        }
                    }

                    @Override
                    public void onCancleCallBack() {
                        XmPlayerManager.getInstance(ctx)
                                .setPlayList(tracks, index);
                    }
                }, isDownloadedTrack, getPlayType(track));
            }
        };

        if (checkHasLiveOnline(ctx, actionCallback)) {
            return;
        } else {
            actionCallback.action();
        }
    }

    public static void playListWithoutWifi(final Context ctx,
                                           final List<Track> tracks, final int index,
                                           final boolean isNeedShowPlayFragment,
                                           final View fromView) {
        playListWithoutWifi(ctx, tracks, index, isNeedShowPlayFragment, fromView, null);
    }

    public static void playListWithoutWifi(final Context ctx,
                                           final List<Track> tracks, final int index,
                                           final boolean isNeedShowPlayFragment,
                                           final View fromView, Bundle bundle) {
        if (ctx == null || tracks == null || tracks.isEmpty()) {
            return;
        }
        //未成年人保护拦截
        if (ChildProtectManager.checkCurrentSoundAndStartChildProtect(ctx, tracks, index)) {
            return;
        }

        LiveActionCallbackSafe actionCallback = new LiveActionCallbackSafe() {
            @Override
            public void actionImpl() {
                UserTrackCookie.getInstance().setXmPlayResource();
                PlayCompleteRecommendManager.getInstance().finishRecommend();
                XmPlayerManager.getInstance(ctx.getApplicationContext()).playList(
                        tracks, index);
                if (tracks.get(0).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY) {
                    checkToOnePlayFrament(ctx, isNeedShowPlayFragment, fromView);
                } else {
                    checkToPlayFragmentAndSetPlayerType(ctx, isNeedShowPlayFragment, fromView, PlayerManager.PLAY_TAG, SharedConstant.CHANNEL_UNKNOWN, bundle);
                }
            }
        };

        if (checkHasLiveOnline(ctx, actionCallback)) {
            return;
        } else {
            actionCallback.action();
        }

    }

    public static void playCommonListWithoutWifi(final Context ctx,
                                                 final CommonTrackList<Track> commonTrackList, final int index,
                                                 final boolean isNeedShowPlayFragment, final View fromView, Bundle bundle) {
        if (ctx == null || commonTrackList == null || ToolUtil.isEmptyCollects(commonTrackList.getTracks())) {
            return;
        }
        //未成年人保护拦截
        if (ChildProtectManager.checkCurrentSoundAndStartChildProtect(ctx, commonTrackList.getTracks(), index)) {
            return;
        }

        LiveActionCallbackSafe actionCallback = new LiveActionCallbackSafe() {
            @Override
            public void actionImpl() {
                List<Track> tracks = commonTrackList.getTracks();
                UserTrackCookie.getInstance().setXmPlayResource();
                PlayCompleteRecommendManager.getInstance().finishRecommend();
                XmPlayerManager.getInstance(ctx.getApplicationContext()).playList(
                        commonTrackList, index);
                if (tracks.get(0).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY) {
                    checkToOnePlayFrament(ctx, isNeedShowPlayFragment, fromView);
                } else {
                    checkToPlayFragmentAndSetPlayerType(ctx, isNeedShowPlayFragment, fromView, PlayerManager.PLAY_TAG, SharedConstant.CHANNEL_UNKNOWN, bundle);
                }
            }
        };

        if (checkHasLiveOnline(ctx, actionCallback)) {
            return;
        } else {
            actionCallback.action();
        }
    }

    public static boolean isAlbumPlaying(Context ctx, long albumId) {
        PlayableModel model = XmPlayerManager.getInstance(ctx).getCurrSound();
        if (model != null && model instanceof Track) {
            Track curTrack = (Track) model;
            return curTrack.getAlbum() != null
                    && curTrack.getAlbum().getAlbumId() == albumId
                    && XmPlayerManager.getInstance(ctx).isPlaying();
        }
        return false;
    }

    public static boolean isAlbumAdPlaying(Context ctx, long albumId) {
        PlayableModel model = XmPlayerManager.getInstance(ctx).getCurrSound();
        if (model instanceof Track) {
            Track curTrack = (Track) model;
            return curTrack.getAlbum() != null
                    && curTrack.getAlbum().getAlbumId() == albumId
                    && XmPlayerManager.getInstance(ctx).isAdPlaying();
        }
        return false;
    }

    public static boolean isAlbumBuffering(Context ctx, long albumId) {
        PlayableModel model = XmPlayerManager.getInstance(ctx).getCurrSound();
        if (model instanceof Track) {
            Track curTrack = (Track) model;
            return curTrack.getAlbum() != null
                    && curTrack.getAlbum().getAlbumId() == albumId
                    && XmPlayerManager.getInstance(ctx).isBuffering();
        }
        return false;
    }

    public static boolean isCurrentAlbum(Context ctx, long albumId) {
        PlayableModel model = XmPlayerManager.getInstance(ctx).getCurrSound();
        if (model instanceof Track) {
            Track curTrack = (Track) model;
            return curTrack.getAlbum() != null
                    && curTrack.getAlbum().getAlbumId() == albumId;
        }
        return false;
    }

    @Nullable
    public static Track getCurTrack(Context context) {
        return XmPlayerManager.getInstance(context).getCurrSoundIgnoreKind(true);
    }

    @Nullable
    public static Track getCurTrack(Context context, boolean useCache) {
        if (XmPlayerManager.getInstance(context) != null) {
            return XmPlayerManager.getInstance(context).getCurrSoundIgnoreKind(useCache);
        }
        return null;
    }

    public static long getCurTrackId(Context context) {
        Track track = getCurTrack(context);
        if (track != null) {
            return track.getDataId();
        }
        return -1;
    }

    public static boolean isCurrentTrack(Context ctx, @NonNull Track t) {
        PlayableModel curModel = XmPlayerManager.getInstance(ctx)
                .getCurrSound();
        return t.equals(curModel);
    }

    public static boolean isCurrentTrack(Context ctx, @NonNull Track t, boolean useCache) {
        PlayableModel curModel = XmPlayerManager.getInstance(ctx)
                .getCurrSound(useCache);
        return t.equals(curModel);
    }

    public static boolean isCurrentTrackPlaying(Context ctx, Track t) {
        XmPlayerManager xmp = XmPlayerManager.getInstance(ctx);
        PlayableModel curModel = xmp.getCurrSound();
        return curModel != null && curModel.equals(t) && xmp.isPlaying();
    }

    //微课 直播
    public static boolean isCurrentWeikeTrackPlaying(Context ctx, String weikeTrackId) {
        XmPlayerManager xmp = XmPlayerManager.getInstance(ctx);
        PlayableModel curModel = xmp.getCurrWeikeSound(true);
        return curModel != null
                && (TextUtils.equals(weikeTrackId, curModel.weikeTrackId))
                && xmp.isPlaying();
    }

    //微课 直播
    public static boolean isCurrentWeikeTrackPlayOrPause(Context ctx, String weikeTrackId) {
        XmPlayerManager xmp = XmPlayerManager.getInstance(ctx);
        PlayableModel curModel = xmp.getCurrWeikeSound(true);
        return curModel != null
                && (TextUtils.equals(weikeTrackId, curModel.weikeTrackId));
    }

    public static void goPlayByTrackId(final Context actContext, long trackId, final View fromView,
                                       final int playSource, final boolean willShowPlay, boolean willPlayList) {
        goPlayByTrackId(actContext, trackId, fromView, playSource, willShowPlay, willPlayList, true);
    }

    /**
     * 根据trackid播放声音
     *
     * @param willPlayList 如果播放列表有这声音就从播放列表中播放
     */
    public static void goPlayByTrackId(final Context actContext, long trackId, final View fromView,
                                       final int playSource, final boolean willShowPlay,
                                       boolean willPlayList, boolean isNeedLoadPlayCompleteData) {
        Track t = new Track();
        t.setDataId(trackId);
        XmPlayerManager xmp = XmPlayerManager.getInstance(actContext);

        List<Track> trackList = xmp.getPlayList();

        if (willPlayList && trackList != null && trackList.size() != 0) {
            if (trackList.contains(t)) {
                int index = trackList.indexOf(t);
                if (index >= 0) {
                    xmp.play(index);
                    if (actContext instanceof MainActivity) {
                        ((MainActivity) actContext).showPlayFragment(fromView,
                                PlayerManager.PLAY_TAG);
                    }
                    return;
                }
            }
        }
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_TRACK_ID, trackId + "");
        CommonRequestM.getTrackInfoDetail(params, new IDataCallBack<TrackM>() {

            @Override
            public void onSuccess(TrackM object) {
                if (object != null) {
                    object.setPlaySource(playSource);
                    object.setUpdateStatus(true);
                    object.setNeedLoadPlayCompleteData(isNeedLoadPlayCompleteData);
                    PlayCompleteRecommendManager.getInstance().finishRecommend();
                    playTrack(actContext, object, willShowPlay, fromView);
                }
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showFailToast(message);
            }
        });
    }

    /**
     * 根据trackid播放声音
     *
     * @param willPlayList 如果播放列表有这声音就从播放列表中播放
     */
    public static void goPlayByTrackIdSetSceneId(final Context actContext, long trackId, long sceneId, final View fromView,
                                       final int playSource, final boolean willShowPlay,
                                       boolean willPlayList, boolean isNeedLoadPlayCompleteData, boolean forcePlayTrackId) {

        Track t = new Track();

        XmPlayerManager xmp = XmPlayerManager.getInstance(actContext);

        List<Track> trackList = xmp.getPlayList();

        if (forcePlayTrackId) {
            t.setDataId(trackId);
        } else {
            t.setDataId(QuickListenManager.findLastCollectTrackId(trackId));
        }

        Logger.log("PlayTools : goPlayByTrackIdSetSceneId  trackId=" + trackId + "   dataId=" + t.getDataId());

        if (willPlayList && trackList != null && trackList.size() != 0) {
            if (trackList.contains(t)) {
                int index = trackList.indexOf(t);
                if (index >= 0) {
                    xmp.play(index);
                    if (actContext instanceof MainActivity) {
                        ((MainActivity) actContext).showPlayFragment(fromView,
                                PlayerManager.PLAY_TAG);
                    }
                    return;
                }
            }
        }
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_TRACK_ID, trackId + "");
        CommonRequestM.getTrackInfoDetail(params, new IDataCallBack<TrackM>() {

            @Override
            public void onSuccess(TrackM object) {
                if (object != null) {
                    object.setPlaySource(playSource);
                    object.setChannelGroupId(sceneId);
                    object.setUpdateStatus(true);
                    object.setNeedLoadPlayCompleteData(isNeedLoadPlayCompleteData);
                    PlayCompleteRecommendManager.getInstance().finishRecommend();
                    playTrack(actContext, object, willShowPlay, fromView);
                }
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showFailToast(message);
            }
        });
    }

    /**
     * startPos 从指定位置开始播放  用于标记声音
     */
    public static void goPlayByTrackId(final Context actContext, long trackId, final View fromView,
                                       final int playSource, final boolean willShowPlay, int startPos) {
        Map<String, String> params = new ArrayMap<>(1);
        params.put(HttpParamsConstants.PARAM_TRACK_ID, trackId + "");
        CommonRequestM.getTrackInfoDetail(params, new IDataCallBack<TrackM>() {

            @Override
            public void onSuccess(TrackM object) {
                if (object != null) {
                    object.setPlaySource(playSource);
                    object.setUpdateStatus(true);
                    PlayCompleteRecommendManager.getInstance().finishRecommend();
                    object.setType(Track.TYPE_MARK_TRACK);
                    object.setStartPlayPos(startPos);
                    playTrack(actContext, object, willShowPlay, fromView);
                }
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showFailToast(message);
            }
        });
    }

    public static void goPlayByTrackId(final Context actContext, long trackId, final View fromView, final int playSource, final boolean willShowPlay) {
        goPlayByTrackId(actContext, trackId, fromView, playSource, willShowPlay, true);
    }

    public static void goPlayByTrackId(final Context actContext, long trackId, final View fromView, final int playSource) {
        goPlayByTrackId(actContext, trackId, fromView, playSource, true);
    }

    public static void playCommonListAfterPlayServiceConnected(final Context ctx,
                                                               final CommonTrackList data, final int position,
                                                               final boolean startPlayer, final View fromView) {
        if (XmPlayerManager.getInstance(ctx).isConnected()) {
            Logger.d("playTools", "playCommonListAfterPlayServiceConnected connected 1");
            playCommonList(ctx, data, position, startPlayer, fromView);
        } else {
            XmPlayerManager.getInstance(ctx).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                @Override
                public void onConnected() {
                    XmPlayerManager.getInstance(ctx).removeOnConnectedListerner(this);

                    Logger.d("playTools", "playCommonListAfterPlayServiceConnected connected 2");
                    playCommonList(ctx, data, position, startPlayer, fromView);
                }
            });
        }
    }

    public static void playCommonList(final Context ctx,
                                      final CommonTrackList data, final int position,
                                      final boolean isNeedShowPlayFragment, final View fromView) {
        playCommonList(ctx, data, position, isNeedShowPlayFragment, fromView, SharedConstant.CHANNEL_UNKNOWN);
    }

    public static void playCommonList(final Context ctx,
                                      final CommonTrackList data, final int position,
                                      final boolean startPlayer, final View fromView, final int channel) {
        playCommonList(ctx, data, position, startPlayer, fromView, channel, true);
    }

    public static void playCommonList(final Context ctx,
                                      final CommonTrackList data, final int position, final boolean isNeedShowPlayFragment,
                                      final View fromView, final int channel, boolean willPlay) {
        playCommonList(ctx, data, position, isNeedShowPlayFragment, fromView, channel, willPlay, null);
    }

    public static void playCommonList(final Context ctx,
                                      final CommonTrackList data, final int position, final boolean isNeedShowPlayFragment,
                                      final View fromView, final int channel, boolean willPlay, Bundle playBundle) {

        Logger.logToFile("XiaoaiControl == playCommonList 1 ");
        if (data == null || data.getTracks() == null
                || data.getTracks().size() == 0 || position >= data.getTracks().size() || position < 0) {
            return;
        }
        Logger.logToFile("XiaoaiControl == playCommonList 2 ");
        //未成年人保护拦截
        if (ChildProtectManager.checkCurrentSoundAndStartChildProtect(ctx, data.getTracks(), position)) {
            return;
        }
        Logger.logToFile("XiaoaiControl == playCommonList 3 ");

        LiveActionCallbackSafe actionCallback = new LiveActionCallbackSafe() {
            @Override
            public void actionImpl() {
                Logger.logToFile("XiaoaiControl == playCommonList 4 ");
                UserTrackCookie.getInstance().setXmPlayResource();
                Object object = data.getTracks().get(position);
                boolean isDownloadedTrack = false;
                int playType = DownloadTools.NORMAL_SOUND_TYPE;
                if (object instanceof Track) {
                    Track track = (Track) object;
                    playType = getPlayType(track);
                    isDownloadedTrack = RouteServiceUtil.getDownloadService().isDownloadedAndFileExist(track);
                }
                final boolean finalWillPlay = willPlay;

                NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
                    @Override
                    public void onOkCallBack() {
                        Logger.logToFile("XiaoaiControl == playCommonList 5 ");
                        if (isNeedShowPlayFragment) {
                            XmPlayerManager.getInstance(ctx).setPlayFragmentIsShowing(true);
                        }
                        PlayCompleteRecommendManager.getInstance().finishRecommend();

                        if (XmPlayerManager.getInstance(ctx).isConnected()) {
                            Logger.logToFile("XiaoaiControl == playCommonList 6 ");
                            if (object instanceof Track) {
                                seekToStartPos(ctx, (Track) object);
                            }
                            if (finalWillPlay) {
                                XmPlayerManager.getInstance(ctx).playList(data, position);
                            } else {
                                XmPlayerManager.getInstance(ctx).setPlayList(data, position);
                            }
                        } else {
                            Logger.logToFile("XiaoaiControl == playCommonList 7 ");

                            String stepName = "waitPlayerServiceProcessConnected";
                            UserInteractivePlayStatistics.stepBegin(stepName);
                            XmPlayerManager.getInstance(ctx).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                                @Override
                                public void onConnected() {
                                    Logger.logToFile("XiaoaiControl == playCommonList 8 ");

                                    XmPlayerManager.getInstance(ctx).removeOnConnectedListerner(this);
                                    if (object instanceof Track) {
                                        seekToStartPos(ctx, (Track) object);

                                        Track track = (Track) object;
                                        UserInteractivePlayStatistics.stepEnd(track.getDataId(), stepName);
                                    }

                                    if (finalWillPlay) {
                                        XmPlayerManager.getInstance(ctx).playList(data, position);
                                    } else {
                                        XmPlayerManager.getInstance(ctx).setPlayList(data, position);
                                    }
                                }
                            });
                        }

                        if (data.getTracks().get(0) instanceof Track &&
                                ((Track) data.getTracks().get(0)).getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY) {
                            checkToPlayFragmentAndSetPlayerType(ctx, isNeedShowPlayFragment, fromView, PlayerManager.PLAY_TAG, SharedConstant.CHANNEL_UNKNOWN, playBundle);
                        } else {
                            checkToPlayFragmentAndSetPlayerType(ctx, isNeedShowPlayFragment, fromView, PlayerManager.PLAY_TAG, channel, playBundle);
                        }
                    }

                    @Override
                    public void onCancleCallBack() {
                        Logger.logToFile("XiaoaiControl == playCommonList 6 ");
                        XmPlayerManager.getInstance(ctx).setPlayList(data, position);
                    }
                }, isDownloadedTrack, playType);
            }
        };

        if (checkHasLiveOnline(ctx, actionCallback)) {
            return;
        } else {
            actionCallback.action();
        }
    }

    private static void seekToStartPos(Context context, Track track) {
        if (track != null && track.isNeedSeekToDesPos() && track.getStartPlayPos() >= 0) {
            int playPos = track.getStartPlayPos() * 1000;
            if (playPos < 0) {
                playPos = 0;
            }
            XmPlayerManager.getInstance(context).setHistoryPos(track.getDataId(), playPos);
        }
    }

    public static void play(final Context ctx) {

        NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
            @Override
            public void onOkCallBack() {
                XmPlayerManager.getInstance(ctx).play();
            }

            @Override
            public void onCancleCallBack() {

            }
        }, !XmPlayerManager.getInstance(ctx).isOnlineSource(), getPlayType(XmPlayerManager.getInstance(ctx).getCurrSound()));
    }

    public static void play(final Context ctx, final int index, boolean isDownloaded) {
        if (isDownloaded) {
            XmPlayerManager.getInstance(ctx).play(index);
            return;
        }
        NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
            @Override
            public void onOkCallBack() {
                XmPlayerManager.getInstance(ctx).play(index);
            }

            @Override
            public void onCancleCallBack() {

            }
        }, isDownloaded, getPlayType(XmPlayerManager.getInstance(ctx).getTrack(index)));
    }

    public static void play(final Context ctx, final int index, boolean isDownloaded, boolean playNow) {
        if (isDownloaded) {
            if (playNow) {
                XmPlayerManager.getInstance(ctx).play(index);
            } else {
                XmPlayerManager.getInstance(ctx).setPlayIndex(index);
            }
            return;
        }
        NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
            @Override
            public void onOkCallBack() {
                if (playNow) {
                    XmPlayerManager.getInstance(ctx).play(index);
                } else {
                    XmPlayerManager.getInstance(ctx).setPlayIndex(index);
                }
            }

            @Override
            public void onCancleCallBack() {

            }
        }, isDownloaded, getPlayType(XmPlayerManager.getInstance(ctx).getTrack(index)));
    }

    /**
     * 加载播放历史音频
     */
    private static void loadAndPlay(Context activity, final CommonTrackList<TrackM> data, int position,
                                    View view, boolean startPlayer) {
        for (Track track : data.getTracks()) {
            track.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PLAY_HISTORY);
        }
        PlayTools.playCommonList(activity, data, position, startPlayer, view);
    }

    private static CommonTrackList<Track> getCommonTrackList(Context context, long albumId, List<Track> tracks) {
        CommonTrackList<Track> commonTrackList = new CommonTrackList<>();
        commonTrackList.setTracks(tracks);

        Map<String, String> params = new HashMap<>();
        boolean isAsc = true;
        try {
            isAsc = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().isAlbumAsc(context, albumId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        params.put(DTransferConstants.LOCAL_IS_ASC, String.valueOf(!isAsc));  // NOTICE: 播放器内部的升序代表的意义貌似和此处定义的相反
        commonTrackList.setParams(params);

        return commonTrackList;
    }

    public static void playTrackHistoy(final Context activity, final boolean needPlay, final Track t, final IplayTrackHistoryCallback uiCallback) {
        playTrackHistoy(activity, needPlay, t, uiCallback, true, false, false);
    }

    public static void playTrackHistoy(final Context activity, final boolean needPlay, final Track t, final IplayTrackHistoryCallback uiCallback, boolean startPlayer, boolean inDrivingMode) {
        playTrackHistoy(activity, needPlay, t, uiCallback, startPlayer, inDrivingMode, false);
    }

    public static void playTrackHistoy(final Context activity, final boolean needPlay, final Track t, final IplayTrackHistoryCallback uiCallback, boolean startPlayer, boolean inDrivingMode, boolean skipCompleteSound) {
        playTrackHistoy(activity, needPlay, t, uiCallback, startPlayer, inDrivingMode, skipCompleteSound, false);
    }

    public static void playTrackHistoy(final Context activity, final boolean needPlay, final Track t, final IplayTrackHistoryCallback uiCallback, boolean startPlayer, boolean inDrivingMode, boolean skipCompleteSound, IDataCallbackWithDriveMode dataCallbackWithDriveMode) {
        playTrackHistoy(activity, needPlay, t, uiCallback, startPlayer, inDrivingMode, skipCompleteSound, false, dataCallbackWithDriveMode);
    }

    /**
     * @param activity
     * @param needPlay
     * @param t
     * @param uiCallback
     * @param startPlayer
     * @param inDrivingMode
     * @param skipCompleteSound 是否跳过已经播完的声音
     * @param notOnlyDownload   不要只取已下载的
     */
    public static void playTrackHistoy(final Context activity, final boolean needPlay, final Track t,
                                       final IplayTrackHistoryCallback uiCallback, boolean startPlayer,
                                       boolean inDrivingMode, boolean skipCompleteSound, boolean notOnlyDownload) {

        if (!notOnlyDownload) {
            boolean trackIsDownload = RouteServiceUtil.getDownloadService().isDownloadedAndFileExist(t);//DownloadTools.trackIsDownloadedAndCheckFileIsExist(t);
            if (trackIsDownload) {
                SubordinatedAlbum album = t.getAlbum();
                if (album != null) {
                    List<Track> trackList = RouteServiceUtil.getDownloadService().getDownloadedTrackListInAlbumSorted(album.getAlbumId());//downloader.getDownloadedTrackListInAlbum(album.getAlbumId());
                    boolean isAsc = true;
                    try {
                        isAsc = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().isAlbumAsc(activity, album.getAlbumId());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (!isAsc) {  //无论isAsc的值为什么， getDownloadedTrackListInAlbumSorted返回的列表顺序是一定。为false时需要reverse以下
                        Collections.reverse(trackList);
                    }

                    if (trackList != null && trackList.size() > 0) {
                        for (Track track : trackList) {
                            track.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PLAY_HISTORY);
                        }
                        int tIndex = trackList.indexOf(t);
                        if (tIndex >= 0) {
                            if (needPlay) {
                                if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_PLAYSORT_OPTIMIZATION, false)) {
                                    PlayTools.playTrackWithAlbum(activity, t, startPlayer);
                                } else {
                                    PlayTools.playCommonList(activity, getCommonTrackList(activity, album.getAlbumId(), trackList), tIndex, startPlayer, null);
                                }
                            } else {
                                if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_PLAYSORT_OPTIMIZATION, false)) {
                                    PlayTools.playTrackWithAlbum(activity, t, false, false);
                                } else {
                                    XmPlayerManager.getInstance(activity).setPlayList(getCommonTrackList(activity, album.getAlbumId(), trackList), tIndex);
                                }
                            }
                        }
                        if (uiCallback != null) {
                            uiCallback.onSuccess();

                        }
                        return;
                    }
                }
            }
        }

        //		新接口
        final HashMap<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, t.getAlbum() != null ? t.getAlbum()
                .getAlbumId() + "" : "0");
        params.put(HttpParamsConstants.PARAM_TRACK_ID, t.getDataId() + "");

        IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
        int order = historyManager != null ? historyManager.getAlbumSortByAlbumId(
                t.getAlbum() != null ? t.getAlbum().getAlbumId() : 0) : IHistoryManagerForMain.ASC_ORDER;
        //asc为true时貌似时倒序，和示意有点不符,服务端默认是倒序
        final boolean isAsc = (order == IHistoryManagerForMain.ASC_ORDER);
        params.put(HttpParamsConstants.PARAM_ASC, String.valueOf(isAsc));
        if (inDrivingMode) {
            params.put("inDrivingMode", "true");
        }
        CommonRequestM.getPlayHistory(params, new IDataCallBack<ListModeBase<TrackM>>() {
            @Override
            public void onSuccess(ListModeBase<TrackM> object) {
                if (object != null && object.getList() != null) {

                    if (object.getList().indexOf(t) == -1) {
                        if (uiCallback != null) {
                            uiCallback.onError(-1, activity.getResources().getString(R.string.host_track_deleted));
                        }
                        return;
                    }

                    int pageId = object.getPageId();
                    int maxPageId = object.getMaxPageId();
                    int totalCount = object.getTotalCount();
                    params.put(DTransferConstants.PAGE, pageId + "");
                    params.put(DTransferConstants.TOTAL_PAGE, String.valueOf(maxPageId));
                    params.put(DTransferConstants.PRE_PAGE, String.valueOf(pageId - 1));

                    if (object.getList() != null) {
                        if (uiCallback != null) {
                            uiCallback.onSuccess();
                        }
                        int playIndex = object.getList().indexOf(t);
                        Logger.d("playHis", "skipCompleteSound " + skipCompleteSound
                                + ", playIndex: " + playIndex + ", listSize: " + object.getList().size());
                        if (skipCompleteSound && playIndex + 1 < object.getList().size()) {
                            int lastPos = XmPlayerManager.getInstance(activity).getHistoryPos(t.getDataId());
                            boolean trackPlayComplete = ToolUtil.isTrackPlayComplete(lastPos, t.getDuration());
                            Logger.d("playHis", "skipCompleteSound true, lastPos: " + lastPos
                                    + "， t.getDuration(): " + t.getDuration()
                                    + ", trackPlayComplete: " + trackPlayComplete);
                            if (trackPlayComplete) {
                                Logger.d("playHis", "trackPlayComplete playNext");
                                playIndex += 1;
                            }
                        }
                        Track willPlayTrack = t;
                        if (playIndex >= 0 && playIndex < object.getList().size()) {
                            willPlayTrack = object.getList().get(playIndex);
                        }
                        if (needPlay) {
                            if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_PLAYSORT_OPTIMIZATION, false)) {
                                willPlayTrack.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PLAY_HISTORY);
                                PlayTools.playTrackWithAlbum(activity, willPlayTrack, startPlayer);
                            } else {
                                loadAndPlay(activity, ListModeBase.toCommonTrackList(object), playIndex, null, startPlayer);
                            }
                        } else {
                            if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_PLAYSORT_OPTIMIZATION, false)) {
                                PlayTools.playTrackWithAlbum(activity, willPlayTrack, false, false);
                            } else {
                                XmPlayerManager.getInstance(activity).setPlayList(ListModeBase.toCommonTrackList(object), playIndex);
                            }
                        }
                    } else {
                        if (uiCallback != null) {
                            uiCallback.onError(-1, object.getMsg());
                        }
                    }
                } else {
                    if (uiCallback != null) {
                        uiCallback.onError(-1, activity.getResources().getString(R.string.host_network_error));
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                SubordinatedAlbum album = t.getAlbum();
                if (album != null) {
                    List<Track> trackList = RouteServiceUtil.getDownloadService().getDownloadedTrackListInAlbum(album.getAlbumId());//downloader.getDownloadedTrackListInAlbum(album.getAlbumId());
                    DownloadTools.sortByDownloadTime(trackList);
                    if (trackList != null && trackList.size() > 0) {
                        for (Track track : trackList) {
                            track.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PLAY_HISTORY);
                        }
                        int index = trackList.indexOf(t);
                        if (index >= 0) {
                            if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_PLAYSORT_OPTIMIZATION, false)) {
                                PlayTools.playTrackWithAlbum(activity, t, false);
                            } else {
                                PlayTools.playList(activity, trackList, index, null);
                            }
                            if (uiCallback != null) {
                                uiCallback.onError(-1, message + "");
                            }
                        }
                        return;
                    }
                }
                if (uiCallback != null) {
                    uiCallback.onError(code, message + "");
                }
            }
        });

    }

    public interface IDataCallbackWithDriveMode {
        void getDataSuccess(CommonTrackList commonTrackList, int playIndex);

        void getDataFailed(int errorCode, String message);
    }

    /**
     * @param activity
     * @param needPlay
     * @param t
     * @param uiCallback
     * @param startPlayer
     * @param inDrivingMode
     * @param skipCompleteSound 是否跳过已经播完的声音
     * @param notOnlyDownload   不要只取已下载的
     */
    public static void playTrackHistoy(final Context activity, final boolean needPlay, final Track t,
                                       final IplayTrackHistoryCallback uiCallback, boolean startPlayer,
                                       boolean inDrivingMode, boolean skipCompleteSound, boolean notOnlyDownload, IDataCallbackWithDriveMode dataCallbackWithDriveMode) {

        if (!notOnlyDownload) {
            boolean trackIsDownload = RouteServiceUtil.getDownloadService().isDownloadedAndFileExist(t);//DownloadTools.trackIsDownloadedAndCheckFileIsExist(t);
            if (trackIsDownload) {
                SubordinatedAlbum album = t.getAlbum();
                if (album != null) {
                    List<Track> trackList = RouteServiceUtil.getDownloadService().getDownloadedTrackListInAlbumSorted(album.getAlbumId());//downloader.getDownloadedTrackListInAlbum(album.getAlbumId());
                    boolean isAsc = true;
                    try {
                        isAsc = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().isAlbumAsc(activity, album.getAlbumId());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (!isAsc) {  //无论isAsc的值为什么， getDownloadedTrackListInAlbumSorted返回的列表顺序是一定。为false时需要reverse以下
                        Collections.reverse(trackList);
                    }

                    if (trackList != null && trackList.size() > 0) {
                        for (Track track : trackList) {
                            track.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PLAY_HISTORY);
                        }
                        int tIndex = trackList.indexOf(t);
                        if (tIndex >= 0) {
                            if (needPlay) {
                                PlayTools.playCommonList(activity, getCommonTrackList(activity, album.getAlbumId(), trackList), tIndex, startPlayer, null);
                            } else {
                                XmPlayerManager.getInstance(activity).setPlayList(getCommonTrackList(activity, album.getAlbumId(), trackList), tIndex);
                            }
                        }
                        if (uiCallback != null) {
                            uiCallback.onSuccess();

                        }
                        return;
                    }
                }
            }
        }

        //		新接口
        final HashMap<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, t.getAlbum() != null ? t.getAlbum()
                .getAlbumId() + "" : "0");
        params.put(HttpParamsConstants.PARAM_TRACK_ID, t.getDataId() + "");

        IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
        int order = historyManager != null ? historyManager.getAlbumSortByAlbumId(
                t.getAlbum() != null ? t.getAlbum().getAlbumId() : 0) : IHistoryManagerForMain.ASC_ORDER;
        //asc为true时貌似时倒序，和示意有点不符,服务端默认是倒序
        final boolean isAsc = (order == IHistoryManagerForMain.ASC_ORDER);
        params.put(HttpParamsConstants.PARAM_ASC, String.valueOf(isAsc));
        if (inDrivingMode) {
            params.put("inDrivingMode", "true");
        }
        CommonRequestM.getPlayHistory(params, new IDataCallBack<ListModeBase<TrackM>>() {
            @Override
            public void onSuccess(ListModeBase<TrackM> object) {
                if (object != null && object.getList() != null) {

                    if (object.getList().indexOf(t) == -1) {
                        if (uiCallback != null) {
                            uiCallback.onError(-1, activity.getResources().getString(R.string.host_track_deleted));
                        }
                        return;
                    }

                    int pageId = object.getPageId();
                    int maxPageId = object.getMaxPageId();
                    int totalCount = object.getTotalCount();
                    params.put(DTransferConstants.PAGE, pageId + "");
                    params.put(DTransferConstants.TOTAL_PAGE, String.valueOf(maxPageId));
                    params.put(DTransferConstants.PRE_PAGE, String.valueOf(pageId - 1));

                    if (object.getList() != null) {
                        if (uiCallback != null) {
                            uiCallback.onSuccess();
                        }
                        int playIndex = object.getList().indexOf(t);
                        Logger.d("playHis", "skipCompleteSound " + skipCompleteSound
                                + ", playIndex: " + playIndex + ", listSize: " + object.getList().size());
                        if (skipCompleteSound && playIndex + 1 < object.getList().size()) {
                            int lastPos = XmPlayerManager.getInstance(activity).getHistoryPos(t.getDataId());
                            boolean trackPlayComplete = ToolUtil.isTrackPlayComplete(lastPos, t.getDuration());
                            Logger.d("playHis", "skipCompleteSound true, lastPos: " + lastPos
                                    + "， t.getDuration(): " + t.getDuration()
                                    + ", trackPlayComplete: " + trackPlayComplete);
                            if (trackPlayComplete) {
                                Logger.d("playHis", "trackPlayComplete playNext");
                                playIndex += 1;
                            }
                        }
                        if (dataCallbackWithDriveMode != null) {
                            dataCallbackWithDriveMode.getDataSuccess(ListModeBase.toCommonTrackList(object), playIndex);
                        }
                        if (needPlay) {
                            loadAndPlay(activity, ListModeBase.toCommonTrackList(object), playIndex, null, startPlayer);

                        } else {
                            XmPlayerManager.getInstance(activity).setPlayList(ListModeBase.toCommonTrackList(object), playIndex);
                        }
                    } else {
                        if (uiCallback != null) {
                            uiCallback.onError(-1, object.getMsg());
                        }
                    }
                } else {
                    if (uiCallback != null) {
                        uiCallback.onError(-1, activity.getResources().getString(R.string.host_network_error));
                    }
                    if (dataCallbackWithDriveMode != null) {
                        dataCallbackWithDriveMode.getDataFailed(-1, activity.getResources().getString(R.string.host_network_error));
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                SubordinatedAlbum album = t.getAlbum();
                if (album != null) {
                    List<Track> trackList = RouteServiceUtil.getDownloadService().getDownloadedTrackListInAlbum(album.getAlbumId());//downloader.getDownloadedTrackListInAlbum(album.getAlbumId());
                    DownloadTools.sortByDownloadTime(trackList);
                    if (trackList != null && trackList.size() > 0) {
                        for (Track track : trackList) {
                            track.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PLAY_HISTORY);
                        }
                        int index = trackList.indexOf(t);
                        if (index >= 0) {
                            PlayTools.playList(activity, trackList, index, null);
                            if (uiCallback != null) {
                                uiCallback.onError(-1, message + "");
                            }
                        }
                        return;
                    }
                }
                if (uiCallback != null) {
                    uiCallback.onError(code, message + "");
                }
            }
        });

    }

    public static void playPre(final Context ctx) {
        // 以下判断在TingLocalMediaService中的onPlayStart中
        XmPlayerManager.getInstance(ctx).playPre();
    }

    public static void playNext(final Context ctx) {
        XmPlayerManager.getInstance(ctx).playNext();
    }

    public static int getPlayCurrentPosition(Context context) {
        return XmPlayerManager.getInstance(context).getPlayCurrPositon();
    }

    @Deprecated
    // 需要传入具体原因，应该使用 playOrPause(Context context, int reason)
    public static void playOrPause(Context context) {
        playOrPause(context, PauseReason.NONE);
    }

    public static void playOrPause(Context context, int reason) {
        XmPlayerManager xpm = XmPlayerManager.getInstance(context);
        if (xpm.isPlaying()) {
            xpm.pause(reason);
        } else {
            PlayTools.play(context);
        }
    }

    @Deprecated
    // 需要传入具体原因，应该使用 pause(Context context, int reason)
    public static void pause(Context context) {
        pause(context, PauseReason.NONE);
    }

    public static void pause(Context context, int reason) {
        XmPlayerManager xpm = XmPlayerManager.getInstance(context);
        xpm.pause(reason);
    }

    public static void stop(Context context) {
        XmPlayerManager xpm = XmPlayerManager.getInstance(context);
        xpm.stop();
    }

    public static void setDLNAState(Context context, boolean isDLNAState) {
        XmPlayerManager xpm = XmPlayerManager.getInstance(context);
        xpm.setDLNAState(isDLNAState);
    }

    public static boolean isDLNAState(Context context) {
        XmPlayerManager xpm = XmPlayerManager.getInstance(context);
        return xpm.isDLNAState();
    }

    public static void needContinuePlay(Context context, boolean isContinuePlay) {
        XmPlayerManager xpm = XmPlayerManager.getInstance(context);
        xpm.needContinuePlay(isContinuePlay);
    }

    public static void setRecordModel(Context context, RecordModel model) {
        XmPlayerManager xpm = XmPlayerManager.getInstance(context);
        xpm.setRecordModel(model);
    }

    public static boolean isPlayCurrTrackById(Context context, long trackId) {
        PlayableModel curModel = XmPlayerManager.getInstance(context)
                .getCurrSound();
        return curModel != null && curModel.getDataId() == trackId;
    }

    //判断是否 播放的是当前的微课 直播语音条
    public static boolean isPlayCurrWeikeTrackById(Context context, String trackId) {
        PlayableModel curModel = XmPlayerManager.getInstance(context)
                .getCurrWeikeSound(true);
        return curModel != null && TextUtils.equals(trackId, curModel.weikeTrackId);
    }


    public static boolean isPlayCurrRadioById(Context context, long radioId) {
        PlayableModel curModel = XmPlayerManager.getInstance(context)
                .getCurrSound();

        if (curModel == null) {
            return false;
        }

        if (PlayableModel.KIND_SCHEDULE.equals(curModel.getKind())) {
            Schedule schedule = (Schedule) curModel;
            if (schedule.getRadioId() == radioId) {
                return true;
            }
        }

        if (PlayableModel.KIND_RADIO.equals(curModel.getKind())) {
            Radio radio = (Radio) curModel;
            if (radio.getDataId() == radioId) {
                return true;
            }
        }

        return curModel != null && curModel.getDataId() == radioId;
    }

    /**
     * !!!!!!!!!!!!! 您的 model 中如果有roomId, 请使用 playLiveAudioByRoomId
     *
     * @param activity 上下文
     * @param liveId   直播场次id
     * @deprecated 请使用带有showBack参数的方法
     */
    @Deprecated
    public static void playLiveAudioByLiveIdWithPlaySource(final FragmentActivity activity, final long liveId, final int playSource) {
        playLiveAudioByLiveIdWithPlaySourceInternal(activity, liveId, playSource);
    }

    /**
     * !!!!!!!!!!!!! 您的 model 中如果有roomId, 请使用 playLiveAudioByRoomId
     *
     * @param activity 上下文
     * @param liveId   直播场次id
     * @deprecated 请使用 playLiveAudioByLiveIdWithPlaySource,带有showBack参数的方法
     */
    @Deprecated
    public static void playLiveAudioByLiveId(final FragmentActivity activity, final long liveId) {
        playLiveAudioByLiveIdWithPlaySourceInternal(activity, liveId, 0);
    }

    private static void playLiveAudioByLiveIdWithPlaySourceInternal(final FragmentActivity activity, final long liveId, final int playSource) {
        if (ConstantsOpenSdk.isDebug) {
            DialogBuilder builder = new DialogBuilder(activity);
            builder.setCancelBtn("有roomId，去改改", new DialogBuilder.DialogCallback() {
                @Override
                public void onExecute() {

                }
            });
            builder.setOkBtn("没有RoomId,继续跳转", new DialogBuilder.DialogCallback() {
                @Override
                public void onExecute() {
                    try {
                        PlayCompleteRecommendManager.getInstance().finishRecommend();

                        StartRoomIntent room = new StartRoomIntent()
                                .setActivity(activity)
                                .setLiveId(liveId)
                                .setDisableScroll(false)
                                .setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO)
                                .setPlaySource(playSource);

                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction()
                                .startLiveRoom(room);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
            builder.setMessage(" 如果有 liveRoomId, 请使用 playLiveAudioByRoomId() 方法，传参 roomId ！！！！！！！");
            builder.showConfirm();
        } else {
            try {
                PlayCompleteRecommendManager.getInstance().finishRecommend();

                StartRoomIntent room = new StartRoomIntent()
                        .setActivity(activity)
                        .setDisableScroll(false)
                        .setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO)
                        .setPlaySource(playSource);

                Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction()
                        .startLiveRoom(room);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 根据直播间id，进入直播界面，可设置是否左右滑
     *
     * @param activity      上下文
     * @param roomId        直播间id
     * @param disableScroll 是否禁止左右滑
     * @param playSource    播放来源
     * @param showBack      非上下滑场景的跳转直播间，一律传true，展示返回上个直播间
     */
    public static void playLiveAudioByRoomIdWithPlaySource(final FragmentActivity activity, final long roomId, final boolean disableScroll, final int playSource, boolean showBack) {
        playLiveAudioByRoomIdWithPlaySourceInternal(activity, roomId, disableScroll, playSource, showBack);
    }

    public static void playLiveAudioByRoomIdWithPlaySource(final FragmentActivity activity, final long roomId, final boolean disableScroll, final int playSource, boolean showBack, boolean isOfficialSource, boolean autoChange, int itemId) {
        playLiveAudioByRoomIdWithPlaySourceInternal(activity, roomId, disableScroll, playSource, showBack, isOfficialSource, autoChange, itemId);
    }

    /**
     * 根据直播间id，进入直播界面
     *
     * @param activity   上下文
     * @param roomId     直播间id
     * @param playSource 播放来源
     * @deprecated 使用带有上面showBack参数的方法
     */
    @Deprecated
    public static void playLiveAudioByRoomIdWithPlaySource(final FragmentActivity activity, final long roomId, final int playSource) {
        playLiveAudioByRoomIdWithPlaySourceInternal(activity, roomId, false, playSource, false);
    }

    /**
     * 根据直播间id，进入直播界面，并且跳转后支持定位到具体功能（iting）
     *
     * @param activity
     * @param roomId
     * @param playSource
     * @param bundle     bundle参数：需要携带 bundle.putBoolean(ILiveFunctionAction.KEY_SHOW_BACK, showBack) 参数
     */
    public static void playLiveAudioByRoomIdWithPlaySource(final FragmentActivity activity, final long roomId, final int playSource, Bundle bundle) {
        playLiveAudioByRoomIdWithPlaySourceInternal(
                activity, roomId, 0, false, playSource, bundle
        );
    }

    /**
     * 根据直播间id，进入直播界面
     *
     * @param activity 上下文
     * @param roomId   直播间id
     * @deprecated 请使用 playLiveAudioByRoomIdWithPlaySource携带showBack参数
     */
    @Deprecated
    public static void playLiveAudioByRoomId(final FragmentActivity activity, final long roomId) {
        playLiveAudioByRoomIdWithPlaySourceInternal(activity, roomId, false, 0, false);
    }

    /**
     * @param showBack 非上下滑场景的跳转直播间，一律传true，展示返回上个直播间
     * @param activity
     * @param roomId
     */
    public static void playLiveAudioByRoomId(final FragmentActivity activity, final long roomId, boolean showBack) {
        playLiveAudioByRoomIdWithPlaySourceInternal(activity, roomId, false, 0, showBack);
    }

    public static void playLiveAudioByRoomId(final FragmentActivity activity, final long roomId, int playsource, boolean showBack) {
        playLiveAudioByRoomIdWithPlaySourceInternal(activity, roomId, false, playsource, showBack);
    }

    private static void playLiveAudioByRoomIdWithPlaySourceInternal(final FragmentActivity activity, final long roomId, final boolean disableScroll, final int playSource, final boolean showBack) {
        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    try {
                        PlayCompleteRecommendManager.getInstance().finishRecommend();
                        Bundle bundle = new Bundle();
                        bundle.putBoolean(ILiveFunctionAction.KEY_SHOW_BACK, showBack);

                        StartRoomIntent room = new StartRoomIntent()
                                .setActivity(activity)
                                .setBundle(bundle)
                                .setPlaySource(playSource)
                                .setBundle(bundle)
                                .setDisableScroll(disableScroll)
                                .setRoomId(roomId);
                        int mediaType = bundle.getInt(ILiveFunctionAction.KEY_LIVE_MEDIA_TYPE, BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO);
                        if (mediaType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO) {
                            room.setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO);
                        } else {
                            room.setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO);
                        }
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().startLiveRoom(room);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    CustomToast.showFailToast("直播模块加载失败");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }

    private static void playLiveAudioByRoomIdWithPlaySourceInternal(final FragmentActivity activity, final long roomId, final boolean disableScroll, final int playSource, final boolean showBack, final boolean isOfficialSource, boolean autoChange, int itemId) {
        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    try {
                        PlayCompleteRecommendManager.getInstance().finishRecommend();
                        Bundle bundle = new Bundle();
                        bundle.putBoolean(ILiveFunctionAction.KEY_SHOW_BACK, showBack);
                        bundle.putBoolean(ILiveFunctionAction.KEY_IS_OFFICIAL_SOURCE, isOfficialSource);
                        bundle.putBoolean(ILiveFunctionAction.KEY_AUTO_CHANGE, autoChange);
                        bundle.putInt(ILiveFunctionAction.KEY_ITEM_ID, itemId);

                        StartRoomIntent room = new StartRoomIntent()
                                .setActivity(activity)
                                .setBundle(bundle)
                                .setPlaySource(playSource)
                                .setBundle(bundle)
                                .setDisableScroll(disableScroll)
                                .setRoomId(roomId);
                        int mediaType = bundle.getInt(ILiveFunctionAction.KEY_LIVE_MEDIA_TYPE, BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO);
                        if (mediaType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO) {
                            room.setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO);
                        } else {
                            room.setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO);
                        }
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().startLiveRoom(room);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    CustomToast.showFailToast("直播模块加载失败");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }

    public static void playLiveAudioByRoomIdAndPlaySource(
            FragmentActivity activity,
            long roomId,
            long liveId,
            int playsource,
            boolean disableScroll,
            Bundle bundle
    ) {
        playLiveAudioByRoomIdWithPlaySourceInternal(
                activity, roomId, liveId, disableScroll, playsource, bundle
        );
    }

    private static void playLiveAudioByRoomIdWithPlaySourceInternal(
            final FragmentActivity activity,
            final long roomId,
            final long liveId,
            final boolean disableScroll,
            final int playSource,
            Bundle bundle
    ) {
        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    try {
                        PlayCompleteRecommendManager.getInstance().finishRecommend();

                        StartRoomIntent room = new StartRoomIntent()
                                .setActivity(activity)
                                .setBundle(bundle)
                                .setDisableScroll(disableScroll)
                                .setPlaySource(playSource)
                                .setRoomId(roomId)
                                .setLiveId(liveId);
                        int mediaType = bundle.getInt(ILiveFunctionAction.KEY_LIVE_MEDIA_TYPE, BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO);
                        if (mediaType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO) {
                            room.setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO);
                        } else {
                            room.setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO);
                        }
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().startLiveRoom(room);

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    CustomToast.showFailToast("直播模块加载失败");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }

    /**
     * 播放本地广播直播
     */
    public static void playRadio(final FragmentActivity activity,
                                 final Radio radio, final boolean startPlayer, final View fromView) {
        if (radio == null) {
            return;
        }

        NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
            @Override
            public void onOkCallBack() {
                if (startPlayer) {
                    XmPlayerManager.getInstance(activity).setPlayFragmentIsShowing(true);
                }
                PlayCompleteRecommendManager.getInstance().finishRecommend();
                XmPlayerManager.getInstance(activity).playActivityRadio(radio);
                checkToPlayFragmentAndSetPlayerType(activity, startPlayer, fromView, PlayerManager.ACTIVITY_LIVE_TAG);
            }

            @Override
            public void onCancleCallBack() {

            }
        }, false, DownloadTools.NORMAL_SOUND_TYPE);

    }

    public static void PlayLiveRadio(final Context activity,
                                     final Radio radio, final boolean startPlayer, final View fromView) {
        PlayLiveRadio(activity, radio, startPlayer, fromView, false);
    }

    public static void PlayLiveRadioFromHistoryFragment(final Context activity,
                                                        final Radio radio, final boolean startPlayer, final View fromView) {
        PlayLiveRadio(activity, radio, startPlayer, fromView, true);
    }

    /**
     * 播放直播电台
     */
    public static void PlayLiveRadio(final Context activity,
                                     final Radio radio, final boolean startPlayer, final View fromView, boolean noCheckIsPlayed) {
        if (radio == null || activity == null || radio.getDataId() == 0) {
            return;
        }

        PlayCompleteRecommendManager.getInstance().finishRecommend();
        NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
            @Override
            public void onOkCallBack() {
                isRequestRadioInfo = true;
                if (TextUtils.isEmpty(radio.getRate24AacUrl())
                        || TextUtils.isEmpty(radio.getRate24TsUrl())
                        || TextUtils.isEmpty(radio.getRate64AacUrl())
                        || TextUtils.isEmpty(radio.getRate64TsUrl())) {
                    Map<String, String> map = new HashMap<String, String>();
                    map.put("radioId", String.valueOf(radio.getDataId()));
                    CommonRequestM.getCurrentRadioProgram(map, new IDataCallBack<Program>() {
                        @Override
                        public void onSuccess(Program object) {
                            playLiveRadioFixForCloud(activity, radio, startPlayer, fromView, noCheckIsPlayed);
                        }

                        @Override
                        public void onError(int code, String message) {
                            playLiveRadioFixForCloud(activity, radio, startPlayer, fromView, noCheckIsPlayed);
                        }
                    }, radio);


                } else {
                    playLiveRadioFixForCloud(activity, radio, startPlayer, fromView, noCheckIsPlayed);
                }
            }

            @Override
            public void onCancleCallBack() {

            }
        }, false, DownloadTools.NORMAL_SOUND_TYPE);
    }

    private static void playLiveRadioFixForCloud(final Context activity,
                                                 final Radio radio, final boolean startPlayer, final View fromView, boolean noCheckIsPlayed) {

        if (checkHasLiveOnline(activity)) {
            return;
        }

        SharedPreferencesUtil sharedPreferencesUtil = SharedPreferencesUtil.getInstance(activity);
        try {
            sharedPreferencesUtil.saveString(
                    PreferenceConstantsInHost.TINGMAIN_KEY_PLAY_LAST_RADIO,
                    new Gson().toJson(radio));
        } catch (Exception e) {
            e.printStackTrace();
            if (ConstantsOpenSdk.isDebug) {
                throw new RuntimeException(e.toString());
            }
        }


        if (startPlayer) {
            XmPlayerManager.getInstance(activity).setPlayFragmentIsShowing(true);
        }
        if (!noCheckIsPlayed && isPlayCurrRadioById(activity, radio.getDataId())) {
            if (!XmPlayerManager.getInstance(activity).isPlaying()) {
                PlayTools.play(activity);
            }

            checkToPlayFragmentAndSetPlayerType(activity, startPlayer, fromView, PlayerManager.LIVE_TAG);
            return;
        }

        Map<String, String> params = new HashMap<>();
        params.put("radioId", radio.getDataId() + "");
        params.put("device", "android");
        params.put("statpage", "tab@发现_广播");
        String view = fromView + "";
        if (view.contains("recommend_radio1")) {
            params.put("statEvent", "pageview/radio@" + radio.getDataId());
            params.put("statmodule", "推荐电台");
            params.put("statposition", "1");
        }
        if (view.contains("recommend_radio2")) {
            params.put("statEvent", "pageview/radio@" + radio.getDataId());
            params.put("statmodule", "推荐电台");
            params.put("statposition", "2");
        }
        if (view.contains("recommend_radio3")) {
            params.put("statEvent", "pageview/radio@" + radio.getDataId());
            params.put("statmodule", "推荐电台");
            params.put("statposition", "3");
        }
        if (view.contains("top_radio1_holder")) {
            params.put("statEvent", "pageview/radio@" + radio.getDataId());
            params.put("statmodule", "排行榜");
            params.put("statposition", "1");
        }
        if (view.contains("top_radio2_holder")) {
            params.put("statEvent", "pageview/radio@" + radio.getDataId());
            params.put("statmodule", "排行榜");
            params.put("statposition", "2");
        }
        if (view.contains("top_radio3_holder")) {
            params.put("statEvent", "pageview/radio@" + radio.getDataId());
            params.put("statmodule", "排行榜");
            params.put("statposition", "3");
        }


        CommonRequestM.getProgressSchedules(params,
                new IDataCallBack<Map<String, List<Schedule>>>() {
                    @Override
                    public void onSuccess(
                            final Map<String, List<Schedule>> scheduleMap) {

                        // 错误回复
                        if (scheduleMap.containsKey("ret")
                                || scheduleMap.isEmpty()
                                || !scheduleMap.containsKey(AppConstants.RADIO_TODAY)) {

                            Schedule schedule = ModelUtil
                                    .radioToSchedule(radio);
                            if (schedule == null && activity != null) {
                                ToastCompat.makeText(activity,
                                        R.string.host_net_error,
                                        Toast.LENGTH_SHORT).show();
                                return;
                            }

                            List<Schedule> schedules = new ArrayList<>();
                            schedules.add(schedule);

                            PlayTools.playSchedule(activity, schedules, -1);
                            isRequestRadioInfo = false;
                            return;
                        }

                        List<Schedule> schedules = new ArrayList<>();

                        for (int i = 0; i < AppConstants.DAY_TYPES.length; i++) {
                            if (scheduleMap
                                    .containsKey(AppConstants.DAY_TYPES[i])) {
                                schedules.addAll(scheduleMap
                                        .get(AppConstants.DAY_TYPES[i]));
                            }
                        }
                        for (int i = 0; i < schedules.size(); i++) {
                            if (schedules.get(i) == null) {
                                continue;
                            }
                            if (radio.getChannelId() != 0) {
                                schedules.get(i).setChannelId(radio.getChannelId());
                                schedules.get(i).setChannelName(radio.getChannelName());
                                schedules.get(i).setChannelPic(radio.getChannelPic());
                            }
                        }

                        PlayTools.playSchedule(activity, schedules, -1);
                        isRequestRadioInfo = false;
                        checkToPlayFragmentAndSetPlayerType(activity, startPlayer, fromView, PlayerManager.LIVE_TAG);
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (activity instanceof MainActivity) {
                            CustomToast.showFailToast(message);
                        }
                    }
                }, radio);

    }

    private static void playSchedule(final Context context,
                                     final List<Schedule> schedules, final int index,
                                     boolean willShowDialog) {
        if (schedules == null || schedules.isEmpty()) {
            return;
        }

        PlayCompleteRecommendManager.getInstance().finishRecommend();
        if (willShowDialog) {
            NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
                @Override
                public void onOkCallBack() {
                    XmPlayerManager.getInstance(context).playSchedule(
                            schedules, index);
                }

                @Override
                public void onCancleCallBack() {

                }
            }, false, DownloadTools.NORMAL_SOUND_TYPE);
        } else {
            XmPlayerManager.getInstance(context).playSchedule(schedules, index);
        }
    }

    /**
     * playSchedule:(设置直播播放排期表并且播放) -1 时直接播放排期表中的当前直播
     * 该方法为了在直播播放页实现点击“上一首”播放排期表中上一首回听
     */
    public static void playSchedule(final Context context,
                                    final List<Schedule> schedules, final int index) {
        playSchedule(context, schedules, index, false);
    }

    /**
     * 通过传递一个Track的Id进行播放
     */
    public static void gotoPlayWithoutUrl(final Track track,
                                          final Context actContext, final boolean isShowPlayActivity,
                                          final View fromView) {
        if (track == null) {
            return;
        }
        UserTrackCookie.getInstance().setXmPlayResource();
        final MyProgressDialog dialog = new MyProgressDialog(actContext);
        dialog.setIndeterminate(true);
        dialog.setCancelable(true);
        dialog.setMessage("加载声音详情中...");
        dialog.delayShow();

        Map<String, String> params = new HashMap<>();
        params.put("device", "android");
        params.put("trackId", track.getDataId() + "");
        CommonRequestM.getTrackInfoDetail(params, new IDataCallBack<TrackM>() {
            @Override
            public void onSuccess(TrackM object) {
                if (null != dialog) {
                    dialog.dismiss();
                }
                object.setPlaySource(track.getPlaySource());
                PlayCompleteRecommendManager.getInstance().finishRecommend();
                PlayTools.playTrack(actContext, object, isShowPlayActivity,
                        fromView);
            }

            @Override
            public void onError(int code, String message) {
                if (null != dialog) {
                    dialog.dismiss();
                }
            }
        });
    }

    /**
     * 电台点击小的播放按钮的时候
     */
    public static void LivePlayBtnToClick(FragmentActivity activity, Radio radio,
                                          View playBtn) {
        if (activity == null || radio == null) {
            return;
        }

        PlayCompleteRecommendManager.getInstance().finishRecommend();
        boolean isplaying = XmPlayerManager.getInstance(activity)
                .isPlaying();

        if (isPlayCurrRadioById(activity, radio.getDataId())) {
            if (isplaying) {
                XmPlayerManager.getInstance(activity).pause(PauseReason.Business.RadioLivePlayBtnClick);
            } else {
                play(activity);
            }
        } else {

            if (PlayableModel.KIND_SCHEDULE.equals(radio.getKind())) {
                PlayLiveRadio(activity, radio, false, playBtn);
            } else if (PlayableModel.KIND_RADIO.equals(radio.getKind())) {
                if (radio.isActivityLive()) {
                    //活动直播已废弃
                } else {
                    PlayLiveRadio(activity, radio, false, playBtn);
                }
            }
        }
    }

    public static void updateTrackAuthorizedByAlbumIdsAndPlay(Context mContext, List<Long> albumIds) {
        UserTrackCookie.getInstance().setXmPlayResource();
        int index = XmPlayerManager.getInstance(mContext).getCurrentIndex();
        CommonTrackList commonTrackList = XmPlayerManager.getInstance(mContext).getCommonTrackList();
        if (commonTrackList != null && commonTrackList.getTracks() != null && !commonTrackList.getTracks().isEmpty()) {
            List<Track> tracks = commonTrackList.getTracks();
            int i = 0;
            boolean willPlay = false;
            for (Track track : tracks) {
                if (track.getAlbum() != null) {
                    if (albumIds.contains(track.getAlbum().getAlbumId())) {
                        if (index == i) {
                            willPlay = true;
                        }
                        track.setAuthorized(true);
                    }
                }
                i++;
            }
            PlayCompleteRecommendManager.getInstance().finishRecommend();
            if (willPlay) {
                PlayTools.playCommonList(mContext, commonTrackList, index, false, null);
            } else {
                XmPlayerManager.getInstance(mContext).setPlayList(commonTrackList, index);
            }
        }
    }

    public static void updateTrackAuthorizedByTracksAndPlay(Context mContext, List<Track> tracks) {
        if (tracks != null) {
            List<Long> trackIds = new ArrayList<>();
            for (Track track : tracks) {
                if (track != null) {
                    trackIds.add(track.getDataId());
                }
            }
            updateTrackAuthorizedByTrackIdsAndPlay(mContext, trackIds);
        }
    }

    public static void updateTrackAuthorizedByTrackIdsAndPlay(Context mContext, List<Long> trackIds) {
        UserTrackCookie.getInstance().setXmPlayResource();
        int index = XmPlayerManager.getInstance(mContext).getCurrentIndex();
        CommonTrackList commonTrackList = XmPlayerManager.getInstance(mContext).getCommonTrackList();
        if (commonTrackList != null && commonTrackList.getTracks() != null && !commonTrackList.getTracks().isEmpty()) {
            List<Track> tracks = commonTrackList.getTracks();
            int i = 0;
            boolean willPlay = false;
            for (Track track : tracks) {
                if (track == null) {
                    continue;
                }

                if (trackIds.contains(track.getDataId())) {
                    if (index == i) {
                        willPlay = true;
                    }
                    track.setAuthorized(true);
                }
                i++;
            }
            PlayCompleteRecommendManager.getInstance().finishRecommend();
            if (willPlay) {
                PlayTools.playCommonList(mContext, commonTrackList, index, false, null);
            } else {
                XmPlayerManager.getInstance(mContext).setPlayList(commonTrackList, index);
            }
        }
    }

    /*
     * 播放时可以从列表选择这条声音所属专辑的其他声音
     */
    public static void playTrackByCommonList(final Context context, long trackId, final int playSource, final View view) {
        playTrackByCommonList(context, trackId, playSource, view, true);
    }


    /*
     * 播放时可以从列表选择这条声音所属专辑的其他声音
     */
    public static void playTrackByCommonList(final Context context, long trackId, final int playSource,
                                             final View view, final boolean startPlayer) {
        playTrackByCommonList(context, trackId, playSource, view, startPlayer, null);
    }

    public static void playTrackByCommonList(final Context context, long trackId, final int playSource,
                                             final View view, final boolean startPlayer, IDataCallBack callBack) {
        playTrackByCommonList(context, trackId, playSource, view, startPlayer, callBack, SharedConstant.CHANNEL_UNKNOWN);
    }

    public static void playTrackByCommonList(final Context context, long trackId, final int playSource,
                                             final View view, final boolean startPlayer, IDataCallBack callBack, final int channel) {
        playTrackByCommonList(context, trackId, playSource, view, startPlayer, callBack, channel, true);
    }

    public static void playTrackByCommonList(final Context context, long trackId, final int playSource,
                                             final View view, final boolean startPlayer, IDataCallBack callBack,
                                             final int channel, final boolean willPlay) {
        playTrackByCommonList(context, trackId, playSource, view, startPlayer, callBack, channel, willPlay, null);
    }

    /*
     * 播放时可以从列表选择这条声音所属专辑的其他声音
     */
    public static void playTrackByCommonList(final Context context, long trackId, final int playSource,
                                             final View view, final boolean startPlayer, IDataCallBack callBack,
                                             final int channel, final boolean willPlay, IDataCallbackWithDriveMode dataCallback) {
        if (trackId <= 0) {
            return;
        }
        Logger.logToFile("XiaoaiControl == playTrackByCommonList");
        Map<String, String> specificParams = new HashMap<>();
        specificParams.put("device", "android");
        specificParams.put("trackId", trackId + "");
        specificParams.put("extNeedType", "1");
        CommonRequestM.getTrackInfoDetail(specificParams, new IDataCallBack<TrackM>() {
            @Override
            public void onSuccess(TrackM t) {
                Logger.logToFile("XiaoaiControl == playTrackByCommonList 1");
                if (callBack != null) {
                    callBack.onSuccess(t);
                }
                PlayCompleteRecommendManager.getInstance().finishRecommend();
                if (t != null && t.getType() == Track.TYPE_DUBSHOW) {
                    Bundle bundle = new Bundle();
                    bundle.putLong(BundleKeyConstants.KEY_TRACK_ID, t.getDataId());
                    checkToDubShowPPTPlayFragment(context, bundle, startPlayer, view);
                    Logger.logToFile("XiaoaiControl == playTrackByCommonList 2");
                } else if (t != null && t.getType() == Track.TYPE_TOPIC) {
                    PlayTools.goPlayByTrackId(context, t.getDataId(), view,
                            ConstantsOpenSdk.PLAY_FROM_OTHER, startPlayer, false);
                    Logger.logToFile("XiaoaiControl == playTrackByCommonList 3");
                } else if (t != null && t.isSketchVideoTrack()) {
                    PlayTools.playMiniDrama(context, t.getDataId());
                    Logger.logToFile("XiaoaiControl == playTrackByCommonList 3.2");
                } else {
                    Logger.logToFile("XiaoaiControl == playTrackByCommonList 4");
                    PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("4", "after getTrackInfoDetail");
                    playTrackHistoy(context, t, view, playSource, startPlayer, null, channel, willPlay, dataCallback);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (callBack != null) {
                    callBack.onError(code, message);
                }
                PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("play getTrackInfoDetail error:" + message);

                Logger.logToFile("XiaoaiControl == playTrackByCommonList 5");
                message = TextUtils.isEmpty(message) ? "声音不存在" : message;
                CustomToast.showFailToast(message);
            }
        });
    }

    /**
     * 播放声音，会自动拉取声音所在专辑的声音列表
     *
     * @param context     Context
     * @param track       请注意需要track.getAlbum不为空，且有albumId
     * @param startPlayer 是否启动播放页
     */
    public static void playTrackWithAlbum(Context context, @NonNull Track track, boolean startPlayer) {
        playTrackWithAlbum(context, track, startPlayer, true);
    }

    public static void playTrackWithAlbum(Context context, @NonNull Track track, boolean startPlayer, Bundle playBundle) {
        playTrackWithAlbum(context, track, startPlayer, true, playBundle);
    }


    public static void playTrackWithAlbum(Context context, long trackId, boolean startPlayer, boolean willPlay, int playSource) {
        playTrackWithAlbum(context, trackId, startPlayer, willPlay, playSource, true);
    }

    /**
     * 根据声音id播放声音，会自动拉取声音所在专辑的声音列表
     *
     * @param context                    Context
     * @param trackId                    声音id
     * @param startPlayer                是否启动播放页
     * @param willPlay                   是否开始播放
     * @param isNeedLoadPlayCompleteData 是否需要加载完播数据
     */
    public static void playTrackWithAlbum(Context context, long trackId, boolean startPlayer,
                                          boolean willPlay, int playSource, boolean isNeedLoadPlayCompleteData) {
        if (trackId <= 0) {
            return;
        }
        Track track = new Track();
        track.setDataId(trackId);
        track.setKind(PlayableModel.KIND_TRACK);
        track.setPlaySource(playSource);
        track.setNeedLoadPlayCompleteData(isNeedLoadPlayCompleteData);
        playTrackWithAlbum(context, track, startPlayer, willPlay);
    }


    /**
     * 根据声音id播放声音，同时并发获取输入的 trackIdList 信息
     *
     * @param context     Context
     * @param trackId     声音id
     * @param trackIdList 剩余的声音ID列表
     */
    public static void playTrackAndGetTrackListDetail(Context context, long trackId, String trackIdList) {
        if (trackId <= 0) {
            return;
        }
        Track track = new Track();
        track.setDataId(trackId);
        track.setKind(PlayableModel.KIND_TRACK);
        track.setAuthorized(true);

        CommonTrackList<Track> commonTrackList = new CommonTrackList<>();
        List<Track> trackList = new ArrayList<>();
        trackList.add(track);
        commonTrackList.setTracks(trackList);
        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.LOAD_PLAY_LIST_BY_TRACK_ID, "true");
        params.put(DTransferConstants.LOAD_PLAY_LIST_BY_TRACK_ID_LIST, trackIdList);

        Logger.logToFile("PlayTools playTrackAndGetTrackListDetail: " + params + ",track=" + TrackUtil.trackToStr(track));

        commonTrackList.setParams(params);
        playCommonList(context, commonTrackList, 0, true, null,
                SharedConstant.CHANNEL_UNKNOWN, true, null);
    }

    /**
     * 根据声音id播放声音，会自动拉取声音所在专辑的声音列表
     *
     * @param context                    Context
     * @param track                      声音
     * @param startPlayer                是否启动播放页
     * @param willPlay                   是否开始播放
     * @param isNeedLoadPlayCompleteData 是否需要加载完播数据
     */
    public static void playTrackWithAlbum(Context context, Track track, boolean startPlayer,
                                          boolean willPlay, int playSource, boolean isNeedLoadPlayCompleteData) {
        if (track == null || track.getDataId() <= 0) {
            return;
        }
        track.setKind(PlayableModel.KIND_TRACK);
        track.setPlaySource(playSource);
        track.setNeedLoadPlayCompleteData(isNeedLoadPlayCompleteData);
        playTrackWithAlbum(context, track, startPlayer, willPlay);
    }

    /**
     * 播放声音，会自动拉取声音所在专辑的声音列表
     *
     * @param context     Context
     * @param track       请注意需要track.getAlbum不为空，且有albumId
     * @param startPlayer 是否启动播放页
     * @param willPlay    是否开始播放
     */
    public static void playTrackWithAlbum(Context context, @NonNull Track track, boolean startPlayer, boolean willPlay) {
        playTrackWithAlbum(context, track, startPlayer, willPlay, null);
    }

    /**
     * 播放声音，会自动拉取声音所在专辑的声音列表
     *
     * @param context     Context
     * @param track       请注意需要track.getAlbum不为空，且有albumId
     * @param startPlayer 是否启动播放页
     * @param willPlay    是否开始播放
     */
    public static void playTrackWithAlbum(Context context, @NonNull Track track, boolean startPlayer, boolean willPlay, Bundle playBundle) {
        playTrackWithAlbum(context, track, startPlayer, willPlay, playBundle, false, false);
    }

    /**
     * 播放声音，会自动拉取声音所在专辑的声音列表
     *
     * @param context      Context
     * @param track        请注意需要track.getAlbum不为空，且有albumId
     * @param startPlayer  是否启动播放页
     * @param willPlay     是否开始播放
     * @param useAlbumSort 是否使用专辑列表顺序开播，配合isAlbumAsc使用
     * @param isAlbumAsc   专辑列表顺序
     */
    public static void playTrackWithAlbum(Context context, @NonNull Track track, boolean startPlayer, boolean willPlay, Bundle playBundle, boolean useAlbumSort, boolean isAlbumAsc) {
        if (track == null) {
            return;
        }
        CommonTrackList<Track> commonTrackList = new CommonTrackList<>();
        List<Track> trackList = new ArrayList<>();
        trackList.add(track);
        commonTrackList.setTracks(trackList);
        Map<String, String> params;
        if (track.isRecommendSound()) {
            long albumId = 0;
            if (track != null && track.getAlbum() != null) {
                albumId = track.getAlbum().getAlbumId();
            }
            params = PlayListUtilForMainKt.createCommonTrackListParams(track.getDataId(), albumId, track.isInHotPool());
        } else {
            params = new HashMap<>();
            params.put(DTransferConstants.TRACK_BASE_URL, UrlConstants.getInstanse().getAlbumPlayList());
            params.put(DTransferConstants.LOAD_PLAY_LIST_BY_TRACK_ID, "true");
            if (track.getAlbum() != null) {
                long albumId = track.getAlbum().getAlbumId();
                params.put(DTransferConstants.ALBUMID, String.valueOf(albumId));
                if (useAlbumSort) {
                    params.put(DTransferConstants.USE_ALBUM_SORT, "true");
                    params.put(DTransferConstants.LOCAL_IS_ASC, String.valueOf(isAlbumAsc));
                } else {
                    IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
                    int order = historyManager != null ? historyManager.getAlbumSortByAlbumId(albumId) : IHistoryManagerForMain.ASC_ORDER;
                    boolean isAsc = (order == IHistoryManagerForMain.ASC_ORDER);
                    params.put(DTransferConstants.LOCAL_IS_ASC, String.valueOf(isAsc));
                }
            }
        }
        Logger.logToFile("PlayTools playTrackWithAlbum: " + params + ",track=" + TrackUtil.trackToStr(track));

        commonTrackList.setParams(params);
        playCommonList(context, commonTrackList, 0, startPlayer, null, SharedConstant.CHANNEL_UNKNOWN, willPlay, playBundle);
    }

    /**
     * 播放声音，会自动拉取声音所在专辑的声音列表
     *
     * @param context     Context
     * @param track       请注意需要track.getAlbum不为空，且有albumId
     * @param isAsc       请求列表的排序
     * @param startPlayer 是否启动播放页
     * @param willPlay    是否开始播放
     */
    public static void playTrackWithAlbumAndOrder(Context context, @NonNull Track track, boolean isAsc, boolean startPlayer, boolean willPlay) {
        if (track == null) {
            return;
        }
        CommonTrackList<Track> commonTrackList = new CommonTrackList<>();
        List<Track> trackList = new ArrayList<>();
        trackList.add(track);
        commonTrackList.setTracks(trackList);
        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.TRACK_BASE_URL, UrlConstants.getInstanse().getAlbumPlayList());
        params.put(DTransferConstants.LOAD_PLAY_LIST_BY_TRACK_ID, "true");
        if (track.getAlbum() != null) {
            long albumId = track.getAlbum().getAlbumId();
            params.put(DTransferConstants.ALBUMID, String.valueOf(albumId));
            params.put(DTransferConstants.LOCAL_IS_ASC, String.valueOf(isAsc));
        }

        commonTrackList.setParams(params);
        playCommonList(context, commonTrackList, 0, startPlayer, null, SharedConstant.CHANNEL_UNKNOWN, willPlay);
    }

    public static void playTrainingCampTrack(Context context, long trackId, long campId, boolean startPlayer, boolean willPlay) {
        Track track = new Track();
        track.setDataId(trackId);
        track.setKind(PlayableModel.KIND_TRACK);
        CommonTrackList<Track> commonTrackList = new CommonTrackList<>();
        List<Track> trackList = new ArrayList<>();
        trackList.add(track);
        commonTrackList.setTracks(trackList);
        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.TRACK_BASE_URL, UrlConstants.getInstanse().getTrainingCampPlayList());
        params.put(DTransferConstants.LOAD_PLAY_LIST_BY_TRACK_ID, "true");
        params.put(DTransferConstants.CAMPID, String.valueOf(campId));
        IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
        int order = historyManager != null ? historyManager.getAlbumSortByAlbumId(campId) : IHistoryManagerForMain.ASC_ORDER;
        boolean isAsc = (order == IHistoryManagerForMain.ASC_ORDER);
        params.put(DTransferConstants.LOCAL_IS_ASC, String.valueOf(isAsc));

        commonTrackList.setParams(params);
        playCommonList(context, commonTrackList, 0, startPlayer, null, SharedConstant.CHANNEL_UNKNOWN, willPlay);
    }

    public static void playTrackHistoy(final Context context, long trackId, long albumId, View view, int playSource, boolean startPlayer) {
        if (trackId <= 0 || albumId <= 0) {
            return;
        }
        PlayCompleteRecommendManager.getInstance().finishRecommend();
        Track track = new Track();
        track.setKind(PlayableModel.KIND_TRACK);
        track.setDataId(trackId);
        SubordinatedAlbum album = new SubordinatedAlbum();
        album.setAlbumId(albumId);
        track.setAlbum(album);
        playTrackHistoy(context, track, view, playSource, startPlayer);
    }


    public static void playTrackHistoy(final Context context, final Track t, final View view, final int playSource, final boolean startPlayer) {
        playTrackHistoy(context, t, view, playSource, startPlayer, null);
    }

    public static void playTrackHistoy(final Context context, final Track t, final View view, final int playSource, final boolean startPlayer, IDataCallBack callBack) {
        playTrackHistoy(context, t, view, playSource, startPlayer, callBack, SharedConstant.CHANNEL_UNKNOWN);
    }

    public static void playTrackHistoy(final Context context, final Track t, final View view, final int playSource, final boolean startPlayer, IDataCallBack callBack, final int channel) {
        playTrackHistoy(context, t, view, playSource, startPlayer, callBack, channel, true);
    }

    public static void playTrackHistoy(final Context context, final Track t, final View view, final int playSource,
                                       final boolean startPlayer, IDataCallBack callBack, final int channel, final boolean willPlay) {
        playTrackHistoy(context, t, view, playSource, startPlayer, callBack, channel, willPlay, null);
    }

    public static void playTrackHistoy(final Context context, final Track t, final View view, final int playSource,
                                       final boolean startPlayer, IDataCallBack callBack, final int channel,
                                       final boolean willPlay, final IDataCallbackWithDriveMode dataCallback) {
        if (t == null) {
            return;
        }
        final HashMap<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, t.getAlbum() != null ? t.getAlbum()
                .getAlbumId() + "" : "0");
        params.put(HttpParamsConstants.PARAM_TRACK_ID, t.getDataId() + "");
//        params.put(HttpParamsConstants.PARAM_ASC, "true");
        IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
        int order = historyManager != null ? historyManager.getAlbumSortByAlbumId(
                t.getAlbum() != null ? t.getAlbum().getAlbumId() : 0) : IHistoryManagerForMain.ASC_ORDER;
        //asc为true时貌似时倒序，和示意有点不符,服务端默认是倒序
        final boolean isAsc = (order == IHistoryManagerForMain.ASC_ORDER);
        params.put(HttpParamsConstants.PARAM_ASC, String.valueOf(isAsc));

        CommonRequestM.getPlayHistory(params,
                new IDataCallBack<ListModeBase<TrackM>>() {

                    @Override
                    public void onSuccess(ListModeBase<TrackM> object) {
                        PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("5", "after playTrackHistory");
                        Logger.logToFile("XiaoaiControl == playTrackHistoy 1 " + object);
                        if (object != null) {
                            Logger.logToFile("XiaoaiControl == playTrackHistoy 2 ");
                            if (object.getList() != null) {
                                Logger.logToFile("XiaoaiControl == playTrackHistoy 3 ");
                                int size = object.getList().size();
                                if (size < 1 || object.getList().indexOf(t) < 0 || object.getList().indexOf(t) >= size) {
                                    Logger.logToFile("XiaoaiControl == playTrackHistoy 4 ");
                                    playTrack(context, t, startPlayer, view, willPlay);
                                    if (dataCallback != null) {
                                        dataCallback.getDataSuccess(ListModeBase.toCommonTrackList(object), -1);
                                        if (callBack instanceof IDataCallBackWithIndex) {
                                            ((IDataCallBackWithIndex) callBack).onSuccess(object, 0, t);
                                        }
                                    }
                                } else {
                                    Logger.logToFile("XiaoaiControl == playTrackHistoy 5 ");
                                    int index = object.getList().indexOf(t);
                                    Track track = object.getList().get(index);
                                    if (t.isNeedSeekToDesPos() && t.getStartPlayPos() >= 0) {
                                        track.setNeedSeekToDesPos(true);
                                        track.setStartPlayPos(t.getStartPlayPos());
                                    }
                                    if (t.isNeedPauseAtDesPos() && t.getEndPlayPos() > 0) {
                                        track.setNeedPauseAtDesPos(true);
                                        track.setEndPlayPos(t.getEndPlayPos());
                                    }
                                    if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_PLAYSORT_OPTIMIZATION, false)) {
                                        PlayTools.playTrackWithAlbum(context, t, startPlayer, willPlay);
                                    } else {
                                        loadAndPlay(context,
                                                ListModeBase.toCommonTrackList(object),
                                                object.getList().indexOf(t), view, playSource, startPlayer, channel, willPlay);
                                    }
                                    if (dataCallback != null) {
                                        dataCallback.getDataSuccess(ListModeBase.toCommonTrackList(object), object.getList().indexOf(t));
                                    }
                                    if (callBack instanceof IDataCallBackWithIndex) {
                                        ((IDataCallBackWithIndex) callBack).onSuccess(object, object.getList().indexOf(t), t);
                                    }
                                }

                                if (callBack != null) {
                                    callBack.onSuccess(object);
                                }
                            } else {
                                if (callBack != null) {
                                    callBack.onError(object.getRet(), object.getMsg());
                                }
                                CustomToast.showFailToast(object.getMsg());
                            }
                        } else {
                            if (callBack != null) {
                                callBack.onError(BaseCall.ERROR_CODE_DEFALUT, "服务端异常");
                            }
                            if (dataCallback != null) {
                                dataCallback.getDataFailed(BaseCall.ERROR_CODE_DEFALUT, "服务端异常");
                            }
                            CustomToast.showFailToast(R.string.host_network_error);
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        Logger.logToFile("XiaoaiControl == playTrackHistoy 2");
                        if (callBack != null) {
                            callBack.onError(code, message);
                        }
                        PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("play getPlayHistory error:" + message);

                        if (TextUtils.isEmpty(message)) {
                            CustomToast.showFailToast(R.string.host_network_error);
                        } else {
                            CustomToast.showFailToast(message);
                        }
                    }
                });
    }

    /**
     * 判断是否是助眠
     *
     * @param playableModel
     * @return
     */
    public static boolean isPlayModelSleepMode(PlayableModel playableModel) {
        return playableModel != null && PlayableModel.KIND_MODE_SLEEP.equals(playableModel.getKind());
    }

    /**
     * 判断是否是个人直播
     *
     * @param playableModel
     * @return
     */
    public static boolean isPlayModelLive(PlayableModel playableModel) {
        return getAnchorLiveId(playableModel) > 0;
    }

    /**
     * 判断是否是娱乐厅直播
     *
     * @param playableModel
     */
    public static boolean isPlayModelEntLive(PlayableModel playableModel) {
        return playableModel != null && PlayableModel.KIND_ENT_FLY.equals(playableModel.getKind());
    }

    /**
     * 判断是否是课程直播
     *
     * @param playableModel
     */
    public static boolean isPlayModelCourseLive(PlayableModel playableModel) {
        return playableModel != null && PlayableModel.KIND_LIVE_COURSE.equals(playableModel.getKind());
    }


    /**
     * 判断是否是MYCLUB直播
     *
     * @param playableModel
     */
    public static boolean isPlayModelMyclubLive(PlayableModel playableModel) {
        return playableModel != null && PlayableModel.KIND_MYCLUB_FLV.equals(playableModel.getKind());
    }

    /**
     * 判断是否是MYCLUB回放
     *
     * @param playableModel
     */
    public static boolean isPlayModelMyclubReplay(PlayableModel playableModel) {
        return playableModel != null && PlayableModel.KIND_MYCLUB_REPLAY_TRACK.equals(playableModel.getKind());
    }

    /**
     * 获取个播/课程直播直播场次ID
     *
     * @param playableModel
     */
    public static long getLiveId(PlayableModel playableModel) {
        if (isCourseLiveOrAnchorLiveMode(playableModel)) {
            return playableModel.getDataId();
        }
        return -1;
    }

    /**
     * 获取个播直播ID
     *
     * @param playableModel
     */
    public static long getAnchorLiveId(PlayableModel playableModel) {
        if (playableModel != null && PlayableModel.KIND_LIVE_FLV.equals(playableModel.getKind())) {
            return playableModel.getDataId();
        }
        return -1;
    }


    /**
     * 获取个人直播的roomId
     *
     * @return
     */
    public static long getLiveRoomId(PlayableModel playableModel) {
        if (playableModel == null) {
            return -1;
        }

        boolean isLiveFlv = isLiveMode(playableModel);
        if (isLiveFlv && playableModel instanceof Track) {
            Track track = (Track) playableModel;
            return track.getLiveRoomId();
        }
        return -1;
    }

    /**
     * 获取个人直播的主播id
     *
     * @param playableModel
     * @return
     */
    public static long getLiveAnchorUid(PlayableModel playableModel) {
        if (playableModel == null) {
            return -1;
        }

        boolean isLiveFlv = isLiveMode(playableModel);
        if (isLiveFlv && playableModel instanceof Track) {
            Track track = (Track) playableModel;
            return track.getAnchorUid();
        }
        return -1;
    }

    /**
     * 获取直播类型
     *
     * @param playableModel
     * @return
     */
    public static int getLiveType(PlayableModel playableModel) {
        if (playableModel == null) {
            return -1;
        }

        boolean isLiveFlv = isLiveMode(playableModel);
        if (isLiveFlv && playableModel instanceof Track) {
            Track track = (Track) playableModel;
            return track.getLiveType();
        }
        return -1;
    }

    /**
     * 获取课程直播场次id
     *
     * @param playableModel
     * @return
     */
    public static long getCourseLiveId(PlayableModel playableModel) {
        if (playableModel != null && PlayableModel.KIND_LIVE_COURSE.equals(playableModel.getKind())) {
            return playableModel.getDataId();
        }
        return -1;
    }

    public static boolean isLiveMode(PlayableModel playableModel) {
        if (playableModel == null) {
            return false;
        }
        return PlayableModel.KIND_LIVE_FLV.equals(playableModel.getKind())
                || PlayableModel.KIND_ENT_FLY.equals(playableModel.getKind())
                || PlayableModel.KIND_LIVE_COURSE.equals(playableModel.getKind());
    }

    public static boolean isPlayFromTingList() {
        CommonTrackList<Track> commonTrackList = XmPlayerManager.getInstance(ToolUtil.getCtx()).getCommonTrackList();
        if (commonTrackList == null) {
            return false;
        }
        if (commonTrackList.getParams() != null
                && commonTrackList.getParams().size() > 0
                && commonTrackList.getParams().containsKey(DTransferConstants.PARAM_PLAY_LIST_FROM)) {
            String sourceFrom = commonTrackList.getParams().get(DTransferConstants.PARAM_PLAY_LIST_FROM);
            if (DTransferConstants.PARAM_PLAY_LIST_FROM.equals(sourceFrom)) {
                return true;
            }
        }
        List<Track> playList = commonTrackList.getTracks();
        if (playList == null) {
            return false;
        }
        for (Track track : playList) {
            if (track == null || track.isIgnorePlaySource()) {
                continue;
            }
            int tingListOpType = track.getTingListOpType();
            boolean fromTingList = tingListOpType == AppConstants.TYPE_TINGLIST_ALBUM
                    || tingListOpType == AppConstants.TYPE_TINGLIST_TRACK;
            if (!fromTingList) {
                return false;
            }
        }
        return true;
    }

    public static boolean isCourseLiveOrAnchorLiveMode(PlayableModel playableModel) {
        if (playableModel == null) {
            return false;
        }
        return PlayableModel.KIND_LIVE_FLV.equals(playableModel.getKind())
                || PlayableModel.KIND_LIVE_COURSE.equals(playableModel.getKind());
    }

    /**
     * 判断是否广播或广播回听
     *
     * @param playableModel
     * @return
     */
    public static boolean isPlayModeRadioOrSchedule(PlayableModel playableModel) {
        if (playableModel == null) {
            return false;
        }

        return PlayableModel.KIND_RADIO.equals(playableModel.getKind())
                || PlayableModel.KIND_SCHEDULE.equals(playableModel.getKind());

    }

    /**
     * 获取 Radio 的 ActivityId
     *
     * @param playableModel
     * @return
     */
    public static long getActivityId(PlayableModel playableModel) {
        if (playableModel != null && playableModel instanceof Radio) {
            Radio radio = (Radio) playableModel;
            if (radio.isActivityLive())
                return radio.getActivityId();
        }
        return -1;
    }

    private static void loadAndPlay(Context context, CommonTrackList<TrackM> data, int position,
                                    View view, int playSource, boolean startPlayer) {
        loadAndPlay(context, data, position, view, playSource, startPlayer, SharedConstant.CHANNEL_UNKNOWN);
    }

    private static void loadAndPlay(Context context, CommonTrackList<TrackM> data, int position,
                                    View view, int playSource, boolean startPlayer, int channel) {
        loadAndPlay(context, data, position, view, playSource, startPlayer, channel, true);
    }

    /**
     * 加载播放历史音频
     */
    private static void loadAndPlay(Context context, CommonTrackList<TrackM> data, int position,
                                    View view, int playSource, boolean startPlayer, int channel, boolean willPlay) {
        for (Track track : data.getTracks()) {
            track.setPlaySource(playSource);
        }
        PlayTools.playCommonList(context, data, position, startPlayer, view, channel, willPlay);
    }

    @Deprecated
    private static void showToast(Context context, int resId) {
        if (resId > 0 && context != null) {
            CustomToast.showToast(resId);
        }
    }

    @Deprecated
    private static void showToast(Context context, String str) {
        if (!TextUtils.isEmpty(str) && context != null) {
            CustomToast.showToast(str);
        }
    }

    public interface IplayTrackHistoryCallback {
        void onSuccess();

        void onError(int code, String message);
    }

    /**
     * @return 播放器的播放列表是否播完
     */
    public static boolean isPlayListComplete(Context context) {
        int currentIndex = XmPlayerManager.getInstance(context).getCurrentIndex(); //播放完成的声音的index
        int listSize = XmPlayerManager.getInstance(context).getPlayListSize();
        XmPlayListControl.PlayMode playMode = XmPlayerManager.getInstance(context).getPlayMode();
        //列表播放完成
        return currentIndex == listSize - 1 && playMode == XmPlayListControl.PlayMode.PLAY_MODEL_LIST;

    }


    /**
     * TODO 启动微课直播的直播间播放页
     * TODO 启动微课直播的直播间播放页
     *
     * @param context
     * @param roomId
     * @param lessonId
     * @param fromView
     */
    public static void startWeikeLivePlay(Context context,
                                          long roomId,
                                          long lessonId,
                                          boolean startPlayer,
                                          View fromView) {

        Bundle bundle = new Bundle();
        bundle.putLong("key_live_roomid", roomId);
        bundle.putLong("key_live_lessonid", lessonId);

        checkToWeikeLiveFragment(context, bundle, startPlayer, fromView);
    }


    /**
     * 启动微课直播的直播间播放页
     *
     * @param context
     * @param roomId
     * @param lessonId
     * @param fromView
     */
    public static void startWeikeLivePlayWithDiscuss(Context context,
                                                     long roomId,
                                                     long lessonId,
                                                     long discussId,
                                                     boolean startPlayer,
                                                     View fromView) {

        Bundle bundle = new Bundle();
        bundle.putLong("key_live_roomid", roomId);
        bundle.putLong("key_live_lessonid", lessonId);
        bundle.putBoolean("key_weike_open_discuss", true);
        bundle.putLong("key_weike_open_doscuss_id", discussId);

        checkToWeikeLiveFragment(context, bundle, startPlayer, fromView);
    }


    /**
     * TODO 启动微课 极简 播放页
     *
     * @param context
     * @param roomId
     * @param lessonId
     * @param fromView
     */
    public static void startWeikeSimplePlay(Context context,
                                            long roomId,
                                            long lessonId,
                                            boolean startPlayer,
                                            View fromView) {

        Bundle bundle = new Bundle();
        bundle.putLong("key_live_roomid", roomId);
        bundle.putLong("key_live_lessonid", lessonId);
        bundle.putBoolean("key_is_weike_simpleplay", true);

        checkToWeikeSimplePlayFragment(context, bundle, startPlayer, fromView);
    }


    /**
     * TODO 进行微课直播的播放
     * TODO 进行微课直播的播放
     *
     * @param ctx
     * @param bundle
     * @param startPlayer
     * @param fromView
     */
    private static void checkToWeikeLiveFragment(Context ctx, Bundle bundle, boolean startPlayer, View fromView) {
        PlayCompleteRecommendManager.getInstance().finishRecommend();
        if (startPlayer) {
            if (ctx instanceof MainActivity) {
                ((MainActivity) ctx).showWeikeLiveFragment(fromView, bundle);
            } else {
                Activity activity = MainApplication.getTopActivity();
                if (activity instanceof MainActivity) {
                    ((MainActivity) activity).showWeikeLiveFragment(fromView, bundle);
                }
            }
        } else {
            PlayerManager.getInstanse().setPlayWithNoFragment(PlayerManager.WEIKE_LIVE_ROOM);
        }

    }

    /**
     * 进行微课 极简播放 的播放
     *
     * @param ctx
     * @param bundle
     * @param startPlayer
     * @param fromView
     */
    private static void checkToWeikeSimplePlayFragment(Context ctx, Bundle bundle, boolean startPlayer, View fromView) {
        PlayCompleteRecommendManager.getInstance().finishRecommend();
        if (startPlayer) {
            if (ctx instanceof MainActivity) {
                ((MainActivity) ctx).showWeikeSimplePlayFragment(fromView, bundle);
            } else {
                Activity activity = MainApplication.getTopActivity();
                if (activity instanceof MainActivity) {
                    ((MainActivity) activity).showWeikeSimplePlayFragment(fromView, bundle);
                }
            }
        } else {
            PlayerManager.getInstanse().setPlayWithNoFragment(PlayerManager.WEIKE_SIMPLE_PLAY);
        }

    }

    public static void checkToDubShowPPTPlayFragment(Context ctx, Bundle bundle, boolean startPlayer, View fromView) {
        PlayCompleteRecommendManager.getInstance().finishRecommend();
        if (startPlayer) {
            if (ChildProtectManager.checkChildrenModeOpenFromToB(ctx)) {
                return;
            }

            LiveActionCallbackSafe actionCallback = new LiveActionCallbackSafe() {
                @Override
                public void actionImpl() {
                    if (ctx instanceof MainActivity) {
                        ((MainActivity) ctx).showDubShowPPTPlayFragment(fromView, bundle);
                    } else {
                        Activity activity = MainApplication.getTopActivity();
                        if (activity instanceof MainActivity) {
                            ((MainActivity) activity).showDubShowPPTPlayFragment(fromView, bundle);
                        }
                    }
                }
            };

            if (checkHasLiveOnline(ctx, actionCallback)) {
                return;
            } else {
                actionCallback.action();
            }
        }
    }

    public static void checkToMyclubRoomFragment(Context ctx, long roomId, boolean purchased) {
        boolean isRoomOpen = false;
        try {
            isRoomOpen = Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                    .getFunctionAction().isRoomOpen(roomId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (isRoomOpen) {   //如果该房间已存在，跳过myclub模块检查，只检查live模块
            LiveActionCallbackSafe actionCallback = new LiveActionCallbackSafe() {
                @Override
                protected void actionImpl() {
                    Activity mainActivity = BaseApplication.getMainActivity();
                    if (mainActivity instanceof MainActivity) {
                        try {
                            Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                    .getFragmentAction().startRoomFragment((MainActivity) mainActivity, roomId, true, purchased);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            };
            boolean hasLiveOnline = false;
            try {
                hasLiveOnline = Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction()
                        .checkOpenCalling(BaseApplication.getMainActivity(), actionCallback);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (!hasLiveOnline) {
                actionCallback.action();
            }
        } else {
            LiveActionCallbackSafe actionCallback = new LiveActionCallbackSafe() {
                @Override
                protected void actionImpl() {
                    Bundle bundle = new Bundle();
                    bundle.putLong(ILiveFunctionAction.KEY_ROOM_ID, roomId);
                    PlayerManager.getInstanse().startPlayFragment(false, PlayerManager.MYCLUB_TAG, bundle);
                }
            };
            if (!checkHasLiveOnline(ctx, actionCallback)) {
                actionCallback.action();
            }
        }
    }

    // 是否可以显示音量平衡
    public static boolean canShowVolumBalance() {
        return false; // ConfigureCenter.getInstance().getBool("toc", "sound-balance", false);
    }

    public static boolean checkVipCanPlayNew(Track track) {
        if (track == null) {
            return false;
        }
        LoginInfoModelNew user = UserInfoMannage.getInstance().getUser();
        Logger.i("checkVipCanPlayNew", "isFree=" + track.isFree()
                + ",authorizedSource=" + track.getAuthorizedSource()
                + ",vip=" + (user != null && user.isVip()) + ",childVip=" + (user != null && user.isChildVip()));
        if (track.isFree()) {
            return true;
        }
        if (track.getAuthorizedSource() == Track.AUTHORIZED_TYPE_VIP_LISTEN) {
            return user != null && user.isVip();
        }
        if (track.getAuthorizedSource() == Track.AUTHORIZED_TYPE_CHILD_VIP_LISTEN) {
            return user != null && (user.isChildVip() || user.isShadowChild());
        }
        // 如果其他类型的，不做校验，允许播放
        return true;
    }

    // 这里做了简单处理，如果用户是vip里的任意一个，那么都会授权可以继续播放
    @Deprecated
    public static boolean checkVipCanPlay(Track track) {
        if (track == null) {
            return false;
        }
        LoginInfoModelNew user = UserInfoMannage.getInstance().getUser();
        Logger.d("zimotag", "PlayTools checkVipCanPlay free=" + track.isFree() + ",authorizedType=" + track.getAuthorizedType() + ",vip=" + (user != null && user.isVip()) + ",childVip=" + (user != null && user.isChildVip()) + ",user=" + user);
        if (track.isFree()) {
            return true;
        }
        if (track.getAuthorizedType() == Track.AUTHORIZED_TYPE_VIP_LISTEN) {
            return user != null && (user.isVip() || user.isChildVip());
        }
        return false;
    }

    // 检查是否vip可以播放，这个方法有些问题，慎用
    @Deprecated
    public static boolean checkIsVipCanPlay(Track track) {
        if (track == null) {
            return false;
        }

        LoginInfoModelNew user = UserInfoMannage.getInstance().getUser();

        return (track.getAuthorizedType() == Track.AUTHORIZED_TYPE_VIP_LISTEN && user != null && user.isVip())
                || track.isFree();

    }

    // 根据专辑播放 ,如果有历史记录获取历史记录进行播放
    public static void playByAlbumByIdIfHasHistoryUseHistoryV2(Context context, long albumId, IDataCallBack dataCallBack) {
        Track lastPlayTrackInAlbum = XmPlayerManager.getInstance(context).getLastPlayTrackInAlbum(albumId);
        if (lastPlayTrackInAlbum != null) {
            PlayTools.playTrackHistoy(context, lastPlayTrackInAlbum, null, ConstantsOpenSdk.PLAY_FROM_NONE, false, dataCallBack);
        } else {
            HashMap<String, String> params = new HashMap<>();
            params.put(HttpParamsConstants.PARAM_PAGE_ID, "1");
            params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
            params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
            params.put(HttpParamsConstants.PARAM_DEVICE, "android");
            CommonRequestM.getNewsTrackList(params, new IDataCallBack<ListModeBase<TrackM>>() {
                @Override
                public void onSuccess(@Nullable ListModeBase<TrackM> object) {
                    if (object != null && !ToolUtil.isEmptyCollects(object.getList())) {
                        TrackM track = object.getList().get(0);
                        int playType = getPlayType(track);
                        boolean isDownloadedTrack = RouteServiceUtil.getDownloadService().isDownloadedAndFileExist(track);

                        NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
                            @Override
                            public void onOkCallBack() {
                                PlayTools.playTrackWithAlbum(context, track, false, true);
                                if (dataCallBack != null) {
                                    dataCallBack.onSuccess(object);
                                    if (dataCallBack instanceof IDataCallBackWithIndex) {
                                        ((IDataCallBackWithIndex) dataCallBack).onSuccess(object, 0, track);
                                    }
                                }
                            }

                            @Override
                            public void onCancleCallBack() {
                                PlayTools.playTrackWithAlbum(context, track, false, true);
                                if (dataCallBack != null) {
                                    dataCallBack.onSuccess(object);
                                    if (dataCallBack instanceof IDataCallBackWithIndex) {
                                        ((IDataCallBackWithIndex) dataCallBack).onSuccess(object, 0, track);
                                    }
                                }
                            }
                        }, isDownloadedTrack, playType);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(code, message);
                    }
                }
            });
        }
    }

    // 根据专辑播放 ,如果有历史记录获取历史记录进行播放
    public static void playByAlbumByIdIfHasHistoryUseHistory(Context context, long albumId, IDataCallBack dataCallBack) {
        Track lastPlayTrackInAlbum = XmPlayerManager.getInstance(context).getLastPlayTrackInAlbum(albumId);
        if (lastPlayTrackInAlbum != null) {
            PlayTools.playTrackHistoy(context, lastPlayTrackInAlbum, null, ConstantsOpenSdk.PLAY_FROM_NONE, false, dataCallBack);
        } else {
            HashMap<String, String> params = new HashMap<>();
            params.put(HttpParamsConstants.PARAM_PAGE_ID, "1");
            params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
            params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
            params.put(HttpParamsConstants.PARAM_DEVICE, "android");
            CommonRequestM.getNewsTrackList(params, new IDataCallBack<ListModeBase<TrackM>>() {
                @Override
                public void onSuccess(@Nullable ListModeBase<TrackM> object) {
                    if (object != null && !ToolUtil.isEmptyCollects(object.getList())) {
                        TrackM track = object.getList().get(0);
                        int playType = getPlayType(track);
                        boolean isDownloadedTrack = RouteServiceUtil.getDownloadService().isDownloadedAndFileExist(track);

                        NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
                            @Override
                            public void onOkCallBack() {
                                if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_PLAYSORT_OPTIMIZATION, false)) {
                                    PlayTools.playTrackWithAlbum(context, track, false, true);
                                } else {
                                    XmPlayerManager.getInstance(context).playList(TrackM.convertTrackMList(object.getList()), 0);
                                }
                                if (dataCallBack != null) {
                                    dataCallBack.onSuccess(object);
                                    if (dataCallBack instanceof IDataCallBackWithIndex) {
                                        ((IDataCallBackWithIndex) dataCallBack).onSuccess(object, 0, track);
                                    }
                                }
                            }

                            @Override
                            public void onCancleCallBack() {
                                if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_PLAYSORT_OPTIMIZATION, false)) {
                                    PlayTools.playTrackWithAlbum(context, track, false, true);
                                } else {
                                    XmPlayerManager.getInstance(context).setPlayList(TrackM.convertTrackMList(object.getList()), 0);
                                }

                                if (dataCallBack != null) {
                                    dataCallBack.onSuccess(object);
                                    if (dataCallBack instanceof IDataCallBackWithIndex) {
                                        ((IDataCallBackWithIndex) dataCallBack).onSuccess(object, 0, track);
                                    }
                                }
                            }
                        }, isDownloadedTrack, playType);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(code, message);
                    }
                }
            });
        }
    }

    // 根据专辑播放 ,如果有历史记录获取历史记录进行播放
    public static void playByAlbumByIdIfHasHistoryUseHistoryStartPlayPage(Context context, long albumId, IDataCallBack dataCallBack) {
        Track lastPlayTrackInAlbum = XmPlayerManager.getInstance(context).getLastPlayTrackInAlbum(albumId);
        if (lastPlayTrackInAlbum != null) {
            PlayTools.playTrackHistoy(context, lastPlayTrackInAlbum, null, ConstantsOpenSdk.PLAY_FROM_NONE, true);
        } else {
            HashMap<String, String> params = new HashMap<>();
            params.put(HttpParamsConstants.PARAM_PAGE_ID, "1");
            params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
            params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
            params.put(HttpParamsConstants.PARAM_DEVICE, "android");
            CommonRequestM.getNewsTrackList(params, new IDataCallBack<ListModeBase<TrackM>>() {
                @Override
                public void onSuccess(@Nullable ListModeBase<TrackM> object) {
                    if (object != null && !ToolUtil.isEmptyCollects(object.getList())) {
                        TrackM track = object.getList().get(0);
                        int playType = getPlayType(track);
                        boolean isDownloadedTrack = RouteServiceUtil.getDownloadService().isDownloadedAndFileExist(track);
                        CommonTrackList<TrackM> commonTrackList = new CommonTrackList<>();
                        commonTrackList.setTracks(object.getList());
                        Map<String, String> params = new HashMap<>();
                        params.put(DTransferConstants.TRACK_BASE_URL, UrlConstants.getInstanse().getAlbumTrackList());
                        params.put(DTransferConstants.LOAD_PLAY_LIST_BY_TRACK_ID, "true");
                        params.put(DTransferConstants.ALBUMID, String.valueOf(albumId));
//                        params.put(DTransferConstants.LOCAL_IS_ASC, "true");
                        IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
                        int order = historyManager != null ? historyManager.getAlbumSortByAlbumId(albumId) : IHistoryManagerForMain.ASC_ORDER;
                        boolean isAsc = (order == IHistoryManagerForMain.ASC_ORDER);
                        params.put(DTransferConstants.LOCAL_IS_ASC, String.valueOf(isAsc));


                        commonTrackList.setParams(params);

                        NetworkUtils.confirmNetwork(new NetworkUtils.ConfirmNetWorkClickCallBack() {
                            @Override
                            public void onOkCallBack() {
//                                XmPlayerManager.getInstance(context)
//                                        .playList(TrackM.convertTrackMList(object.getList()), 0);

                                PlayTools.playCommonList(context, commonTrackList, 0, true, null);
                                if (dataCallBack != null) {
                                    dataCallBack.onSuccess(object);
                                }
                            }

                            @Override
                            public void onCancleCallBack() {
//                                XmPlayerManager.getInstance(context)
//                                        .setPlayList(TrackM.convertTrackMList(object.getList()), 0);

                                PlayTools.playCommonList(context, commonTrackList, 0, true, null, SharedConstant.CHANNEL_UNKNOWN, false);

                                if (dataCallBack != null) {
                                    dataCallBack.onSuccess(object);
                                }
                            }
                        }, isDownloadedTrack, playType);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(code, message);
                    }
                }
            });
        }
    }


    // 驾驶模式根据专辑播放 ,如果有历史记录获取历史记录进行播放 和playByAlbumByIdIfHasHistoryUseHistory区别是 播放历史的时候带顺播放序
    // 咨询类的专辑每次都从最新一条开播
    public static void playByAlbumByIdIfHasHistoryUseHistoryForDriveMode(Context context, long albumId, boolean hasInfoTag, IDataCallBack dataCallBack) {
        Track lastPlayTrackInAlbum = XmPlayerManager.getInstance(context).getLastPlayTrackInAlbum(albumId);
        if (lastPlayTrackInAlbum != null && !hasInfoTag) {
            playTrackHistoy(context, true, lastPlayTrackInAlbum, new IplayTrackHistoryCallback() {
                @Override
                public void onSuccess() {
                    if (dataCallBack != null) {
                        dataCallBack.onSuccess(lastPlayTrackInAlbum);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(code, message);
                    }
                }
            }, false, true, false);


        } else {
            HashMap<String, String> params = new HashMap<>();
            params.put(HttpParamsConstants.PARAM_PAGE_ID, "1");
            params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
            params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
            params.put(HttpParamsConstants.PARAM_DEVICE, "android");
            CommonRequestM.getNewsTrackList(params, new IDataCallBack<ListModeBase<TrackM>>() {
                @Override
                public void onSuccess(@Nullable ListModeBase<TrackM> object) {
                    if (object != null && !ToolUtil.isEmptyCollects(object.getList())) {
                        XmPlayerManager.getInstance(context)
                                .playList(ListModeBase.toCommonTrackList(object), 0);
                        if (dataCallBack != null) {
                            dataCallBack.onSuccess(object.getList().get(0));
                        }
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(code, message);
                    }
                }
            });
        }
    }

    public static void playByAlbumByIdIfHasHistoryUseHistoryForDriveMode(Context context, long albumId, boolean hasInfoTag, IDataCallBack dataCallBack, IDataCallbackWithDriveMode dataCallbackWithDriveMode) {
        Track lastPlayTrackInAlbum = XmPlayerManager.getInstance(context).getLastPlayTrackInAlbum(albumId);
        if (lastPlayTrackInAlbum != null && !hasInfoTag && lastPlayTrackInAlbum.getAlbum() != null && lastPlayTrackInAlbum.getAlbum().getAlbumId() == albumId) {
            playTrackHistoy(context, true, lastPlayTrackInAlbum, new IplayTrackHistoryCallback() {
                @Override
                public void onSuccess() {
                    if (dataCallBack != null) {
                        dataCallBack.onSuccess(lastPlayTrackInAlbum);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(code, message);
                    }
                }
            }, false, true, false, dataCallbackWithDriveMode);


        } else {
            HashMap<String, String> params = new HashMap<>();
            params.put(HttpParamsConstants.PARAM_PAGE_ID, "1");
            params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
            params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
            params.put(HttpParamsConstants.PARAM_DEVICE, "android");
            CommonRequestM.getNewsTrackList(params, new IDataCallBack<ListModeBase<TrackM>>() {
                @Override
                public void onSuccess(@Nullable ListModeBase<TrackM> object) {
                    if (object != null && !ToolUtil.isEmptyCollects(object.getList())) {
                        XmPlayerManager.getInstance(context)
                                .playList(ListModeBase.toCommonTrackList(object), 0);
                        if (dataCallBack != null) {
                            dataCallBack.onSuccess(object.getList().get(0));
                        }
                        dataCallbackWithDriveMode.getDataSuccess(ListModeBase.toCommonTrackList(object), 0);
                    }
                }

                @Override
                public void onError(int code, String message) {
                    if (dataCallBack != null) {
                        dataCallBack.onError(code, message);
                    }
                }
            });
        }
    }


    /**
     * 获取娱乐厅直播间 roomId
     *
     * @return
     */
    public static long getEntRoomId(PlayableModel playableModel) {
        if ((playableModel != null && PlayableModel.KIND_ENT_FLY.equals(playableModel.getKind())) && playableModel instanceof Track) {
            Track track = (Track) playableModel;
            return track.getLiveRoomId();
        }
        return -1;
    }

    /**
     * 根据直播场次id，进入课程直播间
     *
     * @param roomId     房间id，非必传
     * @param liveId     直播场次id，必传
     * @param playSource 播放来源，必传
     * @param showBack   非上下滑场景的跳转直播间，一律传true，展示返回上个直播间，必传
     */
    public static void playCourseLiveByLiveId(final FragmentActivity activity, long roomId, long liveId, @ILivePlaySource int playSource, boolean showBack) {
        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    try {
                        Bundle bundle = new Bundle();
                        bundle.putBoolean(ILiveFunctionAction.KEY_SHOW_BACK, showBack);
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().startLiveRoom(new StartRoomIntent()
                                .setActivity(activity)
                                .setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE)
                                .setRoomId(roomId)
                                .setBundle(bundle)
                                .setLiveId(liveId)
                                .setPlaySource(playSource));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    CustomToast.showFailToast("直播模块加载失败");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }

    /**
     * 进入 PGC 聊天室
     *
     * @param activity   上下文
     * @param roomId     PGC直播间id
     * @param playSource 播放来源
     * @param showBack   非上下滑场景的跳转直播间，一律传true，展示返回上个直播间
     */
    public static void playEntHallWithPlaySource(final FragmentActivity activity, long roomId, @ILivePlaySource int playSource, boolean showBack) {
        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    try {
                        PlayCompleteRecommendManager.getInstance().finishRecommend();
                        Bundle bundle = new Bundle();
                        bundle.putBoolean(ILiveFunctionAction.KEY_SHOW_BACK, showBack);
                        bundle.putLong(ILiveFunctionAction.KEY_ROOM_ID, roomId);
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().startLiveRoom(new StartRoomIntent()
                                .setActivity(activity)
                                .setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC)
                                .setPlaySource(playSource)
                                .setBundle(bundle));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    CustomToast.showFailToast("直播模块加载失败");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }

    /**
     * 进入 PGC 聊天室
     *
     * @param activity   上下文
     * @param playSource 播放来源
     * @param bundle     携带的参数
     */
    public static void playEntHallWithPlaySource(final FragmentActivity activity, @ILivePlaySource int playSource, Bundle bundle) {
        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    try {
                        PlayCompleteRecommendManager.getInstance().finishRecommend();
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().startLiveRoom(new StartRoomIntent()
                                .setActivity(activity)
                                .setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC)
                                .setPlaySource(playSource)
                                .setBundle(bundle));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    CustomToast.showFailToast("直播模块加载失败");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }

    /**
     * 进入娱乐厅
     *
     * @param bundle bundle参数：需要携带 bundle.putBoolean(ILiveFunctionAction.KEY_SHOW_BACK, showBack) 参数
     */
    public static void playEntHallRoom(final FragmentActivity activity, Bundle bundle) {
        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    try {
                        PlayCompleteRecommendManager.getInstance().finishRecommend();
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().startLiveRoom(new StartRoomIntent()
                                .setActivity(activity)
                                .setRoomType(BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC)
                                .setBundle(bundle));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.liveBundleModel) {
                    CustomToast.showFailToast("直播模块加载失败");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }

    private static boolean checkHasLiveOnline(Context activity) {

        return LivePlayControlUtil.checkHasLiveOnline(BaseApplication.getMainActivity());
    }

    private static boolean checkHasLiveOnline(Context activity, LiveActionCallbackSafe actionCallback) {
        return LivePlayControlUtil.checkHasLiveOnline(BaseApplication.getMainActivity(), actionCallback);
    }

    public static void showVipBenefitDialog(Context context, String url) {
        Intent intent = new Intent();
        intent.setAction(ActionConstants.ACTION_SHOW_VIP_BENEFIT);
        intent.putExtra(BundleKeyConstants.KEY_VIP_EXTRA_URL, url);
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
            }
        }, 1000);
    }

    /**
     * 播放器恢复成主播放器的内容。直播、助眠等退出播放的时候调用。
     */
    public static void restoreToMainPlayer(Context context) {
        restoreToMainPlayer(context, false);
    }

    /**
     * 播放器恢复成主播放器的内容，并自动播放。直播、助眠等退出播放的时候调用。
     */
    public static void restoreToMainPlayer(Context context, boolean willPlay) {
        Logger.i("PlayTools", "restoreToMainPlayer");
        if (DailyNewsLogicManager.INSTANCE.getIndexBeforeLive() > 0) {
            // 今日热点直播卡片需求
            PlayTools.playCommonList(context, DailyNewsLogicManager.INSTANCE.getCommonTrackListBeforeLive(),
                    DailyNewsLogicManager.INSTANCE.getIndexBeforeLive(), false,
                    null, SharedConstant.CHANNEL_UNKNOWN, false);
            return;
        }
        CommonTrackList<PlayableModel> lastAudioPlayList = LastAudioPlayListCache.INSTANCE.getLastAudioPlayList();
        if (lastAudioPlayList != null && !ToolUtil.isEmptyCollects(lastAudioPlayList.getTracks())) {
            int index = LastAudioPlayListCache.INSTANCE.getLastAudioPlayIndex();
            LastAudioPlayListCache.INSTANCE.clear();
            Map<String, String> params = new HashMap<>();
            params.put(DTransferConstants.TRACK_BASE_URL, UrlConstants.getInstanse().getAlbumPlayList());
            params.put(DTransferConstants.LOAD_PLAY_LIST_BY_TRACK_ID, "true");
            if (index >= 0 && index < lastAudioPlayList.getTracks().size()) {
                PlayableModel track = lastAudioPlayList.getTracks().get(index);
                if (track instanceof Track) {
                    ((Track) track).setDoNotAddToHistoryThisTime(true);
                }
            }
            playCommonList(context, lastAudioPlayList, index, false, null, SharedConstant.CHANNEL_UNKNOWN, willPlay);
            XmPlayerManager.getInstance(context).clearMixPlayTrack(); // 清理助眠的
        } else {
            IHistoryManagerForMain historyManagerForMain =
                    RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
            if (historyManagerForMain != null) {
                long startTime = System.currentTimeMillis();
                List<HistoryModel> historyModelList = historyManagerForMain.getTrackList();
                boolean nothingToRestore = true;
                if (!ToolUtil.isEmptyCollects(historyModelList)) {
                    HistoryModel trackHistoryModel = null;
                    for (HistoryModel historyModel : historyModelList) {
                        if (historyModel.isTrack()) {
                            trackHistoryModel = historyModel;
                            break;
                        }
                    }
                    if (trackHistoryModel != null && trackHistoryModel.getTrack() != null) {
                        nothingToRestore = false;
                        Track track = trackHistoryModel.getTrack();
                        // 还原的这种场景，不是用户主动播放，就不要去更新播放历史了
                        track.setDoNotAddToHistoryThisTime(true);
                        playTrackWithAlbum(context, track, false, willPlay);
                        Logger.i("PlayTools", "restoreToMainPlayer set play list " + track);
                    }
                }
                if (nothingToRestore) {
                    TingLocalMediaService.triggerLive();
                    XmPlayerManager.getInstance(context).resetPlayListForRestoreToMainPlayer();
                    XmPlayerManager.getInstance(context).clearPlayList();
                }
                XmPlayerManager.getInstance(context).clearMixPlayTrack(); // 清理助眠的
                long costTime = System.currentTimeMillis() - startTime;
                Logger.i("PlayTools", "restoreToMainPlayer get history cost time: " + costTime);
            }
        }
    }

    public static int getSavedPlayMode(Context context) {
        return XmPlayerManager.getInstance(context).getSavedPlayMode(context);
    }

    public static void savePlayModeToMmkv(Context context, int playMode) {
        PlayListMMKVUtil.getInstance(context).saveInt(PreferenceConstantsInHost.TINGMAIN_KEY_PLAY_MODE, playMode);
    }

    /**
     * 打开短剧播放页
     *
     * @param context 上下文
     * @param trackId 声音id
     */
    public static void playMiniDrama(final Context context, long trackId) {
        playMiniDrama(context, trackId, 0);
    }

    /**
     * 打开短剧播放页
     *
     * @param context    上下文
     * @param trackId    声音id
     * @param playSource 来源标识
     */
    public static void playMiniDrama(final Context context, long trackId, int playSource) {
        Router.getActionByCallback(Configure.BUNDLE_MINI_DRAMA, new Router.IBundleInstallCallback() {

            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.miniDramaBundleModel) {
                    try {
                        MainActivity act = null;
                        if (context instanceof MainActivity) {
                            act = (MainActivity) context;
                        } else if (MainApplication.getMainActivity() instanceof MainActivity) {
                            act = (MainActivity) MainApplication.getMainActivity();
                        }
                        if (null == act) {
                            return;
                        }

                        Router.<MiniDramaActionRouter>getActionRouter(Configure.BUNDLE_MINI_DRAMA)
                                .getFunctionAction()
                                .openMiniDramaDetailFeedPage(act, trackId, null, playSource);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.miniDramaBundleModel) {
                    CustomToast.showFailToast("短剧模块加载失败");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                if (bundleModel != null && bundleModel == Configure.miniDramaBundleModel) {
                    CustomToast.showFailToast("短剧模块加载失败");
                }
            }
        }, true, BundleModel.DOWNLOAD_SHOW_PROGRESS);
    }

    public static void playQuickListenListWithTrackId(List<JSONObject> data, long trackId, boolean useLastPlay, long tabId, boolean pageVisable) {
        // 设置到播放进程
        List<Track> tracks = XimaTenDataUtil.convertDataToTrack(tabId, data);
        int index = -1;
        if (tracks != null) {
            for (int i = 0; i < tracks.size(); i++) {
                Track track = tracks.get(i);
                if (track == null) {
                    continue;
                }
                // 快听的特殊业务标识
                track.setChannelGroupId(9999);
                if (track.getDataId() == trackId) {
                    index = i;
                }
            }
        }

        if (index < 0) {
            index = 0;

            if (data != null && data.size() > 0) {
                trackId = QuickListenDataManager.Companion.getInstance().getPlayIndex(data, 0);

                if (trackId > 0 && tracks != null) {
                    for (int i = 0; i < tracks.size(); i++) {
                        Track track = tracks.get(i);
                        if (track == null) {
                            continue;
                        }
                        // 快听的特殊业务标识
                        track.setChannelGroupId(9999);
                        if (track.getDataId() == trackId) {
                            index = i;
                        }
                    }
                }
            }
        }
        CommonTrackList<Track> commonTrackList = QuickListenPlayUtil.INSTANCE.buildPlayCommonTrackList(tracks, tabId);

        boolean isVideo = false;
        if (tracks != null && tracks.size() > 0 && index < tracks.size()) {
            Track track = tracks.get(index);
            if (track != null) {
                isVideo = track.isVideo();
            }
        }

        boolean finalIsVideo = isVideo;
        int finalIndex = index;
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if (finalIsVideo) {
                    XmPlayerManager.getInstance(ToolUtil.getCtx()).setPlayList(commonTrackList, finalIndex);
                } else {
                    if (useLastPlay) {
                        if (XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying()) {
                            XmPlayerManager.getInstance(ToolUtil.getCtx()).playList(commonTrackList, finalIndex);
                        } else {
                            XmPlayerManager.getInstance(ToolUtil.getCtx()).setPlayList(commonTrackList, finalIndex);
                        }
                    } else {
                        XmPlayerManager.getInstance(ToolUtil.getCtx()).playList(commonTrackList, finalIndex);
                    }
                }
            }
        };

        if (XmPlayerManager.getInstance(ToolUtil.getCtx()).isConnected()) {
            runnable.run();
        } else {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                @Override
                public void onConnected() {
                    XmPlayerManager.getInstance(ToolUtil.getCtx()).removeOnConnectedListerner(this);
                    runnable.run();
                }
            });
        }
    }

    public static void playQuickListenListNew(List<JSONObject> data, int index, boolean useLastPlay, long tabId) {
        // 设置到播放进程
        List<Track> tracks = XimaTenDataUtil.convertDataToTrack(tabId, data);
        if (tracks != null) {
            for (Track track : tracks) {
                // 快听的特殊业务标识
                track.setChannelGroupId(9999);
            }
        }
        CommonTrackList<Track> commonTrackList = QuickListenPlayUtil.INSTANCE.buildPlayCommonTrackList(tracks, tabId);

        if (index >= commonTrackList.getTracks().size()) {
            index = 0;
        }

        if (useLastPlay) {
            if (XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying()) {
                XmPlayerManager.getInstance(ToolUtil.getCtx()).playList(commonTrackList, index);
            } else {
                XmPlayerManager.getInstance(ToolUtil.getCtx()).setPlayList(commonTrackList, index);
            }
        } else {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).playList(commonTrackList, index);
        }
    }

    public static CommonTrackList<Track> convertToCommonTrackList(List<JSONObject> data, long tabId) {
        // 设置到播放进程
        List<Track> tracks = XimaTenDataUtil.convertDataToTrack(tabId, data);
        if (tracks != null) {
            for (Track track : tracks) {
                // 快听的特殊业务标识
                track.setChannelGroupId(9999);
            }
        }
        return QuickListenPlayUtil.INSTANCE.buildPlayCommonTrackList(tracks, tabId);
    }

    public static void playQuickListenListNew(CommonTrackList<Track> commonTrackList, int index, boolean useLastPlay) {
        if (commonTrackList == null) {
            return;
        }
        if (useLastPlay) {
            if (XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying()) {
                XmPlayerManager.getInstance(ToolUtil.getCtx()).playList(commonTrackList, index);
            } else {
                XmPlayerManager.getInstance(ToolUtil.getCtx()).setPlayList(commonTrackList, index);
            }
        } else {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).playList(commonTrackList, index);
        }
    }

    public static void requestXimaTenAndPlay(boolean fromCache, boolean autoPlay, boolean refresh, IDataCallBack<List<JSONObject>> callBack) {
        requestXimaTenAndPlay(fromCache, autoPlay, refresh, null, callBack);
    }

    public static void requestXimaTenAndPlay(boolean fromCache, boolean autoPlay, boolean refresh, List<Long> deleTrackIds, IDataCallBack<List<JSONObject>> callBack) {
        long startTime = System.currentTimeMillis();
        if (refresh) {
            long lastSaveTime = MMKVUtil.getInstance().getLong(PreferenceConstantsInOpenSdk.KEY_LAST_SAVE_TIME_FOR_XIMA_TEN, 0);
            if (Math.abs(lastSaveTime - System.currentTimeMillis()) >= 24 * 3600 * 1000) {
                fromCache = false;
                XimaTenDataManager.Companion.getInstance().resetList();
            }
        }
        if (fromCache) {
            Logger.d("XimaTenData", "fromCache diff1=" + (System.currentTimeMillis() - startTime));
            List<JSONObject> data = XimaTenDataManager.Companion.getInstance().cache();
            XmPlayerManager playerManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext());
            if (autoPlay && !playerManager.isQuickListen()) {
                Logger.d("XimaTenData", "fromCache diff2=" + (System.currentTimeMillis() - startTime));
                // 设置到播放进程
                playQuickListenList(data, 0, false);
                Logger.d("XimaTenData", "fromCache diff3=" + (System.currentTimeMillis() - startTime));
            }
            callBack.onSuccess(data);
            return;
        }
        if (refresh) {
            MMKVUtil.getInstance().saveString(PreferenceConstantsInOpenSdk.KEY_XIMA_TEN_SHORT_CONTENT_LAST_PAGE_ITEMS, null);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("hasAgentTalk", false);
        if (refresh) {
            params.put("pageIndex", 1);
        }

        if (!TextUtils.isEmpty(QuickListenTabAbManager.getRequestTenData())) {
            params.put("ximaTenAb", QuickListenTabAbManager.getRequestTenData());
        }

        if (deleTrackIds != null && !deleTrackIds.isEmpty()) {
            params.put("discardItems", deleTrackIds);
        }

        params.put(DTransferConstants.XIMA_TAB_RECOMMEND, "1");

        CommonRequestM.requestXimaTen(refresh, params, new IDataCallBack<List<JSONObject>>() {
            @Override
            public void onSuccess(@Nullable List<JSONObject> data) {
                Logger.d("XimaTenData", "requestXimaTen net-request diff1=" + (System.currentTimeMillis() - startTime));
                if (refresh) {
                    XimaTenDataManager.Companion.getInstance().resetList();
                    MMKVUtil.getInstance().saveLong(PreferenceConstantsInOpenSdk.KEY_LAST_SAVE_TIME_FOR_XIMA_TEN, System.currentTimeMillis());
                }
                // 将缓存数据清掉
                XimaTenDataManager.Companion.getInstance().cacheData(data, !refresh, deleTrackIds);

                Logger.d("XimaTenData", "requestXimaTen net-request diff2=" + (System.currentTimeMillis() - startTime));

                XmPlayerManager playManager = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext());
                if (ToolUtil.isEmptyCollects(deleTrackIds)) {
                    if (autoPlay) {
                        // 设置到播放进程
                        playQuickListenList(data, 0, false);
                    } else {
                        // 检查播放进程是否被同步，如果之前同步过的话，那会add到播放进程
                        if (XimaTenDataUtil.isXimaTenPlayList()) {
                            playManager.addTracksToPlayList(XimaTenDataUtil.convertDataToTrack(data));
                        } else {
                            Logger.logToFile("XimaTenData", "requestXimaTenAndPlay isXimaTenTRack=false");
                        }
                    }
                } else {
                    // 要删除的数据,需要重新设置播放列表
                    List<JSONObject> dataNew = XimaTenDataManager.Companion.getInstance().cache();
                        // 设置到播放进程
                    playQuickListenList(dataNew, XmPlayerManager.getInstance(ToolUtil.getCtx()).getCurrentIndex(), true);
                }
                Logger.d("XimaTenData", "requestXimaTen net-request diff3=" + (System.currentTimeMillis() - startTime));

                callBack.onSuccess(data);
            }

            @Override
            public void onError(int code, String message) {
                callBack.onError(code, message);
            }
        });
    }

    private static void playQuickListenList(List<JSONObject> data, int index, boolean useLastPlay) {
        // 设置到播放进程
        List<Track> tracks = XimaTenDataUtil.convertDataToTrack(data);
        if (tracks != null) {
            for (Track track : tracks) {
                // 快听的特殊业务标识
                track.setChannelGroupId(9999);
            }
        }
        CommonTrackList<Track> commonTrackList = QuickListenPlayUtil.INSTANCE.buildPlayCommonTrackList(tracks, -1);

        if (index >= commonTrackList.getTracks().size()) {
            index = 0;
        }

        if (useLastPlay) {
            if (XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying()) {
                XmPlayerManager.getInstance(ToolUtil.getCtx()).playList(commonTrackList, index);
            } else {
                XmPlayerManager.getInstance(ToolUtil.getCtx()).setPlayList(commonTrackList, index);
            }
        } else {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).playList(commonTrackList, index);
        }
    }

}
