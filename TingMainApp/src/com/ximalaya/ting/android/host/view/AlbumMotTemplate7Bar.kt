package com.ximalaya.ting.android.host.view

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.model.album.middlebar.AlbumMotBarModel
import com.ximalaya.ting.android.host.util.CountDownTimeUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import java.util.concurrent.TimeUnit
import kotlin.math.max
import kotlin.math.min

/**
 * Author: <PERSON>
 * Email: <EMAIL>
 * Date: 2025/8/7
 * Description：专辑页818mot条
 */
class AlbumMotTemplate7Bar : BaseAlbumTemplateMotBar {
    companion object {
        const val TAG = "AlbumMotTemplate7Bar"
    }

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    private lateinit var mContentBgView: ImageFilterView
    private lateinit var mIvBtnBg: ImageFilterView
    private lateinit var mIvLogo: ImageFilterView
    private lateinit var mCenterTextView: TextView
    private lateinit var mSubTitleTextView: TextView
    private lateinit var mRightButtonTextView: TextView


    override fun initView(itemView: View) {
        mContentBgView = itemView.findViewById(R.id.host_view_bg)
        mIvLogo = itemView.findViewById(R.id.host_iv_logo)
        mCenterTextView = itemView.findViewById(R.id.host_tv_center_title)
        mSubTitleTextView = itemView.findViewById(R.id.host_tv_sub_title)
        mIvBtnBg = itemView.findViewById(R.id.host_iv_mot_buy_btn_bg)
        mRightButtonTextView = itemView.findViewById(R.id.host_tv_mot_buy_btn)
    }

    override fun getLayoutId(): Int {
        return R.layout.host_view_album_mot_bar_template_7
    }

    override fun setMainView() {
        mData ?: return
        val oneSec = TimeUnit.SECONDS.toMillis(1)
        val timeLeft = CountDownTimeUtil.getRealCountDownDuration(
            mData!!.expireTimeInMill,
            mData!!.localBaseTimeStamp
        )
        if (0 < timeLeft && mData!!.text.contains("%s")) {
            mCenterTextView.text = getCenterCountDownText(mData, timeLeft)
            mCountDownView = CountDown(
                this,
                mData!!,
                timeLeft,
                oneSec
            )
            mCountDownView?.start()
        } else {
            ViewStatusUtil.setText(mCenterTextView, mData!!.text)
        }
        if (mData?.subText.isNullOrEmpty()) {
            ViewStatusUtil.setVisible(View.GONE, mSubTitleTextView)
        } else {
            ViewStatusUtil.setText(mSubTitleTextView, mData!!.subText)
            ViewStatusUtil.setVisible(View.VISIBLE, mSubTitleTextView)
        }
    }


    override fun doOnTicking(
        millisUntilFinished: Long
    ) {
        mData ?: return
        mCenterTextView.text = getCenterCountDownText(mData, millisUntilFinished)
    }

    override fun doOnCountDownFinish() {
        mData ?: return
        if (mData!!.text.contains("%s")) {
            mCenterTextView.text = getCenterCountDownText(mData, 0)
        } else {
            mCenterTextView.text = mData!!.text
        }
    }

    override fun setStyle() {
        mData ?: return
        val s = mData!!
        mCenterTextView.setTextColor(getTextColor(s))
        mSubTitleTextView.setTextColor(getTextColor(s))
        if (mData?.backgroundImage.isNullOrEmpty()) {
            mContentBgView.background = getBackgroundDrawable(s)
        } else {
            ImageManager.from(context).displayImage(mContentBgView, mData!!.backgroundImage, -1)
        }

        if (!TextUtils.isEmpty(mData?.logo?.logoUrl)) {
            ViewStatusUtil.setVisible(VISIBLE, mIvLogo)
            val targetHeight = 20.dp
            var scale = 0f
            try {
                scale = mData?.logo?.size?.toFloat() ?: 1f
            } catch (e: Exception) {
            }
            if (scale > 0) {
                val lp = mIvLogo.layoutParams
                lp.height = targetHeight
                lp.width = (targetHeight * scale).toInt()
                mIvLogo.setLayoutParams(lp)
                ImageManager.from(context).displayImage(mIvLogo, mData?.logo?.logoUrl, -1)
            } else {
                ImageManager.from(context).downloadBitmap(
                    mData?.logo?.logoUrl
                ) { lastUrl, bitmap ->
                    if (bitmap == null) {
                        ViewStatusUtil.setVisible(
                            GONE,
                            mIvLogo
                        )
                    } else {
                        mIvLogo.setImageBitmap(bitmap)
                        val bw = bitmap.width
                        val bh = bitmap.height
                        if (bw > 0 && bh > 0) {
                            val targetWidth = ((1f * targetHeight / bh) * bw).toInt()
                            val lp = mIvLogo.layoutParams
                            lp.height = targetHeight
                            lp.width = targetWidth
                            mIvLogo.setLayoutParams(lp)
                        }
                    }
                }
            }
        } else {
            ViewStatusUtil.setVisible(GONE, mIvLogo)
        }

        val buttonInfo = mData?.firstButton
        if (buttonInfo != null) {
            mRightButtonTextView.setTextColor(
                getColorWithAlpha(
                    if (buttonInfo.actionId == -1) 0.3f else 1f,
                    getBtnTexColor(s)
                )
            )
            ViewStatusUtil.setText(mRightButtonTextView, if (TextUtils.isEmpty(buttonInfo.text)) "锁定底价" else buttonInfo.text)
            mContentBgView.setOnClickListener {
                mActionListener?.onMiddleGuidanceBarClick(buttonInfo)
            }
            if (TextUtils.isEmpty(buttonInfo.buttonBackgroundImage)) {
                ViewStatusUtil.setVisible(View.INVISIBLE, mIvBtnBg)
                ViewStatusUtil.setVisible(View.VISIBLE, mRightButtonTextView)
                mRightButtonTextView.background = getBtnBackgroundDrawable(s)

                if (mRightButtonTextView.layoutParams != null) {
                    mRightButtonTextView.layoutParams.width = 68.dp
                    mRightButtonTextView.layoutParams.height = 28.dp
                }
            } else {
                ViewStatusUtil.setVisible(
                    VISIBLE,
                    mIvBtnBg,
                    mRightButtonTextView
                )
                ImageManager.from(context)
                    .displayImage(mIvBtnBg, buttonInfo.buttonBackgroundImage, -1)
            }
        } else {
            ViewStatusUtil.setVisible(GONE, mRightButtonTextView, mIvBtnBg)
        }
    }


    fun getColorWithAlpha(alpha: Float, baseColor: Int): Int {
        val a = min(255, max(0, (alpha * 255).toInt())) shl 24
        val rgb = 0x00ffffff and baseColor
        return a + rgb
    }

    private fun getBackgroundDrawable(s: AlbumMotBarModel): Drawable? {
        val builder = ViewStatusUtil.GradientDrawableBuilder()
        return builder.cornerRadius(BaseUtil.dp2pxReturnFloat(context, 6f))
            .color(s.backgroundColor)
            .build()
    }

    private fun getBtnBackgroundDrawable(s: AlbumMotBarModel): Drawable? {
        val builder = ViewStatusUtil.GradientDrawableBuilder()
        return builder.cornerRadius(BaseUtil.dp2pxReturnFloat(context, 110f))
            .color(s.buttonBackgroundColor)
            .build()
    }

    private fun getTextColor(s: AlbumMotBarModel): Int {
        return s.textColor
    }

    private fun getBtnTexColor(s: AlbumMotBarModel): Int {
        return s.buttonTextColor
    }

}