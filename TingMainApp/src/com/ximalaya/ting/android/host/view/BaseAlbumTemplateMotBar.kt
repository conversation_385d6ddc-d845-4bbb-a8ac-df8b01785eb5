package com.ximalaya.ting.android.host.view

import android.content.Context
import android.os.CountDownTimer
import android.text.SpannableString
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.model.BaseMotBarModel
import com.ximalaya.ting.android.host.model.album.middlebar.AlbumMotBarModel
import com.ximalaya.ting.android.host.util.CountDownTimeUtil
import java.lang.ref.WeakReference

/**
 * Created by mark on 2025/3/3 14:04
 */
abstract class BaseAlbumTemplateMotBar : FrameLayout {
    companion object {
        const val TAG = "AlbumMiddleGuidanceBar"
        fun getAlbumTemplateMotBar(
            context: Context?,
            data: AlbumMotBarModel?
        ): BaseAlbumTemplateMotBar? {
            context ?: return null
            data ?: return null
            return when (data.templateId) {
                2, 5 -> AlbumMiddleGuidanceBar(context)
                4 -> AlbumMotTemplate4Bar(context)
                7 -> AlbumMotTemplate7Bar(context)
                else -> null
            }
        }
    }

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context)
    }

    protected lateinit var mContentView: View

    protected var mData: AlbumMotBarModel? = null
    protected var mCountDownView: CountDownTimer? = null

    protected var mActionListener: IAlbumMiddleGuidanceBarActionListener? = null

    protected fun init(context: Context) {
        val itemView = LayoutInflater.from(context)
            .inflate(getLayoutId(), this, true)
        mContentView = itemView
        initView(itemView)
    }

    abstract fun initView(itemView:View)

    abstract fun getLayoutId(): Int

    abstract fun setMainView()

    abstract fun doOnTicking(millisUntilFinished: Long)

    abstract fun doOnCountDownFinish()

    abstract fun setStyle()

    fun bindDataToView(
        data: AlbumMotBarModel?,
        listener: IAlbumMiddleGuidanceBarActionListener
    ): Boolean {
        if (data?.isValid != true) {
            return false
        }
        mData = data
        mActionListener = listener
        resetCountDown()
        setStyle()
        setMainView()
        listener.onShow()
        return data.isValid
    }


    private fun resetCountDown() {
        if (null != mCountDownView) {
            try {
                mCountDownView!!.cancel()
                mCountDownView = null
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }


    protected fun getCenterCountDownText(
        yellowZone: AlbumMotBarModel?,
        timeLeft: Long
    ): CharSequence {
        if (null == yellowZone || TextUtils.isEmpty(yellowZone.text)) {
            return ""
        }
        val timePart = CountDownTimeUtil.buildCountDownStyle3(timeLeft)
        val headPart = yellowZone.text
        val spannableString: SpannableString
        spannableString = if (TextUtils.isEmpty(timePart)) {
            SpannableString(timePart)
        } else {
            if (headPart.contains("%s")) {
                SpannableString(headPart.replace("%s", timePart))
            } else {
                SpannableString(headPart)
            }
        }
        return spannableString
    }

    class CountDown(
        view: BaseAlbumTemplateMotBar,
        yellowZoneModel: AlbumMotBarModel,
        millisInFuture: Long,
        countDownInterval: Long
    ) : CountDownTimer(millisInFuture, countDownInterval) {
        private val viewReference: WeakReference<BaseAlbumTemplateMotBar>
        private val yellowZoneModel: AlbumMotBarModel?

        init {
            this.yellowZoneModel = yellowZoneModel
            viewReference = WeakReference(view)
        }

        override fun onTick(millisUntilFinished: Long) {
            val view = viewReference.get()
            if (null == view || null == yellowZoneModel) {
                cancel()
                return
            }
            view.doOnTicking(millisUntilFinished)
        }

        override fun onFinish() {
            val view = viewReference.get()
            if (null == view || null == yellowZoneModel) {
                cancel()
                return
            }
            view.doOnCountDownFinish()
        }

    }

    interface IAlbumMiddleGuidanceBarActionListener {
        fun checkLoginStatus(): Boolean {
            if (UserInfoMannage.hasLogined()) {
                return true
            }
            UserInfoMannage.gotoLogin(BaseApplication.getMyApplicationContext())
            return false
        }

        fun onMiddleGuidanceBarClick(button: BaseMotBarModel.ButtonInfo)
        fun onMiddleGuidanceBarClose()
        fun onShow()
    }
}