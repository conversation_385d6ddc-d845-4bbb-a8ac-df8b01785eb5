package com.ximalaya.ting.android.host.view;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.Log;
import android.widget.RadioButton;

import com.ximalaya.ting.android.xmutil.Logger;

import androidx.annotation.Nullable;

/**
 * Created by nali on 2025/8/2.
 *
 * <AUTHOR>
 */

public class MyRadioButton extends RadioButton {
    public MyRadioButton(Context context) {
        super(context);
    }

    public MyRadioButton(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MyRadioButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    @Override
    public void setTextColor(int color) {
        super.setTextColor(color);
        
        Logger.log("MyCustomRadioButton : setTextColor " + color  + "   " + getText() + "   " + Log.getStackTraceString(new Throwable()));
    }


    @Override
    public void setTextColor(ColorStateList colors) {
        super.setTextColor(colors);

        Logger.log("MyCustomRadioButton : setTextColorColorList " + colors  + "   " + getText() + "   " + Log.getStackTraceString(new Throwable()));

    }

    @Override
    public void setCompoundDrawablesWithIntrinsicBounds(@Nullable Drawable left, @Nullable Drawable top, @Nullable Drawable right, @Nullable Drawable bottom) {
        super.setCompoundDrawablesWithIntrinsicBounds(left, top, right, bottom);

        Logger.log("MyCustomRadioButton : setCompoundDrawablesWithIntrinsicBounds " + top  + "   " + getText() + "   " + Log.getStackTraceString(new Throwable()));
    }
}
