package com.ximalaya.ting.android.host.view.lrcview;

import androidx.annotation.NonNull;

/**
 * Create by {jian.kang} on 12/15/22
 *
 * <AUTHOR>
 */
public class WordInfoModel {
    public long start;
    public long end;
    public long duration;
    public String word;

    public WordInfoModel(long start, long end, String word) {
        this.start = start;
        this.end = end;
        this.duration = end - start;
        this.word = word;
    }

    @NonNull
    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("start = ").append(start)
                .append("; end = ").append(end)
                .append("; word = ").append(word);
        return builder.toString();
    }
}
