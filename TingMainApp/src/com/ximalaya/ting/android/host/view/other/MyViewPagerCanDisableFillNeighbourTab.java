package com.ximalaya.ting.android.host.view.other;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.viewpager.widget.ViewPagerCanDisableFillNeighbourTab;

/**
 *  fix pointerIndex out of range
 */

/**
 * <AUTHOR>
 */
public class MyViewPagerCanDisableFillNeighbourTab extends ViewPagerCanDisableFillNeighbourTab implements IViewPagerCanSlide {
   private boolean mCanSlide = true;

   private boolean mChildCanScroll = true; // 和mCanSlide不一样，mCanScroll用于ViewPager的嵌套滑动

   private ScrollListener mScrollListener;

   public interface ScrollListener {
       boolean canChildViewScroll(float x, float y);
   }

   public MyViewPagerCanDisableFillNeighbourTab(Context context) {
       super(context);
   }

   public MyViewPagerCanDisableFillNeighbourTab(Context context, AttributeSet attrs) {
       super(context, attrs);
   }

    public void setScrollListener(ScrollListener scrollListener) {
        mScrollListener = scrollListener;
    }

    @Override
   public boolean onTouchEvent(MotionEvent ev) {
       if (!mCanSlide) {
           return false;
       }
       try {
           return super.onTouchEvent(ev);
       } catch (IllegalArgumentException ex) {
           ex.printStackTrace();
       }
       return false;
   }

   @Override
   public boolean onInterceptTouchEvent(MotionEvent ev) {
       if (!mCanSlide) {
           return false;
       }
       try {
           if (mScrollListener != null
                   && mScrollListener.canChildViewScroll(ev.getX(), ev.getY())) {
               return false;
           }
           return super.onInterceptTouchEvent(ev);
       } catch (IllegalArgumentException ex) {
           ex.printStackTrace();
       }
       return false;
   }

   @Override
   public boolean hasFocus() {
       return false;
   }

   @Override
    public void setCanSlide(boolean canSlide) {
        this.mCanSlide = canSlide;
    }

    public void setChildCanScroll(boolean childCanScroll) {
        mChildCanScroll = childCanScroll;
    }

    @Override
    public boolean canScrollHorizontally(int direction) {
        if (!mChildCanScroll) {
            return false;
        }
        return super.canScrollHorizontally(direction);
    }
}
