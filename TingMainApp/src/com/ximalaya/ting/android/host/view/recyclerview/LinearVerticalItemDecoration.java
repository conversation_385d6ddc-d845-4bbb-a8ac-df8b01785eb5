package com.ximalaya.ting.android.host.view.recyclerview;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by WolfXu on 2019/4/19.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
public class LinearVerticalItemDecoration extends RecyclerView.ItemDecoration {
    private int mHalfSpacing;
    private int mMargin;

    public LinearVerticalItemDecoration(int spacing, int margin) {
        mHalfSpacing = spacing / 2;
        mMargin = margin;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        int position = parent.getChildAdapterPosition(view);
        outRect.top = mHalfSpacing;
        outRect.bottom = mHalfSpacing;
        if (position == 0) {
            outRect.top = mMargin;
        } else if (position == (parent.getAdapter().getItemCount() - 1)) {
            outRect.bottom = mMargin;
        }
    }
}