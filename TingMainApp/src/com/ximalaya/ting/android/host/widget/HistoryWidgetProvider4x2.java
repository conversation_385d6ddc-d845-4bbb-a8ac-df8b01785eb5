package com.ximalaya.ting.android.host.widget;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.RemoteViews;

import androidx.annotation.Nullable;
import androidx.collection.LruCache;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.opensdk.model.history.XmPlayRecord;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationStyleUtils;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationCreater;
import com.ximalaya.ting.android.opensdk.player.appnotification.XmNotificationPendingIntentCreateUtils;
import com.ximalaya.ting.android.opensdk.player.appwidget.BaseAppWidgetProvider;
import com.ximalaya.ting.android.opensdk.player.appwidget.BaseWidgetUtil;
import com.ximalaya.ting.android.opensdk.util.FileUtilBase;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.TrackUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForPlay;
import com.ximalaya.ting.android.routeservice.service.push.IWidgetService;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.app.XmAppHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2023/7/14
 * Description：
 */
public class HistoryWidgetProvider4x2 extends BaseAppWidgetProvider {
    private AppWidgetManager mAppWidgetManager;
    private static LruCache<String, Bitmap> mBitmapLruCache = new LruCache<>(16);
    private final AtomicInteger mGetBitmapCount = new AtomicInteger(0);
    private static volatile boolean isRequesting = false;

    public static final String TRACE_TAG = "HistoryWidgetProvider4x2";

    @Override
    public void onReceive(Context context, Intent intent) {
        mIsHistoryWidget = true;
        Logger.w("WidgetProvider", "onReceive action " + intent.getAction());
        super.onReceive(context, intent);
    }

    private RemoteViews bindWidgetPendingIntent(Context context) {
        try {
            RemoteViews remoteViews = new RemoteViews(context.getPackageName(), BaseWidgetUtil.useMatchParentLayout() ?
                    R.layout.host_reflect_appwidget_history_4x2_layout_match : R.layout.host_reflect_appwidget_history_4x2_layout);
            if (mAppWidgetManager == null) {
                mAppWidgetManager = AppWidgetManager.getInstance(context);
            }
            if (remoteViews != null) {
                setPendingIntent(context, remoteViews, R.id.host_widget_root, "我的历史", null, true);
                setPendingIntent(context, remoteViews, R.id.host_widget_album_layout1, "历史1", null, true);
                setPendingIntent(context, remoteViews, R.id.host_widget_album_layout2, "历史2", null, true);
                setPendingIntent(context, remoteViews, R.id.host_widget_album_layout3, "历史3", null, true);
                setPendingIntent(context, remoteViews, R.id.host_widget_album_layout4, "历史4", null, true);
                setPendingIntent(context, remoteViews, R.id.host_more_tv, "更多历史", null, true);
                Logger.i("WidgetProvider", "bindWidgetPendingIntent");
            }
            return remoteViews;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void setPendingIntent(Context context, RemoteViews remoteViews, int viewId, String area, AlbumM albumM, boolean goHome) {
        Intent openIntent = new Intent(Intent.ACTION_VIEW);
        if (albumM == null) {
            if (goHome) {
                // 没有历史  去首页
                openIntent.setData(Uri.parse("iting://open?msg_type=332&id=recommend"));
            } else {
                // 历史页
                openIntent.setData(Uri.parse("iting://open?msg_type=125&tab=history&guiyin=no_ggzs"));
            }
        } else {
            boolean isDailyNewsOld = albumM.getHistoryModel() != null &&
                    ((albumM.getHistoryModel().getType() == XmPlayRecord.RECORD_ONE_KEY_LISTEN) ||
                    (albumM.getHistoryModel().getTrack() != null &&
                            albumM.getHistoryModel().getTrack().getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY));
            if (isDailyNewsOld) {
                openIntent.setData(Uri.parse("iting://open?msg_type=74&channelGroupId=" +
                        albumM.getHistoryModel().getTrack().getChannelGroupId() + "&toChannelId=" +
                        albumM.getHistoryModel().getTrack().getChannelId()));
            } else {
                openIntent.setData(Uri.parse("iting://open?msg_type=11&guiyin=no_ggzs&album_id=" + albumM.getId()));
            }
        }
        openIntent.putExtra(XmNotificationCreater.EXTRA_FROM_DESK_WIDGET_PROVIDER, true);
        openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_PROVIDER_NAME, TRACE_TAG);
        openIntent.putExtra(XmNotificationCreater.EXTRA_DESK_WIDGET_FROM_OTHER_AREA, area);
        PendingIntent openPendingIntent = PendingIntent.getActivity(context, new Random().nextInt(3000), openIntent, XmNotificationPendingIntentCreateUtils.getPendingIntentFlag());
        remoteViews.setOnClickPendingIntent(viewId, openPendingIntent);
    }

    private void updateWidget(Context context) {
        boolean isAgree = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getBooleanCompat(
                PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PRIVACY_POLICY_AGREED_NEW);
        if (!isAgree) {
            RemoteViews remoteViews = bindWidgetPendingIntent(context);
            if (remoteViews == null) {
                return;
            }
            remoteViews.setViewVisibility(R.id.host_control_layout, View.GONE);
            remoteViews.setViewVisibility(R.id.host_default_layout, View.VISIBLE);
            if (BaseWidgetUtil.useMatchParentLayout()) {
                remoteViews.setTextViewText(R.id.host_tv_tip1, "请授权“喜马拉雅”\n以使用此小组件");
                remoteViews.setTextViewText(R.id.host_btn_tv, "去授权");
                remoteViews.setViewVisibility(R.id.host_tv_tip2, View.GONE);
            }
            commitUpdate(context, remoteViews);
            return;
        }
        if (isRequesting) {
            return;
        }
        isRequesting = true;
        XmAppHelper.runOnOnWorkThreadDelayed(() -> {
            isRequesting = false;
            setDataToView(context);
        }, 1000);
    }

    private void commitUpdate(Context context, RemoteViews remoteViews) {
        if (mAppWidgetManager == null) {
            return;
        }
        int[] appWidgetIds = new int[0];
        try {
            appWidgetIds = mAppWidgetManager.getAppWidgetIds(new ComponentName(context, getClass()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (appWidgetIds == null || appWidgetIds.length == 0) {
            return;
        }
        Logger.i("WidgetProvider", "commitUpdate");
        try {
            mAppWidgetManager.updateAppWidget(appWidgetIds, remoteViews);
        } catch (Exception e) {
            e.printStackTrace();
            //https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/1362?pid=1
        }
    }

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        super.onUpdate(context, appWidgetManager, appWidgetIds);
        mAppWidgetManager = appWidgetManager;
        try {
            if (appWidgetIds.length > 0) {
                BaseWidgetUtil.traceWidgetNewUpdate(TRACE_TAG);
                updateWidget(context);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDeleted(Context context, int[] appWidgetIds) {
        super.onDeleted(context, appWidgetIds);
        // 当控件被删除的时候调用该方法
        Logger.i("WidgetProvider", "onDeleted");
    }

    @Override
    public void onDisabled(Context context) {
        super.onDisabled(context);
        // 最后一个widget从屏幕移除
        traceWidget("卸载");
        MMKVUtil.getInstance().saveInt("history_widget_number_key_box_4x2", 0);
        Logger.i("WidgetProvider", "onDisabled");
        mBitmapLruCache = null;
    }

    @Override
    public void onEnabled(Context context) {
        super.onEnabled(context);
        mBitmapLruCache = new LruCache<>(16);
        // 第一个加入到屏幕上
        int num = MMKVUtil.getInstance().getInt("history_widget_number_key_box_4x2", 0);
        if (num == 0) {
            num++;
            MMKVUtil.getInstance().saveInt("history_widget_number_key_box_4x2", num);
        }
        traceWidget("添加");
        Logger.i("WidgetProvider", "onEnabled");
    }

    @Override
    protected void onUpdateWidget(Context context) {
        updateWidget(context);
        Logger.i("WidgetProvider", "onUpdateWidget");
    }

    @Override
    public void onInitUI(Context context, Track track) {
        Logger.i("WidgetProvider", "onInitUI");
    }

    private void setDataToView(Context context) {
        if (context == null) {
            return;
        }
        getHistoryData(new IDataCallBack<List<AlbumM>>() {
            @Override
            public void onSuccess(@Nullable List<AlbumM> data) {
                if (data != null && data.size() > 0) {
                    showAlbumLayout(context, data);
                } else {
                    showAlbumLayout(context, null);
                }
            }

            @Override
            public void onError(int code, String message) {
                showAlbumLayout(context, null);
            }
        });
    }

    private void showAlbumLayout(Context context, List<AlbumM> albumResults) {
        if (context == null || TextUtils.isEmpty(context.getPackageName()) || !context.getPackageName().equals("com.ximalaya.ting.android")) {
            return;
        }
        if (albumResults == null || albumResults.size() == 0) {
            RemoteViews remoteViews = bindWidgetPendingIntent(context);
            if (remoteViews == null) {
                return;
            }
            remoteViews.setViewVisibility(R.id.host_control_layout, View.GONE);
            remoteViews.setViewVisibility(R.id.host_default_layout, View.VISIBLE);
            setPendingIntent(context, remoteViews, R.id.host_widget_root, "我的历史", null, true);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout1, "历史1", null, true);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout2, "历史2", null, true);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout3, "历史3", null, true);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout4, "历史4", null, true);
            setPendingIntent(context, remoteViews, R.id.host_more_tv, "更多历史", null, true);
            commitUpdate(context, remoteViews);
            return;
        }
        mGetBitmapCount.set(albumResults.size());

        for (int i = 0; i < albumResults.size(); i++) {
            updateRemoteBitmapInner(context, albumResults.get(i).getValidCover(), albumResults);
        }
    }

    private void updateRemoteBitmapInner(final Context context, String url, List<AlbumM> albumResults) {
        if (mBitmapLruCache != null && mBitmapLruCache.get(url) != null) {
            Bitmap cacheMap = mBitmapLruCache.get(url);
            if (cacheMap != null && !cacheMap.isRecycled()) {
                if (mGetBitmapCount.decrementAndGet() <= 0) {
                    updateRemoteViewWithBitmapFinal(context, albumResults);
                }
                return;
            }
        }
        int width = NotificationStyleUtils.isVivoDevice() ? BaseWidgetUtil.dp2px(context, 37) : BaseWidgetUtil.dp2px(context, 62);
        try {
            long curTime = System.currentTimeMillis();
            FileUtilBase.getBitmapByUrlByUrl(true, context, url, width, width, bitmap ->
                    XmAppHelper.runOnWorkThread(() -> {
                        if (bitmap != null && !bitmap.isRecycled()) {
                            Bitmap cornerImage = null;
                            try {
                                cornerImage = NotificationStyleUtils.isVivoDevice() ? bitmap : BaseWidgetUtil.toRoundCornerImage(bitmap, BaseWidgetUtil.dp2px(context, 4), width, width);
                            } catch (Exception e) {
                                cornerImage = bitmap;
                                e.printStackTrace();
                            }
                            if (mBitmapLruCache != null && url != null) {
                                mBitmapLruCache.put(url, cornerImage);
                            }
                            Logger.log("WidgetProvider : time = " + (System.currentTimeMillis() - curTime));
                        }
                        if (mGetBitmapCount.decrementAndGet() <= 0) {
                            updateRemoteViewWithBitmapFinal(context, albumResults);
                        }
                    }));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateRemoteViewWithBitmapFinal(Context context, List<AlbumM> albumResults) {
        RemoteViews remoteViews = bindWidgetPendingIntent(context);
        if (remoteViews == null || albumResults == null) {
            return;
        }
        remoteViews.setViewVisibility(R.id.host_control_layout, View.VISIBLE);
        remoteViews.setViewVisibility(R.id.host_default_layout, View.GONE);
        setPendingIntent(context, remoteViews, R.id.host_more_tv, "更多历史", null, false);
        if (albumResults.size() == 1) {
            remoteViews.setViewVisibility(R.id.host_widget_album_layout1, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout2, View.INVISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout3, View.INVISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout4, View.INVISIBLE);
            remoteViews.setTextViewText(R.id.host_widget_album_title1, albumResults.get(0).getAlbumTitle());
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout1, "历史1", albumResults.get(0), false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout2, "我的历史", null, false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout3, "我的历史", null, false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout4, "我的历史", null, false);
            Bitmap bitmap1 = getBitmapFromCache(albumResults.get(0).getValidCover());
            if (bitmap1 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover1, bitmap1);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover1, "setImageResource", R.drawable.host_album_default_1_145);
            }
        } else if (albumResults.size() == 2) {
            remoteViews.setViewVisibility(R.id.host_widget_album_layout1, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout2, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout3, View.INVISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout4, View.INVISIBLE);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout1, "历史1", albumResults.get(0), false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout2, "历史2", albumResults.get(1), false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout3, "我的历史", null, false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout4, "我的历史", null, false);
            remoteViews.setTextViewText(R.id.host_widget_album_title1, albumResults.get(0).getAlbumTitle());
            remoteViews.setTextViewText(R.id.host_widget_album_title2, albumResults.get(1).getAlbumTitle());
            Bitmap bitmap1 = getBitmapFromCache(albumResults.get(0).getValidCover());
            if (bitmap1 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover1, bitmap1);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover1, "setImageResource", R.drawable.host_album_default_1_145);
            }
            Bitmap bitmap2 = getBitmapFromCache(albumResults.get(1).getValidCover());
            if (bitmap2 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover2, bitmap2);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover2, "setImageResource", R.drawable.host_album_default_1_145);
            }
        } else if (albumResults.size() == 3) {
            remoteViews.setViewVisibility(R.id.host_widget_album_layout1, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout2, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout3, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout4, View.INVISIBLE);
            remoteViews.setTextViewText(R.id.host_widget_album_title1, albumResults.get(0).getAlbumTitle());
            remoteViews.setTextViewText(R.id.host_widget_album_title2, albumResults.get(1).getAlbumTitle());
            remoteViews.setTextViewText(R.id.host_widget_album_title3, albumResults.get(2).getAlbumTitle());
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout1, "历史1", albumResults.get(0), false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout2, "历史2", albumResults.get(1), false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout3, "历史3", albumResults.get(2), false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout4, "我的历史", null, false);
            Bitmap bitmap1 = getBitmapFromCache(albumResults.get(0).getValidCover());
            if (bitmap1 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover1, bitmap1);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover1, "setImageResource", R.drawable.host_album_default_1_145);
            }
            Bitmap bitmap2 = getBitmapFromCache(albumResults.get(1).getValidCover());
            if (bitmap2 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover2, bitmap2);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover2, "setImageResource", R.drawable.host_album_default_1_145);
            }
            Bitmap bitmap3 = getBitmapFromCache(albumResults.get(2).getValidCover());
            if (bitmap3 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover3, bitmap3);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover3, "setImageResource", R.drawable.host_album_default_1_145);
            }
        } else if (albumResults.size() >= 4) {
            remoteViews.setViewVisibility(R.id.host_widget_album_layout1, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout2, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout3, View.VISIBLE);
            remoteViews.setViewVisibility(R.id.host_widget_album_layout4, View.VISIBLE);
            remoteViews.setTextViewText(R.id.host_widget_album_title1, albumResults.get(0).getAlbumTitle());
            remoteViews.setTextViewText(R.id.host_widget_album_title2, albumResults.get(1).getAlbumTitle());
            remoteViews.setTextViewText(R.id.host_widget_album_title3, albumResults.get(2).getAlbumTitle());
            remoteViews.setTextViewText(R.id.host_widget_album_title4, albumResults.get(3).getAlbumTitle());
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout1, "历史1", albumResults.get(0), false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout2, "历史2", albumResults.get(1), false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout3, "历史3", albumResults.get(2), false);
            setPendingIntent(context, remoteViews, R.id.host_widget_album_layout4, "历史4", albumResults.get(3), false);
            Bitmap bitmap1 = getBitmapFromCache(albumResults.get(0).getValidCover());
            if (bitmap1 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover1, bitmap1);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover1, "setImageResource", R.drawable.host_album_default_1_145);
            }
            Bitmap bitmap2 = getBitmapFromCache(albumResults.get(1).getValidCover());
            if (bitmap2 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover2, bitmap2);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover2, "setImageResource", R.drawable.host_album_default_1_145);
            }
            Bitmap bitmap3 = getBitmapFromCache(albumResults.get(2).getValidCover());
            if (bitmap3 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover3, bitmap3);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover3, "setImageResource", R.drawable.host_album_default_1_145);
            }
            Bitmap bitmap4 = getBitmapFromCache(albumResults.get(3).getValidCover());
            if (bitmap4 != null) {
                remoteViews.setImageViewBitmap(R.id.host_widget_album_cover4, bitmap4);
            } else {
                remoteViews.setInt(R.id.host_widget_album_cover4, "setImageResource", R.drawable.host_album_default_1_145);
            }
        }
        commitUpdate(context, remoteViews);
    }

    private Bitmap getBitmapFromCache(String url) {
        if (mBitmapLruCache != null && mBitmapLruCache.get(url) != null) {
            Bitmap cacheMap = mBitmapLruCache.get(url);
            if (cacheMap != null && !cacheMap.isRecycled()) {
                return cacheMap;
            }
        }
        return null;
    }

    public static void traceWidget(String type) {
        IWidgetService iWidgetService = RouterServiceManager.getInstance().getService(IWidgetService.class);
        if (iWidgetService != null) {
            iWidgetService.traceWidgetNewClick(TRACE_TAG, type);
        }
    }

    private void getHistoryData(IDataCallBack<List<AlbumM>> callBack) {
        if (callBack == null) {
            return;
        }
        List<HistoryModel> historyModels = new ArrayList<>();
        IHistoryManagerForPlay historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay.class);
        if (historyManager != null) {
            historyModels = historyManager.getTrackList();
        }
        if (historyModels == null || historyModels.size() == 0) {
            callBack.onError(0, "no history");
            return;
        }

        // 今日热点（一键听）历史过滤  过滤声音和今日热点频道下播放的广播
        // 旧版本android的一键听是按照普通声音上报 客户端自己做过滤 和ios逻辑不一致 这个版本开始统一 这里的过滤是为了过滤掉旧版本上报的数据
        HistoryModel onekeyHis = null;
        int size = historyModels.size();
        for (int i = size - 1; i >= 0; i--) {
            if (i >= 0 && i < historyModels.size()) {
                HistoryModel historyModel = historyModels.get(i);
                if ((historyModel != null && historyModel.getType() == XmPlayRecord.RECORD_ONE_KEY_LISTEN) ||
                        (historyModel != null && historyModel.getTrack() != null
                                && (historyModel.getTrack().getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY || historyModel.getTrack().getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4)) ||
                        (historyModel != null && historyModel.getRadio() != null && historyModel.getRadio().getChannelId() != 0)) {
                    if (onekeyHis == null) {
                        onekeyHis = historyModel;
                    } else if (onekeyHis.getEndedAt() < historyModel.getEndedAt()) {
                        historyModels.remove(onekeyHis);
                        onekeyHis = historyModel;
                    } else {
                        historyModels.remove(i);
                    }
                }
            }
        }

        HistoryModel quickListenHis = null;
        int quickListenSize = historyModels.size();
        for (int i = quickListenSize - 1; i >= 0; i--) {
            if (i >= 0 && i < historyModels.size()) {
                HistoryModel historyModel = historyModels.get(i);
                if (historyModel != null && (historyModel.getType() == XmPlayRecord.RECORD_QUICK_LISTEN) ||
                        historyModel != null && TrackUtil.isQuickListenTrack(historyModel.getTrack())) {
                    if (quickListenHis == null) {
                        quickListenHis = historyModel;
                    } else if (quickListenHis.getEndedAt() < historyModel.getEndedAt()) {
                        historyModels.remove(quickListenHis);
                        quickListenHis = historyModel;
                    } else {
                        historyModels.remove(i);
                    }
                }
            }
        }

        // 助眠声音过滤
        HistoryModel sleepTrackHis = null;
        size = historyModels.size();
        for (int i = size - 1; i >= 0; i--) {
            if (i >= 0 && i < historyModels.size()) {
                try {
                    HistoryModel historyModel = historyModels.get(i);
                    boolean localSleepModelTrack = historyModel != null && historyModel.getTrack() != null
                            && !TextUtils.isEmpty(historyModel.getTrack().getKind())
                            && historyModel.getTrack().getKind().equals(PlayableModel.KIND_MODE_SLEEP);
                    boolean netSleepModelTrack = historyModel != null && historyModel.getAlbumId() == AppConstants.ALBUM_SLEEP_MODE_ID_FOR_HISTORY;
                    if (localSleepModelTrack || netSleepModelTrack) {
                        if (sleepTrackHis == null) {
                            sleepTrackHis = historyModel;
                        } else if (sleepTrackHis.getEndedAt() < historyModel.getEndedAt()) {
                            historyModels.remove(sleepTrackHis);
                            sleepTrackHis = historyModel;
                        } else {
                            historyModels.remove(i);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        // 直播、广播过滤
        size = historyModels.size();
        for (int i = size - 1; i >= 0; i--) {
            if (i >= 0 && i < historyModels.size()) {
                HistoryModel model = historyModels.get(i);
                if (model.getTrack() == null ||
                        ((model.getTrack() != null && model.getTrack().isKindOfLive()) ||
                                model.isRadio ||
                                model.getType() == XmPlayRecord.RECORD_LIVE ||
                                model.getType() == XmPlayRecord.RECORD_MYCLUB_LIVE ||
                                model.getType() == XmPlayRecord.RECORD_LOCAL_BOOK)) {
                    historyModels.remove(model);
                }
            }
        }


        // 异常专辑过滤
        size = historyModels.size();
        for (int i = size - 1; i >= 0; i--) {
            if (i >= 0 && i < historyModels.size()) {
                HistoryModel model = historyModels.get(i);
                boolean isTrackAccess = model.getTrack() != null && (model.getTrack().getAlbum() == null
                        || model.getTrack().getAlbum().getAlbumId() == 0
                        || TextUtils.isEmpty(model.getTrack().getAlbum().getAlbumTitle()));
                if (isTrackAccess) {
                    historyModels.remove(model);
                }
            }
        }

        CopyOnWriteArrayList<HistoryModel> copyTracks = new CopyOnWriteArrayList<>(historyModels);
//        for (HistoryModel track : copyTracks) {
//            boolean isTrackAccess = track.getTrack() != null && (track.getTrack().getAlbum() == null
//                    || track.getTrack().getAlbum().getAlbumId() == 0
//                    || TextUtils.isEmpty(track.getTrack().getAlbum().getAlbumTitle()));
//            if (isTrackAccess) {
//                Map<String, String> specificParams = new HashMap<>();
//                specificParams.put("device", "android");
//                specificParams.put("trackId", track.getTrack().getDataId() + "");
//                TrackM trackM = CommonRequestM.getTrackInfoDetailSyncForCar(specificParams);
//                if (trackM != null && trackM.getAlbum() != null && trackM.getAlbum().getAlbumId() > 0)
//                    track.getTrack().setAlbum(trackM.getAlbum());
//            }
//        }
        List<AlbumM> albumMArrayList = new ArrayList<>();
        for (HistoryModel historyModel : copyTracks) {
            if (historyModel != null && !historyModel.isDeleted() && historyModel.getTrack() != null) {
                AlbumM albumM = new AlbumM();
                albumM.setHistoryModel(historyModel);
                albumM.setId(historyModel.getAlbumId());
                albumM.setAlbumTitle("私人FM".equals(historyModel.getAlbumTitle()) ? "一键听" : historyModel.getAlbumTitle());
                if (historyModel.getTrack() != null) {
                    albumM.setCoverUrlMiddle(historyModel.getTrack().getValidCover());
                }
                albumMArrayList.add(albumM);
                if (albumMArrayList.size() == 4) {
                    break;
                }
            }
        }
        callBack.onSuccess(albumMArrayList);
    }
}