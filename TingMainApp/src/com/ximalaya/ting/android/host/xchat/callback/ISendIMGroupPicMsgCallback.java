package com.ximalaya.ting.android.host.xchat.callback;

import com.ximalaya.ting.android.host.xchat.model.message.GroupChatMessage;

/**
 * 发送私信图片消息的回调
 * <p>
 * 由于图片消息会经历上传操作，回调中的操作会体现上传结果
 *
 * <AUTHOR>
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2019-07-02
 */
public interface ISendIMGroupPicMsgCallback {

    /**
     * 完成图片初始处理后的失败回调
     * 包含查看文件是否存在、解析大小、数据库入库
     *
     * @param errorCode 错误码
     * @param errorMsg  错误信息
     */
    void onPreProcessFail(int errorCode, String errorMsg);

    /**
     * 完成图片初始处理后的完成回调
     * 包含查看文件是否存在、解析大小、数据库入库
     *
     * @param sendingMsg 预处理完成后显示的消息内容，此时上传未能完成
     */
    void onPreProcessDone(GroupChatMessage sendingMsg);


    /**
     * 上传成功回调
     *
     * @param sendingMsg 发送消息，完成上传后内容更新
     */
    void onUploadSuccess(GroupChatMessage sendingMsg);

    /**
     * 上传失败回调
     *
     * @param sendUniqueId 发送操作的uid
     */
    void onUploadFail(long sendUniqueId);


    /**
     * 获取上传进度
     *
     * @param progress 上传进度
     */
    void onGetUploadProgress(int progress);

    /**
     * 发送消息成功回调
     *
     * @param message 消息
     */
    void onSuccess(GroupChatMessage message);

    /**
     * 发送消息失败回调
     *
     * @param sendUniqueId 发送操作的uid
     * @param errorCode    错误码
     * @param errorMsg     错误描述
     */
    void onError(long sendUniqueId, int errorCode, String errorMsg);
}
