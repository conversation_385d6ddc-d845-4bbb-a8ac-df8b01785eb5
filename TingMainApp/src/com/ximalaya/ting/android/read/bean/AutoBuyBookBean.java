package com.ximalaya.ting.android.read.bean;

import java.util.List;

/**
 * Created by zk on 2020-05-22
 * 自动购买书籍列表
 */
public class AutoBuyBookBean {

    /**
     * code : 200
     * data : [{"book_id":603,"book_name":"契约娇妻：总裁老公宠上天","pen_name":"筱桃"},{"book_id":583,
     * "book_name":"秀色田妻有情郎",
     * "pen_name":"咸菜菜"}]
     * msg : 请求成功
     * timestampName : 1577864563
     */

    private int code;
    private String msg;
    private int timestampName;
    private List<DataBean> data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getTimestampName() {
        return timestampName;
    }

    public void setTimestampName(int timestampName) {
        this.timestampName = timestampName;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * book_id : 603
         * book_name : 契约娇妻：总裁老公宠上天
         * pen_name : 筱桃
         */

        private int bookId;
        private String bookName;
        private String penName;

        public int getBookId() {
            return bookId;
        }

        public void setBookId(int bookId) {
            this.bookId = bookId;
        }

        public String getBookName() {
            return bookName;
        }

        public void setBookName(String bookName) {
            this.bookName = bookName;
        }

        public String getPenName() {
            return penName;
        }

        public void setPenName(String penName) {
            this.penName = penName;
        }
    }
}