package com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.paint;


import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.hyper.HyperInformation;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.structure.ExtraInformation;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.style.StyleModel;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.style.item.Img;
import com.ximalaya.ting.android.read.widgets.epub.lib.epub.model.table.TableInformation;

/**
 * Created by 5Greatest on 2020.09.25
 *
 * <AUTHOR>
 * On 2020-09-25
 */
public class EpubPaint extends BasePaint {
    private HyperInformation hyperInformation;
    // 不向后传播
    private TableInformation tableInformation;
    private StyleModel styleModel;
    //floating<0  LEFT
    //floating>0  RIGHT
    private short floating = 0;
    private boolean small = false;
    private Img img = null;

    private ExtraInformation extraInformation = null;

    public EpubPaint() {
    }

    public EpubPaint(EpubPaint paint) {
        super(paint);
        if (null != paint.getHyperInformation() && null != paint.getHyperInformation().getHref()) {
            hyperInformation = new HyperInformation(paint.getHyperInformation().getHref(), null);
        }
        if (null == paint.getStyleModel()) {
            styleModel = new StyleModel();
        } else {
            styleModel = new StyleModel(paint.getStyleModel());
        }
        if (null != paint.getImg()) {
            img = new Img(paint.getImg());
        }
        this.floating = paint.getFloating();
        this.small = paint.isSmall();
    }


//    public void genePaint(EpubPaint paint) {
//        this.sizeRatio = paint.getSizeRatio();
//        this.absSize = paint.getAbsSize();
//        this.halfLineSpacingRatio = paint.getHalfLineSpacingRatio();
//        this.absHalfLineSpacing = paint.getAbsHalfLineSpacing();
//        this.textAlign.setAlign(paint.getAlign());
//        this.intent = paint.getIntent();
//        this.absIntent = paint.getAbsIntent();
//
//
//        if (null != paint.getHyperInformation() && null != paint.getHyperInformation().getHref()) {
//            hyperInformation = new HyperInformation(paint.getHyperInformation().getHref(), null);
//        }
//        if (null == paint.getStyleModel()) {
//            styleModel = new StyleModel();
//        } else {
//            styleModel = new StyleModel(paint.getStyleModel());
//        }
//        if (null != paint.getImg()) {
//            img = new Img(paint.getImg());
//        }
//        this.floating = paint.getFloating();
//        this.small = paint.isSmall();
//
//
//    }


    public short getFloating() {
        return floating;
    }

    public void setFloating(short floating) {
        this.floating = floating;
    }

    public boolean isSmall() {
        return small;
    }

    public void setSmall(boolean small) {
        this.small = small;
    }

    public Img getImg() {
        return img;
    }

    public void setImg(Img img) {
        this.img = img;
    }

    public StyleModel getStyleModel() {
        return styleModel;
    }

    public ExtraInformation getExtraInformation() {
        return extraInformation;
    }

    public void setExtraInformation(ExtraInformation extraInformation) {
        this.extraInformation = extraInformation;
    }

    public HyperInformation getHyperInformation() {
        return hyperInformation;
    }

    public void setHyperInformation(HyperInformation hyperInformation) {
        this.hyperInformation = hyperInformation;
    }

    public TableInformation getTableInformation() {
        return tableInformation;
    }

    public void setTableInformation(TableInformation tableInformation) {
        this.tableInformation = tableInformation;
    }
}
