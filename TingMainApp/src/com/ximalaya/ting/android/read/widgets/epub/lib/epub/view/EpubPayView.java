package com.ximalaya.ting.android.read.widgets.epub.lib.epub.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.read.IReadFunctionAction;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.read.router.ReadBundleHostRouter;
import com.ximalaya.ting.android.host.read.router.callback.IPageStyle;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.read.bean.ConfigCenterBean;
import com.ximalaya.ting.android.read.listener.IReaderBottomAdCallback;
import com.ximalaya.ting.android.read.utils.LogUtils;
import com.ximalaya.ting.android.read.utils.StringUtils;
import com.ximalaya.ting.android.read.utils.XMUtils;
import com.ximalaya.ting.android.read.widgets.epub.entity.ChapterData;
import com.ximalaya.ting.android.read.widgets.epub.lib.commen.model.info.PaginationInfo;
import com.ximalaya.ting.android.read.widgets.epub.lib.commen.model.info.SizeInfo;
import com.ximalaya.ting.android.read.widgets.epub.reader.listener.IEpubPayViewListener;
import com.ximalaya.ting.android.read.widgets.pageview.ReadSettingManager;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * epub支付的View
 */
public class EpubPayView extends FrameLayout {

    private Context mContext;
    private ChapterData mChapterData;
    private final IPageStyle mPageStyle;
    private IEpubPayViewListener mListener;

    private RoundImageView mImgFreeCard;//已经不再使用，可以剔除
    private TextView mTvFreeCard;  //已经不再使用，可以剔除
    private TextView mTvPayTips;
    private View mLineLeft;
    private View mLineRight;
    private TextView mTvBalance;
    private TextView mTvLoginState;
    private CheckBox mCbAutoBuy;
    private LinearLayout mBuyContainer;
    private TextView mTvBuyBook;
    private TextView mTvPrice;
    private LinearLayout mBuyBatchContainer;
    private TextView mTvBuyBatch;

    // 继续免费阅读
    private FrameLayout mFlContinueFreeRead;

    public void setEpubPayViewListener(IEpubPayViewListener listener) {
        mListener = listener;
    }

    public ChapterData getChapterData() {
        return mChapterData;
    }

    public void setChapterData(ChapterData chapterData) {
        mChapterData = chapterData;
        dealPayView(mContext);
    }

    public EpubPayView(Context context) {
        this(context, null);
        this.mContext = context;
    }

    public EpubPayView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EpubPayView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mPageStyle = ReadSettingManager.getInstance().getPageStyle();
        initView(context);
    }

    private void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.read_layout_epub_pay_page, this);
        mImgFreeCard = findViewById(R.id.img_free_card);
        mTvFreeCard = findViewById(R.id.tv_free_card);
        mTvPayTips = findViewById(R.id.tv_pay_tips);
        mLineLeft = findViewById(R.id.line_left);
        mLineRight = findViewById(R.id.line_right);
        mTvBalance = findViewById(R.id.tv_balance);
        mTvLoginState = findViewById(R.id.tv_login_state);
        mCbAutoBuy = findViewById(R.id.cb_auto_buy);
        mBuyContainer = findViewById(R.id.buyContainer);
        mTvBuyBook = findViewById(R.id.tv_buy_this_chapter);
        mTvPrice = findViewById(R.id.tv_price);
        mBuyBatchContainer = findViewById(R.id.buyBatchContainer);
        mTvBuyBatch = findViewById(R.id.tv_buy_batch);
        mFlContinueFreeRead = findViewById(R.id.read_fl_continue_free_read);
    }

    /**
     * 处理Epub控件
     */
    private void dealPayView(Context context) {
        LogUtils.e("reader", "dealPayView");
        if (mChapterData == null) {
            return;
        }
        if (mPageStyle != null) {
            mTvPayTips.setTextColor(mPageStyle.getTipStateColor());

            mTvBalance.setTextColor(mPageStyle.getFontColor(context));
            mTvFreeCard.setTextColor(mPageStyle.getStateBuyColor(context));

            mTvLoginState.setTextColor(mPageStyle.getLoginStateColor());
            mCbAutoBuy.setTextColor(mPageStyle.getTvAlphaColor(context));
            mCbAutoBuy.setButtonDrawable(mPageStyle.getPayPageCheckBoxDrawLeft(context));
            mLineLeft.setBackgroundColor(mPageStyle.getTipStateColor());
            mLineRight.setBackgroundColor(mPageStyle.getTipStateColor());
        }

        IReadFunctionAction functionActionNoCallBack = ReadBundleHostRouter.getFunctionActionNoCallBack();
        ConfigCenterBean.BtnTitleBean titleBean = null;
        if (functionActionNoCallBack != null) {
            titleBean = functionActionNoCallBack.getPayButtonConfig();
        }
        if (titleBean == null) {
            return;
        }
        boolean hasLogin = UserInfoMannage.hasLogined();
        //用户昵称
        String userNickName = "";
        LoginInfoModelNew user = UserInfoMannage.getInstance().getUser();
        if (user != null && !TextUtils.isEmpty(user.getNickname())) {
            userNickName = user.getNickname();
        }
        if (mChapterData.showVipBox) {
            int bookBuyType = mChapterData.bookBuyType;
            if (bookBuyType == 1) {//按章
                mCbAutoBuy.setVisibility(VISIBLE);
                mTvPrice.setVisibility(VISIBLE);
                mTvBalance.setVisibility(VISIBLE);
                mTvLoginState.setVisibility(VISIBLE);
                mBuyBatchContainer.setVisibility(GONE);
                mTvPrice.setText(String.format("%s喜点", StringUtils.get2RadixPointWithout0(String.valueOf(mChapterData != null ? mChapterData.totalPrice : 0))));
                if (hasLogin) {
                    mTvBalance.setText(String.format("余额：%s喜点", StringUtils.get2RadixPoint(String.valueOf(mChapterData.xiCoin))));
                    mTvLoginState.setText(String.format("（%s）", userNickName));
                    if (mChapterData.xiCoin < mChapterData.totalPrice) {
                        mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getChapterBuy02()) ? titleBean.getChapterBuy02() : "充值并购买本章");
                    } else {
                        mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getChapterBuy01()) ? titleBean.getChapterBuy01() : "购买本章");
                    }
                } else {
                    mTvBalance.setText(String.format("余额：%s喜点", 0));
                    mTvLoginState.setText("（未登录）");
                    mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getChapterBuy01()) ? titleBean.getChapterBuy01() : "购买本章");
                }

                if (titleBean != null && !TextUtils.isEmpty(titleBean.getVipBuy())) {
                    mTvBuyBatch.setText(titleBean.getVipBuy());
                }

            } else if (bookBuyType == 2) {//按本
                mCbAutoBuy.setVisibility(GONE);
                mTvPrice.setVisibility(VISIBLE);
                mTvBalance.setVisibility(VISIBLE);
                mTvLoginState.setVisibility(VISIBLE);
                mBuyBatchContainer.setVisibility(GONE);
                mTvPrice.setText(String.format("%s喜点", StringUtils.get2RadixPointWithout0(String.valueOf(mChapterData != null ? mChapterData.totalPrice : 0))));
                if (hasLogin) {
                    mTvBalance.setText(String.format("余额：%s喜点", StringUtils.get2RadixPoint(String.valueOf(mChapterData.xiCoin))));
                    mTvLoginState.setText(String.format("（%s）", userNickName));
                    if (mChapterData.xiCoin < mChapterData.totalPrice) {
                        mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getBookBuy02()) ? titleBean.getBookBuy02() : "充值并购买本书");
                    } else {
                        mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getBookBuy01()) ? titleBean.getBookBuy01() : "购买本书");
                    }
                } else {
                    mTvBalance.setText(String.format("余额：%s喜点", 0));
                    mTvLoginState.setText("（未登录）");
                    mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getBookBuy01()) ? titleBean.getBookBuy01() : "购买本书");
                }

                if (titleBean != null && !TextUtils.isEmpty(titleBean.getVipBuy())) {
                    mTvBuyBatch.setText(titleBean.getVipBuy());
                }

            } else if (bookBuyType == 3) {//vip
                mCbAutoBuy.setVisibility(GONE);
                mTvPrice.setVisibility(GONE);
                mTvBalance.setVisibility(GONE);
                mTvLoginState.setVisibility(GONE);
                mBuyContainer.setVisibility(View.GONE);
                mBuyBatchContainer.setVisibility(VISIBLE);
                if (hasLogin) {
                    mTvBalance.setText(String.format("余额：%s喜点", StringUtils.get2RadixPoint(String.valueOf(mChapterData.xiCoin))));
                    if (!TextUtils.isEmpty(userNickName)) {
                        mTvLoginState.setText(String.format("（%s）", userNickName));
                    }
                } else {
                    mTvBalance.setText(String.format("余额：%s喜点", 0));
                    mTvLoginState.setText("登录");
                }
                mTvBuyBatch.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getVipBuy()) ? titleBean.getVipBuy() : "会员免费看");
            } else if (bookBuyType == 4) {//vip和按章
                mCbAutoBuy.setVisibility(VISIBLE);
                mTvPrice.setVisibility(VISIBLE);
                mTvBalance.setVisibility(VISIBLE);
                mTvLoginState.setVisibility(VISIBLE);
                mBuyContainer.setVisibility(VISIBLE);
                mBuyBatchContainer.setVisibility(VISIBLE);
                mTvPrice.setText(String.format("%s喜点", StringUtils.get2RadixPointWithout0(String.valueOf(mChapterData != null ? mChapterData.totalPrice : 0))));
                if (hasLogin) {
                    mTvBalance.setText(String.format("余额：%s喜点", StringUtils.get2RadixPoint(String.valueOf(mChapterData.xiCoin))));
                    mTvLoginState.setText(String.format("（%s）", userNickName));
                    if (mChapterData.xiCoin < mChapterData.totalPrice) {
                        mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getChapterBuy02()) ? titleBean.getChapterBuy02() : "充值并购买本章");
                    } else {
                        mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getChapterBuy01()) ? titleBean.getChapterBuy01() : "购买本章");
                    }
                } else {
                    mTvBalance.setText(String.format("余额：%s喜点", 0));
                    mTvLoginState.setText("登录");
                    mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getChapterBuy01()) ? titleBean.getChapterBuy01() : "购买本章");
                }
                mTvBuyBatch.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getVipBuy()) ? titleBean.getVipBuy() : "会员免费看");

            } else if (bookBuyType == 5) {//vip和按本
                mTvPrice.setVisibility(VISIBLE);
                mTvBalance.setVisibility(VISIBLE);
                mTvLoginState.setVisibility(VISIBLE);
                mBuyContainer.setVisibility(VISIBLE);
                mBuyBatchContainer.setVisibility(VISIBLE);
                mTvPrice.setText(String.format("%s喜点", StringUtils.get2RadixPointWithout0(String.valueOf(mChapterData != null ? mChapterData.totalPrice : 0))));
                if (hasLogin) {
                    mTvBalance.setText(String.format("余额：%s喜点", StringUtils.get2RadixPoint(String.valueOf(mChapterData.xiCoin))));
                    mTvLoginState.setText(String.format("（%s）", userNickName));
                    if (mChapterData.xiCoin < mChapterData.totalPrice) {
                        mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getBookBuy02()) ? titleBean.getBookBuy02() : "充值并购买本书");
                    } else {
                        mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getBookBuy01()) ? titleBean.getBookBuy01() : "购买本书");
                    }
                } else {
                    mTvBalance.setText(String.format("余额：%s喜点", 0));
                    mTvLoginState.setText("登录");
                    mTvBuyBook.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getBookBuy01()) ? titleBean.getBookBuy01() : "购买本书");
                }
                mTvBuyBatch.setText(titleBean != null && !TextUtils.isEmpty(titleBean.getVipBuy()) ? titleBean.getVipBuy() : "会员免费看");
            }
            mBuyContainer.setOnClickListener(v -> {
                if (mListener != null) {
                    mListener.onBuyWholeBook(mChapterData != null && mChapterData.xiCoin >= mChapterData.totalPrice,
                            mTvBuyBook != null && mTvBuyBook.getText() != null ? mTvBuyBook.getText().toString() : "",
                            String.valueOf(mChapterData != null ? mChapterData.totalPrice : 0));
                }
            });
            mBuyBatchContainer.setOnClickListener(v -> {
                if (XMUtils.isFastClick()) {
                    return;
                }
                if (mListener != null) {
                    mListener.onBuyVip(
                            mTvBuyBatch != null && mTvBuyBatch.getText() != null ? mTvBuyBatch.getText().toString() : "",
                            "0");
                }
            });

            mTvLoginState.setOnClickListener(v -> {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(getContext());
                }
            });
        }

        if (mChapterData != null && mChapterData.chapterInfo != null) {
            // 阅读器付费页  页面展示
            new XMTraceApi.Trace()
                    .pageView(44647, "readerPay")
                    .put("currPage", "readerPay")
                    .put("chapterId", String.valueOf(mChapterData.chapterInfo.chapterId))
                    .put("bookId", String.valueOf(mChapterData.chapterInfo.bookId))
                    .createTrace();
        }

//        if (mChapterData != null) {
//            // true 代表 只显示继续阅读  false 代表 显示 付费按钮、 vip充值按钮 、继续阅读
//            if (mChapterData.isShowContinueFreeButton()) {
//                mTvBalance.setVisibility(View.GONE);
//                mTvLoginState.setVisibility(View.GONE);
//                mCbAutoBuy.setVisibility(View.GONE);
//                mBuyContainer.setVisibility(View.GONE);
//                mBuyBatchContainer.setVisibility(View.GONE);
//            }
//
//
//            IReaderBottomAdCallback iReaderBottomAdCallback = new IReaderBottomAdCallback() {
//
//                @Nullable
//                @Override
//                public String bookTitle() {
//                    return mChapterData.bookName;
//                }
//
//                @Nullable
//                @Override
//                public String bookAuthor() {
//                    return mChapterData.bookAuthor;
//                }
//
//                @Nullable
//                @Override
//                public String bookChapter() {
//                    return mChapterData.chapterInfo != null ? mChapterData.chapterInfo.title : "";
//                }
//
//                @Nullable
//                @Override
//                public String bookCover() {
//                    return mChapterData.bookCover;
//                }
//            };
//            ReadBundleHostRouter.getFunctionAction(new ReadBundleHostRouter.OnLoadReadBundleFunctionAction() {
//                @Override
//                public void onLoadReadBundleActivityAction(@Nullable IReadFunctionAction onAction) {
//                    if (onAction != null) {
//                        onAction.addDrainageManager_addContinueFreeReadBtnView(mFlContinueFreeRead, mChapterData.isShowVipBox(), iReaderBottomAdCallback);
//                    }
//                }
//            });
//        }

    }

    public void setPaginationInfo(PaginationInfo paginationInfo) {
    }

    public void setSizeInfo(SizeInfo sizeInfo) {
    }
}