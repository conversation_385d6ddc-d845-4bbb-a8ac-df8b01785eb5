package com.ximalaya.ting.android.record.data.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by ervin.li on 2018/4/26.
 * <AUTHOR>
 */
/**
 * @param <T> 数据元素
 */
public class PageList<T> {
    private boolean hasMore;
    private int pageSize;
    private int pageNo;
    private int totalPage;
    private int totalCount;
    @SerializedName(value = "result" ,alternate = {"dubWorkInfos"})
    private List<T> result;

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<T> getResult() {
        return result;
    }

    public void setResult(List<T> result) {
        this.result = result;
    }

    public boolean hasMore() {
        return pageNo < totalPage || hasMore;
    }

}
