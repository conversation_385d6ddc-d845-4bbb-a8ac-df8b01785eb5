package com.ximalaya.ting.android.ad.manager;

import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.NoLoadThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.AbstractRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.NoLoadAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.XmRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.XmNativeAd;
import com.ximalaya.ting.android.adsdk.base.log.AdLogger;
import com.ximalaya.ting.android.adsdk.util.EncryptPriceUtils;
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class HostCommonRtbSortUtil {

    public static boolean commonRtbAdEnable(List<Advertis> adList) {
        for (Advertis advertis : adList) {
            if (advertis == null) {
                continue;
            }
            if (advertis.isMobileRtb()) {
                return true;
            }
        }
        return false;
    }

    public static boolean rtbRewardAdEnable(List<AdUnLockVipTrackAdvertis> adList) {
        for (Advertis advertis : adList) {
            if (advertis == null) {
                continue;
            }
            if (advertis.isMobileRtb()) {
                return true;
            }
        }
        return false;
    }

    public static List<AbstractThirdAd> sortAbstractThirdAdModel(List<AbstractThirdAd> adxAdModelList) {
        try {
            ArrayList<AbstractThirdAd> sortArrayList = new ArrayList<>(adxAdModelList);
            Collections.sort(sortArrayList, new Comparator<AbstractThirdAd>() {
                @Override
                public int compare(AbstractThirdAd o1, AbstractThirdAd o2) {
                    try {
                        if (ConstantsOpenSdk.isDebug) {
                            Logger.w("--------msg_rtb", "------- compare o1 --- " + o1 +
                                    ", adid = " + o1.getAdvertis().getAdid() +
                                    ", adtype = " + o1.getAdvertis().getAdtype() +
                                    ", isCache = " + o1.getAdvertis().isCache() +
                                    ", level = " + o1.getAdvertis().getRankLevel() +
                                    ", price = " + o1.getRtbPrice() +
                                    ", minPrice = " + o1.getAdvertis().getRtbBaseMinPrice() +
                                    ", limitPrice = " + o1.getAdvertis().getSdkBaseLimitPrice() +
                                    ", sdkCallBackStatus = " + o1.getSdkCallBackStatus() +
                                    ", dspResult = " + o1.getDspResult());
                            Logger.w("--------msg_rtb", "------- compare o2 --- " + o2 +
                                    ", adid = " + o2.getAdvertis().getAdid() +
                                    ", adtype = " + o2.getAdvertis().getAdtype() +
                                    ", isCache = " + o1.getAdvertis().isCache() +
                                    ", level = " + o2.getAdvertis().getRankLevel() +
                                    ", price = " + o2.getRtbPrice() +
                                    ", minPrice = " + o2.getAdvertis().getRtbBaseMinPrice() +
                                    ", limitPrice = " + o2.getAdvertis().getSdkBaseLimitPrice() +
                                    ", sdkCallBackStatus = " + o2.getSdkCallBackStatus() +
                                    ", dspResult = " + o2.getDspResult());
                            boolean cacheWin = false;
                            try {
                                cacheWin = "1".equals(ToolUtil.getSystemProperty("debug.rtb.cache", "-1"));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            // 开关直接让缓存竞胜
                            if (cacheWin) {
                                if (o1.isCached()) {
                                    Logger.w("--------msg_rtb", "------- compare o1 cache win --- ");
                                    return -1;
                                } else if (o2.isCached()) {
                                    Logger.w("--------msg_rtb", "------- compare o2 cache win --- ");
                                    return 1;
                                }
                            }
                        }
                        
                        if (o1.getSdkCallBackStatus() == AbstractThirdAd.SDK_CALL_BACK_STATUS_SDK_PRICE_LOW_MIN_BASE_PRICE
                                || o1.getDspResult() == AbstractThirdAd.RESULT_TYPE_PRICE_LOW_MIN_BASE_PRICE) {
                            Logger.w("--------msg_rtb", "------- compare o2 win--- ");
                            return 1;
                        }

                        if (o2.getSdkCallBackStatus() == AbstractThirdAd.SDK_CALL_BACK_STATUS_SDK_PRICE_LOW_MIN_BASE_PRICE
                                || o2.getDspResult() == AbstractThirdAd.RESULT_TYPE_PRICE_LOW_MIN_BASE_PRICE) {
                            Logger.w("--------msg_rtb", "------- compare o1 win--- ");
                            return -1;
                        }

                        if ((o1 instanceof NoLoadThirdAd && o2 instanceof NoLoadThirdAd) ||
                                (o1 instanceof NoLoadAd && o2 instanceof NoLoadAd)) {
                            Logger.w("--------msg_rtb", "------- compare o1 == 02--- ");
                            return 0;
                        }
                        if (o1 instanceof NoLoadThirdAd || o1 instanceof NoLoadAd) {
                            Logger.w("--------msg_rtb", "------- compare o2 win--- ");
                            return 1;
                        }
                        if (o2 instanceof NoLoadThirdAd || o2 instanceof NoLoadAd) {
                            Logger.w("--------msg_rtb", "------- compare o1 win--- ");
                            return -1;
                        }
                        if (o1.getAdvertis() != null && o2.getAdvertis() != null) {
                            if ((o2.getAdvertis().getRankLevel() == o1.getAdvertis().getRankLevel())) {
                                if (o2.getRtbPrice() > o1.getRtbPrice()) {
                                    Logger.w("--------msg_rtb", "------- compare o2 win--- ");
                                    return 1;
                                }
                                if (o2.getRtbPrice() < o1.getRtbPrice()) {
                                    Logger.w("--------msg_rtb", "------- compare o1 win--- ");
                                    return -1;
                                }
                                return 0;
                            }
                            return o2.getAdvertis().getRankLevel() - o1.getAdvertis().getRankLevel();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        AdLogger.eToApm(e);
                    }
                    return 0;

                }
            });
            return sortArrayList;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return adxAdModelList;
    }


    public static List<Advertis> sortAdxAdModel(List<Advertis> adxAdList) {

        try {
            ArrayList<Advertis> sortArrayList = new ArrayList<>(adxAdList);
            Collections.sort(sortArrayList, new Comparator<Advertis>() {
                @Override
                public int compare(Advertis o1, Advertis o2) {
                    try {
                        return (int) (o2.getPrice() - o1.getPrice());
//                    return (int) (getAdDecryptPrice(o2) - getAdDecryptPrice(o1));
                    } catch (Exception e) {
                        e.printStackTrace();
                        AdLogger.eToApm(e);
                    }
                    return 0;
                }
            });
            return sortArrayList;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return adxAdList;
    }

    public static Advertis getBestAdxAd(List<Advertis> adxAdList) {
        try {
            if (adxAdList != null && !adxAdList.isEmpty()) {
                printList(adxAdList, " getBestAdxAd - 排序前");
                sortAdxAdModel(adxAdList);
                printList(adxAdList, " getBestAdxAd - 排序后 ------- -- --");
                return adxAdList.get(0);
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return null;
    }

    public static AbstractThirdAd createXmRtbAd(Advertis advertis) {
        if (advertis == null) {
            return null;
        }
        return XmNativeAd.createXmNativeAdByAdvertis(advertis);
    }

    public static AbstractRewardVideoAd<Advertis> createXmRewardRtbAd(Advertis advertis) {
        if (advertis == null) {
            return null;
        }
        return new XmRewardVideoAd(advertis, advertis, "0");
    }


    public static void printList(List<Advertis> printList, String tagStr) {
        AdLogger.iToApm("--------msg_rtb", " ------------ printList adModel start -------------  " + tagStr);
        for (Advertis advertis : printList) {
            if (advertis == null) {
                AdLogger.eToApm("--------msg_rtb", "------- printAbstractList ---  ， adModel = null");
            } else {
                AdLogger.iToApm("--------msg_rtb", "物料 adModel --- " + advertis + " ， adid = " + advertis.getAdid() + " ,一价-price: " + advertis.getPrice());
            }
        }
        AdLogger.iToApm("--------msg_rtb", " ------------ printList adModel end ------------- " + tagStr);
    }

    public static void printAbstractList(List<AbstractThirdAd> printList, String tagStr) {
        AdLogger.eToApm("--------msg_rtb", "------- printAbstractList start-----  " + tagStr);
        for (AbstractThirdAd thirdAd : printList) {
            if (thirdAd == null) {
                AdLogger.eToApm("--------msg_rtb", "------- printAbstractList ---  ， adModel = null");
            } else {
                AdLogger.eToApm("--------msg_rtb", "------- printAbstractList --- " + thirdAd + " ， adid = " + thirdAd.getAdvertis().getAdid() + ", adtype = " + thirdAd.getAdvertis().getAdtype() + ", level = " + thirdAd.getAdvertis().getRankLevel() + " price = " + thirdAd.getRtbPrice());
            }
        }
        AdLogger.eToApm("--------msg_rtb", "------- printAbstractList end ---- " + tagStr);
    }

    public static AbstractThirdAd chargeSecondPrice(AbstractThirdAd bestAdModel, List<AbstractThirdAd> finalAllAbstractAdList) {
        AdLogger.eToApm("--------msg_rtb", "------- ads link追加上报 ----  点击 上报rtb 价格 -------------- ");
        if (bestAdModel == null) {
            return null;
        }
        if (finalAllAbstractAdList == null || finalAllAbstractAdList.isEmpty() || finalAllAbstractAdList.size() < 1) {
            return bestAdModel;
        }
        finalAllAbstractAdList.remove(bestAdModel);

        Map<String, String> adid_price_map = new ConcurrentHashMap<>();
        for (AbstractThirdAd AbstractThirdAd : finalAllAbstractAdList) {
            if (AbstractThirdAd != null && AbstractThirdAd != bestAdModel && AbstractThirdAd.getAdvertis() != null
                    && !(AbstractThirdAd instanceof NoLoadThirdAd) && !(AbstractThirdAd instanceof XmNativeAd)
                    && (AbstractThirdAd.getDspResult() != AbstractThirdAd.RESULT_TYPE_PRICE_LOW_MIN_BASE_PRICE || AbstractThirdAd.getSdkCallBackStatus() != AbstractThirdAd.SDK_CALL_BACK_STATUS_SDK_PRICE_LOW_MIN_BASE_PRICE)) {
                adid_price_map.put(String.valueOf(AbstractThirdAd.getAdvertis().getAdid()),
                        EncryptPriceUtils.encodeAdsPrice(AbstractThirdAd.getRtbPrice()));
                AdLogger.eToApm("--------msg_rtb", "------- ads link追加上报, type = " + AbstractThirdAd.getAdvertis().getAdtype() + " ,物料ID= " + AbstractThirdAd.getAdvertis().getAdid() + " , 价格 = " + AbstractThirdAd.getRtbPrice());
            }
        }
        AdLogger.dToApm("--------msg_rtb", "------- ads link追加上报  map 打印-------------- map toString - before " + adid_price_map.toString());
        if (bestAdModel != null && bestAdModel.getAdvertis() != null) {
            try {
                JSONObject jsonObject = new JSONObject(adid_price_map);
                bestAdModel.getAdvertis().setRtbEcpmStr(jsonObject.toString());
                AdLogger.eToApm("--------msg_rtb", "------- ads link追加上报  map 打印-------------- map toString  after - " + jsonObject.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return bestAdModel;
    }

}
