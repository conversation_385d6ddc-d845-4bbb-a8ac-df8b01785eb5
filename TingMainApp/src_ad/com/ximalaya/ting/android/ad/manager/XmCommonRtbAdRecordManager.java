package com.ximalaya.ting.android.ad.manager;

import android.text.TextUtils;

import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.NoLoadThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.NoLoadAd;
import com.ximalaya.ting.android.adsdk.base.log.AdLogger;
import com.ximalaya.ting.android.adsdk.bridge.importsdk.IXmLogger;
import com.ximalaya.ting.android.adsdk.util.EncryptPriceUtils;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class XmCommonRtbAdRecordManager {

    public static final String XLOG_SUB_TYPE_MOBILE_RTB_ADS = "mobileRtbAds"; // 客户端实时竞价-广告数据上报文档

    private XmCommonRtbAdRecordManager() {
    }

    public static XmCommonRtbAdRecordManager getInstance() {
        return XmCommonRtbAdRecordManager.SingletonHolder.INSTANCE;
    }


    private static class SingletonHolder {
        private static final XmCommonRtbAdRecordManager INSTANCE = new XmCommonRtbAdRecordManager();
    }

    private AbstractThirdAd mBestAbstractAd;
    private AbstractThirdAd mSecondAbstractAd;
    private List<AbstractThirdAd> mAllAdList;
    private List<Advertis> mWaitingAdList;
    private List<Advertis> mTimeOutAdList;
    private boolean isNotifyBestAd = false;
    private boolean isNotifyDspLoadFinished = false;
    private boolean isNotifyTimeOut = false;

    public void notifyBestAbstractAd(AbstractThirdAd bestAd) {
        notifyBestAbstractAd(bestAd, null);
    }

    public void notifyBestAbstractAd(AbstractThirdAd bestAd, AbstractThirdAd secondAd) {
        if (this.mBestAbstractAd == null) {
            this.mBestAbstractAd = bestAd;
        }
        AdLogger.wToApm("--------msg_rtb_time", " --- setBestAbstractAd xlog - 出现最佳竞胜物料， 竞胜物料为 = " + mBestAbstractAd);
        if (this.mSecondAbstractAd == null){
            this.mSecondAbstractAd = secondAd;
            AdLogger.wToApm("--------msg_rtb_time", " --- setBestAbstractAd xlog - 有次高价物料， 物料为 = " + mSecondAbstractAd);
        }
        isNotifyBestAd = true;
        recordAd();
    }

    public void notifyDspLoadFinished(List<AbstractThirdAd> mResponseAbstractAdList) {
        try {
            if (mAllAdList == null) {
                mAllAdList = new CopyOnWriteArrayList<>();
            } else {
                mAllAdList.clear();
            }
            if (mResponseAbstractAdList != null) {
                if (mAllAdList != null) {
                    mAllAdList.addAll(mResponseAbstractAdList);
                }
            }
            isNotifyDspLoadFinished = true;
            AdLogger.wToApm("--------msg_rtb_time", " --- notifyDspLoadFinished  xlog - 所有sdk物料都返回了 -  mResponseAbstractAdList = " + mResponseAbstractAdList);
            recordAd();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onSdkTimeOutRecord(List<AbstractThirdAd> mResponseAbstractAdList, List<Advertis> waitingAdList) {
        try {
            if (mAllAdList == null) {
                mAllAdList = new CopyOnWriteArrayList<>();
            } else {
                mAllAdList.clear();
            }
            if (mResponseAbstractAdList != null) {
                if (mAllAdList != null) {
                    mAllAdList.addAll(mResponseAbstractAdList);
                }
            }
            if (mWaitingAdList == null) {
                mWaitingAdList = new CopyOnWriteArrayList<>();
            } else {
                mWaitingAdList.clear();
            }
            if (waitingAdList != null) {
                if (mWaitingAdList != null) {
                    mWaitingAdList.addAll(waitingAdList);
                }
            }
            isNotifyTimeOut = true;
            AdLogger.wToApm("--------msg_rtb_time", " --- onSdkTimeOutRecord  xlog -  有超时没返回， 已经返回都sdk物料 = " + mResponseAbstractAdList + "， 还没返回都物料 = " + waitingAdList);
            recordAd();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void recordAd() {
        AdLogger.wToApm("--------msg_rtb_time", " --- 尝试上报 recordAd  isNotifyBestAd = " + isNotifyBestAd + " , isNotifyDspLoadFinished = " + isNotifyDspLoadFinished + " ，isNotifyTimeOut = " + isNotifyTimeOut);
        if (isNotifyBestAd && (isNotifyDspLoadFinished || isNotifyTimeOut)) {
            AdLogger.wToApm("--------msg_rtb_time", " --- recordAd  最佳物料 和 物料列表都有了， 开始上报 ！！！！ ");
            AdLogger.wToApm("--------msg_rtb_time", " --- recordAd xlog上报 22222 开始 ------------ ---------------   -------------- -- -- --");
            AdLogger.dToApm("--------msg_rtb_time", " --- recordAd xlog上报 22222 开始 竞胜 物料 bestAdModel： " + mBestAbstractAd);
            AdLogger.dToApm("--------msg_rtb_time", " --- recordAd xlog上报 22222 开始 竞败上报 物料 finalAllAbstractAdList： " + mAllAdList);
            AdLogger.dToApm("--------msg_rtb_time", " --- recordAd xlog上报 22222 开始 超时 物料 mWaitingAdList： " + mWaitingAdList);

            try {
                if (mAllAdList != null && mAllAdList.contains(mBestAbstractAd)) {
                    AdLogger.eToApm("--------msg_rtb_time", " --- onDspLoadFinished xlog上报 回调list里 包含竞胜物料， 删除竞胜物料 = " + mBestAbstractAd);
                    mAllAdList.remove(mBestAbstractAd);
                }
                recordRtbAd(mBestAbstractAd, mAllAdList, mWaitingAdList);
            } catch (Throwable throwable) {
                throwable.printStackTrace();
            }
            mBestAbstractAd = null;
            mSecondAbstractAd = null;
            mAllAdList = null;
            mWaitingAdList = null;
            isNotifyBestAd = false;
            isNotifyDspLoadFinished = false;
            isNotifyTimeOut = false;
            AdLogger.eToApm("--------msg_rtb_time", " --- recordAd xlog上报 22222 结束 ------------ ---------------   -------------- -- -- ----------------------------");
        }
    }

    public void recordRtbAd(AbstractThirdAd bestAbstractAd, List<AbstractThirdAd> finalAllAdList, List<Advertis> dspTimeOutList) {
        mBestAbstractAd = bestAbstractAd;
        List<AbstractThirdAd> allAdList = new CopyOnWriteArrayList<>();
        if (finalAllAdList != null && !finalAllAdList.isEmpty()) {
            allAdList.addAll(finalAllAdList);
        }

        List<Advertis>  timeOutAdList = new CopyOnWriteArrayList<>();
        if (dspTimeOutList != null && !dspTimeOutList.isEmpty()) {
            timeOutAdList.addAll(dspTimeOutList);
        }

        AdLogger.iToApm("------msg_rtb", "XmXLog  上报: mBestAbstractAd = " + mBestAbstractAd + " ,有返回的mAllAdList = " + allAdList + " ， 超时的list = " + timeOutAdList);
        IXmLogger builder = CommonRtbAdRecord.createTraceApi(XLOG_SUB_TYPE_MOBILE_RTB_ADS);
        AdLogger.iToApm("------msg_rtb", "XmXLog  基础信息 = " + builder.getLogContent());
        if (mBestAbstractAd == null && allAdList.isEmpty() && timeOutAdList.isEmpty()) {
            AdLogger.eToApm("------msg_rtb", "XmXLog error 上报: " + builder.getLogContent());
            CommonRtbAdRecord.record(builder);
            return;
        }

        JSONArray jsonArray = new JSONArray();

        Advertis bestAdvertise = null;

        if (mBestAbstractAd != null && mBestAbstractAd.getAdvertis() != null) {
            bestAdvertise = mBestAbstractAd.getAdvertis();
        }

        // TODO
//        builder.put("isCacheOpen", SplashRtbCacheSDKAdManager.getInstance().isCacheOpen() ? "1" : "0");
        builder.put("isCacheOpen", "0");
        if (mBestAbstractAd != null) {
            builder.put("isCacheAdWin", mBestAbstractAd.isCacheAdToSort() ? "1" : "0");
        }
        if (bestAdvertise != null) {
            builder.put("responseId", bestAdvertise.getResponseId() + "");
            builder.put("positionId", bestAdvertise.getAdPositionId());
        } else {
            try {
                Advertis recordAdvertise = null;
                if (!ToolUtil.isEmptyCollects(allAdList) && allAdList.size() > 0 && allAdList.get(0) != null) {
                    recordAdvertise = allAdList.get(0).getAdvertis();
                }
                if (recordAdvertise == null && timeOutAdList.size() > 0) {
                    recordAdvertise = timeOutAdList.get(0);
                }
                if (recordAdvertise != null) {
                    builder.put("responseId", recordAdvertise.getResponseId() + "");
                    builder.put("positionId", recordAdvertise.getAdPositionId());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        AdLogger.iToApm("------msg_rtb", "XmXLog  基础信息+基本物料信息 = " + builder.getLogContent());
        try {
            if (mBestAbstractAd != null && bestAdvertise != null && !(mBestAbstractAd instanceof NoLoadThirdAd) && !(mBestAbstractAd instanceof NoLoadAd)) {
                AdLogger.eToApm("------msg_rtb", "XmXLog 上报， --------- -------- 竞胜物料 " + mBestAbstractAd);
                jsonArray.put(createRtbAdJson(bestAdvertise, mBestAbstractAd, mBestAbstractAd.getDspResult(), 1));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        for (AbstractThirdAd baseAd : allAdList) {
            if (baseAd != null && baseAd.getAdvertis() != null) {
                try {
                    if (baseAd != mBestAbstractAd) {
                        AdLogger.eToApm("------msg_rtb", "XmXLog 上报，  =============== 失败 " + baseAd);
                        jsonArray.put(createRtbAdJson(baseAd.getAdvertis(), baseAd, baseAd.getDspResult(), 0));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        if (!timeOutAdList.isEmpty()) {
            AdLogger.eToApm("--------msg_rtb", " --- xlog上报 ： --- dsp list 有超时物料  ---------- ");
            for (Advertis advertis : timeOutAdList) {
                try {
                    AdLogger.eToApm("------msg_rtb", "XmXLog 上报， 超时 -- 物料ID = " + advertis.getAdid() + "， 超时物料 adtype = " + advertis.getAdtype());
                    jsonArray.put(createRtbAdJson(advertis, null, 3, 0));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        builder.put("mobileRtbReportList", jsonArray.toString());
        AdLogger.eToApm("------msg_rtb", "XmXLog 上报物料个数 : " + jsonArray.length());
        AdLogger.wToApm("------msg_rtb_time", "XmXLog 完整上报信息: " + builder.getLogContent());
        CommonRtbAdRecord.record(builder);

        mBestAbstractAd = null;
        mSecondAbstractAd = null;
        allAdList.clear();
        timeOutAdList.clear();
    }

    private JSONObject createRtbAdJson(Advertis advertis, AbstractThirdAd dspAd, int dspResult, int rtbResult) {
        JSONObject jsonObject = new JSONObject();
        if (advertis == null) {
            return jsonObject;
        }
        String rtbResultMsg = "";
        try {
            String parallelLoadAbId = CommonRtbAdRecord.getParallelLoadAbIdFromCommonReportMap(advertis.getCommonReportMap());
            AdLogger.i("-------msg_parallel", " parallelLoadAbId = " + parallelLoadAbId);
            jsonObject.putOpt("adid", advertis.getAdid());
            jsonObject.putOpt("price", EncryptPriceUtils.encodeAdsPrice(advertis.getPrice()));
            jsonObject.putOpt("adUniqId", advertis.getAdUniqId());
            jsonObject.putOpt("dspResult", dspResult);
            jsonObject.putOpt("rtbResult", rtbResult);
            jsonObject.putOpt("rtbPrice", EncryptPriceUtils.encodeAdsPrice(dspAd == null ? -1 : dspAd.getRtbPrice()));
            jsonObject.putOpt("adSource", advertis.getAdtype());
            jsonObject.putOpt("baseMinPrice", advertis.getBidMinPrice());
            jsonObject.putOpt("isMobileRtb", advertis.isMobileRtb());
            jsonObject.putOpt("sdkBluffingRatio", advertis.getSdkBluffingRatio());
            jsonObject.putOpt("sdkLimitPrice", advertis.getSdkLimitPrice());
            if (!TextUtils.isEmpty(parallelLoadAbId)) {
                jsonObject.putOpt("parallelLoadAbId", parallelLoadAbId);
            }
            // 9.0.84 新增
            jsonObject.putOpt("slotId", advertis.getDspPositionId() + "");
            if (dspResult == 1) {
                if (rtbResult == 1) {
                    rtbResultMsg = " - 竞胜";
                } else {
                    rtbResultMsg = " - 竞败";
                }
            }

            if (rtbResult == 1 && mSecondAbstractAd != null){
                jsonObject.putOpt("secondHighestPrice", EncryptPriceUtils.encodeAdsPrice(mSecondAbstractAd.getRtbPrice()));
            }
            if (dspAd != null) {
                jsonObject.putOpt("timeCost", dspAd.getSplashTimeCost());
                jsonObject.putOpt("preTimeCost", dspAd.getPreSplashTimeCost());
                jsonObject.putOpt("sdkCallbackStatus", dspAd.getSdkCallBackStatus());
                jsonObject.putOpt("sdkCallbackMsg", dspAd.getSdkCallbackMsg() + rtbResultMsg);
                jsonObject.putOpt("startSplashTime", dspAd.getStartRtbTime());
                jsonObject.putOpt("dspRequestTime", dspAd.getStartRequestTime());
                jsonObject.putOpt("dspResponseTime", dspAd.getGetResponseTime());
                jsonObject.putOpt("isCacheAd", dspAd.isCacheAdToSort());
                jsonObject.putOpt("isCache", dspAd.isCached() && dspAd.isCacheAdToSort()); // 其实跟isCacheAd一样，双端同步字段
            } else {
                jsonObject.putOpt("timeCost", 10000);
                jsonObject.putOpt("preTimeCost", -1);
                jsonObject.putOpt("sdkCallbackStatus", AbstractThirdAd.SDK_CALL_BACK_STATUS_SDK_TIME_OUT);
                jsonObject.putOpt("sdkCallbackMsg", AbstractThirdAd.SDK_CALL_BACK_MSG_SDK_TIME_OUT);
                jsonObject.putOpt("startSplashTime", -1);
                jsonObject.putOpt("dspRequestTime", -1);
                jsonObject.putOpt("dspResponseTime", -1);
                jsonObject.putOpt("isConfigMergeAd", false);
            }

            AdLogger.e("------msg_rtb", "XmXLog 单个物料信息 : jsonObject  = " + jsonObject.toString());
            return jsonObject;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

}
