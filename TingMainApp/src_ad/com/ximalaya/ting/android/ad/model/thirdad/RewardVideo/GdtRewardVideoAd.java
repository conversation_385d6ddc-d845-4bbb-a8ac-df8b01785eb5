package com.ximalaya.ting.android.ad.model.thirdad.RewardVideo;

import android.app.Activity;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.qq.e.ads.rewardvideo.RewardVideoAD;
import com.qq.e.comm.pi.IBidding;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.adsdk.constants.IXmAdConstants;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

import java.util.HashMap;
import java.util.Map;

public class GdtRewardVideoAd extends AbstractRewardVideoAd<RewardVideoAD>{
    public GdtRewardVideoAd(@Nullable Advertis advertis, RewardVideoAD rewardVideoAD, String dspPositionId) {
        super(advertis, rewardVideoAD, dspPositionId);
    }

    @Override
    public int getType() {
        return THIRD_AD_GDT_REWARD;
    }

    @Override
    public void showRewardVideoAd(@NonNull Activity activity, RewardExtraParams extraParams, IVideoAdStatueCallBack callBack) {
        if (getAdData() == null) {
            return;
        }
        if (!ToolUtil.activityIsValid(activity)) {
            if (callBack != null) {
                callBack.onAdLoadError(
                        IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                        "Activity 已不能展示广告");
            }
            return;
        }

        if (callBack != null) {
            callBack.onAdLoad(this);
        }
        getAdData().showAD(activity);
    }

    private final static int IBIDDING_LOSS_TO_GDT_NO_BIDDING = 1;
    private final static int IBidding_LOSS_TO_OTHER_THIRD_AD = 2;
    private final static int IBidding_LOSS_TO_ADX = 3;
    private final static int IBIDDING_LOSS_TO_GDT_BIDDING_AD = 4;

    public void sendWinNotification(double rtbPrice, double secondPrice) {
        if (getAdData() != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(IBidding.EXPECT_COST_PRICE, (int) Math.round(rtbPrice) + "");
            if (secondPrice > 0) {
                map.put(IBidding.HIGHEST_LOSS_PRICE, (int) Math.round(secondPrice) + "");
            }
            getAdData().sendWinNotification(map);
        }
    }

    public void sendLossNotification(AbstractThirdAd bestAdvertise) {
        if (getAdData() != null || bestAdvertise != null) {
            String and_ID = getGdtLossReason(bestAdvertise);
            double winPrice = -1;
            Advertis advertis = bestAdvertise.getAdvertis();
            if (advertis != null && advertis.getSdkWinNotice() == 1 && advertis.getSdkBluffingRatio() > 0){
                winPrice = bestAdvertise.getRtbPrice() * advertis.getSdkBluffingRatio() * 100;
            }
            Map<String, Object> map = new HashMap<>();
            map.put(IBidding.LOSS_REASON, "1");
            map.put(IBidding.ADN_ID, and_ID);
            if (winPrice > 0){
                map.put(IBidding.WIN_PRICE, (int) Math.round(winPrice));
            }
            getAdData().sendLossNotification(map);
        }
    }

    private String getGdtLossReason(AbstractThirdAd bestAdModel) {
        Advertis model = bestAdModel.getAdvertis();
        if (model == null) {
            return IBidding_LOSS_TO_ADX + "";
        }
        int adType = model.getAdtype();
        int reason;
        switch (adType) {
            case IXmAdConstants.IADType.AD_TYPE_XM:
                reason = IBidding_LOSS_TO_ADX;
                break;
            case IXmAdConstants.IADType.AD_TYPE_GDT:
                reason = model.isMobileRtb() ? IBIDDING_LOSS_TO_GDT_BIDDING_AD : IBIDDING_LOSS_TO_GDT_NO_BIDDING;
                break;
            default:
                reason = IBidding_LOSS_TO_OTHER_THIRD_AD;
        }
        return reason + "";
    }

    /**
     * 获取广点通 实时价格
     */
    @Override
    public double getRtbPrice() {
        try{
            if (getAdvertis() != null && !getAdvertis().isMobileRtb() && getAdvertis().getPrice() > 0) {
                return getAdvertis().getPrice();
            }
            String gdtRtbPrice = "-1";
            if (getAdData() != null) {
                RewardVideoAD nativeUnifiedADData = getAdData();
                gdtRtbPrice = nativeUnifiedADData.getECPM() + "";
            }
            return Double.parseDouble(gdtRtbPrice) / 100d;
        }catch (Throwable throwable){
            throwable.printStackTrace();
        }

        return super.getRtbPrice();
    }
}
