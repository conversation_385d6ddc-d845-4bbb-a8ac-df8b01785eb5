package com.ximalaya.ting.android.ad.splashad;

/**
 * Created by le.xin on 2019-09-28.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public interface SplashAdRecordType {

    int WELCOME_ADRECORD_TYPE_NORMAL = 0;   // 正常上报
    int WELCOME_ADRECORD_TYPE_GDT_NO_BACK = 2;   // 广点通无返回顺延展示上报
    int WELCOME_ADRECORD_TYPE_LONG_AD_NO_CACHE = 3;   // 长图广告没预加载是顺延展示上报
    int WELCOME_ADRECORD_TYPE_SOURCE_LOAD_TIMEOUT = 1;// 资源加载超时
    int WELCOME_ADRECORD_TYPE_SOURCE_LOAD_FAIL = 11;  // 资源加载失败
    int WELCOME_ADRECORD_TYPE_AD_REQUEST_TIMEOUT = 12;  // 广告接口超时
    int WELCOME_ADRECORD_TYPE_AD_REQUEST_FAIL = 13;  // 广告接口失败
}
