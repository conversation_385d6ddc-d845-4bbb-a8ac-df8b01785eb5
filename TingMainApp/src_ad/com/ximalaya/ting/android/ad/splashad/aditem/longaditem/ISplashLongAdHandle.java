package com.ximalaya.ting.android.ad.splashad.aditem.longaditem;

import com.ximalaya.ting.android.host.model.ad.AdDownUpPositionModel;

/**
 * Created by le.xin on 2019-06-27.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public interface ISplashLongAdHandle {
    // 处理广告点击事件
    void handleAdClick(int position, AdDownUpPositionModel adDownUpPositionModel, float clickX, float clickY);

    // 广告真正展示的时候
    void onAdRealShow();

    // 当引导点击条点击的时候
    void handleAdHintClick();
}
