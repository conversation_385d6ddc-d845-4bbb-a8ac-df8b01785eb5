package com.ximalaya.ting.android.ad.uve.handler.request.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @Time 2023/8/18
 * @Description
 */
public class UveSceneInfo implements Parcelable {
    private int positionId;
    private String sceneId;

    public int getPositionId() {
        return positionId;
    }

    public void setPositionId(int positionId) {
        this.positionId = positionId;
    }

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.positionId);
        dest.writeString(this.sceneId);
    }

    public void readFromParcel(Parcel source) {
        this.positionId = source.readInt();
        this.sceneId = source.readString();
    }

    public UveSceneInfo() {
    }

    protected UveSceneInfo(Parcel in) {
        this.positionId = in.readInt();
        this.sceneId = in.readString();
    }

    public static final Parcelable.Creator<UveSceneInfo> CREATOR = new Creator<UveSceneInfo>() {
        @Override
        public UveSceneInfo createFromParcel(Parcel source) {
            return new UveSceneInfo(source);
        }

        @Override
        public UveSceneInfo[] newArray(int size) {
            return new UveSceneInfo[size];
        }
    };

    @Override
    public String toString() {
        return "UveSceneInfo{" +
                "positionId=" + positionId +
                ", sceneId='" + sceneId + '\'' +
                '}';
    }
}
