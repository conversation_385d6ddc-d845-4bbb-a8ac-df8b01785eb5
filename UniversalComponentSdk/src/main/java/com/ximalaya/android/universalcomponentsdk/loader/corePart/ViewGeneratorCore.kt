package com.ximalaya.android.universalcomponentsdk.loader.corePart

import android.content.res.Configuration
import android.view.View
import android.view.ViewGroup
import com.ximalaya.android.componentelementarysdk.model.pageInfo.PageInfo
import com.ximalaya.android.componentelementarysdk.util.SdkBaseUtil
import com.ximalaya.android.nativecomponentsdk.factory.ModuleViewNativeFactory
import com.ximalaya.android.universalcomponentsdk.loader.presenter.LoaderPresenter
import com.ximalaya.android.universalcomponentsdk.model.universalProduct.UniversalProductModule

/**
 * Created by 5Greatest on 2021.11.01
 *
 * <AUTHOR>
 *   On 2021/11/1
 */
class ViewGeneratorCore<T : ViewGroup>(private val mPresenter: LoaderPresenter<T>) {

    /**
     * 生成子模块的view
     * */
    fun initChildView(callBack: IViewGenerateCallBack?) {
        val pageType: String = mPresenter.pageInfo?.pageType?: return
        val moduleTypeList: List<String> = mPresenter.moduleTypeList?: return
        val spuId = mPresenter.pageInfo?.spuId?:return
        val size: Int = moduleTypeList.size
        for (index in 0 until size) {
            val moduleModel: UniversalProductModule = SdkBaseUtil.Common.safelyGetItemFromList(mPresenter.pageModel?.modules, index)?: continue
            val type: String = moduleModel.type?: continue
            mPresenter.moduleVersionMap[type] = moduleModel.dataVersionIndicator
            val pageInfo: PageInfo = mPresenter.pageInfo ?: continue
            val childView: View? = ModuleViewNativeFactory.getInstance().findModuleViewCreator(spuId,pageType, type)?.create(pageInfo, moduleModel.realConfig, moduleModel.parseRealModel(pageType, false, mPresenter.loaderParam.getToParseModuleClazz(type), UniversalProductModule.PARSE_TYPE_INITIAL))
            if (null == childView) {
                callBack?.onGenerateFail(pageType, type, moduleModel, -1, "")
            } else {
                callBack?.onGenerateSuccess(pageType, type, childView)
            }
        }
    }

    fun updateChildView(pageType: String, type: String, childView: View?, callBack: IViewUpdateCallBack?): View? {
        if (null == childView) {
            callBack?.onUpdateFail(pageType, type, mPresenter.pageModel?.moduleMap?.get(type), -1, "")
            return childView
        }
        val version: Int = mPresenter.moduleVersionMap[type] ?: 0
        val moduleModel: UniversalProductModule? = mPresenter.pageModel?.moduleMap?.get(type)
        if (null == moduleModel) {
            callBack?.onUpdateFail(pageType, type, mPresenter.pageModel?.moduleMap?.get(type), -1, "")
            return childView
        }
        if (version >= moduleModel.dataVersionIndicator) {
            callBack?.onUpdateFail(pageType, type, mPresenter.pageModel?.moduleMap?.get(type), -1, "")
            return childView
        }
        // 更新版本记录
        mPresenter.moduleVersionMap[type] = moduleModel.dataVersionIndicator
        val pageInfo: PageInfo? = mPresenter.pageInfo
        if (null == pageInfo) {
            callBack?.onUpdateFail(pageType, type, mPresenter.pageModel?.moduleMap?.get(type), -1, "")
            return childView
        }
        val resultView: View? = ModuleViewNativeFactory.getInstance().findModuleViewCreator(pageInfo.spuId?:0,pageType, type)?.reBindData(childView, pageInfo, moduleModel.parseRealModel(pageType, true, mPresenter.loaderParam.getToParseModuleClazz(type), UniversalProductModule.PARSE_TYPE_UPDATE))
        if (null != resultView) {
            callBack?.onUpdateSuccess(pageType, type, resultView)
        } else {
            callBack?.onUpdateFail(pageType, type, mPresenter.pageModel?.moduleMap?.get(type), -1, "")
        }
        return resultView
    }

    fun updateOnConfigurationChanged(configuration: Configuration) {
        val pageType: String = mPresenter.pageInfo?.pageType ?: return
        val moduleTypeList: List<String> = mPresenter.moduleTypeList ?: return
        val size: Int = moduleTypeList.size
        for (index in 0 until size) {
            val moduleModel: UniversalProductModule =
                SdkBaseUtil.Common.safelyGetItemFromList(mPresenter.pageModel?.modules, index)
                    ?: continue
            val type: String = moduleModel.type ?: continue
            mPresenter.moduleVersionMap[type] = moduleModel.dataVersionIndicator
            val view = mPresenter.findChildView(type) ?: continue
            ModuleViewNativeFactory.getInstance().findModuleViewCreator(mPresenter.pageInfo?.spuId?:0,pageType, type)
                ?.onConfigurationChanged(view, configuration)
        }
    }

    /**
     * 寻找配置中的显示区域
     * */
    fun findViewGroupInParam(type: String?): ViewGroup? {
        return mPresenter.loaderParam.getCustomTargetViewGroupAreaByType(type, mPresenter.rootViewGroup)
    }

    interface IViewGenerateCallBack {
        fun onGenerateSuccess(pageType: String, type: String, childView: View)
        fun onGenerateFail(pageType: String?, type: String?, moduleModel: UniversalProductModule?, code: Int, msg: String?)
    }

    interface IViewUpdateCallBack {
        fun onUpdateSuccess(pageType: String, type: String, childView: View)
        fun onUpdateFail(pageType: String?, type: String?, moduleModel: UniversalProductModule?, code: Int, msg: String?)
    }
}