package com.ximalaya.android.universalcomponentsdk.loader.presenter;

import android.net.Uri;
import android.view.ViewGroup;

import com.ximalaya.android.componentelementarysdk.model.pageInfo.PageInfo;
import com.ximalaya.android.universalcomponentsdk.ComponentConfigurationCenter;
import com.ximalaya.android.universalcomponentsdk.material.universalProduct.FragmentLoadMaterial;
import com.ximalaya.android.universalcomponentsdk.model.universalProduct.UniversalProductModel;
import com.ximalaya.ting.android.host.util.XmRequestPage;

import java.util.List;

/**
 * Created by 5Greatest on 2021.11.23
 *
 * <AUTHOR>
 * On 2021/11/23
 */
public abstract class AutoWorkLoaderPresenter<T extends ViewGroup> extends BaseLoaderPresenter<T> {

    protected FragmentLoadMaterial loadBaseMaterial;
    protected PageInfo pageInfo;

    protected Uri itingUri;

    protected UniversalProductModel pageModel;

    public AutoWorkLoaderPresenter(T viewGroup) {
        super(viewGroup);
    }

    //////////////////////////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////// setter and getter //////////////////////////////////////
    /////////////////////////////////////       start       //////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////

    public FragmentLoadMaterial getLoadBaseMaterial() {
        return loadBaseMaterial;
    }

    public void setLoadBaseMaterial(FragmentLoadMaterial loadBaseMaterial) {
        this.loadBaseMaterial = loadBaseMaterial;
        if (null != loadBaseMaterial) {
            this.pageInfo = new PageInfo(ComponentConfigurationCenter.getApplicationContext());
            this.pageInfo.setQueryHandler(loadBaseMaterial.queryHandler);
            this.pageInfo.pageName = loadBaseMaterial.pageName;
            this.pageInfo.spuId = loadBaseMaterial.spuId;
            this.pageInfo.pageType = loadBaseMaterial.pageType;
            this.pageInfo.subPageType = loadBaseMaterial.subPageType;
            this.pageInfo.materialJson = loadBaseMaterial.getMaterialJson();
            this.pageInfo.utmSource = loadBaseMaterial.utmSource;
            this.pageInfo.setViewRelativeInterceptor(getViewRelativeInterceptor());
            this.pageInfo.setDataRelativeInterceptor(getDataRelativeInterceptor());
            this.pageInfo.requestId = XmRequestPage.INSTANCE.getPageUniqueRequestId(XmRequestPage.PAGE_CUSTOM_ALBUM_PAGE);
        }
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public UniversalProductModel getPageModel() {
        return pageModel;
    }

    public void setPageModel(UniversalProductModel pageModel) {
        this.pageModel = pageModel;
    }

    public Uri getItingUri() {
        return itingUri;
    }

    public void setItingUri(Uri itingUri) {
        this.itingUri = itingUri;
    }

    public List<String> getModuleTypeList() {
        if (null != pageModel) {
            return pageModel.typeIndexList;
        }
        return null;
    }

    //////////////////////////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////// setter and getter //////////////////////////////////////
    /////////////////////////////////////        end        //////////////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////

}
