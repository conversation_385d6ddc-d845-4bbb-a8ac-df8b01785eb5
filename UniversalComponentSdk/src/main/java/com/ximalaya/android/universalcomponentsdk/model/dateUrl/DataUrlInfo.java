package com.ximalaya.android.universalcomponentsdk.model.dateUrl;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;

import org.json.JSONObject;

import java.io.Serializable;

/**
 * Created by 5Greatest on 2021.10.28
 *
 * <AUTHOR>
 * On 2021/10/28
 */
public class DataUrlInfo implements Serializable {
    @SerializedName("param")
    public JsonObject param;
    @SerializedName("module")
    public String module;
    @SerializedName("isPaging")
    public boolean isPaging;
    @SerializedName("itingParam")
    public JsonArray itingParam;
}
