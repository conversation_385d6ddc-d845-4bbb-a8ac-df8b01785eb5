package com.ximalaya.ting.android.video.playtab;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.view.KeyEvent;
import android.view.View;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.IVideoEventListener;
import com.ximalaya.ting.android.player.Logger;
import com.ximalaya.ting.android.video.playtab.listener.IPlayTabVideoWrapper;
import com.ximalaya.ting.android.xmplaysdk.IMediaPlayerControl;
import com.ximalaya.ting.android.xmplaysdk.video.player.IVideoPlayStatusListener;
import com.ximalaya.ting.android.xmplaysdk.video.player.VideoSource;
import com.ximalaya.ting.android.xmplaysdk.video.player.controller.IControllerStateFactory;

import com.ximalaya.ting.android.player.video.player.IMediaPlayer;

import java.util.ArrayList;


/**
 * Created by Travis on 2017/8/11 上午9:39.
 *
 * <AUTHOR>
 * Updated by sigma on 2018/1/22
 * <p>
 * 注意点：
 * 1.所有的状态转化都要经过 IControllerStateContext 中的方法来实现
 */
public class PlayTabVideoWrapper implements IPlayTabVideoWrapper {

    @NonNull
    private PlayTabVideo mPlayTabVideo;
    @NonNull
    private PlayTabVideoController mPlayTabVideoController;

    public PlayTabVideoWrapper(Context context) {
        mPlayTabVideo = new PlayTabVideo(context);
        mPlayTabVideo.setPlayTabVideoWrapper(this);
        mPlayTabVideoController = new PlayTabVideoController(context);
    }

    @Override
    public void setVideoSource(VideoSource videoSource) {
        mPlayTabVideo.setVideoSource(videoSource);
    }

    @Override
    public void start() {
        mPlayTabVideo.start();
    }

    @Override
    public void start(boolean isRealStart, boolean isTrackChanged) {
        mPlayTabVideo.start(isRealStart, isTrackChanged);
    }

    @Override
    public void restart() {
        mPlayTabVideo.restart();
    }

    @Override
    public void pause() {
        mPlayTabVideo.pause();
    }

    @Override
    public void stop() {
        mPlayTabVideo.stop();
    }

    @Override
    public void clearRenderView() {
        mPlayTabVideo.clearRenderView();
    }

    @Override
    public void setPrepareLoadingState() {
        mPlayTabVideo.setPrepareLoadingState();
    }

    @Override
    public void setPlayStateListener(IVideoPlayStatusListener listener) {
    }

    @Override
    public void setAllowUseMobileNetwork(boolean allow) {
        mPlayTabVideo.setAllowUseMobileNetwork(allow);
    }

    @Override
    public void changeResolution(int resolutionIndex) {
        mPlayTabVideo.changeResolution(resolutionIndex);
    }

    @Override
    public void setStateFactory(IControllerStateFactory factory) {

    }

    @Override
    public boolean isPauseByUser() {
        return false;
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return mPlayTabVideo.dispatchKeyEvent(event);
    }

    @Override
    public void showUniversalPayment(boolean show, @Nullable CharSequence title, @Nullable View paymentView, boolean isPlaceHolder) {
        mPlayTabVideo.showUniversalPayment(show, title, paymentView, isPlaceHolder);
    }

    @Override
    public void showRestartView(boolean show) {
        mPlayTabVideo.showRestartView(show);
    }

    @Override
    public void showPlayAudioView(boolean show) {
        mPlayTabVideo.showPlayAudioView(show);
    }

    @Override
    public void setHasNext(boolean hasNext) {
        mPlayTabVideo.setHasNext(hasNext);
    }

    @Override
    public void setHasPrev(boolean hasPrev) {
        mPlayTabVideo.setHasPrev(hasPrev);
    }

    @Override
    public void showPortraitShareView(boolean show) {
        mPlayTabVideo.showPortraitShareView(show);
    }

    @Override
    public void seekToPosition(int position) {
        mPlayTabVideo.seekToPosition(position);
    }

    @Override
    public void setTitle(String title) {
        mPlayTabVideo.setTitle(title);
    }

    @Override
    public void showMoreBtn(boolean show) {
        mPlayTabVideo.showMoreBtn(show);
    }

    @Override
    public void showSyncHintView(int soundPosition, boolean isFromAudio) {
        mPlayTabVideo.showSyncHintView(soundPosition, isFromAudio);
    }

    @Override
    public void customOnConfigurationChanged(Configuration configuration) {
        mPlayTabVideo.customOnConfigurationChanged(configuration);
    }

    @Override
    public void showNextHint(String title) {
        mPlayTabVideo.showNextHint(title);
    }

    @Override
    public void showBackBtn(boolean show) {
        mPlayTabVideo.showBackBtn(show);
    }

    @Override
    public void showShareBtn(boolean show) {
        mPlayTabVideo.showShareBtn(show);
    }

    @Override
    public void setShareBtnIcon(int iconResId) {
        mPlayTabVideo.setShareBtnIcon(iconResId);
    }

    @Override
    public void setLyric(String lyric) {
        mPlayTabVideo.setLyric(lyric);
    }

    @Override
    public void showTopShareView(boolean show) {
        mPlayTabVideo.showTopShareView(show);
    }

    @Override
    public void setInterceptBackUpBtn(boolean interceptBackUpBtn) {
        mPlayTabVideo.setInterceptBackUpBtn(interceptBackUpBtn);
    }

    @Override
    public void setMuteBtn(boolean show, boolean defaultMuteState) {
        mPlayTabVideo.setMuteBtn(show, defaultMuteState);
    }

    @Override
    public void setIntercept(boolean shouldIntercept) {
        mPlayTabVideo.setIntercept(shouldIntercept);
    }

    @Override
    public void shouldShowNextBtn(boolean show) {
        mPlayTabVideo.shouldShowNextBtn(show);
    }

    @Override
    public void setOutsideEndingBitmap(boolean isSet, Bitmap bitmap) {
        mPlayTabVideo.setOutsideEndingBitmap(isSet, bitmap);
    }

    @Override
    public void setVideoPortrait(boolean portrait) {
        mPlayTabVideo.setVideoPortrait(portrait);
    }

    @Override
    public void setFullScreen(boolean full, boolean nav) {
        mPlayTabVideo.setFullScreen(full, nav);
    }

    @Override
    public SeekBar.OnSeekBarChangeListener getSeekBarChangeListener() {
        return null;
    }

    @Override
    public void onLoadingViewVisibilityChanged(boolean isVisible) {
        mPlayTabVideo.onLoadingViewVisibilityChanged(isVisible);
    }

    @Override
    public void setMaskViewAlpha(float alpha) {
        Logger.d("feiwen", "setMaskViewAlpha PlayTabVideoWrapper alpha = " + alpha);
        mPlayTabVideo.setMaskViewAlpha(alpha);
    }

    @Override
    public void addDanmakuView(View danmakuViewView) {
        mPlayTabVideo.addDanmakuView(danmakuViewView);
    }

    @Override
    public void setTrackId(long trackId) {
        mPlayTabVideo.setTrackId(trackId);
    }

    @Override
    public void hide() {
        mPlayTabVideo.hide();
    }

    @Override
    public boolean isShowing() {
        return false;
    }

    @Override
    public void setAnchorView(View view) {
        mPlayTabVideo.setAnchorView(view);
    }

    @Override
    public void setEnabled(boolean enabled) {
        mPlayTabVideo.setEnabled(enabled);
    }

    @Override
    public void setMediaPlayer(IMediaPlayerControl player) {
        mPlayTabVideo.setMediaPlayer(player);
    }

    @Override
    public boolean goToNormalState(boolean force) {
        return false;
    }

    @Override
    public void goToUniversalPaymentState(CharSequence title, View paymentView, boolean isPlaceHolder) {

    }

    @Override
    public void goToUseMobileNetworkState() {

    }

    @Override
    public void goToErrorState() {

    }

    @Override
    public void goToNoNetworkState() {

    }

    @Override
    public void goToLoadingState() {

    }

    @Override
    public void goToChangingResolutionState() {

    }

    @Override
    public void goToChangingResolutionWithoutHintState() {

    }

    @Override
    public void goToResolutionChangedState() {

    }

    @Override
    public void goToPortraitShareState() {

    }

    @Override
    public void goToRestartState() {

    }

    @Override
    public void goToNextHintState(String title) {
        mPlayTabVideo.goToNextHintState(title);
    }

    @Override
    public void goToSmoothHintState() {

    }

    @Override
    public void goToSmoothWithoutHintState() {

    }

    @Override
    public void goToNextHintWithoutHintState(String title) {

    }

    @Override
    public void goToSyncSoundHintState(int soundPosition, boolean isFromAudio) {

    }

    @Override
    public void goToSyncSoundHintWithoutHintState(int soundPosition) {

    }

    @Override
    public boolean isPortrait() {
        return false;
    }

    @Override
    public int getBottomBarHeight() {
        return 0;
    }

    @Override
    public void updateViewByState() {
        mPlayTabVideo.updateViewByState();
    }

    @Override
    public void show() {
        mPlayTabVideo.show();
    }

    @Override
    public void showOnce() {
        mPlayTabVideo.showOnce();
    }

    @Override
    public void onRequestAllowMobileNetwork() {
        mPlayTabVideo.onRequestAllowMobileNetwork();
    }

    @Override
    public boolean onError(IMediaPlayer mp, int what, int extra) {
        return mPlayTabVideo.onError(mp,what,extra);
    }

    @Override
    public boolean onInfo(IMediaPlayer mp, int what, int extra) {
        return mPlayTabVideo.onInfo(mp,what,extra);
    }

    @Override
    public void onPrepared(IMediaPlayer mp) {
        mPlayTabVideo.onPrepared(mp);
    }

    @Override
    public boolean shouldShowGuide(Context context) {
        return mPlayTabVideoController.shouldShowGuide(context);
    }

    @Override
    public void onShareClick() {
        mPlayTabVideoController.onShareClick();
    }

    @Override
    public void onMoreClick() {
        mPlayTabVideoController.onMoreClick();
    }

    @Override
    public void onCompletion(IMediaPlayer mp) {
        mPlayTabVideoController.onCompletion(mp);
    }

    @Override
    public void onShareToWeixinClicked() {
        mPlayTabVideoController.onShareToWeixinClicked();
    }

    @Override
    public void onShareToWxCircleClicked() {
        mPlayTabVideoController.onShareToWxCircleClicked();
    }

    @Override
    public void onShareToWeiboClicked() {
        mPlayTabVideoController.onShareToWeiboClicked();
    }

    @Override
    public void onShareToQQClicked() {
        mPlayTabVideoController.onShareToQQClicked();
    }

    @Override
    public void onPlayClick() {
        mPlayTabVideoController.onPlayClick();
    }

    @Override
    public void onPlayAudioBtnClick() {
        mPlayTabVideoController.onPlayAudioBtnClick();
    }

    @Override
    public void onPlayNextBtnClick() {
        mPlayTabVideoController.onPlayNextBtnClick();
    }

    @Override
    public void onPlayPrevBtnClick() {
        mPlayTabVideoController.onPlayPrevBtnClick();
    }

    @Override
    public void onPauseClick() {
        mPlayTabVideoController.onPauseClick();
    }

    @Override
    public void onNormalResolutionClick() {
        mPlayTabVideoController.onNormalResolutionClick();
    }

    @Override
    public void onHighResolutionClick() {
        mPlayTabVideoController.onHighResolutionClick();
    }

    @Override
    public void onSeekComplete(int startTime, int endTime) {
        mPlayTabVideoController.onSeekComplete(startTime, endTime);
    }

    @Override
    public void onBackClick() {
        mPlayTabVideoController.onBackClick();
    }

    @Override
    public void onFullScreenClick() {
        mPlayTabVideoController.onFullScreenClick();
    }

    @Override
    public void onEnding() {
        mPlayTabVideoController.onEnding();
    }

    @Override
    public boolean isUsingFreeFlow() {
        return mPlayTabVideoController.isUsingFreeFlow();
    }

    @Override
    public void showToast(String toast) {
        mPlayTabVideoController.showToast(toast);
    }


    @Override
    public void onRendingStart(long spentTime) {
        mPlayTabVideoController.onRendingStart(spentTime);
    }

    @Override
    public void onBlock(long blockMillisecond) {
        mPlayTabVideoController.onBlock(blockMillisecond);
    }

    @Override
    public void onPlayNextClick() {
        mPlayTabVideoController.onPlayNextClick();
    }

    @Override
    public void onProgress(long curPos, long duration) {
        mPlayTabVideoController.onProgress(curPos, duration);
    }

    @Override
    public void onProgressAndSecondaryProgressChanged(int progress, int secondaryProgress) {
        mPlayTabVideoController.onProgressAndSecondaryProgressChanged(progress, secondaryProgress);
    }

    @Override
    public void onHidePanel() {
        mPlayTabVideoController.onHidePanel();
    }

    @Override
    public void onShowPanel() {
        mPlayTabVideoController.onShowPanel();
    }

    @Override
    public void onDanmakuBtnClick() {
        mPlayTabVideoController.onDanmakuBtnClick();
    }

    @Override
    public void onSendDanmakuClick() {
        mPlayTabVideoController.onSendDanmakuClick();
    }

    @Override
    public void onAnthologyClick() {
        mPlayTabVideoController.onAnthologyClick();
    }

    @Override
    public void onScreenRotationLockClick() {
        mPlayTabVideoController.onScreenRotationLockClick();
    }

    @Override
    public void onMuteClick() {
        mPlayTabVideoController.onMuteClick();
    }

    @Override
    public void onUnMuteClick() {
        mPlayTabVideoController.onUnMuteClick();
    }

    @Override
    public void onRetryClick() {
        mPlayTabVideoController.onRetryClick();
    }

    @Override
    public void onVolumeChanged(int curVolume) {
        mPlayTabVideoController.onVolumeChanged(curVolume);
    }

    @Override
    public void onResolutionChanged(int newWidth, int newHeight) {
        mPlayTabVideo.onResolutionChanged(newWidth, newHeight);
        mPlayTabVideoController.onResolutionChanged(newWidth, newHeight);
    }

    @Override
    public void onResolutionBtnClick() {
        mPlayTabVideoController.onResolutionBtnClick();
    }

    @Override
    public void onResolutionDialogItemClick(String resolutionsName) {
        mPlayTabVideoController.onResolutionDialogItemClick(resolutionsName);
    }

    @Override
    public void onResolutionDialogShow(ArrayList<String> resolutions) {
        mPlayTabVideoController.onResolutionDialogShow(resolutions);
    }

    @Override
    public void onSeekbarClick() {
        mPlayTabVideoController.onSeekbarClick();
    }

    @Override
    public void onControllerViewVisibilityChanged(boolean isVideoHorizontalFull, boolean isPortraitFull, boolean controllerViewVisibility) {
        mPlayTabVideoController.onControllerViewVisibilityChanged(isVideoHorizontalFull, isPortraitFull, controllerViewVisibility);
    }

    @Override
    public void onVideoMaskViewAlphaChanged(float alpha) {
        mPlayTabVideoController.onVideoMaskViewAlphaChanged(alpha);
    }

    @Override
    public void changeSeekBarVisibility(boolean isVisible) {
        mPlayTabVideoController.changeSeekBarVisibility(isVisible);
    }

    @Override
    public void setVideoEventListener(IVideoEventListener listener) {
        mPlayTabVideoController.setVideoEventListener(listener);
    }

    @Override
    public boolean canGoToNextHintState() {
        return mPlayTabVideo.canGoToNextHintState();
    }

}
