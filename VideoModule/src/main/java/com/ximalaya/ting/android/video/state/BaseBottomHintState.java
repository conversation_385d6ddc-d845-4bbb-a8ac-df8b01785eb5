package com.ximalaya.ting.android.video.state;

import android.content.Context;
import androidx.annotation.NonNull;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.video.R;
import com.ximalaya.ting.android.video.playtab.PlayTabVideo;
import com.ximalaya.ting.android.xmplaysdk.video.player.controller.ControllerViewHolder;
import com.ximalaya.ting.android.xmplaysdk.video.player.controller.IControllerStateContext;
import com.ximalaya.ting.android.xmplaysdk.video.player.controller.VideoController;

/**
 * <AUTHOR> on 2018/2/24.
 */

public abstract class BaseBottomHintState extends BaseControllerState {

    public BaseBottomHintState(IControllerStateContext context) {
        super(context);
    }

    @Override
    public void updateViewVisibility(@NonNull ControllerViewHolder viewHolder, @NonNull final
    FrameLayout controllerView) {
        super.updateViewVisibility(viewHolder, controllerView);
        if (viewHolder.bottomHintContainer == null) {
            initStateView(viewHolder, controllerView);
        }
        if (viewHolder.topBar != null) {
            viewHolder.topBar.setVisibility(View.VISIBLE);
        }
    }

    protected void initStateView(@NonNull ControllerViewHolder viewHolder, @NonNull final FrameLayout controllerView) {
        Context context = controllerView.getContext();
        View view = LayoutInflater.from(context).inflate(R.layout.video_bottom_hint_bar, null);
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(ViewGroup.LayoutParams
                .WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        lp.gravity = Gravity.BOTTOM;
        lp.bottomMargin = BaseUtil.dp2px(context, 16);
        lp.leftMargin = BaseUtil.dp2px(context, 8);
        if (controllerView instanceof VideoController) {
            if (((VideoController) controllerView).isVideoPortrait && ((VideoController) controllerView).isPortraitFull) {
                lp.bottomMargin = BaseUtil.dp2px(context, 150);
                lp.leftMargin = BaseUtil.dp2px(context, 16);
            }
        }
        viewHolder.bottomHintText = view.findViewById(R.id.video_tv_hint);
        viewHolder.bottomHintAction = view.findViewById(R.id.video_tv_action);
        viewHolder.ivClose = view.findViewById(R.id.video_iv_close);
        controllerView.addView(view, lp);
        viewHolder.bottomHintContainer = view;
    }
}
