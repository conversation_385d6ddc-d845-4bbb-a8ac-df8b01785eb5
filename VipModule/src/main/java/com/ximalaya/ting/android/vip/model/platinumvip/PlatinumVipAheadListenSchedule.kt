package com.ximalaya.ting.android.vip.model.platinumvip

import androidx.annotation.Keep

/**
 * Created by mark on 2025/6/10 13:48
 */
@Keep
data class PlatinumVipAheadListenSchedule(
    val plans: List<UpdateSchedulePlan>? = null
)

@Keep
data class DateDesc(
    val date: String? = null,
    val week: String? = null,
    val isToday: Boolean? = false,
    val isEndOfWeek: Boolean? = false
)

@Keep
data class VipSchedule(
    val isBurstUpdate: Boolean? = false,
    val title: String? = null,
    val trackCount: Int? = 0
)

@Keep
data class UpdateSchedulePlan(
    val dateDesc: DateDesc? = null,
    val platinumVip: VipSchedule? = null,
    val vip: VipSchedule? = null
)


