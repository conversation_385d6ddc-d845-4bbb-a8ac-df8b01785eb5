package com.ximalaya.ting.android.vip.model.vipFragmentV2.module;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.model.album.AlbumSubscript;
import com.ximalaya.ting.android.vip.constant.VipFragmentConstants;
import com.ximalaya.ting.android.vip.model.vipFragmentV2.VipFragmentV2ModuleModel;

import org.json.JSONObject;

import java.io.Serializable;
import java.util.List;

public class VipFragmentV2NewAlbumReservationModel extends VipFragmentV2ModuleModel {

    @SerializedName("moreUrl")
    public String moreUrl;                                  // 更多跳转链接
    @SerializedName("reservationTabs")
    public List<NewAlbumReservationList> reservationTabs;

    public int localCurrentPosition = 0;
    private int totalAlbumsCount = 0;

    public int getTotalAlbumsCount() {
        if (totalAlbumsCount == 0) {
            if (null != reservationTabs) {
                for (NewAlbumReservationList list : reservationTabs) {
                    if (null == list || list.albums == null) {
                        continue;
                    }
                    totalAlbumsCount += list.albums.size();
                }
            }
        }
        return totalAlbumsCount;
    }

    @Nullable
    @Override
    public String getVipViewType() {
        return VipFragmentConstants.NAME_MODULE_NEW_ALBUM_RESERVATION;
    }


    public void prePareModelData() {
        int index = 0;
        if (null != reservationTabs) {
            for (NewAlbumReservationList list : reservationTabs) {
                if (null == list) {
                    continue;
                }
                list.localIndex = index++;
            }
        }
    }

    public static class NewAlbumReservationList implements Serializable {
        @SerializedName("tabName")
        public String tabName;
        @SerializedName("albums")
        public List<NewAlbumReservationItem> albums;

        public int localIndex = 0;
    }

    public static class Roster implements Serializable {
        @SerializedName("anchorImage")
        public String anchorImage;
        @SerializedName("anchorName")
        public String anchorName;
        @SerializedName("roleName")
        public String roleName;
    }

    public static class AnchorInfo implements Serializable {
        @SerializedName("anchorId")
        public long anchorId;
        @SerializedName("logoPic")
        public String logoPic;
        @SerializedName("nickname")
        public String nickName;
        @SerializedName("followStatus")
        public boolean followStatus;
    }


    public static class NewAlbumReservationItem implements Serializable {
        public static boolean parseResult(String resultJson) {
            if (null == resultJson) {
                return false;
            }
            try {
                JSONObject jsonObject = new JSONObject(resultJson);
                if (jsonObject.has("ret")) {
                    int ret = jsonObject.optInt("ret", -1);
                    if (0 == ret) {
                        return true;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return false;
        }

        public boolean localIsPreOrderOperating = false;

        @SerializedName("reservationProductId")
        public long reservationProductId;           // 预约商品id，调用预约/取消预约接口时需要回传该参数
        @SerializedName("albumId")
        public long albumId;
        @SerializedName("onlineTime")
        public String onlineTime;
        @SerializedName("cover")
        public String cover;
        @SerializedName("reservationCount")
        public long reservationCount;
        @SerializedName("title")
        public String title;
        @SerializedName("intro")
        public String intro;
        @SerializedName("longIntro")
        public String longIntro;
        @SerializedName("reservationStatus")
        public boolean reservationStatus;
        @SerializedName("albumStatus")
        public boolean albumStatus;                             // 是否已上新
        @SerializedName("albumSubscript")
        public String albumSubscript;
        @SerializedName("url")
        public String url;
        @SerializedName("ipBigImage")
        public String ipBigImage;
        @SerializedName("albumTag")
        public String albumTag;
        @SerializedName("testSound")
        public String testSound;
        @SerializedName("rosters")
        public List<Roster> rosters;
        @SerializedName("anchorInfo")
        public AnchorInfo anchorInfo;

        private AlbumSubscript localAlbumSubscript;

        public AlbumSubscript getLocalAlbumSubscript() {
            if (null == localAlbumSubscript) {
                if (null != albumSubscript) {
                    localAlbumSubscript = new AlbumSubscript(albumSubscript);
                }
            }
            return localAlbumSubscript;
        }

        public int getAlbumSubscriptValue() {
            getLocalAlbumSubscript();
            if (localAlbumSubscript != null) {
                return localAlbumSubscript.getAlbumSubscriptValue();
            }
            return 0;
        }

        public boolean hasPreOrdered() {
            return reservationStatus;
        }

        public void updatePreOrderStatus(boolean hasPreOrdered) {
            this.reservationStatus = hasPreOrdered;
        }

        public String getBtnText() {
            if (albumStatus) {
                return "去收听";
            } else {
                if (reservationStatus) {
                    return "已预约";
                } else {
                    return "预约";
                }
            }
        }
    }
}
