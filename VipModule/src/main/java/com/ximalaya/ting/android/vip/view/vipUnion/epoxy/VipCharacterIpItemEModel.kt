package com.ximalaya.ting.android.vip.view.vipUnion.epoxy

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyHolder
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.autosize.AutoSize
import com.ximalaya.ting.android.framework.autosize.AutoSizeConfig
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.vip.R
import com.ximalaya.ting.android.vip.manager.vipFragment.vipUnion.event.VipCharacterIpEventHelper
import com.ximalaya.ting.android.vip.model.vipUnion.VipCharacterIpItemModel
import com.ximalaya.ting.android.vip.util.VipFragmentUtil
import com.ximalaya.ting.android.vip.util.setOnOneClickListener
import kotlin.math.roundToInt

/**
 * Created by mark on 2024/6/12 13:54
 */
@EpoxyModelClass
abstract class VipCharacterIpItemEModel :
    EpoxyModelWithHolder<VipCharacterIpItemEModel.ItemHolder>() {
    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var context: Context

    @EpoxyAttribute
    lateinit var ipItemModel: VipCharacterIpItemModel

    @EpoxyAttribute
    var position: Int = 0

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var clickListener: ((String, Int, VipCharacterIpItemModel) -> Unit)? = null

    private val coverWidth by lazy {
        ((VipFragmentUtil.FoldScreenUtils.getFoldScreenCompatWidth(context) - context.resources.getDimensionPixelOffset(
            com.ximalaya.ting.android.host.R.dimen.host_x64
        )) / 3.2f).roundToInt()
    }

    override fun getDefaultLayout(): Int = R.layout.vip_item_character_ip_item


    private fun fixAutoSize() {
        BaseApplication.getMainActivity()?.let {
            if (AutoSizeConfig.getInstance().application != null && !AutoSizeConfig.getInstance().isStop) {
                AutoSize.autoConvertDensityOfGlobal(it)
            }
        }
    }

    override fun buildView(parent: ViewGroup): View {
        fixAutoSize()
        return super.buildView(parent)
    }

    override fun bind(holder: ItemHolder) {
        ipItemModel.apply {
            holder.vTitle.setTag(R.id.vip_id_tag_contain_model, this)
            val imageUrl = if (BaseFragmentActivity2.sIsDarkMode) {
                if (ipImageDark.isNullOrEmpty()) {
                    ipImage
                } else {
                    ipImageDark
                }
            } else {
                ipImage
            }
            holder.vIpCover.updateLayoutParams<ConstraintLayout.LayoutParams> {
                width = coverWidth
            }
            ImageManager.from(context)
                .displayImageNotIncludeDownloadCacheSizeInDp(
                    holder.vIpCover,
                    imageUrl,
                    com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default_rectangle,
                    com.ximalaya.ting.android.host.R.drawable.host_ic_avatar_default_rectangle,
                    coverWidth,
                    coverWidth
                )
            ViewStatusUtil.setText(holder.vTitle, text)
            if (!describe.isNullOrEmpty()) {
                ViewStatusUtil.setText(holder.vIntro, describe)
                ViewStatusUtil.setVisible(View.VISIBLE, holder.vIntro)
            } else {
                ViewStatusUtil.setVisible(View.INVISIBLE, holder.vIntro)
            }
            holder.container.setOnOneClickListener {
                clickListener?.invoke(
                    VipCharacterIpEventHelper.ClickEventHelper.CLICK_CHARACTER_IP_ITEM,
                    position,
                    this
                )
            }
        }
    }

    class ItemHolder : EpoxyHolder() {
        lateinit var container: ConstraintLayout
        lateinit var vTitle: TextView
        lateinit var vIntro: TextView
        lateinit var vIpCover: ImageFilterView
        override fun bindView(itemView: View) {
            container = itemView as ConstraintLayout
            vIpCover = itemView.findViewById(R.id.vip_iv_character_cover)
            vTitle = itemView.findViewById(R.id.vip_tv_character_name)
            vIntro = itemView.findViewById(R.id.vip_tv_character_intro)
        }
    }
}