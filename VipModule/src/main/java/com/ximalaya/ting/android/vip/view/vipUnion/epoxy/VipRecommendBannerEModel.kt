package com.ximalaya.ting.android.vip.view.vipUnion.epoxy

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
import androidx.core.view.updateLayoutParams
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyHolder
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.ximalaya.ting.android.easyfloat.utils.DisplayUtils
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.autosize.AutoSize
import com.ximalaya.ting.android.framework.autosize.AutoSizeConfig
import com.ximalaya.ting.android.framework.manager.DynamicImageProcessor
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.Blur
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.common.RemoteTypefaceManager
import com.ximalaya.ting.android.host.util.common.smoothScrollToPositionWithOffset
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.ui.DrawableUtil.GradientDrawableBuilder
import com.ximalaya.ting.android.host.util.view.DomainColorUtil
import com.ximalaya.ting.android.host.util.view.LocalImageUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.util.view.setTextSizeSp
import com.ximalaya.ting.android.vip.R
import com.ximalaya.ting.android.vip.model.vipUnion.*
import com.ximalaya.ting.android.vip.util.VipFragmentUtil
import com.ximalaya.ting.android.vip.util.setOnOneClickListener
import com.ximalaya.ting.android.vip.util.vipUnion.VipChannelPageMaterialHelper
import com.ximalaya.ting.android.vip.view.EasyRecyclerViewHolder
import com.ximalaya.ting.android.vip.view.XmSubEpoxyRecyclerView
import com.ximalaya.ting.android.vip.view.vipUnion.VipCyclicAdapter
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Created by mark on 2024/6/12 13:54
 */
@EpoxyModelClass
abstract class VipRecommendBannerEModel :
    EpoxyModelWithHolder<VipRecommendBannerEModel.ItemHolder>() {
    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var context: Context

    @EpoxyAttribute
    lateinit var vipNewRecommendBannerModel: VipNewContentModel

    @EpoxyAttribute
    open var isFoldScreenExpandMode:Boolean = false

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var clickListener: ((Int, VipNewContentItem) -> Unit)? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var exposedListener: ((Int, VipNewContentItem?) -> Unit)? = null

    @EpoxyAttribute
    var vipType: Int = 0

    private var mRealItemCount = 0
        get() {
            if (field > 0) {
                return field
            }
            field = vipNewRecommendBannerModel.contents?.size ?: 0
            return field
        }

    override fun getDefaultLayout(): Int = R.layout.vip_item_new_recommend_banner

    private var mSelectedPosition: Int = 0
    private var mIsSwitchingByUser = false
    private var mPageChangeCallback: BannerOnPageChangeCallback? = null
    private var mAutoScrollRunnable: AutoScrollRunnable? = null
    private var mBannerAdapter: VipCyclicAdapter? = null
    private val mFragmentLifeCycleBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == VipChannelPageMaterialHelper.ACTION_FRAGMENT_LIFE_CYCLE_FILTER) {
                intent.extras?.let {
                    Logger.d("VipRecommendBannerEModel", "onReceive:$it")
                    if (it.getInt("vip_type") == vipType) {
                        if (it.getString("fragment_lifecycle") == "onResume") {
                            stopAutoScroll()
                            startAutoScroll()
                        } else if (it.getString("fragment_lifecycle") == "onPause") {
                            stopAutoScroll()
                        }
                    }
                }
            }
        }
    }

    private fun fixAutoSize(){
        BaseApplication.getMainActivity()?.let {
            if (AutoSizeConfig.getInstance().application != null && !AutoSizeConfig.getInstance().isStop) {
                AutoSize.autoConvertDensityOfGlobal(it)
            }
        }
    }

    override fun buildView(parent: ViewGroup): View {
        fixAutoSize()
        return super.buildView(parent)
    }


    override fun bind(holder: ItemHolder) {
        Logger.d("VipRecommendBannerEModel", "bind")
        LocalBroadcastManager.getInstance(context).registerReceiver(
            mFragmentLifeCycleBroadcastReceiver,
            IntentFilter(VipChannelPageMaterialHelper.ACTION_FRAGMENT_LIFE_CYCLE_FILTER)
        )
        vipNewRecommendBannerModel.contents?.filterNotNull()?.apply {
            if (holder.bannerView.adapter is VipCyclicAdapter
                && (holder.bannerView.adapter as VipCyclicAdapter).actualAdapter is BannerRecyclerAdapter
            ) {
                val adapter = holder.bannerView.adapter as VipCyclicAdapter
                (adapter.actualAdapter as BannerRecyclerAdapter).setData(this)
                adapter.notifyDataSetChanged()
            } else {
                mBannerAdapter = VipCyclicAdapter(
                    BannerRecyclerAdapter(
                        context,
                        this,
                        <EMAIL>
                    )
                )
                holder.bannerTabs.layoutManager =
                    LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                holder.bannerView.adapter = mBannerAdapter
                holder.bannerView.offscreenPageLimit = 3
            }
            holder.bannerTabsContainer.updateLayoutParams<ConstraintLayout.LayoutParams> {
                height = if (isFoldScreenExpandMode) 88.dp else 70.dp
            }
            holder.bannerTabs.updateLayoutParams<FrameLayout.LayoutParams> {
                height = if (isFoldScreenExpandMode) 64.dp else 54.dp
            }
            val pageChangeCallback = BannerOnPageChangeCallback(holder)
            mPageChangeCallback = pageChangeCallback
            holder.bannerView.registerOnPageChangeCallback(pageChangeCallback)
            if (mSelectedPosition == 0) {
                mSelectedPosition =
                    (holder.bannerView.getTag(R.id.vip_id_tag_item_position) as? Int)
                        ?: ((vipNewRecommendBannerModel.contents?.size ?: 20) * 1000)
            }
            val runnable = AutoScrollRunnable(holder)
            mAutoScrollRunnable = runnable
            holder.bannerView.setCurrentItem(mSelectedPosition, false)
            exposedListener?.invoke(getRealPosition(mSelectedPosition), getOrNull(getRealPosition(mSelectedPosition)))
            holder.bannerView.setTag(R.id.vip_id_tag_item_position, mSelectedPosition)
            updateScrollTabs(holder)
            HandlerManager.postOnUIThreadDelay4Kt(5000, runnable)
        }
    }

    private fun updateScrollTabs(holder: ItemHolder) {
        val realPosition = getRealPosition(mSelectedPosition)
        holder.bannerTabs.withModels {
            vipNewRecommendBannerModel.contents?.filterNotNull()
                ?.forEachIndexed { index, vipNewContentItem ->
                    vipRecommendBannerTabE {
                        id("banner-tab-${index}_${vipNewContentItem.albumId}")
                        context(holder.container.context)
                        isFoldScreenExpandMode(isFoldScreenExpandMode)
                        tabItem(vipNewContentItem)
                        isTabSelected(index == realPosition)
                        clickListener { isSelected, vipNewContentItem ->
                            setCurrentItemWithRealPosition(index, holder)
                        }
                    }
                }
        }
        scrollToPositionTab(holder.bannerTabs, realPosition, true)
    }

    private fun setCurrentItemWithRealPosition(position: Int, holder: ItemHolder) {
        if (mRealItemCount > 0) {
            val ratio = mSelectedPosition / mRealItemCount
            val targetPosition = ratio * mRealItemCount + position
            holder.bannerView.setCurrentItem(targetPosition, true)
        } else {
            holder.bannerView.setCurrentItem(position, false)
        }
    }

    // 滚动到指定tab并且居中
    private fun scrollToPositionTab(recyclerView: RecyclerView, position: Int, smooth: Boolean) {

        var offset =
            (recyclerView.width - 54.dp) / 2 - 13.dp // 7 + 6 + 14/2为斜杠到文字的距离
        if (offset < 0) {
            offset = 0
        }
        if (smooth) {
            recyclerView.smoothScrollToPositionWithOffset(position, offset)
        } else {
            val manager = recyclerView.layoutManager as? LinearLayoutManager ?: return
            manager.scrollToPositionWithOffset(position, offset)
        }
    }

    private fun getRealPosition(position: Int): Int {
        var realPosition = 0
        val dataSize = vipNewRecommendBannerModel.contents?.size ?: 0
        if (dataSize <= 0) return position
        if (position >= dataSize) {
            realPosition = position % dataSize
        } else {
            if (position >= 0) {
                realPosition = position
            }
        }
        return realPosition
    }

    private fun stopAutoScroll() {
        mAutoScrollRunnable?.let {
            HandlerManager.removeCallbacks(it)
        }
    }

    private fun startAutoScroll() {
        mAutoScrollRunnable?.let {
            HandlerManager.postOnUIThreadDelay(it, 5000)
        }
    }

    override fun unbind(holder: ItemHolder) {
        super.unbind(holder)
        Logger.d("VipRecommendBannerEModel", "unbind")
        stopAutoScroll()
        mPageChangeCallback?.let {
            holder.bannerView.unregisterOnPageChangeCallback(it)
        }
        LocalBroadcastManager.getInstance(context)
            .unregisterReceiver(mFragmentLifeCycleBroadcastReceiver)
    }

    inner class AutoScrollRunnable(val holder: ItemHolder) : Runnable {
        override fun run() {
            if (!mIsSwitchingByUser) {
                val targetItem = if (holder.bannerView.currentItem == mSelectedPosition) {
                    mSelectedPosition + 1
                } else {
                    mSelectedPosition
                }
                holder.bannerView.setCurrentItem(targetItem, true)
                holder.bannerView.setTag(R.id.vip_id_tag_item_position, targetItem)
            }
        }
    }

    inner class BannerOnPageChangeCallback(val holder: ItemHolder) :
        ViewPager2.OnPageChangeCallback() {
        override fun onPageScrollStateChanged(state: Int) {
            super.onPageScrollStateChanged(state)
            if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                mIsSwitchingByUser = true
            } else if (state == ViewPager2.SCROLL_STATE_IDLE) {
                mIsSwitchingByUser = false
            }
            if (mIsSwitchingByUser) {
                stopAutoScroll()
            } else {
                stopAutoScroll()
                startAutoScroll()
            }
        }

        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            if (position != mSelectedPosition) {
                mSelectedPosition = position
                exposedListener?.invoke(
                    getRealPosition(mSelectedPosition),
                    vipNewRecommendBannerModel.contents?.getOrNull(getRealPosition(mSelectedPosition))
                )
                updateScrollTabs(holder)
                if (mIsSwitchingByUser) {
                    mIsSwitchingByUser = false
                }
                stopAutoScroll()
                startAutoScroll()
            }
        }
    }

    class ItemHolder : EpoxyHolder() {
        lateinit var container: FrameLayout
        lateinit var bannerView: ViewPager2
        lateinit var bannerTabs: XmSubEpoxyRecyclerView
        lateinit var bannerTabsContainer:FrameLayout
        override fun bindView(itemView: View) {
            container = itemView as FrameLayout
            bannerView = itemView.findViewById(R.id.vip_vp_banner)
            bannerTabsContainer = itemView.findViewById(R.id.vip_fl_indicator_Container)
            bannerTabs = itemView.findViewById(R.id.vip_rv_banner_tabs)
        }
    }

    class BannerRecyclerAdapter(
        private val context: Context,
        private var data: List<VipNewContentItem>,
        private val clickFunc: ((Int, VipNewContentItem) -> Unit)?
    ) :
        RecyclerView.Adapter<EasyRecyclerViewHolder>() {

        private val mCachedBgColor = mutableMapOf<String, Int>()
        private fun fixAutoSize(){
            BaseApplication.getMainActivity()?.let {
                if (AutoSizeConfig.getInstance().application != null && !AutoSizeConfig.getInstance().isStop) {
                    AutoSize.autoConvertDensityOfGlobal(it)
                }
            }
        }
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EasyRecyclerViewHolder {
            fixAutoSize()
            return EasyRecyclerViewHolder.get(
                context,
                null,
                parent,
                R.layout.vip_item_new_recommend_banner_item
            )
        }

        fun setData(data: List<VipNewContentItem>?) {
            data ?: return
            this.data = data
        }

        override fun onBindViewHolder(holder: EasyRecyclerViewHolder, position: Int) {
            holder.getView<View>(R.id.vip_view_indicator_placeholder)
                ?.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    height =
                        if (VipFragmentUtil.FoldScreenUtils.isFoldScreenInExpandMode(context)) {
                            88.dp
                        } else {
                            70.dp
                        }
                }
            val banner = if (position >= 0 && position < data.size) data[position] else null
            banner?.let {
                holder.setText(R.id.vip_tv_banner_title, it.imageName)
                holder.getView<TextView>(R.id.vip_tv_banner_title)?.setTextSizeSp(
                    if (VipFragmentUtil.FoldScreenUtils.isFoldScreenInExpandMode(context)) {
                        25
                    } else {
                        20
                    }
                )
                RemoteTypefaceManager.setRemoteTypeface(
                    RemoteTypefaceManager.RemoteTypeface.SourceHanSerifBold,
                    holder.getView(R.id.vip_tv_banner_title)
                )
                val coverWidth =
                    if (VipFragmentUtil.FoldScreenUtils.isFoldScreenInExpandMode(context)) {
                        BaseUtil.getScreenWidth(context) - 2 * context.resources.getDimensionPixelSize(
                            com.ximalaya.ting.android.host.R.dimen.host_default_side_margin
                        ) - 2 * 90.dp
                    } else {
                        BaseUtil.getScreenWidth(context) - 2 * context.resources.getDimensionPixelSize(
                            com.ximalaya.ting.android.host.R.dimen.host_default_side_margin
                        )
                    }
                val coverHeight =
                    if(VipFragmentUtil.FoldScreenUtils.isFoldScreenInExpandMode(context)){
                        (coverWidth * 230 / (1f * 428.85f)).toInt()
                    }else{
                        (coverWidth * 184 / (1f * 343)).toInt()
                    }
                holder.getView<ImageView>(R.id.vip_iv_banner_item)?.let { cover ->
                    cover.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        width = coverWidth
                        height = coverHeight
                    }
                    val coverUrl = it.image
                    if (!coverUrl.isNullOrEmpty()) {
                        ImageManager.from(context).displayImage(
                            cover,
                            coverUrl,
                            com.ximalaya.ting.android.host.R.drawable.host_default_focus_img,
                            coverWidth,
                            coverHeight
                        ) { lastUrl, bitmap ->
                            if (lastUrl == coverUrl || lastUrl == DynamicImageProcessor.getSingleInstance()
                                    .fitNoWebpImage(coverUrl, coverWidth, coverHeight)
                            ) {
                                if (VipFragmentUtil.FoldScreenUtils.isFoldScreenInExpandMode(context)) {
                                    holder.getView<ImageView>(R.id.vip_iv_banner_item_blur_bg)
                                        ?.let { blurBg ->
                                            blurBg.setImageBitmap(
                                                Blur.fastBlur(
                                                    context, bitmap, 8, 20
                                                )
                                            )
                                            blurBg.setOnOneClickListener {
                                                clickFunc?.invoke(position, it)
                                            }
                                        }
                                }
                                var bgColor = mCachedBgColor[coverUrl]
                                if (bgColor == null) {
                                    DomainColorUtil.getDomainColorForRecommend(
                                        bitmap,
                                        Color.BLACK,
                                        object : LocalImageUtil.Callback {
                                            override fun onMainColorGot(color: Int) {
                                                val fixedColor =
                                                    ColorUtil.covertColorToFixedSaturationAndLightness(
                                                        color,
                                                        0.21f,
                                                        0.16f
                                                    )
                                                mCachedBgColor[coverUrl] = fixedColor
                                                updateBackground(fixedColor, holder)
                                            }
                                        })
                                } else {
                                    updateBackground(bgColor, holder)
                                }
                            }
                        }
                    } else {
                        cover.setImageResource(R.drawable.host_default_focus_img)
                        updateBackground(0xFF30291F.toInt(), holder)
                    }

                    cover.setOnOneClickListener {
                        clickFunc?.invoke(position, it)
                    }
                }
                holder.getView<LinearLayout>(R.id.vip_ll_recommend_reason_container)?.setOnOneClickListener {
                    clickFunc?.invoke(position, it)
                }
                val reason1 = holder.getView<View>(R.id.vip_view_recommend_reason_1)
                val reason2 = holder.getView<View>(R.id.vip_view_recommend_reason_2)
                val reason3 = holder.getView<View>(R.id.vip_view_recommend_reason_3)
                val reasonViews = arrayOf(reason1, reason2, reason3)
                ViewStatusUtil.setVisible(View.INVISIBLE, reason1, reason2, reason3)
                it.recommendReasons?.forEachIndexed { index, vipNewContentRecommendReason ->
                    if (index < 3) {
                        updateRecommendReason(reasonViews[index], vipNewContentRecommendReason)
                    }
                }
            }
        }

        private fun updateRecommendReason(reasonView: View, reason: VipNewContentRecommendReason) {
            ViewStatusUtil.setVisible(View.VISIBLE, reasonView)
            reasonView.findViewById<TextView>(R.id.vip_tv_recommend_label).let {
                it.text = reason.text
                RemoteTypefaceManager.setRemoteTypeface(
                    RemoteTypefaceManager.RemoteTypeface.SourceHanSerifBold,
                    it
                )
                it.setTextSizeSp(
                    if (VipFragmentUtil.FoldScreenUtils.isFoldScreenInExpandMode(context)) {
                        17.5f
                    } else {
                        15f
                    }
                )
            }

            reasonView.findViewById<TextView>(R.id.vip_tv_recommend_reason).text = reason.subText
            reasonView.findViewById<TextView>(R.id.vip_tv_recommend_reason).setTextSizeSp(
                if (VipFragmentUtil.FoldScreenUtils.isFoldScreenInExpandMode(context)) {
                    12.5f
                } else {
                    10f
                }
            )
        }

        private fun updateBackground(color: Int, holder: EasyRecyclerViewHolder) {
            holder.getView<View>(R.id.vip_view_bottom_space)?.setBackgroundColor(color)
//            fixPlaceholderHeight(holder.getView<View>(R.id.vip_view_indicator_placeholder))
            holder.getView<View>(R.id.vip_cover_bottom_mask)?.background =
                GradientDrawableBuilder().color(
                    intArrayOf(color, Color.TRANSPARENT)
                ).orientation(GradientDrawable.Orientation.BOTTOM_TOP).build()
        }

//        private fun fixPlaceholderHeight(placeholder: View?) {
//            placeholder?.layoutParams?.let {
//                Logger.i("Mark11221s", "height: = ${it.height},70.dp:${70.dp}")
//                if (it.height != 70.dp) {
//                    it.height = 70.dp
//                    placeholder.layoutParams = it
//                }
//            }
//        }

        override fun getItemCount(): Int {
            return data.size
        }
    }

}