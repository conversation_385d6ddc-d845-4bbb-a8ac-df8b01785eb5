<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="270dp"
    android:layout_height="wrap_content"
    android:background="@drawable/vip_bg_rect_ffffffff_ff282828_radius_10">

    <ImageView
        android:id="@+id/vip_kid_agreement_close"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/vip_ic_kid_agreement_dialog_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/vip_kid_agreement_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:text="服务协议与隐私保护"
        android:textColor="@color/host_color_333333_ffffff"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/vip_kid_agreement_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="8dp"
        android:textColor="@color/host_color_666666_8d8d91"
        android:textColorHighlight="@color/host_transparent"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vip_kid_agreement_title"
        tools:text="已阅读并同意《会员服务协议》 及《自动续费服务规则》，确认开通该套餐。" />

    <Button
        android:id="@+id/vip_kid_agreement_btn"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/vip_kids_bg_purchase_button"
        android:text="确认并开通"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vip_kid_agreement_content" />

</androidx.constraintlayout.widget.ConstraintLayout>