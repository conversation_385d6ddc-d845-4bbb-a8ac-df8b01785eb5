<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/host_x8"
    android:minWidth="94dp">

    <View
        android:id="@+id/vip_view_bg_mask"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/vip_bg_master_face_to_face_tab" />

    <TextView
        android:id="@+id/vip_tv_tab_month"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/vip_color_bbbbbb_59ffffff"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@+id/vip_tv_tab_master_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        android:text="更多大师" />

    <TextView
        android:id="@+id/vip_tv_tab_master_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/vip_color_bbbbbb_59ffffff"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vip_tv_tab_month"
        android:text="敬请期待" />


</androidx.constraintlayout.widget.ConstraintLayout>