<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/vip_ll_tab_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:paddingRight="10dp">

    <ImageView
        android:id="@+id/vip_v_rank_title_line"
        android:layout_width="6dp"
        android:layout_height="10dp"
        android:layout_marginRight="10dp"
        android:src="@drawable/vip_ic_rank_tab_divider" />

    <ImageView
        android:id="@+id/vip_iv_rank_title_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/vip_ic_rank_feather"
        android:tint="@color/host_color_111111_ffffff" />

    <TextView
        android:id="@+id/vip_tv_ting_tab_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="2dp"
        android:fontFamily="sans-serif-light"
        android:gravity="center"
        android:textColor="@color/host_color_111111_ffffff"
        android:textSize="13sp"
        tools:text="四大名著" />

    <ImageView
        android:id="@+id/vip_iv_rank_title_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:rotationY="180"
        android:src="@drawable/vip_ic_rank_feather"
        android:tint="@color/host_color_111111_ffffff" />
</LinearLayout>