package com.ximalaya.ting.android.framework.adapter;

import android.content.Context;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.widget.BaseAdapter;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.BaseApplication;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version 2015-6-18 下午7:57:36
 *
 * 抽象适配器（免去一些有共性的代码）
 *
 */
public abstract class AbstractAdapter<T> extends BaseAdapter {
	
	protected Context context;
	
	protected List<T> listData;
	
	protected LayoutInflater layoutInflater;
	
	public AbstractAdapter(Context context,List<T> listData){
		this.context = context;
		if (this.context == null) {
			this.context = BaseApplication.getTopActivity();
		}
		this.listData = listData;
		layoutInflater = LayoutInflater.from(context);
	}

	@Override
	public int getCount() {
		if(listData!=null){
			return listData.size();
		}
		return 0;
	}

	@Override
	public Object getItem(int position) {
		//List的index为0会抛出异常，listview的index需要和headerview进行计算，此处可能小于0
		if (position<0){
			return null;
		}
		if(listData!=null && getCount()>0&&position<listData.size()){
			return listData.get(position);
		}
		return null;
	}
	
	public void updateItem(T item){
		if(listData==null)
			return;
		int index = listData.indexOf(item);
		if(index>=0){
			listData.set(index, item);
		}
	}
	
	public int indexOf(T item){
		if(listData!=null)
			return listData.indexOf(item);
		else{
			return -1;
		}
	}

	@Override
	public long getItemId(int position) {
		return position;
	}
	
	public boolean containItem(T item){
		
		if(listData==null)
			return false;
		else{
			return listData.contains(item);
		}
		
	}

	@Nullable
	public List<T> getListData() {
		return listData;
	}

	public void setListData(List<T> data) {
		this.listData = data;
	}
	
	public void addListDataWithoutNotify(List<T> data){
		if(listData==null){
			listData = data;
		}else{
			listData.addAll(data);
		}
	}

	@SuppressWarnings("unchecked")
	public void addListData(List<T> data){
		if(listData==null){
			listData = data;
		}else{
			listData.addAll(data);
		}
		notifyDataSetChanged();
	}

    @SuppressWarnings("unchecked")
    public void addListData(int position, List<T> data) {
        if (listData == null) {
            listData = data;
        } else {
            listData.addAll(position, data);
        }
        notifyDataSetChanged();
    }

    public void deleteListData(int position) {
        if(listData!=null&&listData.size()>position){
			listData.remove(position);
			notifyDataSetChanged();
		}
	}
	
	public void deleteListData(T data) {
		if(listData!=null){
			listData.remove(data);
			notifyDataSetChanged();
		}
	}

	public void deleteListDatas(List<T> data) {
		if(listData!=null){
			listData.removeAll(data);
			notifyDataSetChanged();
		}
	}

	public void clear() {
		if(listData!=null){
			listData.clear();
			notifyDataSetChanged();
		} else {
			listData = new ArrayList<>();
		}
	}

	public void resetData(List<T> data){
		if(listData!=null){
			listData.clear();
			listData.addAll(data);
		} else {
			listData = new ArrayList<>();
			listData.addAll(data);
		}
	}
	
	@Override
	public void notifyDataSetChanged() {
		if(!isInMainLooper("notifyDataSetChanged")){
			return;
		}
		super.notifyDataSetChanged();
	}
	
	@Override
	public void notifyDataSetInvalidated() {
		if(!isInMainLooper("notifyDataSetInvalidated")){
			return;
		}
		super.notifyDataSetInvalidated();
	}
	
	
	private boolean isInMainLooper(String name){
		if(Looper.myLooper()!=Looper.getMainLooper()){
			Log.e("ERROR", "不能在线程中调用"+name+"方法"+getClass().getName());
			return false;
		}
		return true;
	}

	

}

