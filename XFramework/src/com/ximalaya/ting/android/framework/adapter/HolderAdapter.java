package com.ximalaya.ting.android.framework.adapter;

import android.content.Context;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ListView;
import android.widget.Toast;

import com.ximalaya.ting.android.framework.R;
import com.ximalaya.ting.android.framework.util.fixtoast.ToastCompat;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version 2015-6-18 下午7:58:54
 * 
 *          通用适配器（适合一些常规的适配器）
 * 
 */
public abstract class HolderAdapter<T> extends AbstractAdapter<T> implements
		OnClickListener, View.OnLongClickListener {

	public HolderAdapter(Context context, List<T> listData) {
		super(context, listData);
	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {
		BaseViewHolder holder = null;
		if (convertView == null) {

			convertView = layoutInflater.inflate(getConvertViewId(), parent, false);

			holder = buildHolder(convertView);

			convertView.setTag(holder);
		} else {
			holder = (BaseViewHolder) convertView.getTag();
		}
		if (position < getCount()) {
			bindViewDatas(holder, (T)getItem(position), position);
		}else{
			if(ConstantsOpenSdk.isDebug){
				throw new RuntimeException(getClass().getName()+" error:getView listData:"+listData+"position:"+position);
			}
		}

		return convertView;
	}

	public void updateSingleItemByPos(ListView listView, int posInData) {
		int firstVisiblePosition = listView.getFirstVisiblePosition();
		int lastVisiblePosition = listView.getLastVisiblePosition();
		if (posInData >= firstVisiblePosition && posInData <= lastVisiblePosition) {
			View itemView = listView.getChildAt(posInData
					- firstVisiblePosition + listView.getHeaderViewsCount());
			this.updateViewItem(itemView, posInData);
		}
	}

	public void updateSingleItem(ListView listView, T data) {
		/**第一个可见的位置**/
		int firstVisiblePosition = listView.getFirstVisiblePosition();
		/**最后一个可见的位置**/
		int lastVisiblePosition = listView.getLastVisiblePosition();
		int position = indexOf(data);
		if (position == -1) {
			Logger.w("HolderAdapter", "没有找到数据，position == -1");
			return;
		}
		/**在看见范围内才更新，不可见的滑动后自动会调用getView方法更新**/
		if (position + listView.getHeaderViewsCount() >= firstVisiblePosition
				&& position + listView.getHeaderViewsCount() <= lastVisiblePosition) {
			/**获取指定位置view对象**/
			View view = listView.getChildAt(position - firstVisiblePosition + listView.getHeaderViewsCount());
			updateViewItem(view, position);
		}
	}

	public void updateViewItem(View itemView, int position) {
		if(listData == null){
			if(ConstantsOpenSdk.isDebug){
				throw new NullPointerException("your listData is Null");
			}
			return;
		}
		
		if(itemView == null || position<0 || position > (listData.size() - 1)){
			if(ConstantsOpenSdk.isDebug){
				throw new NullPointerException("itemView == null Or position error; position = " + position + " listData size = " + listData.size());
			}
			return;
		}
		if (itemView.getTag() instanceof BaseViewHolder) {
			bindViewDatas((BaseViewHolder) itemView.getTag(), listData.get(position), position);
		}
	}

	/**
	 * 
	 * 给view绑定事件
	 * 
	 * @param view
	 * @param t
	 * @param position
	 */
	public void setClickListener(View view, T t, int position,
			BaseViewHolder viewHolder) {
		if(view == null){
			return;
		}
		view.setOnClickListener(this);
        view.setTag(R.id.framework_view_holder_position, new Integer(position));
        view.setTag(R.id.framework_view_holder_data, t);
        view.setTag(R.id.framework_view_holder, viewHolder);

		AutoTraceHelper.bindData(view,t);
    }

	protected void setLongClickListener(View view, T t, int position, BaseViewHolder holder){
		if (view == null) {
			return;
		}
		view.setTag(R.id.framework_view_holder_position, new Integer(position));
		view.setTag(R.id.framework_view_holder_data, t);
		view.setTag(R.id.framework_view_holder, holder);
		view.setOnLongClickListener(this);
	}

	@Override
	public void onClick(View view) {
        int position = 0;
        Object obj = view.getTag(R.id.framework_view_holder_position);
        if (obj instanceof Integer) {
            position = ((Integer) obj).intValue();
        }

        T t = (T) view.getTag(R.id.framework_view_holder_data);
        BaseViewHolder holder = (BaseViewHolder) view.getTag(R.id.framework_view_holder);
        onClick(view, t, position, holder);
	}

	@Override
	public boolean onLongClick(View view) {
		int position = 0;
		Object obj = view.getTag(R.id.framework_view_holder_position);
		if (obj instanceof Integer) {
			position = ((Integer) obj).intValue();
		}

		T model = (T) view.getTag(R.id.framework_view_holder_data);
		BaseViewHolder holder = (BaseViewHolder) view.getTag(R.id.framework_view_holder);
		return onLongClick(view, model, position, holder);
	}

	public abstract void onClick(View view, T t, int position, BaseViewHolder holder);

	protected boolean onLongClick(View view, T model, int position, BaseViewHolder holder) {
		return false;
	}

	/**
	 * 获取item布局文件id
	 * 
	 * @return
	 */
	public abstract int getConvertViewId();

	/**
	 * 建立视图Holder
	 * 
	 * @param convertView
	 * @return
	 */
	public abstract BaseViewHolder buildHolder(View convertView);

	/**
	 * 绑定数据
	 * 
	 * @param holder
	 * @param t
	 * @param position
	 */
	public abstract void bindViewDatas(BaseViewHolder holder, T t, int position);

	/**
	 * 持有Item相关的类，减少findviewbyid的时间
	 */
	public static class BaseViewHolder {

	}

	protected void showToast(String str) {
		if (context != null) {
			ToastCompat.makeText(context, str, Toast.LENGTH_SHORT).show();
		}
	}
}
