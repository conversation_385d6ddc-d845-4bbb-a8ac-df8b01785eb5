package com.ximalaya.ting.android.framework.hotfixtrace;

import android.text.TextUtils;

import com.ximalaya.ting.android.opensdk.util.AsyncGson;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.HashSet;
import java.util.Set;

public class HotFixMonitor {
    private static boolean IS_HAS_REPORT_DOWNLOAD_START;
    private long mStartDownloadPatchTime;
    private Set<String> mHasSendTrace;

    private HotFixMonitor() {
        mHasSendTrace = new HashSet<>();
    }

    public static HotFixMonitor getInstance() {
        return HotFixMonitor.HolderClass.instance;
    }

    public void traceHotFix(String traceType) {
        traceHotFix(traceType, 0, "", 0, "");
    }

    public void traceHotFix(String traceType, int code, String message) {
        traceHotFix(traceType, code, message, 0, "");
    }

    public void traceHotFix(String traceType, int code, String message, long costTime) {
        traceHotFix(traceType, code, message, costTime, "");
    }

    public void traceHotFix(String traceType, int code, String message, long costTime, String pathVersion) {
        HotFixTrace hotFixTrace = new HotFixTrace(traceType, code, message, costTime, pathVersion);
        postHotFix(hotFixTrace);
    }

    public void postHotFix(HotFixTrace hotFixTrace) {
//        if (hotFixTrace == null || TextUtils.isEmpty(hotFixTrace.getTraceType())){
//            return;
//        }
//        String uniqueString = hotFixTrace.getUniqueString();
//        if (mHasSendTrace.contains(uniqueString)) {
//            return;
//        }
//        mHasSendTrace.add(uniqueString);
//        if (hotFixTrace.getTraceType() == HotFixTrace.TYPE_FINISH_DOWNLOAD) {
//            IS_HAS_REPORT_DOWNLOAD_START = false;
//            hotFixTrace.setCostTime(System.currentTimeMillis() - mStartDownloadPatchTime);
//            mStartDownloadPatchTime = 0;
//        }
//        if (hotFixTrace.getTraceType() == HotFixTrace.TYPE_START_DOWNLOAD) {
//            if (IS_HAS_REPORT_DOWNLOAD_START) {
//                return;
//            }
//            mStartDownloadPatchTime = System.currentTimeMillis();
//            IS_HAS_REPORT_DOWNLOAD_START = true;
//        }
//        try {
//            Logger.i("HotFixMonitor", hotFixTrace.toString());
//            new AsyncGson<String>().toJsonResultOnThread(hotFixTrace, new AsyncGson.IResult<String>() {
//                @Override
//                public void postResult(String result) {
//                    XmLogger.syncLog("apm", "hotFixTrace", result);
//                    Logger.i("HotFixMonitor:___hotFixTrace", result);
//                }
//
//                @Override
//                public void postException(Exception e) {
//                }
//            });
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    private static class HolderClass {
        private final static HotFixMonitor instance = new HotFixMonitor();
    }
}
