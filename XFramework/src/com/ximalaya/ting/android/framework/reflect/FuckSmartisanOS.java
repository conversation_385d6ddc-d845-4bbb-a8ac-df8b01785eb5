package com.ximalaya.ting.android.framework.reflect;

import androidx.annotation.NonNull;
import android.view.View;
import android.view.Window;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.List;


/**
 * Created by roc on 2017/7/11.
* <AUTHOR>
 */

public class FuckSmartisanOS {

    /**
     * 修复部分锤子手机崩溃：
     *
     * java.lang.NullPointerException
     * Attempt to invoke virtual method 'void android.view.ViewRootImpl.getScreenSizeOffset(int[])' on a null object reference
     * android.widget.PressGestureDetector$3.run(PressGestureDetector.java:148)
     * android.os.Handler.handleCallback(Handler.java:739)
     * android.os.Handler.dispatchMessage(Handler.java:95)
     * android.os.Looper.loop(Looper.java:148)
     * android.app.ActivityThread.main(ActivityThread.java:5541)
     * java.lang.reflect.Method.invoke(Native Method)
     * com.android.internal.os.ZygoteInit$MethodAndArgsCaller.run(ZygoteInit.java:935)
     * com.android.internal.os.ZygoteInit.main(ZygoteInit.java:726)
     *
     * 详情链接：
     * https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/8457/report?bundleId=&channelId=&date=all&pid=1&search=PressGestureDetector&searchType=detail&start=0&tagList=&version=all
     *
     * @param window
     */
    public static void fuckSmartisanOSNPE(@NonNull Window window){

        if(android.os.Build.VERSION.SDK_INT<22||android.os.Build.VERSION.SDK_INT>23||window==null) {
            return;
        }

        try {
            final View decorView = window.getDecorView();
            Object pressGestureDetectorOld = FieldUtils.readField(decorView,"mPressGestureDetector");
            final Object imageBoomRunnableOld = FieldUtils.readField(pressGestureDetectorOld,"mImageBoomRunnable");
            List<Class<?>> interfaces = Utils.getAllInterfaces(imageBoomRunnableOld.getClass());
            Class[] ifs = interfaces != null && interfaces.size() > 0 ? interfaces.toArray(new Class[interfaces.size()]) : new Class[0];
            Object proxy = Proxy.newProxyInstance(imageBoomRunnableOld.getClass().getClassLoader(), ifs,
                    new InvocationHandler() {
                        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                            try {
                                return method.invoke(imageBoomRunnableOld, args);
                            }catch (Exception e){
                                e.printStackTrace();
                            }
                            return null;
                        }
                    });
            FieldUtils.writeField(pressGestureDetectorOld, "mImageBoomRunnable", proxy);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
