package com.ximalaya.ting.android.framework.view.snackbar.enums;

/**
 * <AUTHOR>
 */
public enum SnackbarType {

    /**
     * Snackbar with a single line
     */
    SINGLE_LINE(48, 51, 1,false),
    /**
     * Snackbar with a single line
     */
    MARQUEE_SINGLE_LINE(48, 51, 1,true),
    /**
     * Snackbar with two lines
     */
    MULTI_LINE(48, 83, 2,false);

    private int minHeight;
    private int maxHeight;
    private int maxLines;
    private boolean marquee;

    SnackbarType(int minHeight, int maxHeight, int maxLines,boolean marquee) {
        this.minHeight = minHeight;
        this.maxHeight = maxHeight;
        this.maxLines = maxLines;
        this.marquee = marquee;
    }

    public int getMinHeight() {
        return minHeight;
    }

    public int getMaxHeight() {
        return maxHeight;
    }

    public int getMaxLines() {
        return maxLines;
    }

    public boolean isMarquee(){
        return marquee;
    }
}
