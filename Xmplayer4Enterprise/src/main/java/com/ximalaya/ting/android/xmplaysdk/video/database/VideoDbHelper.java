package com.ximalaya.ting.android.xmplaysdk.video.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.ximalaya.ting.android.xmplaysdk.video.VideoDataSource;

import java.util.List;

/**
 * Created by sigma on 2018/1/31.
 *
 * <AUTHOR>
 */
public class VideoDbHelper {

    private SQLiteDatabase mDatabase;

    private static class Holder {
        private static final VideoDbHelper INSTANCE = new VideoDbHelper();
    }

    public static VideoDbHelper getInstance() {
        return Holder.INSTANCE;
    }

    private VideoDbHelper() {
        mDatabase = new SQLiteOpenHelperInner().getWritableDatabase();
    }

    public void insert(Video video) {
        long currentTime = System.currentTimeMillis();
        video.setCreateTime(currentTime);
        video.setLastUseTime(currentTime);
        ContentValues values = Video.bean2ContentValues(video);
        long id = mDatabase.insert(Video.TABLE_NAME, null, values);
        video.setId(id);
    }

    public void insert(VideoPart videoPart) {
        long currentTime = System.currentTimeMillis();
        videoPart.setCreateTime(currentTime);
        videoPart.setLastUseTime(currentTime);
        ContentValues values = VideoPart.bean2ContentValues(videoPart);
        long id = mDatabase.insert(VideoPart.TABLE_NAME, null, values);
        videoPart.setId(id);
    }

    private static final String QUERY_ELDEST_VIDEO = "SELECT * FROM " + Video.TABLE_NAME +
            " ORDER BY " + Video.LAST_USE_TIME + " LIMIT 1";

    public List<Video> queryAllVideo() {
        Cursor cursor = mDatabase.query(Video.TABLE_NAME, null, null, null, null, null, null);
        return Video.cursor2Beans(cursor);
    }

    public void deleteAllVideo() {
        mDatabase.delete(Video.TABLE_NAME, null, null);
        mDatabase.delete(VideoPart.TABLE_NAME, null, null);
    }

    public void deleteAllVideoPart(long videoId) {
        mDatabase.delete(VideoPart.TABLE_NAME, VideoPart.VIDEO_ID + "=?",
                new String[]{String.valueOf(videoId)});
    }

    public Video queryEldestVideo() {
        Cursor cursor = mDatabase.rawQuery(QUERY_ELDEST_VIDEO, null);
        if (cursor != null && cursor.moveToFirst()) {
            return Video.cursor2Bean(cursor);
        }
        return null;
    }

    public Video queryVideoByUrl(String url) {
        Cursor cursor = mDatabase.query(Video.TABLE_NAME, null, Video.URL + "=?", new String[]{url},
                null, null, null);
        return Video.cursor2Bean(cursor);
    }

    public int deleteVideoByUrl(String url) {
        int result = mDatabase.delete(Video.TABLE_NAME, Video.URL + "=?", new String[]{url});
        return result;
    }

    public List<VideoPart> queryAllVideoPart(long videoId) {
        Cursor cursor = mDatabase.query(VideoPart.TABLE_NAME, null, VideoPart.VIDEO_ID + "=?",
                new String[]{String.valueOf(videoId)}, null, null, null);
        return VideoPart.cursor2Beans(cursor);
    }

    public void deleteVideo(long id) {
        String[] whereArgs = new String[]{String.valueOf(id)};
        mDatabase.delete(Video.TABLE_NAME, Video.ID + "=?", whereArgs);
        mDatabase.delete(VideoPart.TABLE_NAME, VideoPart.VIDEO_ID + "=?", whereArgs);
    }

    public void deleteVideoPart(long id) {
        mDatabase.delete(VideoPart.TABLE_NAME, VideoPart.ID + "=?",
                new String[]{String.valueOf(id)});
    }

    public void update(Video video) {
        long currentTime = System.currentTimeMillis();
        video.setLastUseTime(currentTime);
        ContentValues values = Video.bean2ContentValues(video);
        values.put(Video.ID, video.getId());
        mDatabase.update(Video.TABLE_NAME, values, Video.ID + "=?",
                new String[]{String.valueOf(video.getId())});
    }

    public void update(VideoPart videoPart) {
        long currentTime = System.currentTimeMillis();
        videoPart.setLastUseTime(currentTime);
        ContentValues values = VideoPart.bean2ContentValues(videoPart);
        values.put(VideoPart.ID, videoPart.getId());
        mDatabase.update(VideoPart.TABLE_NAME, values, VideoPart.ID + "=?",
                new String[]{String.valueOf(videoPart.getId())});
    }

    private static class SQLiteOpenHelperInner extends SQLiteOpenHelper {
        public static final String DB_NAME = "VideoCache.db";
        public static final int VERSION = 1;

        public SQLiteOpenHelperInner() {
            super(VideoDataSource.getInstance().getConfig().context, DB_NAME, null, VERSION);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            db.execSQL(Video.CREATE_TABLE_SQL);
            db.execSQL(VideoPart.CREATE_TABLE_SQL);
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            db.execSQL("DROP TABLE " + Video.TABLE_NAME);
            db.execSQL("DROP TABLE " + VideoPart.TABLE_NAME);
            db.execSQL(Video.CREATE_TABLE_SQL);
            db.execSQL(VideoPart.CREATE_TABLE_SQL);
        }
    }

}
