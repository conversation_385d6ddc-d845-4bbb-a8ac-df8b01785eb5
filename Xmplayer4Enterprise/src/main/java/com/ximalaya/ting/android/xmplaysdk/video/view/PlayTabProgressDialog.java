package com.ximalaya.ting.android.xmplaysdk.video.view;

import android.content.Context;
import androidx.annotation.NonNull;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.ximalaya.ting.android.xmplaysdk.video.R;
import com.ximalaya.ting.android.xmplaysdk.video.utils.Utils;

/**
 * Created by sigma on 2018/1/19.
 * <AUTHOR>
 */
public class PlayTabProgressDialog extends VideoGestureDialog {

    private static final int MAX = 100;
    private TextView mTvProgress;
//    private ProgressBar mPbProgress;
    private long mDuration;

    public PlayTabProgressDialog(@NonNull Context context, int position, long duration) {
        super(context);
        mTvProgress = getContentView().findViewById(R.id.video_tv_progress);
//        mPbProgress = getContentView().findViewById(R.id.video_pb_progress);
//        mPbProgress.setMax(MAX);
        mDuration = duration;
        updatePosition(position);
    }

    public void setDuration(long duration){
        mDuration = duration;
    }

    public void updatePosition(long position) {
        if (position < 0) {
            position = 0;
        }
        position = Math.min(position, mDuration);
        String text = Utils.parseTimeToString(position) + " / " + Utils.parseTimeToString(mDuration);
        mTvProgress.setText(text);
        int progress = (int) (position * MAX / mDuration);
//        mPbProgress.setProgress(progress);
    }

    public void hideProgressBar(boolean hide) {
//        if (hide) {
//            mPbProgress.setVisibility(View.GONE);
//        } else {
//            mPbProgress.setVisibility(View.VISIBLE);
//        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.video_progress_dialog_play_tab;
    }

}
