package com.ximalaya.ting.android.xmplaysdk.video.view;

import android.content.Context;
import androidx.annotation.NonNull;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow;

import static android.view.View.MeasureSpec.AT_MOST;

/**
 * Created by sigma on 2018/1/19.
 * <AUTHOR>
 */
public abstract class VideoGestureDialog extends PopupWindow {

    protected final int mWidth, mHeight;

    public VideoGestureDialog(@NonNull Context context) {
        View contentView = LayoutInflater.from(context).inflate(getLayoutId(), null);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        windowManager.getDefaultDisplay().getMetrics(displayMetrics);
        int widthMeasureSpec = View.MeasureSpec.makeMeasureSpec(displayMetrics.widthPixels, AT_MOST);
        int heightMeasureSpec = View.MeasureSpec.makeMeasureSpec(displayMetrics.heightPixels, AT_MOST);
        contentView.measure(widthMeasureSpec, heightMeasureSpec);
        mWidth = contentView.getMeasuredWidth();
        mHeight = contentView.getMeasuredHeight();
        setContentView(contentView);
        setWidth(mWidth);
        setHeight(mHeight);
    }

    protected abstract int getLayoutId();

    public int getWidth() {
        return mWidth;
    }

    public int getHeight() {
        return mHeight;
    }

    public void showAtCenter(View parent) {
        int[] location = new int[2];
        parent.getLocationInWindow(location);
        super.showAtLocation(parent, Gravity.LEFT | Gravity.TOP,
                location[0] + parent.getWidth() / 2 - mWidth / 2,
                location[1] + parent.getHeight() / 2 - mHeight / 2);

    }

}
