<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/video_brightness_dialog_bg"
    android:orientation="horizontal"
    android:paddingLeft="18dp"
    android:paddingRight="20dp"
    android:paddingTop="17dp"
    android:paddingBottom="17dp">

    <ImageView
        android:id="@+id/video_iv_volume"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/video_ic_volume"
        tools:ignore="contentDescription"/>

    <ProgressBar
        android:id="@+id/video_pb_volume"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="16dp"
        style="@style/video_progress"
        android:max="1000"
        tools:progress="300" />

</LinearLayout>