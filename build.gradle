apply from: "config.gradle"
apply from: 'dependencies.gradle'

buildscript {
//    ext.kotlin_version = '1.1.3'
//    ext.anko_version = '0.10.1'
    repositories {
        maven { url "http://************:8082/artifactory/ximalaya-android-maven-repo/" }
        maven { url "http://************:8082/artifactory/host/" }
        maven { url 'http://************:8081/repository/ximalaya/' }
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/jcenter" }
        maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
        google()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:${ANDROID_TOOLS_BUILD_GRADLE}"
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//        classpath "org.jetbrains.kotlin:kotlin-android-extensions:$kotlin_version"
        classpath 'com.ximalaya.gradle:bundle-developer:2.0'
//        classpath 'com.neenbedankt.gradle.plugins:android-apt:1.8'
    }
//    project.gradle.startParameter.excludedTaskNames.add("lint")
}

allprojects {
    repositories {
        maven { url "http://************:8082/artifactory/ximalaya-android-maven-repo/" }
        maven { url "http://************:8082/artifactory/host/" }
        maven { url 'http://************:8081/repository/ximalaya/' }
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/jcenter" }
        maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
    }
}

ext {
    compileSdkVersion = 26
    buildToolsVersion = '25.0.2'
    minSdkVersion = "16"
    targetSdkVersion = "23"
}