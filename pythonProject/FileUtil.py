#!/usr/bin/python
#coding:utf-8
import os
import os.path
import sys

def getImages(path, fileList, fileEnd):
    # print u"========= 开始遍历图片:%s" % path
    for root, dirs, files in os.walk(path):
        if (not "build" in root) and (("drawable" in root and "res" in root) or ("lottie" in root)):
            for file in files:
                filePath = os.path.join(root, file)
                endStr = os.path.splitext(filePath)[-1]
                if endStr == fileEnd and (not ".9" in file):
                    if "lottie" in filePath:
                        print ("file:"+filePath)
                    if (not isBack(filePath)):
                        fileList.append(filePath)
    # print u"========= 结束遍历图片:%d" % len(fileList)

def getAllFileInPath(pathList,path):
    for root, dirs, files in os.walk(path):
        if (not "build" in root) and ("res" in root):
            for file in files:
                filePath = os.path.join(root, file)
                # endStr = os.path.splitext(filePath)[-1]
                if (not isBack(filePath) and os.path.isfile(filePath)):
                    pathList.append(filePath)


def isBack(file):
    return False
# def main(arv):
#     imageList = []
#     fileMap = {}
#     for path in arv:
#         getImages(path,imageList,".png")
#     totalSize = 0
#     canDeleteFielSize = 0
#     for file in imageList:
#         totalSize = totalSize + os.path.getsize(file)
#         if os.path.getsize(file) > 50*1024:
#             # print file
#             fileMap.setdefault(file, os.path.getsize(file))
#             canDeleteFielSize = canDeleteFielSize + os.path.getsize(file)
#     print canDeleteFielSize/float(1024*1024)
#     print totalSize/float(1024*1024)
#     b = sorted(fileMap.items(),key=lambda d:d[1],reverse=True)
#     for filename,size in b:
#         print("filename is %s , and size is %d" % (filename, size))



# if __name__ == '__main__':
#     main(sys.argv[1:])

